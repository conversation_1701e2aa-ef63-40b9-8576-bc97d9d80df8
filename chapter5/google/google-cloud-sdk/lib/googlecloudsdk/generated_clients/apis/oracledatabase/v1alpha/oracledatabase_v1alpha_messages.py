"""Generated message classes for oracledatabase version v1alpha.

The Oracle Database@Google Cloud API provides a set of APIs to manage Oracle
database services, such as Exadata and Autonomous Databases.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'oracledatabase'


class AllConnectionStrings(_messages.Message):
  r"""A list of all connection strings that can be used to connect to the
  Autonomous Database.

  Fields:
    high: Output only. The database service provides the highest level of
      resources to each SQL statement.
    low: Output only. The database service provides the least level of
      resources to each SQL statement.
    medium: Output only. The database service provides a lower level of
      resources to each SQL statement.
  """

  high = _messages.StringField(1)
  low = _messages.StringField(2)
  medium = _messages.StringField(3)


class AutonomousDatabase(_messages.Message):
  r"""Details of the Autonomous Database resource. https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/AutonomousDatabase/

  Messages:
    LabelsValue: Optional. The labels or tags associated with the Autonomous
      Database.

  Fields:
    adminPassword: Optional. The password for the default ADMIN user.
    cidr: Optional. The subnet CIDR range for the Autonomous Database.
    createTime: Output only. The date and time that the Autonomous Database
      was created.
    database: Optional. The name of the Autonomous Database. The database name
      must be unique in the project. The name must begin with a letter and can
      contain a maximum of 30 alphanumeric characters.
    disasterRecoverySupportedLocations: Output only. List of supported GCP
      region to clone the Autonomous Database for disaster recovery. Format:
      `project/{project}/locations/{location}`.
    displayName: Optional. The display name for the Autonomous Database. The
      name does not have to be unique within your project.
    entitlementId: Output only. The ID of the subscription entitlement
      associated with the Autonomous Database.
    labels: Optional. The labels or tags associated with the Autonomous
      Database.
    name: Identifier. The name of the Autonomous Database resource in the
      following format: projects/{project}/locations/{region}/autonomousDataba
      ses/{autonomous_database}
    network: Optional. The name of the VPC network used by the Autonomous
      Database in the following format:
      projects/{project}/global/networks/{network}
    odbNetwork: Optional. The name of the OdbNetwork associated with the
      Autonomous Database. Format:
      projects/{project}/locations/{location}/odbNetworks/{odb_network} It is
      optional but if specified, this should match the parent ODBNetwork of
      the OdbSubnet.
    odbSubnet: Optional. The name of the OdbSubnet associated with the
      Autonomous Database. Format: projects/{project}/locations/{location}/odb
      Networks/{odb_network}/odbSubnets/{odb_subnet}
    peerAutonomousDatabases: Output only. The peer Autonomous Database names
      of the given Autonomous Database.
    properties: Optional. The properties of the Autonomous Database.
    sourceConfig: Optional. The source Autonomous Database configuration for
      the standby Autonomous Database. The source Autonomous Database is
      configured while creating the Peer Autonomous Database and can't be
      updated after creation.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels or tags associated with the Autonomous Database.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  adminPassword = _messages.StringField(1)
  cidr = _messages.StringField(2)
  createTime = _messages.StringField(3)
  database = _messages.StringField(4)
  disasterRecoverySupportedLocations = _messages.StringField(5, repeated=True)
  displayName = _messages.StringField(6)
  entitlementId = _messages.StringField(7)
  labels = _messages.MessageField('LabelsValue', 8)
  name = _messages.StringField(9)
  network = _messages.StringField(10)
  odbNetwork = _messages.StringField(11)
  odbSubnet = _messages.StringField(12)
  peerAutonomousDatabases = _messages.StringField(13, repeated=True)
  properties = _messages.MessageField('AutonomousDatabaseProperties', 14)
  sourceConfig = _messages.MessageField('SourceConfig', 15)


class AutonomousDatabaseApex(_messages.Message):
  r"""Oracle APEX Application Development. https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/datatypes/AutonomousDatabaseApex

  Fields:
    apexVersion: Output only. The Oracle APEX Application Development version.
    ordsVersion: Output only. The Oracle REST Data Services (ORDS) version.
  """

  apexVersion = _messages.StringField(1)
  ordsVersion = _messages.StringField(2)


class AutonomousDatabaseBackup(_messages.Message):
  r"""Details of the Autonomous Database Backup resource.
  https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/AutonomousDatabaseBackup/

  Messages:
    LabelsValue: Optional. labels or tags associated with the resource.

  Fields:
    autonomousDatabase: Required. The name of the Autonomous Database resource
      for which the backup is being created. Format: projects/{project}/locati
      ons/{region}/autonomousDatabases/{autonomous_database}
    displayName: Optional. User friendly name for the Backup. The name does
      not have to be unique.
    labels: Optional. labels or tags associated with the resource.
    name: Identifier. The name of the Autonomous Database Backup resource with
      the format: projects/{project}/locations/{region}/autonomousDatabaseBack
      ups/{autonomous_database_backup}
    properties: Optional. Various properties of the backup.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. labels or tags associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  autonomousDatabase = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  properties = _messages.MessageField('AutonomousDatabaseBackupProperties', 5)


class AutonomousDatabaseBackupProperties(_messages.Message):
  r"""Properties of the Autonomous Database Backup resource.

  Enums:
    LifecycleStateValueValuesEnum: Output only. The lifecycle state of the
      backup.
    TypeValueValuesEnum: Output only. The type of the backup.

  Fields:
    availableTillTime: Output only. Timestamp until when the backup will be
      available.
    compartmentId: Output only. The OCID of the compartment.
    databaseSizeTb: Output only. The quantity of data in the database, in
      terabytes.
    dbVersion: Output only. A valid Oracle Database version for Autonomous
      Database.
    endTime: Output only. The date and time the backup completed.
    isAutomaticBackup: Output only. Indicates if the backup is automatic or
      user initiated.
    isLongTermBackup: Output only. Indicates if the backup is long term
      backup.
    isRestorable: Output only. Indicates if the backup can be used to restore
      the Autonomous Database.
    keyStoreId: Optional. The OCID of the key store of Oracle Vault.
    keyStoreWallet: Optional. The wallet name for Oracle Key Vault.
    kmsKeyId: Optional. The OCID of the key container that is used as the
      master encryption key in database transparent data encryption (TDE)
      operations.
    kmsKeyVersionId: Optional. The OCID of the key container version that is
      used in database transparent data encryption (TDE) operations KMS Key
      can have multiple key versions. If none is specified, the current key
      version (latest) of the Key Id is used for the operation. Autonomous
      Database Serverless does not use key versions, hence is not applicable
      for Autonomous Database Serverless instances.
    lifecycleDetails: Output only. Additional information about the current
      lifecycle state.
    lifecycleState: Output only. The lifecycle state of the backup.
    ocid: Output only. OCID of the Autonomous Database backup.
      https://docs.oracle.com/en-
      us/iaas/Content/General/Concepts/identifiers.htm#Oracle
    retentionPeriodDays: Optional. Retention period in days for the backup.
    sizeTb: Output only. The backup size in terabytes.
    startTime: Output only. The date and time the backup started.
    type: Output only. The type of the backup.
    vaultId: Optional. The OCID of the vault.
  """

  class LifecycleStateValueValuesEnum(_messages.Enum):
    r"""Output only. The lifecycle state of the backup.

    Values:
      STATE_UNSPECIFIED: Default unspecified value.
      CREATING: Indicates that the resource is in creating state.
      ACTIVE: Indicates that the resource is in active state.
      DELETING: Indicates that the resource is in deleting state.
      DELETED: Indicates that the resource is in deleted state.
      FAILED: Indicates that the resource is in failed state.
      UPDATING: Indicates that the resource is in updating state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    DELETED = 4
    FAILED = 5
    UPDATING = 6

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the backup.

    Values:
      TYPE_UNSPECIFIED: Default unspecified value.
      INCREMENTAL: Incremental backups.
      FULL: Full backups.
      LONG_TERM: Long term backups.
    """
    TYPE_UNSPECIFIED = 0
    INCREMENTAL = 1
    FULL = 2
    LONG_TERM = 3

  availableTillTime = _messages.StringField(1)
  compartmentId = _messages.StringField(2)
  databaseSizeTb = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  dbVersion = _messages.StringField(4)
  endTime = _messages.StringField(5)
  isAutomaticBackup = _messages.BooleanField(6)
  isLongTermBackup = _messages.BooleanField(7)
  isRestorable = _messages.BooleanField(8)
  keyStoreId = _messages.StringField(9)
  keyStoreWallet = _messages.StringField(10)
  kmsKeyId = _messages.StringField(11)
  kmsKeyVersionId = _messages.StringField(12)
  lifecycleDetails = _messages.StringField(13)
  lifecycleState = _messages.EnumField('LifecycleStateValueValuesEnum', 14)
  ocid = _messages.StringField(15)
  retentionPeriodDays = _messages.IntegerField(16, variant=_messages.Variant.INT32)
  sizeTb = _messages.FloatField(17, variant=_messages.Variant.FLOAT)
  startTime = _messages.StringField(18)
  type = _messages.EnumField('TypeValueValuesEnum', 19)
  vaultId = _messages.StringField(20)


class AutonomousDatabaseCharacterSet(_messages.Message):
  r"""Details of the Autonomous Database character set resource.
  https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/AutonomousDatabaseCharacterSets/

  Enums:
    CharacterSetTypeValueValuesEnum: Output only. The character set type for
      the Autonomous Database.

  Fields:
    characterSet: Output only. The character set name for the Autonomous
      Database which is the ID in the resource name.
    characterSetType: Output only. The character set type for the Autonomous
      Database.
    name: Identifier. The name of the Autonomous Database Character Set
      resource in the following format: projects/{project}/locations/{region}/
      autonomousDatabaseCharacterSets/{autonomous_database_character_set}
  """

  class CharacterSetTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The character set type for the Autonomous Database.

    Values:
      CHARACTER_SET_TYPE_UNSPECIFIED: Character set type is not specified.
      DATABASE: Character set type is set to database.
      NATIONAL: Character set type is set to national.
    """
    CHARACTER_SET_TYPE_UNSPECIFIED = 0
    DATABASE = 1
    NATIONAL = 2

  characterSet = _messages.StringField(1)
  characterSetType = _messages.EnumField('CharacterSetTypeValueValuesEnum', 2)
  name = _messages.StringField(3)


class AutonomousDatabaseConnectionStrings(_messages.Message):
  r"""The connection string used to connect to the Autonomous Database.
  https://docs.oracle.com/en-us/iaas/api/#/en/database/********/datatypes/Auto
  nomousDatabaseConnectionStrings

  Fields:
    allConnectionStrings: Output only. Returns all connection strings that can
      be used to connect to the Autonomous Database.
    dedicated: Output only. The database service provides the least level of
      resources to each SQL statement, but supports the most number of
      concurrent SQL statements.
    high: Output only. The database service provides the highest level of
      resources to each SQL statement.
    low: Output only. The database service provides the least level of
      resources to each SQL statement.
    medium: Output only. The database service provides a lower level of
      resources to each SQL statement.
    profiles: Output only. A list of connection string profiles to allow
      clients to group, filter, and select values based on the structured
      metadata.
  """

  allConnectionStrings = _messages.MessageField('AllConnectionStrings', 1)
  dedicated = _messages.StringField(2)
  high = _messages.StringField(3)
  low = _messages.StringField(4)
  medium = _messages.StringField(5)
  profiles = _messages.MessageField('DatabaseConnectionStringProfile', 6, repeated=True)


class AutonomousDatabaseConnectionUrls(_messages.Message):
  r"""The URLs for accessing Oracle Application Express (APEX) and SQL
  Developer Web with a browser from a Compute instance.
  https://docs.oracle.com/en-us/iaas/api/#/en/database/********/datatypes/Auto
  nomousDatabaseConnectionUrls

  Fields:
    apexUri: Output only. Oracle Application Express (APEX) URL.
    databaseTransformsUri: Output only. The URL of the Database Transforms for
      the Autonomous Database.
    graphStudioUri: Output only. The URL of the Graph Studio for the
      Autonomous Database.
    machineLearningNotebookUri: Output only. The URL of the Oracle Machine
      Learning (OML) Notebook for the Autonomous Database.
    machineLearningUserManagementUri: Output only. The URL of Machine Learning
      user management the Autonomous Database.
    mongoDbUri: Output only. The URL of the MongoDB API for the Autonomous
      Database.
    ordsUri: Output only. The Oracle REST Data Services (ORDS) URL of the Web
      Access for the Autonomous Database.
    sqlDevWebUri: Output only. The URL of the Oracle SQL Developer Web for the
      Autonomous Database.
  """

  apexUri = _messages.StringField(1)
  databaseTransformsUri = _messages.StringField(2)
  graphStudioUri = _messages.StringField(3)
  machineLearningNotebookUri = _messages.StringField(4)
  machineLearningUserManagementUri = _messages.StringField(5)
  mongoDbUri = _messages.StringField(6)
  ordsUri = _messages.StringField(7)
  sqlDevWebUri = _messages.StringField(8)


class AutonomousDatabaseProperties(_messages.Message):
  r"""The properties of an Autonomous Database.

  Enums:
    DataSafeStateValueValuesEnum: Output only. The current state of the Data
      Safe registration for the Autonomous Database.
    DatabaseManagementStateValueValuesEnum: Output only. The current state of
      database management for the Autonomous Database.
    DbEditionValueValuesEnum: Optional. The edition of the Autonomous
      Databases.
    DbWorkloadValueValuesEnum: Required. The workload type of the Autonomous
      Database.
    LicenseTypeValueValuesEnum: Required. The license type used for the
      Autonomous Database.
    LocalDisasterRecoveryTypeValueValuesEnum: Output only. This field
      indicates the local disaster recovery (DR) type of an Autonomous
      Database.
    MaintenanceScheduleTypeValueValuesEnum: Optional. The maintenance schedule
      of the Autonomous Database.
    OpenModeValueValuesEnum: Output only. This field indicates the current
      mode of the Autonomous Database.
    OperationsInsightsStateValueValuesEnum: Output only. This field indicates
      the state of Operations Insights for the Autonomous Database.
    PermissionLevelValueValuesEnum: Output only. The permission level of the
      Autonomous Database.
    RefreshableModeValueValuesEnum: Output only. The refresh mode of the
      cloned Autonomous Database.
    RefreshableStateValueValuesEnum: Output only. The refresh State of the
      clone.
    RoleValueValuesEnum: Output only. The Data Guard role of the Autonomous
      Database.
    StateValueValuesEnum: Output only. The current lifecycle state of the
      Autonomous Database.

  Fields:
    actualUsedDataStorageSizeTb: Output only. The amount of storage currently
      being used for user and system data, in terabytes.
    allocatedStorageSizeTb: Output only. The amount of storage currently
      allocated for the database tables and billed for, rounded up in
      terabytes.
    allowlistedIps: Optional. The list of allowlisted IP addresses for the
      Autonomous Database.
    apexDetails: Output only. The details for the Oracle APEX Application
      Development.
    arePrimaryAllowlistedIpsUsed: Output only. This field indicates the status
      of Data Guard and Access control for the Autonomous Database. The
      field's value is null if Data Guard is disabled or Access Control is
      disabled. The field's value is TRUE if both Data Guard and Access
      Control are enabled, and the Autonomous Database is using primary IP
      access control list (ACL) for standby. The field's value is FALSE if
      both Data Guard and Access Control are enabled, and the Autonomous
      Database is using a different IP access control list (ACL) for standby
      compared to primary.
    autonomousContainerDatabaseId: Output only. The Autonomous Container
      Database OCID.
    availableUpgradeVersions: Output only. The list of available Oracle
      Database upgrade versions for an Autonomous Database.
    backupRetentionPeriodDays: Optional. The retention period for the
      Autonomous Database. This field is specified in days, can range from 1
      day to 60 days, and has a default value of 60 days.
    characterSet: Optional. The character set for the Autonomous Database. The
      default is AL32UTF8.
    computeCount: Optional. The number of compute servers for the Autonomous
      Database.
    connectionStrings: Output only. The connection strings used to connect to
      an Autonomous Database.
    connectionUrls: Output only. The Oracle Connection URLs for an Autonomous
      Database.
    cpuCoreCount: Optional. The number of CPU cores to be made available to
      the database.
    customerContacts: Optional. The list of customer contacts.
    dataGuardRoleChangedTime: Output only. The date and time the Autonomous
      Data Guard role was changed for the standby Autonomous Database.
    dataSafeState: Output only. The current state of the Data Safe
      registration for the Autonomous Database.
    dataStorageSizeGb: Optional. The size of the data stored in the database,
      in gigabytes.
    dataStorageSizeTb: Optional. The size of the data stored in the database,
      in terabytes.
    databaseManagementState: Output only. The current state of database
      management for the Autonomous Database.
    dbEdition: Optional. The edition of the Autonomous Databases.
    dbVersion: Optional. The Oracle Database version for the Autonomous
      Database.
    dbWorkload: Required. The workload type of the Autonomous Database.
    disasterRecoveryRoleChangedTime: Output only. The date and time the
      Disaster Recovery role was changed for the standby Autonomous Database.
    encryptionKey: Optional. The encryption key used to encrypt the Autonomous
      Database. Updating this field will add a new entry in the
      `encryption_key_history_entries` field with the former version.
    encryptionKeyHistoryEntries: Output only. The history of the encryption
      keys used to encrypt the Autonomous Database.
    failedDataRecoveryDuration: Output only. This field indicates the number
      of seconds of data loss during a Data Guard failover.
    isAutoScalingEnabled: Optional. This field indicates if auto scaling is
      enabled for the Autonomous Database CPU core count.
    isLocalDataGuardEnabled: Output only. This field indicates whether the
      Autonomous Database has local (in-region) Data Guard enabled.
    isStorageAutoScalingEnabled: Optional. This field indicates if auto
      scaling is enabled for the Autonomous Database storage.
    licenseType: Required. The license type used for the Autonomous Database.
    lifecycleDetails: Output only. The details of the current lifestyle state
      of the Autonomous Database.
    localAdgAutoFailoverMaxDataLossLimit: Output only. This field indicates
      the maximum data loss limit for an Autonomous Database, in seconds.
    localDisasterRecoveryType: Output only. This field indicates the local
      disaster recovery (DR) type of an Autonomous Database.
    localStandbyDb: Output only. The details of the Autonomous Data Guard
      standby database.
    maintenanceBeginTime: Output only. The date and time when maintenance will
      begin.
    maintenanceEndTime: Output only. The date and time when maintenance will
      end.
    maintenanceScheduleType: Optional. The maintenance schedule of the
      Autonomous Database.
    memoryPerOracleComputeUnitGbs: Output only. The amount of memory enabled
      per ECPU, in gigabytes.
    memoryTableGbs: Output only. The memory assigned to in-memory tables in an
      Autonomous Database.
    mtlsConnectionRequired: Optional. This field specifies if the Autonomous
      Database requires mTLS connections.
    nCharacterSet: Optional. The national character set for the Autonomous
      Database. The default is AL16UTF16.
    nextLongTermBackupTime: Output only. The long term backup schedule of the
      Autonomous Database.
    ociUrl: Output only. The Oracle Cloud Infrastructure link for the
      Autonomous Database.
    ocid: Output only. OCID of the Autonomous Database.
      https://docs.oracle.com/en-
      us/iaas/Content/General/Concepts/identifiers.htm#Oracle
    openMode: Output only. This field indicates the current mode of the
      Autonomous Database.
    operationsInsightsState: Output only. This field indicates the state of
      Operations Insights for the Autonomous Database.
    peerDbIds: Output only. The list of OCIDs of standby databases located in
      Autonomous Data Guard remote regions that are associated with the source
      database.
    permissionLevel: Output only. The permission level of the Autonomous
      Database.
    privateEndpoint: Output only. The private endpoint for the Autonomous
      Database.
    privateEndpointIp: Optional. The private endpoint IP address for the
      Autonomous Database.
    privateEndpointLabel: Optional. The private endpoint label for the
      Autonomous Database.
    refreshableMode: Output only. The refresh mode of the cloned Autonomous
      Database.
    refreshableState: Output only. The refresh State of the clone.
    role: Output only. The Data Guard role of the Autonomous Database.
    scheduledOperationDetails: Output only. The list and details of the
      scheduled operations of the Autonomous Database.
    secretId: Optional. The ID of the Oracle Cloud Infrastructure vault
      secret.
    serviceAgentEmail: Output only. An Oracle-managed Google Cloud service
      account on which customers can grant roles to access resources in the
      customer project.
    sqlWebDeveloperUrl: Output only. The SQL Web Developer URL for the
      Autonomous Database.
    state: Output only. The current lifecycle state of the Autonomous
      Database.
    supportedCloneRegions: Output only. The list of available regions that can
      be used to create a clone for the Autonomous Database.
    totalAutoBackupStorageSizeGbs: Output only. The storage space used by
      automatic backups of Autonomous Database, in gigabytes.
    usedDataStorageSizeTbs: Output only. The storage space used by Autonomous
      Database, in gigabytes.
    vaultId: Optional. The ID of the Oracle Cloud Infrastructure vault.
  """

  class DataSafeStateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the Data Safe registration for the
    Autonomous Database.

    Values:
      DATA_SAFE_STATE_UNSPECIFIED: Default unspecified value.
      REGISTERING: Registering data safe state.
      REGISTERED: Registered data safe state.
      DEREGISTERING: Deregistering data safe state.
      NOT_REGISTERED: Not registered data safe state.
      FAILED: Failed data safe state.
    """
    DATA_SAFE_STATE_UNSPECIFIED = 0
    REGISTERING = 1
    REGISTERED = 2
    DEREGISTERING = 3
    NOT_REGISTERED = 4
    FAILED = 5

  class DatabaseManagementStateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of database management for the
    Autonomous Database.

    Values:
      DATABASE_MANAGEMENT_STATE_UNSPECIFIED: Default unspecified value.
      ENABLING: Enabling Database Management state
      ENABLED: Enabled Database Management state
      DISABLING: Disabling Database Management state
      NOT_ENABLED: Not Enabled Database Management state
      FAILED_ENABLING: Failed enabling Database Management state
      FAILED_DISABLING: Failed disabling Database Management state
    """
    DATABASE_MANAGEMENT_STATE_UNSPECIFIED = 0
    ENABLING = 1
    ENABLED = 2
    DISABLING = 3
    NOT_ENABLED = 4
    FAILED_ENABLING = 5
    FAILED_DISABLING = 6

  class DbEditionValueValuesEnum(_messages.Enum):
    r"""Optional. The edition of the Autonomous Databases.

    Values:
      DATABASE_EDITION_UNSPECIFIED: Default unspecified value.
      STANDARD_EDITION: Standard Database Edition
      ENTERPRISE_EDITION: Enterprise Database Edition
    """
    DATABASE_EDITION_UNSPECIFIED = 0
    STANDARD_EDITION = 1
    ENTERPRISE_EDITION = 2

  class DbWorkloadValueValuesEnum(_messages.Enum):
    r"""Required. The workload type of the Autonomous Database.

    Values:
      DB_WORKLOAD_UNSPECIFIED: Default unspecified value.
      OLTP: Autonomous Transaction Processing database.
      DW: Autonomous Data Warehouse database.
      AJD: Autonomous JSON Database.
      APEX: Autonomous Database with the Oracle APEX Application Development
        workload type.
    """
    DB_WORKLOAD_UNSPECIFIED = 0
    OLTP = 1
    DW = 2
    AJD = 3
    APEX = 4

  class LicenseTypeValueValuesEnum(_messages.Enum):
    r"""Required. The license type used for the Autonomous Database.

    Values:
      LICENSE_TYPE_UNSPECIFIED: Unspecified
      LICENSE_INCLUDED: License included part of offer
      BRING_YOUR_OWN_LICENSE: Bring your own license
    """
    LICENSE_TYPE_UNSPECIFIED = 0
    LICENSE_INCLUDED = 1
    BRING_YOUR_OWN_LICENSE = 2

  class LocalDisasterRecoveryTypeValueValuesEnum(_messages.Enum):
    r"""Output only. This field indicates the local disaster recovery (DR)
    type of an Autonomous Database.

    Values:
      LOCAL_DISASTER_RECOVERY_TYPE_UNSPECIFIED: Default unspecified value.
      ADG: Autonomous Data Guard recovery.
      BACKUP_BASED: Backup based recovery.
    """
    LOCAL_DISASTER_RECOVERY_TYPE_UNSPECIFIED = 0
    ADG = 1
    BACKUP_BASED = 2

  class MaintenanceScheduleTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The maintenance schedule of the Autonomous Database.

    Values:
      MAINTENANCE_SCHEDULE_TYPE_UNSPECIFIED: Default unspecified value.
      EARLY: An EARLY maintenance schedule patches the database before the
        regular scheduled maintenance.
      REGULAR: A REGULAR maintenance schedule follows the normal maintenance
        cycle.
    """
    MAINTENANCE_SCHEDULE_TYPE_UNSPECIFIED = 0
    EARLY = 1
    REGULAR = 2

  class OpenModeValueValuesEnum(_messages.Enum):
    r"""Output only. This field indicates the current mode of the Autonomous
    Database.

    Values:
      OPEN_MODE_UNSPECIFIED: Default unspecified value.
      READ_ONLY: Read Only Mode
      READ_WRITE: Read Write Mode
    """
    OPEN_MODE_UNSPECIFIED = 0
    READ_ONLY = 1
    READ_WRITE = 2

  class OperationsInsightsStateValueValuesEnum(_messages.Enum):
    r"""Output only. This field indicates the state of Operations Insights for
    the Autonomous Database.

    Values:
      OPERATIONS_INSIGHTS_STATE_UNSPECIFIED: Default unspecified value.
      ENABLING: Enabling status for operation insights.
      ENABLED: Enabled status for operation insights.
      DISABLING: Disabling status for operation insights.
      NOT_ENABLED: Not Enabled status for operation insights.
      FAILED_ENABLING: Failed enabling status for operation insights.
      FAILED_DISABLING: Failed disabling status for operation insights.
    """
    OPERATIONS_INSIGHTS_STATE_UNSPECIFIED = 0
    ENABLING = 1
    ENABLED = 2
    DISABLING = 3
    NOT_ENABLED = 4
    FAILED_ENABLING = 5
    FAILED_DISABLING = 6

  class PermissionLevelValueValuesEnum(_messages.Enum):
    r"""Output only. The permission level of the Autonomous Database.

    Values:
      PERMISSION_LEVEL_UNSPECIFIED: Default unspecified value.
      RESTRICTED: Restricted mode allows access only by admin users.
      UNRESTRICTED: Normal access.
    """
    PERMISSION_LEVEL_UNSPECIFIED = 0
    RESTRICTED = 1
    UNRESTRICTED = 2

  class RefreshableModeValueValuesEnum(_messages.Enum):
    r"""Output only. The refresh mode of the cloned Autonomous Database.

    Values:
      REFRESHABLE_MODE_UNSPECIFIED: The default unspecified value.
      AUTOMATIC: AUTOMATIC indicates that the cloned database is automatically
        refreshed with data from the source Autonomous Database.
      MANUAL: MANUAL indicates that the cloned database is manually refreshed
        with data from the source Autonomous Database.
    """
    REFRESHABLE_MODE_UNSPECIFIED = 0
    AUTOMATIC = 1
    MANUAL = 2

  class RefreshableStateValueValuesEnum(_messages.Enum):
    r"""Output only. The refresh State of the clone.

    Values:
      REFRESHABLE_STATE_UNSPECIFIED: Default unspecified value.
      REFRESHING: Refreshing
      NOT_REFRESHING: Not refreshed
    """
    REFRESHABLE_STATE_UNSPECIFIED = 0
    REFRESHING = 1
    NOT_REFRESHING = 2

  class RoleValueValuesEnum(_messages.Enum):
    r"""Output only. The Data Guard role of the Autonomous Database.

    Values:
      ROLE_UNSPECIFIED: Default unspecified value.
      PRIMARY: Primary role
      STANDBY: Standby role
      DISABLED_STANDBY: Disabled standby role
      BACKUP_COPY: Backup copy role
      SNAPSHOT_STANDBY: Snapshot standby role
    """
    ROLE_UNSPECIFIED = 0
    PRIMARY = 1
    STANDBY = 2
    DISABLED_STANDBY = 3
    BACKUP_COPY = 4
    SNAPSHOT_STANDBY = 5

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current lifecycle state of the Autonomous Database.

    Values:
      STATE_UNSPECIFIED: Default unspecified value.
      PROVISIONING: Indicates that the Autonomous Database is in provisioning
        state.
      AVAILABLE: Indicates that the Autonomous Database is in available state.
      STOPPING: Indicates that the Autonomous Database is in stopping state.
      STOPPED: Indicates that the Autonomous Database is in stopped state.
      STARTING: Indicates that the Autonomous Database is in starting state.
      TERMINATING: Indicates that the Autonomous Database is in terminating
        state.
      TERMINATED: Indicates that the Autonomous Database is in terminated
        state.
      UNAVAILABLE: Indicates that the Autonomous Database is in unavailable
        state.
      RESTORE_IN_PROGRESS: Indicates that the Autonomous Database restore is
        in progress.
      RESTORE_FAILED: Indicates that the Autonomous Database failed to
        restore.
      BACKUP_IN_PROGRESS: Indicates that the Autonomous Database backup is in
        progress.
      SCALE_IN_PROGRESS: Indicates that the Autonomous Database scale is in
        progress.
      AVAILABLE_NEEDS_ATTENTION: Indicates that the Autonomous Database is
        available but needs attention state.
      UPDATING: Indicates that the Autonomous Database is in updating state.
      MAINTENANCE_IN_PROGRESS: Indicates that the Autonomous Database's
        maintenance is in progress state.
      RESTARTING: Indicates that the Autonomous Database is in restarting
        state.
      RECREATING: Indicates that the Autonomous Database is in recreating
        state.
      ROLE_CHANGE_IN_PROGRESS: Indicates that the Autonomous Database's role
        change is in progress state.
      UPGRADING: Indicates that the Autonomous Database is in upgrading state.
      INACCESSIBLE: Indicates that the Autonomous Database is in inaccessible
        state.
      STANDBY: Indicates that the Autonomous Database is in standby state.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    AVAILABLE = 2
    STOPPING = 3
    STOPPED = 4
    STARTING = 5
    TERMINATING = 6
    TERMINATED = 7
    UNAVAILABLE = 8
    RESTORE_IN_PROGRESS = 9
    RESTORE_FAILED = 10
    BACKUP_IN_PROGRESS = 11
    SCALE_IN_PROGRESS = 12
    AVAILABLE_NEEDS_ATTENTION = 13
    UPDATING = 14
    MAINTENANCE_IN_PROGRESS = 15
    RESTARTING = 16
    RECREATING = 17
    ROLE_CHANGE_IN_PROGRESS = 18
    UPGRADING = 19
    INACCESSIBLE = 20
    STANDBY = 21

  actualUsedDataStorageSizeTb = _messages.FloatField(1)
  allocatedStorageSizeTb = _messages.FloatField(2)
  allowlistedIps = _messages.StringField(3, repeated=True)
  apexDetails = _messages.MessageField('AutonomousDatabaseApex', 4)
  arePrimaryAllowlistedIpsUsed = _messages.BooleanField(5)
  autonomousContainerDatabaseId = _messages.StringField(6)
  availableUpgradeVersions = _messages.StringField(7, repeated=True)
  backupRetentionPeriodDays = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  characterSet = _messages.StringField(9)
  computeCount = _messages.FloatField(10, variant=_messages.Variant.FLOAT)
  connectionStrings = _messages.MessageField('AutonomousDatabaseConnectionStrings', 11)
  connectionUrls = _messages.MessageField('AutonomousDatabaseConnectionUrls', 12)
  cpuCoreCount = _messages.IntegerField(13, variant=_messages.Variant.INT32)
  customerContacts = _messages.MessageField('CustomerContact', 14, repeated=True)
  dataGuardRoleChangedTime = _messages.StringField(15)
  dataSafeState = _messages.EnumField('DataSafeStateValueValuesEnum', 16)
  dataStorageSizeGb = _messages.IntegerField(17, variant=_messages.Variant.INT32)
  dataStorageSizeTb = _messages.IntegerField(18, variant=_messages.Variant.INT32)
  databaseManagementState = _messages.EnumField('DatabaseManagementStateValueValuesEnum', 19)
  dbEdition = _messages.EnumField('DbEditionValueValuesEnum', 20)
  dbVersion = _messages.StringField(21)
  dbWorkload = _messages.EnumField('DbWorkloadValueValuesEnum', 22)
  disasterRecoveryRoleChangedTime = _messages.StringField(23)
  encryptionKey = _messages.MessageField('EncryptionKey', 24)
  encryptionKeyHistoryEntries = _messages.MessageField('EncryptionKeyHistoryEntry', 25, repeated=True)
  failedDataRecoveryDuration = _messages.StringField(26)
  isAutoScalingEnabled = _messages.BooleanField(27)
  isLocalDataGuardEnabled = _messages.BooleanField(28)
  isStorageAutoScalingEnabled = _messages.BooleanField(29)
  licenseType = _messages.EnumField('LicenseTypeValueValuesEnum', 30)
  lifecycleDetails = _messages.StringField(31)
  localAdgAutoFailoverMaxDataLossLimit = _messages.IntegerField(32, variant=_messages.Variant.INT32)
  localDisasterRecoveryType = _messages.EnumField('LocalDisasterRecoveryTypeValueValuesEnum', 33)
  localStandbyDb = _messages.MessageField('AutonomousDatabaseStandbySummary', 34)
  maintenanceBeginTime = _messages.StringField(35)
  maintenanceEndTime = _messages.StringField(36)
  maintenanceScheduleType = _messages.EnumField('MaintenanceScheduleTypeValueValuesEnum', 37)
  memoryPerOracleComputeUnitGbs = _messages.IntegerField(38, variant=_messages.Variant.INT32)
  memoryTableGbs = _messages.IntegerField(39, variant=_messages.Variant.INT32)
  mtlsConnectionRequired = _messages.BooleanField(40)
  nCharacterSet = _messages.StringField(41)
  nextLongTermBackupTime = _messages.StringField(42)
  ociUrl = _messages.StringField(43)
  ocid = _messages.StringField(44)
  openMode = _messages.EnumField('OpenModeValueValuesEnum', 45)
  operationsInsightsState = _messages.EnumField('OperationsInsightsStateValueValuesEnum', 46)
  peerDbIds = _messages.StringField(47, repeated=True)
  permissionLevel = _messages.EnumField('PermissionLevelValueValuesEnum', 48)
  privateEndpoint = _messages.StringField(49)
  privateEndpointIp = _messages.StringField(50)
  privateEndpointLabel = _messages.StringField(51)
  refreshableMode = _messages.EnumField('RefreshableModeValueValuesEnum', 52)
  refreshableState = _messages.EnumField('RefreshableStateValueValuesEnum', 53)
  role = _messages.EnumField('RoleValueValuesEnum', 54)
  scheduledOperationDetails = _messages.MessageField('ScheduledOperationDetails', 55, repeated=True)
  secretId = _messages.StringField(56)
  serviceAgentEmail = _messages.StringField(57)
  sqlWebDeveloperUrl = _messages.StringField(58)
  state = _messages.EnumField('StateValueValuesEnum', 59)
  supportedCloneRegions = _messages.StringField(60, repeated=True)
  totalAutoBackupStorageSizeGbs = _messages.FloatField(61, variant=_messages.Variant.FLOAT)
  usedDataStorageSizeTbs = _messages.IntegerField(62, variant=_messages.Variant.INT32)
  vaultId = _messages.StringField(63)


class AutonomousDatabaseStandbySummary(_messages.Message):
  r"""Autonomous Data Guard standby database details.
  https://docs.oracle.com/en-us/iaas/api/#/en/database/********/datatypes/Auto
  nomousDatabaseStandbySummary

  Enums:
    StateValueValuesEnum: Output only. The current lifecycle state of the
      Autonomous Database.

  Fields:
    dataGuardRoleChangedTime: Output only. The date and time the Autonomous
      Data Guard role was switched for the standby Autonomous Database.
    disasterRecoveryRoleChangedTime: Output only. The date and time the
      Disaster Recovery role was switched for the standby Autonomous Database.
    lagTimeDuration: Output only. The amount of time, in seconds, that the
      data of the standby database lags in comparison to the data of the
      primary database.
    lifecycleDetails: Output only. The additional details about the current
      lifecycle state of the Autonomous Database.
    state: Output only. The current lifecycle state of the Autonomous
      Database.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current lifecycle state of the Autonomous Database.

    Values:
      STATE_UNSPECIFIED: Default unspecified value.
      PROVISIONING: Indicates that the Autonomous Database is in provisioning
        state.
      AVAILABLE: Indicates that the Autonomous Database is in available state.
      STOPPING: Indicates that the Autonomous Database is in stopping state.
      STOPPED: Indicates that the Autonomous Database is in stopped state.
      STARTING: Indicates that the Autonomous Database is in starting state.
      TERMINATING: Indicates that the Autonomous Database is in terminating
        state.
      TERMINATED: Indicates that the Autonomous Database is in terminated
        state.
      UNAVAILABLE: Indicates that the Autonomous Database is in unavailable
        state.
      RESTORE_IN_PROGRESS: Indicates that the Autonomous Database restore is
        in progress.
      RESTORE_FAILED: Indicates that the Autonomous Database failed to
        restore.
      BACKUP_IN_PROGRESS: Indicates that the Autonomous Database backup is in
        progress.
      SCALE_IN_PROGRESS: Indicates that the Autonomous Database scale is in
        progress.
      AVAILABLE_NEEDS_ATTENTION: Indicates that the Autonomous Database is
        available but needs attention state.
      UPDATING: Indicates that the Autonomous Database is in updating state.
      MAINTENANCE_IN_PROGRESS: Indicates that the Autonomous Database's
        maintenance is in progress state.
      RESTARTING: Indicates that the Autonomous Database is in restarting
        state.
      RECREATING: Indicates that the Autonomous Database is in recreating
        state.
      ROLE_CHANGE_IN_PROGRESS: Indicates that the Autonomous Database's role
        change is in progress state.
      UPGRADING: Indicates that the Autonomous Database is in upgrading state.
      INACCESSIBLE: Indicates that the Autonomous Database is in inaccessible
        state.
      STANDBY: Indicates that the Autonomous Database is in standby state.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    AVAILABLE = 2
    STOPPING = 3
    STOPPED = 4
    STARTING = 5
    TERMINATING = 6
    TERMINATED = 7
    UNAVAILABLE = 8
    RESTORE_IN_PROGRESS = 9
    RESTORE_FAILED = 10
    BACKUP_IN_PROGRESS = 11
    SCALE_IN_PROGRESS = 12
    AVAILABLE_NEEDS_ATTENTION = 13
    UPDATING = 14
    MAINTENANCE_IN_PROGRESS = 15
    RESTARTING = 16
    RECREATING = 17
    ROLE_CHANGE_IN_PROGRESS = 18
    UPGRADING = 19
    INACCESSIBLE = 20
    STANDBY = 21

  dataGuardRoleChangedTime = _messages.StringField(1)
  disasterRecoveryRoleChangedTime = _messages.StringField(2)
  lagTimeDuration = _messages.StringField(3)
  lifecycleDetails = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class AutonomousDbVersion(_messages.Message):
  r"""Details of the Autonomous Database version. https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/AutonomousDbVersionSummary/

  Enums:
    DbWorkloadValueValuesEnum: Output only. The Autonomous Database workload
      type.

  Fields:
    dbWorkload: Output only. The Autonomous Database workload type.
    name: Identifier. The name of the Autonomous Database Version resource
      with the format: projects/{project}/locations/{region}/autonomousDbVersi
      ons/{autonomous_db_version}
    version: Output only. An Oracle Database version for Autonomous Database.
    workloadUri: Output only. A URL that points to a detailed description of
      the Autonomous Database version.
  """

  class DbWorkloadValueValuesEnum(_messages.Enum):
    r"""Output only. The Autonomous Database workload type.

    Values:
      DB_WORKLOAD_UNSPECIFIED: Default unspecified value.
      OLTP: Autonomous Transaction Processing database.
      DW: Autonomous Data Warehouse database.
      AJD: Autonomous JSON Database.
      APEX: Autonomous Database with the Oracle APEX Application Development
        workload type.
    """
    DB_WORKLOAD_UNSPECIFIED = 0
    OLTP = 1
    DW = 2
    AJD = 3
    APEX = 4

  dbWorkload = _messages.EnumField('DbWorkloadValueValuesEnum', 1)
  name = _messages.StringField(2)
  version = _messages.StringField(3)
  workloadUri = _messages.StringField(4)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CloudAccountDetails(_messages.Message):
  r"""Details of the OCI Cloud Account.

  Fields:
    accountCreationUri: Output only. URL to create a new account and link.
    cloudAccount: Output only. OCI account name.
    cloudAccountHomeRegion: Output only. OCI account home region.
    linkExistingAccountUri: Output only. URL to link an existing account.
  """

  accountCreationUri = _messages.StringField(1)
  cloudAccount = _messages.StringField(2)
  cloudAccountHomeRegion = _messages.StringField(3)
  linkExistingAccountUri = _messages.StringField(4)


class CloudExadataInfrastructure(_messages.Message):
  r"""Represents CloudExadataInfrastructure resource.
  https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/CloudExadataInfrastructure/

  Messages:
    LabelsValue: Optional. Labels or tags associated with the resource.

  Fields:
    createTime: Output only. The date and time that the Exadata Infrastructure
      was created.
    displayName: Optional. User friendly name for this resource.
    entitlementId: Output only. Entitlement ID of the private offer against
      which this infrastructure resource is provisioned.
    gcpOracleZone: Optional. Google Cloud Platform location where Oracle
      Exadata is hosted.
    labels: Optional. Labels or tags associated with the resource.
    name: Identifier. The name of the Exadata Infrastructure resource with the
      format: projects/{project}/locations/{region}/cloudExadataInfrastructure
      s/{cloud_exadata_infrastructure}
    properties: Optional. Various properties of the infra.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels or tags associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  entitlementId = _messages.StringField(3)
  gcpOracleZone = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  properties = _messages.MessageField('CloudExadataInfrastructureProperties', 7)


class CloudExadataInfrastructureProperties(_messages.Message):
  r"""Various properties of Exadata Infrastructure.

  Enums:
    ComputeModelValueValuesEnum: Output only. The compute model of the Exadata
      Infrastructure.
    StateValueValuesEnum: Output only. The current lifecycle state of the
      Exadata Infrastructure.

  Fields:
    activatedStorageCount: Output only. The requested number of additional
      storage servers activated for the Exadata Infrastructure.
    additionalStorageCount: Output only. The requested number of additional
      storage servers for the Exadata Infrastructure.
    availableStorageSizeGb: Output only. The available storage can be
      allocated to the Exadata Infrastructure resource, in gigabytes (GB).
    computeCount: Optional. The number of compute servers for the Exadata
      Infrastructure.
    computeModel: Output only. The compute model of the Exadata
      Infrastructure.
    cpuCount: Output only. The number of enabled CPU cores.
    customerContacts: Optional. The list of customer contacts.
    dataStorageSizeTb: Output only. Size, in terabytes, of the DATA disk
      group.
    databaseServerType: Output only. The database server type of the Exadata
      Infrastructure.
    dbNodeStorageSizeGb: Output only. The local node storage allocated in GBs.
    dbServerVersion: Output only. The software version of the database servers
      (dom0) in the Exadata Infrastructure.
    maintenanceWindow: Optional. Maintenance window for repair.
    maxCpuCount: Output only. The total number of CPU cores available.
    maxDataStorageTb: Output only. The total available DATA disk group size.
    maxDbNodeStorageSizeGb: Output only. The total local node storage
      available in GBs.
    maxMemoryGb: Output only. The total memory available in GBs.
    memorySizeGb: Output only. The memory allocated in GBs.
    monthlyDbServerVersion: Output only. The monthly software version of the
      database servers (dom0) in the Exadata Infrastructure. Example: 20.1.15
    monthlyStorageServerVersion: Output only. The monthly software version of
      the storage servers (cells) in the Exadata Infrastructure. Example:
      20.1.15
    nextMaintenanceRunId: Output only. The OCID of the next maintenance run.
    nextMaintenanceRunTime: Output only. The time when the next maintenance
      run will occur.
    nextSecurityMaintenanceRunTime: Output only. The time when the next
      security maintenance run will occur.
    ociUrl: Output only. Deep link to the OCI console to view this resource.
    ocid: Output only. OCID of created infra. https://docs.oracle.com/en-
      us/iaas/Content/General/Concepts/identifiers.htm#Oracle
    shape: Required. The shape of the Exadata Infrastructure. The shape
      determines the amount of CPU, storage, and memory resources allocated to
      the instance.
    state: Output only. The current lifecycle state of the Exadata
      Infrastructure.
    storageCount: Optional. The number of Cloud Exadata storage servers for
      the Exadata Infrastructure.
    storageServerType: Output only. The storage server type of the Exadata
      Infrastructure.
    storageServerVersion: Output only. The software version of the storage
      servers (cells) in the Exadata Infrastructure.
    totalStorageSizeGb: Optional. The total storage allocated to the Exadata
      Infrastructure resource, in gigabytes (GB).
  """

  class ComputeModelValueValuesEnum(_messages.Enum):
    r"""Output only. The compute model of the Exadata Infrastructure.

    Values:
      COMPUTE_MODEL_UNSPECIFIED: Unspecified compute model.
      COMPUTE_MODEL_ECPU: Abstract measure of compute resources. ECPUs are
        based on the number of cores elastically allocated from a pool of
        compute and storage servers.
      COMPUTE_MODEL_OCPU: Physical measure of compute resources. OCPUs are
        based on the physical core of a processor.
    """
    COMPUTE_MODEL_UNSPECIFIED = 0
    COMPUTE_MODEL_ECPU = 1
    COMPUTE_MODEL_OCPU = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current lifecycle state of the Exadata
    Infrastructure.

    Values:
      STATE_UNSPECIFIED: Default unspecified value.
      PROVISIONING: The Exadata Infrastructure is being provisioned.
      AVAILABLE: The Exadata Infrastructure is available for use.
      UPDATING: The Exadata Infrastructure is being updated.
      TERMINATING: The Exadata Infrastructure is being terminated.
      TERMINATED: The Exadata Infrastructure is terminated.
      FAILED: The Exadata Infrastructure is in failed state.
      MAINTENANCE_IN_PROGRESS: The Exadata Infrastructure is in maintenance.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    AVAILABLE = 2
    UPDATING = 3
    TERMINATING = 4
    TERMINATED = 5
    FAILED = 6
    MAINTENANCE_IN_PROGRESS = 7

  activatedStorageCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  additionalStorageCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  availableStorageSizeGb = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  computeCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  computeModel = _messages.EnumField('ComputeModelValueValuesEnum', 5)
  cpuCount = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  customerContacts = _messages.MessageField('CustomerContact', 7, repeated=True)
  dataStorageSizeTb = _messages.FloatField(8)
  databaseServerType = _messages.StringField(9)
  dbNodeStorageSizeGb = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  dbServerVersion = _messages.StringField(11)
  maintenanceWindow = _messages.MessageField('MaintenanceWindow', 12)
  maxCpuCount = _messages.IntegerField(13, variant=_messages.Variant.INT32)
  maxDataStorageTb = _messages.FloatField(14)
  maxDbNodeStorageSizeGb = _messages.IntegerField(15, variant=_messages.Variant.INT32)
  maxMemoryGb = _messages.IntegerField(16, variant=_messages.Variant.INT32)
  memorySizeGb = _messages.IntegerField(17, variant=_messages.Variant.INT32)
  monthlyDbServerVersion = _messages.StringField(18)
  monthlyStorageServerVersion = _messages.StringField(19)
  nextMaintenanceRunId = _messages.StringField(20)
  nextMaintenanceRunTime = _messages.StringField(21)
  nextSecurityMaintenanceRunTime = _messages.StringField(22)
  ociUrl = _messages.StringField(23)
  ocid = _messages.StringField(24)
  shape = _messages.StringField(25)
  state = _messages.EnumField('StateValueValuesEnum', 26)
  storageCount = _messages.IntegerField(27, variant=_messages.Variant.INT32)
  storageServerType = _messages.StringField(28)
  storageServerVersion = _messages.StringField(29)
  totalStorageSizeGb = _messages.IntegerField(30, variant=_messages.Variant.INT32)


class CloudVmCluster(_messages.Message):
  r"""Details of the Cloud VM Cluster resource. https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/CloudVmCluster/

  Messages:
    LabelsValue: Optional. Labels or tags associated with the VM Cluster.

  Fields:
    backupOdbSubnet: Optional. The name of the backup OdbSubnet associated
      with the VM Cluster. Format: projects/{project}/locations/{location}/odb
      Networks/{odb_network}/odbSubnets/{odb_subnet}
    backupSubnetCidr: Optional. CIDR range of the backup subnet.
    cidr: Optional. Network settings. CIDR to use for cluster IP allocation.
    createTime: Output only. The date and time that the VM cluster was
      created.
    displayName: Optional. User friendly name for this resource.
    exadataInfrastructure: Required. The name of the Exadata Infrastructure
      resource on which VM cluster resource is created, in the following
      format: projects/{project}/locations/{region}/cloudExadataInfrastuctures
      /{cloud_extradata_infrastructure}
    gcpOracleZone: Output only. Google Cloud Platform location where Oracle
      Exadata is hosted. It is same as Google Cloud Platform Oracle zone of
      Exadata infrastructure.
    identityConnector: Output only. The identity connector details which will
      allow OCI to securely access the resources in the customer project.
    labels: Optional. Labels or tags associated with the VM Cluster.
    name: Identifier. The name of the VM Cluster resource with the format:
      projects/{project}/locations/{region}/cloudVmClusters/{cloud_vm_cluster}
    network: Optional. The name of the VPC network. Format:
      projects/{project}/global/networks/{network}
    odbNetwork: Optional. The name of the OdbNetwork associated with the VM
      Cluster. Format:
      projects/{project}/locations/{location}/odbNetworks/{odb_network} It is
      optional but if specified, this should match the parent ODBNetwork of
      the odb_subnet and backup_odb_subnet.
    odbSubnet: Optional. The name of the OdbSubnet associated with the VM
      Cluster for IP allocation. Format: projects/{project}/locations/{locatio
      n}/odbNetworks/{odb_network}/odbSubnets/{odb_subnet}
    properties: Optional. Various properties of the VM Cluster.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels or tags associated with the VM Cluster.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backupOdbSubnet = _messages.StringField(1)
  backupSubnetCidr = _messages.StringField(2)
  cidr = _messages.StringField(3)
  createTime = _messages.StringField(4)
  displayName = _messages.StringField(5)
  exadataInfrastructure = _messages.StringField(6)
  gcpOracleZone = _messages.StringField(7)
  identityConnector = _messages.MessageField('IdentityConnector', 8)
  labels = _messages.MessageField('LabelsValue', 9)
  name = _messages.StringField(10)
  network = _messages.StringField(11)
  odbNetwork = _messages.StringField(12)
  odbSubnet = _messages.StringField(13)
  properties = _messages.MessageField('CloudVmClusterProperties', 14)


class CloudVmClusterProperties(_messages.Message):
  r"""Various properties and settings associated with Exadata VM cluster.

  Enums:
    ComputeModelValueValuesEnum: Output only. The compute model of the VM
      Cluster.
    DiskRedundancyValueValuesEnum: Optional. The type of redundancy.
    LicenseTypeValueValuesEnum: Required. License type of VM Cluster.
    StateValueValuesEnum: Output only. State of the cluster.

  Fields:
    clusterName: Optional. OCI Cluster name.
    compartmentId: Output only. Compartment ID of cluster.
    computeModel: Output only. The compute model of the VM Cluster.
    cpuCoreCount: Required. Number of enabled CPU cores.
    dataStorageSizeTb: Optional. The data disk group size to be allocated in
      TBs.
    dbNodeStorageSizeGb: Optional. Local storage per VM.
    dbServerOcids: Optional. OCID of database servers.
    diagnosticsDataCollectionOptions: Optional. Data collection options for
      diagnostics.
    diskRedundancy: Optional. The type of redundancy.
    dnsListenerIp: Output only. DNS listener IP.
    domain: Output only. Parent DNS domain where SCAN DNS and hosts names are
      qualified. ex: ocispdelegated.ocisp10jvnet.oraclevcn.com
    giVersion: Optional. Grid Infrastructure Version.
    hostname: Output only. host name without domain. format: "-" with some
      suffix. ex: sp2-yi0xq where "sp2" is the hostname_prefix.
    hostnamePrefix: Optional. Prefix for VM cluster host names.
    licenseType: Required. License type of VM Cluster.
    localBackupEnabled: Optional. Use local backup.
    memorySizeGb: Optional. Memory allocated in GBs.
    nodeCount: Optional. Number of database servers.
    ociUrl: Output only. Deep link to the OCI console to view this resource.
    ocid: Output only. Oracle Cloud Infrastructure ID of VM Cluster.
    ocpuCount: Optional. OCPU count per VM. Minimum is 0.1.
    scanDns: Output only. SCAN DNS name. ex: sp2-yi0xq-
      scan.ocispdelegated.ocisp10jvnet.oraclevcn.com
    scanDnsRecordId: Output only. OCID of scan DNS record.
    scanIpIds: Output only. OCIDs of scan IPs.
    scanListenerPortTcp: Output only. SCAN listener port - TCP
    scanListenerPortTcpSsl: Output only. SCAN listener port - TLS
    shape: Output only. Shape of VM Cluster.
    sparseDiskgroupEnabled: Optional. Use exadata sparse snapshots.
    sshPublicKeys: Optional. SSH public keys to be stored with cluster.
    state: Output only. State of the cluster.
    storageSizeGb: Output only. The storage allocation for the disk group, in
      gigabytes (GB).
    systemVersion: Optional. Operating system version of the image.
    timeZone: Optional. Time zone of VM Cluster to set. Defaults to UTC if not
      specified.
  """

  class ComputeModelValueValuesEnum(_messages.Enum):
    r"""Output only. The compute model of the VM Cluster.

    Values:
      COMPUTE_MODEL_UNSPECIFIED: Unspecified compute model.
      COMPUTE_MODEL_ECPU: Abstract measure of compute resources. ECPUs are
        based on the number of cores elastically allocated from a pool of
        compute and storage servers.
      COMPUTE_MODEL_OCPU: Physical measure of compute resources. OCPUs are
        based on the physical core of a processor.
    """
    COMPUTE_MODEL_UNSPECIFIED = 0
    COMPUTE_MODEL_ECPU = 1
    COMPUTE_MODEL_OCPU = 2

  class DiskRedundancyValueValuesEnum(_messages.Enum):
    r"""Optional. The type of redundancy.

    Values:
      DISK_REDUNDANCY_UNSPECIFIED: Unspecified.
      HIGH: High - 3 way mirror.
      NORMAL: Normal - 2 way mirror.
    """
    DISK_REDUNDANCY_UNSPECIFIED = 0
    HIGH = 1
    NORMAL = 2

  class LicenseTypeValueValuesEnum(_messages.Enum):
    r"""Required. License type of VM Cluster.

    Values:
      LICENSE_TYPE_UNSPECIFIED: Unspecified
      LICENSE_INCLUDED: License included part of offer
      BRING_YOUR_OWN_LICENSE: Bring your own license
    """
    LICENSE_TYPE_UNSPECIFIED = 0
    LICENSE_INCLUDED = 1
    BRING_YOUR_OWN_LICENSE = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the cluster.

    Values:
      STATE_UNSPECIFIED: Default unspecified value.
      PROVISIONING: Indicates that the resource is in provisioning state.
      AVAILABLE: Indicates that the resource is in available state.
      UPDATING: Indicates that the resource is in updating state.
      TERMINATING: Indicates that the resource is in terminating state.
      TERMINATED: Indicates that the resource is in terminated state.
      FAILED: Indicates that the resource is in failed state.
      MAINTENANCE_IN_PROGRESS: Indicates that the resource is in maintenance
        in progress state.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    AVAILABLE = 2
    UPDATING = 3
    TERMINATING = 4
    TERMINATED = 5
    FAILED = 6
    MAINTENANCE_IN_PROGRESS = 7

  clusterName = _messages.StringField(1)
  compartmentId = _messages.StringField(2)
  computeModel = _messages.EnumField('ComputeModelValueValuesEnum', 3)
  cpuCoreCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  dataStorageSizeTb = _messages.FloatField(5)
  dbNodeStorageSizeGb = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  dbServerOcids = _messages.StringField(7, repeated=True)
  diagnosticsDataCollectionOptions = _messages.MessageField('DataCollectionOptions', 8)
  diskRedundancy = _messages.EnumField('DiskRedundancyValueValuesEnum', 9)
  dnsListenerIp = _messages.StringField(10)
  domain = _messages.StringField(11)
  giVersion = _messages.StringField(12)
  hostname = _messages.StringField(13)
  hostnamePrefix = _messages.StringField(14)
  licenseType = _messages.EnumField('LicenseTypeValueValuesEnum', 15)
  localBackupEnabled = _messages.BooleanField(16)
  memorySizeGb = _messages.IntegerField(17, variant=_messages.Variant.INT32)
  nodeCount = _messages.IntegerField(18, variant=_messages.Variant.INT32)
  ociUrl = _messages.StringField(19)
  ocid = _messages.StringField(20)
  ocpuCount = _messages.FloatField(21, variant=_messages.Variant.FLOAT)
  scanDns = _messages.StringField(22)
  scanDnsRecordId = _messages.StringField(23)
  scanIpIds = _messages.StringField(24, repeated=True)
  scanListenerPortTcp = _messages.IntegerField(25, variant=_messages.Variant.INT32)
  scanListenerPortTcpSsl = _messages.IntegerField(26, variant=_messages.Variant.INT32)
  shape = _messages.StringField(27)
  sparseDiskgroupEnabled = _messages.BooleanField(28)
  sshPublicKeys = _messages.StringField(29, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 30)
  storageSizeGb = _messages.IntegerField(31, variant=_messages.Variant.INT32)
  systemVersion = _messages.StringField(32)
  timeZone = _messages.MessageField('TimeZone', 33)


class CustomerContact(_messages.Message):
  r"""The CustomerContact reference as defined by Oracle.
  https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/datatypes/CustomerContact

  Fields:
    email: Required. The email address used by Oracle to send notifications
      regarding databases and infrastructure.
  """

  email = _messages.StringField(1)


class DataCollectionOptions(_messages.Message):
  r"""Data collection options for diagnostics.

  Fields:
    diagnosticsEventsEnabled: Optional. Indicates whether diagnostic
      collection is enabled for the VM cluster
    healthMonitoringEnabled: Optional. Indicates whether health monitoring is
      enabled for the VM cluster
    incidentLogsEnabled: Optional. Indicates whether incident logs and trace
      collection are enabled for the VM cluster
  """

  diagnosticsEventsEnabled = _messages.BooleanField(1)
  healthMonitoringEnabled = _messages.BooleanField(2)
  incidentLogsEnabled = _messages.BooleanField(3)


class DataCollectionOptionsCommon(_messages.Message):
  r"""Data collection options for diagnostics. https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/datatypes/DataCollectionOptions

  Fields:
    isDiagnosticsEventsEnabled: Optional. Indicates whether to enable data
      collection for diagnostics.
    isHealthMonitoringEnabled: Optional. Indicates whether to enable health
      monitoring.
    isIncidentLogsEnabled: Optional. Indicates whether to enable incident logs
      and trace collection.
  """

  isDiagnosticsEventsEnabled = _messages.BooleanField(1)
  isHealthMonitoringEnabled = _messages.BooleanField(2)
  isIncidentLogsEnabled = _messages.BooleanField(3)


class DatabaseConnectionStringProfile(_messages.Message):
  r"""The connection string profile to allow clients to group.
  https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/datatypes/DatabaseConnectionStringProfile

  Enums:
    ConsumerGroupValueValuesEnum: Output only. The current consumer group
      being used by the connection.
    HostFormatValueValuesEnum: Output only. The host name format being
      currently used in connection string.
    ProtocolValueValuesEnum: Output only. The protocol being used by the
      connection.
    SessionModeValueValuesEnum: Output only. The current session mode of the
      connection.
    SyntaxFormatValueValuesEnum: Output only. The syntax of the connection
      string.
    TlsAuthenticationValueValuesEnum: Output only. This field indicates the
      TLS authentication type of the connection.

  Fields:
    consumerGroup: Output only. The current consumer group being used by the
      connection.
    displayName: Output only. The display name for the database connection.
    hostFormat: Output only. The host name format being currently used in
      connection string.
    isRegional: Output only. This field indicates if the connection string is
      regional and is only applicable for cross-region Data Guard.
    protocol: Output only. The protocol being used by the connection.
    sessionMode: Output only. The current session mode of the connection.
    syntaxFormat: Output only. The syntax of the connection string.
    tlsAuthentication: Output only. This field indicates the TLS
      authentication type of the connection.
    value: Output only. The value of the connection string.
  """

  class ConsumerGroupValueValuesEnum(_messages.Enum):
    r"""Output only. The current consumer group being used by the connection.

    Values:
      CONSUMER_GROUP_UNSPECIFIED: Default unspecified value.
      HIGH: High consumer group.
      MEDIUM: Medium consumer group.
      LOW: Low consumer group.
      TP: TP consumer group.
      TPURGENT: TPURGENT consumer group.
    """
    CONSUMER_GROUP_UNSPECIFIED = 0
    HIGH = 1
    MEDIUM = 2
    LOW = 3
    TP = 4
    TPURGENT = 5

  class HostFormatValueValuesEnum(_messages.Enum):
    r"""Output only. The host name format being currently used in connection
    string.

    Values:
      HOST_FORMAT_UNSPECIFIED: Default unspecified value.
      FQDN: FQDN
      IP: IP
    """
    HOST_FORMAT_UNSPECIFIED = 0
    FQDN = 1
    IP = 2

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""Output only. The protocol being used by the connection.

    Values:
      PROTOCOL_UNSPECIFIED: Default unspecified value.
      TCP: Tcp
      TCPS: Tcps
    """
    PROTOCOL_UNSPECIFIED = 0
    TCP = 1
    TCPS = 2

  class SessionModeValueValuesEnum(_messages.Enum):
    r"""Output only. The current session mode of the connection.

    Values:
      SESSION_MODE_UNSPECIFIED: Default unspecified value.
      DIRECT: Direct
      INDIRECT: Indirect
    """
    SESSION_MODE_UNSPECIFIED = 0
    DIRECT = 1
    INDIRECT = 2

  class SyntaxFormatValueValuesEnum(_messages.Enum):
    r"""Output only. The syntax of the connection string.

    Values:
      SYNTAX_FORMAT_UNSPECIFIED: Default unspecified value.
      LONG: Long
      EZCONNECT: Ezconnect
      EZCONNECTPLUS: Ezconnectplus
    """
    SYNTAX_FORMAT_UNSPECIFIED = 0
    LONG = 1
    EZCONNECT = 2
    EZCONNECTPLUS = 3

  class TlsAuthenticationValueValuesEnum(_messages.Enum):
    r"""Output only. This field indicates the TLS authentication type of the
    connection.

    Values:
      TLS_AUTHENTICATION_UNSPECIFIED: Default unspecified value.
      SERVER: Server
      MUTUAL: Mutual
    """
    TLS_AUTHENTICATION_UNSPECIFIED = 0
    SERVER = 1
    MUTUAL = 2

  consumerGroup = _messages.EnumField('ConsumerGroupValueValuesEnum', 1)
  displayName = _messages.StringField(2)
  hostFormat = _messages.EnumField('HostFormatValueValuesEnum', 3)
  isRegional = _messages.BooleanField(4)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 5)
  sessionMode = _messages.EnumField('SessionModeValueValuesEnum', 6)
  syntaxFormat = _messages.EnumField('SyntaxFormatValueValuesEnum', 7)
  tlsAuthentication = _messages.EnumField('TlsAuthenticationValueValuesEnum', 8)
  value = _messages.StringField(9)


class DbNode(_messages.Message):
  r"""Details of the database node resource. https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/DbNode/

  Fields:
    name: Identifier. The name of the database node resource in the following
      format: projects/{project}/locations/{location}/cloudVmClusters/{cloud_v
      m_cluster}/dbNodes/{db_node}
    properties: Optional. Various properties of the database node.
  """

  name = _messages.StringField(1)
  properties = _messages.MessageField('DbNodeProperties', 2)


class DbNodeProperties(_messages.Message):
  r"""Various properties and settings associated with Db node.

  Enums:
    StateValueValuesEnum: Output only. State of the database node.

  Fields:
    dbNodeStorageSizeGb: Optional. Local storage per database node.
    dbServerOcid: Optional. Database server OCID.
    hostname: Optional. DNS
    memorySizeGb: Memory allocated in GBs.
    ocid: Output only. OCID of database node.
    ocpuCount: Optional. OCPU count per database node.
    state: Output only. State of the database node.
    totalCpuCoreCount: Total CPU core count of the database node.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the database node.

    Values:
      STATE_UNSPECIFIED: Default unspecified value.
      PROVISIONING: Indicates that the resource is in provisioning state.
      AVAILABLE: Indicates that the resource is in available state.
      UPDATING: Indicates that the resource is in updating state.
      STOPPING: Indicates that the resource is in stopping state.
      STOPPED: Indicates that the resource is in stopped state.
      STARTING: Indicates that the resource is in starting state.
      TERMINATING: Indicates that the resource is in terminating state.
      TERMINATED: Indicates that the resource is in terminated state.
      FAILED: Indicates that the resource is in failed state.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    AVAILABLE = 2
    UPDATING = 3
    STOPPING = 4
    STOPPED = 5
    STARTING = 6
    TERMINATING = 7
    TERMINATED = 8
    FAILED = 9

  dbNodeStorageSizeGb = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  dbServerOcid = _messages.StringField(2)
  hostname = _messages.StringField(3)
  memorySizeGb = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  ocid = _messages.StringField(5)
  ocpuCount = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  totalCpuCoreCount = _messages.IntegerField(8, variant=_messages.Variant.INT32)


class DbServer(_messages.Message):
  r"""Details of the database server resource. https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/DbServer/

  Fields:
    displayName: Optional. User friendly name for this resource.
    name: Identifier. The name of the database server resource with the
      format: projects/{project}/locations/{location}/cloudExadataInfrastructu
      res/{cloud_exadata_infrastructure}/dbServers/{db_server}
    properties: Optional. Various properties of the database server.
  """

  displayName = _messages.StringField(1)
  name = _messages.StringField(2)
  properties = _messages.MessageField('DbServerProperties', 3)


class DbServerProperties(_messages.Message):
  r"""Various properties and settings associated with Exadata database server.

  Enums:
    StateValueValuesEnum: Output only. State of the database server.

  Fields:
    dbNodeIds: Output only. OCID of database nodes associated with the
      database server.
    dbNodeStorageSizeGb: Optional. Local storage per VM.
    maxDbNodeStorageSizeGb: Optional. Maximum local storage per VM.
    maxMemorySizeGb: Optional. Maximum memory allocated in GBs.
    maxOcpuCount: Optional. Maximum OCPU count per database.
    memorySizeGb: Optional. Memory allocated in GBs.
    ocid: Output only. OCID of database server.
    ocpuCount: Optional. OCPU count per database.
    state: Output only. State of the database server.
    vmCount: Optional. Vm count per database.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the database server.

    Values:
      STATE_UNSPECIFIED: Default unspecified value.
      CREATING: Indicates that the resource is in creating state.
      AVAILABLE: Indicates that the resource is in available state.
      UNAVAILABLE: Indicates that the resource is in unavailable state.
      DELETING: Indicates that the resource is in deleting state.
      DELETED: Indicates that the resource is in deleted state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    AVAILABLE = 2
    UNAVAILABLE = 3
    DELETING = 4
    DELETED = 5

  dbNodeIds = _messages.StringField(1, repeated=True)
  dbNodeStorageSizeGb = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  maxDbNodeStorageSizeGb = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  maxMemorySizeGb = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  maxOcpuCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  memorySizeGb = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  ocid = _messages.StringField(7)
  ocpuCount = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  vmCount = _messages.IntegerField(10, variant=_messages.Variant.INT32)


class DbSystemShape(_messages.Message):
  r"""Details of the Database System Shapes resource.
  https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/DbSystemShapeSummary/

  Fields:
    availableCoreCountPerNode: Optional. Number of cores per node.
    availableDataStorageTb: Optional. Storage per storage server in terabytes.
    availableMemoryPerNodeGb: Optional. Memory per database server node in
      gigabytes.
    maxNodeCount: Optional. Maximum number of database servers.
    maxStorageCount: Optional. Maximum number of storage servers.
    minCoreCountPerNode: Optional. Minimum core count per node.
    minDbNodeStoragePerNodeGb: Optional. Minimum node storage per database
      server in gigabytes.
    minMemoryPerNodeGb: Optional. Minimum memory per node in gigabytes.
    minNodeCount: Optional. Minimum number of database servers.
    minStorageCount: Optional. Minimum number of storage servers.
    name: Identifier. The name of the Database System Shape resource with the
      format:
      projects/{project}/locations/{region}/dbSystemShapes/{db_system_shape}
    shape: Optional. shape
  """

  availableCoreCountPerNode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  availableDataStorageTb = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  availableMemoryPerNodeGb = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  maxNodeCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  maxStorageCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  minCoreCountPerNode = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  minDbNodeStoragePerNodeGb = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  minMemoryPerNodeGb = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  minNodeCount = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  minStorageCount = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  name = _messages.StringField(11)
  shape = _messages.StringField(12)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionKey(_messages.Message):
  r"""The encryption key used to encrypt the Autonomous Database.

  Enums:
    ProviderValueValuesEnum: Optional. The provider of the encryption key.

  Fields:
    kmsKey: Optional. The KMS key used to encrypt the Autonomous Database.
      This field is required if the provider is GOOGLE_MANAGED. The name of
      the KMS key resource in the following format: `projects/{project}/locati
      ons/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}`.
    provider: Optional. The provider of the encryption key.
  """

  class ProviderValueValuesEnum(_messages.Enum):
    r"""Optional. The provider of the encryption key.

    Values:
      PROVIDER_UNSPECIFIED: Default unspecified value.
      GOOGLE_MANAGED: Google Managed KMS key, if selected, please provide the
        KMS key name.
      ORACLE_MANAGED: Oracle Managed.
    """
    PROVIDER_UNSPECIFIED = 0
    GOOGLE_MANAGED = 1
    ORACLE_MANAGED = 2

  kmsKey = _messages.StringField(1)
  provider = _messages.EnumField('ProviderValueValuesEnum', 2)


class EncryptionKeyHistoryEntry(_messages.Message):
  r"""The history of the encryption keys used to encrypt the Autonomous
  Database.

  Fields:
    activationTime: Output only. The date and time when the encryption key was
      activated on the Autonomous Database..
    encryptionKey: Output only. The encryption key used to encrypt the
      Autonomous Database.
  """

  activationTime = _messages.StringField(1)
  encryptionKey = _messages.MessageField('EncryptionKey', 2)


class Entitlement(_messages.Message):
  r"""Details of the Entitlement resource.

  Enums:
    StateValueValuesEnum: Output only. Entitlement State.

  Fields:
    cloudAccountDetails: Details of the OCI Cloud Account.
    entitlementId: Output only. Google Cloud Marketplace order ID (aka
      entitlement ID)
    name: Identifier. The name of the Entitlement resource with the format:
      projects/{project}/locations/{region}/entitlements/{entitlement}
    state: Output only. Entitlement State.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Entitlement State.

    Values:
      STATE_UNSPECIFIED: Default unspecified value.
      ACCOUNT_NOT_LINKED: Account not linked.
      ACCOUNT_NOT_ACTIVE: Account is linked but not active.
      ACTIVE: Entitlement and Account are active.
      ACCOUNT_SUSPENDED: Account is suspended.
      NOT_APPROVED_IN_PRIVATE_MARKETPLACE: Entitlement is not approved in
        private marketplace.
    """
    STATE_UNSPECIFIED = 0
    ACCOUNT_NOT_LINKED = 1
    ACCOUNT_NOT_ACTIVE = 2
    ACTIVE = 3
    ACCOUNT_SUSPENDED = 4
    NOT_APPROVED_IN_PRIVATE_MARKETPLACE = 5

  cloudAccountDetails = _messages.MessageField('CloudAccountDetails', 1)
  entitlementId = _messages.StringField(2)
  name = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class ExadbVmCluster(_messages.Message):
  r"""ExadbVmCluster represents a cluster of VMs that are used to run Exadata
  workloads. https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/ExadbVmCluster/

  Messages:
    LabelsValue: Optional. The labels or tags associated with the
      ExadbVmCluster.

  Fields:
    backupOdbSubnet: Required. The name of the backup OdbSubnet associated
      with the ExadbVmCluster. Format: projects/{project}/locations/{location}
      /odbNetworks/{odb_network}/odbSubnets/{odb_subnet}
    createTime: Output only. The date and time that the ExadbVmCluster was
      created.
    displayName: Required. The display name for the ExadbVmCluster. The name
      does not have to be unique within your project. The name must be 1-255
      characters long and can only contain alphanumeric characters.
    entitlementId: Output only. The ID of the subscription entitlement
      associated with the ExadbVmCluster.
    gcpOracleZone: Optional. Google Cloud Platform location where Oracle Exadb
      vm cluster is hosted. Example: us-east4-b-r1, us-central1-a.
    labels: Optional. The labels or tags associated with the ExadbVmCluster.
    name: Identifier. The name of the ExadbVmCluster resource in the following
      format:
      projects/{project}/locations/{region}/exadbVmClusters/{exadb_vm_cluster}
    odbNetwork: Optional. The name of the OdbNetwork associated with the
      ExadbVmCluster. Format:
      projects/{project}/locations/{location}/odbNetworks/{odb_network} It is
      optional but if specified, this should match the parent ODBNetwork of
      the OdbSubnet.
    odbSubnet: Required. The name of the OdbSubnet associated with the
      ExadbVmCluster for IP allocation. Format: projects/{project}/locations/{
      location}/odbNetworks/{odb_network}/odbSubnets/{odb_subnet}
    properties: Optional. The properties of the ExadbVmCluster.
    storageVaultProperties: Optional. The properties of the storage vault
      associated with the ExadbVmCluster.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels or tags associated with the ExadbVmCluster.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backupOdbSubnet = _messages.StringField(1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  entitlementId = _messages.StringField(4)
  gcpOracleZone = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  odbNetwork = _messages.StringField(8)
  odbSubnet = _messages.StringField(9)
  properties = _messages.MessageField('ExadbVmClusterProperties', 10)
  storageVaultProperties = _messages.MessageField('StorageVaultProperties', 11)


class ExadbVmClusterProperties(_messages.Message):
  r"""The properties of an ExadbVmCluster.

  Enums:
    LicenseModelValueValuesEnum: Required. The license type of the
      ExadbVmCluster.
    LifecycleStateValueValuesEnum: Output only. State of the cluster.
    ShapeAttributeValueValuesEnum: Required. The shape attribute of the VM
      cluster. The type of Exascale storage used for Exadata VM cluster. The
      default is SMART_STORAGE which supports Oracle Database 23ai and later

  Fields:
    clusterName: Required. The cluster name for Exascale vm cluster. The
      cluster name must begin with an alphabetic character and may contain
      hyphens(-) but can not contain underscores(_). It should be not more
      than 11 characters and is not case sensitive. OCI Cluster name.
    dataCollectionOptions: Optional. Indicates user preference for data
      collection options.
    enabledEcpuCount: Required. The number of ECPUs enabled for an exadata vm
      cluster on exascale infrastructure.
    exascaleDbStorageVaultId: Optional. The OCID for exadata database storage
      vault.
    gridImageId: Required. Grid Infrastructure Version.
    hostname: Output only. The hostname of the ExadbVmCluster.
    hostnamePrefix: Optional. Prefix for VM cluster host names.
    licenseModel: Required. The license type of the ExadbVmCluster.
    lifecycleState: Output only. State of the cluster.
    memorySizeGb: Output only. Memory per VM (GB) (Read-only): Shows the
      amount of memory allocated to each VM. Memory is calculated based on
      2.75 GB per Total ECPUs.
    nodeCount: Required. The number of nodes/VMs in the ExadbVmCluster.
    ociUri: Output only. Deep link to the OCI console to view this resource.
    scanListenerPortTcp: Optional. SCAN listener port - TCP
    shapeAttribute: Required. The shape attribute of the VM cluster. The type
      of Exascale storage used for Exadata VM cluster. The default is
      SMART_STORAGE which supports Oracle Database 23ai and later
    sshPublicKeys: Required. The SSH public keys for the ExadbVmCluster.
    timeZone: Optional. The time zone of the ExadbVmCluster.
    totalEcpuCount: Required. The total number of ECPUs available (enabled +
      reserved) for an exadata vm cluster on exascale infrastructure.
    vmFileSystemStorage: Required. Total storage details for the
      ExadbVmCluster.
  """

  class LicenseModelValueValuesEnum(_messages.Enum):
    r"""Required. The license type of the ExadbVmCluster.

    Values:
      LICENSE_MODEL_UNSPECIFIED: Unspecified.
      LICENSE_INCLUDED: Default is license included.
      BRING_YOUR_OWN_LICENSE: Bring your own license.
    """
    LICENSE_MODEL_UNSPECIFIED = 0
    LICENSE_INCLUDED = 1
    BRING_YOUR_OWN_LICENSE = 2

  class LifecycleStateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the cluster.

    Values:
      EXADB_VM_CLUSTER_LIFECYCLE_STATE_UNSPECIFIED: Default unspecified value.
      PROVISIONING: Indicates that the resource is in provisioning state.
      AVAILABLE: Indicates that the resource is in available state.
      UPDATING: Indicates that the resource is in updating state.
      TERMINATING: Indicates that the resource is in terminating state.
      TERMINATED: Indicates that the resource is in terminated state.
      FAILED: Indicates that the resource is in failed state.
      MAINTENANCE_IN_PROGRESS: Indicates that the resource is in maintenance
        in progress state.
    """
    EXADB_VM_CLUSTER_LIFECYCLE_STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    AVAILABLE = 2
    UPDATING = 3
    TERMINATING = 4
    TERMINATED = 5
    FAILED = 6
    MAINTENANCE_IN_PROGRESS = 7

  class ShapeAttributeValueValuesEnum(_messages.Enum):
    r"""Required. The shape attribute of the VM cluster. The type of Exascale
    storage used for Exadata VM cluster. The default is SMART_STORAGE which
    supports Oracle Database 23ai and later

    Values:
      SHAPE_ATTRIBUTE_UNSPECIFIED: Default unspecified value.
      SMART_STORAGE: Indicates that the resource is in smart storage.
      BLOCK_STORAGE: Indicates that the resource is in block storage.
    """
    SHAPE_ATTRIBUTE_UNSPECIFIED = 0
    SMART_STORAGE = 1
    BLOCK_STORAGE = 2

  clusterName = _messages.StringField(1)
  dataCollectionOptions = _messages.MessageField('DataCollectionOptionsCommon', 2)
  enabledEcpuCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  exascaleDbStorageVaultId = _messages.StringField(4)
  gridImageId = _messages.StringField(5)
  hostname = _messages.StringField(6)
  hostnamePrefix = _messages.StringField(7)
  licenseModel = _messages.EnumField('LicenseModelValueValuesEnum', 8)
  lifecycleState = _messages.EnumField('LifecycleStateValueValuesEnum', 9)
  memorySizeGb = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  nodeCount = _messages.IntegerField(11, variant=_messages.Variant.INT32)
  ociUri = _messages.StringField(12)
  scanListenerPortTcp = _messages.IntegerField(13, variant=_messages.Variant.INT32)
  shapeAttribute = _messages.EnumField('ShapeAttributeValueValuesEnum', 14)
  sshPublicKeys = _messages.StringField(15, repeated=True)
  timeZone = _messages.MessageField('TimeZone', 16)
  totalEcpuCount = _messages.IntegerField(17, variant=_messages.Variant.INT32)
  vmFileSystemStorage = _messages.MessageField('ExadbVmClusterStorageDetails', 18)


class ExadbVmClusterStorageDetails(_messages.Message):
  r"""The storage allocation for the exadbvmcluster, in gigabytes (GB).

  Fields:
    totalSizeInGbs: Optional. The storage allocation for the exadbvmcluster,
      in gigabytes (GB).
  """

  totalSizeInGbs = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class ExascaleDbStorageDetails(_messages.Message):
  r"""The storage details of the ExascaleDbStorageVault.

  Fields:
    availableSizeGbs: Optional. The available storage capacity for the
      ExascaleDbStorageVault, in gigabytes (GB).
    totalSizeGbs: Optional. The total storage allocation for the
      ExascaleDbStorageVault, in gigabytes (GB).
  """

  availableSizeGbs = _messages.IntegerField(1)
  totalSizeGbs = _messages.IntegerField(2)


class ExascaleDbStorageVault(_messages.Message):
  r"""ExascaleDbStorageVault represents a storage vault exadb vm cluster
  resource. https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/ExascaleDbStorageVault/

  Fields:
    createTime: Output only. The date and time when the ExascaleDbStorageVault
      was created.
    displayName: Required. The display name for the ExascaleDbStorageVault.
      The name does not have to be unique within your project. The name must
      be 1-255 characters long and can only contain alphanumeric characters.
    entitlementId: Output only. The ID of the subscription entitlement
      associated with the ExascaleDbStorageVault.
    gcpOracleZone: Required. The zone where Oracle ExascaleDbStorageVault is
      hosted. It is an isolated area within a location, designed for single
      point of failure. Example: us-east4-b-r1, us-central1-a.
    name: Identifier. The resource name of the ExascaleDbStorageVault. Format:
      projects/{project}/locations/{location}/exascaleDbStorageVaults/{exascal
      e_db_storage_vault}
    properties: Required. The properties of the ExascaleDbStorageVault.
  """

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  entitlementId = _messages.StringField(3)
  gcpOracleZone = _messages.StringField(4)
  name = _messages.StringField(5)
  properties = _messages.MessageField('ExascaleDbStorageVaultProperties', 6)


class ExascaleDbStorageVaultProperties(_messages.Message):
  r"""The properties of the ExascaleDbStorageVault.

  Enums:
    StateValueValuesEnum: Output only. The state of the
      ExascaleDbStorageVault.

  Fields:
    additionalFlashCachePercent: Optional. The size of additional flash cache
      in percentage of high capacity database storage.
    description: Optional. The description of the ExascaleDbStorageVault.
    exascaleDbStorageDetails: Optional. The storage details of the
      ExascaleDbStorageVault.
    ociUri: Output only. Deep link to the OCI console to view this resource.
    ocid: Output only. The OCID for the ExascaleDbStorageVault.
    state: Output only. The state of the ExascaleDbStorageVault.
    timeZone: Optional. The time zone of the ExascaleDbStorageVault.
    vmClusterIds: Output only. The list of VM cluster OCIDs associated with
      the ExascaleDbStorageVault.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the ExascaleDbStorageVault.

    Values:
      STATE_UNSPECIFIED: The state of the ExascaleDbStorageVault is
        unspecified.
      PROVISIONING: The ExascaleDbStorageVault is being provisioned.
      AVAILABLE: The ExascaleDbStorageVault is available.
      UPDATING: The ExascaleDbStorageVault is being updated.
      TERMINATING: The ExascaleDbStorageVault is being deleted.
      TERMINATED: The ExascaleDbStorageVault has been deleted.
      FAILED: The ExascaleDbStorageVault has failed.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    AVAILABLE = 2
    UPDATING = 3
    TERMINATING = 4
    TERMINATED = 5
    FAILED = 6

  additionalFlashCachePercent = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  description = _messages.StringField(2)
  exascaleDbStorageDetails = _messages.MessageField('ExascaleDbStorageDetails', 3)
  ociUri = _messages.StringField(4)
  ocid = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  timeZone = _messages.MessageField('TimeZone', 7)
  vmClusterIds = _messages.StringField(8, repeated=True)


class GenerateAutonomousDatabaseWalletRequest(_messages.Message):
  r"""The request for `AutonomousDatabase.GenerateWallet`.

  Enums:
    TypeValueValuesEnum: Optional. The type of wallet generation for the
      Autonomous Database. The default value is SINGLE.

  Fields:
    isRegional: Optional. True when requesting regional connection strings in
      PDB connect info, applicable to cross-region Data Guard only.
    password: Required. The password used to encrypt the keys inside the
      wallet. The password must be a minimum of 8 characters.
    type: Optional. The type of wallet generation for the Autonomous Database.
      The default value is SINGLE.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of wallet generation for the Autonomous Database.
    The default value is SINGLE.

    Values:
      GENERATE_TYPE_UNSPECIFIED: Default unspecified value.
      ALL: Used to generate wallet for all databases in the region.
      SINGLE: Used to generate wallet for a single database.
    """
    GENERATE_TYPE_UNSPECIFIED = 0
    ALL = 1
    SINGLE = 2

  isRegional = _messages.BooleanField(1)
  password = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class GenerateAutonomousDatabaseWalletResponse(_messages.Message):
  r"""The response for `AutonomousDatabase.GenerateWallet`.

  Fields:
    archiveContent: Output only. The base64 encoded wallet files.
  """

  archiveContent = _messages.BytesField(1)


class GiVersion(_messages.Message):
  r"""Details of the Oracle Grid Infrastructure (GI) version resource.
  https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/GiVersionSummary/

  Fields:
    name: Identifier. The name of the Oracle Grid Infrastructure (GI) version
      resource with the format:
      projects/{project}/locations/{region}/giVersions/{gi_versions}
    version: Optional. version
  """

  name = _messages.StringField(1)
  version = _messages.StringField(2)


class IdentityConnector(_messages.Message):
  r"""The identity connector details which will allow OCI to securely access
  the resources in the customer project.

  Enums:
    ConnectionStateValueValuesEnum: Output only. The connection state of the
      identity connector.

  Fields:
    connectionState: Output only. The connection state of the identity
      connector.
    serviceAgentEmail: Output only. A google managed service account on which
      customers can grant roles to access resources in the customer project.
      Example: `p176944527254-55-75119d87fd8f@gcp-sa-
      oci.iam.gserviceaccount.com`
  """

  class ConnectionStateValueValuesEnum(_messages.Enum):
    r"""Output only. The connection state of the identity connector.

    Values:
      CONNECTION_STATE_UNSPECIFIED: Default unspecified value.
      CONNECTED: The identity pool connection is connected.
      PARTIALLY_CONNECTED: The identity pool connection is partially
        connected.
      DISCONNECTED: The identity pool connection is disconnected.
      UNKNOWN: The identity pool connection is in an unknown state.
    """
    CONNECTION_STATE_UNSPECIFIED = 0
    CONNECTED = 1
    PARTIALLY_CONNECTED = 2
    DISCONNECTED = 3
    UNKNOWN = 4

  connectionState = _messages.EnumField('ConnectionStateValueValuesEnum', 1)
  serviceAgentEmail = _messages.StringField(2)


class ListAutonomousDatabaseBackupsResponse(_messages.Message):
  r"""The response for `AutonomousDatabaseBackup.List`.

  Fields:
    autonomousDatabaseBackups: The list of Autonomous Database Backups.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  autonomousDatabaseBackups = _messages.MessageField('AutonomousDatabaseBackup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAutonomousDatabaseCharacterSetsResponse(_messages.Message):
  r"""The response for `AutonomousDatabaseCharacterSet.List`.

  Fields:
    autonomousDatabaseCharacterSets: The list of Autonomous Database Character
      Sets.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  autonomousDatabaseCharacterSets = _messages.MessageField('AutonomousDatabaseCharacterSet', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAutonomousDatabasesResponse(_messages.Message):
  r"""The response for `AutonomousDatabase.List`.

  Fields:
    autonomousDatabases: The list of Autonomous Databases.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  autonomousDatabases = _messages.MessageField('AutonomousDatabase', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAutonomousDbVersionsResponse(_messages.Message):
  r"""The response for `AutonomousDbVersion.List`.

  Fields:
    autonomousDbVersions: The list of Autonomous Database versions.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  autonomousDbVersions = _messages.MessageField('AutonomousDbVersion', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListCloudExadataInfrastructuresResponse(_messages.Message):
  r"""The response for `CloudExadataInfrastructures.list`.

  Fields:
    cloudExadataInfrastructures: The list of Exadata Infrastructures.
    nextPageToken: A token for fetching next page of response.
  """

  cloudExadataInfrastructures = _messages.MessageField('CloudExadataInfrastructure', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListCloudVmClustersResponse(_messages.Message):
  r"""The response for `CloudVmCluster.List`.

  Fields:
    cloudVmClusters: The list of VM Clusters.
    nextPageToken: A token to fetch the next page of results.
  """

  cloudVmClusters = _messages.MessageField('CloudVmCluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDbNodesResponse(_messages.Message):
  r"""The response for `DbNode.List`.

  Fields:
    dbNodes: The list of DB Nodes
    nextPageToken: A token identifying a page of results the node should
      return.
  """

  dbNodes = _messages.MessageField('DbNode', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDbServersResponse(_messages.Message):
  r"""The response for `DbServer.List`.

  Fields:
    dbServers: The list of database servers.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  dbServers = _messages.MessageField('DbServer', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDbSystemShapesResponse(_messages.Message):
  r"""The response for `DbSystemShape.List`.

  Fields:
    dbSystemShapes: The list of Database System shapes.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  dbSystemShapes = _messages.MessageField('DbSystemShape', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListEntitlementsResponse(_messages.Message):
  r"""The response for `Entitlement.List`.

  Fields:
    entitlements: The list of Entitlements
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  entitlements = _messages.MessageField('Entitlement', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListExadbVmClustersResponse(_messages.Message):
  r"""The response for `ExadbVmCluster.List`.

  Fields:
    exadbVmClusters: The list of ExadbVmClusters.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  exadbVmClusters = _messages.MessageField('ExadbVmCluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListExascaleDbStorageVaultsResponse(_messages.Message):
  r"""The response for `ExascaleDbStorageVault.List`.

  Fields:
    exascaleDbStorageVaults: The ExascaleDbStorageVaults.
    nextPageToken: A token identifying a page of results the server should
      return. If present, the next page token can be provided to a subsequent
      ListExascaleDbStorageVaults call to list the next page. If empty, there
      are no more pages.
  """

  exascaleDbStorageVaults = _messages.MessageField('ExascaleDbStorageVault', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListGiVersionsResponse(_messages.Message):
  r"""The response for `GiVersion.List`.

  Fields:
    giVersions: The list of Oracle Grid Infrastructure (GI) versions.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  giVersions = _messages.MessageField('GiVersion', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMinorVersionsResponse(_messages.Message):
  r"""The response for `MinorVersion.List`.

  Fields:
    minorVersions: The list of MinorVersions.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  minorVersions = _messages.MessageField('MinorVersion', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOdbNetworksResponse(_messages.Message):
  r"""The response for `OdbNetwork.List`.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    odbNetworks: The list of ODB Networks.
    unreachable: Unreachable locations when listing resources across all
      locations using wildcard location '-'.
  """

  nextPageToken = _messages.StringField(1)
  odbNetworks = _messages.MessageField('OdbNetwork', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListOdbSubnetsResponse(_messages.Message):
  r"""The response for `OdbSubnet.List`.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    odbSubnets: The list of ODB Subnets.
    unreachable: Unreachable locations when listing resources across all
      locations using wildcard location '-'.
  """

  nextPageToken = _messages.StringField(1)
  odbSubnets = _messages.MessageField('OdbSubnet', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListSystemVersionsResponse(_messages.Message):
  r"""The response for `SystemVersion.List`.

  Fields:
    nextPageToken: A token identifying the next page of results to retrieve.
      If empty/omitted, there are no more pages to retrieve.
    systemVersions: The list of System Versions.
  """

  nextPageToken = _messages.StringField(1)
  systemVersions = _messages.MessageField('SystemVersion', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LocationMetadata(_messages.Message):
  r"""Metadata for a given Location.

  Fields:
    gcpOracleZones: Output only. Google Cloud Platform Oracle zones in a
      location.
  """

  gcpOracleZones = _messages.StringField(1, repeated=True)


class MaintenanceWindow(_messages.Message):
  r"""Maintenance window as defined by Oracle. https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/datatypes/MaintenanceWindow

  Enums:
    DaysOfWeekValueListEntryValuesEnum:
    MonthsValueListEntryValuesEnum:
    PatchingModeValueValuesEnum: Optional. Cloud CloudExadataInfrastructure
      node patching method, either "ROLLING" or "NONROLLING". Default value is
      ROLLING.
    PreferenceValueValuesEnum: Optional. The maintenance window scheduling
      preference.

  Fields:
    customActionTimeoutMins: Optional. Determines the amount of time the
      system will wait before the start of each database server patching
      operation. Custom action timeout is in minutes and valid value is
      between 15 to 120 (inclusive).
    daysOfWeek: Optional. Days during the week when maintenance should be
      performed.
    hoursOfDay: Optional. The window of hours during the day when maintenance
      should be performed. The window is a 4 hour slot. Valid values are: 0 -
      represents time slot 0:00 - 3:59 UTC 4 - represents time slot 4:00 -
      7:59 UTC 8 - represents time slot 8:00 - 11:59 UTC 12 - represents time
      slot 12:00 - 15:59 UTC 16 - represents time slot 16:00 - 19:59 UTC 20 -
      represents time slot 20:00 - 23:59 UTC
    isCustomActionTimeoutEnabled: Optional. If true, enables the configuration
      of a custom action timeout (waiting period) between database server
      patching operations.
    leadTimeWeek: Optional. Lead time window allows user to set a lead time to
      prepare for a down time. The lead time is in weeks and valid value is
      between 1 to 4.
    months: Optional. Months during the year when maintenance should be
      performed.
    patchingMode: Optional. Cloud CloudExadataInfrastructure node patching
      method, either "ROLLING" or "NONROLLING". Default value is ROLLING.
    preference: Optional. The maintenance window scheduling preference.
    weeksOfMonth: Optional. Weeks during the month when maintenance should be
      performed. Weeks start on the 1st, 8th, 15th, and 22nd days of the
      month, and have a duration of 7 days. Weeks start and end based on
      calendar dates, not days of the week.
  """

  class DaysOfWeekValueListEntryValuesEnum(_messages.Enum):
    r"""DaysOfWeekValueListEntryValuesEnum enum type.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  class MonthsValueListEntryValuesEnum(_messages.Enum):
    r"""MonthsValueListEntryValuesEnum enum type.

    Values:
      MONTH_UNSPECIFIED: The unspecified month.
      JANUARY: The month of January.
      FEBRUARY: The month of February.
      MARCH: The month of March.
      APRIL: The month of April.
      MAY: The month of May.
      JUNE: The month of June.
      JULY: The month of July.
      AUGUST: The month of August.
      SEPTEMBER: The month of September.
      OCTOBER: The month of October.
      NOVEMBER: The month of November.
      DECEMBER: The month of December.
    """
    MONTH_UNSPECIFIED = 0
    JANUARY = 1
    FEBRUARY = 2
    MARCH = 3
    APRIL = 4
    MAY = 5
    JUNE = 6
    JULY = 7
    AUGUST = 8
    SEPTEMBER = 9
    OCTOBER = 10
    NOVEMBER = 11
    DECEMBER = 12

  class PatchingModeValueValuesEnum(_messages.Enum):
    r"""Optional. Cloud CloudExadataInfrastructure node patching method,
    either "ROLLING" or "NONROLLING". Default value is ROLLING.

    Values:
      PATCHING_MODE_UNSPECIFIED: Default unspecified value.
      ROLLING: Updates the Cloud Exadata database server hosts in a rolling
        fashion.
      NON_ROLLING: The non-rolling maintenance method first updates your
        storage servers at the same time, then your database servers at the
        same time.
    """
    PATCHING_MODE_UNSPECIFIED = 0
    ROLLING = 1
    NON_ROLLING = 2

  class PreferenceValueValuesEnum(_messages.Enum):
    r"""Optional. The maintenance window scheduling preference.

    Values:
      MAINTENANCE_WINDOW_PREFERENCE_UNSPECIFIED: Default unspecified value.
      CUSTOM_PREFERENCE: Custom preference.
      NO_PREFERENCE: No preference.
    """
    MAINTENANCE_WINDOW_PREFERENCE_UNSPECIFIED = 0
    CUSTOM_PREFERENCE = 1
    NO_PREFERENCE = 2

  customActionTimeoutMins = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  daysOfWeek = _messages.EnumField('DaysOfWeekValueListEntryValuesEnum', 2, repeated=True)
  hoursOfDay = _messages.IntegerField(3, repeated=True, variant=_messages.Variant.INT32)
  isCustomActionTimeoutEnabled = _messages.BooleanField(4)
  leadTimeWeek = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  months = _messages.EnumField('MonthsValueListEntryValuesEnum', 6, repeated=True)
  patchingMode = _messages.EnumField('PatchingModeValueValuesEnum', 7)
  preference = _messages.EnumField('PreferenceValueValuesEnum', 8)
  weeksOfMonth = _messages.IntegerField(9, repeated=True, variant=_messages.Variant.INT32)


class MinorVersion(_messages.Message):
  r"""MinorVersion represents a minor version of a GI.
  https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/GiMinorVersionSummary/

  Fields:
    gridImageId: Optional. The ID of the Grid Image.
    name: Identifier. The name of the MinorVersion resource with the format: p
      rojects/{project}/locations/{region}/giVersions/{gi_version}/minorVersio
      ns/{minor_version}
    version: Optional. The valid Oracle grid infrastructure software version.
  """

  gridImageId = _messages.StringField(1)
  name = _messages.StringField(2)
  version = _messages.StringField(3)


class OdbNetwork(_messages.Message):
  r"""Represents OdbNetwork resource.

  Enums:
    StateValueValuesEnum: Output only. State of the ODB Network.

  Messages:
    LabelsValue: Optional. Labels or tags associated with the resource.

  Fields:
    createTime: Output only. The date and time that the OdbNetwork was
      created.
    entitlementId: Output only. The ID of the subscription entitlement
      associated with the OdbNetwork.
    labels: Optional. Labels or tags associated with the resource.
    name: Identifier. The name of the OdbNetwork resource in the following
      format: projects/{project}/locations/{region}/odbNetworks/{odb_network}
    network: Required. The name of the VPC network in the following format:
      projects/{project}/global/networks/{network}
    state: Output only. State of the ODB Network.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the ODB Network.

    Values:
      STATE_UNSPECIFIED: Default unspecified value.
      PROVISIONING: Indicates that the resource is in provisioning state.
      AVAILABLE: Indicates that the resource is in available state.
      TERMINATING: Indicates that the resource is in terminating state.
      FAILED: Indicates that the resource is in failed state.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    AVAILABLE = 2
    TERMINATING = 3
    FAILED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels or tags associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  entitlementId = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  network = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class OdbSubnet(_messages.Message):
  r"""Represents OdbSubnet resource.

  Enums:
    PurposeValueValuesEnum: Required. Purpose of the subnet.
    StateValueValuesEnum: Output only. State of the ODB Subnet.

  Messages:
    LabelsValue: Optional. Labels or tags associated with the resource.

  Fields:
    cidrRange: Required. The CIDR range of the subnet.
    createTime: Output only. The date and time that the OdbNetwork was
      created.
    labels: Optional. Labels or tags associated with the resource.
    name: Identifier. The name of the OdbSubnet resource in the following
      format: projects/{project}/locations/{location}/odbNetworks/{odb_network
      }/odbSubnets/{odb_subnet}
    purpose: Required. Purpose of the subnet.
    state: Output only. State of the ODB Subnet.
  """

  class PurposeValueValuesEnum(_messages.Enum):
    r"""Required. Purpose of the subnet.

    Values:
      PURPOSE_UNSPECIFIED: Default unspecified value.
      CLIENT_SUBNET: Subnet to be used for client connections.
      BACKUP_SUBNET: Subnet to be used for backup.
    """
    PURPOSE_UNSPECIFIED = 0
    CLIENT_SUBNET = 1
    BACKUP_SUBNET = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the ODB Subnet.

    Values:
      STATE_UNSPECIFIED: Default unspecified value.
      PROVISIONING: Indicates that the resource is in provisioning state.
      AVAILABLE: Indicates that the resource is in available state.
      TERMINATING: Indicates that the resource is in terminating state.
      FAILED: Indicates that the resource is in failed state.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    AVAILABLE = 2
    TERMINATING = 3
    FAILED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels or tags associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cidrRange = _messages.StringField(1)
  createTime = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  purpose = _messages.EnumField('PurposeValueValuesEnum', 5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    percentComplete: Output only. An estimated percentage of the operation
      that has been completed at a given moment of time, between 0 and 100.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. The status of the operation.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  percentComplete = _messages.FloatField(4)
  requestedCancellation = _messages.BooleanField(5)
  statusMessage = _messages.StringField(6)
  target = _messages.StringField(7)
  verb = _messages.StringField(8)


class OracledatabaseProjectsLocationsAutonomousDatabaseBackupsListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsAutonomousDatabaseBackupsListRequest
  object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Only the **autonomous_database_id** field is supported in the following
      format: `autonomous_database_id="{autonomous_database_id}"`. The
      accepted values must be a valid Autonomous Database ID, limited to the
      naming restrictions of the ID: ^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$). The
      ID must start with a letter, end with a letter or a number, and be a
      maximum of 63 characters.
    pageSize: Optional. The maximum number of items to return. If unspecified,
      at most 50 Autonomous DB Backups will be returned. The maximum value is
      1000; values above 1000 will be coerced to 1000.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent value for ListAutonomousDatabaseBackups in
      the following format: projects/{project}/locations/{location}.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class OracledatabaseProjectsLocationsAutonomousDatabaseCharacterSetsGetRequest(_messages.Message):
  r"""A
  OracledatabaseProjectsLocationsAutonomousDatabaseCharacterSetsGetRequest
  object.

  Fields:
    name: Required. The name of the Autonomous Database Character Set in the
      following format: projects/{project}/locations/{location}/autonomousData
      baseCharacterSets/{autonomous_database_character_set}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsAutonomousDatabaseCharacterSetsListRequest(_messages.Message):
  r"""A
  OracledatabaseProjectsLocationsAutonomousDatabaseCharacterSetsListRequest
  object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Only the **character_set_type** field is supported in the following
      format: `character_set_type="{characterSetType}"`. Accepted values
      include `DATABASE` and `NATIONAL`.
    pageSize: Optional. The maximum number of items to return. If unspecified,
      at most 50 Autonomous DB Character Sets will be returned. The maximum
      value is 1000; values above 1000 will be coerced to 1000.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent value for the Autonomous Database in the
      following format: projects/{project}/locations/{location}.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class OracledatabaseProjectsLocationsAutonomousDatabasesCreateRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsAutonomousDatabasesCreateRequest
  object.

  Fields:
    autonomousDatabase: A AutonomousDatabase resource to be passed as the
      request body.
    autonomousDatabaseId: Required. The ID of the Autonomous Database to
      create. This value is restricted to (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$)
      and must be a maximum of 63 characters in length. The value must start
      with a letter and end with a letter or a number.
    parent: Required. The name of the parent in the following format:
      projects/{project}/locations/{location}.
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  autonomousDatabase = _messages.MessageField('AutonomousDatabase', 1)
  autonomousDatabaseId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class OracledatabaseProjectsLocationsAutonomousDatabasesDeleteRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsAutonomousDatabasesDeleteRequest
  object.

  Fields:
    name: Required. The name of the resource in the following format: projects
      /{project}/locations/{location}/autonomousDatabases/{autonomous_database
      }.
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class OracledatabaseProjectsLocationsAutonomousDatabasesGenerateWalletRequest(_messages.Message):
  r"""A
  OracledatabaseProjectsLocationsAutonomousDatabasesGenerateWalletRequest
  object.

  Fields:
    generateAutonomousDatabaseWalletRequest: A
      GenerateAutonomousDatabaseWalletRequest resource to be passed as the
      request body.
    name: Required. The name of the Autonomous Database in the following
      format: projects/{project}/locations/{location}/autonomousDatabases/{aut
      onomous_database}.
  """

  generateAutonomousDatabaseWalletRequest = _messages.MessageField('GenerateAutonomousDatabaseWalletRequest', 1)
  name = _messages.StringField(2, required=True)


class OracledatabaseProjectsLocationsAutonomousDatabasesGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsAutonomousDatabasesGetRequest object.

  Fields:
    name: Required. The name of the Autonomous Database in the following
      format: projects/{project}/locations/{location}/autonomousDatabases/{aut
      onomous_database}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsAutonomousDatabasesListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsAutonomousDatabasesListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
    orderBy: Optional. An expression for ordering the results of the request.
    pageSize: Optional. The maximum number of items to return. If unspecified,
      at most 50 Autonomous Database will be returned. The maximum value is
      1000; values above 1000 will be coerced to 1000.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent value for the Autonomous Database in the
      following format: projects/{project}/locations/{location}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class OracledatabaseProjectsLocationsAutonomousDatabasesPatchRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsAutonomousDatabasesPatchRequest object.

  Fields:
    autonomousDatabase: A AutonomousDatabase resource to be passed as the
      request body.
    name: Identifier. The name of the Autonomous Database resource in the
      following format: projects/{project}/locations/{region}/autonomousDataba
      ses/{autonomous_database}
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Exadata resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  autonomousDatabase = _messages.MessageField('AutonomousDatabase', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class OracledatabaseProjectsLocationsAutonomousDatabasesRestartRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsAutonomousDatabasesRestartRequest
  object.

  Fields:
    name: Required. The name of the Autonomous Database in the following
      format: projects/{project}/locations/{location}/autonomousDatabases/{aut
      onomous_database}.
    restartAutonomousDatabaseRequest: A RestartAutonomousDatabaseRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  restartAutonomousDatabaseRequest = _messages.MessageField('RestartAutonomousDatabaseRequest', 2)


class OracledatabaseProjectsLocationsAutonomousDatabasesRestoreRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsAutonomousDatabasesRestoreRequest
  object.

  Fields:
    name: Required. The name of the Autonomous Database in the following
      format: projects/{project}/locations/{location}/autonomousDatabases/{aut
      onomous_database}.
    restoreAutonomousDatabaseRequest: A RestoreAutonomousDatabaseRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  restoreAutonomousDatabaseRequest = _messages.MessageField('RestoreAutonomousDatabaseRequest', 2)


class OracledatabaseProjectsLocationsAutonomousDatabasesStartRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsAutonomousDatabasesStartRequest object.

  Fields:
    name: Required. The name of the Autonomous Database in the following
      format: projects/{project}/locations/{location}/autonomousDatabases/{aut
      onomous_database}.
    startAutonomousDatabaseRequest: A StartAutonomousDatabaseRequest resource
      to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  startAutonomousDatabaseRequest = _messages.MessageField('StartAutonomousDatabaseRequest', 2)


class OracledatabaseProjectsLocationsAutonomousDatabasesStopRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsAutonomousDatabasesStopRequest object.

  Fields:
    name: Required. The name of the Autonomous Database in the following
      format: projects/{project}/locations/{location}/autonomousDatabases/{aut
      onomous_database}.
    stopAutonomousDatabaseRequest: A StopAutonomousDatabaseRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  stopAutonomousDatabaseRequest = _messages.MessageField('StopAutonomousDatabaseRequest', 2)


class OracledatabaseProjectsLocationsAutonomousDatabasesSwitchoverRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsAutonomousDatabasesSwitchoverRequest
  object.

  Fields:
    name: Required. The name of the Autonomous Database in the following
      format: projects/{project}/locations/{location}/autonomousDatabases/{aut
      onomous_database}.
    switchoverAutonomousDatabaseRequest: A SwitchoverAutonomousDatabaseRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  switchoverAutonomousDatabaseRequest = _messages.MessageField('SwitchoverAutonomousDatabaseRequest', 2)


class OracledatabaseProjectsLocationsAutonomousDbVersionsGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsAutonomousDbVersionsGetRequest object.

  Fields:
    name: Required. The name of the Autonomous Database Version in the
      following format: projects/{project}/locations/{location}/autonomousDbVe
      rsions/{autonomous_db_version}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsAutonomousDbVersionsListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsAutonomousDbVersionsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of items to return. If unspecified,
      at most 50 Autonomous DB Versions will be returned. The maximum value is
      1000; values above 1000 will be coerced to 1000.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent value for the Autonomous Database in the
      following format: projects/{project}/locations/{location}.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OracledatabaseProjectsLocationsCloudExadataInfrastructuresCreateRequest(_messages.Message):
  r"""A
  OracledatabaseProjectsLocationsCloudExadataInfrastructuresCreateRequest
  object.

  Fields:
    cloudExadataInfrastructure: A CloudExadataInfrastructure resource to be
      passed as the request body.
    cloudExadataInfrastructureId: Required. The ID of the Exadata
      Infrastructure to create. This value is restricted to
      (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$) and must be a maximum of 63
      characters in length. The value must start with a letter and end with a
      letter or a number.
    parent: Required. The parent value for CloudExadataInfrastructure in the
      following format: projects/{project}/locations/{location}.
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  cloudExadataInfrastructure = _messages.MessageField('CloudExadataInfrastructure', 1)
  cloudExadataInfrastructureId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class OracledatabaseProjectsLocationsCloudExadataInfrastructuresDbServersGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsCloudExadataInfrastructuresDbServersGet
  Request object.

  Fields:
    name: Required. The name of the database server in the following format: p
      rojects/{project}/locations/{location}/cloudExadataInfrastructures/{clou
      dExadataInfrastructure}/dbServers/{db_server}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsCloudExadataInfrastructuresDbServersListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsCloudExadataInfrastructuresDbServersLis
  tRequest object.

  Fields:
    pageSize: Optional. The maximum number of items to return. If unspecified,
      a maximum of 50 db servers will be returned. The maximum value is 1000;
      values above 1000 will be reset to 1000.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent value for database server in the following
      format: projects/{project}/locations/{location}/cloudExadataInfrastructu
      res/{cloudExadataInfrastructure}.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OracledatabaseProjectsLocationsCloudExadataInfrastructuresDeleteRequest(_messages.Message):
  r"""A
  OracledatabaseProjectsLocationsCloudExadataInfrastructuresDeleteRequest
  object.

  Fields:
    force: Optional. If set to true, all VM clusters for this Exadata
      Infrastructure will be deleted. An Exadata Infrastructure can only be
      deleted once all its VM clusters have been deleted.
    name: Required. The name of the Cloud Exadata Infrastructure in the
      following format: projects/{project}/locations/{location}/cloudExadataIn
      frastructures/{cloud_exadata_infrastructure}.
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class OracledatabaseProjectsLocationsCloudExadataInfrastructuresGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsCloudExadataInfrastructuresGetRequest
  object.

  Fields:
    name: Required. The name of the Cloud Exadata Infrastructure in the
      following format: projects/{project}/locations/{location}/cloudExadataIn
      frastructures/{cloud_exadata_infrastructure}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsCloudExadataInfrastructuresListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsCloudExadataInfrastructuresListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of items to return. If unspecified,
      at most 50 Exadata infrastructures will be returned. The maximum value
      is 1000; values above 1000 will be coerced to 1000.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent value for CloudExadataInfrastructure in the
      following format: projects/{project}/locations/{location}.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OracledatabaseProjectsLocationsCloudExadataInfrastructuresPatchRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsCloudExadataInfrastructuresPatchRequest
  object.

  Fields:
    cloudExadataInfrastructure: A CloudExadataInfrastructure resource to be
      passed as the request body.
    name: Identifier. The name of the Exadata Infrastructure resource with the
      format: projects/{project}/locations/{region}/cloudExadataInfrastructure
      s/{cloud_exadata_infrastructure}
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Exadata resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  cloudExadataInfrastructure = _messages.MessageField('CloudExadataInfrastructure', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class OracledatabaseProjectsLocationsCloudVmClustersCreateRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsCloudVmClustersCreateRequest object.

  Fields:
    cloudVmCluster: A CloudVmCluster resource to be passed as the request
      body.
    cloudVmClusterId: Required. The ID of the VM Cluster to create. This value
      is restricted to (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$) and must be a
      maximum of 63 characters in length. The value must start with a letter
      and end with a letter or a number.
    parent: Required. The name of the parent in the following format:
      projects/{project}/locations/{location}.
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  cloudVmCluster = _messages.MessageField('CloudVmCluster', 1)
  cloudVmClusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class OracledatabaseProjectsLocationsCloudVmClustersDbNodesGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsCloudVmClustersDbNodesGetRequest
  object.

  Fields:
    name: Required. The name of the database node in the following format: pro
      jects/{project}/locations/{location}/cloudVmClusters/{cloud_vm_cluster}/
      dbNodes/{db_node}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsCloudVmClustersDbNodesListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsCloudVmClustersDbNodesListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of items to return. If unspecified,
      at most 50 db nodes will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A token identifying a page of results the node should
      return.
    parent: Required. The parent value for database node in the following
      format: projects/{project}/locations/{location}/cloudVmClusters/{cloudVm
      Cluster}. .
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OracledatabaseProjectsLocationsCloudVmClustersDeleteRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsCloudVmClustersDeleteRequest object.

  Fields:
    force: Optional. If set to true, all child resources for the VM Cluster
      will be deleted. A VM Cluster can only be deleted once all its child
      resources have been deleted.
    name: Required. The name of the Cloud VM Cluster in the following format:
      projects/{project}/locations/{location}/cloudVmClusters/{cloud_vm_cluste
      r}.
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class OracledatabaseProjectsLocationsCloudVmClustersGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsCloudVmClustersGetRequest object.

  Fields:
    name: Required. The name of the Cloud VM Cluster in the following format:
      projects/{project}/locations/{location}/cloudVmClusters/{cloud_vm_cluste
      r}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsCloudVmClustersListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsCloudVmClustersListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
    pageSize: Optional. The number of VM clusters to return. If unspecified,
      at most 50 VM clusters will be returned. The maximum value is 1,000.
    pageToken: Optional. A token identifying the page of results the server
      returns.
    parent: Required. The name of the parent in the following format:
      projects/{project}/locations/{location}.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class OracledatabaseProjectsLocationsCloudVmClustersPatchRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsCloudVmClustersPatchRequest object.

  Fields:
    cloudVmCluster: A CloudVmCluster resource to be passed as the request
      body.
    name: Identifier. The name of the VM Cluster resource with the format:
      projects/{project}/locations/{region}/cloudVmClusters/{cloud_vm_cluster}
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. A mask specifying which fields in th VM Cluster
      should be updated. A field specified in the mask is overwritten. If a
      mask isn't provided then all the fields in the VM Cluster are
      overwritten.
  """

  cloudVmCluster = _messages.MessageField('CloudVmCluster', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class OracledatabaseProjectsLocationsDbSystemShapesGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsDbSystemShapesGetRequest object.

  Fields:
    name: Required. The name of the Database System Shape in the following
      format: projects/{project}/locations/{location}/dbSystemShapes/{db_syste
      m_shape}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsDbSystemShapesListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsDbSystemShapesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of items to return. If unspecified,
      at most 50 database system shapes will be returned. The maximum value is
      1000; values above 1000 will be coerced to 1000.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent value for Database System Shapes in the
      following format: projects/{project}/locations/{location}.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OracledatabaseProjectsLocationsEntitlementsGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsEntitlementsGetRequest object.

  Fields:
    name: Required. The name of the entitlement in the following format:
      projects/{project}/locations/{location}/entitlements/{entitlement}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsEntitlementsListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsEntitlementsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of items to return. If unspecified,
      a maximum of 50 entitlements will be returned. The maximum value is
      1000.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent value for the entitlement in the following
      format: projects/{project}/locations/{location}.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OracledatabaseProjectsLocationsExadbVmClustersCreateRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsExadbVmClustersCreateRequest object.

  Fields:
    exadbVmCluster: A ExadbVmCluster resource to be passed as the request
      body.
    exadbVmClusterId: Required. The ID of the ExadbVmCluster to create. This
      value is restricted to (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$) and must be a
      maximum of 63 characters in length. The value must start with a letter
      and end with a letter or a number.
    parent: Required. The value for parent of the ExadbVmCluster in the
      following format: projects/{project}/locations/{location}.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  exadbVmCluster = _messages.MessageField('ExadbVmCluster', 1)
  exadbVmClusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class OracledatabaseProjectsLocationsExadbVmClustersDbNodesGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsExadbVmClustersDbNodesGetRequest
  object.

  Fields:
    name: Required. The name of the database node in the following format: pro
      jects/{project}/locations/{location}/cloudVmClusters/{cloud_vm_cluster}/
      dbNodes/{db_node}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsExadbVmClustersDbNodesListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsExadbVmClustersDbNodesListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of items to return. If unspecified,
      at most 50 db nodes will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A token identifying a page of results the node should
      return.
    parent: Required. The parent value for database node in the following
      format: projects/{project}/locations/{location}/cloudVmClusters/{cloudVm
      Cluster}. .
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OracledatabaseProjectsLocationsExadbVmClustersDeleteRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsExadbVmClustersDeleteRequest object.

  Fields:
    name: Required. The name of the ExadbVmCluster in the following format: pr
      ojects/{project}/locations/{location}/exadbVmClusters/{exadb_vm_cluster}
      .
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class OracledatabaseProjectsLocationsExadbVmClustersGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsExadbVmClustersGetRequest object.

  Fields:
    name: Required. The name of the ExadbVmCluster in the following format: pr
      ojects/{project}/locations/{location}/exadbVmClusters/{exadb_vm_cluster}
      .
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsExadbVmClustersListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsExadbVmClustersListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
    orderBy: Optional. An expression for ordering the results of the request.
    pageSize: Optional. The maximum number of items to return. If unspecified,
      at most 50 ExadbVmClusters will be returned. The maximum value is 1000;
      values above 1000 will be coerced to 1000.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent value for ExadbVmClusters in the following
      format: projects/{project}/locations/{location}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class OracledatabaseProjectsLocationsExascaleDbStorageVaultsDeleteRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsExascaleDbStorageVaultsDeleteRequest
  object.

  Fields:
    name: Required. The name of the ExascaleDbStorageVault in the following
      format: projects/{project}/locations/{location}/exascaleDbStorageVaults/
      {exascale_db_storage_vault}.
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class OracledatabaseProjectsLocationsExascaleDbStorageVaultsGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsExascaleDbStorageVaultsGetRequest
  object.

  Fields:
    name: Required. The name of the ExascaleDbStorageVault in the following
      format: projects/{project}/locations/{location}/exascaleDbStorageVaults/
      {exascale_db_storage_vault}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsExascaleDbStorageVaultsListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsExascaleDbStorageVaultsListRequest
  object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Filter the list as specified in https://google.aip.dev/160.
    orderBy: Optional. An expression for ordering the results of the request.
      Order results as specified in https://google.aip.dev/132.
    pageSize: Optional. The maximum number of items to return. If unspecified,
      at most 50 ExascaleDbStorageVaults will be returned. The maximum value
      is 1000; values above 1000 will be coerced to 1000.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent value for ExascaleDbStorageVault in the
      following format: projects/{project}/locations/{location}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class OracledatabaseProjectsLocationsGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsGiVersionsGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsGiVersionsGetRequest object.

  Fields:
    name: Required. The name of the Grid Infrastructure (GI) version in the
      following format:
      projects/{project}/locations/{location}/giVersions/{gi_version}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsGiVersionsListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsGiVersionsListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Only the shape and gi_version fields are supported in this format:
      `shape="{shape}"`.
    pageSize: Optional. The maximum number of items to return. If unspecified,
      a maximum of 50 Oracle Grid Infrastructure (GI) versions will be
      returned. The maximum value is 1000; values above 1000 will be reset to
      1000.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent value for Grid Infrastructure Version in the
      following format: Format: projects/{project}/locations/{location}.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class OracledatabaseProjectsLocationsGiVersionsMinorVersionsGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsGiVersionsMinorVersionsGetRequest
  object.

  Fields:
    name: Required. The name of the MinorVersion resource with the format: pro
      jects/{project}/locations/{location}/giVersions/{gi_version}/minorVersio
      ns/{minor_version}
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsGiVersionsMinorVersionsListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsGiVersionsMinorVersionsListRequest
  object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Only shapeFamily and gcp_oracle_zone_id are supported in this format:
      `shape_family="{shapeFamily}" AND
      gcp_oracle_zone_id="{gcp_oracle_zone_id}"`.
    pageSize: Optional. The maximum number of items to return. If unspecified,
      a maximum of 50 System Versions will be returned. The maximum value is
      1000; values above 1000 will be reset to 1000.
    pageToken: Optional. A token identifying the requested page of results to
      return. All fields except the filter should remain the same as in the
      request that provided this page token.
    parent: Required. The parent value for the MinorVersion resource with the
      format: projects/{project}/locations/{location}/giVersions/{gi_version}
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class OracledatabaseProjectsLocationsListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class OracledatabaseProjectsLocationsOdbNetworksCreateRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsOdbNetworksCreateRequest object.

  Fields:
    odbNetwork: A OdbNetwork resource to be passed as the request body.
    odbNetworkId: Required. The ID of the OdbNetwork to create. This value is
      restricted to (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$) and must be a maximum
      of 63 characters in length. The value must start with a letter and end
      with a letter or a number.
    parent: Required. The parent value for the OdbNetwork in the following
      format: projects/{project}/locations/{location}.
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  odbNetwork = _messages.MessageField('OdbNetwork', 1)
  odbNetworkId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class OracledatabaseProjectsLocationsOdbNetworksDeleteRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsOdbNetworksDeleteRequest object.

  Fields:
    name: Required. The name of the resource in the following format:
      projects/{project}/locations/{location}/odbNetworks/{odb_network}.
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class OracledatabaseProjectsLocationsOdbNetworksGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsOdbNetworksGetRequest object.

  Fields:
    name: Required. The name of the OdbNetwork in the following format:
      projects/{project}/locations/{location}/odbNetworks/{odb_network}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsOdbNetworksListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsOdbNetworksListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
    orderBy: Optional. An expression for ordering the results of the request.
    pageSize: Optional. The maximum number of items to return. If unspecified,
      at most 50 ODB Networks will be returned. The maximum value is 1000;
      values above 1000 will be coerced to 1000.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent value for the ODB Network in the following
      format: projects/{project}/locations/{location}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsCreateRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsCreateRequest
  object.

  Fields:
    odbSubnet: A OdbSubnet resource to be passed as the request body.
    odbSubnetId: Required. The ID of the OdbSubnet to create. This value is
      restricted to (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$) and must be a maximum
      of 63 characters in length. The value must start with a letter and end
      with a letter or a number.
    parent: Required. The parent value for the OdbSubnet in the following
      format:
      projects/{project}/locations/{location}/odbNetworks/{odb_network}.
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  odbSubnet = _messages.MessageField('OdbSubnet', 1)
  odbSubnetId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsDeleteRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsDeleteRequest
  object.

  Fields:
    name: Required. The name of the resource in the following format: projects
      /{project}/locations/{region}/odbNetworks/{odb_network}/odbSubnets/{odb_
      subnet}.
    requestId: Optional. An optional ID to identify the request. This value is
      used to identify duplicate requests. If you make a request with the same
      request ID and the original request is still in progress or completed,
      the server ignores the second request. This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsGetRequest object.

  Fields:
    name: Required. The name of the OdbSubnet in the following format: project
      s/{project}/locations/{location}/odbNetworks/{odb_network}/odbSubnets/{o
      db_subnet}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsListRequest
  object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
    orderBy: Optional. An expression for ordering the results of the request.
    pageSize: Optional. The maximum number of items to return. If unspecified,
      at most 50 ODB Networks will be returned. The maximum value is 1000;
      values above 1000 will be coerced to 1000.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent value for the OdbSubnet in the following
      format:
      projects/{project}/locations/{location}/odbNetworks/{odb_network}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class OracledatabaseProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class OracledatabaseProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class OracledatabaseProjectsLocationsSystemVersionsGetRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsSystemVersionsGetRequest object.

  Fields:
    name: Required. The name of the System Version in the following format:
      projects/{project}/locations/{location}/systemVersions/{system_version}.
  """

  name = _messages.StringField(1, required=True)


class OracledatabaseProjectsLocationsSystemVersionsListRequest(_messages.Message):
  r"""A OracledatabaseProjectsLocationsSystemVersionsListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Only the shape and gi_version fields are supported in this format:
      `shape="{shape}" AND gi_version="{gi_version}"`.
    pageSize: Optional. The maximum number of items to return. If unspecified,
      a maximum of 50 System Versions will be returned. The maximum value is
      1000; values above 1000 will be reset to 1000.
    pageToken: Optional. A token identifying the requested page of results to
      return. All fields except the filter should remain the same as in the
      request that provided this page token.
    parent: Required. The parent value for System Version in the following
      format: Format: projects/{project}/locations/{location}.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class RestartAutonomousDatabaseRequest(_messages.Message):
  r"""The request for `AutonomousDatabase.Restart`."""


class RestoreAutonomousDatabaseRequest(_messages.Message):
  r"""The request for `AutonomousDatabase.Restore`.

  Fields:
    restoreTime: Required. The time and date to restore the database to.
  """

  restoreTime = _messages.StringField(1)


class ScheduledOperationDetails(_messages.Message):
  r"""Details of scheduled operation. https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/datatypes/ScheduledOperationDetails

  Enums:
    DayOfWeekValueValuesEnum: Output only. Day of week.

  Fields:
    dayOfWeek: Output only. Day of week.
    startTime: Output only. Auto start time.
    stopTime: Output only. Auto stop time.
  """

  class DayOfWeekValueValuesEnum(_messages.Enum):
    r"""Output only. Day of week.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  dayOfWeek = _messages.EnumField('DayOfWeekValueValuesEnum', 1)
  startTime = _messages.MessageField('TimeOfDay', 2)
  stopTime = _messages.MessageField('TimeOfDay', 3)


class SourceConfig(_messages.Message):
  r"""The source configuration for the standby Autonomous Database.

  Fields:
    automaticBackupsReplicationEnabled: Optional. This field specifies if the
      replication of automatic backups is enabled when creating a Data Guard.
    autonomousDatabase: Optional. The name of the primary Autonomous Database
      that is used to create a Peer Autonomous Database from a source.
  """

  automaticBackupsReplicationEnabled = _messages.BooleanField(1)
  autonomousDatabase = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartAutonomousDatabaseRequest(_messages.Message):
  r"""The request for `AutonomousDatabase.Start`."""


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StopAutonomousDatabaseRequest(_messages.Message):
  r"""The request for `AutonomousDatabase.Stop`."""


class StorageVaultProperties(_messages.Message):
  r"""The storage vault properties for the ExadbVmCluster.

  Fields:
    additionalFlashCacheInPercent: Optional. The size of additional flash
      cache in percentage of high capacity database storage.
    displayName: Optional. The name of the exadata database storage vault. The
      display name does not have to be unique.
    highCapacityDatabaseStorage: Required. Total Capacity of the Exadata
      Database Storage Vault.
  """

  additionalFlashCacheInPercent = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  displayName = _messages.StringField(2)
  highCapacityDatabaseStorage = _messages.MessageField('ExadbVmClusterStorageDetails', 3)


class SwitchoverAutonomousDatabaseRequest(_messages.Message):
  r"""The request for `OracleDatabase.SwitchoverAutonomousDatabase`.

  Fields:
    peerAutonomousDatabase: Required. The peer database name to switch over
      to.
  """

  peerAutonomousDatabase = _messages.StringField(1)


class SystemVersion(_messages.Message):
  r"""Details of the System version. https://docs.oracle.com/en-
  us/iaas/api/#/en/database/********/datatypes/SystemVersionSummary

  Fields:
    giVersion: Output only. Oracle Grid Infrastructure (GI) version.
    name: Identifier. The name of the System Version resource with the format:
      projects/{project}/locations/{location}/systemVersions/{system_version}
    shape: Output only. The Exadata shape.
    systemVersions: Output only. Compatible Exadata system versions for a
      given shape and GI version.
  """

  giVersion = _messages.StringField(1)
  name = _messages.StringField(2)
  shape = _messages.StringField(3)
  systemVersions = _messages.StringField(4, repeated=True)


class TimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of a day in 24 hour format. Must be greater than or equal to
      0 and typically must be less than or equal to 23. An API may choose to
      allow the value "24:00:00" for scenarios like business closing time.
    minutes: Minutes of an hour. Must be greater than or equal to 0 and less
      than or equal to 59.
    nanos: Fractions of seconds, in nanoseconds. Must be greater than or equal
      to 0 and less than or equal to 999,999,999.
    seconds: Seconds of a minute. Must be greater than or equal to 0 and
      typically must be less than or equal to 59. An API may allow the value
      60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class TimeZone(_messages.Message):
  r"""Represents a time zone from the [IANA Time Zone
  Database](https://www.iana.org/time-zones).

  Fields:
    id: IANA Time Zone Database time zone. For example "America/New_York".
    version: Optional. IANA Time Zone Database version number. For example
      "2019a".
  """

  id = _messages.StringField(1)
  version = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
