"""Generated client library for oracledatabase version v1alpha."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.oracledatabase.v1alpha import oracledatabase_v1alpha_messages as messages


class OracledatabaseV1alpha(base_api.BaseApiClient):
  """Generated client library for service oracledatabase version v1alpha."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://oracledatabase.googleapis.com/'
  MTLS_BASE_URL = 'https://oracledatabase.mtls.googleapis.com/'

  _PACKAGE = 'oracledatabase'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'OracledatabaseV1alpha'
  _URL_VERSION = 'v1alpha'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new oracledatabase handle."""
    url = url or self.BASE_URL
    super(OracledatabaseV1alpha, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_autonomousDatabaseBackups = self.ProjectsLocationsAutonomousDatabaseBackupsService(self)
    self.projects_locations_autonomousDatabaseCharacterSets = self.ProjectsLocationsAutonomousDatabaseCharacterSetsService(self)
    self.projects_locations_autonomousDatabases = self.ProjectsLocationsAutonomousDatabasesService(self)
    self.projects_locations_autonomousDbVersions = self.ProjectsLocationsAutonomousDbVersionsService(self)
    self.projects_locations_cloudExadataInfrastructures_dbServers = self.ProjectsLocationsCloudExadataInfrastructuresDbServersService(self)
    self.projects_locations_cloudExadataInfrastructures = self.ProjectsLocationsCloudExadataInfrastructuresService(self)
    self.projects_locations_cloudVmClusters_dbNodes = self.ProjectsLocationsCloudVmClustersDbNodesService(self)
    self.projects_locations_cloudVmClusters = self.ProjectsLocationsCloudVmClustersService(self)
    self.projects_locations_dbSystemShapes = self.ProjectsLocationsDbSystemShapesService(self)
    self.projects_locations_entitlements = self.ProjectsLocationsEntitlementsService(self)
    self.projects_locations_exadbVmClusters_dbNodes = self.ProjectsLocationsExadbVmClustersDbNodesService(self)
    self.projects_locations_exadbVmClusters = self.ProjectsLocationsExadbVmClustersService(self)
    self.projects_locations_exascaleDbStorageVaults = self.ProjectsLocationsExascaleDbStorageVaultsService(self)
    self.projects_locations_giVersions_minorVersions = self.ProjectsLocationsGiVersionsMinorVersionsService(self)
    self.projects_locations_giVersions = self.ProjectsLocationsGiVersionsService(self)
    self.projects_locations_odbNetworks_odbSubnets = self.ProjectsLocationsOdbNetworksOdbSubnetsService(self)
    self.projects_locations_odbNetworks = self.ProjectsLocationsOdbNetworksService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_systemVersions = self.ProjectsLocationsSystemVersionsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsAutonomousDatabaseBackupsService(base_api.BaseApiService):
    """Service class for the projects_locations_autonomousDatabaseBackups resource."""

    _NAME = 'projects_locations_autonomousDatabaseBackups'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsAutonomousDatabaseBackupsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the long-term and automatic backups of an Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabaseBackupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAutonomousDatabaseBackupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDatabaseBackups',
        http_method='GET',
        method_id='oracledatabase.projects.locations.autonomousDatabaseBackups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/autonomousDatabaseBackups',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabaseBackupsListRequest',
        response_type_name='ListAutonomousDatabaseBackupsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAutonomousDatabaseCharacterSetsService(base_api.BaseApiService):
    """Service class for the projects_locations_autonomousDatabaseCharacterSets resource."""

    _NAME = 'projects_locations_autonomousDatabaseCharacterSets'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsAutonomousDatabaseCharacterSetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the details of a single Autonomous Database Character Set.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabaseCharacterSetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AutonomousDatabaseCharacterSet) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDatabaseCharacterSets/{autonomousDatabaseCharacterSetsId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.autonomousDatabaseCharacterSets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabaseCharacterSetsGetRequest',
        response_type_name='AutonomousDatabaseCharacterSet',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Autonomous Database Character Sets in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabaseCharacterSetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAutonomousDatabaseCharacterSetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDatabaseCharacterSets',
        http_method='GET',
        method_id='oracledatabase.projects.locations.autonomousDatabaseCharacterSets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/autonomousDatabaseCharacterSets',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabaseCharacterSetsListRequest',
        response_type_name='ListAutonomousDatabaseCharacterSetsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAutonomousDatabasesService(base_api.BaseApiService):
    """Service class for the projects_locations_autonomousDatabases resource."""

    _NAME = 'projects_locations_autonomousDatabases'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsAutonomousDatabasesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Autonomous Database in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDatabases',
        http_method='POST',
        method_id='oracledatabase.projects.locations.autonomousDatabases.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['autonomousDatabaseId', 'requestId'],
        relative_path='v1alpha/{+parent}/autonomousDatabases',
        request_field='autonomousDatabase',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}',
        http_method='DELETE',
        method_id='oracledatabase.projects.locations.autonomousDatabases.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def GenerateWallet(self, request, global_params=None):
      r"""Generates a wallet for an Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesGenerateWalletRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GenerateAutonomousDatabaseWalletResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateWallet')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateWallet.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}:generateWallet',
        http_method='POST',
        method_id='oracledatabase.projects.locations.autonomousDatabases.generateWallet',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:generateWallet',
        request_field='generateAutonomousDatabaseWalletRequest',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesGenerateWalletRequest',
        response_type_name='GenerateAutonomousDatabaseWalletResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the details of a single Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AutonomousDatabase) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.autonomousDatabases.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesGetRequest',
        response_type_name='AutonomousDatabase',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Autonomous Databases in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAutonomousDatabasesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDatabases',
        http_method='GET',
        method_id='oracledatabase.projects.locations.autonomousDatabases.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/autonomousDatabases',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesListRequest',
        response_type_name='ListAutonomousDatabasesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}',
        http_method='PATCH',
        method_id='oracledatabase.projects.locations.autonomousDatabases.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='autonomousDatabase',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Restart(self, request, global_params=None):
      r"""Restarts an Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesRestartRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Restart')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restart.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}:restart',
        http_method='POST',
        method_id='oracledatabase.projects.locations.autonomousDatabases.restart',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:restart',
        request_field='restartAutonomousDatabaseRequest',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesRestartRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Restore(self, request, global_params=None):
      r"""Restores a single Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Restore')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restore.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}:restore',
        http_method='POST',
        method_id='oracledatabase.projects.locations.autonomousDatabases.restore',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:restore',
        request_field='restoreAutonomousDatabaseRequest',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesRestoreRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Start(self, request, global_params=None):
      r"""Starts an Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesStartRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Start')
      return self._RunMethod(
          config, request, global_params=global_params)

    Start.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}:start',
        http_method='POST',
        method_id='oracledatabase.projects.locations.autonomousDatabases.start',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:start',
        request_field='startAutonomousDatabaseRequest',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesStartRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stops an Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}:stop',
        http_method='POST',
        method_id='oracledatabase.projects.locations.autonomousDatabases.stop',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:stop',
        request_field='stopAutonomousDatabaseRequest',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesStopRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Switchover(self, request, global_params=None):
      r"""Initiates a switchover of specified autonomous database to the associated peer database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesSwitchoverRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Switchover')
      return self._RunMethod(
          config, request, global_params=global_params)

    Switchover.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}:switchover',
        http_method='POST',
        method_id='oracledatabase.projects.locations.autonomousDatabases.switchover',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:switchover',
        request_field='switchoverAutonomousDatabaseRequest',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesSwitchoverRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsAutonomousDbVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_autonomousDbVersions resource."""

    _NAME = 'projects_locations_autonomousDbVersions'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsAutonomousDbVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the details of an Autonomous Database version.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDbVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AutonomousDbVersion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDbVersions/{autonomousDbVersionsId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.autonomousDbVersions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDbVersionsGetRequest',
        response_type_name='AutonomousDbVersion',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the available Autonomous Database versions for a project and location.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDbVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAutonomousDbVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/autonomousDbVersions',
        http_method='GET',
        method_id='oracledatabase.projects.locations.autonomousDbVersions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/autonomousDbVersions',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDbVersionsListRequest',
        response_type_name='ListAutonomousDbVersionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsCloudExadataInfrastructuresDbServersService(base_api.BaseApiService):
    """Service class for the projects_locations_cloudExadataInfrastructures_dbServers resource."""

    _NAME = 'projects_locations_cloudExadataInfrastructures_dbServers'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsCloudExadataInfrastructuresDbServersService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of an Exadata Infrastructure's database server.

      Args:
        request: (OracledatabaseProjectsLocationsCloudExadataInfrastructuresDbServersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DbServer) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudExadataInfrastructures/{cloudExadataInfrastructuresId}/dbServers/{dbServersId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.cloudExadataInfrastructures.dbServers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudExadataInfrastructuresDbServersGetRequest',
        response_type_name='DbServer',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the database servers of an Exadata Infrastructure instance.

      Args:
        request: (OracledatabaseProjectsLocationsCloudExadataInfrastructuresDbServersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDbServersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudExadataInfrastructures/{cloudExadataInfrastructuresId}/dbServers',
        http_method='GET',
        method_id='oracledatabase.projects.locations.cloudExadataInfrastructures.dbServers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/dbServers',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudExadataInfrastructuresDbServersListRequest',
        response_type_name='ListDbServersResponse',
        supports_download=False,
    )

  class ProjectsLocationsCloudExadataInfrastructuresService(base_api.BaseApiService):
    """Service class for the projects_locations_cloudExadataInfrastructures resource."""

    _NAME = 'projects_locations_cloudExadataInfrastructures'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsCloudExadataInfrastructuresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Exadata Infrastructure in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsCloudExadataInfrastructuresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudExadataInfrastructures',
        http_method='POST',
        method_id='oracledatabase.projects.locations.cloudExadataInfrastructures.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['cloudExadataInfrastructureId', 'requestId'],
        relative_path='v1alpha/{+parent}/cloudExadataInfrastructures',
        request_field='cloudExadataInfrastructure',
        request_type_name='OracledatabaseProjectsLocationsCloudExadataInfrastructuresCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Exadata Infrastructure.

      Args:
        request: (OracledatabaseProjectsLocationsCloudExadataInfrastructuresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudExadataInfrastructures/{cloudExadataInfrastructuresId}',
        http_method='DELETE',
        method_id='oracledatabase.projects.locations.cloudExadataInfrastructures.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudExadataInfrastructuresDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Exadata Infrastructure.

      Args:
        request: (OracledatabaseProjectsLocationsCloudExadataInfrastructuresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CloudExadataInfrastructure) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudExadataInfrastructures/{cloudExadataInfrastructuresId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.cloudExadataInfrastructures.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudExadataInfrastructuresGetRequest',
        response_type_name='CloudExadataInfrastructure',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Exadata Infrastructures in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsCloudExadataInfrastructuresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCloudExadataInfrastructuresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudExadataInfrastructures',
        http_method='GET',
        method_id='oracledatabase.projects.locations.cloudExadataInfrastructures.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/cloudExadataInfrastructures',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudExadataInfrastructuresListRequest',
        response_type_name='ListCloudExadataInfrastructuresResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Exadata Infrastructure.

      Args:
        request: (OracledatabaseProjectsLocationsCloudExadataInfrastructuresPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudExadataInfrastructures/{cloudExadataInfrastructuresId}',
        http_method='PATCH',
        method_id='oracledatabase.projects.locations.cloudExadataInfrastructures.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='cloudExadataInfrastructure',
        request_type_name='OracledatabaseProjectsLocationsCloudExadataInfrastructuresPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsCloudVmClustersDbNodesService(base_api.BaseApiService):
    """Service class for the projects_locations_cloudVmClusters_dbNodes resource."""

    _NAME = 'projects_locations_cloudVmClusters_dbNodes'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsCloudVmClustersDbNodesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about the specified database node.

      Args:
        request: (OracledatabaseProjectsLocationsCloudVmClustersDbNodesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DbNode) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudVmClusters/{cloudVmClustersId}/dbNodes/{dbNodesId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.cloudVmClusters.dbNodes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudVmClustersDbNodesGetRequest',
        response_type_name='DbNode',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the database nodes of a VM Cluster.

      Args:
        request: (OracledatabaseProjectsLocationsCloudVmClustersDbNodesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDbNodesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudVmClusters/{cloudVmClustersId}/dbNodes',
        http_method='GET',
        method_id='oracledatabase.projects.locations.cloudVmClusters.dbNodes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/dbNodes',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudVmClustersDbNodesListRequest',
        response_type_name='ListDbNodesResponse',
        supports_download=False,
    )

  class ProjectsLocationsCloudVmClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_cloudVmClusters resource."""

    _NAME = 'projects_locations_cloudVmClusters'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsCloudVmClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new VM Cluster in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsCloudVmClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudVmClusters',
        http_method='POST',
        method_id='oracledatabase.projects.locations.cloudVmClusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['cloudVmClusterId', 'requestId'],
        relative_path='v1alpha/{+parent}/cloudVmClusters',
        request_field='cloudVmCluster',
        request_type_name='OracledatabaseProjectsLocationsCloudVmClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single VM Cluster.

      Args:
        request: (OracledatabaseProjectsLocationsCloudVmClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudVmClusters/{cloudVmClustersId}',
        http_method='DELETE',
        method_id='oracledatabase.projects.locations.cloudVmClusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudVmClustersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single VM Cluster.

      Args:
        request: (OracledatabaseProjectsLocationsCloudVmClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CloudVmCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudVmClusters/{cloudVmClustersId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.cloudVmClusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudVmClustersGetRequest',
        response_type_name='CloudVmCluster',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the VM Clusters in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsCloudVmClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCloudVmClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudVmClusters',
        http_method='GET',
        method_id='oracledatabase.projects.locations.cloudVmClusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/cloudVmClusters',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudVmClustersListRequest',
        response_type_name='ListCloudVmClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Vm Cluster.

      Args:
        request: (OracledatabaseProjectsLocationsCloudVmClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudVmClusters/{cloudVmClustersId}',
        http_method='PATCH',
        method_id='oracledatabase.projects.locations.cloudVmClusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='cloudVmCluster',
        request_type_name='OracledatabaseProjectsLocationsCloudVmClustersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsDbSystemShapesService(base_api.BaseApiService):
    """Service class for the projects_locations_dbSystemShapes resource."""

    _NAME = 'projects_locations_dbSystemShapes'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsDbSystemShapesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single database system shape.

      Args:
        request: (OracledatabaseProjectsLocationsDbSystemShapesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DbSystemShape) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/dbSystemShapes/{dbSystemShapesId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.dbSystemShapes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsDbSystemShapesGetRequest',
        response_type_name='DbSystemShape',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the database system shapes available for the project and location.

      Args:
        request: (OracledatabaseProjectsLocationsDbSystemShapesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDbSystemShapesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/dbSystemShapes',
        http_method='GET',
        method_id='oracledatabase.projects.locations.dbSystemShapes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/dbSystemShapes',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsDbSystemShapesListRequest',
        response_type_name='ListDbSystemShapesResponse',
        supports_download=False,
    )

  class ProjectsLocationsEntitlementsService(base_api.BaseApiService):
    """Service class for the projects_locations_entitlements resource."""

    _NAME = 'projects_locations_entitlements'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsEntitlementsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the entitlements of a given project.

      Args:
        request: (OracledatabaseProjectsLocationsEntitlementsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Entitlement) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements/{entitlementsId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.entitlements.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsEntitlementsGetRequest',
        response_type_name='Entitlement',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the entitlements in a given project.

      Args:
        request: (OracledatabaseProjectsLocationsEntitlementsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEntitlementsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements',
        http_method='GET',
        method_id='oracledatabase.projects.locations.entitlements.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/entitlements',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsEntitlementsListRequest',
        response_type_name='ListEntitlementsResponse',
        supports_download=False,
    )

  class ProjectsLocationsExadbVmClustersDbNodesService(base_api.BaseApiService):
    """Service class for the projects_locations_exadbVmClusters_dbNodes resource."""

    _NAME = 'projects_locations_exadbVmClusters_dbNodes'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsExadbVmClustersDbNodesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about the specified database node.

      Args:
        request: (OracledatabaseProjectsLocationsExadbVmClustersDbNodesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DbNode) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/exadbVmClusters/{exadbVmClustersId}/dbNodes/{dbNodesId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.exadbVmClusters.dbNodes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsExadbVmClustersDbNodesGetRequest',
        response_type_name='DbNode',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the database nodes of a VM Cluster.

      Args:
        request: (OracledatabaseProjectsLocationsExadbVmClustersDbNodesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDbNodesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/exadbVmClusters/{exadbVmClustersId}/dbNodes',
        http_method='GET',
        method_id='oracledatabase.projects.locations.exadbVmClusters.dbNodes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/dbNodes',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsExadbVmClustersDbNodesListRequest',
        response_type_name='ListDbNodesResponse',
        supports_download=False,
    )

  class ProjectsLocationsExadbVmClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_exadbVmClusters resource."""

    _NAME = 'projects_locations_exadbVmClusters'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsExadbVmClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Exadb (Exascale) VM Cluster resource.

      Args:
        request: (OracledatabaseProjectsLocationsExadbVmClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/exadbVmClusters',
        http_method='POST',
        method_id='oracledatabase.projects.locations.exadbVmClusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['exadbVmClusterId', 'requestId'],
        relative_path='v1alpha/{+parent}/exadbVmClusters',
        request_field='exadbVmCluster',
        request_type_name='OracledatabaseProjectsLocationsExadbVmClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Exadb (Exascale) VM Cluster.

      Args:
        request: (OracledatabaseProjectsLocationsExadbVmClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/exadbVmClusters/{exadbVmClustersId}',
        http_method='DELETE',
        method_id='oracledatabase.projects.locations.exadbVmClusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsExadbVmClustersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Exadb (Exascale) VM Cluster.

      Args:
        request: (OracledatabaseProjectsLocationsExadbVmClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ExadbVmCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/exadbVmClusters/{exadbVmClustersId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.exadbVmClusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsExadbVmClustersGetRequest',
        response_type_name='ExadbVmCluster',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the Exadb (Exascale) VM Clusters for the given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsExadbVmClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListExadbVmClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/exadbVmClusters',
        http_method='GET',
        method_id='oracledatabase.projects.locations.exadbVmClusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/exadbVmClusters',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsExadbVmClustersListRequest',
        response_type_name='ListExadbVmClustersResponse',
        supports_download=False,
    )

  class ProjectsLocationsExascaleDbStorageVaultsService(base_api.BaseApiService):
    """Service class for the projects_locations_exascaleDbStorageVaults resource."""

    _NAME = 'projects_locations_exascaleDbStorageVaults'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsExascaleDbStorageVaultsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a single ExascaleDB Storage Vault.

      Args:
        request: (OracledatabaseProjectsLocationsExascaleDbStorageVaultsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/exascaleDbStorageVaults/{exascaleDbStorageVaultsId}',
        http_method='DELETE',
        method_id='oracledatabase.projects.locations.exascaleDbStorageVaults.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsExascaleDbStorageVaultsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ExascaleDB Storage Vault.

      Args:
        request: (OracledatabaseProjectsLocationsExascaleDbStorageVaultsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ExascaleDbStorageVault) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/exascaleDbStorageVaults/{exascaleDbStorageVaultsId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.exascaleDbStorageVaults.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsExascaleDbStorageVaultsGetRequest',
        response_type_name='ExascaleDbStorageVault',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the ExascaleDB Storage Vaults for the given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsExascaleDbStorageVaultsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListExascaleDbStorageVaultsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/exascaleDbStorageVaults',
        http_method='GET',
        method_id='oracledatabase.projects.locations.exascaleDbStorageVaults.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/exascaleDbStorageVaults',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsExascaleDbStorageVaultsListRequest',
        response_type_name='ListExascaleDbStorageVaultsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGiVersionsMinorVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_giVersions_minorVersions resource."""

    _NAME = 'projects_locations_giVersions_minorVersions'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsGiVersionsMinorVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single minor version.

      Args:
        request: (OracledatabaseProjectsLocationsGiVersionsMinorVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MinorVersion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/giVersions/{giVersionsId}/minorVersions/{minorVersionsId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.giVersions.minorVersions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsGiVersionsMinorVersionsGetRequest',
        response_type_name='MinorVersion',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the valid minor versions for the given project, location, gi version and shape family.

      Args:
        request: (OracledatabaseProjectsLocationsGiVersionsMinorVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMinorVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/giVersions/{giVersionsId}/minorVersions',
        http_method='GET',
        method_id='oracledatabase.projects.locations.giVersions.minorVersions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/minorVersions',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsGiVersionsMinorVersionsListRequest',
        response_type_name='ListMinorVersionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGiVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_giVersions resource."""

    _NAME = 'projects_locations_giVersions'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsGiVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single grid infrastructure version.

      Args:
        request: (OracledatabaseProjectsLocationsGiVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GiVersion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/giVersions/{giVersionsId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.giVersions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsGiVersionsGetRequest',
        response_type_name='GiVersion',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the valid Oracle Grid Infrastructure (GI) versions for the given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsGiVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGiVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/giVersions',
        http_method='GET',
        method_id='oracledatabase.projects.locations.giVersions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/giVersions',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsGiVersionsListRequest',
        response_type_name='ListGiVersionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOdbNetworksOdbSubnetsService(base_api.BaseApiService):
    """Service class for the projects_locations_odbNetworks_odbSubnets resource."""

    _NAME = 'projects_locations_odbNetworks_odbSubnets'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsOdbNetworksOdbSubnetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ODB Subnet in a given ODB Network.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/odbNetworks/{odbNetworksId}/odbSubnets',
        http_method='POST',
        method_id='oracledatabase.projects.locations.odbNetworks.odbSubnets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['odbSubnetId', 'requestId'],
        relative_path='v1alpha/{+parent}/odbSubnets',
        request_field='odbSubnet',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ODB Subnet.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/odbNetworks/{odbNetworksId}/odbSubnets/{odbSubnetsId}',
        http_method='DELETE',
        method_id='oracledatabase.projects.locations.odbNetworks.odbSubnets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ODB Subnet.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OdbSubnet) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/odbNetworks/{odbNetworksId}/odbSubnets/{odbSubnetsId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.odbNetworks.odbSubnets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsGetRequest',
        response_type_name='OdbSubnet',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the ODB Subnets in a given ODB Network.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOdbSubnetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/odbNetworks/{odbNetworksId}/odbSubnets',
        http_method='GET',
        method_id='oracledatabase.projects.locations.odbNetworks.odbSubnets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/odbSubnets',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsListRequest',
        response_type_name='ListOdbSubnetsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOdbNetworksService(base_api.BaseApiService):
    """Service class for the projects_locations_odbNetworks resource."""

    _NAME = 'projects_locations_odbNetworks'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsOdbNetworksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ODB Network in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/odbNetworks',
        http_method='POST',
        method_id='oracledatabase.projects.locations.odbNetworks.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['odbNetworkId', 'requestId'],
        relative_path='v1alpha/{+parent}/odbNetworks',
        request_field='odbNetwork',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ODB Network.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/odbNetworks/{odbNetworksId}',
        http_method='DELETE',
        method_id='oracledatabase.projects.locations.odbNetworks.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ODB Network.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OdbNetwork) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/odbNetworks/{odbNetworksId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.odbNetworks.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksGetRequest',
        response_type_name='OdbNetwork',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the ODB Networks in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOdbNetworksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/odbNetworks',
        http_method='GET',
        method_id='oracledatabase.projects.locations.odbNetworks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/odbNetworks',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksListRequest',
        response_type_name='ListOdbNetworksResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (OracledatabaseProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='oracledatabase.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='OracledatabaseProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (OracledatabaseProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='oracledatabase.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (OracledatabaseProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (OracledatabaseProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='oracledatabase.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/operations',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSystemVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_systemVersions resource."""

    _NAME = 'projects_locations_systemVersions'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsSystemVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a system version.

      Args:
        request: (OracledatabaseProjectsLocationsSystemVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SystemVersion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/systemVersions/{systemVersionsId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.systemVersions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsSystemVersionsGetRequest',
        response_type_name='SystemVersion',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the system versions available for the project and location.

      Args:
        request: (OracledatabaseProjectsLocationsSystemVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSystemVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/systemVersions',
        http_method='GET',
        method_id='oracledatabase.projects.locations.systemVersions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/systemVersions',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsSystemVersionsListRequest',
        response_type_name='ListSystemVersionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (OracledatabaseProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (OracledatabaseProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations',
        http_method='GET',
        method_id='oracledatabase.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/locations',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(OracledatabaseV1alpha.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
