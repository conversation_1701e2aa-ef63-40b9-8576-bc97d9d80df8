"""Generated client library for oracledatabase version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.oracledatabase.v1 import oracledatabase_v1_messages as messages


class OracledatabaseV1(base_api.BaseApiClient):
  """Generated client library for service oracledatabase version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://oracledatabase.googleapis.com/'
  MTLS_BASE_URL = 'https://oracledatabase.mtls.googleapis.com/'

  _PACKAGE = 'oracledatabase'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'OracledatabaseV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new oracledatabase handle."""
    url = url or self.BASE_URL
    super(OracledatabaseV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_autonomousDatabaseBackups = self.ProjectsLocationsAutonomousDatabaseBackupsService(self)
    self.projects_locations_autonomousDatabaseCharacterSets = self.ProjectsLocationsAutonomousDatabaseCharacterSetsService(self)
    self.projects_locations_autonomousDatabases = self.ProjectsLocationsAutonomousDatabasesService(self)
    self.projects_locations_autonomousDbVersions = self.ProjectsLocationsAutonomousDbVersionsService(self)
    self.projects_locations_cloudExadataInfrastructures_dbServers = self.ProjectsLocationsCloudExadataInfrastructuresDbServersService(self)
    self.projects_locations_cloudExadataInfrastructures = self.ProjectsLocationsCloudExadataInfrastructuresService(self)
    self.projects_locations_cloudVmClusters_dbNodes = self.ProjectsLocationsCloudVmClustersDbNodesService(self)
    self.projects_locations_cloudVmClusters = self.ProjectsLocationsCloudVmClustersService(self)
    self.projects_locations_dbSystemShapes = self.ProjectsLocationsDbSystemShapesService(self)
    self.projects_locations_entitlements = self.ProjectsLocationsEntitlementsService(self)
    self.projects_locations_giVersions = self.ProjectsLocationsGiVersionsService(self)
    self.projects_locations_odbNetworks_odbSubnets = self.ProjectsLocationsOdbNetworksOdbSubnetsService(self)
    self.projects_locations_odbNetworks = self.ProjectsLocationsOdbNetworksService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsAutonomousDatabaseBackupsService(base_api.BaseApiService):
    """Service class for the projects_locations_autonomousDatabaseBackups resource."""

    _NAME = 'projects_locations_autonomousDatabaseBackups'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsAutonomousDatabaseBackupsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the long-term and automatic backups of an Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabaseBackupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAutonomousDatabaseBackupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autonomousDatabaseBackups',
        http_method='GET',
        method_id='oracledatabase.projects.locations.autonomousDatabaseBackups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/autonomousDatabaseBackups',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabaseBackupsListRequest',
        response_type_name='ListAutonomousDatabaseBackupsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAutonomousDatabaseCharacterSetsService(base_api.BaseApiService):
    """Service class for the projects_locations_autonomousDatabaseCharacterSets resource."""

    _NAME = 'projects_locations_autonomousDatabaseCharacterSets'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsAutonomousDatabaseCharacterSetsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists Autonomous Database Character Sets in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabaseCharacterSetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAutonomousDatabaseCharacterSetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autonomousDatabaseCharacterSets',
        http_method='GET',
        method_id='oracledatabase.projects.locations.autonomousDatabaseCharacterSets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/autonomousDatabaseCharacterSets',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabaseCharacterSetsListRequest',
        response_type_name='ListAutonomousDatabaseCharacterSetsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAutonomousDatabasesService(base_api.BaseApiService):
    """Service class for the projects_locations_autonomousDatabases resource."""

    _NAME = 'projects_locations_autonomousDatabases'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsAutonomousDatabasesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Autonomous Database in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autonomousDatabases',
        http_method='POST',
        method_id='oracledatabase.projects.locations.autonomousDatabases.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['autonomousDatabaseId', 'requestId'],
        relative_path='v1/{+parent}/autonomousDatabases',
        request_field='autonomousDatabase',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}',
        http_method='DELETE',
        method_id='oracledatabase.projects.locations.autonomousDatabases.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def GenerateWallet(self, request, global_params=None):
      r"""Generates a wallet for an Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesGenerateWalletRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GenerateAutonomousDatabaseWalletResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateWallet')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateWallet.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}:generateWallet',
        http_method='POST',
        method_id='oracledatabase.projects.locations.autonomousDatabases.generateWallet',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:generateWallet',
        request_field='generateAutonomousDatabaseWalletRequest',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesGenerateWalletRequest',
        response_type_name='GenerateAutonomousDatabaseWalletResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the details of a single Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AutonomousDatabase) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.autonomousDatabases.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesGetRequest',
        response_type_name='AutonomousDatabase',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Autonomous Databases in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAutonomousDatabasesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autonomousDatabases',
        http_method='GET',
        method_id='oracledatabase.projects.locations.autonomousDatabases.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/autonomousDatabases',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesListRequest',
        response_type_name='ListAutonomousDatabasesResponse',
        supports_download=False,
    )

    def Restart(self, request, global_params=None):
      r"""Restarts an Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesRestartRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Restart')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restart.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}:restart',
        http_method='POST',
        method_id='oracledatabase.projects.locations.autonomousDatabases.restart',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:restart',
        request_field='restartAutonomousDatabaseRequest',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesRestartRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Restore(self, request, global_params=None):
      r"""Restores a single Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Restore')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restore.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}:restore',
        http_method='POST',
        method_id='oracledatabase.projects.locations.autonomousDatabases.restore',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:restore',
        request_field='restoreAutonomousDatabaseRequest',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesRestoreRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Start(self, request, global_params=None):
      r"""Starts an Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesStartRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Start')
      return self._RunMethod(
          config, request, global_params=global_params)

    Start.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}:start',
        http_method='POST',
        method_id='oracledatabase.projects.locations.autonomousDatabases.start',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:start',
        request_field='startAutonomousDatabaseRequest',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesStartRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stops an Autonomous Database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}:stop',
        http_method='POST',
        method_id='oracledatabase.projects.locations.autonomousDatabases.stop',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:stop',
        request_field='stopAutonomousDatabaseRequest',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesStopRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Switchover(self, request, global_params=None):
      r"""Initiates a switchover of specified autonomous database to the associated peer database.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDatabasesSwitchoverRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Switchover')
      return self._RunMethod(
          config, request, global_params=global_params)

    Switchover.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autonomousDatabases/{autonomousDatabasesId}:switchover',
        http_method='POST',
        method_id='oracledatabase.projects.locations.autonomousDatabases.switchover',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:switchover',
        request_field='switchoverAutonomousDatabaseRequest',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDatabasesSwitchoverRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsAutonomousDbVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_autonomousDbVersions resource."""

    _NAME = 'projects_locations_autonomousDbVersions'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsAutonomousDbVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all the available Autonomous Database versions for a project and location.

      Args:
        request: (OracledatabaseProjectsLocationsAutonomousDbVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAutonomousDbVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autonomousDbVersions',
        http_method='GET',
        method_id='oracledatabase.projects.locations.autonomousDbVersions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/autonomousDbVersions',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsAutonomousDbVersionsListRequest',
        response_type_name='ListAutonomousDbVersionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsCloudExadataInfrastructuresDbServersService(base_api.BaseApiService):
    """Service class for the projects_locations_cloudExadataInfrastructures_dbServers resource."""

    _NAME = 'projects_locations_cloudExadataInfrastructures_dbServers'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsCloudExadataInfrastructuresDbServersService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the database servers of an Exadata Infrastructure instance.

      Args:
        request: (OracledatabaseProjectsLocationsCloudExadataInfrastructuresDbServersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDbServersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cloudExadataInfrastructures/{cloudExadataInfrastructuresId}/dbServers',
        http_method='GET',
        method_id='oracledatabase.projects.locations.cloudExadataInfrastructures.dbServers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/dbServers',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudExadataInfrastructuresDbServersListRequest',
        response_type_name='ListDbServersResponse',
        supports_download=False,
    )

  class ProjectsLocationsCloudExadataInfrastructuresService(base_api.BaseApiService):
    """Service class for the projects_locations_cloudExadataInfrastructures resource."""

    _NAME = 'projects_locations_cloudExadataInfrastructures'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsCloudExadataInfrastructuresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Exadata Infrastructure in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsCloudExadataInfrastructuresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cloudExadataInfrastructures',
        http_method='POST',
        method_id='oracledatabase.projects.locations.cloudExadataInfrastructures.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['cloudExadataInfrastructureId', 'requestId'],
        relative_path='v1/{+parent}/cloudExadataInfrastructures',
        request_field='cloudExadataInfrastructure',
        request_type_name='OracledatabaseProjectsLocationsCloudExadataInfrastructuresCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Exadata Infrastructure.

      Args:
        request: (OracledatabaseProjectsLocationsCloudExadataInfrastructuresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cloudExadataInfrastructures/{cloudExadataInfrastructuresId}',
        http_method='DELETE',
        method_id='oracledatabase.projects.locations.cloudExadataInfrastructures.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudExadataInfrastructuresDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Exadata Infrastructure.

      Args:
        request: (OracledatabaseProjectsLocationsCloudExadataInfrastructuresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CloudExadataInfrastructure) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cloudExadataInfrastructures/{cloudExadataInfrastructuresId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.cloudExadataInfrastructures.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudExadataInfrastructuresGetRequest',
        response_type_name='CloudExadataInfrastructure',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Exadata Infrastructures in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsCloudExadataInfrastructuresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCloudExadataInfrastructuresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cloudExadataInfrastructures',
        http_method='GET',
        method_id='oracledatabase.projects.locations.cloudExadataInfrastructures.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/cloudExadataInfrastructures',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudExadataInfrastructuresListRequest',
        response_type_name='ListCloudExadataInfrastructuresResponse',
        supports_download=False,
    )

  class ProjectsLocationsCloudVmClustersDbNodesService(base_api.BaseApiService):
    """Service class for the projects_locations_cloudVmClusters_dbNodes resource."""

    _NAME = 'projects_locations_cloudVmClusters_dbNodes'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsCloudVmClustersDbNodesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the database nodes of a VM Cluster.

      Args:
        request: (OracledatabaseProjectsLocationsCloudVmClustersDbNodesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDbNodesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cloudVmClusters/{cloudVmClustersId}/dbNodes',
        http_method='GET',
        method_id='oracledatabase.projects.locations.cloudVmClusters.dbNodes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/dbNodes',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudVmClustersDbNodesListRequest',
        response_type_name='ListDbNodesResponse',
        supports_download=False,
    )

  class ProjectsLocationsCloudVmClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_cloudVmClusters resource."""

    _NAME = 'projects_locations_cloudVmClusters'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsCloudVmClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new VM Cluster in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsCloudVmClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cloudVmClusters',
        http_method='POST',
        method_id='oracledatabase.projects.locations.cloudVmClusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['cloudVmClusterId', 'requestId'],
        relative_path='v1/{+parent}/cloudVmClusters',
        request_field='cloudVmCluster',
        request_type_name='OracledatabaseProjectsLocationsCloudVmClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single VM Cluster.

      Args:
        request: (OracledatabaseProjectsLocationsCloudVmClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cloudVmClusters/{cloudVmClustersId}',
        http_method='DELETE',
        method_id='oracledatabase.projects.locations.cloudVmClusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudVmClustersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single VM Cluster.

      Args:
        request: (OracledatabaseProjectsLocationsCloudVmClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CloudVmCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cloudVmClusters/{cloudVmClustersId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.cloudVmClusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudVmClustersGetRequest',
        response_type_name='CloudVmCluster',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the VM Clusters in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsCloudVmClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCloudVmClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/cloudVmClusters',
        http_method='GET',
        method_id='oracledatabase.projects.locations.cloudVmClusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/cloudVmClusters',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsCloudVmClustersListRequest',
        response_type_name='ListCloudVmClustersResponse',
        supports_download=False,
    )

  class ProjectsLocationsDbSystemShapesService(base_api.BaseApiService):
    """Service class for the projects_locations_dbSystemShapes resource."""

    _NAME = 'projects_locations_dbSystemShapes'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsDbSystemShapesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the database system shapes available for the project and location.

      Args:
        request: (OracledatabaseProjectsLocationsDbSystemShapesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDbSystemShapesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/dbSystemShapes',
        http_method='GET',
        method_id='oracledatabase.projects.locations.dbSystemShapes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/dbSystemShapes',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsDbSystemShapesListRequest',
        response_type_name='ListDbSystemShapesResponse',
        supports_download=False,
    )

  class ProjectsLocationsEntitlementsService(base_api.BaseApiService):
    """Service class for the projects_locations_entitlements resource."""

    _NAME = 'projects_locations_entitlements'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsEntitlementsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the entitlements in a given project.

      Args:
        request: (OracledatabaseProjectsLocationsEntitlementsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEntitlementsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/entitlements',
        http_method='GET',
        method_id='oracledatabase.projects.locations.entitlements.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/entitlements',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsEntitlementsListRequest',
        response_type_name='ListEntitlementsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGiVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_giVersions resource."""

    _NAME = 'projects_locations_giVersions'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsGiVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all the valid Oracle Grid Infrastructure (GI) versions for the given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsGiVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGiVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/giVersions',
        http_method='GET',
        method_id='oracledatabase.projects.locations.giVersions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/giVersions',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsGiVersionsListRequest',
        response_type_name='ListGiVersionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOdbNetworksOdbSubnetsService(base_api.BaseApiService):
    """Service class for the projects_locations_odbNetworks_odbSubnets resource."""

    _NAME = 'projects_locations_odbNetworks_odbSubnets'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsOdbNetworksOdbSubnetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ODB Subnet in a given ODB Network.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/odbNetworks/{odbNetworksId}/odbSubnets',
        http_method='POST',
        method_id='oracledatabase.projects.locations.odbNetworks.odbSubnets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['odbSubnetId', 'requestId'],
        relative_path='v1/{+parent}/odbSubnets',
        request_field='odbSubnet',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ODB Subnet.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/odbNetworks/{odbNetworksId}/odbSubnets/{odbSubnetsId}',
        http_method='DELETE',
        method_id='oracledatabase.projects.locations.odbNetworks.odbSubnets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ODB Subnet.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OdbSubnet) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/odbNetworks/{odbNetworksId}/odbSubnets/{odbSubnetsId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.odbNetworks.odbSubnets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsGetRequest',
        response_type_name='OdbSubnet',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the ODB Subnets in a given ODB Network.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOdbSubnetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/odbNetworks/{odbNetworksId}/odbSubnets',
        http_method='GET',
        method_id='oracledatabase.projects.locations.odbNetworks.odbSubnets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/odbSubnets',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksOdbSubnetsListRequest',
        response_type_name='ListOdbSubnetsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOdbNetworksService(base_api.BaseApiService):
    """Service class for the projects_locations_odbNetworks resource."""

    _NAME = 'projects_locations_odbNetworks'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsOdbNetworksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ODB Network in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/odbNetworks',
        http_method='POST',
        method_id='oracledatabase.projects.locations.odbNetworks.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['odbNetworkId', 'requestId'],
        relative_path='v1/{+parent}/odbNetworks',
        request_field='odbNetwork',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ODB Network.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/odbNetworks/{odbNetworksId}',
        http_method='DELETE',
        method_id='oracledatabase.projects.locations.odbNetworks.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ODB Network.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OdbNetwork) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/odbNetworks/{odbNetworksId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.odbNetworks.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksGetRequest',
        response_type_name='OdbNetwork',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the ODB Networks in a given project and location.

      Args:
        request: (OracledatabaseProjectsLocationsOdbNetworksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOdbNetworksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/odbNetworks',
        http_method='GET',
        method_id='oracledatabase.projects.locations.odbNetworks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/odbNetworks',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOdbNetworksListRequest',
        response_type_name='ListOdbNetworksResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (OracledatabaseProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='oracledatabase.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='OracledatabaseProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (OracledatabaseProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='oracledatabase.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (OracledatabaseProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (OracledatabaseProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='oracledatabase.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (OracledatabaseProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='oracledatabase.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (OracledatabaseProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='oracledatabase.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='OracledatabaseProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(OracledatabaseV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
