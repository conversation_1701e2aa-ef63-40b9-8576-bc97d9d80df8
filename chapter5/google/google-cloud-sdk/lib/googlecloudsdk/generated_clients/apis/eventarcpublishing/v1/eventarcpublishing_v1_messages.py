"""Generated message classes for eventarcpublishing version v1.

Processes events generated by an event provider and delivers them to a
subscriber.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'eventarcpublishing'


class EventarcpublishingProjectsLocationsChannelConnectionsPublishEventsRequest(_messages.Message):
  r"""A
  EventarcpublishingProjectsLocationsChannelConnectionsPublishEventsRequest
  object.

  Fields:
    channelConnection: The channel_connection that the events are published
      from. For example: `projects/{partner_project_id}/locations/{location}/c
      hannelConnections/{channel_connection_id}`.
    googleCloudEventarcPublishingV1PublishChannelConnectionEventsRequest: A
      GoogleCloudEventarcPublishingV1PublishChannelConnectionEventsRequest
      resource to be passed as the request body.
  """

  channelConnection = _messages.StringField(1, required=True)
  googleCloudEventarcPublishingV1PublishChannelConnectionEventsRequest = _messages.MessageField('GoogleCloudEventarcPublishingV1PublishChannelConnectionEventsRequest', 2)


class EventarcpublishingProjectsLocationsChannelsPublishEventsRequest(_messages.Message):
  r"""A EventarcpublishingProjectsLocationsChannelsPublishEventsRequest
  object.

  Fields:
    channel: The full name of the channel to publish to. For example:
      `projects/{project}/locations/{location}/channels/{channel-id}`.
    googleCloudEventarcPublishingV1PublishEventsRequest: A
      GoogleCloudEventarcPublishingV1PublishEventsRequest resource to be
      passed as the request body.
  """

  channel = _messages.StringField(1, required=True)
  googleCloudEventarcPublishingV1PublishEventsRequest = _messages.MessageField('GoogleCloudEventarcPublishingV1PublishEventsRequest', 2)


class EventarcpublishingProjectsLocationsMessageBusesPublishRequest(_messages.Message):
  r"""A EventarcpublishingProjectsLocationsMessageBusesPublishRequest object.

  Fields:
    googleCloudEventarcPublishingV1PublishRequest: A
      GoogleCloudEventarcPublishingV1PublishRequest resource to be passed as
      the request body.
    messageBus: Required. The full name of the message bus to publish events
      to. Format:
      `projects/{project}/locations/{location}/messageBuses/{messageBus}`.
  """

  googleCloudEventarcPublishingV1PublishRequest = _messages.MessageField('GoogleCloudEventarcPublishingV1PublishRequest', 1)
  messageBus = _messages.StringField(2, required=True)


class GoogleCloudEventarcPublishingV1CloudEvent(_messages.Message):
  r"""CloudEvent represents a vendor-neutral specification for defining the
  format of event data.

  Messages:
    AttributesValue: Optional. Used for Optional & Extension Attributes
    ProtoDataValue: Optional. Proto data.

  Fields:
    attributes: Optional. Used for Optional & Extension Attributes
    binaryData: Optional. Binary data.
    id: Required. Identifies the event. Producers MUST ensure that source + id
      is unique for each distinct event.
    protoData: Optional. Proto data.
    source: Required. Identifies the context in which an event happened. URI-
      reference
    specVersion: Required. The version of the CloudEvents specification which
      the event uses.
    textData: Optional. Text data.
    type: Required. This attribute contains a value describing the type of
      event related to the originating occurrence.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional. Used for Optional & Extension Attributes

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A
          GoogleCloudEventarcPublishingV1CloudEventCloudEventAttributeValue
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudEventarcPublishingV1CloudEventCloudEventAttributeValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ProtoDataValue(_messages.Message):
    r"""Optional. Proto data.

    Messages:
      AdditionalProperty: An additional property for a ProtoDataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ProtoDataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('AttributesValue', 1)
  binaryData = _messages.BytesField(2)
  id = _messages.StringField(3)
  protoData = _messages.MessageField('ProtoDataValue', 4)
  source = _messages.StringField(5)
  specVersion = _messages.StringField(6)
  textData = _messages.StringField(7)
  type = _messages.StringField(8)


class GoogleCloudEventarcPublishingV1CloudEventCloudEventAttributeValue(_messages.Message):
  r"""The following abstract data types are available for use in attributes.

  Fields:
    ceBoolean: Boolean value.
    ceBytes: Bytes value.
    ceInteger: Integer value.
    ceString: String value.
    ceTimestamp: Timestamp value.
    ceUri: URI value.
    ceUriRef: URI-reference value.
  """

  ceBoolean = _messages.BooleanField(1)
  ceBytes = _messages.BytesField(2)
  ceInteger = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  ceString = _messages.StringField(4)
  ceTimestamp = _messages.StringField(5)
  ceUri = _messages.StringField(6)
  ceUriRef = _messages.StringField(7)


class GoogleCloudEventarcPublishingV1PublishChannelConnectionEventsRequest(_messages.Message):
  r"""The request message for the PublishChannelConnectionEvents method.

  Messages:
    EventsValueListEntry: A EventsValueListEntry object.

  Fields:
    events: The CloudEvents v1.0 events to publish. No other types are
      allowed. If this field is set, then the `text_events` fields must not be
      set.
    textEvents: The text representation of events to publish. CloudEvent v1.0
      in JSON format is the only allowed type. Refer to https://github.com/clo
      udevents/spec/blob/v1.0.2/cloudevents/formats/json-format.md for
      specification. If this field is set, then the `events` fields must not
      be set.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EventsValueListEntry(_messages.Message):
    r"""A EventsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a EventsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EventsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  events = _messages.MessageField('EventsValueListEntry', 1, repeated=True)
  textEvents = _messages.StringField(2, repeated=True)


class GoogleCloudEventarcPublishingV1PublishChannelConnectionEventsResponse(_messages.Message):
  r"""The response message for the PublishChannelConnectionEvents method."""


class GoogleCloudEventarcPublishingV1PublishEventsRequest(_messages.Message):
  r"""The request message for the PublishEvents method.

  Messages:
    EventsValueListEntry: A EventsValueListEntry object.

  Fields:
    events: The CloudEvents v1.0 events to publish. No other types are
      allowed. If this field is set, then the `text_events` fields must not be
      set.
    textEvents: The text representation of events to publish. CloudEvent v1.0
      in JSON format is the only allowed type. Refer to https://github.com/clo
      udevents/spec/blob/v1.0.2/cloudevents/formats/json-format.md for
      specification. If this field is set, then the `events` fields must not
      be set.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EventsValueListEntry(_messages.Message):
    r"""A EventsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a EventsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EventsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  events = _messages.MessageField('EventsValueListEntry', 1, repeated=True)
  textEvents = _messages.StringField(2, repeated=True)


class GoogleCloudEventarcPublishingV1PublishEventsResponse(_messages.Message):
  r"""The response message for the PublishEvents method."""


class GoogleCloudEventarcPublishingV1PublishRequest(_messages.Message):
  r"""The request message for the Publish method.

  Fields:
    avroMessage: The Avro format of the CloudEvent being published.
      Specification can be found here: https://github.com/cloudevents/spec/blo
      b/v1.0.2/cloudevents/formats/avro-format.md
    jsonMessage: The JSON format of the CloudEvent being published.
      Specification can be found here: https://github.com/cloudevents/spec/blo
      b/v1.0.2/cloudevents/formats/json-format.md
    messageUid: Optional. This field provides a message_uid override uniquely
      associated with a message in Eventarc Advanced systems, and should be
      propagated by the first ingestion point of the message.
    protoMessage: The Protobuf format of the CloudEvent being published.
      Specification can be found here: https://github.com/cloudevents/spec/blo
      b/v1.0.2/cloudevents/formats/protobuf-format.md
  """

  avroMessage = _messages.BytesField(1)
  jsonMessage = _messages.StringField(2)
  messageUid = _messages.StringField(3)
  protoMessage = _messages.MessageField('GoogleCloudEventarcPublishingV1CloudEvent', 4)


class GoogleCloudEventarcPublishingV1PublishResponse(_messages.Message):
  r"""The response message for the Publish method."""


class IoCloudeventsV1CloudEvent(_messages.Message):
  r"""-- CloudEvent Context Attributes

  Messages:
    AttributesValue: Optional & Extension Attributes
    ProtoDataValue: A ProtoDataValue object.

  Fields:
    attributes: Optional & Extension Attributes
    binaryData: A byte attribute.
    id: Required Attributes
    protoData: A ProtoDataValue attribute.
    source: URI-reference
    specVersion: A string attribute.
    textData: A string attribute.
    type: A string attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional & Extension Attributes

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A IoCloudeventsV1CloudEventCloudEventAttributeValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('IoCloudeventsV1CloudEventCloudEventAttributeValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ProtoDataValue(_messages.Message):
    r"""A ProtoDataValue object.

    Messages:
      AdditionalProperty: An additional property for a ProtoDataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ProtoDataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('AttributesValue', 1)
  binaryData = _messages.BytesField(2)
  id = _messages.StringField(3)
  protoData = _messages.MessageField('ProtoDataValue', 4)
  source = _messages.StringField(5)
  specVersion = _messages.StringField(6)
  textData = _messages.StringField(7)
  type = _messages.StringField(8)


class IoCloudeventsV1CloudEventCloudEventAttributeValue(_messages.Message):
  r"""A IoCloudeventsV1CloudEventCloudEventAttributeValue object.

  Fields:
    ceBoolean: A boolean attribute.
    ceBytes: A byte attribute.
    ceInteger: A integer attribute.
    ceString: A string attribute.
    ceTimestamp: A string attribute.
    ceUri: A string attribute.
    ceUriRef: A string attribute.
  """

  ceBoolean = _messages.BooleanField(1)
  ceBytes = _messages.BytesField(2)
  ceInteger = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  ceString = _messages.StringField(4)
  ceTimestamp = _messages.StringField(5)
  ceUri = _messages.StringField(6)
  ceUriRef = _messages.StringField(7)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
