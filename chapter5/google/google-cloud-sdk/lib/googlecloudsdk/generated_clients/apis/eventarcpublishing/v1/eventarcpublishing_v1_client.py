"""Generated client library for eventarcpublishing version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.eventarcpublishing.v1 import eventarcpublishing_v1_messages as messages


class EventarcpublishingV1(base_api.BaseApiClient):
  """Generated client library for service eventarcpublishing version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://eventarcpublishing.googleapis.com/'
  MTLS_BASE_URL = 'https://eventarcpublishing.mtls.googleapis.com/'

  _PACKAGE = 'eventarcpublishing'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'EventarcpublishingV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new eventarcpublishing handle."""
    url = url or self.BASE_URL
    super(EventarcpublishingV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_channelConnections = self.ProjectsLocationsChannelConnectionsService(self)
    self.projects_locations_channels = self.ProjectsLocationsChannelsService(self)
    self.projects_locations_messageBuses = self.ProjectsLocationsMessageBusesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsChannelConnectionsService(base_api.BaseApiService):
    """Service class for the projects_locations_channelConnections resource."""

    _NAME = 'projects_locations_channelConnections'

    def __init__(self, client):
      super(EventarcpublishingV1.ProjectsLocationsChannelConnectionsService, self).__init__(client)
      self._upload_configs = {
          }

    def PublishEvents(self, request, global_params=None):
      r"""Publish events to a ChannelConnection in a partner's project.

      Args:
        request: (EventarcpublishingProjectsLocationsChannelConnectionsPublishEventsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudEventarcPublishingV1PublishChannelConnectionEventsResponse) The response message.
      """
      config = self.GetMethodConfig('PublishEvents')
      return self._RunMethod(
          config, request, global_params=global_params)

    PublishEvents.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channelConnections/{channelConnectionsId}:publishEvents',
        http_method='POST',
        method_id='eventarcpublishing.projects.locations.channelConnections.publishEvents',
        ordered_params=['channelConnection'],
        path_params=['channelConnection'],
        query_params=[],
        relative_path='v1/{+channelConnection}:publishEvents',
        request_field='googleCloudEventarcPublishingV1PublishChannelConnectionEventsRequest',
        request_type_name='EventarcpublishingProjectsLocationsChannelConnectionsPublishEventsRequest',
        response_type_name='GoogleCloudEventarcPublishingV1PublishChannelConnectionEventsResponse',
        supports_download=False,
    )

  class ProjectsLocationsChannelsService(base_api.BaseApiService):
    """Service class for the projects_locations_channels resource."""

    _NAME = 'projects_locations_channels'

    def __init__(self, client):
      super(EventarcpublishingV1.ProjectsLocationsChannelsService, self).__init__(client)
      self._upload_configs = {
          }

    def PublishEvents(self, request, global_params=None):
      r"""Publish events to a subscriber's channel.

      Args:
        request: (EventarcpublishingProjectsLocationsChannelsPublishEventsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudEventarcPublishingV1PublishEventsResponse) The response message.
      """
      config = self.GetMethodConfig('PublishEvents')
      return self._RunMethod(
          config, request, global_params=global_params)

    PublishEvents.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channels/{channelsId}:publishEvents',
        http_method='POST',
        method_id='eventarcpublishing.projects.locations.channels.publishEvents',
        ordered_params=['channel'],
        path_params=['channel'],
        query_params=[],
        relative_path='v1/{+channel}:publishEvents',
        request_field='googleCloudEventarcPublishingV1PublishEventsRequest',
        request_type_name='EventarcpublishingProjectsLocationsChannelsPublishEventsRequest',
        response_type_name='GoogleCloudEventarcPublishingV1PublishEventsResponse',
        supports_download=False,
    )

  class ProjectsLocationsMessageBusesService(base_api.BaseApiService):
    """Service class for the projects_locations_messageBuses resource."""

    _NAME = 'projects_locations_messageBuses'

    def __init__(self, client):
      super(EventarcpublishingV1.ProjectsLocationsMessageBusesService, self).__init__(client)
      self._upload_configs = {
          }

    def Publish(self, request, global_params=None):
      r"""Publish events to a message bus.

      Args:
        request: (EventarcpublishingProjectsLocationsMessageBusesPublishRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudEventarcPublishingV1PublishResponse) The response message.
      """
      config = self.GetMethodConfig('Publish')
      return self._RunMethod(
          config, request, global_params=global_params)

    Publish.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}:publish',
        http_method='POST',
        method_id='eventarcpublishing.projects.locations.messageBuses.publish',
        ordered_params=['messageBus'],
        path_params=['messageBus'],
        query_params=[],
        relative_path='v1/{+messageBus}:publish',
        request_field='googleCloudEventarcPublishingV1PublishRequest',
        request_type_name='EventarcpublishingProjectsLocationsMessageBusesPublishRequest',
        response_type_name='GoogleCloudEventarcPublishingV1PublishResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(EventarcpublishingV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(EventarcpublishingV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
