"""Generated message classes for faultinjectiontesting version v1alpha1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'faultinjectiontesting'


class Abort(_messages.Message):
  r"""message for abort condtions

  Fields:
    httpStatus: The value must be between 200 and 599 inclusive.
    percentage: The value must be between 0 and 100 inclusive. Percentage of
      the traffic to be aborted.
  """

  httpStatus = _messages.IntegerField(1)
  percentage = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Delay(_messages.Message):
  r"""message for delay

  Fields:
    fixedDelay: Delay time for requests.
    percentage: Percentage of the network traffic to be delayed.
  """

  fixedDelay = _messages.StringField(1)
  percentage = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Experiment(_messages.Message):
  r"""Message describing Experiment object

  Fields:
    createTime: Output only. Create time stamp.
    description: End user description of the experiment.
    etag: Server specified etag
    name: Required. Identifier. The format for the name is:
      projects/{project}/locations/{location}/experiments/{experiment_name}
    runFaults: faults to run in experiment
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  etag = _messages.StringField(3)
  name = _messages.StringField(4)
  runFaults = _messages.MessageField('RunFault', 5, repeated=True)


class Fault(_messages.Message):
  r"""Message describing Fault object

  Fields:
    action: Actions to perform in this fault.
    createTime: Output only. [Output only] Create time stamp
    description: End user description of the fault.
    etag: Server specified etag
    name: Identifier. Unique name for the fault per project, provided by the
      end user. The format for the name is:
      projects/{project}/locations/{location}/faults/{fault_name}
  """

  action = _messages.MessageField('FaultInjectionAction', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  name = _messages.StringField(5)


class FaultApplyStatus(_messages.Message):
  r"""Message describing Faults and its apply status apphub targets.

  Enums:
    StateValueValuesEnum: Output only. Message describing fault status

  Fields:
    fault: Message describing the fault config
    faultTarget: Message describing the fault target.
    state: Output only. Message describing fault status
    status: Output only. Message describing status code/error when calling PO
    targetStatuses: List of target statuses.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Message describing fault status

    Values:
      STATE_UNSPECIFIED: Default state.
      APPLY_IN_PROGRESS: Apply fault is queued.
      APPLIED: Fault apply is completed.
      STOP_IN_PROGRESS: Stop apply in progress
      STOPPED: fault apply stopped
      ABORTED: Fault apply aborted.
      APPLY_ERRORED: Fault apply errored.
      STOP_ERRORED: Fault stop errored.
    """
    STATE_UNSPECIFIED = 0
    APPLY_IN_PROGRESS = 1
    APPLIED = 2
    STOP_IN_PROGRESS = 3
    STOPPED = 4
    ABORTED = 5
    APPLY_ERRORED = 6
    STOP_ERRORED = 7

  fault = _messages.MessageField('Fault', 1)
  faultTarget = _messages.MessageField('FaultInjectionTarget', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  status = _messages.MessageField('Status', 4)
  targetStatuses = _messages.MessageField('TargetStatus', 5, repeated=True)


class FaultInjectionAction(_messages.Message):
  r"""An Action identifies steps to perform in the fault.

  Fields:
    faultInjectionPolicy: FaultInectionPolicy action for fault.
  """

  faultInjectionPolicy = _messages.MessageField('FaultInjectionPolicy', 1)


class FaultInjectionPolicy(_messages.Message):
  r"""The specification for fault injection introduced into traffic to test
  the resiliency of clients to destination service failure. As part of fault
  injection, when clients send requests to a destination, delays can be
  introduced by client proxy on a percentage of requests before sending those
  requests to the destination service. Similarly requests can be aborted by
  client proxy for a percentage of requests.

  Fields:
    abort: Abort condtion
    delay: fixed delay time
  """

  abort = _messages.MessageField('Abort', 1)
  delay = _messages.MessageField('Delay', 2)


class FaultInjectionTarget(_messages.Message):
  r"""Message describing apphub targets passed to Job.

  Fields:
    uri: Uri of the apphub target.
  """

  uri = _messages.StringField(1)


class FaultinjectiontestingProjectsLocationsExperimentsCreateRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsExperimentsCreateRequest object.

  Fields:
    experiment: A Experiment resource to be passed as the request body.
    experimentId: Optional. Name of the experiment
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  experiment = _messages.MessageField('Experiment', 1)
  experimentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class FaultinjectiontestingProjectsLocationsExperimentsDeleteRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsExperimentsDeleteRequest object.

  Fields:
    etag: Optional. Etag value for the experiment to be deleted
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class FaultinjectiontestingProjectsLocationsExperimentsGetRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsExperimentsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class FaultinjectiontestingProjectsLocationsExperimentsListRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsExperimentsListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListExperimentsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class FaultinjectiontestingProjectsLocationsExperimentsPatchRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsExperimentsPatchRequest object.

  Fields:
    experiment: A Experiment resource to be passed as the request body.
    name: Required. Identifier. The format for the name is:
      projects/{project}/locations/{location}/experiments/{experiment_name}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Experiment resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  experiment = _messages.MessageField('Experiment', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class FaultinjectiontestingProjectsLocationsFaultsCreateRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsFaultsCreateRequest object.

  Fields:
    fault: A Fault resource to be passed as the request body.
    faultId: Required. fault_id given by user(fault id is nothing but name of
      the fault).
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  fault = _messages.MessageField('Fault', 1)
  faultId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class FaultinjectiontestingProjectsLocationsFaultsDeleteRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsFaultsDeleteRequest object.

  Fields:
    etag: Optional. Etag value for the fault to be deleted
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class FaultinjectiontestingProjectsLocationsFaultsGetRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsFaultsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class FaultinjectiontestingProjectsLocationsFaultsListRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsFaultsListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListFaultsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class FaultinjectiontestingProjectsLocationsFaultsPatchRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsFaultsPatchRequest object.

  Fields:
    fault: A Fault resource to be passed as the request body.
    name: Identifier. Unique name for the fault per project, provided by the
      end user. The format for the name is:
      projects/{project}/locations/{location}/faults/{fault_name}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Fault resource by the update. The fields specified in
      the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  fault = _messages.MessageField('Fault', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class FaultinjectiontestingProjectsLocationsGetRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class FaultinjectiontestingProjectsLocationsJobsCreateRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsJobsCreateRequest object.

  Fields:
    job: A Job resource to be passed as the request body.
    jobId: Required. Job_id given by user(name of the job).
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. When set to true, we will validate the job and
      return users the PEP information.
  """

  job = _messages.MessageField('Job', 1)
  jobId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class FaultinjectiontestingProjectsLocationsJobsDeleteRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsJobsDeleteRequest object.

  Fields:
    etag: Optional. Etag value for the job to be deleted
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class FaultinjectiontestingProjectsLocationsJobsGetRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsJobsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class FaultinjectiontestingProjectsLocationsJobsListRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsJobsListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListJobsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class FaultinjectiontestingProjectsLocationsJobsStopRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsJobsStopRequest object.

  Fields:
    name: Required. Name of the resource
    stopJobRequest: A StopJobRequest resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  stopJobRequest = _messages.MessageField('StopJobRequest', 2)


class FaultinjectiontestingProjectsLocationsListRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class FaultinjectiontestingProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class FaultinjectiontestingProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class FaultinjectiontestingProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class FaultinjectiontestingProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A FaultinjectiontestingProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ForwardingRuleTarget(_messages.Message):
  r"""Definition of FIT Target which describes a specific forwarding rule.

  Fields:
    forwardingRule: Reference to the targeted ForwardingRule (URI) See more:
      https://cloud.google.com/compute/docs/reference/rest/v1/forwardingRules
  """

  forwardingRule = _messages.StringField(1)


class Job(_messages.Message):
  r"""Message describing Job object

  Enums:
    JobStateValueValuesEnum: Output only. The current state of the job.
      Default value is UNSPECIFIED

  Fields:
    createTime: Output only. [Output only] create time stamp
    deleteTime: Output only. [Output only] delete time stamp
    description: End user description of the job.
    etag: Server specified etag
    experiment: Required. Immutable. experiment that job will run.
    experimentSnapshot: Output only. Snapshot of the experiment configuration
      at the time of job creation.
    expireTime: Output only. [Output only] expire time stamp
    faultApplyStatuses: List of fault configurations and their apply statuses
      for the job.
    jobState: Output only. The current state of the job. Default value is
      UNSPECIFIED
    name: Identifier. Job name of the running job. The format for the name is:
      projects/{project}/locations/{location}/jobs/{job_name}
  """

  class JobStateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the job. Default value is
    UNSPECIFIED

    Values:
      STATE_UNSPECIFIED: Job state is STATE_UNSPECIFIED for validate job
      QUEUED: job is queued
      IN_PROGRESS: job is in progress
      COMPLETED: job is completed
      ERRORED: job is errored
      STOP_IN_PROGRESS: Job stop in progress
      STOPPED: Job stopped
    """
    STATE_UNSPECIFIED = 0
    QUEUED = 1
    IN_PROGRESS = 2
    COMPLETED = 3
    ERRORED = 4
    STOP_IN_PROGRESS = 5
    STOPPED = 6

  createTime = _messages.StringField(1)
  deleteTime = _messages.StringField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  experiment = _messages.StringField(5)
  experimentSnapshot = _messages.MessageField('Experiment', 6)
  expireTime = _messages.StringField(7)
  faultApplyStatuses = _messages.MessageField('FaultApplyStatus', 8, repeated=True)
  jobState = _messages.EnumField('JobStateValueValuesEnum', 9)
  name = _messages.StringField(10)


class ListExperimentsResponse(_messages.Message):
  r"""Message for response to listing Experiments

  Fields:
    experiments: The list of Experiment
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  experiments = _messages.MessageField('Experiment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListFaultsResponse(_messages.Message):
  r"""Message for response to listing Faults

  Fields:
    faults: The list of Fault
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  faults = _messages.MessageField('Fault', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListJobsResponse(_messages.Message):
  r"""Message for response to listing Jobs

  Fields:
    jobs: The list of Job
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  jobs = _messages.MessageField('Job', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class RunFault(_messages.Message):
  r"""message to store faults and its durations in experiment

  Fields:
    fault: Fault name to run.
  """

  fault = _messages.StringField(1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StopJobRequest(_messages.Message):
  r"""Message for deleting a Job

  Fields:
    etag: Optional. Etag value for the job to be deleted
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  etag = _messages.StringField(1)
  requestId = _messages.StringField(2)


class Target(_messages.Message):
  r"""Message describing target that apphub target was resolved to.

  Fields:
    forwardingRuleTarget: Forwarding rule target.
  """

  forwardingRuleTarget = _messages.MessageField('ForwardingRuleTarget', 1)


class TargetStatus(_messages.Message):
  r"""Message describing target status.

  Fields:
    status: Output only. Error message from the control plane.
    target: target that apphub target resolved to.
  """

  status = _messages.MessageField('Status', 1)
  target = _messages.MessageField('Target', 2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
