"""Generated client library for kmsinventory version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.kmsinventory.v1 import kmsinventory_v1_messages as messages


class KmsinventoryV1(base_api.BaseApiClient):
  """Generated client library for service kmsinventory version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://kmsinventory.googleapis.com/'
  MTLS_BASE_URL = 'https://kmsinventory.mtls.googleapis.com/'

  _PACKAGE = 'kmsinventory'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'KmsinventoryV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new kmsinventory handle."""
    url = url or self.BASE_URL
    super(KmsinventoryV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.organizations_protectedResources = self.OrganizationsProtectedResourcesService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_cryptoKeys = self.ProjectsCryptoKeysService(self)
    self.projects_locations_keyRings_cryptoKeys = self.ProjectsLocationsKeyRingsCryptoKeysService(self)
    self.projects_locations_keyRings = self.ProjectsLocationsKeyRingsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class OrganizationsProtectedResourcesService(base_api.BaseApiService):
    """Service class for the organizations_protectedResources resource."""

    _NAME = 'organizations_protectedResources'

    def __init__(self, client):
      super(KmsinventoryV1.OrganizationsProtectedResourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def Search(self, request, global_params=None):
      r"""Returns metadata about the resources protected by the given Cloud KMS CryptoKey in the given Cloud organization.

      Args:
        request: (KmsinventoryOrganizationsProtectedResourcesSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SearchProtectedResourcesResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/protectedResources:search',
        http_method='GET',
        method_id='kmsinventory.organizations.protectedResources.search',
        ordered_params=['scope'],
        path_params=['scope'],
        query_params=['cryptoKey', 'pageSize', 'pageToken', 'resourceTypes'],
        relative_path='v1/{+scope}/protectedResources:search',
        request_field='',
        request_type_name='KmsinventoryOrganizationsProtectedResourcesSearchRequest',
        response_type_name='SearchProtectedResourcesResponse',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(KmsinventoryV1.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsCryptoKeysService(base_api.BaseApiService):
    """Service class for the projects_cryptoKeys resource."""

    _NAME = 'projects_cryptoKeys'

    def __init__(self, client):
      super(KmsinventoryV1.ProjectsCryptoKeysService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Returns cryptographic keys managed by Cloud KMS in a given Cloud project. Note that this data is sourced from snapshots, meaning it may not completely reflect the actual state of key metadata at call time.

      Args:
        request: (KmsinventoryProjectsCryptoKeysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCryptoKeysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/cryptoKeys',
        http_method='GET',
        method_id='kmsinventory.projects.cryptoKeys.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/cryptoKeys',
        request_field='',
        request_type_name='KmsinventoryProjectsCryptoKeysListRequest',
        response_type_name='ListCryptoKeysResponse',
        supports_download=False,
    )

  class ProjectsLocationsKeyRingsCryptoKeysService(base_api.BaseApiService):
    """Service class for the projects_locations_keyRings_cryptoKeys resource."""

    _NAME = 'projects_locations_keyRings_cryptoKeys'

    def __init__(self, client):
      super(KmsinventoryV1.ProjectsLocationsKeyRingsCryptoKeysService, self).__init__(client)
      self._upload_configs = {
          }

    def GetProtectedResourcesSummary(self, request, global_params=None):
      r"""Returns aggregate information about the resources protected by the given Cloud KMS CryptoKey. Only resources within the same Cloud organization as the key will be returned. The project that holds the key must be part of an organization in order for this call to succeed.

      Args:
        request: (KmsinventoryProjectsLocationsKeyRingsCryptoKeysGetProtectedResourcesSummaryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ProtectedResourcesSummary) The response message.
      """
      config = self.GetMethodConfig('GetProtectedResourcesSummary')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetProtectedResourcesSummary.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/protectedResourcesSummary',
        http_method='GET',
        method_id='kmsinventory.projects.locations.keyRings.cryptoKeys.getProtectedResourcesSummary',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/protectedResourcesSummary',
        request_field='',
        request_type_name='KmsinventoryProjectsLocationsKeyRingsCryptoKeysGetProtectedResourcesSummaryRequest',
        response_type_name='ProtectedResourcesSummary',
        supports_download=False,
    )

  class ProjectsLocationsKeyRingsService(base_api.BaseApiService):
    """Service class for the projects_locations_keyRings resource."""

    _NAME = 'projects_locations_keyRings'

    def __init__(self, client):
      super(KmsinventoryV1.ProjectsLocationsKeyRingsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(KmsinventoryV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(KmsinventoryV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
