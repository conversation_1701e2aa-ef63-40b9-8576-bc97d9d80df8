"""Generated message classes for kmsinventory version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'kmsinventory'


class CertificateChains(_messages.Message):
  r"""Certificate chains needed to verify the attestation. Certificates in
  chains are PEM-encoded and are ordered based on
  https://tools.ietf.org/html/rfc5246#section-7.4.2.

  Fields:
    caviumCerts: Cavium certificate chain corresponding to the attestation.
    googleCardCerts: Google card certificate chain corresponding to the
      attestation.
    googlePartitionCerts: Google partition certificate chain corresponding to
      the attestation.
  """

  caviumCerts = _messages.StringField(1, repeated=True)
  googleCardCerts = _messages.StringField(2, repeated=True)
  googlePartitionCerts = _messages.StringField(3, repeated=True)


class CryptoKey(_messages.Message):
  r"""A CryptoKey represents a logical key that can be used for cryptographic
  operations. A CryptoKey is made up of zero or more versions, which represent
  the actual key material used in cryptographic operations.

  Enums:
    PurposeValueValuesEnum: Immutable. The immutable purpose of this
      CryptoKey.

  Messages:
    LabelsValue: Labels with user-defined metadata. For more information, see
      [Labeling Keys](https://cloud.google.com/kms/docs/labeling-keys).

  Fields:
    createTime: Output only. The time at which this CryptoKey was created.
    cryptoKeyBackend: Immutable. The resource name of the backend environment
      where the key material for all CryptoKeyVersions associated with this
      CryptoKey reside and where all related cryptographic operations are
      performed. Only applicable if CryptoKeyVersions have a ProtectionLevel
      of EXTERNAL_VPC, with the resource name in the format
      `projects/*/locations/*/ekmConnections/*`. Note, this list is non-
      exhaustive and may apply to additional ProtectionLevels in the future.
    destroyScheduledDuration: Immutable. The period of time that versions of
      this key spend in the DESTROY_SCHEDULED state before transitioning to
      DESTROYED. If not specified at creation time, the default duration is 24
      hours.
    importOnly: Immutable. Whether this key may contain imported versions
      only.
    labels: Labels with user-defined metadata. For more information, see
      [Labeling Keys](https://cloud.google.com/kms/docs/labeling-keys).
    name: Output only. The resource name for this CryptoKey in the format
      `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
    nextRotationTime: At next_rotation_time, the Key Management Service will
      automatically: 1. Create a new version of this CryptoKey. 2. Mark the
      new version as primary. Key rotations performed manually via
      CreateCryptoKeyVersion and UpdateCryptoKeyPrimaryVersion do not affect
      next_rotation_time. Keys with purpose ENCRYPT_DECRYPT support automatic
      rotation. For other keys, this field must be omitted.
    primary: Output only. A copy of the "primary" CryptoKeyVersion that will
      be used by Encrypt when this CryptoKey is given in EncryptRequest.name.
      The CryptoKey's primary version can be updated via
      UpdateCryptoKeyPrimaryVersion. Keys with purpose ENCRYPT_DECRYPT may
      have a primary. For other keys, this field will be omitted.
    purpose: Immutable. The immutable purpose of this CryptoKey.
    rotationPeriod: next_rotation_time will be advanced by this period when
      the service automatically rotates a key. Must be at least 24 hours and
      at most 876,000 hours. If rotation_period is set, next_rotation_time
      must also be set. Keys with purpose ENCRYPT_DECRYPT support automatic
      rotation. For other keys, this field must be omitted.
    versionTemplate: A template describing settings for new CryptoKeyVersion
      instances. The properties of new CryptoKeyVersion instances created by
      either CreateCryptoKeyVersion or auto-rotation are controlled by this
      template.
  """

  class PurposeValueValuesEnum(_messages.Enum):
    r"""Immutable. The immutable purpose of this CryptoKey.

    Values:
      CRYPTO_KEY_PURPOSE_UNSPECIFIED: Not specified.
      ENCRYPT_DECRYPT: CryptoKeys with this purpose may be used with Encrypt
        and Decrypt.
      ASYMMETRIC_SIGN: CryptoKeys with this purpose may be used with
        AsymmetricSign and GetPublicKey.
      ASYMMETRIC_DECRYPT: CryptoKeys with this purpose may be used with
        AsymmetricDecrypt and GetPublicKey.
      RAW_ENCRYPT_DECRYPT: CryptoKeys with this purpose may be used with
        RawEncrypt and RawDecrypt. This purpose is meant to be used for
        interoperable symmetric encryption and does not support automatic
        CryptoKey rotation.
      MAC: CryptoKeys with this purpose may be used with MacSign.
    """
    CRYPTO_KEY_PURPOSE_UNSPECIFIED = 0
    ENCRYPT_DECRYPT = 1
    ASYMMETRIC_SIGN = 2
    ASYMMETRIC_DECRYPT = 3
    RAW_ENCRYPT_DECRYPT = 4
    MAC = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels with user-defined metadata. For more information, see [Labeling
    Keys](https://cloud.google.com/kms/docs/labeling-keys).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  cryptoKeyBackend = _messages.StringField(2)
  destroyScheduledDuration = _messages.StringField(3)
  importOnly = _messages.BooleanField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  nextRotationTime = _messages.StringField(7)
  primary = _messages.MessageField('CryptoKeyVersion', 8)
  purpose = _messages.EnumField('PurposeValueValuesEnum', 9)
  rotationPeriod = _messages.StringField(10)
  versionTemplate = _messages.MessageField('CryptoKeyVersionTemplate', 11)


class CryptoKeyVersion(_messages.Message):
  r"""A CryptoKeyVersion represents an individual cryptographic key, and the
  associated key material. An ENABLED version can be used for cryptographic
  operations. For security reasons, the raw cryptographic key material
  represented by a CryptoKeyVersion can never be viewed or exported. It can
  only be used to encrypt, decrypt, or sign data when an authorized user or
  application invokes Cloud KMS.

  Enums:
    AlgorithmValueValuesEnum: Output only. The CryptoKeyVersionAlgorithm that
      this CryptoKeyVersion supports.
    ProtectionLevelValueValuesEnum: Output only. The ProtectionLevel
      describing how crypto operations are performed with this
      CryptoKeyVersion.
    StateValueValuesEnum: The current state of the CryptoKeyVersion.

  Fields:
    algorithm: Output only. The CryptoKeyVersionAlgorithm that this
      CryptoKeyVersion supports.
    attestation: Output only. Statement that was generated and signed by the
      HSM at key creation time. Use this statement to verify attributes of the
      key as stored on the HSM, independently of Google. Only provided for key
      versions with protection_level HSM.
    createTime: Output only. The time at which this CryptoKeyVersion was
      created.
    destroyEventTime: Output only. The time this CryptoKeyVersion's key
      material was destroyed. Only present if state is DESTROYED.
    destroyTime: Output only. The time this CryptoKeyVersion's key material is
      scheduled for destruction. Only present if state is DESTROY_SCHEDULED.
    externalDestructionFailureReason: Output only. The root cause of the most
      recent external destruction failure. Only present if state is
      EXTERNAL_DESTRUCTION_FAILED.
    externalProtectionLevelOptions: ExternalProtectionLevelOptions stores a
      group of additional fields for configuring a CryptoKeyVersion that are
      specific to the EXTERNAL protection level and EXTERNAL_VPC protection
      levels.
    generateTime: Output only. The time this CryptoKeyVersion's key material
      was generated.
    generationFailureReason: Output only. The root cause of the most recent
      generation failure. Only present if state is GENERATION_FAILED.
    importFailureReason: Output only. The root cause of the most recent import
      failure. Only present if state is IMPORT_FAILED.
    importJob: Output only. The name of the ImportJob used in the most recent
      import of this CryptoKeyVersion. Only present if the underlying key
      material was imported.
    importTime: Output only. The time at which this CryptoKeyVersion's key
      material was most recently imported.
    name: Output only. The resource name for this CryptoKeyVersion in the
      format
      `projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*`.
    protectionLevel: Output only. The ProtectionLevel describing how crypto
      operations are performed with this CryptoKeyVersion.
    reimportEligible: Output only. Whether or not this key version is eligible
      for reimport, by being specified as a target in
      ImportCryptoKeyVersionRequest.crypto_key_version.
    state: The current state of the CryptoKeyVersion.
  """

  class AlgorithmValueValuesEnum(_messages.Enum):
    r"""Output only. The CryptoKeyVersionAlgorithm that this CryptoKeyVersion
    supports.

    Values:
      CRYPTO_KEY_VERSION_ALGORITHM_UNSPECIFIED: Not specified.
      GOOGLE_SYMMETRIC_ENCRYPTION: Creates symmetric encryption keys.
      AES_128_GCM: AES-GCM (Galois Counter Mode) using 128-bit keys.
      AES_256_GCM: AES-GCM (Galois Counter Mode) using 256-bit keys.
      AES_128_CBC: AES-CBC (Cipher Block Chaining Mode) using 128-bit keys.
      AES_256_CBC: AES-CBC (Cipher Block Chaining Mode) using 256-bit keys.
      AES_128_CTR: AES-CTR (Counter Mode) using 128-bit keys.
      AES_256_CTR: AES-CTR (Counter Mode) using 256-bit keys.
      RSA_SIGN_PSS_2048_SHA256: RSASSA-PSS 2048 bit key with a SHA256 digest.
      RSA_SIGN_PSS_3072_SHA256: RSASSA-PSS 3072 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA256: RSASSA-PSS 4096 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA512: RSASSA-PSS 4096 bit key with a SHA512 digest.
      RSA_SIGN_PKCS1_2048_SHA256: RSASSA-PKCS1-v1_5 with a 2048 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_3072_SHA256: RSASSA-PKCS1-v1_5 with a 3072 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA256: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA512: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA512 digest.
      RSA_SIGN_RAW_PKCS1_2048: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 2048 bit key.
      RSA_SIGN_RAW_PKCS1_3072: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 3072 bit key.
      RSA_SIGN_RAW_PKCS1_4096: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 4096 bit key.
      RSA_DECRYPT_OAEP_2048_SHA256: RSAES-OAEP 2048 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_3072_SHA256: RSAES-OAEP 3072 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_4096_SHA256: RSAES-OAEP 4096 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_4096_SHA512: RSAES-OAEP 4096 bit key with a SHA512
        digest.
      RSA_DECRYPT_OAEP_2048_SHA1: RSAES-OAEP 2048 bit key with a SHA1 digest.
      RSA_DECRYPT_OAEP_3072_SHA1: RSAES-OAEP 3072 bit key with a SHA1 digest.
      RSA_DECRYPT_OAEP_4096_SHA1: RSAES-OAEP 4096 bit key with a SHA1 digest.
      EC_SIGN_P256_SHA256: ECDSA on the NIST P-256 curve with a SHA256 digest.
        Other hash functions can also be used:
        https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_P384_SHA384: ECDSA on the NIST P-384 curve with a SHA384 digest.
        Other hash functions can also be used:
        https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_SECP256K1_SHA256: ECDSA on the non-NIST secp256k1 curve. This
        curve is only supported for HSM protection level. Other hash functions
        can also be used: https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      HMAC_SHA256: HMAC-SHA256 signing with a 256 bit key.
      HMAC_SHA1: HMAC-SHA1 signing with a 160 bit key.
      HMAC_SHA384: HMAC-SHA384 signing with a 384 bit key.
      HMAC_SHA512: HMAC-SHA512 signing with a 512 bit key.
      HMAC_SHA224: HMAC-SHA224 signing with a 224 bit key.
      EXTERNAL_SYMMETRIC_ENCRYPTION: Algorithm representing symmetric
        encryption by an external key manager.
    """
    CRYPTO_KEY_VERSION_ALGORITHM_UNSPECIFIED = 0
    GOOGLE_SYMMETRIC_ENCRYPTION = 1
    AES_128_GCM = 2
    AES_256_GCM = 3
    AES_128_CBC = 4
    AES_256_CBC = 5
    AES_128_CTR = 6
    AES_256_CTR = 7
    RSA_SIGN_PSS_2048_SHA256 = 8
    RSA_SIGN_PSS_3072_SHA256 = 9
    RSA_SIGN_PSS_4096_SHA256 = 10
    RSA_SIGN_PSS_4096_SHA512 = 11
    RSA_SIGN_PKCS1_2048_SHA256 = 12
    RSA_SIGN_PKCS1_3072_SHA256 = 13
    RSA_SIGN_PKCS1_4096_SHA256 = 14
    RSA_SIGN_PKCS1_4096_SHA512 = 15
    RSA_SIGN_RAW_PKCS1_2048 = 16
    RSA_SIGN_RAW_PKCS1_3072 = 17
    RSA_SIGN_RAW_PKCS1_4096 = 18
    RSA_DECRYPT_OAEP_2048_SHA256 = 19
    RSA_DECRYPT_OAEP_3072_SHA256 = 20
    RSA_DECRYPT_OAEP_4096_SHA256 = 21
    RSA_DECRYPT_OAEP_4096_SHA512 = 22
    RSA_DECRYPT_OAEP_2048_SHA1 = 23
    RSA_DECRYPT_OAEP_3072_SHA1 = 24
    RSA_DECRYPT_OAEP_4096_SHA1 = 25
    EC_SIGN_P256_SHA256 = 26
    EC_SIGN_P384_SHA384 = 27
    EC_SIGN_SECP256K1_SHA256 = 28
    HMAC_SHA256 = 29
    HMAC_SHA1 = 30
    HMAC_SHA384 = 31
    HMAC_SHA512 = 32
    HMAC_SHA224 = 33
    EXTERNAL_SYMMETRIC_ENCRYPTION = 34

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""Output only. The ProtectionLevel describing how crypto operations are
    performed with this CryptoKeyVersion.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the CryptoKeyVersion.

    Values:
      CRYPTO_KEY_VERSION_STATE_UNSPECIFIED: Not specified.
      PENDING_GENERATION: This version is still being generated. It may not be
        used, enabled, disabled, or destroyed yet. Cloud KMS will
        automatically mark this version ENABLED as soon as the version is
        ready.
      ENABLED: This version may be used for cryptographic operations.
      DISABLED: This version may not be used, but the key material is still
        available, and the version can be placed back into the ENABLED state.
      DESTROYED: This version is destroyed, and the key material is no longer
        stored. This version may only become ENABLED again if this version is
        reimport_eligible and the original key material is reimported with a
        call to KeyManagementService.ImportCryptoKeyVersion.
      DESTROY_SCHEDULED: This version is scheduled for destruction, and will
        be destroyed soon. Call RestoreCryptoKeyVersion to put it back into
        the DISABLED state.
      PENDING_IMPORT: This version is still being imported. It may not be
        used, enabled, disabled, or destroyed yet. Cloud KMS will
        automatically mark this version ENABLED as soon as the version is
        ready.
      IMPORT_FAILED: This version was not imported successfully. It may not be
        used, enabled, disabled, or destroyed. The submitted key material has
        been discarded. Additional details can be found in
        CryptoKeyVersion.import_failure_reason.
      GENERATION_FAILED: This version was not generated successfully. It may
        not be used, enabled, disabled, or destroyed. Additional details can
        be found in CryptoKeyVersion.generation_failure_reason.
      PENDING_EXTERNAL_DESTRUCTION: This version was destroyed, and it may not
        be used or enabled again. Cloud KMS is waiting for the corresponding
        key material residing in an external key manager to be destroyed.
      EXTERNAL_DESTRUCTION_FAILED: This version was destroyed, and it may not
        be used or enabled again. However, Cloud KMS could not confirm that
        the corresponding key material residing in an external key manager was
        destroyed. Additional details can be found in
        CryptoKeyVersion.external_destruction_failure_reason.
    """
    CRYPTO_KEY_VERSION_STATE_UNSPECIFIED = 0
    PENDING_GENERATION = 1
    ENABLED = 2
    DISABLED = 3
    DESTROYED = 4
    DESTROY_SCHEDULED = 5
    PENDING_IMPORT = 6
    IMPORT_FAILED = 7
    GENERATION_FAILED = 8
    PENDING_EXTERNAL_DESTRUCTION = 9
    EXTERNAL_DESTRUCTION_FAILED = 10

  algorithm = _messages.EnumField('AlgorithmValueValuesEnum', 1)
  attestation = _messages.MessageField('KeyOperationAttestation', 2)
  createTime = _messages.StringField(3)
  destroyEventTime = _messages.StringField(4)
  destroyTime = _messages.StringField(5)
  externalDestructionFailureReason = _messages.StringField(6)
  externalProtectionLevelOptions = _messages.MessageField('ExternalProtectionLevelOptions', 7)
  generateTime = _messages.StringField(8)
  generationFailureReason = _messages.StringField(9)
  importFailureReason = _messages.StringField(10)
  importJob = _messages.StringField(11)
  importTime = _messages.StringField(12)
  name = _messages.StringField(13)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 14)
  reimportEligible = _messages.BooleanField(15)
  state = _messages.EnumField('StateValueValuesEnum', 16)


class CryptoKeyVersionTemplate(_messages.Message):
  r"""A CryptoKeyVersionTemplate specifies the properties to use when creating
  a new CryptoKeyVersion, either manually with CreateCryptoKeyVersion or
  automatically as a result of auto-rotation.

  Enums:
    AlgorithmValueValuesEnum: Required. Algorithm to use when creating a
      CryptoKeyVersion based on this template. For backwards compatibility,
      GOOGLE_SYMMETRIC_ENCRYPTION is implied if both this field is omitted and
      CryptoKey.purpose is ENCRYPT_DECRYPT.
    ProtectionLevelValueValuesEnum: ProtectionLevel to use when creating a
      CryptoKeyVersion based on this template. Immutable. Defaults to
      SOFTWARE.

  Fields:
    algorithm: Required. Algorithm to use when creating a CryptoKeyVersion
      based on this template. For backwards compatibility,
      GOOGLE_SYMMETRIC_ENCRYPTION is implied if both this field is omitted and
      CryptoKey.purpose is ENCRYPT_DECRYPT.
    protectionLevel: ProtectionLevel to use when creating a CryptoKeyVersion
      based on this template. Immutable. Defaults to SOFTWARE.
  """

  class AlgorithmValueValuesEnum(_messages.Enum):
    r"""Required. Algorithm to use when creating a CryptoKeyVersion based on
    this template. For backwards compatibility, GOOGLE_SYMMETRIC_ENCRYPTION is
    implied if both this field is omitted and CryptoKey.purpose is
    ENCRYPT_DECRYPT.

    Values:
      CRYPTO_KEY_VERSION_ALGORITHM_UNSPECIFIED: Not specified.
      GOOGLE_SYMMETRIC_ENCRYPTION: Creates symmetric encryption keys.
      AES_128_GCM: AES-GCM (Galois Counter Mode) using 128-bit keys.
      AES_256_GCM: AES-GCM (Galois Counter Mode) using 256-bit keys.
      AES_128_CBC: AES-CBC (Cipher Block Chaining Mode) using 128-bit keys.
      AES_256_CBC: AES-CBC (Cipher Block Chaining Mode) using 256-bit keys.
      AES_128_CTR: AES-CTR (Counter Mode) using 128-bit keys.
      AES_256_CTR: AES-CTR (Counter Mode) using 256-bit keys.
      RSA_SIGN_PSS_2048_SHA256: RSASSA-PSS 2048 bit key with a SHA256 digest.
      RSA_SIGN_PSS_3072_SHA256: RSASSA-PSS 3072 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA256: RSASSA-PSS 4096 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA512: RSASSA-PSS 4096 bit key with a SHA512 digest.
      RSA_SIGN_PKCS1_2048_SHA256: RSASSA-PKCS1-v1_5 with a 2048 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_3072_SHA256: RSASSA-PKCS1-v1_5 with a 3072 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA256: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA512: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA512 digest.
      RSA_SIGN_RAW_PKCS1_2048: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 2048 bit key.
      RSA_SIGN_RAW_PKCS1_3072: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 3072 bit key.
      RSA_SIGN_RAW_PKCS1_4096: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 4096 bit key.
      RSA_DECRYPT_OAEP_2048_SHA256: RSAES-OAEP 2048 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_3072_SHA256: RSAES-OAEP 3072 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_4096_SHA256: RSAES-OAEP 4096 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_4096_SHA512: RSAES-OAEP 4096 bit key with a SHA512
        digest.
      RSA_DECRYPT_OAEP_2048_SHA1: RSAES-OAEP 2048 bit key with a SHA1 digest.
      RSA_DECRYPT_OAEP_3072_SHA1: RSAES-OAEP 3072 bit key with a SHA1 digest.
      RSA_DECRYPT_OAEP_4096_SHA1: RSAES-OAEP 4096 bit key with a SHA1 digest.
      EC_SIGN_P256_SHA256: ECDSA on the NIST P-256 curve with a SHA256 digest.
        Other hash functions can also be used:
        https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_P384_SHA384: ECDSA on the NIST P-384 curve with a SHA384 digest.
        Other hash functions can also be used:
        https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_SECP256K1_SHA256: ECDSA on the non-NIST secp256k1 curve. This
        curve is only supported for HSM protection level. Other hash functions
        can also be used: https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      HMAC_SHA256: HMAC-SHA256 signing with a 256 bit key.
      HMAC_SHA1: HMAC-SHA1 signing with a 160 bit key.
      HMAC_SHA384: HMAC-SHA384 signing with a 384 bit key.
      HMAC_SHA512: HMAC-SHA512 signing with a 512 bit key.
      HMAC_SHA224: HMAC-SHA224 signing with a 224 bit key.
      EXTERNAL_SYMMETRIC_ENCRYPTION: Algorithm representing symmetric
        encryption by an external key manager.
    """
    CRYPTO_KEY_VERSION_ALGORITHM_UNSPECIFIED = 0
    GOOGLE_SYMMETRIC_ENCRYPTION = 1
    AES_128_GCM = 2
    AES_256_GCM = 3
    AES_128_CBC = 4
    AES_256_CBC = 5
    AES_128_CTR = 6
    AES_256_CTR = 7
    RSA_SIGN_PSS_2048_SHA256 = 8
    RSA_SIGN_PSS_3072_SHA256 = 9
    RSA_SIGN_PSS_4096_SHA256 = 10
    RSA_SIGN_PSS_4096_SHA512 = 11
    RSA_SIGN_PKCS1_2048_SHA256 = 12
    RSA_SIGN_PKCS1_3072_SHA256 = 13
    RSA_SIGN_PKCS1_4096_SHA256 = 14
    RSA_SIGN_PKCS1_4096_SHA512 = 15
    RSA_SIGN_RAW_PKCS1_2048 = 16
    RSA_SIGN_RAW_PKCS1_3072 = 17
    RSA_SIGN_RAW_PKCS1_4096 = 18
    RSA_DECRYPT_OAEP_2048_SHA256 = 19
    RSA_DECRYPT_OAEP_3072_SHA256 = 20
    RSA_DECRYPT_OAEP_4096_SHA256 = 21
    RSA_DECRYPT_OAEP_4096_SHA512 = 22
    RSA_DECRYPT_OAEP_2048_SHA1 = 23
    RSA_DECRYPT_OAEP_3072_SHA1 = 24
    RSA_DECRYPT_OAEP_4096_SHA1 = 25
    EC_SIGN_P256_SHA256 = 26
    EC_SIGN_P384_SHA384 = 27
    EC_SIGN_SECP256K1_SHA256 = 28
    HMAC_SHA256 = 29
    HMAC_SHA1 = 30
    HMAC_SHA384 = 31
    HMAC_SHA512 = 32
    HMAC_SHA224 = 33
    EXTERNAL_SYMMETRIC_ENCRYPTION = 34

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""ProtectionLevel to use when creating a CryptoKeyVersion based on this
    template. Immutable. Defaults to SOFTWARE.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  algorithm = _messages.EnumField('AlgorithmValueValuesEnum', 1)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 2)


class ExternalProtectionLevelOptions(_messages.Message):
  r"""ExternalProtectionLevelOptions stores a group of additional fields for
  configuring a CryptoKeyVersion that are specific to the EXTERNAL protection
  level and EXTERNAL_VPC protection levels.

  Fields:
    ekmConnectionKeyPath: The path to the external key material on the EKM
      when using EkmConnection e.g., "v0/my/key". Set this field instead of
      external_key_uri when using an EkmConnection.
    externalKeyUri: The URI for an external resource that this
      CryptoKeyVersion represents.
  """

  ekmConnectionKeyPath = _messages.StringField(1)
  externalKeyUri = _messages.StringField(2)


class KeyOperationAttestation(_messages.Message):
  r"""Contains an HSM-generated attestation about a key operation. For more
  information, see [Verifying attestations]
  (https://cloud.google.com/kms/docs/attest-key).

  Enums:
    FormatValueValuesEnum: Output only. The format of the attestation data.

  Fields:
    certChains: Output only. The certificate chains needed to validate the
      attestation
    content: Output only. The attestation data provided by the HSM when the
      key operation was performed.
    format: Output only. The format of the attestation data.
  """

  class FormatValueValuesEnum(_messages.Enum):
    r"""Output only. The format of the attestation data.

    Values:
      ATTESTATION_FORMAT_UNSPECIFIED: Not specified.
      CAVIUM_V1_COMPRESSED: Cavium HSM attestation compressed with gzip. Note
        that this format is defined by Cavium and subject to change at any
        time. See https://www.marvell.com/products/security-solutions/nitrox-
        hs-adapters/software-key-attestation.html.
      CAVIUM_V2_COMPRESSED: Cavium HSM attestation V2 compressed with gzip.
        This is a new format introduced in Cavium's version 3.2-08.
    """
    ATTESTATION_FORMAT_UNSPECIFIED = 0
    CAVIUM_V1_COMPRESSED = 1
    CAVIUM_V2_COMPRESSED = 2

  certChains = _messages.MessageField('CertificateChains', 1)
  content = _messages.BytesField(2)
  format = _messages.EnumField('FormatValueValuesEnum', 3)


class KmsinventoryOrganizationsProtectedResourcesSearchRequest(_messages.Message):
  r"""A KmsinventoryOrganizationsProtectedResourcesSearchRequest object.

  Fields:
    cryptoKey: Required. The resource name of the CryptoKey.
    pageSize: The maximum number of resources to return. The service may
      return fewer than this value. If unspecified, at most 500 resources will
      be returned. The maximum value is 500; values above 500 will be coerced
      to 500.
    pageToken: A page token, received from a previous
      KeyTrackingService.SearchProtectedResources call. Provide this to
      retrieve the subsequent page. When paginating, all other parameters
      provided to KeyTrackingService.SearchProtectedResources must match the
      call that provided the page token.
    resourceTypes: Optional. A list of resource types that this request
      searches for. If empty, it will search all the [trackable resource
      types](https://cloud.google.com/kms/docs/view-key-usage#tracked-
      resource-types). Regular expressions are also supported. For example: *
      `compute.googleapis.com.*` snapshots resources whose type starts with
      `compute.googleapis.com`. * `.*Image` snapshots resources whose type
      ends with `Image`. * `.*Image.*` snapshots resources whose type contains
      `Image`. See [RE2](https://github.com/google/re2/wiki/Syntax) for all
      supported regular expression syntax. If the regular expression does not
      match any supported resource type, an INVALID_ARGUMENT error will be
      returned.
    scope: Required. Resource name of the organization. Example:
      organizations/123
  """

  cryptoKey = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  resourceTypes = _messages.StringField(4, repeated=True)
  scope = _messages.StringField(5, required=True)


class KmsinventoryProjectsCryptoKeysListRequest(_messages.Message):
  r"""A KmsinventoryProjectsCryptoKeysListRequest object.

  Fields:
    pageSize: Optional. The maximum number of keys to return. The service may
      return fewer than this value. If unspecified, at most 1000 keys will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. Pass this into a subsequent request in order to
      receive the next page of results.
    parent: Required. The Google Cloud project for which to retrieve key
      metadata, in the format `projects/*`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class KmsinventoryProjectsLocationsKeyRingsCryptoKeysGetProtectedResourcesSummaryRequest(_messages.Message):
  r"""A KmsinventoryProjectsLocationsKeyRingsCryptoKeysGetProtectedResourcesSu
  mmaryRequest object.

  Fields:
    name: Required. The resource name of the CryptoKey.
  """

  name = _messages.StringField(1, required=True)


class ListCryptoKeysResponse(_messages.Message):
  r"""Response message for KeyDashboardService.ListCryptoKeys.

  Fields:
    cryptoKeys: The list of CryptoKeys.
    nextPageToken: The page token returned from the previous response if the
      next page is desired.
  """

  cryptoKeys = _messages.MessageField('CryptoKey', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ProtectedResource(_messages.Message):
  r"""Metadata about a resource protected by a Cloud KMS key.

  Messages:
    LabelsValue: A key-value pair of the resource's labels (v1) to their
      values.

  Fields:
    cloudProduct: The Cloud product that owns the resource. Example: `compute`
    createTime: Output only. The time at which this resource was created. The
      granularity is in seconds. Timestamp.nanos will always be 0.
    cryptoKeyVersion: The name of the Cloud KMS [CryptoKeyVersion](https://clo
      ud.google.com/kms/docs/reference/rest/v1/projects.locations.keyRings.cry
      ptoKeys.cryptoKeyVersions?hl=en) used to protect this resource via CMEK.
      This field is empty if the Google Cloud product owning the resource does
      not provide key version data to Asset Inventory. If there are multiple
      key versions protecting the resource, then this is same value as the
      first element of crypto_key_versions.
    cryptoKeyVersions: The names of the Cloud KMS [CryptoKeyVersion](https://c
      loud.google.com/kms/docs/reference/rest/v1/projects.locations.keyRings.c
      ryptoKeys.cryptoKeyVersions?hl=en) used to protect this resource via
      CMEK. This field is empty if the Google Cloud product owning the
      resource does not provide key versions data to Asset Inventory. The
      first element of this field is stored in crypto_key_version.
    labels: A key-value pair of the resource's labels (v1) to their values.
    location: Location can be `global`, regional like `us-east1`, or zonal
      like `us-west1-b`.
    name: The full resource name of the resource. Example: `//compute.googleap
      is.com/projects/my_project_123/zones/zone1/instances/instance1`.
    project: Format: `projects/{PROJECT_NUMBER}`.
    projectId: The ID of the project that owns the resource.
    resourceType: Example: `compute.googleapis.com/Disk`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""A key-value pair of the resource's labels (v1) to their values.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cloudProduct = _messages.StringField(1)
  createTime = _messages.StringField(2)
  cryptoKeyVersion = _messages.StringField(3)
  cryptoKeyVersions = _messages.StringField(4, repeated=True)
  labels = _messages.MessageField('LabelsValue', 5)
  location = _messages.StringField(6)
  name = _messages.StringField(7)
  project = _messages.StringField(8)
  projectId = _messages.StringField(9)
  resourceType = _messages.StringField(10)


class ProtectedResourcesSummary(_messages.Message):
  r"""Aggregate information about the resources protected by a Cloud KMS key
  in the same Cloud organization as the key.

  Messages:
    CloudProductsValue: The number of resources protected by the key grouped
      by Cloud product.
    LocationsValue: The number of resources protected by the key grouped by
      region.
    ResourceTypesValue: The number of resources protected by the key grouped
      by resource type.

  Fields:
    cloudProducts: The number of resources protected by the key grouped by
      Cloud product.
    locations: The number of resources protected by the key grouped by region.
    name: The full name of the ProtectedResourcesSummary resource. Example:
      projects/test-project/locations/us/keyRings/test-
      keyring/cryptoKeys/test-key/protectedResourcesSummary
    projectCount: The number of distinct Cloud projects in the same Cloud
      organization as the key that have resources protected by the key.
    resourceCount: The total number of protected resources in the same Cloud
      organization as the key.
    resourceTypes: The number of resources protected by the key grouped by
      resource type.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CloudProductsValue(_messages.Message):
    r"""The number of resources protected by the key grouped by Cloud product.

    Messages:
      AdditionalProperty: An additional property for a CloudProductsValue
        object.

    Fields:
      additionalProperties: Additional properties of type CloudProductsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CloudProductsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LocationsValue(_messages.Message):
    r"""The number of resources protected by the key grouped by region.

    Messages:
      AdditionalProperty: An additional property for a LocationsValue object.

    Fields:
      additionalProperties: Additional properties of type LocationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LocationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceTypesValue(_messages.Message):
    r"""The number of resources protected by the key grouped by resource type.

    Messages:
      AdditionalProperty: An additional property for a ResourceTypesValue
        object.

    Fields:
      additionalProperties: Additional properties of type ResourceTypesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceTypesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cloudProducts = _messages.MessageField('CloudProductsValue', 1)
  locations = _messages.MessageField('LocationsValue', 2)
  name = _messages.StringField(3)
  projectCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  resourceCount = _messages.IntegerField(5)
  resourceTypes = _messages.MessageField('ResourceTypesValue', 6)


class SearchProtectedResourcesResponse(_messages.Message):
  r"""Response message for KeyTrackingService.SearchProtectedResources.

  Fields:
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    protectedResources: Protected resources for this page.
  """

  nextPageToken = _messages.StringField(1)
  protectedResources = _messages.MessageField('ProtectedResource', 2, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
