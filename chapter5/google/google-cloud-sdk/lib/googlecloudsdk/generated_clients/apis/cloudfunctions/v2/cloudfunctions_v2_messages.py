"""Generated message classes for cloudfunctions version v2.

Manages lightweight user-provided functions executed in response to events.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'cloudfunctions'


class AbortFunctionUpgradeRequest(_messages.Message):
  r"""Request for the `AbortFunctionUpgrade` method."""


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class AutomaticUpdatePolicy(_messages.Message):
  r"""Security patches are applied automatically to the runtime without
  requiring the function to be redeployed.
  """



class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BuildConfig(_messages.Message):
  r"""Describes the Build step of the function that builds a container from
  the given source.

  Enums:
    DockerRegistryValueValuesEnum: Docker Registry to use for this deployment.
      This configuration is only applicable to 1st Gen functions, 2nd Gen
      functions can only use Artifact Registry. Deprecated: as of March 2025,
      `CONTAINER_REGISTRY` option is no longer available in response to
      Container Registry's deprecation: https://cloud.google.com/artifact-
      registry/docs/transition/transition-from-gcr Please use Artifact
      Registry instead, which is the default choice. If unspecified, it
      defaults to `ARTIFACT_REGISTRY`. If `docker_repository` field is
      specified, this field should either be left unspecified or set to
      `ARTIFACT_REGISTRY`.

  Messages:
    EnvironmentVariablesValue: User-provided build-time environment variables
      for the function

  Fields:
    automaticUpdatePolicy: A AutomaticUpdatePolicy attribute.
    build: Output only. The Cloud Build name of the latest successful
      deployment of the function.
    dockerRegistry: Docker Registry to use for this deployment. This
      configuration is only applicable to 1st Gen functions, 2nd Gen functions
      can only use Artifact Registry. Deprecated: as of March 2025,
      `CONTAINER_REGISTRY` option is no longer available in response to
      Container Registry's deprecation: https://cloud.google.com/artifact-
      registry/docs/transition/transition-from-gcr Please use Artifact
      Registry instead, which is the default choice. If unspecified, it
      defaults to `ARTIFACT_REGISTRY`. If `docker_repository` field is
      specified, this field should either be left unspecified or set to
      `ARTIFACT_REGISTRY`.
    dockerRepository: Repository in Artifact Registry to which the function
      docker image will be pushed after it is built by Cloud Build. If
      specified by user, it is created and managed by user with a customer
      managed encryption key. Otherwise, GCF will create and use a repository
      named 'gcf-artifacts' for every deployed region. It must match the
      pattern
      `projects/{project}/locations/{location}/repositories/{repository}`.
      Repository format must be 'DOCKER'.
    entryPoint: The name of the function (as defined in source code) that will
      be executed. Defaults to the resource name suffix, if not specified. For
      backward compatibility, if function with given name is not found, then
      the system will try to use function named "function". For Node.js this
      is name of a function exported by the module specified in
      `source_location`.
    environmentVariables: User-provided build-time environment variables for
      the function
    onDeployUpdatePolicy: A OnDeployUpdatePolicy attribute.
    runtime: The runtime in which to run the function. Required when deploying
      a new function, optional when updating an existing function. For a
      complete list of possible choices, see the [`gcloud` command reference](
      https://cloud.google.com/sdk/gcloud/reference/functions/deploy#--
      runtime).
    serviceAccount: Service account to be used for building the container. The
      format of this field is
      `projects/{projectId}/serviceAccounts/{serviceAccountEmail}`.
    source: The location of the function source code.
    sourceProvenance: Output only. A permanent fixed identifier for source.
    sourceToken: An identifier for Firebase function sources. Disclaimer: This
      field is only supported for Firebase function deployments.
    workerPool: Name of the Cloud Build Custom Worker Pool that should be used
      to build the function. The format of this field is
      `projects/{project}/locations/{region}/workerPools/{workerPool}` where
      {project} and {region} are the project id and region respectively where
      the worker pool is defined and {workerPool} is the short name of the
      worker pool. If the project id is not the same as the function, then the
      Cloud Functions Service Agent (service-@gcf-admin-
      robot.iam.gserviceaccount.com) must be granted the role Cloud Build
      Custom Workers Builder (roles/cloudbuild.customworkers.builder) in the
      project.
  """

  class DockerRegistryValueValuesEnum(_messages.Enum):
    r"""Docker Registry to use for this deployment. This configuration is only
    applicable to 1st Gen functions, 2nd Gen functions can only use Artifact
    Registry. Deprecated: as of March 2025, `CONTAINER_REGISTRY` option is no
    longer available in response to Container Registry's deprecation:
    https://cloud.google.com/artifact-registry/docs/transition/transition-
    from-gcr Please use Artifact Registry instead, which is the default
    choice. If unspecified, it defaults to `ARTIFACT_REGISTRY`. If
    `docker_repository` field is specified, this field should either be left
    unspecified or set to `ARTIFACT_REGISTRY`.

    Values:
      DOCKER_REGISTRY_UNSPECIFIED: Unspecified.
      CONTAINER_REGISTRY: Docker images will be stored in multi-regional
        Container Registry repositories named `gcf`.
      ARTIFACT_REGISTRY: Docker images will be stored in regional Artifact
        Registry repositories. By default, GCF will create and use
        repositories named `gcf-artifacts` in every region in which a function
        is deployed. But the repository to use can also be specified by the
        user using the `docker_repository` field.
    """
    DOCKER_REGISTRY_UNSPECIFIED = 0
    CONTAINER_REGISTRY = 1
    ARTIFACT_REGISTRY = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentVariablesValue(_messages.Message):
    r"""User-provided build-time environment variables for the function

    Messages:
      AdditionalProperty: An additional property for a
        EnvironmentVariablesValue object.

    Fields:
      additionalProperties: Additional properties of type
        EnvironmentVariablesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentVariablesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  automaticUpdatePolicy = _messages.MessageField('AutomaticUpdatePolicy', 1)
  build = _messages.StringField(2)
  dockerRegistry = _messages.EnumField('DockerRegistryValueValuesEnum', 3)
  dockerRepository = _messages.StringField(4)
  entryPoint = _messages.StringField(5)
  environmentVariables = _messages.MessageField('EnvironmentVariablesValue', 6)
  onDeployUpdatePolicy = _messages.MessageField('OnDeployUpdatePolicy', 7)
  runtime = _messages.StringField(8)
  serviceAccount = _messages.StringField(9)
  source = _messages.MessageField('Source', 10)
  sourceProvenance = _messages.MessageField('SourceProvenance', 11)
  sourceToken = _messages.StringField(12)
  workerPool = _messages.StringField(13)


class CloudfunctionsProjectsLocationsFunctionsAbortFunctionUpgradeRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsAbortFunctionUpgradeRequest
  object.

  Fields:
    abortFunctionUpgradeRequest: A AbortFunctionUpgradeRequest resource to be
      passed as the request body.
    name: Required. The name of the function for which upgrade should be
      aborted.
  """

  abortFunctionUpgradeRequest = _messages.MessageField('AbortFunctionUpgradeRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudfunctionsProjectsLocationsFunctionsCommitFunctionUpgradeRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsCommitFunctionUpgradeRequest
  object.

  Fields:
    commitFunctionUpgradeRequest: A CommitFunctionUpgradeRequest resource to
      be passed as the request body.
    name: Required. The name of the function for which upgrade should be
      finalized.
  """

  commitFunctionUpgradeRequest = _messages.MessageField('CommitFunctionUpgradeRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudfunctionsProjectsLocationsFunctionsCreateRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsCreateRequest object.

  Fields:
    function: A Function resource to be passed as the request body.
    functionId: The ID to use for the function, which will become the final
      component of the function's resource name. This value should be 4-63
      characters, and valid characters are /a-z-/.
    parent: Required. The project and location in which the function should be
      created, specified in the format `projects/*/locations/*`
  """

  function = _messages.MessageField('Function', 1)
  functionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudfunctionsProjectsLocationsFunctionsDeleteRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsDeleteRequest object.

  Fields:
    name: Required. The name of the function which should be deleted.
  """

  name = _messages.StringField(1, required=True)


class CloudfunctionsProjectsLocationsFunctionsDetachFunctionRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsDetachFunctionRequest object.

  Fields:
    detachFunctionRequest: A DetachFunctionRequest resource to be passed as
      the request body.
    name: Required. The name of the function for which should be detached.
  """

  detachFunctionRequest = _messages.MessageField('DetachFunctionRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudfunctionsProjectsLocationsFunctionsGenerateDownloadUrlRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsGenerateDownloadUrlRequest
  object.

  Fields:
    generateDownloadUrlRequest: A GenerateDownloadUrlRequest resource to be
      passed as the request body.
    name: Required. The name of function for which source code Google Cloud
      Storage signed URL should be generated.
  """

  generateDownloadUrlRequest = _messages.MessageField('GenerateDownloadUrlRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudfunctionsProjectsLocationsFunctionsGenerateUploadUrlRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsGenerateUploadUrlRequest
  object.

  Fields:
    generateUploadUrlRequest: A GenerateUploadUrlRequest resource to be passed
      as the request body.
    parent: Required. The project and location in which the Google Cloud
      Storage signed URL should be generated, specified in the format
      `projects/*/locations/*`.
  """

  generateUploadUrlRequest = _messages.MessageField('GenerateUploadUrlRequest', 1)
  parent = _messages.StringField(2, required=True)


class CloudfunctionsProjectsLocationsFunctionsGetIamPolicyRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class CloudfunctionsProjectsLocationsFunctionsGetRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsGetRequest object.

  Fields:
    name: Required. The name of the function which details should be obtained.
    revision: Optional. The optional version of the 1st gen function whose
      details should be obtained. The version of a 1st gen function is an
      integer that starts from 1 and gets incremented on redeployments. GCF
      may keep historical configs for old versions of 1st gen function. This
      field can be specified to fetch the historical configs. This field is
      valid only for GCF 1st gen function.
  """

  name = _messages.StringField(1, required=True)
  revision = _messages.StringField(2)


class CloudfunctionsProjectsLocationsFunctionsListRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsListRequest object.

  Fields:
    filter: The filter for Functions that match the filter expression,
      following the syntax outlined in https://google.aip.dev/160.
    orderBy: The sorting order of the resources returned. Value should be a
      comma separated list of fields. The default sorting order is ascending.
      See https://google.aip.dev/132#ordering.
    pageSize: Maximum number of functions to return per call. The largest
      allowed page_size is 1,000, if the page_size is omitted or specified as
      greater than 1,000 then it will be replaced as 1,000. The size of the
      list response can be less than specified when used with filters.
    pageToken: The value returned by the last `ListFunctionsResponse`;
      indicates that this is a continuation of a prior `ListFunctions` call,
      and that the system should return the next page of data.
    parent: Required. The project and location from which the function should
      be listed, specified in the format `projects/*/locations/*` If you want
      to list functions in all locations, use "-" in place of a location. When
      listing functions in all locations, if one or more location(s) are
      unreachable, the response will contain functions from all reachable
      locations along with the names of any unreachable locations.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CloudfunctionsProjectsLocationsFunctionsPatchRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsPatchRequest object.

  Fields:
    function: A Function resource to be passed as the request body.
    name: A user-defined name of the function. Function names must be unique
      globally and match pattern `projects/*/locations/*/functions/*`
    updateMask: The list of fields to be updated. If no field mask is
      provided, all fields will be updated.
  """

  function = _messages.MessageField('Function', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudfunctionsProjectsLocationsFunctionsRedirectFunctionUpgradeTrafficRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsRedirectFunctionUpgradeTraffic
  Request object.

  Fields:
    name: Required. The name of the function for which traffic target should
      be changed to 2nd Gen from 1st Gen.
    redirectFunctionUpgradeTrafficRequest: A
      RedirectFunctionUpgradeTrafficRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  redirectFunctionUpgradeTrafficRequest = _messages.MessageField('RedirectFunctionUpgradeTrafficRequest', 2)


class CloudfunctionsProjectsLocationsFunctionsRollbackFunctionUpgradeTrafficRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsRollbackFunctionUpgradeTraffic
  Request object.

  Fields:
    name: Required. The name of the function for which traffic target should
      be changed back to 1st Gen from 2nd Gen.
    rollbackFunctionUpgradeTrafficRequest: A
      RollbackFunctionUpgradeTrafficRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  rollbackFunctionUpgradeTrafficRequest = _messages.MessageField('RollbackFunctionUpgradeTrafficRequest', 2)


class CloudfunctionsProjectsLocationsFunctionsSetIamPolicyRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class CloudfunctionsProjectsLocationsFunctionsSetupFunctionUpgradeConfigRequest(_messages.Message):
  r"""A
  CloudfunctionsProjectsLocationsFunctionsSetupFunctionUpgradeConfigRequest
  object.

  Fields:
    name: Required. The name of the function which should have configuration
      copied for upgrade.
    setupFunctionUpgradeConfigRequest: A SetupFunctionUpgradeConfigRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  setupFunctionUpgradeConfigRequest = _messages.MessageField('SetupFunctionUpgradeConfigRequest', 2)


class CloudfunctionsProjectsLocationsFunctionsTestIamPermissionsRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class CloudfunctionsProjectsLocationsListRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class CloudfunctionsProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class CloudfunctionsProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class CloudfunctionsProjectsLocationsRuntimesListRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsRuntimesListRequest object.

  Fields:
    filter: The filter for Runtimes that match the filter expression,
      following the syntax outlined in https://google.aip.dev/160.
    parent: Required. The project and location from which the runtimes should
      be listed, specified in the format `projects/*/locations/*`
  """

  filter = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)


class CommitFunctionUpgradeRequest(_messages.Message):
  r"""Request for the `CommitFunctionUpgrade` method."""


class Date(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class DetachFunctionRequest(_messages.Message):
  r"""Request for the `DetachFunction` method."""


class EventFilter(_messages.Message):
  r"""Filters events based on exact matches on the CloudEvents attributes.

  Fields:
    attribute: Required. The name of a CloudEvents attribute.
    operator: Optional. The operator used for matching the events with the
      value of the filter. If not specified, only events that have an exact
      key-value pair specified in the filter are matched. The only allowed
      value is `match-path-pattern`.
    value: Required. The value for the attribute.
  """

  attribute = _messages.StringField(1)
  operator = _messages.StringField(2)
  value = _messages.StringField(3)


class EventTrigger(_messages.Message):
  r"""Describes EventTrigger, used to request events to be sent from another
  service.

  Enums:
    RetryPolicyValueValuesEnum: Optional. If unset, then defaults to ignoring
      failures (i.e. not retrying them).

  Fields:
    channel: Optional. The name of the channel associated with the trigger in
      `projects/{project}/locations/{location}/channels/{channel}` format. You
      must provide a channel to receive events from Eventarc SaaS partners.
    eventFilters: Criteria used to filter events.
    eventType: Required. The type of event to observe. For example:
      `google.cloud.audit.log.v1.written` or
      `google.cloud.pubsub.topic.v1.messagePublished`.
    pubsubTopic: Optional. The name of a Pub/Sub topic in the same project
      that will be used as the transport topic for the event delivery. Format:
      `projects/{project}/topics/{topic}`. This is only valid for events of
      type `google.cloud.pubsub.topic.v1.messagePublished`. The topic provided
      here will not be deleted at function deletion.
    retryPolicy: Optional. If unset, then defaults to ignoring failures (i.e.
      not retrying them).
    service: Optional. The hostname of the service that 1st Gen function
      should be observed. If no string is provided, the default service
      implementing the API will be used. For example, `storage.googleapis.com`
      is the default for all event types in the `google.storage` namespace.
      The field is only applicable to 1st Gen functions.
    serviceAccountEmail: Optional. The email of the trigger's service account.
      The service account must have permission to invoke Cloud Run services,
      the permission is `run.routes.invoke`. If empty, defaults to the Compute
      Engine default service account:
      `{project_number}-<EMAIL>`.
    trigger: Output only. The resource name of the Eventarc trigger. The
      format of this field is
      `projects/{project}/locations/{region}/triggers/{trigger}`.
    triggerRegion: The region that the trigger will be in. The trigger will
      only receive events originating in this region. It can be the same
      region as the function, a different region or multi-region, or the
      global region. If not provided, defaults to the same region as the
      function.
  """

  class RetryPolicyValueValuesEnum(_messages.Enum):
    r"""Optional. If unset, then defaults to ignoring failures (i.e. not
    retrying them).

    Values:
      RETRY_POLICY_UNSPECIFIED: Not specified.
      RETRY_POLICY_DO_NOT_RETRY: Do not retry.
      RETRY_POLICY_RETRY: Retry on any failure, retry up to 7 days with an
        exponential backoff (capped at 10 seconds).
    """
    RETRY_POLICY_UNSPECIFIED = 0
    RETRY_POLICY_DO_NOT_RETRY = 1
    RETRY_POLICY_RETRY = 2

  channel = _messages.StringField(1)
  eventFilters = _messages.MessageField('EventFilter', 2, repeated=True)
  eventType = _messages.StringField(3)
  pubsubTopic = _messages.StringField(4)
  retryPolicy = _messages.EnumField('RetryPolicyValueValuesEnum', 5)
  service = _messages.StringField(6)
  serviceAccountEmail = _messages.StringField(7)
  trigger = _messages.StringField(8)
  triggerRegion = _messages.StringField(9)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Function(_messages.Message):
  r"""Describes a Cloud Function that contains user computation executed in
  response to an event. It encapsulates function and trigger configurations.

  Enums:
    EnvironmentValueValuesEnum: Describe whether the function is 1st Gen or
      2nd Gen.
    StateValueValuesEnum: Output only. State of the function.

  Messages:
    LabelsValue: Labels associated with this Cloud Function.

  Fields:
    buildConfig: Describes the Build step of the function that builds a
      container from the given source.
    buildpackStack: Specifies a Google provided Buildpack Stack -- pair of
      base images (for building and runtime) that include a curated set of
      pre-installed packages.
    createTime: Output only. The create timestamp of a Cloud Function. This is
      only applicable to 2nd Gen functions.
    description: User-provided description of a function.
    environment: Describe whether the function is 1st Gen or 2nd Gen.
    eventTrigger: An Eventarc trigger managed by Google Cloud Functions that
      fires events in response to a condition in another service.
    kmsKeyName: Resource name of a KMS crypto key (managed by the user) used
      to encrypt/decrypt function resources. It must match the pattern `projec
      ts/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto
      _key}`.
    labels: Labels associated with this Cloud Function.
    name: A user-defined name of the function. Function names must be unique
      globally and match pattern `projects/*/locations/*/functions/*`
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    serviceConfig: Describes the Service being deployed. Currently deploys
      services to Cloud Run (fully managed).
    state: Output only. State of the function.
    stateMessages: Output only. State Messages for this Cloud Function.
    updateTime: Output only. The last update timestamp of a Cloud Function.
    upgradeInfo: Output only. UpgradeInfo for this Cloud Function
    url: Output only. The deployed url for the function.
  """

  class EnvironmentValueValuesEnum(_messages.Enum):
    r"""Describe whether the function is 1st Gen or 2nd Gen.

    Values:
      ENVIRONMENT_UNSPECIFIED: Unspecified
      GEN_1: Gen 1
      GEN_2: Gen 2
    """
    ENVIRONMENT_UNSPECIFIED = 0
    GEN_1 = 1
    GEN_2 = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the function.

    Values:
      STATE_UNSPECIFIED: Not specified. Invalid state.
      ACTIVE: Function has been successfully deployed and is serving.
      FAILED: Function deployment failed and the function is not serving.
      DEPLOYING: Function is being created or updated.
      DELETING: Function is being deleted.
      UNKNOWN: Function deployment failed and the function serving state is
        undefined. The function should be updated or deleted to move it out of
        this state.
      DETACHING: Function is being detached.
      DETACH_FAILED: Function detach failed and the function is still serving.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    FAILED = 2
    DEPLOYING = 3
    DELETING = 4
    UNKNOWN = 5
    DETACHING = 6
    DETACH_FAILED = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this Cloud Function.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  buildConfig = _messages.MessageField('BuildConfig', 1)
  buildpackStack = _messages.StringField(2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  environment = _messages.EnumField('EnvironmentValueValuesEnum', 5)
  eventTrigger = _messages.MessageField('EventTrigger', 6)
  kmsKeyName = _messages.StringField(7)
  labels = _messages.MessageField('LabelsValue', 8)
  name = _messages.StringField(9)
  satisfiesPzi = _messages.BooleanField(10)
  satisfiesPzs = _messages.BooleanField(11)
  serviceConfig = _messages.MessageField('ServiceConfig', 12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  stateMessages = _messages.MessageField('GoogleCloudFunctionsV2StateMessage', 14, repeated=True)
  updateTime = _messages.StringField(15)
  upgradeInfo = _messages.MessageField('UpgradeInfo', 16)
  url = _messages.StringField(17)


class GenerateDownloadUrlRequest(_messages.Message):
  r"""Request of `GenerateDownloadUrl` method."""


class GenerateDownloadUrlResponse(_messages.Message):
  r"""Response of `GenerateDownloadUrl` method.

  Fields:
    downloadUrl: The generated Google Cloud Storage signed URL that should be
      used for function source code download.
  """

  downloadUrl = _messages.StringField(1)


class GenerateUploadUrlRequest(_messages.Message):
  r"""Request of `GenerateSourceUploadUrl` method.

  Enums:
    EnvironmentValueValuesEnum: The function environment the generated upload
      url will be used for. The upload url for 2nd Gen functions can also be
      used for 1st gen functions, but not vice versa. If not specified, 2nd
      generation-style upload URLs are generated.

  Fields:
    environment: The function environment the generated upload url will be
      used for. The upload url for 2nd Gen functions can also be used for 1st
      gen functions, but not vice versa. If not specified, 2nd generation-
      style upload URLs are generated.
    kmsKeyName: Resource name of a KMS crypto key (managed by the user) used
      to encrypt/decrypt function source code objects in intermediate Cloud
      Storage buckets. When you generate an upload url and upload your source
      code, it gets copied to an intermediate Cloud Storage bucket. The source
      code is then copied to a versioned directory in the sources bucket in
      the consumer project during the function deployment. It must match the
      pattern `projects/{project}/locations/{location}/keyRings/{key_ring}/cry
      ptoKeys/{crypto_key}`. The Google Cloud Functions service account
      (service-{project_number}@gcf-admin-robot.iam.gserviceaccount.com) must
      be granted the role 'Cloud KMS CryptoKey Encrypter/Decrypter
      (roles/cloudkms.cryptoKeyEncrypterDecrypter)' on the
      Key/KeyRing/Project/Organization (least access preferred).
  """

  class EnvironmentValueValuesEnum(_messages.Enum):
    r"""The function environment the generated upload url will be used for.
    The upload url for 2nd Gen functions can also be used for 1st gen
    functions, but not vice versa. If not specified, 2nd generation-style
    upload URLs are generated.

    Values:
      ENVIRONMENT_UNSPECIFIED: Unspecified
      GEN_1: Gen 1
      GEN_2: Gen 2
    """
    ENVIRONMENT_UNSPECIFIED = 0
    GEN_1 = 1
    GEN_2 = 2

  environment = _messages.EnumField('EnvironmentValueValuesEnum', 1)
  kmsKeyName = _messages.StringField(2)


class GenerateUploadUrlResponse(_messages.Message):
  r"""Response of `GenerateSourceUploadUrl` method.

  Fields:
    storageSource: The location of the source code in the upload bucket. Once
      the archive is uploaded using the `upload_url` use this field to set the
      `function.build_config.source.storage_source` during CreateFunction and
      UpdateFunction. Generation defaults to 0, as Cloud Storage provides a
      new generation only upon uploading a new object or version of an object.
    uploadUrl: The generated Google Cloud Storage signed URL that should be
      used for a function source code upload. The uploaded file should be a
      zip archive which contains a function.
  """

  storageSource = _messages.MessageField('StorageSource', 1)
  uploadUrl = _messages.StringField(2)


class GoogleCloudFunctionsV2LocationMetadata(_messages.Message):
  r"""Extra GCF specific location information.

  Enums:
    EnvironmentsValueListEntryValuesEnum:

  Fields:
    environments: The Cloud Function environments this location supports.
  """

  class EnvironmentsValueListEntryValuesEnum(_messages.Enum):
    r"""EnvironmentsValueListEntryValuesEnum enum type.

    Values:
      ENVIRONMENT_UNSPECIFIED: Unspecified
      GEN_1: Gen 1
      GEN_2: Gen 2
    """
    ENVIRONMENT_UNSPECIFIED = 0
    GEN_1 = 1
    GEN_2 = 2

  environments = _messages.EnumField('EnvironmentsValueListEntryValuesEnum', 1, repeated=True)


class GoogleCloudFunctionsV2OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Enums:
    OperationTypeValueValuesEnum: The operation type.

  Messages:
    RequestResourceValue: The original request that started the operation.

  Fields:
    apiVersion: API version used to start the operation.
    buildName: The build name of the function for create and update
      operations.
    cancelRequested: Identifies whether the user has requested cancellation of
      the operation. Operations that have successfully been cancelled have
      google.longrunning.Operation.error value with a google.rpc.Status.code
      of 1, corresponding to `Code.CANCELLED`.
    createTime: The time the operation was created.
    customIamRoleDetected: Output only. Whether a custom IAM role binding was
      detected during the upgrade.
    endTime: The time the operation finished running.
    operationType: The operation type.
    requestResource: The original request that started the operation.
    sourceToken: An identifier for Firebase function sources. Disclaimer: This
      field is only supported for Firebase function deployments.
    stages: Mechanism for reporting in-progress stages
    statusDetail: Human-readable status of the operation, if any.
    target: Server-defined resource path for the target of the operation.
    verb: Name of the verb executed by the operation.
  """

  class OperationTypeValueValuesEnum(_messages.Enum):
    r"""The operation type.

    Values:
      OPERATIONTYPE_UNSPECIFIED: Unspecified
      CREATE_FUNCTION: CreateFunction
      UPDATE_FUNCTION: UpdateFunction
      DELETE_FUNCTION: DeleteFunction
      REDIRECT_FUNCTION_UPGRADE_TRAFFIC: RedirectFunctionUpgradeTraffic
      ROLLBACK_FUNCTION_UPGRADE_TRAFFIC: RollbackFunctionUpgradeTraffic
      SETUP_FUNCTION_UPGRADE_CONFIG: SetupFunctionUpgradeConfig
      ABORT_FUNCTION_UPGRADE: AbortFunctionUpgrade
      COMMIT_FUNCTION_UPGRADE: CommitFunctionUpgrade
      DETACH_FUNCTION: DetachFunction
    """
    OPERATIONTYPE_UNSPECIFIED = 0
    CREATE_FUNCTION = 1
    UPDATE_FUNCTION = 2
    DELETE_FUNCTION = 3
    REDIRECT_FUNCTION_UPGRADE_TRAFFIC = 4
    ROLLBACK_FUNCTION_UPGRADE_TRAFFIC = 5
    SETUP_FUNCTION_UPGRADE_CONFIG = 6
    ABORT_FUNCTION_UPGRADE = 7
    COMMIT_FUNCTION_UPGRADE = 8
    DETACH_FUNCTION = 9

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RequestResourceValue(_messages.Message):
    r"""The original request that started the operation.

    Messages:
      AdditionalProperty: An additional property for a RequestResourceValue
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RequestResourceValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  apiVersion = _messages.StringField(1)
  buildName = _messages.StringField(2)
  cancelRequested = _messages.BooleanField(3)
  createTime = _messages.StringField(4)
  customIamRoleDetected = _messages.BooleanField(5)
  endTime = _messages.StringField(6)
  operationType = _messages.EnumField('OperationTypeValueValuesEnum', 7)
  requestResource = _messages.MessageField('RequestResourceValue', 8)
  sourceToken = _messages.StringField(9)
  stages = _messages.MessageField('GoogleCloudFunctionsV2Stage', 10, repeated=True)
  statusDetail = _messages.StringField(11)
  target = _messages.StringField(12)
  verb = _messages.StringField(13)


class GoogleCloudFunctionsV2Stage(_messages.Message):
  r"""Each Stage of the deployment process

  Enums:
    NameValueValuesEnum: Name of the Stage. This will be unique for each
      Stage.
    StateValueValuesEnum: Current state of the Stage

  Fields:
    message: Message describing the Stage
    name: Name of the Stage. This will be unique for each Stage.
    resource: Resource of the Stage
    resourceUri: Link to the current Stage resource
    state: Current state of the Stage
    stateMessages: State messages from the current Stage.
  """

  class NameValueValuesEnum(_messages.Enum):
    r"""Name of the Stage. This will be unique for each Stage.

    Values:
      NAME_UNSPECIFIED: Not specified. Invalid name.
      ARTIFACT_REGISTRY: Artifact Registry Stage
      BUILD: Build Stage
      SERVICE: Service Stage
      TRIGGER: Trigger Stage
      SERVICE_ROLLBACK: Service Rollback Stage
      TRIGGER_ROLLBACK: Trigger Rollback Stage
    """
    NAME_UNSPECIFIED = 0
    ARTIFACT_REGISTRY = 1
    BUILD = 2
    SERVICE = 3
    TRIGGER = 4
    SERVICE_ROLLBACK = 5
    TRIGGER_ROLLBACK = 6

  class StateValueValuesEnum(_messages.Enum):
    r"""Current state of the Stage

    Values:
      STATE_UNSPECIFIED: Not specified. Invalid state.
      NOT_STARTED: Stage has not started.
      IN_PROGRESS: Stage is in progress.
      COMPLETE: Stage has completed.
    """
    STATE_UNSPECIFIED = 0
    NOT_STARTED = 1
    IN_PROGRESS = 2
    COMPLETE = 3

  message = _messages.StringField(1)
  name = _messages.EnumField('NameValueValuesEnum', 2)
  resource = _messages.StringField(3)
  resourceUri = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  stateMessages = _messages.MessageField('GoogleCloudFunctionsV2StateMessage', 6, repeated=True)


class GoogleCloudFunctionsV2StateMessage(_messages.Message):
  r"""Informational messages about the state of the Cloud Function or
  Operation.

  Enums:
    SeverityValueValuesEnum: Severity of the state message.

  Fields:
    message: The message.
    severity: Severity of the state message.
    type: One-word CamelCase type of the state message.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Severity of the state message.

    Values:
      SEVERITY_UNSPECIFIED: Not specified. Invalid severity.
      ERROR: ERROR-level severity.
      WARNING: WARNING-level severity.
      INFO: INFO-level severity.
    """
    SEVERITY_UNSPECIFIED = 0
    ERROR = 1
    WARNING = 2
    INFO = 3

  message = _messages.StringField(1)
  severity = _messages.EnumField('SeverityValueValuesEnum', 2)
  type = _messages.StringField(3)


class ListFunctionsResponse(_messages.Message):
  r"""Response for the `ListFunctions` method.

  Fields:
    functions: The functions that match the request.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached. The response does not
      include any functions from these locations.
  """

  functions = _messages.MessageField('Function', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListRuntimesResponse(_messages.Message):
  r"""Response for the `ListRuntimes` method.

  Fields:
    runtimes: The runtimes that match the request.
  """

  runtimes = _messages.MessageField('Runtime', 1, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class OnDeployUpdatePolicy(_messages.Message):
  r"""Security patches are only applied when a function is redeployed.

  Fields:
    runtimeVersion: Output only. contains the runtime version which was used
      during latest function deployment.
  """

  runtimeVersion = _messages.StringField(1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadataV1(_messages.Message):
  r"""Metadata describing an Operation

  Enums:
    TypeValueValuesEnum: Type of operation.

  Messages:
    RequestValue: The original request that started the operation.

  Fields:
    buildId: The Cloud Build ID of the function created or updated by an API
      call. This field is only populated for Create and Update operations.
    buildName: The Cloud Build Name of the function deployment. This field is
      only populated for Create and Update operations.
      `projects//locations//builds/`.
    request: The original request that started the operation.
    sourceToken: An identifier for Firebase function sources. Disclaimer: This
      field is only supported for Firebase function deployments.
    target: Target of the operation - for example
      `projects/project-1/locations/region-1/functions/function-1`
    type: Type of operation.
    updateTime: The last update timestamp of the operation.
    versionId: Version id of the function created or updated by an API call.
      This field is only populated for Create and Update operations.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of operation.

    Values:
      OPERATION_UNSPECIFIED: Unknown operation type.
      CREATE_FUNCTION: Triggered by CreateFunction call
      UPDATE_FUNCTION: Triggered by UpdateFunction call
      DELETE_FUNCTION: Triggered by DeleteFunction call.
    """
    OPERATION_UNSPECIFIED = 0
    CREATE_FUNCTION = 1
    UPDATE_FUNCTION = 2
    DELETE_FUNCTION = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RequestValue(_messages.Message):
    r"""The original request that started the operation.

    Messages:
      AdditionalProperty: An additional property for a RequestValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RequestValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  buildId = _messages.StringField(1)
  buildName = _messages.StringField(2)
  request = _messages.MessageField('RequestValue', 3)
  sourceToken = _messages.StringField(4)
  target = _messages.StringField(5)
  type = _messages.EnumField('TypeValueValuesEnum', 6)
  updateTime = _messages.StringField(7)
  versionId = _messages.IntegerField(8)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class RedirectFunctionUpgradeTrafficRequest(_messages.Message):
  r"""Request for the `RedirectFunctionUpgradeTraffic` method."""


class RepoSource(_messages.Message):
  r"""Location of the source in a Google Cloud Source Repository.

  Fields:
    branchName: Regex matching branches to build. The syntax of the regular
      expressions accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
    commitSha: Explicit commit SHA to build.
    dir: Directory, relative to the source root, in which to run the build.
      This must be a relative path. If a step's `dir` is specified and is an
      absolute path, this value is ignored for that step's execution. eg.
      helloworld (no leading slash allowed)
    projectId: ID of the project that owns the Cloud Source Repository. If
      omitted, the project ID requesting the build is assumed.
    repoName: Name of the Cloud Source Repository.
    tagName: Regex matching tags to build. The syntax of the regular
      expressions accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
  """

  branchName = _messages.StringField(1)
  commitSha = _messages.StringField(2)
  dir = _messages.StringField(3)
  projectId = _messages.StringField(4)
  repoName = _messages.StringField(5)
  tagName = _messages.StringField(6)


class RollbackFunctionUpgradeTrafficRequest(_messages.Message):
  r"""Request for the `RollbackFunctionUpgradeTraffic` method."""


class Runtime(_messages.Message):
  r"""Describes a runtime and any special information (e.g., deprecation
  status) related to it.

  Enums:
    EnvironmentValueValuesEnum: The environment for the runtime.
    StageValueValuesEnum: The stage of life this runtime is in, e.g., BETA,
      GA, etc.

  Fields:
    decommissionDate: Decommission date for the runtime.
    deprecationDate: Deprecation date for the runtime.
    displayName: The user facing name, eg 'Go 1.13', 'Node.js 12', etc.
    environment: The environment for the runtime.
    name: The name of the runtime, e.g., 'go113', 'nodejs12', etc.
    stage: The stage of life this runtime is in, e.g., BETA, GA, etc.
    warnings: Warning messages, e.g., a deprecation warning.
  """

  class EnvironmentValueValuesEnum(_messages.Enum):
    r"""The environment for the runtime.

    Values:
      ENVIRONMENT_UNSPECIFIED: Unspecified
      GEN_1: Gen 1
      GEN_2: Gen 2
    """
    ENVIRONMENT_UNSPECIFIED = 0
    GEN_1 = 1
    GEN_2 = 2

  class StageValueValuesEnum(_messages.Enum):
    r"""The stage of life this runtime is in, e.g., BETA, GA, etc.

    Values:
      RUNTIME_STAGE_UNSPECIFIED: Not specified.
      DEVELOPMENT: The runtime is in development.
      ALPHA: The runtime is in the Alpha stage.
      BETA: The runtime is in the Beta stage.
      GA: The runtime is generally available.
      DEPRECATED: The runtime is deprecated.
      DECOMMISSIONED: The runtime is no longer supported.
    """
    RUNTIME_STAGE_UNSPECIFIED = 0
    DEVELOPMENT = 1
    ALPHA = 2
    BETA = 3
    GA = 4
    DEPRECATED = 5
    DECOMMISSIONED = 6

  decommissionDate = _messages.MessageField('Date', 1)
  deprecationDate = _messages.MessageField('Date', 2)
  displayName = _messages.StringField(3)
  environment = _messages.EnumField('EnvironmentValueValuesEnum', 4)
  name = _messages.StringField(5)
  stage = _messages.EnumField('StageValueValuesEnum', 6)
  warnings = _messages.StringField(7, repeated=True)


class SecretEnvVar(_messages.Message):
  r"""Configuration for a secret environment variable. It has the information
  necessary to fetch the secret value from secret manager and expose it as an
  environment variable.

  Fields:
    key: Name of the environment variable.
    projectId: Project identifier (preferably project number but can also be
      the project ID) of the project that contains the secret. If not set, it
      is assumed that the secret is in the same project as the function.
    secret: Name of the secret in secret manager (not the full resource name).
    version: Version of the secret (version number or the string 'latest'). It
      is recommended to use a numeric version for secret environment variables
      as any updates to the secret value is not reflected until new instances
      start.
  """

  key = _messages.StringField(1)
  projectId = _messages.StringField(2)
  secret = _messages.StringField(3)
  version = _messages.StringField(4)


class SecretVersion(_messages.Message):
  r"""Configuration for a single version.

  Fields:
    path: Relative path of the file under the mount path where the secret
      value for this version will be fetched and made available. For example,
      setting the mount_path as '/etc/secrets' and path as `secret_foo` would
      mount the secret value file at `/etc/secrets/secret_foo`.
    version: Version of the secret (version number or the string 'latest'). It
      is preferable to use `latest` version with secret volumes as secret
      value changes are reflected immediately.
  """

  path = _messages.StringField(1)
  version = _messages.StringField(2)


class SecretVolume(_messages.Message):
  r"""Configuration for a secret volume. It has the information necessary to
  fetch the secret value from secret manager and make it available as files
  mounted at the requested paths within the application container.

  Fields:
    mountPath: The path within the container to mount the secret volume. For
      example, setting the mount_path as `/etc/secrets` would mount the secret
      value files under the `/etc/secrets` directory. This directory will also
      be completely shadowed and unavailable to mount any other secrets.
      Recommended mount path: /etc/secrets
    projectId: Project identifier (preferably project number but can also be
      the project ID) of the project that contains the secret. If not set, it
      is assumed that the secret is in the same project as the function.
    secret: Name of the secret in secret manager (not the full resource name).
    versions: List of secret versions to mount for this secret. If empty, the
      `latest` version of the secret will be made available in a file named
      after the secret under the mount point.
  """

  mountPath = _messages.StringField(1)
  projectId = _messages.StringField(2)
  secret = _messages.StringField(3)
  versions = _messages.MessageField('SecretVersion', 4, repeated=True)


class ServiceConfig(_messages.Message):
  r"""Describes the Service being deployed. Currently Supported : Cloud Run
  (fully managed).

  Enums:
    IngressSettingsValueValuesEnum: The ingress settings for the function,
      controlling what traffic can reach it.
    SecurityLevelValueValuesEnum: Security level configure whether the
      function only accepts https. This configuration is only applicable to
      1st Gen functions with Http trigger. By default https is optional for
      1st Gen functions; 2nd Gen functions are https ONLY.
    VpcConnectorEgressSettingsValueValuesEnum: The egress settings for the
      connector, controlling what traffic is diverted through it.

  Messages:
    EnvironmentVariablesValue: Environment variables that shall be available
      during function execution.

  Fields:
    allTrafficOnLatestRevision: Whether 100% of traffic is routed to the
      latest revision. On CreateFunction and UpdateFunction, when set to true,
      the revision being deployed will serve 100% of traffic, ignoring any
      traffic split settings, if any. On GetFunction, true will be returned if
      the latest revision is serving 100% of traffic.
    availableCpu: The number of CPUs used in a single container instance.
      Default value is calculated from available memory. Supports the same
      values as Cloud Run, see https://cloud.google.com/run/docs/reference/res
      t/v1/Container#resourcerequirements Example: "1" indicates 1 vCPU
    availableMemory: The amount of memory available for a function. Defaults
      to 256M. Supported units are k, M, G, Mi, Gi. If no unit is supplied the
      value is interpreted as bytes. See https://github.com/kubernetes/kuberne
      tes/blob/master/staging/src/k8s.io/apimachinery/pkg/api/resource/quantit
      y.go a full description.
    binaryAuthorizationPolicy: Optional. The binary authorization policy to be
      checked when deploying the Cloud Run service.
    environmentVariables: Environment variables that shall be available during
      function execution.
    ingressSettings: The ingress settings for the function, controlling what
      traffic can reach it.
    maxInstanceCount: The limit on the maximum number of function instances
      that may coexist at a given time. In some cases, such as rapid traffic
      surges, Cloud Functions may, for a short period of time, create more
      instances than the specified max instances limit. If your function
      cannot tolerate this temporary behavior, you may want to factor in a
      safety margin and set a lower max instances value than your function can
      tolerate. See the [Max
      Instances](https://cloud.google.com/functions/docs/max-instances) Guide
      for more details.
    maxInstanceRequestConcurrency: Sets the maximum number of concurrent
      requests that each instance can receive. Defaults to 1.
    minInstanceCount: The limit on the minimum number of function instances
      that may coexist at a given time. Function instances are kept in idle
      state for a short period after they finished executing the request to
      reduce cold start time for subsequent requests. Setting a minimum
      instance count will ensure that the given number of instances are kept
      running in idle state always. This can help with cold start times when
      jump in incoming request count occurs after the idle instance would have
      been stopped in the default case.
    revision: Output only. The name of service revision.
    secretEnvironmentVariables: Secret environment variables configuration.
    secretVolumes: Secret volumes configuration.
    securityLevel: Security level configure whether the function only accepts
      https. This configuration is only applicable to 1st Gen functions with
      Http trigger. By default https is optional for 1st Gen functions; 2nd
      Gen functions are https ONLY.
    service: Output only. Name of the service associated with a Function. The
      format of this field is
      `projects/{project}/locations/{region}/services/{service}`
    serviceAccountEmail: The email of the service's service account. If empty,
      defaults to `{project_number}-<EMAIL>`.
    timeoutSeconds: The function execution timeout. Execution is considered
      failed and can be terminated if the function is not completed at the end
      of the timeout period. Defaults to 60 seconds.
    uri: Output only. URI of the Service deployed.
    vpcConnector: The Serverless VPC Access connector that this cloud function
      can connect to. The format of this field is
      `projects/*/locations/*/connectors/*`.
    vpcConnectorEgressSettings: The egress settings for the connector,
      controlling what traffic is diverted through it.
  """

  class IngressSettingsValueValuesEnum(_messages.Enum):
    r"""The ingress settings for the function, controlling what traffic can
    reach it.

    Values:
      INGRESS_SETTINGS_UNSPECIFIED: Unspecified.
      ALLOW_ALL: Allow HTTP traffic from public and private sources.
      ALLOW_INTERNAL_ONLY: Allow HTTP traffic from only private VPC sources.
      ALLOW_INTERNAL_AND_GCLB: Allow HTTP traffic from private VPC sources and
        through GCLB.
    """
    INGRESS_SETTINGS_UNSPECIFIED = 0
    ALLOW_ALL = 1
    ALLOW_INTERNAL_ONLY = 2
    ALLOW_INTERNAL_AND_GCLB = 3

  class SecurityLevelValueValuesEnum(_messages.Enum):
    r"""Security level configure whether the function only accepts https. This
    configuration is only applicable to 1st Gen functions with Http trigger.
    By default https is optional for 1st Gen functions; 2nd Gen functions are
    https ONLY.

    Values:
      SECURITY_LEVEL_UNSPECIFIED: Unspecified.
      SECURE_ALWAYS: Requests for a URL that match this handler that do not
        use HTTPS are automatically redirected to the HTTPS URL with the same
        path. Query parameters are reserved for the redirect.
      SECURE_OPTIONAL: Both HTTP and HTTPS requests with URLs that match the
        handler succeed without redirects. The application can examine the
        request to determine which protocol was used and respond accordingly.
    """
    SECURITY_LEVEL_UNSPECIFIED = 0
    SECURE_ALWAYS = 1
    SECURE_OPTIONAL = 2

  class VpcConnectorEgressSettingsValueValuesEnum(_messages.Enum):
    r"""The egress settings for the connector, controlling what traffic is
    diverted through it.

    Values:
      VPC_CONNECTOR_EGRESS_SETTINGS_UNSPECIFIED: Unspecified.
      PRIVATE_RANGES_ONLY: Use the VPC Access Connector only for private IP
        space from RFC1918.
      ALL_TRAFFIC: Force the use of VPC Access Connector for all egress
        traffic from the function.
    """
    VPC_CONNECTOR_EGRESS_SETTINGS_UNSPECIFIED = 0
    PRIVATE_RANGES_ONLY = 1
    ALL_TRAFFIC = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentVariablesValue(_messages.Message):
    r"""Environment variables that shall be available during function
    execution.

    Messages:
      AdditionalProperty: An additional property for a
        EnvironmentVariablesValue object.

    Fields:
      additionalProperties: Additional properties of type
        EnvironmentVariablesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentVariablesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allTrafficOnLatestRevision = _messages.BooleanField(1)
  availableCpu = _messages.StringField(2)
  availableMemory = _messages.StringField(3)
  binaryAuthorizationPolicy = _messages.StringField(4)
  environmentVariables = _messages.MessageField('EnvironmentVariablesValue', 5)
  ingressSettings = _messages.EnumField('IngressSettingsValueValuesEnum', 6)
  maxInstanceCount = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  maxInstanceRequestConcurrency = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  minInstanceCount = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  revision = _messages.StringField(10)
  secretEnvironmentVariables = _messages.MessageField('SecretEnvVar', 11, repeated=True)
  secretVolumes = _messages.MessageField('SecretVolume', 12, repeated=True)
  securityLevel = _messages.EnumField('SecurityLevelValueValuesEnum', 13)
  service = _messages.StringField(14)
  serviceAccountEmail = _messages.StringField(15)
  timeoutSeconds = _messages.IntegerField(16, variant=_messages.Variant.INT32)
  uri = _messages.StringField(17)
  vpcConnector = _messages.StringField(18)
  vpcConnectorEgressSettings = _messages.EnumField('VpcConnectorEgressSettingsValueValuesEnum', 19)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SetupFunctionUpgradeConfigRequest(_messages.Message):
  r"""Request for the `SetupFunctionUpgradeConfig` method.

  Fields:
    triggerServiceAccount: Optional. The trigger's service account. The
      service account must have permission to invoke Cloud Run services, the
      permission is `run.routes.invoke`. If empty, defaults to the Compute
      Engine default service account:
      `{project_number}-<EMAIL>`.
  """

  triggerServiceAccount = _messages.StringField(1)


class Source(_messages.Message):
  r"""The location of the function source code.

  Fields:
    gitUri: If provided, get the source from GitHub repository. This option is
      valid only for GCF 1st Gen function. Example:
      https://github.com///blob//
    repoSource: If provided, get the source from this location in a Cloud
      Source Repository.
    storageSource: If provided, get the source from this location in Google
      Cloud Storage.
  """

  gitUri = _messages.StringField(1)
  repoSource = _messages.MessageField('RepoSource', 2)
  storageSource = _messages.MessageField('StorageSource', 3)


class SourceProvenance(_messages.Message):
  r"""Provenance of the source. Ways to find the original source, or verify
  that some source was used for this build.

  Fields:
    gitUri: A copy of the build's `source.git_uri`, if exists, with any
      commits resolved.
    resolvedRepoSource: A copy of the build's `source.repo_source`, if exists,
      with any revisions resolved.
    resolvedStorageSource: A copy of the build's `source.storage_source`, if
      exists, with any generations resolved.
  """

  gitUri = _messages.StringField(1)
  resolvedRepoSource = _messages.MessageField('RepoSource', 2)
  resolvedStorageSource = _messages.MessageField('StorageSource', 3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StorageSource(_messages.Message):
  r"""Location of the source in an archive file in Google Cloud Storage.

  Fields:
    bucket: Google Cloud Storage bucket containing the source (see [Bucket
      Name Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)).
    generation: Google Cloud Storage generation for the object. If the
      generation is omitted, the latest generation will be used.
    object: Google Cloud Storage object containing the source. This object
      must be a gzipped archive file (`.tar.gz`) containing source to build.
    sourceUploadUrl: When the specified storage bucket is a 1st gen function
      uploard url bucket, this field should be set as the generated upload url
      for 1st gen deployment.
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)
  sourceUploadUrl = _messages.StringField(4)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class UpgradeInfo(_messages.Message):
  r"""Information related to: * A function's eligibility for 1st Gen to 2nd
  Gen migration. * Current state of migration for function undergoing
  migration.

  Enums:
    UpgradeStateValueValuesEnum: UpgradeState of the function

  Fields:
    buildConfig: Describes the Build step of the function that builds a
      container to prepare for 2nd gen upgrade.
    eventTrigger: Describes the Event trigger which has been setup to prepare
      for 2nd gen upgrade.
    serviceConfig: Describes the Cloud Run service which has been setup to
      prepare for 2nd gen upgrade.
    upgradeState: UpgradeState of the function
  """

  class UpgradeStateValueValuesEnum(_messages.Enum):
    r"""UpgradeState of the function

    Values:
      UPGRADE_STATE_UNSPECIFIED: Unspecified state. Most functions are in this
        upgrade state.
      ELIGIBLE_FOR_2ND_GEN_UPGRADE: Functions in this state are eligible for
        1st Gen -> 2nd Gen upgrade.
      UPGRADE_OPERATION_IN_PROGRESS: An upgrade related operation is in
        progress.
      SETUP_FUNCTION_UPGRADE_CONFIG_SUCCESSFUL: SetupFunctionUpgradeConfig API
        was successful and a 2nd Gen function has been created based on 1st
        Gen function instance.
      SETUP_FUNCTION_UPGRADE_CONFIG_ERROR: SetupFunctionUpgradeConfig API was
        un-successful.
      ABORT_FUNCTION_UPGRADE_ERROR: AbortFunctionUpgrade API was un-
        successful.
      REDIRECT_FUNCTION_UPGRADE_TRAFFIC_SUCCESSFUL:
        RedirectFunctionUpgradeTraffic API was successful and traffic is
        served by 2nd Gen function stack.
      REDIRECT_FUNCTION_UPGRADE_TRAFFIC_ERROR: RedirectFunctionUpgradeTraffic
        API was un-successful.
      ROLLBACK_FUNCTION_UPGRADE_TRAFFIC_ERROR: RollbackFunctionUpgradeTraffic
        API was un-successful.
      COMMIT_FUNCTION_UPGRADE_ERROR: CommitFunctionUpgrade API was un-
        successful.
    """
    UPGRADE_STATE_UNSPECIFIED = 0
    ELIGIBLE_FOR_2ND_GEN_UPGRADE = 1
    UPGRADE_OPERATION_IN_PROGRESS = 2
    SETUP_FUNCTION_UPGRADE_CONFIG_SUCCESSFUL = 3
    SETUP_FUNCTION_UPGRADE_CONFIG_ERROR = 4
    ABORT_FUNCTION_UPGRADE_ERROR = 5
    REDIRECT_FUNCTION_UPGRADE_TRAFFIC_SUCCESSFUL = 6
    REDIRECT_FUNCTION_UPGRADE_TRAFFIC_ERROR = 7
    ROLLBACK_FUNCTION_UPGRADE_TRAFFIC_ERROR = 8
    COMMIT_FUNCTION_UPGRADE_ERROR = 9

  buildConfig = _messages.MessageField('BuildConfig', 1)
  eventTrigger = _messages.MessageField('EventTrigger', 2)
  serviceConfig = _messages.MessageField('ServiceConfig', 3)
  upgradeState = _messages.EnumField('UpgradeStateValueValuesEnum', 4)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    CloudfunctionsProjectsLocationsFunctionsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
