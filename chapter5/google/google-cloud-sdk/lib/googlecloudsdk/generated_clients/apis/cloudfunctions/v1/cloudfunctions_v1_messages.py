"""Generated message classes for cloudfunctions version v1.

Manages lightweight user-provided functions executed in response to events.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'cloudfunctions'


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class AutomaticUpdatePolicy(_messages.Message):
  r"""Security patches are applied automatically to the runtime without
  requiring the function to be redeployed.
  """



class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CallFunctionRequest(_messages.Message):
  r"""Request for the `CallFunction` method.

  Fields:
    data: Required. Input to be passed to the function.
  """

  data = _messages.StringField(1)


class CallFunctionResponse(_messages.Message):
  r"""Response of `CallFunction` method.

  Fields:
    error: Either system or user-function generated error. Set if execution
      was not successful.
    executionId: Execution id of function invocation.
    result: Result populated for successful execution of synchronous function.
      Will not be populated if function does not return a result through
      context.
  """

  error = _messages.StringField(1)
  executionId = _messages.StringField(2)
  result = _messages.StringField(3)


class CloudFunction(_messages.Message):
  r"""Describes a Cloud Function that contains user computation executed in
  response to an event. It encapsulate function and triggers configurations.

  Enums:
    DockerRegistryValueValuesEnum: Docker Registry to use for this deployment.
      Deprecated: as of March 2025, `CONTAINER_REGISTRY` option is no longer
      available in response to Container Registry's deprecation:
      https://cloud.google.com/artifact-registry/docs/transition/transition-
      from-gcr Please use Artifact Registry instead, which is the default
      choice. If unspecified, it defaults to `ARTIFACT_REGISTRY`. If
      `docker_repository` field is specified, this field should either be left
      unspecified or set to `ARTIFACT_REGISTRY`.
    IngressSettingsValueValuesEnum: The ingress settings for the function,
      controlling what traffic can reach it.
    StatusValueValuesEnum: Output only. Status of the function deployment.
    VpcConnectorEgressSettingsValueValuesEnum: The egress settings for the
      connector, controlling what traffic is diverted through it.

  Messages:
    BuildEnvironmentVariablesValue: Build environment variables that shall be
      available during build time.
    EnvironmentVariablesValue: Environment variables that shall be available
      during function execution.
    LabelsValue: Labels associated with this Cloud Function.

  Fields:
    automaticUpdatePolicy: A AutomaticUpdatePolicy attribute.
    availableMemoryMb: The amount of memory in MB available for a function.
      Defaults to 256MB.
    buildDockerfile: Local path to the dockerfile for customizing the base
      image for the builder, located within the source folder. Deprecated:
      this was significantly revised before reaching public.
    buildEnvironmentVariables: Build environment variables that shall be
      available during build time.
    buildId: Output only. The Cloud Build ID of the latest successful
      deployment of the function.
    buildName: Output only. The Cloud Build Name of the function deployment.
      `projects//locations//builds/`.
    buildServiceAccount: A service account the user provides for use with
      Cloud Build. The format of this field is
      `projects/{projectId}/serviceAccounts/{serviceAccountEmail}`.
    buildWorkerPool: Name of the Cloud Build Custom Worker Pool that should be
      used to build the function. The format of this field is
      `projects/{project}/locations/{region}/workerPools/{workerPool}` where
      `{project}` and `{region}` are the project id and region respectively
      where the worker pool is defined and `{workerPool}` is the short name of
      the worker pool. If the project id is not the same as the function, then
      the Cloud Functions Service Agent (`service-@gcf-admin-
      robot.iam.gserviceaccount.com`) must be granted the role Cloud Build
      Custom Workers Builder (`roles/cloudbuild.customworkers.builder`) in the
      project.
    buildpackStack: Specifies one of the Google provided buildpack stacks.
      Deprecated: this API was suspended after private preview and was never
      revealed publicly.
    customStackUri: Deprecated: customization experience was significantly
      revised, making this field obsolete. It was never revealed to public.
      The URL of a customer provided buildpack stack.
    description: User-provided description of a function.
    dockerRegistry: Docker Registry to use for this deployment. Deprecated: as
      of March 2025, `CONTAINER_REGISTRY` option is no longer available in
      response to Container Registry's deprecation:
      https://cloud.google.com/artifact-registry/docs/transition/transition-
      from-gcr Please use Artifact Registry instead, which is the default
      choice. If unspecified, it defaults to `ARTIFACT_REGISTRY`. If
      `docker_repository` field is specified, this field should either be left
      unspecified or set to `ARTIFACT_REGISTRY`.
    dockerRepository: User-managed repository created in Artifact Registry to
      which the function's Docker image will be pushed after it is built by
      Cloud Build. May optionally be encrypted with a customer-managed
      encryption key (CMEK). If unspecified and `docker_registry` is not
      explicitly set to `CONTAINER_REGISTRY`, GCF will create and use a
      default Artifact Registry repository named 'gcf-artifacts' in the
      region. It must match the pattern
      `projects/{project}/locations/{location}/repositories/{repository}`.
      Cross-project repositories are not supported. Cross-location
      repositories are not supported. Repository format must be 'DOCKER'.
    entryPoint: The name of the function (as defined in source code) that will
      be executed. Defaults to the resource name suffix (ID of the function),
      if not specified.
    environmentVariables: Environment variables that shall be available during
      function execution.
    eventTrigger: A source that fires events in response to a condition in
      another service.
    httpsTrigger: An HTTPS endpoint type of source that can be triggered via
      URL.
    ingressSettings: The ingress settings for the function, controlling what
      traffic can reach it.
    kmsKeyName: Resource name of a KMS crypto key (managed by the user) used
      to encrypt/decrypt function resources. It must match the pattern `projec
      ts/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto
      _key}`. If specified, you must also provide an artifact registry
      repository using the `docker_repository` field that was created with the
      same KMS crypto key. The following service accounts need to be granted
      the role 'Cloud KMS CryptoKey Encrypter/Decrypter
      (roles/cloudkms.cryptoKeyEncrypterDecrypter)' on the
      Key/KeyRing/Project/Organization (least access preferred). 1. Google
      Cloud Functions service account (service-{project_number}@gcf-admin-
      robot.iam.gserviceaccount.com) - Required to protect the function's
      image. 2. Google Storage service account (service-{project_number}@gs-
      project-accounts.iam.gserviceaccount.com) - Required to protect the
      function's source code. If this service account does not exist,
      deploying a function without a KMS key or retrieving the service agent
      name provisions it. For more information, see
      https://cloud.google.com/storage/docs/projects#service-agents and
      https://cloud.google.com/storage/docs/getting-service-agent#gsutil.
      Google Cloud Functions delegates access to service agents to protect
      function resources in internal projects that are not accessible by the
      end user.
    labels: Labels associated with this Cloud Function.
    maxInstances: The limit on the maximum number of function instances that
      may coexist at a given time. In some cases, such as rapid traffic
      surges, Cloud Functions may, for a short period of time, create more
      instances than the specified max instances limit. If your function
      cannot tolerate this temporary behavior, you may want to factor in a
      safety margin and set a lower max instances value than your function can
      tolerate. See the [Max
      Instances](https://cloud.google.com/functions/docs/max-instances) Guide
      for more details.
    minInstances: A lower bound for the number function instances that may
      coexist at a given time.
    name: A user-defined name of the function. Function names must be unique
      globally and match pattern `projects/*/locations/*/functions/*`
    network: Deprecated: use vpc_connector
    onDeployUpdatePolicy: A OnDeployUpdatePolicy attribute.
    pinnedRuntimeVersionPolicy: A PinnedRuntimeVersionPolicy attribute.
    runDockerfile: Local path to the dockerfile for customizing the base image
      for the worker, located within the source folder. Deprecated: this was
      significantly revised before reaching public.
    runtime: The runtime in which to run the function. Required when deploying
      a new function, optional when updating an existing function. For a
      complete list of possible choices, see the [`gcloud` command reference](
      https://cloud.google.com/sdk/gcloud/reference/functions/deploy#--
      runtime).
    satisfiesPzi: Output only.
    satisfiesPzs: Output only.
    secretEnvironmentVariables: Secret environment variables configuration.
    secretVolumes: Secret volumes configuration.
    serviceAccountEmail: The email of the function's service account. If
      empty, defaults to `{project_id}@appspot.gserviceaccount.com`.
    sourceArchiveUrl: The Google Cloud Storage URL, starting with `gs://`,
      pointing to the zip archive which contains the function.
    sourceRepository: **Beta Feature** The source repository where a function
      is hosted.
    sourceToken: Input only. An identifier for Firebase function sources.
      Disclaimer: This field is only supported for Firebase function
      deployments.
    sourceUploadUrl: The Google Cloud Storage signed URL used for source
      uploading, generated by calling
      [google.cloud.functions.v1.GenerateUploadUrl]. The signature is
      validated on write methods (Create, Update) The signature is stripped
      from the Function object on read methods (Get, List)
    status: Output only. Status of the function deployment.
    timeout: The function execution timeout. Execution is considered failed
      and can be terminated if the function is not completed at the end of the
      timeout period. Defaults to 60 seconds.
    updateTime: Output only. The last update timestamp of a Cloud Function.
    versionId: Output only. The version identifier of the Cloud Function. Each
      deployment attempt results in a new version of a function being created.
    vpcConnector: The VPC Network Connector that this cloud function can
      connect to. It can be either the fully-qualified URI, or the short name
      of the network connector resource. The format of this field is
      `projects/*/locations/*/connectors/*` This field is mutually exclusive
      with `network` field and will eventually replace it. See [the VPC
      documentation](https://cloud.google.com/compute/docs/vpc) for more
      information on connecting Cloud projects.
    vpcConnectorEgressSettings: The egress settings for the connector,
      controlling what traffic is diverted through it.
  """

  class DockerRegistryValueValuesEnum(_messages.Enum):
    r"""Docker Registry to use for this deployment. Deprecated: as of March
    2025, `CONTAINER_REGISTRY` option is no longer available in response to
    Container Registry's deprecation: https://cloud.google.com/artifact-
    registry/docs/transition/transition-from-gcr Please use Artifact Registry
    instead, which is the default choice. If unspecified, it defaults to
    `ARTIFACT_REGISTRY`. If `docker_repository` field is specified, this field
    should either be left unspecified or set to `ARTIFACT_REGISTRY`.

    Values:
      DOCKER_REGISTRY_UNSPECIFIED: Unspecified.
      CONTAINER_REGISTRY: Docker images will be stored in multi-regional
        Container Registry repositories named `gcf`.
      ARTIFACT_REGISTRY: Docker images will be stored in regional Artifact
        Registry repositories. By default, GCF will create and use
        repositories named `gcf-artifacts` in every region in which a function
        is deployed. But the repository to use can also be specified by the
        user using the `docker_repository` field.
    """
    DOCKER_REGISTRY_UNSPECIFIED = 0
    CONTAINER_REGISTRY = 1
    ARTIFACT_REGISTRY = 2

  class IngressSettingsValueValuesEnum(_messages.Enum):
    r"""The ingress settings for the function, controlling what traffic can
    reach it.

    Values:
      INGRESS_SETTINGS_UNSPECIFIED: Unspecified.
      ALLOW_ALL: Allow HTTP traffic from public and private sources.
      ALLOW_INTERNAL_ONLY: Allow HTTP traffic from only private VPC sources.
      ALLOW_INTERNAL_AND_GCLB: Allow HTTP traffic from private VPC sources and
        through GCLB.
    """
    INGRESS_SETTINGS_UNSPECIFIED = 0
    ALLOW_ALL = 1
    ALLOW_INTERNAL_ONLY = 2
    ALLOW_INTERNAL_AND_GCLB = 3

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the function deployment.

    Values:
      CLOUD_FUNCTION_STATUS_UNSPECIFIED: Not specified. Invalid state.
      ACTIVE: Function has been successfully deployed and is serving.
      OFFLINE: Function deployment failed and the function isn't serving.
      DEPLOY_IN_PROGRESS: Function is being created or updated.
      DELETE_IN_PROGRESS: Function is being deleted.
      UNKNOWN: Function deployment failed and the function serving state is
        undefined. The function should be updated or deleted to move it out of
        this state.
    """
    CLOUD_FUNCTION_STATUS_UNSPECIFIED = 0
    ACTIVE = 1
    OFFLINE = 2
    DEPLOY_IN_PROGRESS = 3
    DELETE_IN_PROGRESS = 4
    UNKNOWN = 5

  class VpcConnectorEgressSettingsValueValuesEnum(_messages.Enum):
    r"""The egress settings for the connector, controlling what traffic is
    diverted through it.

    Values:
      VPC_CONNECTOR_EGRESS_SETTINGS_UNSPECIFIED: Unspecified.
      PRIVATE_RANGES_ONLY: Use the VPC Access Connector only for private IP
        space from RFC1918.
      ALL_TRAFFIC: Force the use of VPC Access Connector for all egress
        traffic from the function.
    """
    VPC_CONNECTOR_EGRESS_SETTINGS_UNSPECIFIED = 0
    PRIVATE_RANGES_ONLY = 1
    ALL_TRAFFIC = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BuildEnvironmentVariablesValue(_messages.Message):
    r"""Build environment variables that shall be available during build time.

    Messages:
      AdditionalProperty: An additional property for a
        BuildEnvironmentVariablesValue object.

    Fields:
      additionalProperties: Additional properties of type
        BuildEnvironmentVariablesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BuildEnvironmentVariablesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentVariablesValue(_messages.Message):
    r"""Environment variables that shall be available during function
    execution.

    Messages:
      AdditionalProperty: An additional property for a
        EnvironmentVariablesValue object.

    Fields:
      additionalProperties: Additional properties of type
        EnvironmentVariablesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentVariablesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this Cloud Function.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  automaticUpdatePolicy = _messages.MessageField('AutomaticUpdatePolicy', 1)
  availableMemoryMb = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  buildDockerfile = _messages.StringField(3)
  buildEnvironmentVariables = _messages.MessageField('BuildEnvironmentVariablesValue', 4)
  buildId = _messages.StringField(5)
  buildName = _messages.StringField(6)
  buildServiceAccount = _messages.StringField(7)
  buildWorkerPool = _messages.StringField(8)
  buildpackStack = _messages.StringField(9)
  customStackUri = _messages.StringField(10)
  description = _messages.StringField(11)
  dockerRegistry = _messages.EnumField('DockerRegistryValueValuesEnum', 12)
  dockerRepository = _messages.StringField(13)
  entryPoint = _messages.StringField(14)
  environmentVariables = _messages.MessageField('EnvironmentVariablesValue', 15)
  eventTrigger = _messages.MessageField('EventTrigger', 16)
  httpsTrigger = _messages.MessageField('HttpsTrigger', 17)
  ingressSettings = _messages.EnumField('IngressSettingsValueValuesEnum', 18)
  kmsKeyName = _messages.StringField(19)
  labels = _messages.MessageField('LabelsValue', 20)
  maxInstances = _messages.IntegerField(21, variant=_messages.Variant.INT32)
  minInstances = _messages.IntegerField(22, variant=_messages.Variant.INT32)
  name = _messages.StringField(23)
  network = _messages.StringField(24)
  onDeployUpdatePolicy = _messages.MessageField('OnDeployUpdatePolicy', 25)
  pinnedRuntimeVersionPolicy = _messages.MessageField('PinnedRuntimeVersionPolicy', 26)
  runDockerfile = _messages.StringField(27)
  runtime = _messages.StringField(28)
  satisfiesPzi = _messages.BooleanField(29)
  satisfiesPzs = _messages.BooleanField(30)
  secretEnvironmentVariables = _messages.MessageField('SecretEnvVar', 31, repeated=True)
  secretVolumes = _messages.MessageField('SecretVolume', 32, repeated=True)
  serviceAccountEmail = _messages.StringField(33)
  sourceArchiveUrl = _messages.StringField(34)
  sourceRepository = _messages.MessageField('SourceRepository', 35)
  sourceToken = _messages.StringField(36)
  sourceUploadUrl = _messages.StringField(37)
  status = _messages.EnumField('StatusValueValuesEnum', 38)
  timeout = _messages.StringField(39)
  updateTime = _messages.StringField(40)
  versionId = _messages.IntegerField(41)
  vpcConnector = _messages.StringField(42)
  vpcConnectorEgressSettings = _messages.EnumField('VpcConnectorEgressSettingsValueValuesEnum', 43)


class CloudfunctionsOperationsGetRequest(_messages.Message):
  r"""A CloudfunctionsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class CloudfunctionsOperationsListRequest(_messages.Message):
  r"""A CloudfunctionsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class CloudfunctionsProjectsLocationsFunctionsCallRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsCallRequest object.

  Fields:
    callFunctionRequest: A CallFunctionRequest resource to be passed as the
      request body.
    name: Required. The name of the function to be called.
  """

  callFunctionRequest = _messages.MessageField('CallFunctionRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudfunctionsProjectsLocationsFunctionsCreateRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsCreateRequest object.

  Fields:
    cloudFunction: A CloudFunction resource to be passed as the request body.
    location: Required. The project and location in which the function should
      be created, specified in the format `projects/*/locations/*`
  """

  cloudFunction = _messages.MessageField('CloudFunction', 1)
  location = _messages.StringField(2, required=True)


class CloudfunctionsProjectsLocationsFunctionsDeleteRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsDeleteRequest object.

  Fields:
    name: Required. The name of the function which should be deleted.
  """

  name = _messages.StringField(1, required=True)


class CloudfunctionsProjectsLocationsFunctionsGenerateDownloadUrlRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsGenerateDownloadUrlRequest
  object.

  Fields:
    generateDownloadUrlRequest: A GenerateDownloadUrlRequest resource to be
      passed as the request body.
    name: The name of function for which source code Google Cloud Storage
      signed URL should be generated.
  """

  generateDownloadUrlRequest = _messages.MessageField('GenerateDownloadUrlRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudfunctionsProjectsLocationsFunctionsGenerateUploadUrlRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsGenerateUploadUrlRequest
  object.

  Fields:
    generateUploadUrlRequest: A GenerateUploadUrlRequest resource to be passed
      as the request body.
    parent: The project and location in which the Google Cloud Storage signed
      URL should be generated, specified in the format
      `projects/*/locations/*`.
  """

  generateUploadUrlRequest = _messages.MessageField('GenerateUploadUrlRequest', 1)
  parent = _messages.StringField(2, required=True)


class CloudfunctionsProjectsLocationsFunctionsGetIamPolicyRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class CloudfunctionsProjectsLocationsFunctionsGetRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsGetRequest object.

  Fields:
    name: Required. The name of the function which details should be obtained.
    versionId: Optional. The optional version of the function whose details
      should be obtained. The version of a 1st Gen function is an integer that
      starts from 1 and gets incremented on redeployments. Each deployment
      creates a config version of the underlying function. GCF may keep
      historical configs for old versions. This field can be specified to
      fetch the historical configs. Leave it blank or set to 0 to get the
      latest version of the function.
  """

  name = _messages.StringField(1, required=True)
  versionId = _messages.IntegerField(2)


class CloudfunctionsProjectsLocationsFunctionsListRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsListRequest object.

  Fields:
    pageSize: Maximum number of functions to return per call.
    pageToken: The value returned by the last `ListFunctionsResponse`;
      indicates that this is a continuation of a prior `ListFunctions` call,
      and that the system should return the next page of data.
    parent: The project and location from which the function should be listed,
      specified in the format `projects/*/locations/*` If you want to list
      functions in all locations, use "-" in place of a location. When listing
      functions in all locations, if one or more location(s) are unreachable,
      the response will contain functions from all reachable locations along
      with the names of any unreachable locations.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudfunctionsProjectsLocationsFunctionsPatchRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsPatchRequest object.

  Fields:
    cloudFunction: A CloudFunction resource to be passed as the request body.
    name: A user-defined name of the function. Function names must be unique
      globally and match pattern `projects/*/locations/*/functions/*`
    updateMask: Required. The list of fields in `CloudFunction` that have to
      be updated.
  """

  cloudFunction = _messages.MessageField('CloudFunction', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudfunctionsProjectsLocationsFunctionsSetIamPolicyRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class CloudfunctionsProjectsLocationsFunctionsTestIamPermissionsRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsFunctionsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class CloudfunctionsProjectsLocationsListRequest(_messages.Message):
  r"""A CloudfunctionsProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class EventTrigger(_messages.Message):
  r"""Describes EventTrigger, used to request events be sent from another
  service.

  Fields:
    eventType: Required. The type of event to observe. For example:
      `providers/cloud.storage/eventTypes/object.change` and
      `providers/cloud.pubsub/eventTypes/topic.publish`. Event types match
      pattern `providers/*/eventTypes/*.*`. The pattern contains: 1.
      namespace: For example, `cloud.storage` and `google.firebase.analytics`.
      2. resource type: The type of resource on which event occurs. For
      example, the Google Cloud Storage API includes the type `object`. 3.
      action: The action that generates the event. For example, action for a
      Google Cloud Storage Object is 'change'. These parts are lower case.
    failurePolicy: Specifies policy for failed executions.
    resource: Required. The resource(s) from which to observe events, for
      example, `projects/_/buckets/myBucket`. Not all syntactically correct
      values are accepted by all services. For example: 1. The authorization
      model must support it. Google Cloud Functions only allows EventTriggers
      to be deployed that observe resources in the same project as the
      `CloudFunction`. 2. The resource type must match the pattern expected
      for an `event_type`. For example, an `EventTrigger` that has an
      `event_type` of "google.pubsub.topic.publish" should have a resource
      that matches Google Cloud Pub/Sub topics. Additionally, some services
      may support short names when creating an `EventTrigger`. These will
      always be returned in the normalized "long" format. See each *service's*
      documentation for supported formats.
    service: The hostname of the service that should be observed. If no string
      is provided, the default service implementing the API will be used. For
      example, `storage.googleapis.com` is the default for all event types in
      the `google.storage` namespace.
  """

  eventType = _messages.StringField(1)
  failurePolicy = _messages.MessageField('FailurePolicy', 2)
  resource = _messages.StringField(3)
  service = _messages.StringField(4)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FailurePolicy(_messages.Message):
  r"""Describes the policy in case of function's execution failure. If empty,
  then defaults to ignoring failures (i.e. not retrying them).

  Fields:
    retry: If specified, then the function will be retried in case of a
      failure.
  """

  retry = _messages.MessageField('Retry', 1)


class GenerateDownloadUrlRequest(_messages.Message):
  r"""Request of `GenerateDownloadUrl` method.

  Fields:
    versionId: The optional version of function. If not set, default, current
      version is used.
  """

  versionId = _messages.IntegerField(1, variant=_messages.Variant.UINT64)


class GenerateDownloadUrlResponse(_messages.Message):
  r"""Response of `GenerateDownloadUrl` method.

  Fields:
    downloadUrl: The generated Google Cloud Storage signed URL that should be
      used for function source code download.
  """

  downloadUrl = _messages.StringField(1)


class GenerateUploadUrlRequest(_messages.Message):
  r"""Request of `GenerateSourceUploadUrl` method.

  Fields:
    kmsKeyName: Resource name of a KMS crypto key (managed by the user) used
      to encrypt/decrypt function source code objects in intermediate Cloud
      Storage buckets. When you generate an upload url and upload your source
      code, it gets copied to an intermediate Cloud Storage bucket. The source
      code is then copied to a versioned directory in the sources bucket in
      the consumer project during the function deployment. It must match the
      pattern `projects/{project}/locations/{location}/keyRings/{key_ring}/cry
      ptoKeys/{crypto_key}`. The Google Cloud Functions service account
      (service-{project_number}@gcf-admin-robot.iam.gserviceaccount.com) must
      be granted the role 'Cloud KMS CryptoKey Encrypter/Decrypter
      (roles/cloudkms.cryptoKeyEncrypterDecrypter)' on the
      Key/KeyRing/Project/Organization (least access preferred). GCF will
      delegate access to the Google Storage service account in the internal
      project.
  """

  kmsKeyName = _messages.StringField(1)


class GenerateUploadUrlResponse(_messages.Message):
  r"""Response of `GenerateSourceUploadUrl` method.

  Fields:
    uploadUrl: The generated Google Cloud Storage signed URL that should be
      used for a function source code upload. The uploaded file should be a
      zip archive which contains a function.
  """

  uploadUrl = _messages.StringField(1)


class HttpsTrigger(_messages.Message):
  r"""Describes HttpsTrigger, could be used to connect web hooks to function.

  Enums:
    SecurityLevelValueValuesEnum: The security level for the function.

  Fields:
    securityLevel: The security level for the function.
    url: Output only. The deployed url for the function.
  """

  class SecurityLevelValueValuesEnum(_messages.Enum):
    r"""The security level for the function.

    Values:
      SECURITY_LEVEL_UNSPECIFIED: Unspecified.
      SECURE_ALWAYS: Requests for a URL that match this handler that do not
        use HTTPS are automatically redirected to the HTTPS URL with the same
        path. Query parameters are reserved for the redirect.
      SECURE_OPTIONAL: Both HTTP and HTTPS requests with URLs that match the
        handler succeed without redirects. The application can examine the
        request to determine which protocol was used and respond accordingly.
    """
    SECURITY_LEVEL_UNSPECIFIED = 0
    SECURE_ALWAYS = 1
    SECURE_OPTIONAL = 2

  securityLevel = _messages.EnumField('SecurityLevelValueValuesEnum', 1)
  url = _messages.StringField(2)


class ListFunctionsResponse(_messages.Message):
  r"""Response for the `ListFunctions` method.

  Fields:
    functions: The functions that match the request.
    nextPageToken: If not empty, indicates that there may be more functions
      that match the request; this value should be passed in a new
      google.cloud.functions.v1.ListFunctionsRequest to get more functions.
    unreachable: Locations that could not be reached. The response does not
      include any functions from these locations.
  """

  functions = _messages.MessageField('CloudFunction', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class OnDeployUpdatePolicy(_messages.Message):
  r"""Security patches are only applied when a function is redeployed.

  Fields:
    runtimeVersion: Output only. Contains the runtime version which was used
      during latest function deployment.
  """

  runtimeVersion = _messages.StringField(1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadataV1(_messages.Message):
  r"""Metadata describing an Operation

  Enums:
    TypeValueValuesEnum: Type of operation.

  Messages:
    RequestValue: The original request that started the operation.

  Fields:
    buildId: The Cloud Build ID of the function created or updated by an API
      call. This field is only populated for Create and Update operations.
    buildName: The Cloud Build Name of the function deployment. This field is
      only populated for Create and Update operations.
      `projects//locations//builds/`.
    request: The original request that started the operation.
    sourceToken: An identifier for Firebase function sources. Disclaimer: This
      field is only supported for Firebase function deployments.
    target: Target of the operation - for example
      `projects/project-1/locations/region-1/functions/function-1`
    type: Type of operation.
    updateTime: The last update timestamp of the operation.
    versionId: Version id of the function created or updated by an API call.
      This field is only populated for Create and Update operations.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of operation.

    Values:
      OPERATION_UNSPECIFIED: Unknown operation type.
      CREATE_FUNCTION: Triggered by CreateFunction call
      UPDATE_FUNCTION: Triggered by UpdateFunction call
      DELETE_FUNCTION: Triggered by DeleteFunction call.
    """
    OPERATION_UNSPECIFIED = 0
    CREATE_FUNCTION = 1
    UPDATE_FUNCTION = 2
    DELETE_FUNCTION = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RequestValue(_messages.Message):
    r"""The original request that started the operation.

    Messages:
      AdditionalProperty: An additional property for a RequestValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RequestValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  buildId = _messages.StringField(1)
  buildName = _messages.StringField(2)
  request = _messages.MessageField('RequestValue', 3)
  sourceToken = _messages.StringField(4)
  target = _messages.StringField(5)
  type = _messages.EnumField('TypeValueValuesEnum', 6)
  updateTime = _messages.StringField(7)
  versionId = _messages.IntegerField(8)


class PinnedRuntimeVersionPolicy(_messages.Message):
  r"""The function is pinned to a specific runtime version and it will not
  receive security patches, even after redeploying.

  Fields:
    runtimeVersion: The runtime version this function is pinned to. This
      version will be used every time this function is deployed.
  """

  runtimeVersion = _messages.StringField(1)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Retry(_messages.Message):
  r"""Describes the retry policy in case of function's execution failure. A
  function execution will be retried on any failure. A failed execution will
  be retried up to 7 days with an exponential backoff (capped at 10 seconds).
  Retried execution is charged as any other execution.
  """



class SecretEnvVar(_messages.Message):
  r"""Configuration for a secret environment variable. It has the information
  necessary to fetch the secret value from secret manager and expose it as an
  environment variable.

  Fields:
    key: Name of the environment variable.
    projectId: Project identifier (preferably project number but can also be
      the project ID) of the project that contains the secret. If not set, it
      will be populated with the function's project assuming that the secret
      exists in the same project as of the function.
    secret: Name of the secret in secret manager (not the full resource name).
    version: Version of the secret (version number or the string 'latest'). It
      is recommended to use a numeric version for secret environment variables
      as any updates to the secret value is not reflected until new instances
      start.
  """

  key = _messages.StringField(1)
  projectId = _messages.StringField(2)
  secret = _messages.StringField(3)
  version = _messages.StringField(4)


class SecretVersion(_messages.Message):
  r"""Configuration for a single version.

  Fields:
    path: Relative path of the file under the mount path where the secret
      value for this version will be fetched and made available. For example,
      setting the mount_path as '/etc/secrets' and path as `/secret_foo` would
      mount the secret value file at `/etc/secrets/secret_foo`.
    version: Version of the secret (version number or the string 'latest'). It
      is preferable to use `latest` version with secret volumes as secret
      value changes are reflected immediately.
  """

  path = _messages.StringField(1)
  version = _messages.StringField(2)


class SecretVolume(_messages.Message):
  r"""Configuration for a secret volume. It has the information necessary to
  fetch the secret value from secret manager and make it available as files
  mounted at the requested paths within the application container. Secret
  value is not a part of the configuration. Every filesystem read operation
  performs a lookup in secret manager to retrieve the secret value.

  Fields:
    mountPath: The path within the container to mount the secret volume. For
      example, setting the mount_path as `/etc/secrets` would mount the secret
      value files under the `/etc/secrets` directory. This directory will also
      be completely shadowed and unavailable to mount any other secrets.
      Recommended mount paths: /etc/secrets Restricted mount paths: /cloudsql,
      /dev/log, /pod, /proc, /var/log
    projectId: Project identifier (preferrably project number but can also be
      the project ID) of the project that contains the secret. If not set, it
      will be populated with the function's project assuming that the secret
      exists in the same project as of the function.
    secret: Name of the secret in secret manager (not the full resource name).
    versions: List of secret versions to mount for this secret. If empty, the
      `latest` version of the secret will be made available in a file named
      after the secret under the mount point.
  """

  mountPath = _messages.StringField(1)
  projectId = _messages.StringField(2)
  secret = _messages.StringField(3)
  versions = _messages.MessageField('SecretVersion', 4, repeated=True)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SourceRepository(_messages.Message):
  r"""Describes SourceRepository, used to represent parameters related to
  source repository where a function is hosted.

  Fields:
    deployedUrl: Output only. The URL pointing to the hosted repository where
      the function were defined at the time of deployment. It always points to
      a specific commit in the format described above.
    url: The URL pointing to the hosted repository where the function is
      defined. There are supported Cloud Source Repository URLs in the
      following formats: To refer to a specific commit: `https://source.develo
      pers.google.com/projects/*/repos/*/revisions/*/paths/*` To refer to a
      moveable alias (branch):
      `https://source.developers.google.com/projects/*/repos/*/moveable-
      aliases/*/paths/*` In particular, to refer to HEAD use `master` moveable
      alias. To refer to a specific fixed alias (tag):
      `https://source.developers.google.com/projects/*/repos/*/fixed-
      aliases/*/paths/*` You may omit `paths/*` if you want to use the main
      directory. The function response may add an empty `/paths/` to the URL.
  """

  deployedUrl = _messages.StringField(1)
  url = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    CloudfunctionsProjectsLocationsFunctionsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
