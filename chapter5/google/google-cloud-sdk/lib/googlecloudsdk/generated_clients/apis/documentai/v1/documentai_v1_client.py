"""Generated client library for documentai version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.documentai.v1 import documentai_v1_messages as messages


class DocumentaiV1(base_api.BaseApiClient):
  """Generated client library for service documentai version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://documentai.googleapis.com/'
  MTLS_BASE_URL = 'https://documentai.mtls.googleapis.com/'

  _PACKAGE = 'documentai'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'DocumentaiV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new documentai handle."""
    url = url or self.BASE_URL
    super(DocumentaiV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.operations = self.OperationsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_processorTypes = self.ProjectsLocationsProcessorTypesService(self)
    self.projects_locations_processors_humanReviewConfig = self.ProjectsLocationsProcessorsHumanReviewConfigService(self)
    self.projects_locations_processors_processorVersions_evaluations = self.ProjectsLocationsProcessorsProcessorVersionsEvaluationsService(self)
    self.projects_locations_processors_processorVersions = self.ProjectsLocationsProcessorsProcessorVersionsService(self)
    self.projects_locations_processors = self.ProjectsLocationsProcessorsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects_operations = self.ProjectsOperationsService(self)
    self.projects = self.ProjectsService(self)

  class OperationsService(base_api.BaseApiService):
    """Service class for the operations resource."""

    _NAME = 'operations'

    def __init__(self, client):
      super(DocumentaiV1.OperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (DocumentaiOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/operations/{operationsId}',
        http_method='DELETE',
        method_id='documentai.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DocumentaiOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(DocumentaiV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (DocumentaiProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='documentai.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (DocumentaiProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='documentai.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (DocumentaiProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='documentai.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsProcessorTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_processorTypes resource."""

    _NAME = 'projects_locations_processorTypes'

    def __init__(self, client):
      super(DocumentaiV1.ProjectsLocationsProcessorTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a processor type detail.

      Args:
        request: (DocumentaiProjectsLocationsProcessorTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDocumentaiV1ProcessorType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processorTypes/{processorTypesId}',
        http_method='GET',
        method_id='documentai.projects.locations.processorTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsProcessorTypesGetRequest',
        response_type_name='GoogleCloudDocumentaiV1ProcessorType',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the processor types that exist.

      Args:
        request: (DocumentaiProjectsLocationsProcessorTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDocumentaiV1ListProcessorTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processorTypes',
        http_method='GET',
        method_id='documentai.projects.locations.processorTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/processorTypes',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsProcessorTypesListRequest',
        response_type_name='GoogleCloudDocumentaiV1ListProcessorTypesResponse',
        supports_download=False,
    )

  class ProjectsLocationsProcessorsHumanReviewConfigService(base_api.BaseApiService):
    """Service class for the projects_locations_processors_humanReviewConfig resource."""

    _NAME = 'projects_locations_processors_humanReviewConfig'

    def __init__(self, client):
      super(DocumentaiV1.ProjectsLocationsProcessorsHumanReviewConfigService, self).__init__(client)
      self._upload_configs = {
          }

    def ReviewDocument(self, request, global_params=None):
      r"""Send a document for Human Review. The input document should be processed by the specified processor.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsHumanReviewConfigReviewDocumentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ReviewDocument')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReviewDocument.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}/humanReviewConfig:reviewDocument',
        http_method='POST',
        method_id='documentai.projects.locations.processors.humanReviewConfig.reviewDocument',
        ordered_params=['humanReviewConfig'],
        path_params=['humanReviewConfig'],
        query_params=[],
        relative_path='v1/{+humanReviewConfig}:reviewDocument',
        request_field='googleCloudDocumentaiV1ReviewDocumentRequest',
        request_type_name='DocumentaiProjectsLocationsProcessorsHumanReviewConfigReviewDocumentRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsProcessorsProcessorVersionsEvaluationsService(base_api.BaseApiService):
    """Service class for the projects_locations_processors_processorVersions_evaluations resource."""

    _NAME = 'projects_locations_processors_processorVersions_evaluations'

    def __init__(self, client):
      super(DocumentaiV1.ProjectsLocationsProcessorsProcessorVersionsEvaluationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves a specific evaluation.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsProcessorVersionsEvaluationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDocumentaiV1Evaluation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}/processorVersions/{processorVersionsId}/evaluations/{evaluationsId}',
        http_method='GET',
        method_id='documentai.projects.locations.processors.processorVersions.evaluations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsProcessorsProcessorVersionsEvaluationsGetRequest',
        response_type_name='GoogleCloudDocumentaiV1Evaluation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieves a set of evaluations for a given processor version.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsProcessorVersionsEvaluationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDocumentaiV1ListEvaluationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}/processorVersions/{processorVersionsId}/evaluations',
        http_method='GET',
        method_id='documentai.projects.locations.processors.processorVersions.evaluations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/evaluations',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsProcessorsProcessorVersionsEvaluationsListRequest',
        response_type_name='GoogleCloudDocumentaiV1ListEvaluationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsProcessorsProcessorVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_processors_processorVersions resource."""

    _NAME = 'projects_locations_processors_processorVersions'

    def __init__(self, client):
      super(DocumentaiV1.ProjectsLocationsProcessorsProcessorVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchProcess(self, request, global_params=None):
      r"""LRO endpoint to batch process many documents. The output is written to Cloud Storage as JSON in the [Document] format.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsProcessorVersionsBatchProcessRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchProcess')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchProcess.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}/processorVersions/{processorVersionsId}:batchProcess',
        http_method='POST',
        method_id='documentai.projects.locations.processors.processorVersions.batchProcess',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:batchProcess',
        request_field='googleCloudDocumentaiV1BatchProcessRequest',
        request_type_name='DocumentaiProjectsLocationsProcessorsProcessorVersionsBatchProcessRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the processor version, all artifacts under the processor version will be deleted.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsProcessorVersionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}/processorVersions/{processorVersionsId}',
        http_method='DELETE',
        method_id='documentai.projects.locations.processors.processorVersions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsProcessorsProcessorVersionsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Deploy(self, request, global_params=None):
      r"""Deploys the processor version.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsProcessorVersionsDeployRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Deploy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deploy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}/processorVersions/{processorVersionsId}:deploy',
        http_method='POST',
        method_id='documentai.projects.locations.processors.processorVersions.deploy',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:deploy',
        request_field='googleCloudDocumentaiV1DeployProcessorVersionRequest',
        request_type_name='DocumentaiProjectsLocationsProcessorsProcessorVersionsDeployRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def EvaluateProcessorVersion(self, request, global_params=None):
      r"""Evaluates a ProcessorVersion against annotated documents, producing an Evaluation.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsProcessorVersionsEvaluateProcessorVersionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('EvaluateProcessorVersion')
      return self._RunMethod(
          config, request, global_params=global_params)

    EvaluateProcessorVersion.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}/processorVersions/{processorVersionsId}:evaluateProcessorVersion',
        http_method='POST',
        method_id='documentai.projects.locations.processors.processorVersions.evaluateProcessorVersion',
        ordered_params=['processorVersion'],
        path_params=['processorVersion'],
        query_params=[],
        relative_path='v1/{+processorVersion}:evaluateProcessorVersion',
        request_field='googleCloudDocumentaiV1EvaluateProcessorVersionRequest',
        request_type_name='DocumentaiProjectsLocationsProcessorsProcessorVersionsEvaluateProcessorVersionRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a processor version detail.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsProcessorVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDocumentaiV1ProcessorVersion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}/processorVersions/{processorVersionsId}',
        http_method='GET',
        method_id='documentai.projects.locations.processors.processorVersions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsProcessorsProcessorVersionsGetRequest',
        response_type_name='GoogleCloudDocumentaiV1ProcessorVersion',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all versions of a processor.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsProcessorVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDocumentaiV1ListProcessorVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}/processorVersions',
        http_method='GET',
        method_id='documentai.projects.locations.processors.processorVersions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/processorVersions',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsProcessorsProcessorVersionsListRequest',
        response_type_name='GoogleCloudDocumentaiV1ListProcessorVersionsResponse',
        supports_download=False,
    )

    def Process(self, request, global_params=None):
      r"""Processes a single document.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsProcessorVersionsProcessRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDocumentaiV1ProcessResponse) The response message.
      """
      config = self.GetMethodConfig('Process')
      return self._RunMethod(
          config, request, global_params=global_params)

    Process.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}/processorVersions/{processorVersionsId}:process',
        http_method='POST',
        method_id='documentai.projects.locations.processors.processorVersions.process',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:process',
        request_field='googleCloudDocumentaiV1ProcessRequest',
        request_type_name='DocumentaiProjectsLocationsProcessorsProcessorVersionsProcessRequest',
        response_type_name='GoogleCloudDocumentaiV1ProcessResponse',
        supports_download=False,
    )

    def Train(self, request, global_params=None):
      r"""Trains a new processor version. Operation metadata is returned as TrainProcessorVersionMetadata.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsProcessorVersionsTrainRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Train')
      return self._RunMethod(
          config, request, global_params=global_params)

    Train.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}/processorVersions:train',
        http_method='POST',
        method_id='documentai.projects.locations.processors.processorVersions.train',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/processorVersions:train',
        request_field='googleCloudDocumentaiV1TrainProcessorVersionRequest',
        request_type_name='DocumentaiProjectsLocationsProcessorsProcessorVersionsTrainRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Undeploy(self, request, global_params=None):
      r"""Undeploys the processor version.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsProcessorVersionsUndeployRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Undeploy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undeploy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}/processorVersions/{processorVersionsId}:undeploy',
        http_method='POST',
        method_id='documentai.projects.locations.processors.processorVersions.undeploy',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undeploy',
        request_field='googleCloudDocumentaiV1UndeployProcessorVersionRequest',
        request_type_name='DocumentaiProjectsLocationsProcessorsProcessorVersionsUndeployRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsProcessorsService(base_api.BaseApiService):
    """Service class for the projects_locations_processors resource."""

    _NAME = 'projects_locations_processors'

    def __init__(self, client):
      super(DocumentaiV1.ProjectsLocationsProcessorsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchProcess(self, request, global_params=None):
      r"""LRO endpoint to batch process many documents. The output is written to Cloud Storage as JSON in the [Document] format.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsBatchProcessRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchProcess')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchProcess.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}:batchProcess',
        http_method='POST',
        method_id='documentai.projects.locations.processors.batchProcess',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:batchProcess',
        request_field='googleCloudDocumentaiV1BatchProcessRequest',
        request_type_name='DocumentaiProjectsLocationsProcessorsBatchProcessRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a processor from the ProcessorType provided. The processor will be at `ENABLED` state by default after its creation. Note that this method requires the `documentai.processors.create` permission on the project, which is highly privileged. A user or service account with this permission can create new processors that can interact with any gcs bucket in your project.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDocumentaiV1Processor) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors',
        http_method='POST',
        method_id='documentai.projects.locations.processors.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/processors',
        request_field='googleCloudDocumentaiV1Processor',
        request_type_name='DocumentaiProjectsLocationsProcessorsCreateRequest',
        response_type_name='GoogleCloudDocumentaiV1Processor',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the processor, unloads all deployed model artifacts if it was enabled and then deletes all artifacts associated with this processor.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}',
        http_method='DELETE',
        method_id='documentai.projects.locations.processors.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsProcessorsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Disable(self, request, global_params=None):
      r"""Disables a processor.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsDisableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Disable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Disable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}:disable',
        http_method='POST',
        method_id='documentai.projects.locations.processors.disable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:disable',
        request_field='googleCloudDocumentaiV1DisableProcessorRequest',
        request_type_name='DocumentaiProjectsLocationsProcessorsDisableRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Enable(self, request, global_params=None):
      r"""Enables a processor.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsEnableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Enable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}:enable',
        http_method='POST',
        method_id='documentai.projects.locations.processors.enable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:enable',
        request_field='googleCloudDocumentaiV1EnableProcessorRequest',
        request_type_name='DocumentaiProjectsLocationsProcessorsEnableRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a processor detail.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDocumentaiV1Processor) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}',
        http_method='GET',
        method_id='documentai.projects.locations.processors.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsProcessorsGetRequest',
        response_type_name='GoogleCloudDocumentaiV1Processor',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all processors which belong to this project.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDocumentaiV1ListProcessorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors',
        http_method='GET',
        method_id='documentai.projects.locations.processors.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/processors',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsProcessorsListRequest',
        response_type_name='GoogleCloudDocumentaiV1ListProcessorsResponse',
        supports_download=False,
    )

    def Process(self, request, global_params=None):
      r"""Processes a single document.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsProcessRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDocumentaiV1ProcessResponse) The response message.
      """
      config = self.GetMethodConfig('Process')
      return self._RunMethod(
          config, request, global_params=global_params)

    Process.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}:process',
        http_method='POST',
        method_id='documentai.projects.locations.processors.process',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:process',
        request_field='googleCloudDocumentaiV1ProcessRequest',
        request_type_name='DocumentaiProjectsLocationsProcessorsProcessRequest',
        response_type_name='GoogleCloudDocumentaiV1ProcessResponse',
        supports_download=False,
    )

    def SetDefaultProcessorVersion(self, request, global_params=None):
      r"""Set the default (active) version of a Processor that will be used in ProcessDocument and BatchProcessDocuments.

      Args:
        request: (DocumentaiProjectsLocationsProcessorsSetDefaultProcessorVersionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('SetDefaultProcessorVersion')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetDefaultProcessorVersion.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/processors/{processorsId}:setDefaultProcessorVersion',
        http_method='POST',
        method_id='documentai.projects.locations.processors.setDefaultProcessorVersion',
        ordered_params=['processor'],
        path_params=['processor'],
        query_params=[],
        relative_path='v1/{+processor}:setDefaultProcessorVersion',
        request_field='googleCloudDocumentaiV1SetDefaultProcessorVersionRequest',
        request_type_name='DocumentaiProjectsLocationsProcessorsSetDefaultProcessorVersionRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(DocumentaiV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def FetchProcessorTypes(self, request, global_params=None):
      r"""Fetches processor types. Note that we don't use ListProcessorTypes here, because it isn't paginated.

      Args:
        request: (DocumentaiProjectsLocationsFetchProcessorTypesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDocumentaiV1FetchProcessorTypesResponse) The response message.
      """
      config = self.GetMethodConfig('FetchProcessorTypes')
      return self._RunMethod(
          config, request, global_params=global_params)

    FetchProcessorTypes.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}:fetchProcessorTypes',
        http_method='GET',
        method_id='documentai.projects.locations.fetchProcessorTypes',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:fetchProcessorTypes',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsFetchProcessorTypesRequest',
        response_type_name='GoogleCloudDocumentaiV1FetchProcessorTypesResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (DocumentaiProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationLocation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='documentai.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsGetRequest',
        response_type_name='GoogleCloudLocationLocation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (DocumentaiProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='documentai.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='DocumentaiProjectsLocationsListRequest',
        response_type_name='GoogleCloudLocationListLocationsResponse',
        supports_download=False,
    )

  class ProjectsOperationsService(base_api.BaseApiService):
    """Service class for the projects_operations resource."""

    _NAME = 'projects_operations'

    def __init__(self, client):
      super(DocumentaiV1.ProjectsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (DocumentaiProjectsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/operations/{operationsId}',
        http_method='GET',
        method_id='documentai.projects.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DocumentaiProjectsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(DocumentaiV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
