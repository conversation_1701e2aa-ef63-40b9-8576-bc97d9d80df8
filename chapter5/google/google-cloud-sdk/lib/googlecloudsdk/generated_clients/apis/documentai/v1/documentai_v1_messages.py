"""Generated message classes for documentai version v1.

Service to parse structured information from unstructured or semi-structured
documents using state-of-the-art Google AI such as natural language, computer
vision, translation, and AutoML.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'documentai'


class DocumentaiOperationsDeleteRequest(_messages.Message):
  r"""A DocumentaiOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class DocumentaiProjectsLocationsFetchProcessorTypesRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsFetchProcessorTypesRequest object.

  Fields:
    parent: Required. The location of processor types to list. Format:
      `projects/{project}/locations/{location}`.
  """

  parent = _messages.StringField(1, required=True)


class DocumentaiProjectsLocationsGetRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class DocumentaiProjectsLocationsListRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class DocumentaiProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class DocumentaiProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class DocumentaiProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class DocumentaiProjectsLocationsProcessorTypesGetRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorTypesGetRequest object.

  Fields:
    name: Required. The processor type resource name.
  """

  name = _messages.StringField(1, required=True)


class DocumentaiProjectsLocationsProcessorTypesListRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorTypesListRequest object.

  Fields:
    pageSize: The maximum number of processor types to return. If unspecified,
      at most `100` processor types will be returned. The maximum value is
      `500`. Values above `500` will be coerced to `500`.
    pageToken: Used to retrieve the next page of results, empty if at the end
      of the list.
    parent: Required. The location of processor types to list. Format:
      `projects/{project}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DocumentaiProjectsLocationsProcessorsBatchProcessRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsBatchProcessRequest object.

  Fields:
    googleCloudDocumentaiV1BatchProcessRequest: A
      GoogleCloudDocumentaiV1BatchProcessRequest resource to be passed as the
      request body.
    name: Required. The resource name of Processor or ProcessorVersion.
      Format:
      `projects/{project}/locations/{location}/processors/{processor}`, or `pr
      ojects/{project}/locations/{location}/processors/{processor}/processorVe
      rsions/{processorVersion}`
  """

  googleCloudDocumentaiV1BatchProcessRequest = _messages.MessageField('GoogleCloudDocumentaiV1BatchProcessRequest', 1)
  name = _messages.StringField(2, required=True)


class DocumentaiProjectsLocationsProcessorsCreateRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsCreateRequest object.

  Fields:
    googleCloudDocumentaiV1Processor: A GoogleCloudDocumentaiV1Processor
      resource to be passed as the request body.
    parent: Required. The parent (project and location) under which to create
      the processor. Format: `projects/{project}/locations/{location}`
  """

  googleCloudDocumentaiV1Processor = _messages.MessageField('GoogleCloudDocumentaiV1Processor', 1)
  parent = _messages.StringField(2, required=True)


class DocumentaiProjectsLocationsProcessorsDeleteRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsDeleteRequest object.

  Fields:
    name: Required. The processor resource name to be deleted.
  """

  name = _messages.StringField(1, required=True)


class DocumentaiProjectsLocationsProcessorsDisableRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsDisableRequest object.

  Fields:
    googleCloudDocumentaiV1DisableProcessorRequest: A
      GoogleCloudDocumentaiV1DisableProcessorRequest resource to be passed as
      the request body.
    name: Required. The processor resource name to be disabled.
  """

  googleCloudDocumentaiV1DisableProcessorRequest = _messages.MessageField('GoogleCloudDocumentaiV1DisableProcessorRequest', 1)
  name = _messages.StringField(2, required=True)


class DocumentaiProjectsLocationsProcessorsEnableRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsEnableRequest object.

  Fields:
    googleCloudDocumentaiV1EnableProcessorRequest: A
      GoogleCloudDocumentaiV1EnableProcessorRequest resource to be passed as
      the request body.
    name: Required. The processor resource name to be enabled.
  """

  googleCloudDocumentaiV1EnableProcessorRequest = _messages.MessageField('GoogleCloudDocumentaiV1EnableProcessorRequest', 1)
  name = _messages.StringField(2, required=True)


class DocumentaiProjectsLocationsProcessorsGetRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsGetRequest object.

  Fields:
    name: Required. The processor resource name.
  """

  name = _messages.StringField(1, required=True)


class DocumentaiProjectsLocationsProcessorsHumanReviewConfigReviewDocumentRequest(_messages.Message):
  r"""A
  DocumentaiProjectsLocationsProcessorsHumanReviewConfigReviewDocumentRequest
  object.

  Fields:
    googleCloudDocumentaiV1ReviewDocumentRequest: A
      GoogleCloudDocumentaiV1ReviewDocumentRequest resource to be passed as
      the request body.
    humanReviewConfig: Required. The resource name of the HumanReviewConfig
      that the document will be reviewed with.
  """

  googleCloudDocumentaiV1ReviewDocumentRequest = _messages.MessageField('GoogleCloudDocumentaiV1ReviewDocumentRequest', 1)
  humanReviewConfig = _messages.StringField(2, required=True)


class DocumentaiProjectsLocationsProcessorsListRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsListRequest object.

  Fields:
    pageSize: The maximum number of processors to return. If unspecified, at
      most `50` processors will be returned. The maximum value is `100`.
      Values above `100` will be coerced to `100`.
    pageToken: We will return the processors sorted by creation time. The page
      token will point to the next processor.
    parent: Required. The parent (project and location) which owns this
      collection of Processors. Format:
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DocumentaiProjectsLocationsProcessorsProcessRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsProcessRequest object.

  Fields:
    googleCloudDocumentaiV1ProcessRequest: A
      GoogleCloudDocumentaiV1ProcessRequest resource to be passed as the
      request body.
    name: Required. The resource name of the Processor or ProcessorVersion to
      use for processing. If a Processor is specified, the server will use its
      default version. Format:
      `projects/{project}/locations/{location}/processors/{processor}`, or `pr
      ojects/{project}/locations/{location}/processors/{processor}/processorVe
      rsions/{processorVersion}`
  """

  googleCloudDocumentaiV1ProcessRequest = _messages.MessageField('GoogleCloudDocumentaiV1ProcessRequest', 1)
  name = _messages.StringField(2, required=True)


class DocumentaiProjectsLocationsProcessorsProcessorVersionsBatchProcessRequest(_messages.Message):
  r"""A
  DocumentaiProjectsLocationsProcessorsProcessorVersionsBatchProcessRequest
  object.

  Fields:
    googleCloudDocumentaiV1BatchProcessRequest: A
      GoogleCloudDocumentaiV1BatchProcessRequest resource to be passed as the
      request body.
    name: Required. The resource name of Processor or ProcessorVersion.
      Format:
      `projects/{project}/locations/{location}/processors/{processor}`, or `pr
      ojects/{project}/locations/{location}/processors/{processor}/processorVe
      rsions/{processorVersion}`
  """

  googleCloudDocumentaiV1BatchProcessRequest = _messages.MessageField('GoogleCloudDocumentaiV1BatchProcessRequest', 1)
  name = _messages.StringField(2, required=True)


class DocumentaiProjectsLocationsProcessorsProcessorVersionsDeleteRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsProcessorVersionsDeleteRequest
  object.

  Fields:
    name: Required. The processor version resource name to be deleted.
  """

  name = _messages.StringField(1, required=True)


class DocumentaiProjectsLocationsProcessorsProcessorVersionsDeployRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsProcessorVersionsDeployRequest
  object.

  Fields:
    googleCloudDocumentaiV1DeployProcessorVersionRequest: A
      GoogleCloudDocumentaiV1DeployProcessorVersionRequest resource to be
      passed as the request body.
    name: Required. The processor version resource name to be deployed.
  """

  googleCloudDocumentaiV1DeployProcessorVersionRequest = _messages.MessageField('GoogleCloudDocumentaiV1DeployProcessorVersionRequest', 1)
  name = _messages.StringField(2, required=True)


class DocumentaiProjectsLocationsProcessorsProcessorVersionsEvaluateProcessorVersionRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsProcessorVersionsEvaluateProcesso
  rVersionRequest object.

  Fields:
    googleCloudDocumentaiV1EvaluateProcessorVersionRequest: A
      GoogleCloudDocumentaiV1EvaluateProcessorVersionRequest resource to be
      passed as the request body.
    processorVersion: Required. The resource name of the ProcessorVersion to
      evaluate. `projects/{project}/locations/{location}/processors/{processor
      }/processorVersions/{processorVersion}`
  """

  googleCloudDocumentaiV1EvaluateProcessorVersionRequest = _messages.MessageField('GoogleCloudDocumentaiV1EvaluateProcessorVersionRequest', 1)
  processorVersion = _messages.StringField(2, required=True)


class DocumentaiProjectsLocationsProcessorsProcessorVersionsEvaluationsGetRequest(_messages.Message):
  r"""A
  DocumentaiProjectsLocationsProcessorsProcessorVersionsEvaluationsGetRequest
  object.

  Fields:
    name: Required. The resource name of the Evaluation to get. `projects/{pro
      ject}/locations/{location}/processors/{processor}/processorVersions/{pro
      cessorVersion}/evaluations/{evaluation}`
  """

  name = _messages.StringField(1, required=True)


class DocumentaiProjectsLocationsProcessorsProcessorVersionsEvaluationsListRequest(_messages.Message):
  r"""A
  DocumentaiProjectsLocationsProcessorsProcessorVersionsEvaluationsListRequest
  object.

  Fields:
    pageSize: The standard list page size. If unspecified, at most `5`
      evaluations are returned. The maximum value is `100`. Values above `100`
      are coerced to `100`.
    pageToken: A page token, received from a previous `ListEvaluations` call.
      Provide this to retrieve the subsequent page.
    parent: Required. The resource name of the ProcessorVersion to list
      evaluations for. `projects/{project}/locations/{location}/processors/{pr
      ocessor}/processorVersions/{processorVersion}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DocumentaiProjectsLocationsProcessorsProcessorVersionsGetRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsProcessorVersionsGetRequest
  object.

  Fields:
    name: Required. The processor resource name.
  """

  name = _messages.StringField(1, required=True)


class DocumentaiProjectsLocationsProcessorsProcessorVersionsListRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsProcessorVersionsListRequest
  object.

  Fields:
    pageSize: The maximum number of processor versions to return. If
      unspecified, at most `10` processor versions will be returned. The
      maximum value is `20`. Values above `20` will be coerced to `20`.
    pageToken: We will return the processor versions sorted by creation time.
      The page token will point to the next processor version.
    parent: Required. The parent (project, location and processor) to list all
      versions. Format:
      `projects/{project}/locations/{location}/processors/{processor}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DocumentaiProjectsLocationsProcessorsProcessorVersionsProcessRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsProcessorVersionsProcessRequest
  object.

  Fields:
    googleCloudDocumentaiV1ProcessRequest: A
      GoogleCloudDocumentaiV1ProcessRequest resource to be passed as the
      request body.
    name: Required. The resource name of the Processor or ProcessorVersion to
      use for processing. If a Processor is specified, the server will use its
      default version. Format:
      `projects/{project}/locations/{location}/processors/{processor}`, or `pr
      ojects/{project}/locations/{location}/processors/{processor}/processorVe
      rsions/{processorVersion}`
  """

  googleCloudDocumentaiV1ProcessRequest = _messages.MessageField('GoogleCloudDocumentaiV1ProcessRequest', 1)
  name = _messages.StringField(2, required=True)


class DocumentaiProjectsLocationsProcessorsProcessorVersionsTrainRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsProcessorVersionsTrainRequest
  object.

  Fields:
    googleCloudDocumentaiV1TrainProcessorVersionRequest: A
      GoogleCloudDocumentaiV1TrainProcessorVersionRequest resource to be
      passed as the request body.
    parent: Required. The parent (project, location and processor) to create
      the new version for. Format:
      `projects/{project}/locations/{location}/processors/{processor}`.
  """

  googleCloudDocumentaiV1TrainProcessorVersionRequest = _messages.MessageField('GoogleCloudDocumentaiV1TrainProcessorVersionRequest', 1)
  parent = _messages.StringField(2, required=True)


class DocumentaiProjectsLocationsProcessorsProcessorVersionsUndeployRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsProcessorVersionsUndeployRequest
  object.

  Fields:
    googleCloudDocumentaiV1UndeployProcessorVersionRequest: A
      GoogleCloudDocumentaiV1UndeployProcessorVersionRequest resource to be
      passed as the request body.
    name: Required. The processor version resource name to be undeployed.
  """

  googleCloudDocumentaiV1UndeployProcessorVersionRequest = _messages.MessageField('GoogleCloudDocumentaiV1UndeployProcessorVersionRequest', 1)
  name = _messages.StringField(2, required=True)


class DocumentaiProjectsLocationsProcessorsSetDefaultProcessorVersionRequest(_messages.Message):
  r"""A DocumentaiProjectsLocationsProcessorsSetDefaultProcessorVersionRequest
  object.

  Fields:
    googleCloudDocumentaiV1SetDefaultProcessorVersionRequest: A
      GoogleCloudDocumentaiV1SetDefaultProcessorVersionRequest resource to be
      passed as the request body.
    processor: Required. The resource name of the Processor to change default
      version.
  """

  googleCloudDocumentaiV1SetDefaultProcessorVersionRequest = _messages.MessageField('GoogleCloudDocumentaiV1SetDefaultProcessorVersionRequest', 1)
  processor = _messages.StringField(2, required=True)


class DocumentaiProjectsOperationsGetRequest(_messages.Message):
  r"""A DocumentaiProjectsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GoogleCloudDocumentaiUiv1beta3AutoLabelDocumentsMetadata(_messages.Message):
  r"""Metadata of the auto-labeling documents operation.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
    individualAutoLabelStatuses: The list of individual auto-labeling statuses
      of the dataset documents.
    totalDocumentCount: Total number of the auto-labeling documents.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)
  individualAutoLabelStatuses = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3AutoLabelDocumentsMetadataIndividualAutoLabelStatus', 2, repeated=True)
  totalDocumentCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiUiv1beta3AutoLabelDocumentsMetadataIndividualAutoLabelStatus(_messages.Message):
  r"""The status of individual documents in the auto-labeling process.

  Fields:
    documentId: The document id of the auto-labeled document. This will
      replace the gcs_uri.
    status: The status of the document auto-labeling.
  """

  documentId = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3DocumentId', 1)
  status = _messages.MessageField('GoogleRpcStatus', 2)


class GoogleCloudDocumentaiUiv1beta3AutoLabelDocumentsResponse(_messages.Message):
  r"""The response proto of AutoLabelDocuments method."""


class GoogleCloudDocumentaiUiv1beta3BatchDeleteDocumentsMetadata(_messages.Message):
  r"""A GoogleCloudDocumentaiUiv1beta3BatchDeleteDocumentsMetadata object.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
    errorDocumentCount: Total number of documents that failed to be deleted in
      storage.
    individualBatchDeleteStatuses: The list of response details of each
      document.
    totalDocumentCount: Total number of documents deleting from dataset.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)
  errorDocumentCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  individualBatchDeleteStatuses = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3BatchDeleteDocumentsMetadataIndividualBatchDeleteStatus', 3, repeated=True)
  totalDocumentCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiUiv1beta3BatchDeleteDocumentsMetadataIndividualBatchDeleteStatus(_messages.Message):
  r"""The status of each individual document in the batch delete process.

  Fields:
    documentId: The document id of the document.
    status: The status of deleting the document in storage.
  """

  documentId = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3DocumentId', 1)
  status = _messages.MessageField('GoogleRpcStatus', 2)


class GoogleCloudDocumentaiUiv1beta3BatchDeleteDocumentsResponse(_messages.Message):
  r"""Response of the delete documents operation."""


class GoogleCloudDocumentaiUiv1beta3BatchMoveDocumentsMetadata(_messages.Message):
  r"""A GoogleCloudDocumentaiUiv1beta3BatchMoveDocumentsMetadata object.

  Enums:
    DestDatasetTypeValueValuesEnum: The destination dataset split type.
    DestSplitTypeValueValuesEnum: The destination dataset split type.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
    destDatasetType: The destination dataset split type.
    destSplitType: The destination dataset split type.
    individualBatchMoveStatuses: The list of response details of each
      document.
  """

  class DestDatasetTypeValueValuesEnum(_messages.Enum):
    r"""The destination dataset split type.

    Values:
      DATASET_SPLIT_TYPE_UNSPECIFIED: Default value if the enum is not set.
      DATASET_SPLIT_TRAIN: Identifies the train documents.
      DATASET_SPLIT_TEST: Identifies the test documents.
      DATASET_SPLIT_UNASSIGNED: Identifies the unassigned documents.
    """
    DATASET_SPLIT_TYPE_UNSPECIFIED = 0
    DATASET_SPLIT_TRAIN = 1
    DATASET_SPLIT_TEST = 2
    DATASET_SPLIT_UNASSIGNED = 3

  class DestSplitTypeValueValuesEnum(_messages.Enum):
    r"""The destination dataset split type.

    Values:
      DATASET_SPLIT_TYPE_UNSPECIFIED: Default value if the enum is not set.
      DATASET_SPLIT_TRAIN: Identifies the train documents.
      DATASET_SPLIT_TEST: Identifies the test documents.
      DATASET_SPLIT_UNASSIGNED: Identifies the unassigned documents.
    """
    DATASET_SPLIT_TYPE_UNSPECIFIED = 0
    DATASET_SPLIT_TRAIN = 1
    DATASET_SPLIT_TEST = 2
    DATASET_SPLIT_UNASSIGNED = 3

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)
  destDatasetType = _messages.EnumField('DestDatasetTypeValueValuesEnum', 2)
  destSplitType = _messages.EnumField('DestSplitTypeValueValuesEnum', 3)
  individualBatchMoveStatuses = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3BatchMoveDocumentsMetadataIndividualBatchMoveStatus', 4, repeated=True)


class GoogleCloudDocumentaiUiv1beta3BatchMoveDocumentsMetadataIndividualBatchMoveStatus(_messages.Message):
  r"""The status of each individual document in the batch move process.

  Fields:
    documentId: The document id of the document.
    status: The status of moving the document.
  """

  documentId = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3DocumentId', 1)
  status = _messages.MessageField('GoogleRpcStatus', 2)


class GoogleCloudDocumentaiUiv1beta3BatchMoveDocumentsResponse(_messages.Message):
  r"""Response of the batch move documents operation."""


class GoogleCloudDocumentaiUiv1beta3BatchUpdateDocumentsMetadata(_messages.Message):
  r"""A GoogleCloudDocumentaiUiv1beta3BatchUpdateDocumentsMetadata object.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
    individualBatchUpdateStatuses: The list of response details of each
      document.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)
  individualBatchUpdateStatuses = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3BatchUpdateDocumentsMetadataIndividualBatchUpdateStatus', 2, repeated=True)


class GoogleCloudDocumentaiUiv1beta3BatchUpdateDocumentsMetadataIndividualBatchUpdateStatus(_messages.Message):
  r"""The status of each individual document in the batch update process.

  Fields:
    documentId: The document id of the document.
    status: The status of updating the document in storage.
  """

  documentId = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3DocumentId', 1)
  status = _messages.MessageField('GoogleRpcStatus', 2)


class GoogleCloudDocumentaiUiv1beta3BatchUpdateDocumentsResponse(_messages.Message):
  r"""Response of the batch update documents operation."""


class GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata(_messages.Message):
  r"""The common metadata for long running operations.

  Enums:
    StateValueValuesEnum: The state of the operation.

  Fields:
    createTime: The creation time of the operation.
    resource: A related resource to this operation.
    state: The state of the operation.
    stateMessage: A message providing more details about the current state of
      processing.
    updateTime: The last update time of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the operation.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      RUNNING: Operation is still running.
      CANCELLING: Operation is being cancelled.
      SUCCEEDED: Operation succeeded.
      FAILED: Operation failed.
      CANCELLED: Operation is cancelled.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    CANCELLING = 2
    SUCCEEDED = 3
    FAILED = 4
    CANCELLED = 5

  createTime = _messages.StringField(1)
  resource = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  stateMessage = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class GoogleCloudDocumentaiUiv1beta3CreateLabelerPoolOperationMetadata(_messages.Message):
  r"""The long-running operation metadata for the CreateLabelerPool method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3DeleteLabelerPoolOperationMetadata(_messages.Message):
  r"""The long-running operation metadata for DeleteLabelerPool.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3DeleteProcessorMetadata(_messages.Message):
  r"""The long-running operation metadata for the DeleteProcessor method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3DeleteProcessorVersionMetadata(_messages.Message):
  r"""The long-running operation metadata for the DeleteProcessorVersion
  method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3DeployProcessorVersionMetadata(_messages.Message):
  r"""The long-running operation metadata for the DeployProcessorVersion
  method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3DeployProcessorVersionResponse(_messages.Message):
  r"""Response message for the DeployProcessorVersion method."""


class GoogleCloudDocumentaiUiv1beta3DisableProcessorMetadata(_messages.Message):
  r"""The long-running operation metadata for the DisableProcessor method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3DisableProcessorResponse(_messages.Message):
  r"""Response message for the DisableProcessor method. Intentionally empty
  proto for adding fields in future.
  """



class GoogleCloudDocumentaiUiv1beta3DocumentId(_messages.Message):
  r"""Document Identifier.

  Fields:
    gcsManagedDocId: A document id within user-managed Cloud Storage.
    revisionRef: Points to a specific revision of the document if set.
    unmanagedDocId: A document id within unmanaged dataset.
  """

  gcsManagedDocId = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3DocumentIdGCSManagedDocumentId', 1)
  revisionRef = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3RevisionRef', 2)
  unmanagedDocId = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3DocumentIdUnmanagedDocumentId', 3)


class GoogleCloudDocumentaiUiv1beta3DocumentIdGCSManagedDocumentId(_messages.Message):
  r"""Identifies a document uniquely within the scope of a dataset in the
  user-managed Cloud Storage option.

  Fields:
    cwDocId: Id of the document (indexed) managed by Content Warehouse.
    gcsUri: Required. The Cloud Storage URI where the actual document is
      stored.
  """

  cwDocId = _messages.StringField(1)
  gcsUri = _messages.StringField(2)


class GoogleCloudDocumentaiUiv1beta3DocumentIdUnmanagedDocumentId(_messages.Message):
  r"""Identifies a document uniquely within the scope of a dataset in
  unmanaged option.

  Fields:
    docId: Required. The id of the document.
  """

  docId = _messages.StringField(1)


class GoogleCloudDocumentaiUiv1beta3EnableProcessorMetadata(_messages.Message):
  r"""The long-running operation metadata for the EnableProcessor method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3EnableProcessorResponse(_messages.Message):
  r"""Response message for the EnableProcessor method. Intentionally empty
  proto for adding fields in future.
  """



class GoogleCloudDocumentaiUiv1beta3EvaluateProcessorVersionMetadata(_messages.Message):
  r"""Metadata of the EvaluateProcessorVersion method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3EvaluateProcessorVersionResponse(_messages.Message):
  r"""Response of the EvaluateProcessorVersion method.

  Fields:
    evaluation: The resource name of the created evaluation.
  """

  evaluation = _messages.StringField(1)


class GoogleCloudDocumentaiUiv1beta3ExportDocumentsMetadata(_messages.Message):
  r"""Metadata of the batch export documents operation.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
    individualExportStatuses: The list of response details of each document.
    splitExportStats: The list of statistics for each dataset split type.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)
  individualExportStatuses = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3ExportDocumentsMetadataIndividualExportStatus', 2, repeated=True)
  splitExportStats = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3ExportDocumentsMetadataSplitExportStat', 3, repeated=True)


class GoogleCloudDocumentaiUiv1beta3ExportDocumentsMetadataIndividualExportStatus(_messages.Message):
  r"""The status of each individual document in the export process.

  Fields:
    documentId: The path to source docproto of the document.
    outputGcsDestination: The output_gcs_destination of the exported document
      if it was successful, otherwise empty.
    status: The status of the exporting of the document.
  """

  documentId = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3DocumentId', 1)
  outputGcsDestination = _messages.StringField(2)
  status = _messages.MessageField('GoogleRpcStatus', 3)


class GoogleCloudDocumentaiUiv1beta3ExportDocumentsMetadataSplitExportStat(_messages.Message):
  r"""The statistic representing a dataset split type for this export.

  Enums:
    SplitTypeValueValuesEnum: The dataset split type.

  Fields:
    splitType: The dataset split type.
    totalDocumentCount: Total number of documents with the given dataset split
      type to be exported.
  """

  class SplitTypeValueValuesEnum(_messages.Enum):
    r"""The dataset split type.

    Values:
      DATASET_SPLIT_TYPE_UNSPECIFIED: Default value if the enum is not set.
      DATASET_SPLIT_TRAIN: Identifies the train documents.
      DATASET_SPLIT_TEST: Identifies the test documents.
      DATASET_SPLIT_UNASSIGNED: Identifies the unassigned documents.
    """
    DATASET_SPLIT_TYPE_UNSPECIFIED = 0
    DATASET_SPLIT_TRAIN = 1
    DATASET_SPLIT_TEST = 2
    DATASET_SPLIT_UNASSIGNED = 3

  splitType = _messages.EnumField('SplitTypeValueValuesEnum', 1)
  totalDocumentCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiUiv1beta3ExportDocumentsResponse(_messages.Message):
  r"""The response proto of ExportDocuments method."""


class GoogleCloudDocumentaiUiv1beta3ExportProcessorVersionMetadata(_messages.Message):
  r"""Metadata message associated with the ExportProcessorVersion operation.

  Fields:
    commonMetadata: The common metadata about the operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3ExportProcessorVersionResponse(_messages.Message):
  r"""Response message associated with the ExportProcessorVersion operation.

  Fields:
    gcsUri: The Cloud Storage URI containing the output artifacts.
  """

  gcsUri = _messages.StringField(1)


class GoogleCloudDocumentaiUiv1beta3ImportDocumentsMetadata(_messages.Message):
  r"""Metadata of the import document operation.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
    importConfigValidationResults: Validation statuses of the batch documents
      import config.
    individualImportStatuses: The list of response details of each document.
    totalDocumentCount: Total number of the documents that are qualified for
      importing.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)
  importConfigValidationResults = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3ImportDocumentsMetadataImportConfigValidationResult', 2, repeated=True)
  individualImportStatuses = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3ImportDocumentsMetadataIndividualImportStatus', 3, repeated=True)
  totalDocumentCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiUiv1beta3ImportDocumentsMetadataImportConfigValidationResult(_messages.Message):
  r"""The validation status of each import config. Status is set to an error
  if there are no documents to import in the `import_config`, or `OK` if the
  operation will try to proceed with at least one document.

  Fields:
    inputGcsSource: The source Cloud Storage URI specified in the import
      config.
    status: The validation status of import config.
  """

  inputGcsSource = _messages.StringField(1)
  status = _messages.MessageField('GoogleRpcStatus', 2)


class GoogleCloudDocumentaiUiv1beta3ImportDocumentsMetadataIndividualImportStatus(_messages.Message):
  r"""The status of each individual document in the import process.

  Fields:
    inputGcsSource: The source Cloud Storage URI of the document.
    outputDocumentId: The document id of imported document if it was
      successful, otherwise empty.
    outputGcsDestination: The output_gcs_destination of the processed document
      if it was successful, otherwise empty.
    status: The status of the importing of the document.
  """

  inputGcsSource = _messages.StringField(1)
  outputDocumentId = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3DocumentId', 2)
  outputGcsDestination = _messages.StringField(3)
  status = _messages.MessageField('GoogleRpcStatus', 4)


class GoogleCloudDocumentaiUiv1beta3ImportDocumentsResponse(_messages.Message):
  r"""Response of the import document operation."""


class GoogleCloudDocumentaiUiv1beta3ImportProcessorVersionMetadata(_messages.Message):
  r"""The long-running operation metadata for the ImportProcessorVersion
  method.

  Fields:
    commonMetadata: The basic metadata for the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3ImportProcessorVersionResponse(_messages.Message):
  r"""The response message for the ImportProcessorVersion method.

  Fields:
    processorVersion: The destination processor version name.
  """

  processorVersion = _messages.StringField(1)


class GoogleCloudDocumentaiUiv1beta3ResyncDatasetMetadata(_messages.Message):
  r"""The metadata proto of `ResyncDataset` method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
    datasetResyncStatuses: The list of dataset resync statuses. Not checked
      when ResyncDatasetRequest.dataset_documents is specified.
    individualDocumentResyncStatuses: The list of document resync statuses.
      The same document could have multiple
      `individual_document_resync_statuses` if it has multiple
      inconsistencies.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)
  datasetResyncStatuses = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3ResyncDatasetMetadataDatasetResyncStatus', 2, repeated=True)
  individualDocumentResyncStatuses = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3ResyncDatasetMetadataIndividualDocumentResyncStatus', 3, repeated=True)


class GoogleCloudDocumentaiUiv1beta3ResyncDatasetMetadataDatasetResyncStatus(_messages.Message):
  r"""Resync status against inconsistency types on the dataset level.

  Enums:
    DatasetInconsistencyTypeValueValuesEnum: The type of the inconsistency of
      the dataset.

  Fields:
    datasetInconsistencyType: The type of the inconsistency of the dataset.
    status: The status of resyncing the dataset with regards to the detected
      inconsistency. Empty if ResyncDatasetRequest.validate_only is `true`.
  """

  class DatasetInconsistencyTypeValueValuesEnum(_messages.Enum):
    r"""The type of the inconsistency of the dataset.

    Values:
      DATASET_INCONSISTENCY_TYPE_UNSPECIFIED: Default value.
      DATASET_INCONSISTENCY_TYPE_NO_STORAGE_MARKER: The marker file under the
        dataset folder is not found.
    """
    DATASET_INCONSISTENCY_TYPE_UNSPECIFIED = 0
    DATASET_INCONSISTENCY_TYPE_NO_STORAGE_MARKER = 1

  datasetInconsistencyType = _messages.EnumField('DatasetInconsistencyTypeValueValuesEnum', 1)
  status = _messages.MessageField('GoogleRpcStatus', 2)


class GoogleCloudDocumentaiUiv1beta3ResyncDatasetMetadataIndividualDocumentResyncStatus(_messages.Message):
  r"""Resync status for each document per inconsistency type.

  Enums:
    DocumentInconsistencyTypeValueValuesEnum: The type of document
      inconsistency.

  Fields:
    documentId: The document identifier.
    documentInconsistencyType: The type of document inconsistency.
    status: The status of resyncing the document with regards to the detected
      inconsistency. Empty if ResyncDatasetRequest.validate_only is `true`.
  """

  class DocumentInconsistencyTypeValueValuesEnum(_messages.Enum):
    r"""The type of document inconsistency.

    Values:
      DOCUMENT_INCONSISTENCY_TYPE_UNSPECIFIED: Default value.
      DOCUMENT_INCONSISTENCY_TYPE_INVALID_DOCPROTO: The document proto is
        invalid.
      DOCUMENT_INCONSISTENCY_TYPE_MISMATCHED_METADATA: Indexed docproto
        metadata is mismatched.
      DOCUMENT_INCONSISTENCY_TYPE_NO_PAGE_IMAGE: The page image or thumbnails
        are missing.
    """
    DOCUMENT_INCONSISTENCY_TYPE_UNSPECIFIED = 0
    DOCUMENT_INCONSISTENCY_TYPE_INVALID_DOCPROTO = 1
    DOCUMENT_INCONSISTENCY_TYPE_MISMATCHED_METADATA = 2
    DOCUMENT_INCONSISTENCY_TYPE_NO_PAGE_IMAGE = 3

  documentId = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3DocumentId', 1)
  documentInconsistencyType = _messages.EnumField('DocumentInconsistencyTypeValueValuesEnum', 2)
  status = _messages.MessageField('GoogleRpcStatus', 3)


class GoogleCloudDocumentaiUiv1beta3ResyncDatasetResponse(_messages.Message):
  r"""The response proto of ResyncDataset method."""


class GoogleCloudDocumentaiUiv1beta3RevisionRef(_messages.Message):
  r"""The revision reference specifies which revision on the document to read.

  Enums:
    RevisionCaseValueValuesEnum: Reads the revision by the predefined case.

  Fields:
    latestProcessorVersion: Reads the revision generated by the processor
      version. The format takes the full resource name of processor version. `
      projects/{project}/locations/{location}/processors/{processor}/processor
      Versions/{processorVersion}`
    revisionCase: Reads the revision by the predefined case.
    revisionId: Reads the revision given by the id.
  """

  class RevisionCaseValueValuesEnum(_messages.Enum):
    r"""Reads the revision by the predefined case.

    Values:
      REVISION_CASE_UNSPECIFIED: Unspecified case, fall back to read the
        `LATEST_HUMAN_REVIEW`.
      LATEST_HUMAN_REVIEW: The latest revision made by a human.
      LATEST_TIMESTAMP: The latest revision based on timestamp.
      BASE_OCR_REVISION: The first (OCR) revision.
    """
    REVISION_CASE_UNSPECIFIED = 0
    LATEST_HUMAN_REVIEW = 1
    LATEST_TIMESTAMP = 2
    BASE_OCR_REVISION = 3

  latestProcessorVersion = _messages.StringField(1)
  revisionCase = _messages.EnumField('RevisionCaseValueValuesEnum', 2)
  revisionId = _messages.StringField(3)


class GoogleCloudDocumentaiUiv1beta3SampleDocumentsMetadata(_messages.Message):
  r"""Metadata of the sample documents operation.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3SampleDocumentsResponse(_messages.Message):
  r"""Response of the sample documents operation.

  Fields:
    sampleTestStatus: The status of sampling documents in test split.
    sampleTrainingStatus: The status of sampling documents in training split.
    selectedDocuments: The result of the sampling process.
  """

  sampleTestStatus = _messages.MessageField('GoogleRpcStatus', 1)
  sampleTrainingStatus = _messages.MessageField('GoogleRpcStatus', 2)
  selectedDocuments = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3SampleDocumentsResponseSelectedDocument', 3, repeated=True)


class GoogleCloudDocumentaiUiv1beta3SampleDocumentsResponseSelectedDocument(_messages.Message):
  r"""A GoogleCloudDocumentaiUiv1beta3SampleDocumentsResponseSelectedDocument
  object.

  Fields:
    documentId: An internal identifier for document.
  """

  documentId = _messages.StringField(1)


class GoogleCloudDocumentaiUiv1beta3SetDefaultProcessorVersionMetadata(_messages.Message):
  r"""The long-running operation metadata for the SetDefaultProcessorVersion
  method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3SetDefaultProcessorVersionResponse(_messages.Message):
  r"""Response message for the SetDefaultProcessorVersion method."""


class GoogleCloudDocumentaiUiv1beta3TrainProcessorVersionMetadata(_messages.Message):
  r"""The metadata that represents a processor version being created.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
    testDatasetValidation: The test dataset validation information.
    trainingDatasetValidation: The training dataset validation information.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)
  testDatasetValidation = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3TrainProcessorVersionMetadataDatasetValidation', 2)
  trainingDatasetValidation = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3TrainProcessorVersionMetadataDatasetValidation', 3)


class GoogleCloudDocumentaiUiv1beta3TrainProcessorVersionMetadataDatasetValidation(_messages.Message):
  r"""The dataset validation information. This includes any and all errors
  with documents and the dataset.

  Fields:
    datasetErrorCount: The total number of dataset errors.
    datasetErrors: Error information for the dataset as a whole. A maximum of
      10 dataset errors will be returned. A single dataset error is terminal
      for training.
    documentErrorCount: The total number of document errors.
    documentErrors: Error information pertaining to specific documents. A
      maximum of 10 document errors will be returned. Any document with errors
      will not be used throughout training.
  """

  datasetErrorCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  datasetErrors = _messages.MessageField('GoogleRpcStatus', 2, repeated=True)
  documentErrorCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  documentErrors = _messages.MessageField('GoogleRpcStatus', 4, repeated=True)


class GoogleCloudDocumentaiUiv1beta3TrainProcessorVersionResponse(_messages.Message):
  r"""The response for TrainProcessorVersion.

  Fields:
    processorVersion: The resource name of the processor version produced by
      training.
  """

  processorVersion = _messages.StringField(1)


class GoogleCloudDocumentaiUiv1beta3UndeployProcessorVersionMetadata(_messages.Message):
  r"""The long-running operation metadata for the UndeployProcessorVersion
  method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3UndeployProcessorVersionResponse(_messages.Message):
  r"""Response message for the UndeployProcessorVersion method."""


class GoogleCloudDocumentaiUiv1beta3UpdateDatasetOperationMetadata(_messages.Message):
  r"""A GoogleCloudDocumentaiUiv1beta3UpdateDatasetOperationMetadata object.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3UpdateHumanReviewConfigMetadata(_messages.Message):
  r"""The long-running operation metadata for updating the human review
  configuration.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiUiv1beta3UpdateLabelerPoolOperationMetadata(_messages.Message):
  r"""The long-running operation metadata for UpdateLabelerPool.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiUiv1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1Barcode(_messages.Message):
  r"""Encodes the detailed information of a barcode.

  Fields:
    format: Format of a barcode. The supported formats are: - `CODE_128`: Code
      128 type. - `CODE_39`: Code 39 type. - `CODE_93`: Code 93 type. -
      `CODABAR`: Codabar type. - `DATA_MATRIX`: 2D Data Matrix type. - `ITF`:
      ITF type. - `EAN_13`: EAN-13 type. - `EAN_8`: EAN-8 type. - `QR_CODE`:
      2D QR code type. - `UPC_A`: UPC-A type. - `UPC_E`: UPC-E type. -
      `PDF417`: PDF417 type. - `AZTEC`: 2D Aztec code type. - `DATABAR`: GS1
      DataBar code type.
    rawValue: Raw value encoded in the barcode. For example:
      `'MEBKM:TITLE:Google;URL:https://www.google.com;;'`.
    valueFormat: Value format describes the format of the value that a barcode
      encodes. The supported formats are: - `CONTACT_INFO`: Contact
      information. - `EMAIL`: Email address. - `ISBN`: ISBN identifier. -
      `PHONE`: Phone number. - `PRODUCT`: Product. - `SMS`: SMS message. -
      `TEXT`: Text string. - `URL`: URL address. - `WIFI`: Wifi information. -
      `GEO`: Geo-localization. - `CALENDAR_EVENT`: Calendar event. -
      `DRIVER_LICENSE`: Driver's license.
  """

  format = _messages.StringField(1)
  rawValue = _messages.StringField(2)
  valueFormat = _messages.StringField(3)


class GoogleCloudDocumentaiV1BatchDocumentsInputConfig(_messages.Message):
  r"""The common config to specify a set of documents used as input.

  Fields:
    gcsDocuments: The set of documents individually specified on Cloud
      Storage.
    gcsPrefix: The set of documents that match the specified Cloud Storage
      `gcs_prefix`.
  """

  gcsDocuments = _messages.MessageField('GoogleCloudDocumentaiV1GcsDocuments', 1)
  gcsPrefix = _messages.MessageField('GoogleCloudDocumentaiV1GcsPrefix', 2)


class GoogleCloudDocumentaiV1BatchProcessMetadata(_messages.Message):
  r"""The long-running operation metadata for BatchProcessDocuments.

  Enums:
    StateValueValuesEnum: The state of the current batch processing.

  Fields:
    createTime: The creation time of the operation.
    individualProcessStatuses: The list of response details of each document.
    state: The state of the current batch processing.
    stateMessage: A message providing more details about the current state of
      processing. For example, the error message if the operation is failed.
    updateTime: The last update time of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the current batch processing.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      WAITING: Request operation is waiting for scheduling.
      RUNNING: Request is being processed.
      SUCCEEDED: The batch processing completed successfully.
      CANCELLING: The batch processing was being cancelled.
      CANCELLED: The batch processing was cancelled.
      FAILED: The batch processing has failed.
    """
    STATE_UNSPECIFIED = 0
    WAITING = 1
    RUNNING = 2
    SUCCEEDED = 3
    CANCELLING = 4
    CANCELLED = 5
    FAILED = 6

  createTime = _messages.StringField(1)
  individualProcessStatuses = _messages.MessageField('GoogleCloudDocumentaiV1BatchProcessMetadataIndividualProcessStatus', 2, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  stateMessage = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class GoogleCloudDocumentaiV1BatchProcessMetadataIndividualProcessStatus(_messages.Message):
  r"""The status of a each individual document in the batch process.

  Fields:
    humanReviewStatus: The status of human review on the processed document.
    inputGcsSource: The source of the document, same as the input_gcs_source
      field in the request when the batch process started.
    outputGcsDestination: The Cloud Storage output destination (in the request
      as DocumentOutputConfig.GcsOutputConfig.gcs_uri) of the processed
      document if it was successful, otherwise empty.
    status: The status processing the document.
  """

  humanReviewStatus = _messages.MessageField('GoogleCloudDocumentaiV1HumanReviewStatus', 1)
  inputGcsSource = _messages.StringField(2)
  outputGcsDestination = _messages.StringField(3)
  status = _messages.MessageField('GoogleRpcStatus', 4)


class GoogleCloudDocumentaiV1BatchProcessRequest(_messages.Message):
  r"""Request message for BatchProcessDocuments.

  Messages:
    LabelsValue: Optional. The labels with user-defined metadata for the
      request. Label keys and values can be no longer than 63 characters
      (Unicode codepoints) and can only contain lowercase letters, numeric
      characters, underscores, and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter.

  Fields:
    documentOutputConfig: The output configuration for the
      BatchProcessDocuments method.
    inputDocuments: The input documents for the BatchProcessDocuments method.
    labels: Optional. The labels with user-defined metadata for the request.
      Label keys and values can be no longer than 63 characters (Unicode
      codepoints) and can only contain lowercase letters, numeric characters,
      underscores, and dashes. International characters are allowed. Label
      values are optional. Label keys must start with a letter.
    processOptions: Inference-time options for the process API
    skipHumanReview: Whether human review should be skipped for this request.
      Default to `false`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels with user-defined metadata for the request. Label
    keys and values can be no longer than 63 characters (Unicode codepoints)
    and can only contain lowercase letters, numeric characters, underscores,
    and dashes. International characters are allowed. Label values are
    optional. Label keys must start with a letter.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  documentOutputConfig = _messages.MessageField('GoogleCloudDocumentaiV1DocumentOutputConfig', 1)
  inputDocuments = _messages.MessageField('GoogleCloudDocumentaiV1BatchDocumentsInputConfig', 2)
  labels = _messages.MessageField('LabelsValue', 3)
  processOptions = _messages.MessageField('GoogleCloudDocumentaiV1ProcessOptions', 4)
  skipHumanReview = _messages.BooleanField(5)


class GoogleCloudDocumentaiV1BatchProcessResponse(_messages.Message):
  r"""Response message for BatchProcessDocuments."""


class GoogleCloudDocumentaiV1BoundingPoly(_messages.Message):
  r"""A bounding polygon for the detected image annotation.

  Fields:
    normalizedVertices: The bounding polygon normalized vertices.
    vertices: The bounding polygon vertices.
  """

  normalizedVertices = _messages.MessageField('GoogleCloudDocumentaiV1NormalizedVertex', 1, repeated=True)
  vertices = _messages.MessageField('GoogleCloudDocumentaiV1Vertex', 2, repeated=True)


class GoogleCloudDocumentaiV1CommonOperationMetadata(_messages.Message):
  r"""The common metadata for long running operations.

  Enums:
    StateValueValuesEnum: The state of the operation.

  Fields:
    createTime: The creation time of the operation.
    resource: A related resource to this operation.
    state: The state of the operation.
    stateMessage: A message providing more details about the current state of
      processing.
    updateTime: The last update time of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the operation.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      RUNNING: Operation is still running.
      CANCELLING: Operation is being cancelled.
      SUCCEEDED: Operation succeeded.
      FAILED: Operation failed.
      CANCELLED: Operation is cancelled.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    CANCELLING = 2
    SUCCEEDED = 3
    FAILED = 4
    CANCELLED = 5

  createTime = _messages.StringField(1)
  resource = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  stateMessage = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class GoogleCloudDocumentaiV1DeleteProcessorMetadata(_messages.Message):
  r"""The long-running operation metadata for the DeleteProcessor method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1DeleteProcessorVersionMetadata(_messages.Message):
  r"""The long-running operation metadata for the DeleteProcessorVersion
  method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1DeployProcessorVersionMetadata(_messages.Message):
  r"""The long-running operation metadata for the DeployProcessorVersion
  method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1DeployProcessorVersionRequest(_messages.Message):
  r"""Request message for the DeployProcessorVersion method."""


class GoogleCloudDocumentaiV1DeployProcessorVersionResponse(_messages.Message):
  r"""Response message for the DeployProcessorVersion method."""


class GoogleCloudDocumentaiV1DisableProcessorMetadata(_messages.Message):
  r"""The long-running operation metadata for the DisableProcessor method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1DisableProcessorRequest(_messages.Message):
  r"""Request message for the DisableProcessor method."""


class GoogleCloudDocumentaiV1DisableProcessorResponse(_messages.Message):
  r"""Response message for the DisableProcessor method. Intentionally empty
  proto for adding fields in future.
  """



class GoogleCloudDocumentaiV1Document(_messages.Message):
  r"""Document represents the canonical document resource in Document AI. It
  is an interchange format that provides insights into documents and allows
  for collaboration between users and Document AI to iterate and optimize for
  quality.

  Fields:
    chunkedDocument: Document chunked based on chunking config.
    content: Optional. Inline document content, represented as a stream of
      bytes. Note: As with all `bytes` fields, protobuffers use a pure binary
      representation, whereas JSON representations use base64.
    docid: Optional. An internal identifier for document. Should be loggable
      (no PII).
    documentLayout: Parsed layout of the document.
    entities: A list of entities detected on Document.text. For document
      shards, entities in this list may cross shard boundaries.
    entityRelations: Placeholder. Relationship among Document.entities.
    error: Any error that occurred while processing this document.
    mimeType: An IANA published [media type (MIME
      type)](https://www.iana.org/assignments/media-types/media-types.xhtml).
    pages: Visual page layout for the Document.
    revisions: Placeholder. Revision history of this document.
    shardInfo: Information about the sharding if this document is sharded part
      of a larger document. If the document is not sharded, this message is
      not specified.
    text: Optional. UTF-8 encoded text in reading order from the document.
    textChanges: Placeholder. A list of text corrections made to
      Document.text. This is usually used for annotating corrections to OCR
      mistakes. Text changes for a given revision may not overlap with each
      other.
    textStyles: Styles for the Document.text.
    uri: Optional. Currently supports Google Cloud Storage URI of the form
      `gs://bucket_name/object_name`. Object versioning is not supported. For
      more information, refer to [Google Cloud Storage Request
      URIs](https://cloud.google.com/storage/docs/reference-uris).
  """

  chunkedDocument = _messages.MessageField('GoogleCloudDocumentaiV1DocumentChunkedDocument', 1)
  content = _messages.BytesField(2)
  docid = _messages.StringField(3)
  documentLayout = _messages.MessageField('GoogleCloudDocumentaiV1DocumentDocumentLayout', 4)
  entities = _messages.MessageField('GoogleCloudDocumentaiV1DocumentEntity', 5, repeated=True)
  entityRelations = _messages.MessageField('GoogleCloudDocumentaiV1DocumentEntityRelation', 6, repeated=True)
  error = _messages.MessageField('GoogleRpcStatus', 7)
  mimeType = _messages.StringField(8)
  pages = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPage', 9, repeated=True)
  revisions = _messages.MessageField('GoogleCloudDocumentaiV1DocumentRevision', 10, repeated=True)
  shardInfo = _messages.MessageField('GoogleCloudDocumentaiV1DocumentShardInfo', 11)
  text = _messages.StringField(12)
  textChanges = _messages.MessageField('GoogleCloudDocumentaiV1DocumentTextChange', 13, repeated=True)
  textStyles = _messages.MessageField('GoogleCloudDocumentaiV1DocumentStyle', 14, repeated=True)
  uri = _messages.StringField(15)


class GoogleCloudDocumentaiV1DocumentChunkedDocument(_messages.Message):
  r"""Represents the chunks that the document is divided into.

  Fields:
    chunks: List of chunks.
  """

  chunks = _messages.MessageField('GoogleCloudDocumentaiV1DocumentChunkedDocumentChunk', 1, repeated=True)


class GoogleCloudDocumentaiV1DocumentChunkedDocumentChunk(_messages.Message):
  r"""Represents a chunk.

  Fields:
    chunkId: ID of the chunk.
    content: Text content of the chunk.
    pageFooters: Page footers associated with the chunk.
    pageHeaders: Page headers associated with the chunk.
    pageSpan: Page span of the chunk.
    sourceBlockIds: Unused.
  """

  chunkId = _messages.StringField(1)
  content = _messages.StringField(2)
  pageFooters = _messages.MessageField('GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageFooter', 3, repeated=True)
  pageHeaders = _messages.MessageField('GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageHeader', 4, repeated=True)
  pageSpan = _messages.MessageField('GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageSpan', 5)
  sourceBlockIds = _messages.StringField(6, repeated=True)


class GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageFooter(_messages.Message):
  r"""Represents the page footer associated with the chunk.

  Fields:
    pageSpan: Page span of the footer.
    text: Footer in text format.
  """

  pageSpan = _messages.MessageField('GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageSpan', 1)
  text = _messages.StringField(2)


class GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageHeader(_messages.Message):
  r"""Represents the page header associated with the chunk.

  Fields:
    pageSpan: Page span of the header.
    text: Header in text format.
  """

  pageSpan = _messages.MessageField('GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageSpan', 1)
  text = _messages.StringField(2)


class GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageSpan(_messages.Message):
  r"""Represents where the chunk starts and ends in the document.

  Fields:
    pageEnd: Page where chunk ends in the document.
    pageStart: Page where chunk starts in the document.
  """

  pageEnd = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageStart = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1DocumentDocumentLayout(_messages.Message):
  r"""Represents the parsed layout of a document as a collection of blocks
  that the document is divided into.

  Fields:
    blocks: List of blocks in the document.
  """

  blocks = _messages.MessageField('GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlock', 1, repeated=True)


class GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlock(_messages.Message):
  r"""Represents a block. A block could be one of the various types (text,
  table, list) supported.

  Fields:
    blockId: ID of the block.
    boundingBox: Identifies the bounding box for the block.
    listBlock: Block consisting of list content/structure.
    pageSpan: Page span of the block.
    tableBlock: Block consisting of table content/structure.
    textBlock: Block consisting of text content.
  """

  blockId = _messages.StringField(1)
  boundingBox = _messages.MessageField('GoogleCloudDocumentaiV1BoundingPoly', 2)
  listBlock = _messages.MessageField('GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutListBlock', 3)
  pageSpan = _messages.MessageField('GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutPageSpan', 4)
  tableBlock = _messages.MessageField('GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableBlock', 5)
  textBlock = _messages.MessageField('GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTextBlock', 6)


class GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutListBlock(_messages.Message):
  r"""Represents a list type block.

  Fields:
    listEntries: List entries that constitute a list block.
    type: Type of the list_entries (if exist). Available options are `ordered`
      and `unordered`.
  """

  listEntries = _messages.MessageField('GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutListEntry', 1, repeated=True)
  type = _messages.StringField(2)


class GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutListEntry(_messages.Message):
  r"""Represents an entry in the list.

  Fields:
    blocks: A list entry is a list of blocks. Repeated blocks support further
      hierarchies and nested blocks.
  """

  blocks = _messages.MessageField('GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlock', 1, repeated=True)


class GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutPageSpan(_messages.Message):
  r"""Represents where the block starts and ends in the document.

  Fields:
    pageEnd: Page where block ends in the document.
    pageStart: Page where block starts in the document.
  """

  pageEnd = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageStart = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableBlock(_messages.Message):
  r"""Represents a table type block.

  Fields:
    bodyRows: Body rows containing main table content.
    caption: Table caption/title.
    headerRows: Header rows at the top of the table.
  """

  bodyRows = _messages.MessageField('GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableRow', 1, repeated=True)
  caption = _messages.StringField(2)
  headerRows = _messages.MessageField('GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableRow', 3, repeated=True)


class GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableCell(_messages.Message):
  r"""Represents a cell in a table row.

  Fields:
    blocks: A table cell is a list of blocks. Repeated blocks support further
      hierarchies and nested blocks.
    colSpan: How many columns this cell spans.
    rowSpan: How many rows this cell spans.
  """

  blocks = _messages.MessageField('GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlock', 1, repeated=True)
  colSpan = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  rowSpan = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableRow(_messages.Message):
  r"""Represents a row in a table.

  Fields:
    cells: A table row is a list of table cells.
  """

  cells = _messages.MessageField('GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableCell', 1, repeated=True)


class GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTextBlock(_messages.Message):
  r"""Represents a text type block.

  Fields:
    blocks: A text block could further have child blocks. Repeated blocks
      support further hierarchies and nested blocks.
    text: Text content stored in the block.
    type: Type of the text in the block. Available options are: `paragraph`,
      `subtitle`, `heading-1`, `heading-2`, `heading-3`, `heading-4`,
      `heading-5`, `header`, `footer`.
  """

  blocks = _messages.MessageField('GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlock', 1, repeated=True)
  text = _messages.StringField(2)
  type = _messages.StringField(3)


class GoogleCloudDocumentaiV1DocumentEntity(_messages.Message):
  r"""An entity that could be a phrase in the text or a property that belongs
  to the document. It is a known entity type, such as a person, an
  organization, or location.

  Fields:
    confidence: Optional. Confidence of detected Schema entity. Range `[0,
      1]`.
    id: Optional. Canonical id. This will be a unique value in the entity list
      for this document.
    mentionId: Optional. Deprecated. Use `id` field instead.
    mentionText: Optional. Text value of the entity e.g. `1600 Amphitheatre
      Pkwy`.
    normalizedValue: Optional. Normalized entity value. Absent if the
      extracted value could not be converted or the type (e.g. address) is not
      supported for certain parsers. This field is also only populated for
      certain supported document types.
    pageAnchor: Optional. Represents the provenance of this entity wrt. the
      location on the page where it was found.
    properties: Optional. Entities can be nested to form a hierarchical data
      structure representing the content in the document.
    provenance: Optional. The history of this annotation.
    redacted: Optional. Whether the entity will be redacted for de-
      identification purposes.
    textAnchor: Optional. Provenance of the entity. Text anchor indexing into
      the Document.text.
    type: Required. Entity type from a schema e.g. `Address`.
  """

  confidence = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  id = _messages.StringField(2)
  mentionId = _messages.StringField(3)
  mentionText = _messages.StringField(4)
  normalizedValue = _messages.MessageField('GoogleCloudDocumentaiV1DocumentEntityNormalizedValue', 5)
  pageAnchor = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageAnchor', 6)
  properties = _messages.MessageField('GoogleCloudDocumentaiV1DocumentEntity', 7, repeated=True)
  provenance = _messages.MessageField('GoogleCloudDocumentaiV1DocumentProvenance', 8)
  redacted = _messages.BooleanField(9)
  textAnchor = _messages.MessageField('GoogleCloudDocumentaiV1DocumentTextAnchor', 10)
  type = _messages.StringField(11)


class GoogleCloudDocumentaiV1DocumentEntityNormalizedValue(_messages.Message):
  r"""Parsed and normalized entity value.

  Fields:
    addressValue: Postal address. See also: https://github.com/googleapis/goog
      leapis/blob/master/google/type/postal_address.proto
    booleanValue: Boolean value. Can be used for entities with binary values,
      or for checkboxes.
    dateValue: Date value. Includes year, month, day. See also: https://github
      .com/googleapis/googleapis/blob/master/google/type/date.proto
    datetimeValue: DateTime value. Includes date, time, and timezone. See
      also: https://github.com/googleapis/googleapis/blob/master/google/type/d
      atetime.proto
    floatValue: Float value.
    integerValue: Integer value.
    moneyValue: Money value. See also: https://github.com/googleapis/googleapi
      s/blob/master/google/type/money.proto
    signatureValue: A boolean attribute.
    text: Optional. An optional field to store a normalized string. For some
      entity types, one of respective `structured_value` fields may also be
      populated. Also not all the types of `structured_value` will be
      normalized. For example, some processors may not generate `float` or
      `integer` normalized text by default. Below are sample formats mapped to
      structured values. - Money/Currency type (`money_value`) is in the ISO
      4217 text format. - Date type (`date_value`) is in the ISO 8601 text
      format. - Datetime type (`datetime_value`) is in the ISO 8601 text
      format.
  """

  addressValue = _messages.MessageField('GoogleTypePostalAddress', 1)
  booleanValue = _messages.BooleanField(2)
  dateValue = _messages.MessageField('GoogleTypeDate', 3)
  datetimeValue = _messages.MessageField('GoogleTypeDateTime', 4)
  floatValue = _messages.FloatField(5, variant=_messages.Variant.FLOAT)
  integerValue = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  moneyValue = _messages.MessageField('GoogleTypeMoney', 7)
  signatureValue = _messages.BooleanField(8)
  text = _messages.StringField(9)


class GoogleCloudDocumentaiV1DocumentEntityRelation(_messages.Message):
  r"""Relationship between Entities.

  Fields:
    objectId: Object entity id.
    relation: Relationship description.
    subjectId: Subject entity id.
  """

  objectId = _messages.StringField(1)
  relation = _messages.StringField(2)
  subjectId = _messages.StringField(3)


class GoogleCloudDocumentaiV1DocumentOutputConfig(_messages.Message):
  r"""Config that controls the output of documents. All documents will be
  written as a JSON file.

  Fields:
    gcsOutputConfig: Output config to write the results to Cloud Storage.
  """

  gcsOutputConfig = _messages.MessageField('GoogleCloudDocumentaiV1DocumentOutputConfigGcsOutputConfig', 1)


class GoogleCloudDocumentaiV1DocumentOutputConfigGcsOutputConfig(_messages.Message):
  r"""The configuration used when outputting documents.

  Fields:
    fieldMask: Specifies which fields to include in the output documents. Only
      supports top level document and pages field so it must be in the form of
      `{document_field_name}` or `pages.{page_field_name}`.
    gcsUri: The Cloud Storage uri (a directory) of the output.
    shardingConfig: Specifies the sharding config for the output document.
  """

  fieldMask = _messages.StringField(1)
  gcsUri = _messages.StringField(2)
  shardingConfig = _messages.MessageField('GoogleCloudDocumentaiV1DocumentOutputConfigGcsOutputConfigShardingConfig', 3)


class GoogleCloudDocumentaiV1DocumentOutputConfigGcsOutputConfigShardingConfig(_messages.Message):
  r"""The sharding config for the output document.

  Fields:
    pagesOverlap: The number of overlapping pages between consecutive shards.
    pagesPerShard: The number of pages per shard.
  """

  pagesOverlap = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pagesPerShard = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1DocumentPage(_messages.Message):
  r"""A page in a Document.

  Fields:
    blocks: A list of visually detected text blocks on the page. A block has a
      set of lines (collected into paragraphs) that have a common line-spacing
      and orientation.
    detectedBarcodes: A list of detected barcodes.
    detectedLanguages: A list of detected languages together with confidence.
    dimension: Physical dimension of the page.
    formFields: A list of visually detected form fields on the page.
    image: Rendered image for this page. This image is preprocessed to remove
      any skew, rotation, and distortions such that the annotation bounding
      boxes can be upright and axis-aligned.
    imageQualityScores: Image quality scores.
    layout: Layout for the page.
    lines: A list of visually detected text lines on the page. A collection of
      tokens that a human would perceive as a line.
    pageNumber: 1-based index for current Page in a parent Document. Useful
      when a page is taken out of a Document for individual processing.
    paragraphs: A list of visually detected text paragraphs on the page. A
      collection of lines that a human would perceive as a paragraph.
    provenance: The history of this page.
    symbols: A list of visually detected symbols on the page.
    tables: A list of visually detected tables on the page.
    tokens: A list of visually detected tokens on the page.
    transforms: Transformation matrices that were applied to the original
      document image to produce Page.image.
    visualElements: A list of detected non-text visual elements e.g. checkbox,
      signature etc. on the page.
  """

  blocks = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageBlock', 1, repeated=True)
  detectedBarcodes = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageDetectedBarcode', 2, repeated=True)
  detectedLanguages = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageDetectedLanguage', 3, repeated=True)
  dimension = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageDimension', 4)
  formFields = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageFormField', 5, repeated=True)
  image = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageImage', 6)
  imageQualityScores = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageImageQualityScores', 7)
  layout = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageLayout', 8)
  lines = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageLine', 9, repeated=True)
  pageNumber = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  paragraphs = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageParagraph', 11, repeated=True)
  provenance = _messages.MessageField('GoogleCloudDocumentaiV1DocumentProvenance', 12)
  symbols = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageSymbol', 13, repeated=True)
  tables = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageTable', 14, repeated=True)
  tokens = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageToken', 15, repeated=True)
  transforms = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageMatrix', 16, repeated=True)
  visualElements = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageVisualElement', 17, repeated=True)


class GoogleCloudDocumentaiV1DocumentPageAnchor(_messages.Message):
  r"""Referencing the visual context of the entity in the Document.pages. Page
  anchors can be cross-page, consist of multiple bounding polygons and
  optionally reference specific layout element types.

  Fields:
    pageRefs: One or more references to visual page elements
  """

  pageRefs = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageAnchorPageRef', 1, repeated=True)


class GoogleCloudDocumentaiV1DocumentPageAnchorPageRef(_messages.Message):
  r"""Represents a weak reference to a page element within a document.

  Enums:
    LayoutTypeValueValuesEnum: Optional. The type of the layout element that
      is being referenced if any.

  Fields:
    boundingPoly: Optional. Identifies the bounding polygon of a layout
      element on the page. If `layout_type` is set, the bounding polygon must
      be exactly the same to the layout element it's referring to.
    confidence: Optional. Confidence of detected page element, if applicable.
      Range `[0, 1]`.
    layoutId: Optional. Deprecated. Use PageRef.bounding_poly instead.
    layoutType: Optional. The type of the layout element that is being
      referenced if any.
    page: Required. Index into the Document.pages element, for example using
      `Document.pages` to locate the related page element. This field is
      skipped when its value is the default `0`. See
      https://developers.google.com/protocol-buffers/docs/proto3#json.
  """

  class LayoutTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of the layout element that is being referenced if
    any.

    Values:
      LAYOUT_TYPE_UNSPECIFIED: Layout Unspecified.
      BLOCK: References a Page.blocks element.
      PARAGRAPH: References a Page.paragraphs element.
      LINE: References a Page.lines element.
      TOKEN: References a Page.tokens element.
      VISUAL_ELEMENT: References a Page.visual_elements element.
      TABLE: Refrrences a Page.tables element.
      FORM_FIELD: References a Page.form_fields element.
    """
    LAYOUT_TYPE_UNSPECIFIED = 0
    BLOCK = 1
    PARAGRAPH = 2
    LINE = 3
    TOKEN = 4
    VISUAL_ELEMENT = 5
    TABLE = 6
    FORM_FIELD = 7

  boundingPoly = _messages.MessageField('GoogleCloudDocumentaiV1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  layoutId = _messages.StringField(3)
  layoutType = _messages.EnumField('LayoutTypeValueValuesEnum', 4)
  page = _messages.IntegerField(5)


class GoogleCloudDocumentaiV1DocumentPageBlock(_messages.Message):
  r"""A block has a set of lines (collected into paragraphs) that have a
  common line-spacing and orientation.

  Fields:
    detectedLanguages: A list of detected languages together with confidence.
    layout: Layout for Block.
    provenance: The history of this annotation.
  """

  detectedLanguages = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageDetectedLanguage', 1, repeated=True)
  layout = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageLayout', 2)
  provenance = _messages.MessageField('GoogleCloudDocumentaiV1DocumentProvenance', 3)


class GoogleCloudDocumentaiV1DocumentPageDetectedBarcode(_messages.Message):
  r"""A detected barcode.

  Fields:
    barcode: Detailed barcode information of the DetectedBarcode.
    layout: Layout for DetectedBarcode.
  """

  barcode = _messages.MessageField('GoogleCloudDocumentaiV1Barcode', 1)
  layout = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageLayout', 2)


class GoogleCloudDocumentaiV1DocumentPageDetectedLanguage(_messages.Message):
  r"""Detected language for a structural component.

  Fields:
    confidence: Confidence of detected language. Range `[0, 1]`.
    languageCode: The [BCP-47 language
      code](https://www.unicode.org/reports/tr35/#Unicode_locale_identifier),
      such as `en-US` or `sr-Latn`.
  """

  confidence = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  languageCode = _messages.StringField(2)


class GoogleCloudDocumentaiV1DocumentPageDimension(_messages.Message):
  r"""Dimension for the page.

  Fields:
    height: Page height.
    unit: Dimension unit.
    width: Page width.
  """

  height = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  unit = _messages.StringField(2)
  width = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudDocumentaiV1DocumentPageFormField(_messages.Message):
  r"""A form field detected on the page.

  Fields:
    correctedKeyText: Created for Labeling UI to export key text. If
      corrections were made to the text identified by the
      `field_name.text_anchor`, this field will contain the correction.
    correctedValueText: Created for Labeling UI to export value text. If
      corrections were made to the text identified by the
      `field_value.text_anchor`, this field will contain the correction.
    fieldName: Layout for the FormField name. e.g. `Address`, `Email`, `Grand
      total`, `Phone number`, etc.
    fieldValue: Layout for the FormField value.
    nameDetectedLanguages: A list of detected languages for name together with
      confidence.
    provenance: The history of this annotation.
    valueDetectedLanguages: A list of detected languages for value together
      with confidence.
    valueType: If the value is non-textual, this field represents the type.
      Current valid values are: - blank (this indicates the `field_value` is
      normal text) - `unfilled_checkbox` - `filled_checkbox`
  """

  correctedKeyText = _messages.StringField(1)
  correctedValueText = _messages.StringField(2)
  fieldName = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageLayout', 3)
  fieldValue = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageLayout', 4)
  nameDetectedLanguages = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageDetectedLanguage', 5, repeated=True)
  provenance = _messages.MessageField('GoogleCloudDocumentaiV1DocumentProvenance', 6)
  valueDetectedLanguages = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageDetectedLanguage', 7, repeated=True)
  valueType = _messages.StringField(8)


class GoogleCloudDocumentaiV1DocumentPageImage(_messages.Message):
  r"""Rendered image contents for this page.

  Fields:
    content: Raw byte content of the image.
    height: Height of the image in pixels.
    mimeType: Encoding [media type (MIME
      type)](https://www.iana.org/assignments/media-types/media-types.xhtml)
      for the image.
    width: Width of the image in pixels.
  """

  content = _messages.BytesField(1)
  height = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  mimeType = _messages.StringField(3)
  width = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1DocumentPageImageQualityScores(_messages.Message):
  r"""Image quality scores for the page image.

  Fields:
    detectedDefects: A list of detected defects.
    qualityScore: The overall quality score. Range `[0, 1]` where `1` is
      perfect quality.
  """

  detectedDefects = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageImageQualityScoresDetectedDefect', 1, repeated=True)
  qualityScore = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class GoogleCloudDocumentaiV1DocumentPageImageQualityScoresDetectedDefect(_messages.Message):
  r"""Image Quality Defects

  Fields:
    confidence: Confidence of detected defect. Range `[0, 1]` where `1`
      indicates strong confidence that the defect exists.
    type: Name of the defect type. Supported values are: -
      `quality/defect_blurry` - `quality/defect_noisy` - `quality/defect_dark`
      - `quality/defect_faint` - `quality/defect_text_too_small` -
      `quality/defect_document_cutoff` - `quality/defect_text_cutoff` -
      `quality/defect_glare`
  """

  confidence = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  type = _messages.StringField(2)


class GoogleCloudDocumentaiV1DocumentPageLayout(_messages.Message):
  r"""Visual element describing a layout unit on a page.

  Enums:
    OrientationValueValuesEnum: Detected orientation for the Layout.

  Fields:
    boundingPoly: The bounding polygon for the Layout.
    confidence: Confidence of the current Layout within context of the object
      this layout is for. e.g. confidence can be for a single token, a table,
      a visual element, etc. depending on context. Range `[0, 1]`.
    orientation: Detected orientation for the Layout.
    textAnchor: Text anchor indexing into the Document.text.
  """

  class OrientationValueValuesEnum(_messages.Enum):
    r"""Detected orientation for the Layout.

    Values:
      ORIENTATION_UNSPECIFIED: Unspecified orientation.
      PAGE_UP: Orientation is aligned with page up.
      PAGE_RIGHT: Orientation is aligned with page right. Turn the head 90
        degrees clockwise from upright to read.
      PAGE_DOWN: Orientation is aligned with page down. Turn the head 180
        degrees from upright to read.
      PAGE_LEFT: Orientation is aligned with page left. Turn the head 90
        degrees counterclockwise from upright to read.
    """
    ORIENTATION_UNSPECIFIED = 0
    PAGE_UP = 1
    PAGE_RIGHT = 2
    PAGE_DOWN = 3
    PAGE_LEFT = 4

  boundingPoly = _messages.MessageField('GoogleCloudDocumentaiV1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  orientation = _messages.EnumField('OrientationValueValuesEnum', 3)
  textAnchor = _messages.MessageField('GoogleCloudDocumentaiV1DocumentTextAnchor', 4)


class GoogleCloudDocumentaiV1DocumentPageLine(_messages.Message):
  r"""A collection of tokens that a human would perceive as a line. Does not
  cross column boundaries, can be horizontal, vertical, etc.

  Fields:
    detectedLanguages: A list of detected languages together with confidence.
    layout: Layout for Line.
    provenance: The history of this annotation.
  """

  detectedLanguages = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageDetectedLanguage', 1, repeated=True)
  layout = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageLayout', 2)
  provenance = _messages.MessageField('GoogleCloudDocumentaiV1DocumentProvenance', 3)


class GoogleCloudDocumentaiV1DocumentPageMatrix(_messages.Message):
  r"""Representation for transformation matrix, intended to be compatible and
  used with OpenCV format for image manipulation.

  Fields:
    cols: Number of columns in the matrix.
    data: The matrix data.
    rows: Number of rows in the matrix.
    type: This encodes information about what data type the matrix uses. For
      example, 0 (CV_8U) is an unsigned 8-bit image. For the full list of
      OpenCV primitive data types, please refer to
      https://docs.opencv.org/4.3.0/d1/d1b/group__core__hal__interface.html
  """

  cols = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  data = _messages.BytesField(2)
  rows = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  type = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1DocumentPageParagraph(_messages.Message):
  r"""A collection of lines that a human would perceive as a paragraph.

  Fields:
    detectedLanguages: A list of detected languages together with confidence.
    layout: Layout for Paragraph.
    provenance: The history of this annotation.
  """

  detectedLanguages = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageDetectedLanguage', 1, repeated=True)
  layout = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageLayout', 2)
  provenance = _messages.MessageField('GoogleCloudDocumentaiV1DocumentProvenance', 3)


class GoogleCloudDocumentaiV1DocumentPageSymbol(_messages.Message):
  r"""A detected symbol.

  Fields:
    detectedLanguages: A list of detected languages together with confidence.
    layout: Layout for Symbol.
  """

  detectedLanguages = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageDetectedLanguage', 1, repeated=True)
  layout = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageLayout', 2)


class GoogleCloudDocumentaiV1DocumentPageTable(_messages.Message):
  r"""A table representation similar to HTML table structure.

  Fields:
    bodyRows: Body rows of the table.
    detectedLanguages: A list of detected languages together with confidence.
    headerRows: Header rows of the table.
    layout: Layout for Table.
    provenance: The history of this table.
  """

  bodyRows = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageTableTableRow', 1, repeated=True)
  detectedLanguages = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageDetectedLanguage', 2, repeated=True)
  headerRows = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageTableTableRow', 3, repeated=True)
  layout = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageLayout', 4)
  provenance = _messages.MessageField('GoogleCloudDocumentaiV1DocumentProvenance', 5)


class GoogleCloudDocumentaiV1DocumentPageTableTableCell(_messages.Message):
  r"""A cell representation inside the table.

  Fields:
    colSpan: How many columns this cell spans.
    detectedLanguages: A list of detected languages together with confidence.
    layout: Layout for TableCell.
    rowSpan: How many rows this cell spans.
  """

  colSpan = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  detectedLanguages = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageDetectedLanguage', 2, repeated=True)
  layout = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageLayout', 3)
  rowSpan = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1DocumentPageTableTableRow(_messages.Message):
  r"""A row of table cells.

  Fields:
    cells: Cells that make up this row.
  """

  cells = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageTableTableCell', 1, repeated=True)


class GoogleCloudDocumentaiV1DocumentPageToken(_messages.Message):
  r"""A detected token.

  Fields:
    detectedBreak: Detected break at the end of a Token.
    detectedLanguages: A list of detected languages together with confidence.
    layout: Layout for Token.
    provenance: The history of this annotation.
    styleInfo: Text style attributes.
  """

  detectedBreak = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageTokenDetectedBreak', 1)
  detectedLanguages = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageDetectedLanguage', 2, repeated=True)
  layout = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageLayout', 3)
  provenance = _messages.MessageField('GoogleCloudDocumentaiV1DocumentProvenance', 4)
  styleInfo = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageTokenStyleInfo', 5)


class GoogleCloudDocumentaiV1DocumentPageTokenDetectedBreak(_messages.Message):
  r"""Detected break at the end of a Token.

  Enums:
    TypeValueValuesEnum: Detected break type.

  Fields:
    type: Detected break type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Detected break type.

    Values:
      TYPE_UNSPECIFIED: Unspecified break type.
      SPACE: A single whitespace.
      WIDE_SPACE: A wider whitespace.
      HYPHEN: A hyphen that indicates that a token has been split across
        lines.
    """
    TYPE_UNSPECIFIED = 0
    SPACE = 1
    WIDE_SPACE = 2
    HYPHEN = 3

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class GoogleCloudDocumentaiV1DocumentPageTokenStyleInfo(_messages.Message):
  r"""Font and other text style attributes.

  Fields:
    backgroundColor: Color of the background.
    bold: Whether the text is bold (equivalent to font_weight is at least
      `700`).
    fontSize: Font size in points (`1` point is `\xb9\u2044\u2087\u2082`
      inches).
    fontType: Name or style of the font.
    fontWeight: TrueType weight on a scale `100` (thin) to `1000` (ultra-
      heavy). Normal is `400`, bold is `700`.
    handwritten: Whether the text is handwritten.
    italic: Whether the text is italic.
    letterSpacing: Letter spacing in points.
    pixelFontSize: Font size in pixels, equal to _unrounded font_size_ *
      _resolution_ \xf7 `72.0`.
    smallcaps: Whether the text is in small caps. This feature is not
      supported yet.
    strikeout: Whether the text is strikethrough. This feature is not
      supported yet.
    subscript: Whether the text is a subscript. This feature is not supported
      yet.
    superscript: Whether the text is a superscript. This feature is not
      supported yet.
    textColor: Color of the text.
    underlined: Whether the text is underlined.
  """

  backgroundColor = _messages.MessageField('GoogleTypeColor', 1)
  bold = _messages.BooleanField(2)
  fontSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  fontType = _messages.StringField(4)
  fontWeight = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  handwritten = _messages.BooleanField(6)
  italic = _messages.BooleanField(7)
  letterSpacing = _messages.FloatField(8)
  pixelFontSize = _messages.FloatField(9)
  smallcaps = _messages.BooleanField(10)
  strikeout = _messages.BooleanField(11)
  subscript = _messages.BooleanField(12)
  superscript = _messages.BooleanField(13)
  textColor = _messages.MessageField('GoogleTypeColor', 14)
  underlined = _messages.BooleanField(15)


class GoogleCloudDocumentaiV1DocumentPageVisualElement(_messages.Message):
  r"""Detected non-text visual elements e.g. checkbox, signature etc. on the
  page.

  Fields:
    detectedLanguages: A list of detected languages together with confidence.
    layout: Layout for VisualElement.
    type: Type of the VisualElement.
  """

  detectedLanguages = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageDetectedLanguage', 1, repeated=True)
  layout = _messages.MessageField('GoogleCloudDocumentaiV1DocumentPageLayout', 2)
  type = _messages.StringField(3)


class GoogleCloudDocumentaiV1DocumentProvenance(_messages.Message):
  r"""Structure to identify provenance relationships between annotations in
  different revisions.

  Enums:
    TypeValueValuesEnum: The type of provenance operation.

  Fields:
    id: The Id of this operation. Needs to be unique within the scope of the
      revision.
    parents: References to the original elements that are replaced.
    revision: The index of the revision that produced this element.
    type: The type of provenance operation.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of provenance operation.

    Values:
      OPERATION_TYPE_UNSPECIFIED: Operation type unspecified. If no operation
        is specified a provenance entry is simply used to match against a
        `parent`.
      ADD: Add an element.
      REMOVE: Remove an element identified by `parent`.
      UPDATE: Updates any fields within the given provenance scope of the
        message. It overwrites the fields rather than replacing them. Use this
        when you want to update a field value of an entity without also
        updating all the child properties.
      REPLACE: Currently unused. Replace an element identified by `parent`.
      EVAL_REQUESTED: Deprecated. Request human review for the element
        identified by `parent`.
      EVAL_APPROVED: Deprecated. Element is reviewed and approved at human
        review, confidence will be set to 1.0.
      EVAL_SKIPPED: Deprecated. Element is skipped in the validation process.
    """
    OPERATION_TYPE_UNSPECIFIED = 0
    ADD = 1
    REMOVE = 2
    UPDATE = 3
    REPLACE = 4
    EVAL_REQUESTED = 5
    EVAL_APPROVED = 6
    EVAL_SKIPPED = 7

  id = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  parents = _messages.MessageField('GoogleCloudDocumentaiV1DocumentProvenanceParent', 2, repeated=True)
  revision = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class GoogleCloudDocumentaiV1DocumentProvenanceParent(_messages.Message):
  r"""The parent element the current element is based on. Used for
  referencing/aligning, removal and replacement operations.

  Fields:
    id: The id of the parent provenance.
    index: The index of the parent item in the corresponding item list (eg.
      list of entities, properties within entities, etc.) in the parent
      revision.
    revision: The index of the index into current revision's parent_ids list.
  """

  id = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  index = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  revision = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1DocumentRevision(_messages.Message):
  r"""Contains past or forward revisions of this document.

  Fields:
    agent: If the change was made by a person specify the name or id of that
      person.
    createTime: The time that the revision was created, internally generated
      by doc proto storage at the time of create.
    humanReview: Human Review information of this revision.
    id: Id of the revision, internally generated by doc proto storage. Unique
      within the context of the document.
    parent: The revisions that this revision is based on. This can include one
      or more parent (when documents are merged.) This field represents the
      index into the `revisions` field.
    parentIds: The revisions that this revision is based on. Must include all
      the ids that have anything to do with this revision - eg. there are
      `provenance.parent.revision` fields that index into this field.
    processor: If the annotation was made by processor identify the processor
      by its resource name.
  """

  agent = _messages.StringField(1)
  createTime = _messages.StringField(2)
  humanReview = _messages.MessageField('GoogleCloudDocumentaiV1DocumentRevisionHumanReview', 3)
  id = _messages.StringField(4)
  parent = _messages.IntegerField(5, repeated=True, variant=_messages.Variant.INT32)
  parentIds = _messages.StringField(6, repeated=True)
  processor = _messages.StringField(7)


class GoogleCloudDocumentaiV1DocumentRevisionHumanReview(_messages.Message):
  r"""Human Review information of the document.

  Fields:
    state: Human review state. e.g. `requested`, `succeeded`, `rejected`.
    stateMessage: A message providing more details about the current state of
      processing. For example, the rejection reason when the state is
      `rejected`.
  """

  state = _messages.StringField(1)
  stateMessage = _messages.StringField(2)


class GoogleCloudDocumentaiV1DocumentSchema(_messages.Message):
  r"""The schema defines the output of the processed document by a processor.

  Fields:
    description: Description of the schema.
    displayName: Display name to show to users.
    entityTypes: Entity types of the schema.
    metadata: Metadata of the schema.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  entityTypes = _messages.MessageField('GoogleCloudDocumentaiV1DocumentSchemaEntityType', 3, repeated=True)
  metadata = _messages.MessageField('GoogleCloudDocumentaiV1DocumentSchemaMetadata', 4)


class GoogleCloudDocumentaiV1DocumentSchemaEntityType(_messages.Message):
  r"""EntityType is the wrapper of a label of the corresponding model with
  detailed attributes and limitations for entity-based processors. Multiple
  types can also compose a dependency tree to represent nested types.

  Fields:
    baseTypes: The entity type that this type is derived from. For now, one
      and only one should be set.
    displayName: User defined name for the type.
    enumValues: If specified, lists all the possible values for this entity.
      This should not be more than a handful of values. If the number of
      values is >10 or could change frequently use the
      `EntityType.value_ontology` field and specify a list of all possible
      values in a value ontology file.
    name: Name of the type. It must be unique within the schema file and
      cannot be a "Common Type". The following naming conventions are used: -
      Use `snake_casing`. - Name matching is case-sensitive. - Maximum 64
      characters. - Must start with a letter. - Allowed characters: ASCII
      letters `[a-z0-9_-]`. (For backward compatibility internal
      infrastructure and tooling can handle any ascii character.) - The `/` is
      sometimes used to denote a property of a type. For example
      `line_item/amount`. This convention is deprecated, but will still be
      honored for backward compatibility.
    properties: Description the nested structure, or composition of an entity.
  """

  baseTypes = _messages.StringField(1, repeated=True)
  displayName = _messages.StringField(2)
  enumValues = _messages.MessageField('GoogleCloudDocumentaiV1DocumentSchemaEntityTypeEnumValues', 3)
  name = _messages.StringField(4)
  properties = _messages.MessageField('GoogleCloudDocumentaiV1DocumentSchemaEntityTypeProperty', 5, repeated=True)


class GoogleCloudDocumentaiV1DocumentSchemaEntityTypeEnumValues(_messages.Message):
  r"""Defines the a list of enum values.

  Fields:
    values: The individual values that this enum values type can include.
  """

  values = _messages.StringField(1, repeated=True)


class GoogleCloudDocumentaiV1DocumentSchemaEntityTypeProperty(_messages.Message):
  r"""Defines properties that can be part of the entity type.

  Enums:
    MethodValueValuesEnum: Specifies how the entity's value is obtained.
    OccurrenceTypeValueValuesEnum: Occurrence type limits the number of
      instances an entity type appears in the document.

  Fields:
    displayName: User defined name for the property.
    method: Specifies how the entity's value is obtained.
    name: The name of the property. Follows the same guidelines as the
      EntityType name.
    occurrenceType: Occurrence type limits the number of instances an entity
      type appears in the document.
    valueType: A reference to the value type of the property. This type is
      subject to the same conventions as the `Entity.base_types` field.
  """

  class MethodValueValuesEnum(_messages.Enum):
    r"""Specifies how the entity's value is obtained.

    Values:
      METHOD_UNSPECIFIED: Unspecified method. It defaults to `EXTRACT`.
      EXTRACT: The entity's value is directly extracted as-is from the
        document text.
      DERIVE: The entity's value is derived through inference and is not
        necessarily an exact text extraction from the document.
    """
    METHOD_UNSPECIFIED = 0
    EXTRACT = 1
    DERIVE = 2

  class OccurrenceTypeValueValuesEnum(_messages.Enum):
    r"""Occurrence type limits the number of instances an entity type appears
    in the document.

    Values:
      OCCURRENCE_TYPE_UNSPECIFIED: Unspecified occurrence type.
      OPTIONAL_ONCE: There will be zero or one instance of this entity type.
        The same entity instance may be mentioned multiple times.
      OPTIONAL_MULTIPLE: The entity type will appear zero or multiple times.
      REQUIRED_ONCE: The entity type will only appear exactly once. The same
        entity instance may be mentioned multiple times.
      REQUIRED_MULTIPLE: The entity type will appear once or more times.
    """
    OCCURRENCE_TYPE_UNSPECIFIED = 0
    OPTIONAL_ONCE = 1
    OPTIONAL_MULTIPLE = 2
    REQUIRED_ONCE = 3
    REQUIRED_MULTIPLE = 4

  displayName = _messages.StringField(1)
  method = _messages.EnumField('MethodValueValuesEnum', 2)
  name = _messages.StringField(3)
  occurrenceType = _messages.EnumField('OccurrenceTypeValueValuesEnum', 4)
  valueType = _messages.StringField(5)


class GoogleCloudDocumentaiV1DocumentSchemaMetadata(_messages.Message):
  r"""Metadata for global schema behavior.

  Fields:
    documentAllowMultipleLabels: If true, on a given page, there can be
      multiple `document` annotations covering it.
    documentSplitter: If true, a `document` entity type can be applied to
      subdocument (splitting). Otherwise, it can only be applied to the entire
      document (classification).
    prefixedNamingOnProperties: If set, all the nested entities must be
      prefixed with the parents.
    skipNamingValidation: If set, we will skip the naming format validation in
      the schema. So the string values in `DocumentSchema.EntityType.name` and
      `DocumentSchema.EntityType.Property.name` will not be checked.
  """

  documentAllowMultipleLabels = _messages.BooleanField(1)
  documentSplitter = _messages.BooleanField(2)
  prefixedNamingOnProperties = _messages.BooleanField(3)
  skipNamingValidation = _messages.BooleanField(4)


class GoogleCloudDocumentaiV1DocumentShardInfo(_messages.Message):
  r"""For a large document, sharding may be performed to produce several
  document shards. Each document shard contains this field to detail which
  shard it is.

  Fields:
    shardCount: Total number of shards.
    shardIndex: The 0-based index of this shard.
    textOffset: The index of the first character in Document.text in the
      overall document global text.
  """

  shardCount = _messages.IntegerField(1)
  shardIndex = _messages.IntegerField(2)
  textOffset = _messages.IntegerField(3)


class GoogleCloudDocumentaiV1DocumentStyle(_messages.Message):
  r"""Annotation for common text style attributes. This adheres to CSS
  conventions as much as possible.

  Fields:
    backgroundColor: Text background color.
    color: Text color.
    fontFamily: Font family such as `Arial`, `Times New Roman`.
      https://www.w3schools.com/cssref/pr_font_font-family.asp
    fontSize: Font size.
    fontWeight: [Font
      weight](https://www.w3schools.com/cssref/pr_font_weight.asp). Possible
      values are `normal`, `bold`, `bolder`, and `lighter`.
    textAnchor: Text anchor indexing into the Document.text.
    textDecoration: [Text
      decoration](https://www.w3schools.com/cssref/pr_text_text-
      decoration.asp). Follows CSS standard.
    textStyle: [Text style](https://www.w3schools.com/cssref/pr_font_font-
      style.asp). Possible values are `normal`, `italic`, and `oblique`.
  """

  backgroundColor = _messages.MessageField('GoogleTypeColor', 1)
  color = _messages.MessageField('GoogleTypeColor', 2)
  fontFamily = _messages.StringField(3)
  fontSize = _messages.MessageField('GoogleCloudDocumentaiV1DocumentStyleFontSize', 4)
  fontWeight = _messages.StringField(5)
  textAnchor = _messages.MessageField('GoogleCloudDocumentaiV1DocumentTextAnchor', 6)
  textDecoration = _messages.StringField(7)
  textStyle = _messages.StringField(8)


class GoogleCloudDocumentaiV1DocumentStyleFontSize(_messages.Message):
  r"""Font size with unit.

  Fields:
    size: Font size for the text.
    unit: Unit for the font size. Follows CSS naming (such as `in`, `px`, and
      `pt`).
  """

  size = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  unit = _messages.StringField(2)


class GoogleCloudDocumentaiV1DocumentTextAnchor(_messages.Message):
  r"""Text reference indexing into the Document.text.

  Fields:
    content: Contains the content of the text span so that users do not have
      to look it up in the text_segments. It is always populated for
      formFields.
    textSegments: The text segments from the Document.text.
  """

  content = _messages.StringField(1)
  textSegments = _messages.MessageField('GoogleCloudDocumentaiV1DocumentTextAnchorTextSegment', 2, repeated=True)


class GoogleCloudDocumentaiV1DocumentTextAnchorTextSegment(_messages.Message):
  r"""A text segment in the Document.text. The indices may be out of bounds
  which indicate that the text extends into another document shard for large
  sharded documents. See ShardInfo.text_offset

  Fields:
    endIndex: TextSegment half open end UTF-8 char index in the Document.text.
    startIndex: TextSegment start UTF-8 char index in the Document.text.
  """

  endIndex = _messages.IntegerField(1)
  startIndex = _messages.IntegerField(2)


class GoogleCloudDocumentaiV1DocumentTextChange(_messages.Message):
  r"""This message is used for text changes aka. OCR corrections.

  Fields:
    changedText: The text that replaces the text identified in the
      `text_anchor`.
    provenance: The history of this annotation.
    textAnchor: Provenance of the correction. Text anchor indexing into the
      Document.text. There can only be a single `TextAnchor.text_segments`
      element. If the start and end index of the text segment are the same,
      the text change is inserted before that index.
  """

  changedText = _messages.StringField(1)
  provenance = _messages.MessageField('GoogleCloudDocumentaiV1DocumentProvenance', 2, repeated=True)
  textAnchor = _messages.MessageField('GoogleCloudDocumentaiV1DocumentTextAnchor', 3)


class GoogleCloudDocumentaiV1EnableProcessorMetadata(_messages.Message):
  r"""The long-running operation metadata for the EnableProcessor method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1EnableProcessorRequest(_messages.Message):
  r"""Request message for the EnableProcessor method."""


class GoogleCloudDocumentaiV1EnableProcessorResponse(_messages.Message):
  r"""Response message for the EnableProcessor method. Intentionally empty
  proto for adding fields in future.
  """



class GoogleCloudDocumentaiV1EvaluateProcessorVersionMetadata(_messages.Message):
  r"""Metadata of the EvaluateProcessorVersion method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1EvaluateProcessorVersionRequest(_messages.Message):
  r"""Evaluates the given ProcessorVersion against the supplied documents.

  Fields:
    evaluationDocuments: Optional. The documents used in the evaluation. If
      unspecified, use the processor's dataset as evaluation input.
  """

  evaluationDocuments = _messages.MessageField('GoogleCloudDocumentaiV1BatchDocumentsInputConfig', 1)


class GoogleCloudDocumentaiV1EvaluateProcessorVersionResponse(_messages.Message):
  r"""Response of the EvaluateProcessorVersion method.

  Fields:
    evaluation: The resource name of the created evaluation.
  """

  evaluation = _messages.StringField(1)


class GoogleCloudDocumentaiV1Evaluation(_messages.Message):
  r"""An evaluation of a ProcessorVersion's performance.

  Messages:
    EntityMetricsValue: Metrics across confidence levels, for different
      entities.

  Fields:
    allEntitiesMetrics: Metrics for all the entities in aggregate.
    createTime: The time that the evaluation was created.
    documentCounters: Counters for the documents used in the evaluation.
    entityMetrics: Metrics across confidence levels, for different entities.
    kmsKeyName: The KMS key name used for encryption.
    kmsKeyVersionName: The KMS key version with which data is encrypted.
    name: The resource name of the evaluation. Format: `projects/{project}/loc
      ations/{location}/processors/{processor}/processorVersions/{processor_ve
      rsion}/evaluations/{evaluation}`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EntityMetricsValue(_messages.Message):
    r"""Metrics across confidence levels, for different entities.

    Messages:
      AdditionalProperty: An additional property for a EntityMetricsValue
        object.

    Fields:
      additionalProperties: Additional properties of type EntityMetricsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EntityMetricsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudDocumentaiV1EvaluationMultiConfidenceMetrics
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudDocumentaiV1EvaluationMultiConfidenceMetrics', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allEntitiesMetrics = _messages.MessageField('GoogleCloudDocumentaiV1EvaluationMultiConfidenceMetrics', 1)
  createTime = _messages.StringField(2)
  documentCounters = _messages.MessageField('GoogleCloudDocumentaiV1EvaluationCounters', 3)
  entityMetrics = _messages.MessageField('EntityMetricsValue', 4)
  kmsKeyName = _messages.StringField(5)
  kmsKeyVersionName = _messages.StringField(6)
  name = _messages.StringField(7)


class GoogleCloudDocumentaiV1EvaluationConfidenceLevelMetrics(_messages.Message):
  r"""Evaluations metrics, at a specific confidence level.

  Fields:
    confidenceLevel: The confidence level.
    metrics: The metrics at the specific confidence level.
  """

  confidenceLevel = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  metrics = _messages.MessageField('GoogleCloudDocumentaiV1EvaluationMetrics', 2)


class GoogleCloudDocumentaiV1EvaluationCounters(_messages.Message):
  r"""Evaluation counters for the documents that were used.

  Fields:
    evaluatedDocumentsCount: How many documents were used in the evaluation.
    failedDocumentsCount: How many documents were not included in the
      evaluation as Document AI failed to process them.
    inputDocumentsCount: How many documents were sent for evaluation.
    invalidDocumentsCount: How many documents were not included in the
      evaluation as they didn't pass validation.
  """

  evaluatedDocumentsCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  failedDocumentsCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  inputDocumentsCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  invalidDocumentsCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1EvaluationMetrics(_messages.Message):
  r"""Evaluation metrics, either in aggregate or about a specific entity.

  Fields:
    f1Score: The calculated f1 score.
    falseNegativesCount: The amount of false negatives.
    falsePositivesCount: The amount of false positives.
    groundTruthDocumentCount: The amount of documents with a ground truth
      occurrence.
    groundTruthOccurrencesCount: The amount of occurrences in ground truth
      documents.
    precision: The calculated precision.
    predictedDocumentCount: The amount of documents with a predicted
      occurrence.
    predictedOccurrencesCount: The amount of occurrences in predicted
      documents.
    recall: The calculated recall.
    totalDocumentsCount: The amount of documents that had an occurrence of
      this label.
    truePositivesCount: The amount of true positives.
  """

  f1Score = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  falseNegativesCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  falsePositivesCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  groundTruthDocumentCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  groundTruthOccurrencesCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  precision = _messages.FloatField(6, variant=_messages.Variant.FLOAT)
  predictedDocumentCount = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  predictedOccurrencesCount = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  recall = _messages.FloatField(9, variant=_messages.Variant.FLOAT)
  totalDocumentsCount = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  truePositivesCount = _messages.IntegerField(11, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1EvaluationMultiConfidenceMetrics(_messages.Message):
  r"""Metrics across multiple confidence levels.

  Enums:
    MetricsTypeValueValuesEnum: The metrics type for the label.

  Fields:
    auprc: The calculated area under the precision recall curve (AUPRC),
      computed by integrating over all confidence thresholds.
    auprcExact: The AUPRC for metrics with fuzzy matching disabled, i.e.,
      exact matching only.
    confidenceLevelMetrics: Metrics across confidence levels with fuzzy
      matching enabled.
    confidenceLevelMetricsExact: Metrics across confidence levels with only
      exact matching.
    estimatedCalibrationError: The Estimated Calibration Error (ECE) of the
      confidence of the predicted entities.
    estimatedCalibrationErrorExact: The ECE for the predicted entities with
      fuzzy matching disabled, i.e., exact matching only.
    metricsType: The metrics type for the label.
  """

  class MetricsTypeValueValuesEnum(_messages.Enum):
    r"""The metrics type for the label.

    Values:
      METRICS_TYPE_UNSPECIFIED: The metrics type is unspecified. By default,
        metrics without a particular specification are for leaf entity types
        (i.e., top-level entity types without child types, or child types
        which are not parent types themselves).
      AGGREGATE: Indicates whether metrics for this particular label type
        represent an aggregate of metrics for other types instead of being
        based on actual TP/FP/FN values for the label type. Metrics for parent
        (i.e., non-leaf) entity types are an aggregate of metrics for their
        children.
    """
    METRICS_TYPE_UNSPECIFIED = 0
    AGGREGATE = 1

  auprc = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  auprcExact = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  confidenceLevelMetrics = _messages.MessageField('GoogleCloudDocumentaiV1EvaluationConfidenceLevelMetrics', 3, repeated=True)
  confidenceLevelMetricsExact = _messages.MessageField('GoogleCloudDocumentaiV1EvaluationConfidenceLevelMetrics', 4, repeated=True)
  estimatedCalibrationError = _messages.FloatField(5, variant=_messages.Variant.FLOAT)
  estimatedCalibrationErrorExact = _messages.FloatField(6, variant=_messages.Variant.FLOAT)
  metricsType = _messages.EnumField('MetricsTypeValueValuesEnum', 7)


class GoogleCloudDocumentaiV1EvaluationReference(_messages.Message):
  r"""Gives a short summary of an evaluation, and links to the evaluation
  itself.

  Fields:
    aggregateMetrics: An aggregate of the statistics for the evaluation with
      fuzzy matching on.
    aggregateMetricsExact: An aggregate of the statistics for the evaluation
      with fuzzy matching off.
    evaluation: The resource name of the evaluation.
    operation: The resource name of the Long Running Operation for the
      evaluation.
  """

  aggregateMetrics = _messages.MessageField('GoogleCloudDocumentaiV1EvaluationMetrics', 1)
  aggregateMetricsExact = _messages.MessageField('GoogleCloudDocumentaiV1EvaluationMetrics', 2)
  evaluation = _messages.StringField(3)
  operation = _messages.StringField(4)


class GoogleCloudDocumentaiV1FetchProcessorTypesResponse(_messages.Message):
  r"""Response message for the FetchProcessorTypes method.

  Fields:
    processorTypes: The list of processor types.
  """

  processorTypes = _messages.MessageField('GoogleCloudDocumentaiV1ProcessorType', 1, repeated=True)


class GoogleCloudDocumentaiV1GcsDocument(_messages.Message):
  r"""Specifies a document stored on Cloud Storage.

  Fields:
    gcsUri: The Cloud Storage object uri.
    mimeType: An IANA MIME type (RFC6838) of the content.
  """

  gcsUri = _messages.StringField(1)
  mimeType = _messages.StringField(2)


class GoogleCloudDocumentaiV1GcsDocuments(_messages.Message):
  r"""Specifies a set of documents on Cloud Storage.

  Fields:
    documents: The list of documents.
  """

  documents = _messages.MessageField('GoogleCloudDocumentaiV1GcsDocument', 1, repeated=True)


class GoogleCloudDocumentaiV1GcsPrefix(_messages.Message):
  r"""Specifies all documents on Cloud Storage with a common prefix.

  Fields:
    gcsUriPrefix: The URI prefix.
  """

  gcsUriPrefix = _messages.StringField(1)


class GoogleCloudDocumentaiV1HumanReviewStatus(_messages.Message):
  r"""The status of human review on a processed document.

  Enums:
    StateValueValuesEnum: The state of human review on the processing request.

  Fields:
    humanReviewOperation: The name of the operation triggered by the processed
      document. This field is populated only when the state is
      `HUMAN_REVIEW_IN_PROGRESS`. It has the same response type and metadata
      as the long-running operation returned by ReviewDocument.
    state: The state of human review on the processing request.
    stateMessage: A message providing more details about the human review
      state.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of human review on the processing request.

    Values:
      STATE_UNSPECIFIED: Human review state is unspecified. Most likely due to
        an internal error.
      SKIPPED: Human review is skipped for the document. This can happen
        because human review isn't enabled on the processor or the processing
        request has been set to skip this document.
      VALIDATION_PASSED: Human review validation is triggered and passed, so
        no review is needed.
      IN_PROGRESS: Human review validation is triggered and the document is
        under review.
      ERROR: Some error happened during triggering human review, see the
        state_message for details.
    """
    STATE_UNSPECIFIED = 0
    SKIPPED = 1
    VALIDATION_PASSED = 2
    IN_PROGRESS = 3
    ERROR = 4

  humanReviewOperation = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  stateMessage = _messages.StringField(3)


class GoogleCloudDocumentaiV1ListEvaluationsResponse(_messages.Message):
  r"""The response from `ListEvaluations`.

  Fields:
    evaluations: The evaluations requested.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  evaluations = _messages.MessageField('GoogleCloudDocumentaiV1Evaluation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudDocumentaiV1ListProcessorTypesResponse(_messages.Message):
  r"""Response message for the ListProcessorTypes method.

  Fields:
    nextPageToken: Points to the next page, otherwise empty.
    processorTypes: The processor types.
  """

  nextPageToken = _messages.StringField(1)
  processorTypes = _messages.MessageField('GoogleCloudDocumentaiV1ProcessorType', 2, repeated=True)


class GoogleCloudDocumentaiV1ListProcessorVersionsResponse(_messages.Message):
  r"""Response message for the ListProcessorVersions method.

  Fields:
    nextPageToken: Points to the next processor, otherwise empty.
    processorVersions: The list of processors.
  """

  nextPageToken = _messages.StringField(1)
  processorVersions = _messages.MessageField('GoogleCloudDocumentaiV1ProcessorVersion', 2, repeated=True)


class GoogleCloudDocumentaiV1ListProcessorsResponse(_messages.Message):
  r"""Response message for the ListProcessors method.

  Fields:
    nextPageToken: Points to the next processor, otherwise empty.
    processors: The list of processors.
  """

  nextPageToken = _messages.StringField(1)
  processors = _messages.MessageField('GoogleCloudDocumentaiV1Processor', 2, repeated=True)


class GoogleCloudDocumentaiV1NormalizedVertex(_messages.Message):
  r"""A vertex represents a 2D point in the image. NOTE: the normalized vertex
  coordinates are relative to the original image and range from 0 to 1.

  Fields:
    x: X coordinate.
    y: Y coordinate (starts from the top of the image).
  """

  x = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  y = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class GoogleCloudDocumentaiV1OcrConfig(_messages.Message):
  r"""Config for Document OCR.

  Fields:
    advancedOcrOptions: A list of advanced OCR options to further fine-tune
      OCR behavior. Current valid values are: - `legacy_layout`: a heuristics
      layout detection algorithm, which serves as an alternative to the
      current ML-based layout detection algorithm. Customers can choose the
      best suitable layout algorithm based on their situation.
    computeStyleInfo: Turn on font identification model and return font style
      information. Deprecated, use PremiumFeatures.compute_style_info instead.
    disableCharacterBoxesDetection: Turn off character box detector in OCR
      engine. Character box detection is enabled by default in OCR 2.0 (and
      later) processors.
    enableImageQualityScores: Enables intelligent document quality scores
      after OCR. Can help with diagnosing why OCR responses are of poor
      quality for a given input. Adds additional latency comparable to regular
      OCR to the process call.
    enableNativePdfParsing: Enables special handling for PDFs with existing
      text information. Results in better text extraction quality in such PDF
      inputs.
    enableSymbol: Includes symbol level OCR information if set to true.
    hints: Hints for the OCR model.
    premiumFeatures: Configurations for premium OCR features.
  """

  advancedOcrOptions = _messages.StringField(1, repeated=True)
  computeStyleInfo = _messages.BooleanField(2)
  disableCharacterBoxesDetection = _messages.BooleanField(3)
  enableImageQualityScores = _messages.BooleanField(4)
  enableNativePdfParsing = _messages.BooleanField(5)
  enableSymbol = _messages.BooleanField(6)
  hints = _messages.MessageField('GoogleCloudDocumentaiV1OcrConfigHints', 7)
  premiumFeatures = _messages.MessageField('GoogleCloudDocumentaiV1OcrConfigPremiumFeatures', 8)


class GoogleCloudDocumentaiV1OcrConfigHints(_messages.Message):
  r"""Hints for OCR Engine

  Fields:
    languageHints: List of BCP-47 language codes to use for OCR. In most
      cases, not specifying it yields the best results since it enables
      automatic language detection. For languages based on the Latin alphabet,
      setting hints is not needed. In rare cases, when the language of the
      text in the image is known, setting a hint will help get better results
      (although it will be a significant hindrance if the hint is wrong).
  """

  languageHints = _messages.StringField(1, repeated=True)


class GoogleCloudDocumentaiV1OcrConfigPremiumFeatures(_messages.Message):
  r"""Configurations for premium OCR features.

  Fields:
    computeStyleInfo: Turn on font identification model and return font style
      information.
    enableMathOcr: Turn on the model that can extract LaTeX math formulas.
    enableSelectionMarkDetection: Turn on selection mark detector in OCR
      engine. Only available in OCR 2.0 (and later) processors.
  """

  computeStyleInfo = _messages.BooleanField(1)
  enableMathOcr = _messages.BooleanField(2)
  enableSelectionMarkDetection = _messages.BooleanField(3)


class GoogleCloudDocumentaiV1ProcessOptions(_messages.Message):
  r"""Options for Process API

  Fields:
    fromEnd: Only process certain pages from the end, same as above.
    fromStart: Only process certain pages from the start. Process all if the
      document has fewer pages.
    individualPageSelector: Which pages to process (1-indexed).
    layoutConfig: Optional. Only applicable to `LAYOUT_PARSER_PROCESSOR`.
      Returns error if set on other processor types.
    ocrConfig: Only applicable to `OCR_PROCESSOR` and `FORM_PARSER_PROCESSOR`.
      Returns error if set on other processor types.
    schemaOverride: Optional. Override the schema of the ProcessorVersion.
      Will return an Invalid Argument error if this field is set when the
      underlying ProcessorVersion doesn't support schema override.
  """

  fromEnd = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  fromStart = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  individualPageSelector = _messages.MessageField('GoogleCloudDocumentaiV1ProcessOptionsIndividualPageSelector', 3)
  layoutConfig = _messages.MessageField('GoogleCloudDocumentaiV1ProcessOptionsLayoutConfig', 4)
  ocrConfig = _messages.MessageField('GoogleCloudDocumentaiV1OcrConfig', 5)
  schemaOverride = _messages.MessageField('GoogleCloudDocumentaiV1DocumentSchema', 6)


class GoogleCloudDocumentaiV1ProcessOptionsIndividualPageSelector(_messages.Message):
  r"""A list of individual page numbers.

  Fields:
    pages: Optional. Indices of the pages (starting from 1).
  """

  pages = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1ProcessOptionsLayoutConfig(_messages.Message):
  r"""Serving config for layout parser processor.

  Fields:
    chunkingConfig: Optional. Config for chunking in layout parser processor.
    returnBoundingBoxes: Optional. Whether to include bounding boxes in layout
      parser processor response.
    returnImages: Optional. Whether to include images in layout parser
      processor response.
  """

  chunkingConfig = _messages.MessageField('GoogleCloudDocumentaiV1ProcessOptionsLayoutConfigChunkingConfig', 1)
  returnBoundingBoxes = _messages.BooleanField(2)
  returnImages = _messages.BooleanField(3)


class GoogleCloudDocumentaiV1ProcessOptionsLayoutConfigChunkingConfig(_messages.Message):
  r"""Serving config for chunking.

  Fields:
    chunkSize: Optional. The chunk sizes to use when splitting documents, in
      order of level.
    includeAncestorHeadings: Optional. Whether or not to include ancestor
      headings when splitting.
  """

  chunkSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  includeAncestorHeadings = _messages.BooleanField(2)


class GoogleCloudDocumentaiV1ProcessRequest(_messages.Message):
  r"""Request message for the ProcessDocument method.

  Messages:
    LabelsValue: Optional. The labels with user-defined metadata for the
      request. Label keys and values can be no longer than 63 characters
      (Unicode codepoints) and can only contain lowercase letters, numeric
      characters, underscores, and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter.

  Fields:
    fieldMask: Specifies which fields to include in the
      ProcessResponse.document output. Only supports top-level document and
      pages field, so it must be in the form of `{document_field_name}` or
      `pages.{page_field_name}`.
    gcsDocument: A raw document on Google Cloud Storage.
    imagelessMode: Optional. Option to remove images from the document.
    inlineDocument: An inline document proto.
    labels: Optional. The labels with user-defined metadata for the request.
      Label keys and values can be no longer than 63 characters (Unicode
      codepoints) and can only contain lowercase letters, numeric characters,
      underscores, and dashes. International characters are allowed. Label
      values are optional. Label keys must start with a letter.
    processOptions: Inference-time options for the process API
    rawDocument: A raw document content (bytes).
    skipHumanReview: Whether human review should be skipped for this request.
      Default to `false`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels with user-defined metadata for the request. Label
    keys and values can be no longer than 63 characters (Unicode codepoints)
    and can only contain lowercase letters, numeric characters, underscores,
    and dashes. International characters are allowed. Label values are
    optional. Label keys must start with a letter.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  fieldMask = _messages.StringField(1)
  gcsDocument = _messages.MessageField('GoogleCloudDocumentaiV1GcsDocument', 2)
  imagelessMode = _messages.BooleanField(3)
  inlineDocument = _messages.MessageField('GoogleCloudDocumentaiV1Document', 4)
  labels = _messages.MessageField('LabelsValue', 5)
  processOptions = _messages.MessageField('GoogleCloudDocumentaiV1ProcessOptions', 6)
  rawDocument = _messages.MessageField('GoogleCloudDocumentaiV1RawDocument', 7)
  skipHumanReview = _messages.BooleanField(8)


class GoogleCloudDocumentaiV1ProcessResponse(_messages.Message):
  r"""Response message for the ProcessDocument method.

  Fields:
    document: The document payload, will populate fields based on the
      processor's behavior.
    humanReviewStatus: The status of human review on the processed document.
  """

  document = _messages.MessageField('GoogleCloudDocumentaiV1Document', 1)
  humanReviewStatus = _messages.MessageField('GoogleCloudDocumentaiV1HumanReviewStatus', 2)


class GoogleCloudDocumentaiV1Processor(_messages.Message):
  r"""The first-class citizen for Document AI. Each processor defines how to
  extract structural information from a document.

  Enums:
    StateValueValuesEnum: Output only. The state of the processor.

  Fields:
    createTime: Output only. The time the processor was created.
    defaultProcessorVersion: The default processor version.
    displayName: The display name of the processor.
    kmsKeyName: The [KMS key](https://cloud.google.com/security-key-
      management) used for encryption and decryption in CMEK scenarios.
    name: Output only. Immutable. The resource name of the processor. Format:
      `projects/{project}/locations/{location}/processors/{processor}`
    processEndpoint: Output only. Immutable. The http endpoint that can be
      called to invoke processing.
    processorVersionAliases: Output only. The processor version aliases.
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    state: Output only. The state of the processor.
    type: The processor type, such as: `OCR_PROCESSOR`, `INVOICE_PROCESSOR`.
      To get a list of processor types, see FetchProcessorTypes.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the processor.

    Values:
      STATE_UNSPECIFIED: The processor is in an unspecified state.
      ENABLED: The processor is enabled, i.e., has an enabled version which
        can currently serve processing requests and all the feature
        dependencies have been successfully initialized.
      DISABLED: The processor is disabled.
      ENABLING: The processor is being enabled, will become `ENABLED` if
        successful.
      DISABLING: The processor is being disabled, will become `DISABLED` if
        successful.
      CREATING: The processor is being created, will become either `ENABLED`
        (for successful creation) or `FAILED` (for failed ones). Once a
        processor is in this state, it can then be used for document
        processing, but the feature dependencies of the processor might not be
        fully created yet.
      FAILED: The processor failed during creation or initialization of
        feature dependencies. The user should delete the processor and
        recreate one as all the functionalities of the processor are disabled.
      DELETING: The processor is being deleted, will be removed if successful.
    """
    STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2
    ENABLING = 3
    DISABLING = 4
    CREATING = 5
    FAILED = 6
    DELETING = 7

  createTime = _messages.StringField(1)
  defaultProcessorVersion = _messages.StringField(2)
  displayName = _messages.StringField(3)
  kmsKeyName = _messages.StringField(4)
  name = _messages.StringField(5)
  processEndpoint = _messages.StringField(6)
  processorVersionAliases = _messages.MessageField('GoogleCloudDocumentaiV1ProcessorVersionAlias', 7, repeated=True)
  satisfiesPzi = _messages.BooleanField(8)
  satisfiesPzs = _messages.BooleanField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  type = _messages.StringField(11)


class GoogleCloudDocumentaiV1ProcessorType(_messages.Message):
  r"""A processor type is responsible for performing a certain document
  understanding task on a certain type of document.

  Enums:
    LaunchStageValueValuesEnum: Launch stage of the processor type

  Fields:
    allowCreation: Whether the processor type allows creation. If true, users
      can create a processor of this processor type. Otherwise, users need to
      request access.
    availableLocations: The locations in which this processor is available.
    category: The processor category, used by UI to group processor types.
    launchStage: Launch stage of the processor type
    name: The resource name of the processor type. Format:
      `projects/{project}/processorTypes/{processor_type}`
    sampleDocumentUris: A set of Cloud Storage URIs of sample documents for
      this processor.
    type: The processor type, such as: `OCR_PROCESSOR`, `INVOICE_PROCESSOR`.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Launch stage of the processor type

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  allowCreation = _messages.BooleanField(1)
  availableLocations = _messages.MessageField('GoogleCloudDocumentaiV1ProcessorTypeLocationInfo', 2, repeated=True)
  category = _messages.StringField(3)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 4)
  name = _messages.StringField(5)
  sampleDocumentUris = _messages.StringField(6, repeated=True)
  type = _messages.StringField(7)


class GoogleCloudDocumentaiV1ProcessorTypeLocationInfo(_messages.Message):
  r"""The location information about where the processor is available.

  Fields:
    locationId: The location ID. For supported locations, refer to [regional
      and multi-regional support](/document-ai/docs/regions).
  """

  locationId = _messages.StringField(1)


class GoogleCloudDocumentaiV1ProcessorVersion(_messages.Message):
  r"""A processor version is an implementation of a processor. Each processor
  can have multiple versions, pretrained by Google internally or uptrained by
  the customer. A processor can only have one default version at a time. Its
  document-processing behavior is defined by that version.

  Enums:
    ModelTypeValueValuesEnum: Output only. The model type of this processor
      version.
    StateValueValuesEnum: Output only. The state of the processor version.

  Fields:
    createTime: Output only. The time the processor version was created.
    deprecationInfo: Output only. If set, information about the eventual
      deprecation of this version.
    displayName: The display name of the processor version.
    documentSchema: Output only. The schema of the processor version.
      Describes the output.
    genAiModelInfo: Output only. Information about Generative AI model-based
      processor versions.
    googleManaged: Output only. Denotes that this `ProcessorVersion` is
      managed by Google.
    kmsKeyName: Output only. The KMS key name used for encryption.
    kmsKeyVersionName: Output only. The KMS key version with which data is
      encrypted.
    latestEvaluation: Output only. The most recently invoked evaluation for
      the processor version.
    modelType: Output only. The model type of this processor version.
    name: Identifier. The resource name of the processor version. Format: `pro
      jects/{project}/locations/{location}/processors/{processor}/processorVer
      sions/{processor_version}`
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    state: Output only. The state of the processor version.
  """

  class ModelTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The model type of this processor version.

    Values:
      MODEL_TYPE_UNSPECIFIED: The processor version has unspecified model
        type.
      MODEL_TYPE_GENERATIVE: The processor version has generative model type.
      MODEL_TYPE_CUSTOM: The processor version has custom model type.
    """
    MODEL_TYPE_UNSPECIFIED = 0
    MODEL_TYPE_GENERATIVE = 1
    MODEL_TYPE_CUSTOM = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the processor version.

    Values:
      STATE_UNSPECIFIED: The processor version is in an unspecified state.
      DEPLOYED: The processor version is deployed and can be used for
        processing.
      DEPLOYING: The processor version is being deployed.
      UNDEPLOYED: The processor version is not deployed and cannot be used for
        processing.
      UNDEPLOYING: The processor version is being undeployed.
      CREATING: The processor version is being created.
      DELETING: The processor version is being deleted.
      FAILED: The processor version failed and is in an indeterminate state.
      IMPORTING: The processor version is being imported.
    """
    STATE_UNSPECIFIED = 0
    DEPLOYED = 1
    DEPLOYING = 2
    UNDEPLOYED = 3
    UNDEPLOYING = 4
    CREATING = 5
    DELETING = 6
    FAILED = 7
    IMPORTING = 8

  createTime = _messages.StringField(1)
  deprecationInfo = _messages.MessageField('GoogleCloudDocumentaiV1ProcessorVersionDeprecationInfo', 2)
  displayName = _messages.StringField(3)
  documentSchema = _messages.MessageField('GoogleCloudDocumentaiV1DocumentSchema', 4)
  genAiModelInfo = _messages.MessageField('GoogleCloudDocumentaiV1ProcessorVersionGenAiModelInfo', 5)
  googleManaged = _messages.BooleanField(6)
  kmsKeyName = _messages.StringField(7)
  kmsKeyVersionName = _messages.StringField(8)
  latestEvaluation = _messages.MessageField('GoogleCloudDocumentaiV1EvaluationReference', 9)
  modelType = _messages.EnumField('ModelTypeValueValuesEnum', 10)
  name = _messages.StringField(11)
  satisfiesPzi = _messages.BooleanField(12)
  satisfiesPzs = _messages.BooleanField(13)
  state = _messages.EnumField('StateValueValuesEnum', 14)


class GoogleCloudDocumentaiV1ProcessorVersionAlias(_messages.Message):
  r"""Contains the alias and the aliased resource name of processor version.

  Fields:
    alias: The alias in the form of `processor_version` resource name.
    processorVersion: The resource name of aliased processor version.
  """

  alias = _messages.StringField(1)
  processorVersion = _messages.StringField(2)


class GoogleCloudDocumentaiV1ProcessorVersionDeprecationInfo(_messages.Message):
  r"""Information about the upcoming deprecation of this processor version.

  Fields:
    deprecationTime: The time at which this processor version will be
      deprecated.
    replacementProcessorVersion: If set, the processor version that will be
      used as a replacement.
  """

  deprecationTime = _messages.StringField(1)
  replacementProcessorVersion = _messages.StringField(2)


class GoogleCloudDocumentaiV1ProcessorVersionGenAiModelInfo(_messages.Message):
  r"""Information about Generative AI model-based processor versions.

  Fields:
    customGenAiModelInfo: Information for a custom Generative AI model created
      by the user.
    foundationGenAiModelInfo: Information for a pretrained Google-managed
      foundation model.
  """

  customGenAiModelInfo = _messages.MessageField('GoogleCloudDocumentaiV1ProcessorVersionGenAiModelInfoCustomGenAiModelInfo', 1)
  foundationGenAiModelInfo = _messages.MessageField('GoogleCloudDocumentaiV1ProcessorVersionGenAiModelInfoFoundationGenAiModelInfo', 2)


class GoogleCloudDocumentaiV1ProcessorVersionGenAiModelInfoCustomGenAiModelInfo(_messages.Message):
  r"""Information for a custom Generative AI model created by the user. These
  are created with `Create New Version` in either the `Call foundation model`
  or `Fine tuning` tabs.

  Enums:
    CustomModelTypeValueValuesEnum: The type of custom model created by the
      user.

  Fields:
    baseProcessorVersionId: The base processor version ID for the custom
      model.
    customModelType: The type of custom model created by the user.
  """

  class CustomModelTypeValueValuesEnum(_messages.Enum):
    r"""The type of custom model created by the user.

    Values:
      CUSTOM_MODEL_TYPE_UNSPECIFIED: The model type is unspecified.
      VERSIONED_FOUNDATION: The model is a versioned foundation model.
      FINE_TUNED: The model is a finetuned foundation model.
    """
    CUSTOM_MODEL_TYPE_UNSPECIFIED = 0
    VERSIONED_FOUNDATION = 1
    FINE_TUNED = 2

  baseProcessorVersionId = _messages.StringField(1)
  customModelType = _messages.EnumField('CustomModelTypeValueValuesEnum', 2)


class GoogleCloudDocumentaiV1ProcessorVersionGenAiModelInfoFoundationGenAiModelInfo(_messages.Message):
  r"""Information for a pretrained Google-managed foundation model.

  Fields:
    finetuningAllowed: Whether finetuning is allowed for this base processor
      version.
    minTrainLabeledDocuments: The minimum number of labeled documents in the
      training dataset required for finetuning.
  """

  finetuningAllowed = _messages.BooleanField(1)
  minTrainLabeledDocuments = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1RawDocument(_messages.Message):
  r"""Payload message of raw document content (bytes).

  Fields:
    content: Inline document content.
    displayName: The display name of the document, it supports all Unicode
      characters except the following: `*`, `?`, `[`, `]`, `%`, `{`, `}`,`'`,
      `\"`, `,` `~`, `=` and `:` are reserved. If not specified, a default ID
      is generated.
    mimeType: An IANA MIME type (RFC6838) indicating the nature and format of
      the content.
  """

  content = _messages.BytesField(1)
  displayName = _messages.StringField(2)
  mimeType = _messages.StringField(3)


class GoogleCloudDocumentaiV1ReviewDocumentOperationMetadata(_messages.Message):
  r"""The long-running operation metadata for the ReviewDocument method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
    questionId: The Crowd Compute question ID.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1CommonOperationMetadata', 1)
  questionId = _messages.StringField(2)


class GoogleCloudDocumentaiV1ReviewDocumentRequest(_messages.Message):
  r"""Request message for the ReviewDocument method.

  Enums:
    PriorityValueValuesEnum: The priority of the human review task.

  Fields:
    documentSchema: The document schema of the human review task.
    enableSchemaValidation: Whether the validation should be performed on the
      ad-hoc review request.
    inlineDocument: An inline document proto.
    priority: The priority of the human review task.
  """

  class PriorityValueValuesEnum(_messages.Enum):
    r"""The priority of the human review task.

    Values:
      DEFAULT: The default priority level.
      URGENT: The urgent priority level. The labeling manager should allocate
        labeler resource to the urgent task queue to respect this priority
        level.
    """
    DEFAULT = 0
    URGENT = 1

  documentSchema = _messages.MessageField('GoogleCloudDocumentaiV1DocumentSchema', 1)
  enableSchemaValidation = _messages.BooleanField(2)
  inlineDocument = _messages.MessageField('GoogleCloudDocumentaiV1Document', 3)
  priority = _messages.EnumField('PriorityValueValuesEnum', 4)


class GoogleCloudDocumentaiV1ReviewDocumentResponse(_messages.Message):
  r"""Response message for the ReviewDocument method.

  Enums:
    StateValueValuesEnum: The state of the review operation.

  Fields:
    gcsDestination: The Cloud Storage uri for the human reviewed document if
      the review is succeeded.
    rejectionReason: The reason why the review is rejected by reviewer.
    state: The state of the review operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the review operation.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      REJECTED: The review operation is rejected by the reviewer.
      SUCCEEDED: The review operation is succeeded.
    """
    STATE_UNSPECIFIED = 0
    REJECTED = 1
    SUCCEEDED = 2

  gcsDestination = _messages.StringField(1)
  rejectionReason = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class GoogleCloudDocumentaiV1SetDefaultProcessorVersionMetadata(_messages.Message):
  r"""The long-running operation metadata for the SetDefaultProcessorVersion
  method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1SetDefaultProcessorVersionRequest(_messages.Message):
  r"""Request message for the SetDefaultProcessorVersion method.

  Fields:
    defaultProcessorVersion: Required. The resource name of child
      ProcessorVersion to use as default. Format: `projects/{project}/location
      s/{location}/processors/{processor}/processorVersions/{version}`
  """

  defaultProcessorVersion = _messages.StringField(1)


class GoogleCloudDocumentaiV1SetDefaultProcessorVersionResponse(_messages.Message):
  r"""Response message for the SetDefaultProcessorVersion method."""


class GoogleCloudDocumentaiV1TrainProcessorVersionMetadata(_messages.Message):
  r"""The metadata that represents a processor version being created.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
    testDatasetValidation: The test dataset validation information.
    trainingDatasetValidation: The training dataset validation information.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1CommonOperationMetadata', 1)
  testDatasetValidation = _messages.MessageField('GoogleCloudDocumentaiV1TrainProcessorVersionMetadataDatasetValidation', 2)
  trainingDatasetValidation = _messages.MessageField('GoogleCloudDocumentaiV1TrainProcessorVersionMetadataDatasetValidation', 3)


class GoogleCloudDocumentaiV1TrainProcessorVersionMetadataDatasetValidation(_messages.Message):
  r"""The dataset validation information. This includes any and all errors
  with documents and the dataset.

  Fields:
    datasetErrorCount: The total number of dataset errors.
    datasetErrors: Error information for the dataset as a whole. A maximum of
      10 dataset errors will be returned. A single dataset error is terminal
      for training.
    documentErrorCount: The total number of document errors.
    documentErrors: Error information pertaining to specific documents. A
      maximum of 10 document errors will be returned. Any document with errors
      will not be used throughout training.
  """

  datasetErrorCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  datasetErrors = _messages.MessageField('GoogleRpcStatus', 2, repeated=True)
  documentErrorCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  documentErrors = _messages.MessageField('GoogleRpcStatus', 4, repeated=True)


class GoogleCloudDocumentaiV1TrainProcessorVersionRequest(_messages.Message):
  r"""Request message for the TrainProcessorVersion method.

  Fields:
    baseProcessorVersion: Optional. The processor version to use as a base for
      training. This processor version must be a child of `parent`. Format: `p
      rojects/{project}/locations/{location}/processors/{processor}/processorV
      ersions/{processorVersion}`.
    customDocumentExtractionOptions: Options to control Custom Document
      Extraction (CDE) Processor.
    documentSchema: Optional. The schema the processor version will be trained
      with.
    foundationModelTuningOptions: Options to control foundation model tuning
      of a processor.
    inputData: Optional. The input data used to train the ProcessorVersion.
    processorVersion: Required. The processor version to be created.
  """

  baseProcessorVersion = _messages.StringField(1)
  customDocumentExtractionOptions = _messages.MessageField('GoogleCloudDocumentaiV1TrainProcessorVersionRequestCustomDocumentExtractionOptions', 2)
  documentSchema = _messages.MessageField('GoogleCloudDocumentaiV1DocumentSchema', 3)
  foundationModelTuningOptions = _messages.MessageField('GoogleCloudDocumentaiV1TrainProcessorVersionRequestFoundationModelTuningOptions', 4)
  inputData = _messages.MessageField('GoogleCloudDocumentaiV1TrainProcessorVersionRequestInputData', 5)
  processorVersion = _messages.MessageField('GoogleCloudDocumentaiV1ProcessorVersion', 6)


class GoogleCloudDocumentaiV1TrainProcessorVersionRequestCustomDocumentExtractionOptions(_messages.Message):
  r"""Options to control the training of the Custom Document Extraction (CDE)
  Processor.

  Enums:
    TrainingMethodValueValuesEnum: Training method to use for CDE training.

  Fields:
    trainingMethod: Training method to use for CDE training.
  """

  class TrainingMethodValueValuesEnum(_messages.Enum):
    r"""Training method to use for CDE training.

    Values:
      TRAINING_METHOD_UNSPECIFIED: <no description>
      MODEL_BASED: <no description>
      TEMPLATE_BASED: <no description>
    """
    TRAINING_METHOD_UNSPECIFIED = 0
    MODEL_BASED = 1
    TEMPLATE_BASED = 2

  trainingMethod = _messages.EnumField('TrainingMethodValueValuesEnum', 1)


class GoogleCloudDocumentaiV1TrainProcessorVersionRequestFoundationModelTuningOptions(_messages.Message):
  r"""Options to control foundation model tuning of the processor.

  Fields:
    learningRateMultiplier: Optional. The multiplier to apply to the
      recommended learning rate. Valid values are between 0.1 and 10. If not
      provided, recommended learning rate will be used.
    trainSteps: Optional. The number of steps to run for model tuning. Valid
      values are between 1 and 400. If not provided, recommended steps will be
      used.
  """

  learningRateMultiplier = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  trainSteps = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1TrainProcessorVersionRequestInputData(_messages.Message):
  r"""The input data used to train a new ProcessorVersion.

  Fields:
    testDocuments: The documents used for testing the trained version.
    trainingDocuments: The documents used for training the new version.
  """

  testDocuments = _messages.MessageField('GoogleCloudDocumentaiV1BatchDocumentsInputConfig', 1)
  trainingDocuments = _messages.MessageField('GoogleCloudDocumentaiV1BatchDocumentsInputConfig', 2)


class GoogleCloudDocumentaiV1TrainProcessorVersionResponse(_messages.Message):
  r"""The response for TrainProcessorVersion.

  Fields:
    processorVersion: The resource name of the processor version produced by
      training.
  """

  processorVersion = _messages.StringField(1)


class GoogleCloudDocumentaiV1UndeployProcessorVersionMetadata(_messages.Message):
  r"""The long-running operation metadata for the UndeployProcessorVersion
  method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1UndeployProcessorVersionRequest(_messages.Message):
  r"""Request message for the UndeployProcessorVersion method."""


class GoogleCloudDocumentaiV1UndeployProcessorVersionResponse(_messages.Message):
  r"""Response message for the UndeployProcessorVersion method."""


class GoogleCloudDocumentaiV1Vertex(_messages.Message):
  r"""A vertex represents a 2D point in the image. NOTE: the vertex
  coordinates are in the same scale as the original image.

  Fields:
    x: X coordinate.
    y: Y coordinate (starts from the top of the image).
  """

  x = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  y = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1beta3BatchDeleteDocumentsMetadata(_messages.Message):
  r"""A GoogleCloudDocumentaiV1beta3BatchDeleteDocumentsMetadata object.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
    errorDocumentCount: Total number of documents that failed to be deleted in
      storage.
    individualBatchDeleteStatuses: The list of response details of each
      document.
    totalDocumentCount: Total number of documents deleting from dataset.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1beta3CommonOperationMetadata', 1)
  errorDocumentCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  individualBatchDeleteStatuses = _messages.MessageField('GoogleCloudDocumentaiV1beta3BatchDeleteDocumentsMetadataIndividualBatchDeleteStatus', 3, repeated=True)
  totalDocumentCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1beta3BatchDeleteDocumentsMetadataIndividualBatchDeleteStatus(_messages.Message):
  r"""The status of each individual document in the batch delete process.

  Fields:
    documentId: The document id of the document.
    status: The status of deleting the document in storage.
  """

  documentId = _messages.MessageField('GoogleCloudDocumentaiV1beta3DocumentId', 1)
  status = _messages.MessageField('GoogleRpcStatus', 2)


class GoogleCloudDocumentaiV1beta3BatchDeleteDocumentsResponse(_messages.Message):
  r"""Response of the delete documents operation."""


class GoogleCloudDocumentaiV1beta3BatchProcessMetadata(_messages.Message):
  r"""The long-running operation metadata for BatchProcessDocuments.

  Enums:
    StateValueValuesEnum: The state of the current batch processing.

  Fields:
    createTime: The creation time of the operation.
    individualProcessStatuses: The list of response details of each document.
    state: The state of the current batch processing.
    stateMessage: A message providing more details about the current state of
      processing. For example, the error message if the operation is failed.
    updateTime: The last update time of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the current batch processing.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      WAITING: Request operation is waiting for scheduling.
      RUNNING: Request is being processed.
      SUCCEEDED: The batch processing completed successfully.
      CANCELLING: The batch processing was being cancelled.
      CANCELLED: The batch processing was cancelled.
      FAILED: The batch processing has failed.
    """
    STATE_UNSPECIFIED = 0
    WAITING = 1
    RUNNING = 2
    SUCCEEDED = 3
    CANCELLING = 4
    CANCELLED = 5
    FAILED = 6

  createTime = _messages.StringField(1)
  individualProcessStatuses = _messages.MessageField('GoogleCloudDocumentaiV1beta3BatchProcessMetadataIndividualProcessStatus', 2, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  stateMessage = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class GoogleCloudDocumentaiV1beta3BatchProcessMetadataIndividualProcessStatus(_messages.Message):
  r"""The status of a each individual document in the batch process.

  Fields:
    humanReviewOperation: The name of the operation triggered by the processed
      document. If the human review process isn't triggered, this field will
      be empty. It has the same response type and metadata as the long-running
      operation returned by the ReviewDocument method.
    humanReviewStatus: The status of human review on the processed document.
    inputGcsSource: The source of the document, same as the input_gcs_source
      field in the request when the batch process started.
    outputGcsDestination: The Cloud Storage output destination (in the request
      as DocumentOutputConfig.GcsOutputConfig.gcs_uri) of the processed
      document if it was successful, otherwise empty.
    status: The status processing the document.
  """

  humanReviewOperation = _messages.StringField(1)
  humanReviewStatus = _messages.MessageField('GoogleCloudDocumentaiV1beta3HumanReviewStatus', 2)
  inputGcsSource = _messages.StringField(3)
  outputGcsDestination = _messages.StringField(4)
  status = _messages.MessageField('GoogleRpcStatus', 5)


class GoogleCloudDocumentaiV1beta3BatchProcessResponse(_messages.Message):
  r"""Response message for BatchProcessDocuments."""


class GoogleCloudDocumentaiV1beta3CommonOperationMetadata(_messages.Message):
  r"""The common metadata for long running operations.

  Enums:
    StateValueValuesEnum: The state of the operation.

  Fields:
    createTime: The creation time of the operation.
    resource: A related resource to this operation.
    state: The state of the operation.
    stateMessage: A message providing more details about the current state of
      processing.
    updateTime: The last update time of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the operation.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      RUNNING: Operation is still running.
      CANCELLING: Operation is being cancelled.
      SUCCEEDED: Operation succeeded.
      FAILED: Operation failed.
      CANCELLED: Operation is cancelled.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    CANCELLING = 2
    SUCCEEDED = 3
    FAILED = 4
    CANCELLED = 5

  createTime = _messages.StringField(1)
  resource = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  stateMessage = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class GoogleCloudDocumentaiV1beta3Dataset(_messages.Message):
  r"""A singleton resource under a Processor which configures a collection of
  documents.

  Enums:
    StateValueValuesEnum: Required. State of the dataset. Ignored when
      updating dataset.

  Fields:
    documentWarehouseConfig: Optional. Deprecated. Warehouse-based dataset
      configuration is not supported.
    gcsManagedConfig: Optional. User-managed Cloud Storage dataset
      configuration. Use this configuration if the dataset documents are
      stored under a user-managed Cloud Storage location.
    name: Dataset resource name. Format:
      `projects/{project}/locations/{location}/processors/{processor}/dataset`
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    spannerIndexingConfig: Optional. A lightweight indexing source with low
      latency and high reliability, but lacking advanced features like CMEK
      and content-based search.
    state: Required. State of the dataset. Ignored when updating dataset.
    unmanagedDatasetConfig: Optional. Unmanaged dataset configuration. Use
      this configuration if the dataset documents are managed by the document
      service internally (not user-managed).
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Required. State of the dataset. Ignored when updating dataset.

    Values:
      STATE_UNSPECIFIED: Default unspecified enum, should not be used.
      UNINITIALIZED: Dataset has not been initialized.
      INITIALIZING: Dataset is being initialized.
      INITIALIZED: Dataset has been initialized.
    """
    STATE_UNSPECIFIED = 0
    UNINITIALIZED = 1
    INITIALIZING = 2
    INITIALIZED = 3

  documentWarehouseConfig = _messages.MessageField('GoogleCloudDocumentaiV1beta3DatasetDocumentWarehouseConfig', 1)
  gcsManagedConfig = _messages.MessageField('GoogleCloudDocumentaiV1beta3DatasetGCSManagedConfig', 2)
  name = _messages.StringField(3)
  satisfiesPzi = _messages.BooleanField(4)
  satisfiesPzs = _messages.BooleanField(5)
  spannerIndexingConfig = _messages.MessageField('GoogleCloudDocumentaiV1beta3DatasetSpannerIndexingConfig', 6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  unmanagedDatasetConfig = _messages.MessageField('GoogleCloudDocumentaiV1beta3DatasetUnmanagedDatasetConfig', 8)


class GoogleCloudDocumentaiV1beta3DatasetDocumentWarehouseConfig(_messages.Message):
  r"""Configuration specific to the Document AI Warehouse-based
  implementation.

  Fields:
    collection: Output only. The collection in Document AI Warehouse
      associated with the dataset.
    schema: Output only. The schema in Document AI Warehouse associated with
      the dataset.
  """

  collection = _messages.StringField(1)
  schema = _messages.StringField(2)


class GoogleCloudDocumentaiV1beta3DatasetGCSManagedConfig(_messages.Message):
  r"""Configuration specific to the Cloud Storage-based implementation.

  Fields:
    gcsPrefix: Required. The Cloud Storage URI (a directory) where the
      documents belonging to the dataset must be stored.
  """

  gcsPrefix = _messages.MessageField('GoogleCloudDocumentaiV1beta3GcsPrefix', 1)


class GoogleCloudDocumentaiV1beta3DatasetSpannerIndexingConfig(_messages.Message):
  r"""Configuration specific to spanner-based indexing."""


class GoogleCloudDocumentaiV1beta3DatasetUnmanagedDatasetConfig(_messages.Message):
  r"""Configuration specific to an unmanaged dataset."""


class GoogleCloudDocumentaiV1beta3DeleteProcessorMetadata(_messages.Message):
  r"""The long-running operation metadata for the DeleteProcessor method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1beta3DeleteProcessorVersionMetadata(_messages.Message):
  r"""The long-running operation metadata for the DeleteProcessorVersion
  method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1beta3DeployProcessorVersionMetadata(_messages.Message):
  r"""The long-running operation metadata for the DeployProcessorVersion
  method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1beta3DeployProcessorVersionResponse(_messages.Message):
  r"""Response message for the DeployProcessorVersion method."""


class GoogleCloudDocumentaiV1beta3DisableProcessorMetadata(_messages.Message):
  r"""The long-running operation metadata for the DisableProcessor method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1beta3DisableProcessorResponse(_messages.Message):
  r"""Response message for the DisableProcessor method. Intentionally empty
  proto for adding fields in future.
  """



class GoogleCloudDocumentaiV1beta3DocumentId(_messages.Message):
  r"""Document Identifier.

  Fields:
    gcsManagedDocId: A document id within user-managed Cloud Storage.
    revisionRef: Points to a specific revision of the document if set.
    unmanagedDocId: A document id within unmanaged dataset.
  """

  gcsManagedDocId = _messages.MessageField('GoogleCloudDocumentaiV1beta3DocumentIdGCSManagedDocumentId', 1)
  revisionRef = _messages.MessageField('GoogleCloudDocumentaiV1beta3RevisionRef', 2)
  unmanagedDocId = _messages.MessageField('GoogleCloudDocumentaiV1beta3DocumentIdUnmanagedDocumentId', 3)


class GoogleCloudDocumentaiV1beta3DocumentIdGCSManagedDocumentId(_messages.Message):
  r"""Identifies a document uniquely within the scope of a dataset in the
  user-managed Cloud Storage option.

  Fields:
    cwDocId: Id of the document (indexed) managed by Content Warehouse.
    gcsUri: Required. The Cloud Storage URI where the actual document is
      stored.
  """

  cwDocId = _messages.StringField(1)
  gcsUri = _messages.StringField(2)


class GoogleCloudDocumentaiV1beta3DocumentIdUnmanagedDocumentId(_messages.Message):
  r"""Identifies a document uniquely within the scope of a dataset in
  unmanaged option.

  Fields:
    docId: Required. The id of the document.
  """

  docId = _messages.StringField(1)


class GoogleCloudDocumentaiV1beta3EnableProcessorMetadata(_messages.Message):
  r"""The long-running operation metadata for the EnableProcessor method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1beta3EnableProcessorResponse(_messages.Message):
  r"""Response message for the EnableProcessor method. Intentionally empty
  proto for adding fields in future.
  """



class GoogleCloudDocumentaiV1beta3EvaluateProcessorVersionMetadata(_messages.Message):
  r"""Metadata of the EvaluateProcessorVersion method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1beta3EvaluateProcessorVersionResponse(_messages.Message):
  r"""Response of the EvaluateProcessorVersion method.

  Fields:
    evaluation: The resource name of the created evaluation.
  """

  evaluation = _messages.StringField(1)


class GoogleCloudDocumentaiV1beta3GcsPrefix(_messages.Message):
  r"""Specifies all documents on Cloud Storage with a common prefix.

  Fields:
    gcsUriPrefix: The URI prefix.
  """

  gcsUriPrefix = _messages.StringField(1)


class GoogleCloudDocumentaiV1beta3HumanReviewStatus(_messages.Message):
  r"""The status of human review on a processed document.

  Enums:
    StateValueValuesEnum: The state of human review on the processing request.

  Fields:
    humanReviewOperation: The name of the operation triggered by the processed
      document. This field is populated only when the state is
      `HUMAN_REVIEW_IN_PROGRESS`. It has the same response type and metadata
      as the long-running operation returned by ReviewDocument.
    state: The state of human review on the processing request.
    stateMessage: A message providing more details about the human review
      state.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of human review on the processing request.

    Values:
      STATE_UNSPECIFIED: Human review state is unspecified. Most likely due to
        an internal error.
      SKIPPED: Human review is skipped for the document. This can happen
        because human review isn't enabled on the processor or the processing
        request has been set to skip this document.
      VALIDATION_PASSED: Human review validation is triggered and passed, so
        no review is needed.
      IN_PROGRESS: Human review validation is triggered and the document is
        under review.
      ERROR: Some error happened during triggering human review, see the
        state_message for details.
    """
    STATE_UNSPECIFIED = 0
    SKIPPED = 1
    VALIDATION_PASSED = 2
    IN_PROGRESS = 3
    ERROR = 4

  humanReviewOperation = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  stateMessage = _messages.StringField(3)


class GoogleCloudDocumentaiV1beta3ImportDocumentsMetadata(_messages.Message):
  r"""Metadata of the import document operation.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
    importConfigValidationResults: Validation statuses of the batch documents
      import config.
    individualImportStatuses: The list of response details of each document.
    totalDocumentCount: Total number of the documents that are qualified for
      importing.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1beta3CommonOperationMetadata', 1)
  importConfigValidationResults = _messages.MessageField('GoogleCloudDocumentaiV1beta3ImportDocumentsMetadataImportConfigValidationResult', 2, repeated=True)
  individualImportStatuses = _messages.MessageField('GoogleCloudDocumentaiV1beta3ImportDocumentsMetadataIndividualImportStatus', 3, repeated=True)
  totalDocumentCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudDocumentaiV1beta3ImportDocumentsMetadataImportConfigValidationResult(_messages.Message):
  r"""The validation status of each import config. Status is set to an error
  if there are no documents to import in the `import_config`, or `OK` if the
  operation will try to proceed with at least one document.

  Fields:
    inputGcsSource: The source Cloud Storage URI specified in the import
      config.
    status: The validation status of import config.
  """

  inputGcsSource = _messages.StringField(1)
  status = _messages.MessageField('GoogleRpcStatus', 2)


class GoogleCloudDocumentaiV1beta3ImportDocumentsMetadataIndividualImportStatus(_messages.Message):
  r"""The status of each individual document in the import process.

  Fields:
    inputGcsSource: The source Cloud Storage URI of the document.
    outputDocumentId: The document id of imported document if it was
      successful, otherwise empty.
    status: The status of the importing of the document.
  """

  inputGcsSource = _messages.StringField(1)
  outputDocumentId = _messages.MessageField('GoogleCloudDocumentaiV1beta3DocumentId', 2)
  status = _messages.MessageField('GoogleRpcStatus', 3)


class GoogleCloudDocumentaiV1beta3ImportDocumentsResponse(_messages.Message):
  r"""Response of the import document operation."""


class GoogleCloudDocumentaiV1beta3ImportProcessorVersionMetadata(_messages.Message):
  r"""The long-running operation metadata for the ImportProcessorVersion
  method.

  Fields:
    commonMetadata: The basic metadata for the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1beta3ImportProcessorVersionResponse(_messages.Message):
  r"""The response message for the ImportProcessorVersion method.

  Fields:
    processorVersion: The destination processor version name.
  """

  processorVersion = _messages.StringField(1)


class GoogleCloudDocumentaiV1beta3ReviewDocumentOperationMetadata(_messages.Message):
  r"""The long-running operation metadata for the ReviewDocument method.

  Enums:
    StateValueValuesEnum: Used only when Operation.done is false.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
    createTime: The creation time of the operation.
    questionId: The Crowd Compute question ID.
    state: Used only when Operation.done is false.
    stateMessage: A message providing more details about the current state of
      processing. For example, the error message if the operation is failed.
    updateTime: The last update time of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Used only when Operation.done is false.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      RUNNING: Operation is still running.
      CANCELLING: Operation is being cancelled.
      SUCCEEDED: Operation succeeded.
      FAILED: Operation failed.
      CANCELLED: Operation is cancelled.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    CANCELLING = 2
    SUCCEEDED = 3
    FAILED = 4
    CANCELLED = 5

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1beta3CommonOperationMetadata', 1)
  createTime = _messages.StringField(2)
  questionId = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  stateMessage = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class GoogleCloudDocumentaiV1beta3ReviewDocumentResponse(_messages.Message):
  r"""Response message for the ReviewDocument method.

  Enums:
    StateValueValuesEnum: The state of the review operation.

  Fields:
    gcsDestination: The Cloud Storage uri for the human reviewed document if
      the review is succeeded.
    rejectionReason: The reason why the review is rejected by reviewer.
    state: The state of the review operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the review operation.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      REJECTED: The review operation is rejected by the reviewer.
      SUCCEEDED: The review operation is succeeded.
    """
    STATE_UNSPECIFIED = 0
    REJECTED = 1
    SUCCEEDED = 2

  gcsDestination = _messages.StringField(1)
  rejectionReason = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class GoogleCloudDocumentaiV1beta3RevisionRef(_messages.Message):
  r"""The revision reference specifies which revision on the document to read.

  Enums:
    RevisionCaseValueValuesEnum: Reads the revision by the predefined case.

  Fields:
    latestProcessorVersion: Reads the revision generated by the processor
      version. The format takes the full resource name of processor version. `
      projects/{project}/locations/{location}/processors/{processor}/processor
      Versions/{processorVersion}`
    revisionCase: Reads the revision by the predefined case.
    revisionId: Reads the revision given by the id.
  """

  class RevisionCaseValueValuesEnum(_messages.Enum):
    r"""Reads the revision by the predefined case.

    Values:
      REVISION_CASE_UNSPECIFIED: Unspecified case, fall back to read the
        `LATEST_HUMAN_REVIEW`.
      LATEST_HUMAN_REVIEW: The latest revision made by a human.
      LATEST_TIMESTAMP: The latest revision based on timestamp.
      BASE_OCR_REVISION: The first (OCR) revision.
    """
    REVISION_CASE_UNSPECIFIED = 0
    LATEST_HUMAN_REVIEW = 1
    LATEST_TIMESTAMP = 2
    BASE_OCR_REVISION = 3

  latestProcessorVersion = _messages.StringField(1)
  revisionCase = _messages.EnumField('RevisionCaseValueValuesEnum', 2)
  revisionId = _messages.StringField(3)


class GoogleCloudDocumentaiV1beta3SetDefaultProcessorVersionMetadata(_messages.Message):
  r"""The long-running operation metadata for the SetDefaultProcessorVersion
  method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1beta3SetDefaultProcessorVersionResponse(_messages.Message):
  r"""Response message for the SetDefaultProcessorVersion method."""


class GoogleCloudDocumentaiV1beta3TrainProcessorVersionMetadata(_messages.Message):
  r"""The metadata that represents a processor version being created.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
    testDatasetValidation: The test dataset validation information.
    trainingDatasetValidation: The training dataset validation information.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1beta3CommonOperationMetadata', 1)
  testDatasetValidation = _messages.MessageField('GoogleCloudDocumentaiV1beta3TrainProcessorVersionMetadataDatasetValidation', 2)
  trainingDatasetValidation = _messages.MessageField('GoogleCloudDocumentaiV1beta3TrainProcessorVersionMetadataDatasetValidation', 3)


class GoogleCloudDocumentaiV1beta3TrainProcessorVersionMetadataDatasetValidation(_messages.Message):
  r"""The dataset validation information. This includes any and all errors
  with documents and the dataset.

  Fields:
    datasetErrorCount: The total number of dataset errors.
    datasetErrors: Error information for the dataset as a whole. A maximum of
      10 dataset errors will be returned. A single dataset error is terminal
      for training.
    documentErrorCount: The total number of document errors.
    documentErrors: Error information pertaining to specific documents. A
      maximum of 10 document errors will be returned. Any document with errors
      will not be used throughout training.
  """

  datasetErrorCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  datasetErrors = _messages.MessageField('GoogleRpcStatus', 2, repeated=True)
  documentErrorCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  documentErrors = _messages.MessageField('GoogleRpcStatus', 4, repeated=True)


class GoogleCloudDocumentaiV1beta3TrainProcessorVersionResponse(_messages.Message):
  r"""The response for TrainProcessorVersion.

  Fields:
    processorVersion: The resource name of the processor version produced by
      training.
  """

  processorVersion = _messages.StringField(1)


class GoogleCloudDocumentaiV1beta3UndeployProcessorVersionMetadata(_messages.Message):
  r"""The long-running operation metadata for the UndeployProcessorVersion
  method.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1beta3CommonOperationMetadata', 1)


class GoogleCloudDocumentaiV1beta3UndeployProcessorVersionResponse(_messages.Message):
  r"""Response message for the UndeployProcessorVersion method."""


class GoogleCloudDocumentaiV1beta3UpdateDatasetOperationMetadata(_messages.Message):
  r"""A GoogleCloudDocumentaiV1beta3UpdateDatasetOperationMetadata object.

  Fields:
    commonMetadata: The basic metadata of the long-running operation.
  """

  commonMetadata = _messages.MessageField('GoogleCloudDocumentaiV1beta3CommonOperationMetadata', 1)


class GoogleCloudLocationListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('GoogleCloudLocationLocation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudLocationLocation(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeColor(_messages.Message):
  r"""Represents a color in the RGBA color space. This representation is
  designed for simplicity of conversion to and from color representations in
  various languages over compactness. For example, the fields of this
  representation can be trivially provided to the constructor of
  `java.awt.Color` in Java; it can also be trivially provided to UIColor's
  `+colorWithRed:green:blue:alpha` method in iOS; and, with just a little
  work, it can be easily formatted into a CSS `rgba()` string in JavaScript.
  This reference page doesn't have information about the absolute color space
  that should be used to interpret the RGB value-for example, sRGB, Adobe RGB,
  DCI-P3, and BT.2020. By default, applications should assume the sRGB color
  space. When color equality needs to be decided, implementations, unless
  documented otherwise, treat two colors as equal if all their red, green,
  blue, and alpha values each differ by at most `1e-5`. Example (Java): import
  com.google.type.Color; // ... public static java.awt.Color fromProto(Color
  protocolor) { float alpha = protocolor.hasAlpha() ?
  protocolor.getAlpha().getValue() : 1.0; return new java.awt.Color(
  protocolor.getRed(), protocolor.getGreen(), protocolor.getBlue(), alpha); }
  public static Color toProto(java.awt.Color color) { float red = (float)
  color.getRed(); float green = (float) color.getGreen(); float blue = (float)
  color.getBlue(); float denominator = 255.0; Color.Builder resultBuilder =
  Color .newBuilder() .setRed(red / denominator) .setGreen(green /
  denominator) .setBlue(blue / denominator); int alpha = color.getAlpha(); if
  (alpha != 255) { result.setAlpha( FloatValue .newBuilder()
  .setValue(((float) alpha) / denominator) .build()); } return
  resultBuilder.build(); } // ... Example (iOS / Obj-C): // ... static
  UIColor* fromProto(Color* protocolor) { float red = [protocolor red]; float
  green = [protocolor green]; float blue = [protocolor blue]; FloatValue*
  alpha_wrapper = [protocolor alpha]; float alpha = 1.0; if (alpha_wrapper !=
  nil) { alpha = [alpha_wrapper value]; } return [UIColor colorWithRed:red
  green:green blue:blue alpha:alpha]; } static Color* toProto(UIColor* color)
  { CGFloat red, green, blue, alpha; if (![color getRed:&red green:&green
  blue:&blue alpha:&alpha]) { return nil; } Color* result = [[Color alloc]
  init]; [result setRed:red]; [result setGreen:green]; [result setBlue:blue];
  if (alpha <= 0.9999) { [result setAlpha:floatWrapperWithValue(alpha)]; }
  [result autorelease]; return result; } // ... Example (JavaScript): // ...
  var protoToCssColor = function(rgb_color) { var redFrac = rgb_color.red ||
  0.0; var greenFrac = rgb_color.green || 0.0; var blueFrac = rgb_color.blue
  || 0.0; var red = Math.floor(redFrac * 255); var green =
  Math.floor(greenFrac * 255); var blue = Math.floor(blueFrac * 255); if
  (!('alpha' in rgb_color)) { return rgbToCssColor(red, green, blue); } var
  alphaFrac = rgb_color.alpha.value || 0.0; var rgbParams = [red, green,
  blue].join(','); return ['rgba(', rgbParams, ',', alphaFrac, ')'].join('');
  }; var rgbToCssColor = function(red, green, blue) { var rgbNumber = new
  Number((red << 16) | (green << 8) | blue); var hexString =
  rgbNumber.toString(16); var missingZeros = 6 - hexString.length; var
  resultBuilder = ['#']; for (var i = 0; i < missingZeros; i++) {
  resultBuilder.push('0'); } resultBuilder.push(hexString); return
  resultBuilder.join(''); }; // ...

  Fields:
    alpha: The fraction of this color that should be applied to the pixel.
      That is, the final pixel color is defined by the equation: `pixel color
      = alpha * (this color) + (1.0 - alpha) * (background color)` This means
      that a value of 1.0 corresponds to a solid color, whereas a value of 0.0
      corresponds to a completely transparent color. This uses a wrapper
      message rather than a simple float scalar so that it is possible to
      distinguish between a default value and the value being unset. If
      omitted, this color object is rendered as a solid color (as if the alpha
      value had been explicitly given a value of 1.0).
    blue: The amount of blue in the color as a value in the interval [0, 1].
    green: The amount of green in the color as a value in the interval [0, 1].
    red: The amount of red in the color as a value in the interval [0, 1].
  """

  alpha = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  blue = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  green = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  red = _messages.FloatField(4, variant=_messages.Variant.FLOAT)


class GoogleTypeDate(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleTypeDateTime(_messages.Message):
  r"""Represents civil time (or occasionally physical time). This type can
  represent a civil time in one of a few possible ways: * When utc_offset is
  set and time_zone is unset: a civil time on a calendar day with a particular
  offset from UTC. * When time_zone is set and utc_offset is unset: a civil
  time on a calendar day in a particular time zone. * When neither time_zone
  nor utc_offset is set: a civil time on a calendar day in local time. The
  date is relative to the Proleptic Gregorian Calendar. If year, month, or day
  are 0, the DateTime is considered not to have a specific year, month, or day
  respectively. This type may also be used to represent a physical time if all
  the date and time fields are set and either case of the `time_offset` oneof
  is set. Consider using `Timestamp` message for physical time instead. If
  your use case also would like to store the user's timezone, that can be done
  in another field. This type is more flexible than some applications may
  want. Make sure to document and validate your application's limitations.

  Fields:
    day: Optional. Day of month. Must be from 1 to 31 and valid for the year
      and month, or 0 if specifying a datetime without a day.
    hours: Optional. Hours of day in 24 hour format. Should be from 0 to 23,
      defaults to 0 (midnight). An API may choose to allow the value
      "24:00:00" for scenarios like business closing time.
    minutes: Optional. Minutes of hour of day. Must be from 0 to 59, defaults
      to 0.
    month: Optional. Month of year. Must be from 1 to 12, or 0 if specifying a
      datetime without a month.
    nanos: Optional. Fractions of seconds in nanoseconds. Must be from 0 to
      999,999,999, defaults to 0.
    seconds: Optional. Seconds of minutes of the time. Must normally be from 0
      to 59, defaults to 0. An API may allow the value 60 if it allows leap-
      seconds.
    timeZone: Time zone.
    utcOffset: UTC offset. Must be whole seconds, between -18 hours and +18
      hours. For example, a UTC offset of -4:00 would be represented as {
      seconds: -14400 }.
    year: Optional. Year of date. Must be from 1 to 9999, or 0 if specifying a
      datetime without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  hours = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  timeZone = _messages.MessageField('GoogleTypeTimeZone', 7)
  utcOffset = _messages.StringField(8)
  year = _messages.IntegerField(9, variant=_messages.Variant.INT32)


class GoogleTypeMoney(_messages.Message):
  r"""Represents an amount of money with its currency type.

  Fields:
    currencyCode: The three-letter currency code defined in ISO 4217.
    nanos: Number of nano (10^-9) units of the amount. The value must be
      between -999,999,999 and +999,999,999 inclusive. If `units` is positive,
      `nanos` must be positive or zero. If `units` is zero, `nanos` can be
      positive, zero, or negative. If `units` is negative, `nanos` must be
      negative or zero. For example $-1.75 is represented as `units`=-1 and
      `nanos`=-750,000,000.
    units: The whole units of the amount. For example if `currencyCode` is
      `"USD"`, then 1 unit is one US dollar.
  """

  currencyCode = _messages.StringField(1)
  nanos = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  units = _messages.IntegerField(3)


class GoogleTypePostalAddress(_messages.Message):
  r"""Represents a postal address, such as for postal delivery or payments
  addresses. With a postal address, a postal service can deliver items to a
  premise, P.O. box, or similar. A postal address is not intended to model
  geographical locations like roads, towns, or mountains. In typical usage, an
  address would be created by user input or from importing existing data,
  depending on the type of process. Advice on address input or editing: - Use
  an internationalization-ready address widget such as
  https://github.com/google/libaddressinput. - Users should not be presented
  with UI elements for input or editing of fields outside countries where that
  field is used. For more guidance on how to use this schema, see:
  https://support.google.com/business/answer/6397478.

  Fields:
    addressLines: Unstructured address lines describing the lower levels of an
      address. Because values in `address_lines` do not have type information
      and may sometimes contain multiple values in a single field (for
      example, "Austin, TX"), it is important that the line order is clear.
      The order of address lines should be "envelope order" for the country or
      region of the address. In places where this can vary (for example,
      Japan), `address_language` is used to make it explicit (for example,
      "ja" for large-to-small ordering and "ja-Latn" or "en" for small-to-
      large). In this way, the most specific line of an address can be
      selected based on the language. The minimum permitted structural
      representation of an address consists of a `region_code` with all
      remaining information placed in the `address_lines`. It would be
      possible to format such an address very approximately without geocoding,
      but no semantic reasoning could be made about any of the address
      components until it was at least partially resolved. Creating an address
      only containing a `region_code` and `address_lines` and then geocoding
      is the recommended way to handle completely unstructured addresses (as
      opposed to guessing which parts of the address should be localities or
      administrative areas).
    administrativeArea: Optional. Highest administrative subdivision which is
      used for postal addresses of a country or region. For example, this can
      be a state, a province, an oblast, or a prefecture. For Spain, this is
      the province and not the autonomous community (for example, "Barcelona"
      and not "Catalonia"). Many countries don't use an administrative area in
      postal addresses. For example, in Switzerland, this should be left
      unpopulated.
    languageCode: Optional. BCP-47 language code of the contents of this
      address (if known). This is often the UI language of the input form or
      is expected to match one of the languages used in the address'
      country/region, or their transliterated equivalents. This can affect
      formatting in certain countries, but is not critical to the correctness
      of the data and will never affect any validation or other non-formatting
      related operations. If this value is not known, it should be omitted
      (rather than specifying a possibly incorrect default). Examples: "zh-
      Hant", "ja", "ja-Latn", "en".
    locality: Optional. Generally refers to the city or town portion of the
      address. Examples: US city, IT comune, UK post town. In regions of the
      world where localities are not well defined or do not fit into this
      structure well, leave `locality` empty and use `address_lines`.
    organization: Optional. The name of the organization at the address.
    postalCode: Optional. Postal code of the address. Not all countries use or
      require postal codes to be present, but where they are used, they may
      trigger additional validation with other parts of the address (for
      example, state or zip code validation in the United States).
    recipients: Optional. The recipient at the address. This field may, under
      certain circumstances, contain multiline information. For example, it
      might contain "care of" information.
    regionCode: Required. CLDR region code of the country/region of the
      address. This is never inferred and it is up to the user to ensure the
      value is correct. See https://cldr.unicode.org/ and https://www.unicode.
      org/cldr/charts/30/supplemental/territory_information.html for details.
      Example: "CH" for Switzerland.
    revision: The schema revision of the `PostalAddress`. This must be set to
      0, which is the latest revision. All new revisions **must** be backward
      compatible with old revisions.
    sortingCode: Optional. Additional, country-specific, sorting code. This is
      not used in most regions. Where it is used, the value is either a string
      like "CEDEX", optionally followed by a number (for example, "CEDEX 7"),
      or just a number alone, representing the "sector code" (Jamaica),
      "delivery area indicator" (Malawi) or "post office indicator" (C\xf4te
      d'Ivoire).
    sublocality: Optional. Sublocality of the address. For example, this can
      be a neighborhood, borough, or district.
  """

  addressLines = _messages.StringField(1, repeated=True)
  administrativeArea = _messages.StringField(2)
  languageCode = _messages.StringField(3)
  locality = _messages.StringField(4)
  organization = _messages.StringField(5)
  postalCode = _messages.StringField(6)
  recipients = _messages.StringField(7, repeated=True)
  regionCode = _messages.StringField(8)
  revision = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  sortingCode = _messages.StringField(10)
  sublocality = _messages.StringField(11)


class GoogleTypeTimeZone(_messages.Message):
  r"""Represents a time zone from the [IANA Time Zone
  Database](https://www.iana.org/time-zones).

  Fields:
    id: IANA Time Zone Database time zone. For example "America/New_York".
    version: Optional. IANA Time Zone Database version number. For example
      "2019a".
  """

  id = _messages.StringField(1)
  version = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
