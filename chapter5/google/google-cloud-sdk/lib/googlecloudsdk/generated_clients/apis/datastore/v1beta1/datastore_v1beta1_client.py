"""Generated client library for datastore version v1beta1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.datastore.v1beta1 import datastore_v1beta1_messages as messages


class DatastoreV1beta1(base_api.BaseApiClient):
  """Generated client library for service datastore version v1beta1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://datastore.googleapis.com/'
  MTLS_BASE_URL = 'https://datastore.mtls.googleapis.com/'

  _PACKAGE = 'datastore'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/datastore']
  _VERSION = 'v1beta1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'DatastoreV1beta1'
  _URL_VERSION = 'v1beta1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new datastore handle."""
    url = url or self.BASE_URL
    super(DatastoreV1beta1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects = self.ProjectsService(self)

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(DatastoreV1beta1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def Export(self, request, global_params=None):
      r"""Exports a copy of all or a subset of entities from Google Cloud Datastore to another storage system, such as Google Cloud Storage. Recent updates to entities may not be reflected in the export. The export occurs in the background and its progress can be monitored and managed via the Operation resource that is created. The output of an export may only be used once the associated operation is done. If an export operation is cancelled before completion it may leave partial data behind in Google Cloud Storage.

      Args:
        request: (DatastoreProjectsExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='datastore.projects.export',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=[],
        relative_path='v1beta1/projects/{projectId}:export',
        request_field='googleDatastoreAdminV1beta1ExportEntitiesRequest',
        request_type_name='DatastoreProjectsExportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Imports entities into Google Cloud Datastore. Existing entities with the same key are overwritten. The import occurs in the background and its progress can be monitored and managed via the Operation resource that is created. If an ImportEntities operation is cancelled, it is possible that a subset of the data has already been imported to Cloud Datastore.

      Args:
        request: (DatastoreProjectsImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='datastore.projects.import',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=[],
        relative_path='v1beta1/projects/{projectId}:import',
        request_field='googleDatastoreAdminV1beta1ImportEntitiesRequest',
        request_type_name='DatastoreProjectsImportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )
