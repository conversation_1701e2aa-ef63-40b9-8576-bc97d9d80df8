"""Generated client library for metastore version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.metastore.v1 import metastore_v1_messages as messages


class MetastoreV1(base_api.BaseApiClient):
  """Generated client library for service metastore version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://metastore.googleapis.com/'
  MTLS_BASE_URL = 'https://metastore.mtls.googleapis.com/'

  _PACKAGE = 'metastore'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'MetastoreV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new metastore handle."""
    url = url or self.BASE_URL
    super(MetastoreV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_federations = self.ProjectsLocationsFederationsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_services_backups = self.ProjectsLocationsServicesBackupsService(self)
    self.projects_locations_services_databases_tables = self.ProjectsLocationsServicesDatabasesTablesService(self)
    self.projects_locations_services_databases = self.ProjectsLocationsServicesDatabasesService(self)
    self.projects_locations_services_metadataImports = self.ProjectsLocationsServicesMetadataImportsService(self)
    self.projects_locations_services_migrationExecutions = self.ProjectsLocationsServicesMigrationExecutionsService(self)
    self.projects_locations_services = self.ProjectsLocationsServicesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsFederationsService(base_api.BaseApiService):
    """Service class for the projects_locations_federations resource."""

    _NAME = 'projects_locations_federations'

    def __init__(self, client):
      super(MetastoreV1.ProjectsLocationsFederationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a metastore federation in a project and location.

      Args:
        request: (MetastoreProjectsLocationsFederationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/federations',
        http_method='POST',
        method_id='metastore.projects.locations.federations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['federationId', 'requestId'],
        relative_path='v1/{+parent}/federations',
        request_field='federation',
        request_type_name='MetastoreProjectsLocationsFederationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single federation.

      Args:
        request: (MetastoreProjectsLocationsFederationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/federations/{federationsId}',
        http_method='DELETE',
        method_id='metastore.projects.locations.federations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MetastoreProjectsLocationsFederationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the details of a single federation.

      Args:
        request: (MetastoreProjectsLocationsFederationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Federation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/federations/{federationsId}',
        http_method='GET',
        method_id='metastore.projects.locations.federations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MetastoreProjectsLocationsFederationsGetRequest',
        response_type_name='Federation',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (MetastoreProjectsLocationsFederationsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/federations/{federationsId}:getIamPolicy',
        http_method='GET',
        method_id='metastore.projects.locations.federations.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='MetastoreProjectsLocationsFederationsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists federations in a project and location.

      Args:
        request: (MetastoreProjectsLocationsFederationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFederationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/federations',
        http_method='GET',
        method_id='metastore.projects.locations.federations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/federations',
        request_field='',
        request_type_name='MetastoreProjectsLocationsFederationsListRequest',
        response_type_name='ListFederationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the fields of a federation.

      Args:
        request: (MetastoreProjectsLocationsFederationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/federations/{federationsId}',
        http_method='PATCH',
        method_id='metastore.projects.locations.federations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='federation',
        request_type_name='MetastoreProjectsLocationsFederationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (MetastoreProjectsLocationsFederationsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/federations/{federationsId}:setIamPolicy',
        http_method='POST',
        method_id='metastore.projects.locations.federations.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='MetastoreProjectsLocationsFederationsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (MetastoreProjectsLocationsFederationsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/federations/{federationsId}:testIamPermissions',
        http_method='POST',
        method_id='metastore.projects.locations.federations.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='MetastoreProjectsLocationsFederationsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(MetastoreV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to Code.CANCELLED.

      Args:
        request: (MetastoreProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='metastore.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='MetastoreProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED.

      Args:
        request: (MetastoreProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='metastore.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MetastoreProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (MetastoreProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='metastore.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MetastoreProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.

      Args:
        request: (MetastoreProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='metastore.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='MetastoreProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsServicesBackupsService(base_api.BaseApiService):
    """Service class for the projects_locations_services_backups resource."""

    _NAME = 'projects_locations_services_backups'

    def __init__(self, client):
      super(MetastoreV1.ProjectsLocationsServicesBackupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new backup in a given project and location.

      Args:
        request: (MetastoreProjectsLocationsServicesBackupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/backups',
        http_method='POST',
        method_id='metastore.projects.locations.services.backups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['backupId', 'requestId'],
        relative_path='v1/{+parent}/backups',
        request_field='backup',
        request_type_name='MetastoreProjectsLocationsServicesBackupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single backup.

      Args:
        request: (MetastoreProjectsLocationsServicesBackupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/backups/{backupsId}',
        http_method='DELETE',
        method_id='metastore.projects.locations.services.backups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesBackupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single backup.

      Args:
        request: (MetastoreProjectsLocationsServicesBackupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Backup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/backups/{backupsId}',
        http_method='GET',
        method_id='metastore.projects.locations.services.backups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesBackupsGetRequest',
        response_type_name='Backup',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (MetastoreProjectsLocationsServicesBackupsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/backups/{backupsId}:getIamPolicy',
        http_method='GET',
        method_id='metastore.projects.locations.services.backups.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesBackupsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists backups in a service.

      Args:
        request: (MetastoreProjectsLocationsServicesBackupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBackupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/backups',
        http_method='GET',
        method_id='metastore.projects.locations.services.backups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/backups',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesBackupsListRequest',
        response_type_name='ListBackupsResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (MetastoreProjectsLocationsServicesBackupsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/backups/{backupsId}:setIamPolicy',
        http_method='POST',
        method_id='metastore.projects.locations.services.backups.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='MetastoreProjectsLocationsServicesBackupsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

  class ProjectsLocationsServicesDatabasesTablesService(base_api.BaseApiService):
    """Service class for the projects_locations_services_databases_tables resource."""

    _NAME = 'projects_locations_services_databases_tables'

    def __init__(self, client):
      super(MetastoreV1.ProjectsLocationsServicesDatabasesTablesService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (MetastoreProjectsLocationsServicesDatabasesTablesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/databases/{databasesId}/tables/{tablesId}:getIamPolicy',
        http_method='GET',
        method_id='metastore.projects.locations.services.databases.tables.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesDatabasesTablesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (MetastoreProjectsLocationsServicesDatabasesTablesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/databases/{databasesId}/tables/{tablesId}:setIamPolicy',
        http_method='POST',
        method_id='metastore.projects.locations.services.databases.tables.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='MetastoreProjectsLocationsServicesDatabasesTablesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

  class ProjectsLocationsServicesDatabasesService(base_api.BaseApiService):
    """Service class for the projects_locations_services_databases resource."""

    _NAME = 'projects_locations_services_databases'

    def __init__(self, client):
      super(MetastoreV1.ProjectsLocationsServicesDatabasesService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (MetastoreProjectsLocationsServicesDatabasesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/databases/{databasesId}:getIamPolicy',
        http_method='GET',
        method_id='metastore.projects.locations.services.databases.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesDatabasesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (MetastoreProjectsLocationsServicesDatabasesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/databases/{databasesId}:setIamPolicy',
        http_method='POST',
        method_id='metastore.projects.locations.services.databases.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='MetastoreProjectsLocationsServicesDatabasesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

  class ProjectsLocationsServicesMetadataImportsService(base_api.BaseApiService):
    """Service class for the projects_locations_services_metadataImports resource."""

    _NAME = 'projects_locations_services_metadataImports'

    def __init__(self, client):
      super(MetastoreV1.ProjectsLocationsServicesMetadataImportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new MetadataImport in a given project and location.

      Args:
        request: (MetastoreProjectsLocationsServicesMetadataImportsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/metadataImports',
        http_method='POST',
        method_id='metastore.projects.locations.services.metadataImports.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['metadataImportId', 'requestId'],
        relative_path='v1/{+parent}/metadataImports',
        request_field='metadataImport',
        request_type_name='MetastoreProjectsLocationsServicesMetadataImportsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single import.

      Args:
        request: (MetastoreProjectsLocationsServicesMetadataImportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MetadataImport) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/metadataImports/{metadataImportsId}',
        http_method='GET',
        method_id='metastore.projects.locations.services.metadataImports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesMetadataImportsGetRequest',
        response_type_name='MetadataImport',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists imports in a service.

      Args:
        request: (MetastoreProjectsLocationsServicesMetadataImportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMetadataImportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/metadataImports',
        http_method='GET',
        method_id='metastore.projects.locations.services.metadataImports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/metadataImports',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesMetadataImportsListRequest',
        response_type_name='ListMetadataImportsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a single import. Only the description field of MetadataImport is supported to be updated.

      Args:
        request: (MetastoreProjectsLocationsServicesMetadataImportsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/metadataImports/{metadataImportsId}',
        http_method='PATCH',
        method_id='metastore.projects.locations.services.metadataImports.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='metadataImport',
        request_type_name='MetastoreProjectsLocationsServicesMetadataImportsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsServicesMigrationExecutionsService(base_api.BaseApiService):
    """Service class for the projects_locations_services_migrationExecutions resource."""

    _NAME = 'projects_locations_services_migrationExecutions'

    def __init__(self, client):
      super(MetastoreV1.ProjectsLocationsServicesMigrationExecutionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a single migration execution.

      Args:
        request: (MetastoreProjectsLocationsServicesMigrationExecutionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/migrationExecutions/{migrationExecutionsId}',
        http_method='DELETE',
        method_id='metastore.projects.locations.services.migrationExecutions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesMigrationExecutionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single migration execution.

      Args:
        request: (MetastoreProjectsLocationsServicesMigrationExecutionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MigrationExecution) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/migrationExecutions/{migrationExecutionsId}',
        http_method='GET',
        method_id='metastore.projects.locations.services.migrationExecutions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesMigrationExecutionsGetRequest',
        response_type_name='MigrationExecution',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists migration executions on a service.

      Args:
        request: (MetastoreProjectsLocationsServicesMigrationExecutionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMigrationExecutionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/migrationExecutions',
        http_method='GET',
        method_id='metastore.projects.locations.services.migrationExecutions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/migrationExecutions',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesMigrationExecutionsListRequest',
        response_type_name='ListMigrationExecutionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsServicesService(base_api.BaseApiService):
    """Service class for the projects_locations_services resource."""

    _NAME = 'projects_locations_services'

    def __init__(self, client):
      super(MetastoreV1.ProjectsLocationsServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def AlterLocation(self, request, global_params=None):
      r"""Alter metadata resource location. The metadata resource can be a database, table, or partition. This functionality only updates the parent directory for the respective metadata resource and does not transfer any existing data to the new location.

      Args:
        request: (MetastoreProjectsLocationsServicesAlterLocationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AlterLocation')
      return self._RunMethod(
          config, request, global_params=global_params)

    AlterLocation.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:alterLocation',
        http_method='POST',
        method_id='metastore.projects.locations.services.alterLocation',
        ordered_params=['service'],
        path_params=['service'],
        query_params=[],
        relative_path='v1/{+service}:alterLocation',
        request_field='alterMetadataResourceLocationRequest',
        request_type_name='MetastoreProjectsLocationsServicesAlterLocationRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def AlterTableProperties(self, request, global_params=None):
      r"""Alter metadata table properties.

      Args:
        request: (MetastoreProjectsLocationsServicesAlterTablePropertiesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AlterTableProperties')
      return self._RunMethod(
          config, request, global_params=global_params)

    AlterTableProperties.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:alterTableProperties',
        http_method='POST',
        method_id='metastore.projects.locations.services.alterTableProperties',
        ordered_params=['service'],
        path_params=['service'],
        query_params=[],
        relative_path='v1/{+service}:alterTableProperties',
        request_field='alterTablePropertiesRequest',
        request_type_name='MetastoreProjectsLocationsServicesAlterTablePropertiesRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def CancelMigration(self, request, global_params=None):
      r"""Cancels the ongoing Managed Migration process.

      Args:
        request: (MetastoreProjectsLocationsServicesCancelMigrationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CancelMigration')
      return self._RunMethod(
          config, request, global_params=global_params)

    CancelMigration.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:cancelMigration',
        http_method='POST',
        method_id='metastore.projects.locations.services.cancelMigration',
        ordered_params=['service'],
        path_params=['service'],
        query_params=[],
        relative_path='v1/{+service}:cancelMigration',
        request_field='cancelMigrationRequest',
        request_type_name='MetastoreProjectsLocationsServicesCancelMigrationRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def CompleteMigration(self, request, global_params=None):
      r"""Completes the managed migration process. The Dataproc Metastore service will switch to using its own backend database after successful migration.

      Args:
        request: (MetastoreProjectsLocationsServicesCompleteMigrationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CompleteMigration')
      return self._RunMethod(
          config, request, global_params=global_params)

    CompleteMigration.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:completeMigration',
        http_method='POST',
        method_id='metastore.projects.locations.services.completeMigration',
        ordered_params=['service'],
        path_params=['service'],
        query_params=[],
        relative_path='v1/{+service}:completeMigration',
        request_field='completeMigrationRequest',
        request_type_name='MetastoreProjectsLocationsServicesCompleteMigrationRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a metastore service in a project and location.

      Args:
        request: (MetastoreProjectsLocationsServicesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services',
        http_method='POST',
        method_id='metastore.projects.locations.services.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'serviceId'],
        relative_path='v1/{+parent}/services',
        request_field='service',
        request_type_name='MetastoreProjectsLocationsServicesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single service.

      Args:
        request: (MetastoreProjectsLocationsServicesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}',
        http_method='DELETE',
        method_id='metastore.projects.locations.services.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ExportMetadata(self, request, global_params=None):
      r"""Exports metadata from a service.

      Args:
        request: (MetastoreProjectsLocationsServicesExportMetadataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ExportMetadata')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExportMetadata.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:exportMetadata',
        http_method='POST',
        method_id='metastore.projects.locations.services.exportMetadata',
        ordered_params=['service'],
        path_params=['service'],
        query_params=[],
        relative_path='v1/{+service}:exportMetadata',
        request_field='exportMetadataRequest',
        request_type_name='MetastoreProjectsLocationsServicesExportMetadataRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the details of a single service.

      Args:
        request: (MetastoreProjectsLocationsServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Service) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}',
        http_method='GET',
        method_id='metastore.projects.locations.services.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesGetRequest',
        response_type_name='Service',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (MetastoreProjectsLocationsServicesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:getIamPolicy',
        http_method='GET',
        method_id='metastore.projects.locations.services.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists services in a project and location.

      Args:
        request: (MetastoreProjectsLocationsServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services',
        http_method='GET',
        method_id='metastore.projects.locations.services.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/services',
        request_field='',
        request_type_name='MetastoreProjectsLocationsServicesListRequest',
        response_type_name='ListServicesResponse',
        supports_download=False,
    )

    def MoveTableToDatabase(self, request, global_params=None):
      r"""Move a table to another database.

      Args:
        request: (MetastoreProjectsLocationsServicesMoveTableToDatabaseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('MoveTableToDatabase')
      return self._RunMethod(
          config, request, global_params=global_params)

    MoveTableToDatabase.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:moveTableToDatabase',
        http_method='POST',
        method_id='metastore.projects.locations.services.moveTableToDatabase',
        ordered_params=['service'],
        path_params=['service'],
        query_params=[],
        relative_path='v1/{+service}:moveTableToDatabase',
        request_field='moveTableToDatabaseRequest',
        request_type_name='MetastoreProjectsLocationsServicesMoveTableToDatabaseRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single service.

      Args:
        request: (MetastoreProjectsLocationsServicesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}',
        http_method='PATCH',
        method_id='metastore.projects.locations.services.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='service',
        request_type_name='MetastoreProjectsLocationsServicesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def QueryMetadata(self, request, global_params=None):
      r"""Query Dataproc Metastore metadata.

      Args:
        request: (MetastoreProjectsLocationsServicesQueryMetadataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('QueryMetadata')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryMetadata.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:queryMetadata',
        http_method='POST',
        method_id='metastore.projects.locations.services.queryMetadata',
        ordered_params=['service'],
        path_params=['service'],
        query_params=[],
        relative_path='v1/{+service}:queryMetadata',
        request_field='queryMetadataRequest',
        request_type_name='MetastoreProjectsLocationsServicesQueryMetadataRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Restore(self, request, global_params=None):
      r"""Restores a service from a backup.

      Args:
        request: (MetastoreProjectsLocationsServicesRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Restore')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restore.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:restore',
        http_method='POST',
        method_id='metastore.projects.locations.services.restore',
        ordered_params=['service'],
        path_params=['service'],
        query_params=[],
        relative_path='v1/{+service}:restore',
        request_field='restoreServiceRequest',
        request_type_name='MetastoreProjectsLocationsServicesRestoreRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (MetastoreProjectsLocationsServicesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:setIamPolicy',
        http_method='POST',
        method_id='metastore.projects.locations.services.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='MetastoreProjectsLocationsServicesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def StartMigration(self, request, global_params=None):
      r"""Starts the Managed Migration process.

      Args:
        request: (MetastoreProjectsLocationsServicesStartMigrationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('StartMigration')
      return self._RunMethod(
          config, request, global_params=global_params)

    StartMigration.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:startMigration',
        http_method='POST',
        method_id='metastore.projects.locations.services.startMigration',
        ordered_params=['service'],
        path_params=['service'],
        query_params=[],
        relative_path='v1/{+service}:startMigration',
        request_field='startMigrationRequest',
        request_type_name='MetastoreProjectsLocationsServicesStartMigrationRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (MetastoreProjectsLocationsServicesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:testIamPermissions',
        http_method='POST',
        method_id='metastore.projects.locations.services.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='MetastoreProjectsLocationsServicesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(MetastoreV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (MetastoreProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='metastore.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MetastoreProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (MetastoreProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='metastore.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='MetastoreProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(MetastoreV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
