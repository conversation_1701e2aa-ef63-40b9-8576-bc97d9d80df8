"""Generated message classes for metastore version v1.

The Dataproc Metastore API is used to manage the lifecycle and configuration
of metastore services.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'metastore'


class AlterMetadataResourceLocationRequest(_messages.Message):
  r"""Request message for DataprocMetastore.AlterMetadataResourceLocation.

  Fields:
    locationUri: Required. The new location URI for the metadata resource.
    resourceName: Required. The relative metadata resource name in the
      following format.databases/{database_id} or
      databases/{database_id}/tables/{table_id} or
      databases/{database_id}/tables/{table_id}/partitions/{partition_id}
  """

  locationUri = _messages.StringField(1)
  resourceName = _messages.StringField(2)


class AlterMetadataResourceLocationResponse(_messages.Message):
  r"""Response message for DataprocMetastore.AlterMetadataResourceLocation."""


class AlterTablePropertiesRequest(_messages.Message):
  r"""Request message for DataprocMetastore.AlterTableProperties.

  Messages:
    PropertiesValue: A map that describes the desired values to mutate. If
      update_mask is empty, the properties will not update. Otherwise, the
      properties only alters the value whose associated paths exist in the
      update mask

  Fields:
    properties: A map that describes the desired values to mutate. If
      update_mask is empty, the properties will not update. Otherwise, the
      properties only alters the value whose associated paths exist in the
      update mask
    tableName: Required. The name of the table containing the properties
      you're altering in the following
      format.databases/{database_id}/tables/{table_id}
    updateMask: A field mask that specifies the metadata table properties that
      are overwritten by the update. Fields specified in the update_mask are
      relative to the resource (not to the full request). A field is
      overwritten if it is in the mask.For example, given the target
      properties: properties { a: 1 b: 2 } And an update properties:
      properties { a: 2 b: 3 c: 4 } then if the field mask is:paths:
      "properties.b", "properties.c"then the result will be: properties { a: 1
      b: 3 c: 4 }
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PropertiesValue(_messages.Message):
    r"""A map that describes the desired values to mutate. If update_mask is
    empty, the properties will not update. Otherwise, the properties only
    alters the value whose associated paths exist in the update mask

    Messages:
      AdditionalProperty: An additional property for a PropertiesValue object.

    Fields:
      additionalProperties: Additional properties of type PropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  properties = _messages.MessageField('PropertiesValue', 1)
  tableName = _messages.StringField(2)
  updateMask = _messages.StringField(3)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs.If there are AuditConfigs for both allServices and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted.Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It <NAME_EMAIL> from DATA_READ logging, and
  <EMAIL> from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, storage.googleapis.com, cloudsql.googleapis.com. allServices is
      a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class AutoscalingConfig(_messages.Message):
  r"""Represents the autoscaling configuration of a metastore service.

  Fields:
    autoscalingEnabled: Optional. Whether or not autoscaling is enabled for
      this service.
    autoscalingFactor: Output only. The scaling factor of a service with
      autoscaling enabled.
    limitConfig: Optional. The LimitConfig of the service.
  """

  autoscalingEnabled = _messages.BooleanField(1)
  autoscalingFactor = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  limitConfig = _messages.MessageField('LimitConfig', 3)


class AuxiliaryVersionConfig(_messages.Message):
  r"""Configuration information for the auxiliary service versions.

  Messages:
    ConfigOverridesValue: Optional. A mapping of Hive metastore configuration
      key-value pairs to apply to the auxiliary Hive metastore (configured in
      hive-site.xml) in addition to the primary version's overrides. If keys
      are present in both the auxiliary version's overrides and the primary
      version's overrides, the value from the auxiliary version's overrides
      takes precedence.

  Fields:
    configOverrides: Optional. A mapping of Hive metastore configuration key-
      value pairs to apply to the auxiliary Hive metastore (configured in
      hive-site.xml) in addition to the primary version's overrides. If keys
      are present in both the auxiliary version's overrides and the primary
      version's overrides, the value from the auxiliary version's overrides
      takes precedence.
    networkConfig: Output only. The network configuration contains the
      endpoint URI(s) of the auxiliary Hive metastore service.
    version: Optional. The Hive metastore version of the auxiliary service. It
      must be less than the primary Hive metastore service's version.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConfigOverridesValue(_messages.Message):
    r"""Optional. A mapping of Hive metastore configuration key-value pairs to
    apply to the auxiliary Hive metastore (configured in hive-site.xml) in
    addition to the primary version's overrides. If keys are present in both
    the auxiliary version's overrides and the primary version's overrides, the
    value from the auxiliary version's overrides takes precedence.

    Messages:
      AdditionalProperty: An additional property for a ConfigOverridesValue
        object.

    Fields:
      additionalProperties: Additional properties of type ConfigOverridesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConfigOverridesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  configOverrides = _messages.MessageField('ConfigOverridesValue', 1)
  networkConfig = _messages.MessageField('NetworkConfig', 2)
  version = _messages.StringField(3)


class BackendMetastore(_messages.Message):
  r"""Represents a backend metastore for the federation.

  Enums:
    MetastoreTypeValueValuesEnum: The type of the backend metastore.

  Fields:
    metastoreType: The type of the backend metastore.
    name: The relative resource name of the metastore that is being federated.
      The formats of the relative resource names for the currently supported
      metastores are listed below: BigQuery projects/{project_id} Dataproc
      Metastore
      projects/{project_id}/locations/{location}/services/{service_id}
  """

  class MetastoreTypeValueValuesEnum(_messages.Enum):
    r"""The type of the backend metastore.

    Values:
      METASTORE_TYPE_UNSPECIFIED: The metastore type is not set.
      BIGQUERY: The backend metastore is BigQuery.
      DATAPROC_METASTORE: The backend metastore is Dataproc Metastore.
    """
    METASTORE_TYPE_UNSPECIFIED = 0
    BIGQUERY = 1
    DATAPROC_METASTORE = 2

  metastoreType = _messages.EnumField('MetastoreTypeValueValuesEnum', 1)
  name = _messages.StringField(2)


class Backup(_messages.Message):
  r"""The details of a backup resource.

  Enums:
    StateValueValuesEnum: Output only. The current state of the backup.

  Fields:
    createTime: Output only. The time when the backup was started.
    description: Optional. The description of the backup.
    endTime: Output only. The time when the backup finished creating.
    name: Immutable. Identifier. The relative resource name of the backup, in
      the following form:projects/{project_number}/locations/{location_id}/ser
      vices/{service_id}/backups/{backup_id}
    restoringServices: Output only. Services that are restoring from the
      backup.
    serviceRevision: Output only. The revision of the service at the time of
      backup.
    state: Output only. The current state of the backup.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the backup.

    Values:
      STATE_UNSPECIFIED: The state of the backup is unknown.
      CREATING: The backup is being created.
      DELETING: The backup is being deleted.
      ACTIVE: The backup is active and ready to use.
      FAILED: The backup failed.
      RESTORING: The backup is being restored.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    DELETING = 2
    ACTIVE = 3
    FAILED = 4
    RESTORING = 5

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  endTime = _messages.StringField(3)
  name = _messages.StringField(4)
  restoringServices = _messages.StringField(5, repeated=True)
  serviceRevision = _messages.MessageField('Service', 6)
  state = _messages.EnumField('StateValueValuesEnum', 7)


class BigQueryMetastoreMigrationConfig(_messages.Message):
  r"""Specifies the configuration required for migrating to BigQuery Metastore
  service.

  Enums:
    DesiredMigrationStateValueValuesEnum: Required. The desired state of the
      migration. Note that this also reflects the current state of the
      migration. If an attempt to update to a new desired state fails, the
      migration will revert to the previous state.

  Fields:
    bigqueryDatasetLocation: Required. The location where the BigQuery
      resources (e.g. datasets, tables, etc.) should be created (e.g. us-
      central1, us, eu, etc.)
    bigqueryProjectId: Required. The project ID where the BigQuery resources
      (e.g. datasets, tables, etc.) should be created.
    desiredMigrationState: Required. The desired state of the migration. Note
      that this also reflects the current state of the migration. If an
      attempt to update to a new desired state fails, the migration will
      revert to the previous state.
  """

  class DesiredMigrationStateValueValuesEnum(_messages.Enum):
    r"""Required. The desired state of the migration. Note that this also
    reflects the current state of the migration. If an attempt to update to a
    new desired state fails, the migration will revert to the previous state.

    Values:
      DESIRED_MIGRATION_STATE_UNSPECIFIED: The default value. This value is
        unused.
      MIGRATE: By setting the desired migration state to MIGRATE, metadata
        updates in Dataproc Metastore will be replicated to the BigQuery
        Metastore service, ensuring that it remains consistently synchronized
        with Dataproc Metastore. Note that this includes initial backfill of
        existing metadata.
      CUTOVER: By setting the desired migration state to CUTOVER, all metadata
        requests are routed to BigQuery Metastore service and Dataproc
        Metastore only functions as a proxy. This state can be considered as
        the completion of the migration.
      CANCEL: By setting the desired migration state to CANCEL, the migration
        is effectively cancelled. If the previous migration state was MIGRATE,
        then replication to BigQuery Metastore will be cancelled. If the
        previous state was CUTOVER, then metadata requests will now be served
        from Dataproc Metastore instead of BigQuery Metastore. Note that
        existing metadata changes replicated to BigQuery Metastore service are
        not rolled back.
    """
    DESIRED_MIGRATION_STATE_UNSPECIFIED = 0
    MIGRATE = 1
    CUTOVER = 2
    CANCEL = 3

  bigqueryDatasetLocation = _messages.StringField(1)
  bigqueryProjectId = _messages.StringField(2)
  desiredMigrationState = _messages.EnumField('DesiredMigrationStateValueValuesEnum', 3)


class Binding(_messages.Message):
  r"""Associates members, or principals, with a role.

  Fields:
    condition: The condition that is associated with this binding.If the
      condition evaluates to true, then this binding applies to the current
      request.If the condition evaluates to false, then this binding does not
      apply to the current request. However, a different role binding might
      grant the same role to one or more of the principals in this binding.To
      learn which resources support conditions in their IAM policies, see the
      IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. members can have the following values: allUsers: A special
      identifier that represents anyone who is on the internet; with or
      without a Google account. allAuthenticatedUsers: A special identifier
      that represents anyone who is authenticated with a Google account or a
      service account. Does not include identities that come from external
      identity providers (IdPs) through identity federation. user:{emailid}:
      An email address that represents a specific Google account. For example,
      <EMAIL> . serviceAccount:{emailid}: An email address that
      represents a Google service account. For example, my-other-
      <EMAIL>.
      serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]: An
      identifier for a Kubernetes service account
      (https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-
      service-accounts). For example, my-project.svc.id.goog[my-namespace/my-
      kubernetes-sa]. group:{emailid}: An email address that represents a
      Google group. For example, <EMAIL>. domain:{domain}: The G
      Suite domain (primary) that represents all the users of that domain. For
      example, google.com or example.com. principal://iam.googleapis.com/locat
      ions/global/workforcePools/{pool_id}/subject/{subject_attribute_value}:
      A single identity in a workforce identity pool. principalSet://iam.googl
      eapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}:
      All workforce identities in a group. principalSet://iam.googleapis.com/l
      ocations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{att
      ribute_value}: All workforce identities with a specific attribute value.
      principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_
      id}/*: All identities in a workforce identity pool. principal://iam.goog
      leapis.com/projects/{project_number}/locations/global/workloadIdentityPo
      ols/{pool_id}/subject/{subject_attribute_value}: A single identity in a
      workload identity pool. principalSet://iam.googleapis.com/projects/{proj
      ect_number}/locations/global/workloadIdentityPools/{pool_id}/group/{grou
      p_id}: A workload identity pool group. principalSet://iam.googleapis.com
      /projects/{project_number}/locations/global/workloadIdentityPools/{pool_
      id}/attribute.{attribute_name}/{attribute_value}: All identities in a
      workload identity pool with a certain attribute. principalSet://iam.goog
      leapis.com/projects/{project_number}/locations/global/workloadIdentityPo
      ols/{pool_id}/*: All identities in a workload identity pool.
      deleted:user:{emailid}?uid={uniqueid}: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, <EMAIL>?uid=123456789012345678901. If the user is
      recovered, this value reverts to user:{emailid} and the recovered user
      retains the role in the binding.
      deleted:serviceAccount:{emailid}?uid={uniqueid}: An email address (plus
      unique identifier) representing a service account that has been recently
      deleted. For example, my-other-
      <EMAIL>?uid=123456789012345678901. If the
      service account is undeleted, this value reverts to
      serviceAccount:{emailid} and the undeleted service account retains the
      role in the binding. deleted:group:{emailid}?uid={uniqueid}: An email
      address (plus unique identifier) representing a Google group that has
      been recently deleted. For example,
      <EMAIL>?uid=123456789012345678901. If the group is recovered,
      this value reverts to group:{emailid} and the recovered group retains
      the role in the binding. deleted:principal://iam.googleapis.com/location
      s/global/workforcePools/{pool_id}/subject/{subject_attribute_value}:
      Deleted single identity in a workforce identity pool. For example, delet
      ed:principal://iam.googleapis.com/locations/global/workforcePools/my-
      pool-id/subject/my-subject-attribute-value.
    role: Role that is assigned to the list of members, or principals. For
      example, roles/viewer, roles/editor, or roles/owner.For an overview of
      the IAM roles and permissions, see the IAM documentation
      (https://cloud.google.com/iam/docs/roles-overview). For a list of the
      available pre-defined roles, see here
      (https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelMigrationRequest(_messages.Message):
  r"""Request message for DataprocMetastore.CancelMigration."""


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CdcConfig(_messages.Message):
  r"""Configuration information to start the Change Data Capture (CDC) streams
  from customer database to backend database of Dataproc Metastore.

  Fields:
    bucket: Optional. The bucket to write the intermediate stream event data
      in. The bucket name must be without any prefix like "gs://". See the
      bucket naming requirements
      (https://cloud.google.com/storage/docs/buckets#naming). This field is
      optional. If not set, the Artifacts Cloud Storage bucket will be used.
    password: Required. Input only. The password for the user that Datastream
      service should use for the MySQL connection. This field is not returned
      on request.
    reverseProxySubnet: Required. The URL of the subnetwork resource to create
      the VM instance hosting the reverse proxy in. More context in
      https://cloud.google.com/datastream/docs/private-connectivity#reverse-
      csql-proxy The subnetwork should reside in the network provided in the
      request that Datastream will peer to and should be in the same region as
      Datastream, in the following format.
      projects/{project_id}/regions/{region_id}/subnetworks/{subnetwork_id}
    rootPath: Optional. The root path inside the Cloud Storage bucket. The
      stream event data will be written to this path. The default value is
      /migration.
    subnetIpRange: Required. A /29 CIDR IP range for peering with datastream.
    username: Required. The username that the Datastream service should use
      for the MySQL connection.
    vpcNetwork: Required. Fully qualified name of the Cloud SQL instance's VPC
      network or the shared VPC network that Datastream will peer to, in the
      following format:
      projects/{project_id}/locations/global/networks/{network_id}. More
      context in https://cloud.google.com/datastream/docs/network-
      connectivity-options#privateconnectivity
  """

  bucket = _messages.StringField(1)
  password = _messages.StringField(2)
  reverseProxySubnet = _messages.StringField(3)
  rootPath = _messages.StringField(4)
  subnetIpRange = _messages.StringField(5)
  username = _messages.StringField(6)
  vpcNetwork = _messages.StringField(7)


class CloudSQLConnectionConfig(_messages.Message):
  r"""Configuration information to establish customer database connection
  before the cutover phase of migration

  Fields:
    hiveDatabaseName: Required. The hive database name.
    instanceConnectionName: Required. Cloud SQL database connection name
      (project_id:region:instance_name)
    ipAddress: Required. The private IP address of the Cloud SQL instance.
    natSubnet: Required. The relative resource name of the subnetwork to be
      used for Private Service Connect. Note that this cannot be a regular
      subnet and is used only for NAT.
      (https://cloud.google.com/vpc/docs/about-vpc-hosted-services#psc-
      subnets) This subnet is used to publish the SOCKS5 proxy service. The
      subnet size must be at least /29 and it should reside in a network
      through which the Cloud SQL instance is accessible. The resource name
      should be in the format,
      projects/{project_id}/regions/{region_id}/subnetworks/{subnetwork_id}
    password: Required. Input only. The password for the user that Dataproc
      Metastore service will be using to connect to the database. This field
      is not returned on request.
    port: Required. The network port of the database.
    proxySubnet: Required. The relative resource name of the subnetwork to
      deploy the SOCKS5 proxy service in. The subnetwork should reside in a
      network through which the Cloud SQL instance is accessible. The resource
      name should be in the format,
      projects/{project_id}/regions/{region_id}/subnetworks/{subnetwork_id}
    username: Required. The username that Dataproc Metastore service will use
      to connect to the database.
  """

  hiveDatabaseName = _messages.StringField(1)
  instanceConnectionName = _messages.StringField(2)
  ipAddress = _messages.StringField(3)
  natSubnet = _messages.StringField(4)
  password = _messages.StringField(5)
  port = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  proxySubnet = _messages.StringField(7)
  username = _messages.StringField(8)


class CloudSQLMigrationConfig(_messages.Message):
  r"""Configuration information for migrating from self-managed hive metastore
  on Google Cloud using Cloud SQL as the backend database to Dataproc
  Metastore.

  Fields:
    cdcConfig: Required. Configuration information to start the Change Data
      Capture (CDC) streams from customer database to backend database of
      Dataproc Metastore. Dataproc Metastore switches to using its backend
      database after the cutover phase of migration.
    cloudSqlConnectionConfig: Required. Configuration information to establish
      customer database connection before the cutover phase of migration
  """

  cdcConfig = _messages.MessageField('CdcConfig', 1)
  cloudSqlConnectionConfig = _messages.MessageField('CloudSQLConnectionConfig', 2)


class CompleteMigrationRequest(_messages.Message):
  r"""Request message for DataprocMetastore.CompleteMigration."""


class Consumer(_messages.Message):
  r"""Contains information of the customer's network configurations.

  Fields:
    endpointLocation: Output only. The location of the endpoint URI. Format:
      projects/{project}/locations/{location}.
    endpointUri: Output only. The URI of the endpoint used to access the
      metastore service.
    subnetwork: Immutable. The subnetwork of the customer project from which
      an IP address is reserved and used as the Dataproc Metastore service's
      endpoint. It is accessible to hosts in the subnet and to all hosts in a
      subnet in the same region and same network. There must be at least one
      IP address available in the subnet's primary range. The subnet is
      specified in the following form:projects/{project_number}/regions/{regio
      n_id}/subnetworks/{subnetwork_id}
  """

  endpointLocation = _messages.StringField(1)
  endpointUri = _messages.StringField(2)
  subnetwork = _messages.StringField(3)


class CustomRegionMetadata(_messages.Message):
  r"""Metadata about a custom region. This is only populated if the region is
  a custom region. For single/multi regions, it will be empty.

  Fields:
    optionalReadOnlyRegions: The read-only regions for this custom region.
    requiredReadWriteRegions: The read-write regions for this custom region.
    witnessRegion: The Spanner witness region for this custom region.
  """

  optionalReadOnlyRegions = _messages.StringField(1, repeated=True)
  requiredReadWriteRegions = _messages.StringField(2, repeated=True)
  witnessRegion = _messages.StringField(3)


class DataCatalogConfig(_messages.Message):
  r"""Specifies how metastore metadata should be integrated with the Data
  Catalog service.

  Fields:
    enabled: Optional. Defines whether the metastore metadata should be synced
      to Data Catalog. The default value is to disable syncing metastore
      metadata to Data Catalog.
  """

  enabled = _messages.BooleanField(1)


class DatabaseDump(_messages.Message):
  r"""A specification of the location of and metadata about a database dump
  from a relational database management system.

  Enums:
    DatabaseTypeValueValuesEnum: The type of the database.
    TypeValueValuesEnum: Optional. The type of the database dump. If
      unspecified, defaults to MYSQL.

  Fields:
    databaseType: The type of the database.
    gcsUri: Optional. A Cloud Storage object or folder URI that specifies the
      source from which to import metadata. It must begin with gs://.
    sourceDatabase: Optional. The name of the source database.
    type: Optional. The type of the database dump. If unspecified, defaults to
      MYSQL.
  """

  class DatabaseTypeValueValuesEnum(_messages.Enum):
    r"""The type of the database.

    Values:
      DATABASE_TYPE_UNSPECIFIED: The type of the source database is unknown.
      MYSQL: The type of the source database is MySQL.
    """
    DATABASE_TYPE_UNSPECIFIED = 0
    MYSQL = 1

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of the database dump. If unspecified, defaults to
    MYSQL.

    Values:
      TYPE_UNSPECIFIED: The type of the database dump is unknown.
      MYSQL: Database dump is a MySQL dump file.
      AVRO: Database dump contains Avro files.
    """
    TYPE_UNSPECIFIED = 0
    MYSQL = 1
    AVRO = 2

  databaseType = _messages.EnumField('DatabaseTypeValueValuesEnum', 1)
  gcsUri = _messages.StringField(2)
  sourceDatabase = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionConfig(_messages.Message):
  r"""Encryption settings for the service.

  Fields:
    kmsKey: Optional. The fully qualified customer provided Cloud KMS key name
      to use for customer data encryption, in the following format:projects/{p
      roject_number}/locations/{location_id}/keyRings/{key_ring_id}/cryptoKeys
      /{crypto_key_id}.
  """

  kmsKey = _messages.StringField(1)


class ErrorDetails(_messages.Message):
  r"""Error details in public error message for
  DataprocMetastore.QueryMetadata.

  Messages:
    DetailsValue: Additional structured details about this error.Keys define
      the failure items. Value describes the exception or details of the item.

  Fields:
    details: Additional structured details about this error.Keys define the
      failure items. Value describes the exception or details of the item.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValue(_messages.Message):
    r"""Additional structured details about this error.Keys define the failure
    items. Value describes the exception or details of the item.

    Messages:
      AdditionalProperty: An additional property for a DetailsValue object.

    Fields:
      additionalProperties: Additional properties of type DetailsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  details = _messages.MessageField('DetailsValue', 1)


class ExportMetadataRequest(_messages.Message):
  r"""Request message for DataprocMetastore.ExportMetadata.

  Enums:
    DatabaseDumpTypeValueValuesEnum: Optional. The type of the database dump.
      If unspecified, defaults to MYSQL.

  Fields:
    databaseDumpType: Optional. The type of the database dump. If unspecified,
      defaults to MYSQL.
    destinationGcsFolder: A Cloud Storage URI of a folder, in the format
      gs:///. A sub-folder containing exported files will be created below it.
    requestId: Optional. A request ID. Specify a unique request ID to allow
      the server to ignore the request if it has completed. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 60 minutes after the first request.For example, if an initial
      request times out, followed by another request with the same request ID,
      the server ignores the second request to prevent the creation of
      duplicate commitments.The request ID must be a valid UUID
      (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format). A
      zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  """

  class DatabaseDumpTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of the database dump. If unspecified, defaults to
    MYSQL.

    Values:
      TYPE_UNSPECIFIED: The type of the database dump is unknown.
      MYSQL: Database dump is a MySQL dump file.
      AVRO: Database dump contains Avro files.
    """
    TYPE_UNSPECIFIED = 0
    MYSQL = 1
    AVRO = 2

  databaseDumpType = _messages.EnumField('DatabaseDumpTypeValueValuesEnum', 1)
  destinationGcsFolder = _messages.StringField(2)
  requestId = _messages.StringField(3)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec.Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Federation(_messages.Message):
  r"""Represents a federation of multiple backend metastores.

  Enums:
    StateValueValuesEnum: Output only. The current state of the federation.

  Messages:
    BackendMetastoresValue: A map from BackendMetastore rank to
      BackendMetastores from which the federation service serves metadata at
      query time. The map key represents the order in which BackendMetastores
      should be evaluated to resolve database names at query time and should
      be greater than or equal to zero. A BackendMetastore with a lower number
      will be evaluated before a BackendMetastore with a higher number.
    LabelsValue: User-defined labels for the metastore federation.
    TagsValue: Optional. Input only. Immutable. Tag keys/values directly bound
      to this resource. For example: "123/environment": "production",
      "123/costCenter": "marketing"

  Fields:
    backendMetastores: A map from BackendMetastore rank to BackendMetastores
      from which the federation service serves metadata at query time. The map
      key represents the order in which BackendMetastores should be evaluated
      to resolve database names at query time and should be greater than or
      equal to zero. A BackendMetastore with a lower number will be evaluated
      before a BackendMetastore with a higher number.
    createTime: Output only. The time when the metastore federation was
      created.
    endpointUri: Output only. The federation endpoint.
    labels: User-defined labels for the metastore federation.
    name: Immutable. The relative resource name of the federation, of the
      form: projects/{project_number}/locations/{location_id}/federations/{fed
      eration_id}`.
    state: Output only. The current state of the federation.
    stateMessage: Output only. Additional information about the current state
      of the metastore federation, if available.
    tags: Optional. Input only. Immutable. Tag keys/values directly bound to
      this resource. For example: "123/environment": "production",
      "123/costCenter": "marketing"
    uid: Output only. The globally unique resource identifier of the metastore
      federation.
    updateTime: Output only. The time when the metastore federation was last
      updated.
    version: Immutable. The Apache Hive metastore version of the federation.
      All backend metastore versions must be compatible with the federation
      version.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the federation.

    Values:
      STATE_UNSPECIFIED: The state of the metastore federation is unknown.
      CREATING: The metastore federation is in the process of being created.
      ACTIVE: The metastore federation is running and ready to serve queries.
      UPDATING: The metastore federation is being updated. It remains usable
        but cannot accept additional update requests or be deleted at this
        time.
      DELETING: The metastore federation is undergoing deletion. It cannot be
        used.
      ERROR: The metastore federation has encountered an error and cannot be
        used. The metastore federation should be deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    UPDATING = 3
    DELETING = 4
    ERROR = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BackendMetastoresValue(_messages.Message):
    r"""A map from BackendMetastore rank to BackendMetastores from which the
    federation service serves metadata at query time. The map key represents
    the order in which BackendMetastores should be evaluated to resolve
    database names at query time and should be greater than or equal to zero.
    A BackendMetastore with a lower number will be evaluated before a
    BackendMetastore with a higher number.

    Messages:
      AdditionalProperty: An additional property for a BackendMetastoresValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        BackendMetastoresValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BackendMetastoresValue object.

      Fields:
        key: Name of the additional property.
        value: A BackendMetastore attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('BackendMetastore', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-defined labels for the metastore federation.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Input only. Immutable. Tag keys/values directly bound to
    this resource. For example: "123/environment": "production",
    "123/costCenter": "marketing"

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backendMetastores = _messages.MessageField('BackendMetastoresValue', 1)
  createTime = _messages.StringField(2)
  endpointUri = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  stateMessage = _messages.StringField(7)
  tags = _messages.MessageField('TagsValue', 8)
  uid = _messages.StringField(9)
  updateTime = _messages.StringField(10)
  version = _messages.StringField(11)


class HiveMetastoreConfig(_messages.Message):
  r"""Specifies configuration information specific to running Hive metastore
  software as the metastore service.

  Enums:
    EndpointProtocolValueValuesEnum: Optional. The protocol to use for the
      metastore service endpoint. If unspecified, defaults to THRIFT.

  Messages:
    AuxiliaryVersionsValue: Optional. A mapping of Hive metastore version to
      the auxiliary version configuration. When specified, a secondary Hive
      metastore service is created along with the primary service. All
      auxiliary versions must be less than the service's primary version. The
      key is the auxiliary service name and it must match the regular
      expression a-z?. This means that the first character must be a lowercase
      letter, and all the following characters must be hyphens, lowercase
      letters, or digits, except the last character, which cannot be a hyphen.
    ConfigOverridesValue: Optional. A mapping of Hive metastore configuration
      key-value pairs to apply to the Hive metastore (configured in hive-
      site.xml). The mappings override system defaults (some keys cannot be
      overridden). These overrides are also applied to auxiliary versions and
      can be further customized in the auxiliary version's
      AuxiliaryVersionConfig.

  Fields:
    auxiliaryVersions: Optional. A mapping of Hive metastore version to the
      auxiliary version configuration. When specified, a secondary Hive
      metastore service is created along with the primary service. All
      auxiliary versions must be less than the service's primary version. The
      key is the auxiliary service name and it must match the regular
      expression a-z?. This means that the first character must be a lowercase
      letter, and all the following characters must be hyphens, lowercase
      letters, or digits, except the last character, which cannot be a hyphen.
    configOverrides: Optional. A mapping of Hive metastore configuration key-
      value pairs to apply to the Hive metastore (configured in hive-
      site.xml). The mappings override system defaults (some keys cannot be
      overridden). These overrides are also applied to auxiliary versions and
      can be further customized in the auxiliary version's
      AuxiliaryVersionConfig.
    endpointProtocol: Optional. The protocol to use for the metastore service
      endpoint. If unspecified, defaults to THRIFT.
    kerberosConfig: Optional. Information used to configure the Hive metastore
      service as a service principal in a Kerberos realm. To disable Kerberos,
      use the UpdateService method and specify this field's path
      (hive_metastore_config.kerberos_config) in the request's update_mask
      while omitting this field from the request's service.
    version: Immutable. The Hive metastore schema version.
  """

  class EndpointProtocolValueValuesEnum(_messages.Enum):
    r"""Optional. The protocol to use for the metastore service endpoint. If
    unspecified, defaults to THRIFT.

    Values:
      ENDPOINT_PROTOCOL_UNSPECIFIED: The protocol is not set.
      THRIFT: Use the legacy Apache Thrift protocol for the metastore service
        endpoint.
      GRPC: Use the modernized gRPC protocol for the metastore service
        endpoint.
    """
    ENDPOINT_PROTOCOL_UNSPECIFIED = 0
    THRIFT = 1
    GRPC = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AuxiliaryVersionsValue(_messages.Message):
    r"""Optional. A mapping of Hive metastore version to the auxiliary version
    configuration. When specified, a secondary Hive metastore service is
    created along with the primary service. All auxiliary versions must be
    less than the service's primary version. The key is the auxiliary service
    name and it must match the regular expression a-z?. This means that the
    first character must be a lowercase letter, and all the following
    characters must be hyphens, lowercase letters, or digits, except the last
    character, which cannot be a hyphen.

    Messages:
      AdditionalProperty: An additional property for a AuxiliaryVersionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        AuxiliaryVersionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AuxiliaryVersionsValue object.

      Fields:
        key: Name of the additional property.
        value: A AuxiliaryVersionConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('AuxiliaryVersionConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConfigOverridesValue(_messages.Message):
    r"""Optional. A mapping of Hive metastore configuration key-value pairs to
    apply to the Hive metastore (configured in hive-site.xml). The mappings
    override system defaults (some keys cannot be overridden). These overrides
    are also applied to auxiliary versions and can be further customized in
    the auxiliary version's AuxiliaryVersionConfig.

    Messages:
      AdditionalProperty: An additional property for a ConfigOverridesValue
        object.

    Fields:
      additionalProperties: Additional properties of type ConfigOverridesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConfigOverridesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  auxiliaryVersions = _messages.MessageField('AuxiliaryVersionsValue', 1)
  configOverrides = _messages.MessageField('ConfigOverridesValue', 2)
  endpointProtocol = _messages.EnumField('EndpointProtocolValueValuesEnum', 3)
  kerberosConfig = _messages.MessageField('KerberosConfig', 4)
  version = _messages.StringField(5)


class HiveMetastoreVersion(_messages.Message):
  r"""A specification of a supported version of the Hive Metastore software.

  Fields:
    isDefault: Whether version will be chosen by the server if a metastore
      service is created with a HiveMetastoreConfig that omits the version.
    version: The semantic version of the Hive Metastore software.
  """

  isDefault = _messages.BooleanField(1)
  version = _messages.StringField(2)


class KerberosConfig(_messages.Message):
  r"""Configuration information for a Kerberos principal.

  Fields:
    keytab: Optional. A Kerberos keytab file that can be used to authenticate
      a service principal with a Kerberos Key Distribution Center (KDC).
    krb5ConfigGcsUri: Optional. A Cloud Storage URI that specifies the path to
      a krb5.conf file. It is of the form
      gs://{bucket_name}/path/to/krb5.conf, although the file does not need to
      be named krb5.conf explicitly.
    principal: Optional. A Kerberos principal that exists in the both the
      keytab the KDC to authenticate as. A typical principal is of the form
      primary/instance@REALM, but there is no exact format.
  """

  keytab = _messages.MessageField('Secret', 1)
  krb5ConfigGcsUri = _messages.StringField(2)
  principal = _messages.StringField(3)


class LatestBackup(_messages.Message):
  r"""The details of the latest scheduled backup.

  Enums:
    StateValueValuesEnum: Output only. The current state of the backup.

  Fields:
    backupId: Output only. The ID of an in-progress scheduled backup. Empty if
      no backup is in progress.
    duration: Output only. The duration of the backup completion.
    startTime: Output only. The time when the backup was started.
    state: Output only. The current state of the backup.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the backup.

    Values:
      STATE_UNSPECIFIED: The state of the backup is unknown.
      IN_PROGRESS: The backup is in progress.
      SUCCEEDED: The backup completed.
      FAILED: The backup failed.
    """
    STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    SUCCEEDED = 2
    FAILED = 3

  backupId = _messages.StringField(1)
  duration = _messages.StringField(2)
  startTime = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class LimitConfig(_messages.Message):
  r"""Represents the autoscaling limit configuration of a metastore service.

  Fields:
    maxScalingFactor: Optional. The highest scaling factor that the service
      should be autoscaled to.
    minScalingFactor: Optional. The lowest scaling factor that the service
      should be autoscaled to.
  """

  maxScalingFactor = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  minScalingFactor = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class ListBackupsResponse(_messages.Message):
  r"""Response message for DataprocMetastore.ListBackups.

  Fields:
    backups: The backups of the specified service.
    nextPageToken: A token that can be sent as page_token to retrieve the next
      page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  backups = _messages.MessageField('Backup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListFederationsResponse(_messages.Message):
  r"""Response message for ListFederations

  Fields:
    federations: The services in the specified location.
    nextPageToken: A token that can be sent as page_token to retrieve the next
      page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  federations = _messages.MessageField('Federation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMetadataImportsResponse(_messages.Message):
  r"""Response message for DataprocMetastore.ListMetadataImports.

  Fields:
    metadataImports: The imports in the specified service.
    nextPageToken: A token that can be sent as page_token to retrieve the next
      page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  metadataImports = _messages.MessageField('MetadataImport', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMigrationExecutionsResponse(_messages.Message):
  r"""Response message for DataprocMetastore.ListMigrationExecutions.

  Fields:
    migrationExecutions: The migration executions on the specified service.
    nextPageToken: A token that can be sent as page_token to retrieve the next
      page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  migrationExecutions = _messages.MessageField('MigrationExecution', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListServicesResponse(_messages.Message):
  r"""Response message for DataprocMetastore.ListServices.

  Fields:
    nextPageToken: A token that can be sent as page_token to retrieve the next
      page. If this field is omitted, there are no subsequent pages.
    services: The services in the specified location.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  services = _messages.MessageField('Service', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: "us-east1".
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: "projects/example-project/locations/us-
      east1"
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LocationMetadata(_messages.Message):
  r"""Metadata about the service in a location.

  Fields:
    customRegionMetadata: Possible configurations supported if the current
      region is a custom region.
    multiRegionMetadata: The multi-region metadata if the current region is a
      multi-region.
    supportedHiveMetastoreVersions: The versions of Hive Metastore that can be
      used when creating a new metastore service in this location. The server
      guarantees that exactly one HiveMetastoreVersion in the list will set
      is_default.
  """

  customRegionMetadata = _messages.MessageField('CustomRegionMetadata', 1, repeated=True)
  multiRegionMetadata = _messages.MessageField('MultiRegionMetadata', 2)
  supportedHiveMetastoreVersions = _messages.MessageField('HiveMetastoreVersion', 3, repeated=True)


class MaintenanceWindow(_messages.Message):
  r"""Maintenance window. This specifies when Dataproc Metastore may perform
  system maintenance operation to the service.

  Enums:
    DayOfWeekValueValuesEnum: Optional. The day of week, when the window
      starts.

  Fields:
    dayOfWeek: Optional. The day of week, when the window starts.
    hourOfDay: Optional. The hour of day (0-23) when the window starts.
  """

  class DayOfWeekValueValuesEnum(_messages.Enum):
    r"""Optional. The day of week, when the window starts.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  dayOfWeek = _messages.EnumField('DayOfWeekValueValuesEnum', 1)
  hourOfDay = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class MessageSet(_messages.Message):
  r"""This is proto2's version of MessageSet.DEPRECATED: DO NOT USE FOR NEW
  FIELDS.If you are using editions or proto2, please make your own extendable
  messages for your use case. If you are using proto3, please use Any
  instead.MessageSet was the implementation of extensions for proto1. When
  proto2 was introduced, extensions were implemented as a first-class feature.
  This schema for MessageSet was meant to be a "bridge" solution to migrate
  MessageSet-bearing messages from proto1 to proto2.This schema has been open-
  sourced only to facilitate the migration of Google products with MessageSet-
  bearing messages to open-source environments.
  """



class MetadataExport(_messages.Message):
  r"""The details of a metadata export operation.

  Enums:
    DatabaseDumpTypeValueValuesEnum: Output only. The type of the database
      dump.
    StateValueValuesEnum: Output only. The current state of the export.

  Fields:
    databaseDumpType: Output only. The type of the database dump.
    destinationGcsUri: Output only. A Cloud Storage URI of a folder that
      metadata are exported to, in the form of gs:////, where is automatically
      generated.
    endTime: Output only. The time when the export ended.
    startTime: Output only. The time when the export started.
    state: Output only. The current state of the export.
  """

  class DatabaseDumpTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the database dump.

    Values:
      TYPE_UNSPECIFIED: The type of the database dump is unknown.
      MYSQL: Database dump is a MySQL dump file.
      AVRO: Database dump contains Avro files.
    """
    TYPE_UNSPECIFIED = 0
    MYSQL = 1
    AVRO = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the export.

    Values:
      STATE_UNSPECIFIED: The state of the metadata export is unknown.
      RUNNING: The metadata export is running.
      SUCCEEDED: The metadata export completed successfully.
      FAILED: The metadata export failed.
      CANCELLED: The metadata export is cancelled.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    SUCCEEDED = 2
    FAILED = 3
    CANCELLED = 4

  databaseDumpType = _messages.EnumField('DatabaseDumpTypeValueValuesEnum', 1)
  destinationGcsUri = _messages.StringField(2)
  endTime = _messages.StringField(3)
  startTime = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class MetadataImport(_messages.Message):
  r"""A metastore resource that imports metadata.

  Enums:
    StateValueValuesEnum: Output only. The current state of the metadata
      import.

  Fields:
    createTime: Output only. The time when the metadata import was started.
    databaseDump: Immutable. A database dump from a pre-existing metastore's
      database.
    description: Optional. The description of the metadata import.
    endTime: Output only. The time when the metadata import finished.
    name: Immutable. Identifier. The relative resource name of the metadata
      import, of the form:projects/{project_number}/locations/{location_id}/se
      rvices/{service_id}/metadataImports/{metadata_import_id}.
    state: Output only. The current state of the metadata import.
    updateTime: Output only. The time when the metadata import was last
      updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the metadata import.

    Values:
      STATE_UNSPECIFIED: The state of the metadata import is unknown.
      RUNNING: The metadata import is running.
      SUCCEEDED: The metadata import completed successfully.
      UPDATING: The metadata import is being updated.
      FAILED: The metadata import failed, and attempted metadata changes were
        rolled back.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    SUCCEEDED = 2
    UPDATING = 3
    FAILED = 4

  createTime = _messages.StringField(1)
  databaseDump = _messages.MessageField('DatabaseDump', 2)
  description = _messages.StringField(3)
  endTime = _messages.StringField(4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  updateTime = _messages.StringField(7)


class MetadataIntegration(_messages.Message):
  r"""Specifies how metastore metadata should be integrated with external
  services.

  Fields:
    dataCatalogConfig: Optional. The integration config for the Data Catalog
      service.
  """

  dataCatalogConfig = _messages.MessageField('DataCatalogConfig', 1)


class MetadataManagementActivity(_messages.Message):
  r"""The metadata management activities of the metastore service.

  Fields:
    metadataExports: Output only. The latest metadata exports of the metastore
      service.
    restores: Output only. The latest restores of the metastore service.
  """

  metadataExports = _messages.MessageField('MetadataExport', 1, repeated=True)
  restores = _messages.MessageField('Restore', 2, repeated=True)


class MetastoreProjectsLocationsFederationsCreateRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsFederationsCreateRequest object.

  Fields:
    federation: A Federation resource to be passed as the request body.
    federationId: Required. The ID of the metastore federation, which is used
      as the final component of the metastore federation's name.This value
      must be between 2 and 63 characters long inclusive, begin with a letter,
      end with a letter or number, and consist of alpha-numeric ASCII
      characters or hyphens.
    parent: Required. The relative resource name of the location in which to
      create a federation service, in the following
      form:projects/{project_number}/locations/{location_id}.
    requestId: Optional. A request ID. Specify a unique request ID to allow
      the server to ignore the request if it has completed. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 60 minutes after the first request.For example, if an initial
      request times out, followed by another request with the same request ID,
      the server ignores the second request to prevent the creation of
      duplicate commitments.The request ID must be a valid UUID
      (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A
      zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  """

  federation = _messages.MessageField('Federation', 1)
  federationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class MetastoreProjectsLocationsFederationsDeleteRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsFederationsDeleteRequest object.

  Fields:
    name: Required. The relative resource name of the metastore federation to
      delete, in the following form:projects/{project_number}/locations/{locat
      ion_id}/federations/{federation_id}.
    requestId: Optional. A request ID. Specify a unique request ID to allow
      the server to ignore the request if it has completed. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 60 minutes after the first request.For example, if an initial
      request times out, followed by another request with the same request ID,
      the server ignores the second request to prevent the creation of
      duplicate commitments.The request ID must be a valid UUID
      (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A
      zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class MetastoreProjectsLocationsFederationsGetIamPolicyRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsFederationsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class MetastoreProjectsLocationsFederationsGetRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsFederationsGetRequest object.

  Fields:
    name: Required. The relative resource name of the metastore federation to
      retrieve, in the following form:projects/{project_number}/locations/{loc
      ation_id}/federations/{federation_id}.
  """

  name = _messages.StringField(1, required=True)


class MetastoreProjectsLocationsFederationsListRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsFederationsListRequest object.

  Fields:
    filter: Optional. The filter to apply to list results.
    orderBy: Optional. Specify the ordering of results as described in Sorting
      Order
      (https://cloud.google.com/apis/design/design_patterns#sorting_order). If
      not specified, the results will be sorted in the default order.
    pageSize: Optional. The maximum number of federations to return. The
      response may contain less than the maximum number. If unspecified, no
      more than 500 services are returned. The maximum value is 1000; values
      above 1000 are changed to 1000.
    pageToken: Optional. A page token, received from a previous
      ListFederationServices call. Provide this token to retrieve the
      subsequent page.To retrieve the first page, supply an empty page
      token.When paginating, other parameters provided to
      ListFederationServices must match the call that provided the page token.
    parent: Required. The relative resource name of the location of metastore
      federations to list, in the following form:
      projects/{project_number}/locations/{location_id}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class MetastoreProjectsLocationsFederationsPatchRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsFederationsPatchRequest object.

  Fields:
    federation: A Federation resource to be passed as the request body.
    name: Immutable. The relative resource name of the federation, of the
      form: projects/{project_number}/locations/{location_id}/federations/{fed
      eration_id}`.
    requestId: Optional. A request ID. Specify a unique request ID to allow
      the server to ignore the request if it has completed. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 60 minutes after the first request.For example, if an initial
      request times out, followed by another request with the same request ID,
      the server ignores the second request to prevent the creation of
      duplicate commitments.The request ID must be a valid UUID
      (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A
      zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
    updateMask: Required. A field mask used to specify the fields to be
      overwritten in the metastore federation resource by the update. Fields
      specified in the update_mask are relative to the resource (not to the
      full request). A field is overwritten if it is in the mask.
  """

  federation = _messages.MessageField('Federation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class MetastoreProjectsLocationsFederationsSetIamPolicyRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsFederationsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class MetastoreProjectsLocationsFederationsTestIamPermissionsRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsFederationsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class MetastoreProjectsLocationsGetRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class MetastoreProjectsLocationsListRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like "displayName=tokyo", and is
      documented in more detail in AIP-160 (https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the next_page_token field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class MetastoreProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class MetastoreProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class MetastoreProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class MetastoreProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class MetastoreProjectsLocationsServicesAlterLocationRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesAlterLocationRequest object.

  Fields:
    alterMetadataResourceLocationRequest: A
      AlterMetadataResourceLocationRequest resource to be passed as the
      request body.
    service: Required. The relative resource name of the metastore service to
      mutate metadata, in the following format:projects/{project_id}/locations
      /{location_id}/services/{service_id}.
  """

  alterMetadataResourceLocationRequest = _messages.MessageField('AlterMetadataResourceLocationRequest', 1)
  service = _messages.StringField(2, required=True)


class MetastoreProjectsLocationsServicesAlterTablePropertiesRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesAlterTablePropertiesRequest object.

  Fields:
    alterTablePropertiesRequest: A AlterTablePropertiesRequest resource to be
      passed as the request body.
    service: Required. The relative resource name of the Dataproc Metastore
      service that's being used to mutate metadata table properties, in the
      following format:projects/{project_id}/locations/{location_id}/services/
      {service_id}.
  """

  alterTablePropertiesRequest = _messages.MessageField('AlterTablePropertiesRequest', 1)
  service = _messages.StringField(2, required=True)


class MetastoreProjectsLocationsServicesBackupsCreateRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesBackupsCreateRequest object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    backupId: Required. The ID of the backup, which is used as the final
      component of the backup's name.This value must be between 1 and 64
      characters long, begin with a letter, end with a letter or number, and
      consist of alpha-numeric ASCII characters or hyphens.
    parent: Required. The relative resource name of the service in which to
      create a backup of the following form:projects/{project_number}/location
      s/{location_id}/services/{service_id}.
    requestId: Optional. A request ID. Specify a unique request ID to allow
      the server to ignore the request if it has completed. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 60 minutes after the first request.For example, if an initial
      request times out, followed by another request with the same request ID,
      the server ignores the second request to prevent the creation of
      duplicate commitments.The request ID must be a valid UUID
      (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A
      zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  """

  backup = _messages.MessageField('Backup', 1)
  backupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class MetastoreProjectsLocationsServicesBackupsDeleteRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesBackupsDeleteRequest object.

  Fields:
    name: Required. The relative resource name of the backup to delete, in the
      following form:projects/{project_number}/locations/{location_id}/service
      s/{service_id}/backups/{backup_id}.
    requestId: Optional. A request ID. Specify a unique request ID to allow
      the server to ignore the request if it has completed. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 60 minutes after the first request.For example, if an initial
      request times out, followed by another request with the same request ID,
      the server ignores the second request to prevent the creation of
      duplicate commitments.The request ID must be a valid UUID
      (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A
      zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class MetastoreProjectsLocationsServicesBackupsGetIamPolicyRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesBackupsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class MetastoreProjectsLocationsServicesBackupsGetRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesBackupsGetRequest object.

  Fields:
    name: Required. The relative resource name of the backup to retrieve, in
      the following form:projects/{project_number}/locations/{location_id}/ser
      vices/{service_id}/backups/{backup_id}.
  """

  name = _messages.StringField(1, required=True)


class MetastoreProjectsLocationsServicesBackupsListRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesBackupsListRequest object.

  Fields:
    filter: Optional. The filter to apply to list results.
    orderBy: Optional. Specify the ordering of results as described in Sorting
      Order
      (https://cloud.google.com/apis/design/design_patterns#sorting_order). If
      not specified, the results will be sorted in the default order.
    pageSize: Optional. The maximum number of backups to return. The response
      may contain less than the maximum number. If unspecified, no more than
      500 backups are returned. The maximum value is 1000; values above 1000
      are changed to 1000.
    pageToken: Optional. A page token, received from a previous
      DataprocMetastore.ListBackups call. Provide this token to retrieve the
      subsequent page.To retrieve the first page, supply an empty page
      token.When paginating, other parameters provided to
      DataprocMetastore.ListBackups must match the call that provided the page
      token.
    parent: Required. The relative resource name of the service whose backups
      to list, in the following form:projects/{project_number}/locations/{loca
      tion_id}/services/{service_id}/backups.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class MetastoreProjectsLocationsServicesBackupsSetIamPolicyRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesBackupsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class MetastoreProjectsLocationsServicesCancelMigrationRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesCancelMigrationRequest object.

  Fields:
    cancelMigrationRequest: A CancelMigrationRequest resource to be passed as
      the request body.
    service: Required. The relative resource name of the metastore service to
      cancel the ongoing migration to, in the following format:projects/{proje
      ct_id}/locations/{location_id}/services/{service_id}.
  """

  cancelMigrationRequest = _messages.MessageField('CancelMigrationRequest', 1)
  service = _messages.StringField(2, required=True)


class MetastoreProjectsLocationsServicesCompleteMigrationRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesCompleteMigrationRequest object.

  Fields:
    completeMigrationRequest: A CompleteMigrationRequest resource to be passed
      as the request body.
    service: Required. The relative resource name of the metastore service to
      complete the migration to, in the following format:projects/{project_id}
      /locations/{location_id}/services/{service_id}.
  """

  completeMigrationRequest = _messages.MessageField('CompleteMigrationRequest', 1)
  service = _messages.StringField(2, required=True)


class MetastoreProjectsLocationsServicesCreateRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesCreateRequest object.

  Fields:
    parent: Required. The relative resource name of the location in which to
      create a metastore service, in the following
      form:projects/{project_number}/locations/{location_id}.
    requestId: Optional. A request ID. Specify a unique request ID to allow
      the server to ignore the request if it has completed. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 60 minutes after the first request.For example, if an initial
      request times out, followed by another request with the same request ID,
      the server ignores the second request to prevent the creation of
      duplicate commitments.The request ID must be a valid UUID
      (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A
      zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
    service: A Service resource to be passed as the request body.
    serviceId: Required. The ID of the metastore service, which is used as the
      final component of the metastore service's name.This value must be
      between 2 and 63 characters long inclusive, begin with a letter, end
      with a letter or number, and consist of alpha-numeric ASCII characters
      or hyphens.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  service = _messages.MessageField('Service', 3)
  serviceId = _messages.StringField(4)


class MetastoreProjectsLocationsServicesDatabasesGetIamPolicyRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesDatabasesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class MetastoreProjectsLocationsServicesDatabasesSetIamPolicyRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesDatabasesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class MetastoreProjectsLocationsServicesDatabasesTablesGetIamPolicyRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesDatabasesTablesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class MetastoreProjectsLocationsServicesDatabasesTablesSetIamPolicyRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesDatabasesTablesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class MetastoreProjectsLocationsServicesDeleteRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesDeleteRequest object.

  Fields:
    name: Required. The relative resource name of the metastore service to
      delete, in the following form:projects/{project_number}/locations/{locat
      ion_id}/services/{service_id}.
    requestId: Optional. A request ID. Specify a unique request ID to allow
      the server to ignore the request if it has completed. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 60 minutes after the first request.For example, if an initial
      request times out, followed by another request with the same request ID,
      the server ignores the second request to prevent the creation of
      duplicate commitments.The request ID must be a valid UUID
      (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A
      zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class MetastoreProjectsLocationsServicesExportMetadataRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesExportMetadataRequest object.

  Fields:
    exportMetadataRequest: A ExportMetadataRequest resource to be passed as
      the request body.
    service: Required. The relative resource name of the metastore service to
      run export, in the following form:projects/{project_id}/locations/{locat
      ion_id}/services/{service_id}.
  """

  exportMetadataRequest = _messages.MessageField('ExportMetadataRequest', 1)
  service = _messages.StringField(2, required=True)


class MetastoreProjectsLocationsServicesGetIamPolicyRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class MetastoreProjectsLocationsServicesGetRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesGetRequest object.

  Fields:
    name: Required. The relative resource name of the metastore service to
      retrieve, in the following form:projects/{project_number}/locations/{loc
      ation_id}/services/{service_id}.
  """

  name = _messages.StringField(1, required=True)


class MetastoreProjectsLocationsServicesListRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesListRequest object.

  Fields:
    filter: Optional. The filter to apply to list results.
    orderBy: Optional. Specify the ordering of results as described in Sorting
      Order
      (https://cloud.google.com/apis/design/design_patterns#sorting_order). If
      not specified, the results will be sorted in the default order.
    pageSize: Optional. The maximum number of services to return. The response
      may contain less than the maximum number. If unspecified, no more than
      500 services are returned. The maximum value is 1000; values above 1000
      are changed to 1000.
    pageToken: Optional. A page token, received from a previous
      DataprocMetastore.ListServices call. Provide this token to retrieve the
      subsequent page.To retrieve the first page, supply an empty page
      token.When paginating, other parameters provided to
      DataprocMetastore.ListServices must match the call that provided the
      page token.
    parent: Required. The relative resource name of the location of metastore
      services to list, in the following
      form:projects/{project_number}/locations/{location_id}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class MetastoreProjectsLocationsServicesMetadataImportsCreateRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesMetadataImportsCreateRequest object.

  Fields:
    metadataImport: A MetadataImport resource to be passed as the request
      body.
    metadataImportId: Required. The ID of the metadata import, which is used
      as the final component of the metadata import's name.This value must be
      between 1 and 64 characters long, begin with a letter, end with a letter
      or number, and consist of alpha-numeric ASCII characters or hyphens.
    parent: Required. The relative resource name of the service in which to
      create a metastore import, in the following form:projects/{project_numbe
      r}/locations/{location_id}/services/{service_id}.
    requestId: Optional. A request ID. Specify a unique request ID to allow
      the server to ignore the request if it has completed. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 60 minutes after the first request.For example, if an initial
      request times out, followed by another request with the same request ID,
      the server ignores the second request to prevent the creation of
      duplicate commitments.The request ID must be a valid UUID
      (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A
      zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  """

  metadataImport = _messages.MessageField('MetadataImport', 1)
  metadataImportId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class MetastoreProjectsLocationsServicesMetadataImportsGetRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesMetadataImportsGetRequest object.

  Fields:
    name: Required. The relative resource name of the metadata import to
      retrieve, in the following form:projects/{project_number}/locations/{loc
      ation_id}/services/{service_id}/metadataImports/{import_id}.
  """

  name = _messages.StringField(1, required=True)


class MetastoreProjectsLocationsServicesMetadataImportsListRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesMetadataImportsListRequest object.

  Fields:
    filter: Optional. The filter to apply to list results.
    orderBy: Optional. Specify the ordering of results as described in Sorting
      Order
      (https://cloud.google.com/apis/design/design_patterns#sorting_order). If
      not specified, the results will be sorted in the default order.
    pageSize: Optional. The maximum number of imports to return. The response
      may contain less than the maximum number. If unspecified, no more than
      500 imports are returned. The maximum value is 1000; values above 1000
      are changed to 1000.
    pageToken: Optional. A page token, received from a previous
      DataprocMetastore.ListServices call. Provide this token to retrieve the
      subsequent page.To retrieve the first page, supply an empty page
      token.When paginating, other parameters provided to
      DataprocMetastore.ListServices must match the call that provided the
      page token.
    parent: Required. The relative resource name of the service whose metadata
      imports to list, in the following form:projects/{project_number}/locatio
      ns/{location_id}/services/{service_id}/metadataImports.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class MetastoreProjectsLocationsServicesMetadataImportsPatchRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesMetadataImportsPatchRequest object.

  Fields:
    metadataImport: A MetadataImport resource to be passed as the request
      body.
    name: Immutable. Identifier. The relative resource name of the metadata
      import, of the form:projects/{project_number}/locations/{location_id}/se
      rvices/{service_id}/metadataImports/{metadata_import_id}.
    requestId: Optional. A request ID. Specify a unique request ID to allow
      the server to ignore the request if it has completed. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 60 minutes after the first request.For example, if an initial
      request times out, followed by another request with the same request ID,
      the server ignores the second request to prevent the creation of
      duplicate commitments.The request ID must be a valid UUID
      (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A
      zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
    updateMask: Required. A field mask used to specify the fields to be
      overwritten in the metadata import resource by the update. Fields
      specified in the update_mask are relative to the resource (not to the
      full request). A field is overwritten if it is in the mask.
  """

  metadataImport = _messages.MessageField('MetadataImport', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class MetastoreProjectsLocationsServicesMigrationExecutionsDeleteRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesMigrationExecutionsDeleteRequest
  object.

  Fields:
    name: Required. The relative resource name of the migrationExecution to
      delete, in the following form:projects/{project_number}/locations/{locat
      ion_id}/services/{service_id}/migrationExecutions/{migration_execution_i
      d}.
    requestId: Optional. A request ID. Specify a unique request ID to allow
      the server to ignore the request if it has completed. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 60 minutes after the first request.For example, if an initial
      request times out, followed by another request with the same request ID,
      the server ignores the second request to prevent the creation of
      duplicate commitments.The request ID must be a valid UUID
      (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A
      zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class MetastoreProjectsLocationsServicesMigrationExecutionsGetRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesMigrationExecutionsGetRequest
  object.

  Fields:
    name: Required. The relative resource name of the migration execution to
      retrieve, in the following form:projects/{project_number}/locations/{loc
      ation_id}/services/{service_id}/migrationExecutions/{migration_execution
      _id}.
  """

  name = _messages.StringField(1, required=True)


class MetastoreProjectsLocationsServicesMigrationExecutionsListRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesMigrationExecutionsListRequest
  object.

  Fields:
    filter: Optional. The filter to apply to list results.
    orderBy: Optional. Specify the ordering of results as described in Sorting
      Order
      (https://cloud.google.com/apis/design/design_patterns#sorting_order). If
      not specified, the results will be sorted in the default order.
    pageSize: Optional. The maximum number of migration executions to return.
      The response may contain less than the maximum number. If unspecified,
      no more than 500 migration executions are returned. The maximum value is
      1000; values above 1000 are changed to 1000.
    pageToken: Optional. A page token, received from a previous
      DataprocMetastore.ListMigrationExecutions call. Provide this token to
      retrieve the subsequent page.To retrieve the first page, supply an empty
      page token.When paginating, other parameters provided to
      DataprocMetastore.ListMigrationExecutions must match the call that
      provided the page token.
    parent: Required. The relative resource name of the service whose
      migration executions to list, in the following form:projects/{project_nu
      mber}/locations/{location_id}/services/{service_id}/migrationExecutions.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class MetastoreProjectsLocationsServicesMoveTableToDatabaseRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesMoveTableToDatabaseRequest object.

  Fields:
    moveTableToDatabaseRequest: A MoveTableToDatabaseRequest resource to be
      passed as the request body.
    service: Required. The relative resource name of the metastore service to
      mutate metadata, in the following format:projects/{project_id}/locations
      /{location_id}/services/{service_id}.
  """

  moveTableToDatabaseRequest = _messages.MessageField('MoveTableToDatabaseRequest', 1)
  service = _messages.StringField(2, required=True)


class MetastoreProjectsLocationsServicesPatchRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesPatchRequest object.

  Fields:
    name: Immutable. Identifier. The relative resource name of the metastore
      service, in the following format:projects/{project_number}/locations/{lo
      cation_id}/services/{service_id}.
    requestId: Optional. A request ID. Specify a unique request ID to allow
      the server to ignore the request if it has completed. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 60 minutes after the first request.For example, if an initial
      request times out, followed by another request with the same request ID,
      the server ignores the second request to prevent the creation of
      duplicate commitments.The request ID must be a valid UUID
      (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A
      zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
    service: A Service resource to be passed as the request body.
    updateMask: Required. A field mask used to specify the fields to be
      overwritten in the metastore service resource by the update. Fields
      specified in the update_mask are relative to the resource (not to the
      full request). A field is overwritten if it is in the mask.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  service = _messages.MessageField('Service', 3)
  updateMask = _messages.StringField(4)


class MetastoreProjectsLocationsServicesQueryMetadataRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesQueryMetadataRequest object.

  Fields:
    queryMetadataRequest: A QueryMetadataRequest resource to be passed as the
      request body.
    service: Required. The relative resource name of the metastore service to
      query metadata, in the following format:projects/{project_id}/locations/
      {location_id}/services/{service_id}.
  """

  queryMetadataRequest = _messages.MessageField('QueryMetadataRequest', 1)
  service = _messages.StringField(2, required=True)


class MetastoreProjectsLocationsServicesRestoreRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesRestoreRequest object.

  Fields:
    restoreServiceRequest: A RestoreServiceRequest resource to be passed as
      the request body.
    service: Required. The relative resource name of the metastore service to
      run restore, in the following form:projects/{project_id}/locations/{loca
      tion_id}/services/{service_id}.
  """

  restoreServiceRequest = _messages.MessageField('RestoreServiceRequest', 1)
  service = _messages.StringField(2, required=True)


class MetastoreProjectsLocationsServicesSetIamPolicyRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class MetastoreProjectsLocationsServicesStartMigrationRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesStartMigrationRequest object.

  Fields:
    service: Required. The relative resource name of the metastore service to
      start migrating to, in the following format:projects/{project_id}/locati
      ons/{location_id}/services/{service_id}.
    startMigrationRequest: A StartMigrationRequest resource to be passed as
      the request body.
  """

  service = _messages.StringField(1, required=True)
  startMigrationRequest = _messages.MessageField('StartMigrationRequest', 2)


class MetastoreProjectsLocationsServicesTestIamPermissionsRequest(_messages.Message):
  r"""A MetastoreProjectsLocationsServicesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class MigrationExecution(_messages.Message):
  r"""The details of a migration execution resource.

  Enums:
    PhaseValueValuesEnum: Output only. The current phase of the migration
      execution.
    StateValueValuesEnum: Output only. The current state of the migration
      execution.

  Fields:
    cloudSqlMigrationConfig: Configuration information specific to migrating
      from self-managed hive metastore on Google Cloud using Cloud SQL as the
      backend database to Dataproc Metastore.
    createTime: Output only. The time when the migration execution was
      started.
    endTime: Output only. The time when the migration execution finished.
    name: Output only. The relative resource name of the migration execution,
      in the following form: projects/{project_number}/locations/{location_id}
      /services/{service_id}/migrationExecutions/{migration_execution_id}
    phase: Output only. The current phase of the migration execution.
    state: Output only. The current state of the migration execution.
    stateMessage: Output only. Additional information about the current state
      of the migration execution.
  """

  class PhaseValueValuesEnum(_messages.Enum):
    r"""Output only. The current phase of the migration execution.

    Values:
      PHASE_UNSPECIFIED: The phase of the migration execution is unknown.
      REPLICATION: Replication phase refers to the migration phase when
        Dataproc Metastore is running a pipeline to replicate changes in the
        customer database to its backend database. During this phase, Dataproc
        Metastore uses the customer database as the hive metastore backend
        database.
      CUTOVER: Cutover phase refers to the migration phase when Dataproc
        Metastore switches to using its own backend database. Migration enters
        this phase when customer is done migrating all their
        clusters/workloads to Dataproc Metastore and triggers
        CompleteMigration.
    """
    PHASE_UNSPECIFIED = 0
    REPLICATION = 1
    CUTOVER = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the migration execution.

    Values:
      STATE_UNSPECIFIED: The state of the migration execution is unknown.
      STARTING: The migration execution is starting.
      RUNNING: The migration execution is running.
      CANCELLING: The migration execution is in the process of being
        cancelled.
      AWAITING_USER_ACTION: The migration execution is awaiting user action.
      SUCCEEDED: The migration execution has completed successfully.
      FAILED: The migration execution has failed.
      CANCELLED: The migration execution is cancelled.
      DELETING: The migration execution is being deleted.
    """
    STATE_UNSPECIFIED = 0
    STARTING = 1
    RUNNING = 2
    CANCELLING = 3
    AWAITING_USER_ACTION = 4
    SUCCEEDED = 5
    FAILED = 6
    CANCELLED = 7
    DELETING = 8

  cloudSqlMigrationConfig = _messages.MessageField('CloudSQLMigrationConfig', 1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  name = _messages.StringField(4)
  phase = _messages.EnumField('PhaseValueValuesEnum', 5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  stateMessage = _messages.StringField(7)


class MoveTableToDatabaseRequest(_messages.Message):
  r"""Request message for DataprocMetastore.MoveTableToDatabase.

  Fields:
    dbName: Required. The name of the database where the table resides.
    destinationDbName: Required. The name of the database where the table
      should be moved.
    tableName: Required. The name of the table to be moved.
  """

  dbName = _messages.StringField(1)
  destinationDbName = _messages.StringField(2)
  tableName = _messages.StringField(3)


class MoveTableToDatabaseResponse(_messages.Message):
  r"""Response message for DataprocMetastore.MoveTableToDatabase."""


class MultiRegionMetadata(_messages.Message):
  r"""The metadata for the multi-region that includes the constituent regions.
  The metadata is only populated if the region is multi-region. For single
  region or custom dual region, it will be empty.

  Fields:
    constituentRegions: The regions constituting the multi-region.
    continent: The continent for this multi-region.
    witnessRegion: The Spanner witness region for this multi-region.
  """

  constituentRegions = _messages.StringField(1, repeated=True)
  continent = _messages.StringField(2)
  witnessRegion = _messages.StringField(3)


class NetworkConfig(_messages.Message):
  r"""Network configuration for the Dataproc Metastore service.

  Fields:
    consumers: Immutable. The consumer-side network configuration for the
      Dataproc Metastore instance.
  """

  consumers = _messages.MessageField('Consumer', 1, repeated=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as Delete, the response
      is google.protobuf.Empty. If the original method is standard
      Get/Create/Update, the response should be the resource. For other
      methods, the response should have the type XxxResponse, where Xxx is the
      original method name. For example, if the original method name is
      TakeSnapshot(), the inferred response type is TakeSnapshotResponse.

  Fields:
    done: If the value is false, it means the operation is still in progress.
      If true, the operation is completed, and either error or response is
      available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the name should be a resource name ending with operations/{unique_id}.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as Delete, the response
      is google.protobuf.Empty. If the original method is standard
      Get/Create/Update, the response should be the resource. For other
      methods, the response should have the type XxxResponse, where Xxx is the
      original method name. For example, if the original method name is
      TakeSnapshot(), the inferred response type is TakeSnapshotResponse.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as Delete, the response is
    google.protobuf.Empty. If the original method is standard
    Get/Create/Update, the response should be the resource. For other methods,
    the response should have the type XxxResponse, where Xxx is the original
    method name. For example, if the original method name is TakeSnapshot(),
    the inferred response type is TakeSnapshotResponse.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of a long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the caller has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have google.longrunning.Operation.error
      value with a google.rpc.Status.code of 1, corresponding to
      Code.CANCELLED.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources.A Policy is a collection of bindings. A
  binding binds one or more members, or principals, to a single role.
  Principals can be user accounts, service accounts, Google groups, and
  domains (such as G Suite). A role is a named list of permissions; each role
  can be an IAM predefined role or a user-created custom role.For some types
  of Google Cloud resources, a binding can also specify a condition, which is
  a logical expression that allows access to a resource only if the expression
  evaluates to true. A condition can add constraints based on attributes of
  the request, the resource, or both. To learn which resources support
  conditions in their IAM policies, see the IAM documentation
  (https://cloud.google.com/iam/help/conditions/resource-policies).JSON
  example: { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } YAML example: bindings: - members: -
  user:<EMAIL> - group:<EMAIL> - domain:google.com -
  serviceAccount:<EMAIL> role:
  roles/resourcemanager.organizationAdmin - members: - user:<EMAIL>
  role: roles/resourcemanager.organizationViewer condition: title: expirable
  access description: Does not grant access after Sep 2020 expression:
  request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA=
  version: 3 For a description of IAM and its features, see the IAM
  documentation (https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of members, or principals, with a role.
      Optionally, may specify a condition that determines how and when the
      bindings are applied. Each of the bindings must contain at least one
      principal.The bindings in a Policy can refer to up to 1,500 principals;
      up to 250 of these principals can be Google groups. Each occurrence of a
      principal counts towards these limits. For example, if the bindings
      grant 50 different roles to user:<EMAIL>, and not to any other
      principal, then you can add another 1,450 principals to the bindings in
      the Policy.
    etag: etag is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the etag in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An etag is returned in the response to getIamPolicy, and
      systems are expected to put that etag in the request to setIamPolicy to
      ensure that their change will be applied to the same version of the
      policy.Important: If you use IAM Conditions, you must include the etag
      field whenever you call setIamPolicy. If you omit this field, then IAM
      allows you to overwrite a version 3 policy with a version 1 policy, and
      all of the conditions in the version 3 policy are lost.
    version: Specifies the format of the policy.Valid values are 0, 1, and 3.
      Requests that specify an invalid value are rejected.Any operation that
      affects conditional role bindings must specify version 3. This
      requirement applies to the following operations: Getting a policy that
      includes a conditional role binding Adding a conditional role binding to
      a policy Changing a conditional role binding in a policy Removing any
      role binding, with or without a condition, from a policy that includes
      conditionsImportant: If you use IAM Conditions, you must include the
      etag field whenever you call setIamPolicy. If you omit this field, then
      IAM allows you to overwrite a version 3 policy with a version 1 policy,
      and all of the conditions in the version 3 policy are lost.If a policy
      does not include any conditions, operations on that policy may specify
      any valid version or leave the field unset.To learn which resources
      support conditions in their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class QueryMetadataRequest(_messages.Message):
  r"""Request message for DataprocMetastore.QueryMetadata.

  Fields:
    query: Required. A read-only SQL query to execute against the metadata
      database. The query cannot change or mutate the data.
  """

  query = _messages.StringField(1)


class QueryMetadataResponse(_messages.Message):
  r"""Response message for DataprocMetastore.QueryMetadata.

  Fields:
    resultManifestUri: The manifest URI is link to a JSON instance in Cloud
      Storage. This instance manifests immediately along with
      QueryMetadataResponse. The content of the URI is not retriable until the
      long-running operation query against the metadata finishes.
  """

  resultManifestUri = _messages.StringField(1)


class Restore(_messages.Message):
  r"""The details of a metadata restore operation.

  Enums:
    StateValueValuesEnum: Output only. The current state of the restore.
    TypeValueValuesEnum: Output only. The type of restore.

  Fields:
    backup: Output only. The relative resource name of the metastore service
      backup to restore from, in the following form:projects/{project_id}/loca
      tions/{location_id}/services/{service_id}/backups/{backup_id}.
    backupLocation: Optional. A Cloud Storage URI specifying where the backup
      artifacts are stored, in the format gs:///.
    details: Output only. The restore details containing the revision of the
      service to be restored to, in format of JSON.
    endTime: Output only. The time when the restore ended.
    startTime: Output only. The time when the restore started.
    state: Output only. The current state of the restore.
    type: Output only. The type of restore.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the restore.

    Values:
      STATE_UNSPECIFIED: The state of the metadata restore is unknown.
      RUNNING: The metadata restore is running.
      SUCCEEDED: The metadata restore completed successfully.
      FAILED: The metadata restore failed.
      CANCELLED: The metadata restore is cancelled.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    SUCCEEDED = 2
    FAILED = 3
    CANCELLED = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of restore.

    Values:
      RESTORE_TYPE_UNSPECIFIED: The restore type is unknown.
      FULL: The service's metadata and configuration are restored.
      METADATA_ONLY: Only the service's metadata is restored.
    """
    RESTORE_TYPE_UNSPECIFIED = 0
    FULL = 1
    METADATA_ONLY = 2

  backup = _messages.StringField(1)
  backupLocation = _messages.StringField(2)
  details = _messages.StringField(3)
  endTime = _messages.StringField(4)
  startTime = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  type = _messages.EnumField('TypeValueValuesEnum', 7)


class RestoreServiceRequest(_messages.Message):
  r"""Request message for DataprocMetastore.RestoreService.

  Enums:
    RestoreTypeValueValuesEnum: Optional. The type of restore. If unspecified,
      defaults to METADATA_ONLY.

  Fields:
    backup: Optional. The relative resource name of the metastore service
      backup to restore from, in the following form:projects/{project_id}/loca
      tions/{location_id}/services/{service_id}/backups/{backup_id}. Mutually
      exclusive with backup_location, and exactly one of the two must be set.
    backupLocation: Optional. A Cloud Storage URI specifying the location of
      the backup artifacts, namely - backup avro files under "avro/",
      backup_metastore.json and service.json, in the following form:gs://.
      Mutually exclusive with backup, and exactly one of the two must be set.
    requestId: Optional. A request ID. Specify a unique request ID to allow
      the server to ignore the request if it has completed. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 60 minutes after the first request.For example, if an initial
      request times out, followed by another request with the same request ID,
      the server ignores the second request to prevent the creation of
      duplicate commitments.The request ID must be a valid UUID
      (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format). A
      zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
    restoreType: Optional. The type of restore. If unspecified, defaults to
      METADATA_ONLY.
  """

  class RestoreTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of restore. If unspecified, defaults to
    METADATA_ONLY.

    Values:
      RESTORE_TYPE_UNSPECIFIED: The restore type is unknown.
      FULL: The service's metadata and configuration are restored.
      METADATA_ONLY: Only the service's metadata is restored.
    """
    RESTORE_TYPE_UNSPECIFIED = 0
    FULL = 1
    METADATA_ONLY = 2

  backup = _messages.StringField(1)
  backupLocation = _messages.StringField(2)
  requestId = _messages.StringField(3)
  restoreType = _messages.EnumField('RestoreTypeValueValuesEnum', 4)


class ScalingConfig(_messages.Message):
  r"""Represents the scaling configuration of a metastore service.

  Enums:
    InstanceSizeValueValuesEnum: An enum of readable instance sizes, with each
      instance size mapping to a float value (e.g. InstanceSize.EXTRA_SMALL =
      scaling_factor(0.1))

  Fields:
    autoscalingConfig: Optional. The autoscaling configuration.
    instanceSize: An enum of readable instance sizes, with each instance size
      mapping to a float value (e.g. InstanceSize.EXTRA_SMALL =
      scaling_factor(0.1))
    scalingFactor: Scaling factor, increments of 0.1 for values less than 1.0,
      and increments of 1.0 for values greater than 1.0.
  """

  class InstanceSizeValueValuesEnum(_messages.Enum):
    r"""An enum of readable instance sizes, with each instance size mapping to
    a float value (e.g. InstanceSize.EXTRA_SMALL = scaling_factor(0.1))

    Values:
      INSTANCE_SIZE_UNSPECIFIED: Unspecified instance size
      EXTRA_SMALL: Extra small instance size, maps to a scaling factor of 0.1.
      SMALL: Small instance size, maps to a scaling factor of 0.5.
      MEDIUM: Medium instance size, maps to a scaling factor of 1.0.
      LARGE: Large instance size, maps to a scaling factor of 3.0.
      EXTRA_LARGE: Extra large instance size, maps to a scaling factor of 6.0.
    """
    INSTANCE_SIZE_UNSPECIFIED = 0
    EXTRA_SMALL = 1
    SMALL = 2
    MEDIUM = 3
    LARGE = 4
    EXTRA_LARGE = 5

  autoscalingConfig = _messages.MessageField('AutoscalingConfig', 1)
  instanceSize = _messages.EnumField('InstanceSizeValueValuesEnum', 2)
  scalingFactor = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class ScheduledBackup(_messages.Message):
  r"""This specifies the configuration of scheduled backup.

  Fields:
    backupLocation: Optional. A Cloud Storage URI of a folder, in the format
      gs:///. A sub-folder containing backup files will be stored below it.
    cronSchedule: Optional. The scheduled interval in Cron format, see
      https://en.wikipedia.org/wiki/Cron The default is empty: scheduled
      backup is not enabled. Must be specified to enable scheduled backups.
    enabled: Optional. Defines whether the scheduled backup is enabled. The
      default value is false.
    latestBackup: Output only. The details of the latest scheduled backup.
    nextScheduledTime: Output only. The time when the next backups execution
      is scheduled to start.
    timeZone: Optional. Specifies the time zone to be used when interpreting
      cron_schedule. Must be a time zone name from the time zone database
      (https://en.wikipedia.org/wiki/List_of_tz_database_time_zones), e.g.
      America/Los_Angeles or Africa/Abidjan. If left unspecified, the default
      is UTC.
  """

  backupLocation = _messages.StringField(1)
  cronSchedule = _messages.StringField(2)
  enabled = _messages.BooleanField(3)
  latestBackup = _messages.MessageField('LatestBackup', 4)
  nextScheduledTime = _messages.StringField(5)
  timeZone = _messages.StringField(6)


class Secret(_messages.Message):
  r"""A securely stored value.

  Fields:
    cloudSecret: Optional. The relative resource name of a Secret Manager
      secret version, in the following form:projects/{project_number}/secrets/
      {secret_id}/versions/{version_id}.
  """

  cloudSecret = _messages.StringField(1)


class Service(_messages.Message):
  r"""A managed metastore service that serves metadata queries.

  Enums:
    DatabaseTypeValueValuesEnum: Immutable. The database type that the
      Metastore service stores its data.
    ReleaseChannelValueValuesEnum: Immutable. The release channel of the
      service. If unspecified, defaults to STABLE.
    StateValueValuesEnum: Output only. The current state of the metastore
      service.
    TierValueValuesEnum: Optional. The tier of the service.

  Messages:
    LabelsValue: User-defined labels for the metastore service.
    TagsValue: Optional. Input only. Immutable. Tag keys/values directly bound
      to this resource. For example: "123/environment": "production",
      "123/costCenter": "marketing"

  Fields:
    artifactGcsUri: Output only. A Cloud Storage URI (starting with gs://)
      that specifies where artifacts related to the metastore service are
      stored.
    bigqueryMetastoreMigrationConfig: Optional. Specifies the configuration
      required for migrating to BigQuery Metastore service.
    createTime: Output only. The time when the metastore service was created.
    databaseType: Immutable. The database type that the Metastore service
      stores its data.
    deletionProtection: Optional. Indicates if the dataproc metastore should
      be protected against accidental deletions.
    encryptionConfig: Immutable. Information used to configure the Dataproc
      Metastore service to encrypt customer data at rest. Cannot be updated.
    endpointUri: Output only. The URI of the endpoint used to access the
      metastore service.
    hiveMetastoreConfig: Configuration information specific to running Hive
      metastore software as the metastore service.
    labels: User-defined labels for the metastore service.
    maintenanceWindow: Optional. The one hour maintenance window of the
      metastore service. This specifies when the service can be restarted for
      maintenance purposes in UTC time. Maintenance window is not needed for
      services with the SPANNER database type.
    metadataIntegration: Optional. The setting that defines how metastore
      metadata should be integrated with external services and systems.
    metadataManagementActivity: Output only. The metadata management
      activities of the metastore service.
    name: Immutable. Identifier. The relative resource name of the metastore
      service, in the following format:projects/{project_number}/locations/{lo
      cation_id}/services/{service_id}.
    network: Immutable. The relative resource name of the VPC network on which
      the instance can be accessed. It is specified in the following
      form:projects/{project_number}/global/networks/{network_id}.
    networkConfig: Optional. The configuration specifying the network settings
      for the Dataproc Metastore service.
    port: Optional. The TCP port at which the metastore service is reached.
      Default: 9083.
    releaseChannel: Immutable. The release channel of the service. If
      unspecified, defaults to STABLE.
    scalingConfig: Optional. Scaling configuration of the metastore service.
    scheduledBackup: Optional. The configuration of scheduled backup for the
      metastore service.
    state: Output only. The current state of the metastore service.
    stateMessage: Output only. Additional information about the current state
      of the metastore service, if available.
    tags: Optional. Input only. Immutable. Tag keys/values directly bound to
      this resource. For example: "123/environment": "production",
      "123/costCenter": "marketing"
    telemetryConfig: Optional. The configuration specifying telemetry settings
      for the Dataproc Metastore service. If unspecified defaults to JSON.
    tier: Optional. The tier of the service.
    uid: Output only. The globally unique resource identifier of the metastore
      service.
    updateTime: Output only. The time when the metastore service was last
      updated.
  """

  class DatabaseTypeValueValuesEnum(_messages.Enum):
    r"""Immutable. The database type that the Metastore service stores its
    data.

    Values:
      DATABASE_TYPE_UNSPECIFIED: The DATABASE_TYPE is not set.
      MYSQL: MySQL is used to persist the metastore data.
      SPANNER: Spanner is used to persist the metastore data.
    """
    DATABASE_TYPE_UNSPECIFIED = 0
    MYSQL = 1
    SPANNER = 2

  class ReleaseChannelValueValuesEnum(_messages.Enum):
    r"""Immutable. The release channel of the service. If unspecified,
    defaults to STABLE.

    Values:
      RELEASE_CHANNEL_UNSPECIFIED: Release channel is not specified.
      CANARY: The CANARY release channel contains the newest features, which
        may be unstable and subject to unresolved issues with no known
        workarounds. Services using the CANARY release channel are not subject
        to any SLAs.
      STABLE: The STABLE release channel contains features that are considered
        stable and have been validated for production use.
    """
    RELEASE_CHANNEL_UNSPECIFIED = 0
    CANARY = 1
    STABLE = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the metastore service.

    Values:
      STATE_UNSPECIFIED: The state of the metastore service is unknown.
      CREATING: The metastore service is in the process of being created.
      ACTIVE: The metastore service is running and ready to serve queries.
      SUSPENDING: The metastore service is entering suspension. Its query-
        serving availability may cease unexpectedly.
      SUSPENDED: The metastore service is suspended and unable to serve
        queries.
      UPDATING: The metastore service is being updated. It remains usable but
        cannot accept additional update requests or be deleted at this time.
      DELETING: The metastore service is undergoing deletion. It cannot be
        used.
      ERROR: The metastore service has encountered an error and cannot be
        used. The metastore service should be deleted.
      AUTOSCALING: The Dataproc Metastore service 2 is being scaled up or
        down.
      MIGRATING: The metastore service is processing a managed migration.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    SUSPENDING = 3
    SUSPENDED = 4
    UPDATING = 5
    DELETING = 6
    ERROR = 7
    AUTOSCALING = 8
    MIGRATING = 9

  class TierValueValuesEnum(_messages.Enum):
    r"""Optional. The tier of the service.

    Values:
      TIER_UNSPECIFIED: The tier is not set.
      DEVELOPER: The developer tier provides limited scalability and no fault
        tolerance. Good for low-cost proof-of-concept.
      ENTERPRISE: The enterprise tier provides multi-zone high availability,
        and sufficient scalability for enterprise-level Dataproc Metastore
        workloads.
    """
    TIER_UNSPECIFIED = 0
    DEVELOPER = 1
    ENTERPRISE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-defined labels for the metastore service.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Input only. Immutable. Tag keys/values directly bound to
    this resource. For example: "123/environment": "production",
    "123/costCenter": "marketing"

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  artifactGcsUri = _messages.StringField(1)
  bigqueryMetastoreMigrationConfig = _messages.MessageField('BigQueryMetastoreMigrationConfig', 2)
  createTime = _messages.StringField(3)
  databaseType = _messages.EnumField('DatabaseTypeValueValuesEnum', 4)
  deletionProtection = _messages.BooleanField(5)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 6)
  endpointUri = _messages.StringField(7)
  hiveMetastoreConfig = _messages.MessageField('HiveMetastoreConfig', 8)
  labels = _messages.MessageField('LabelsValue', 9)
  maintenanceWindow = _messages.MessageField('MaintenanceWindow', 10)
  metadataIntegration = _messages.MessageField('MetadataIntegration', 11)
  metadataManagementActivity = _messages.MessageField('MetadataManagementActivity', 12)
  name = _messages.StringField(13)
  network = _messages.StringField(14)
  networkConfig = _messages.MessageField('NetworkConfig', 15)
  port = _messages.IntegerField(16, variant=_messages.Variant.INT32)
  releaseChannel = _messages.EnumField('ReleaseChannelValueValuesEnum', 17)
  scalingConfig = _messages.MessageField('ScalingConfig', 18)
  scheduledBackup = _messages.MessageField('ScheduledBackup', 19)
  state = _messages.EnumField('StateValueValuesEnum', 20)
  stateMessage = _messages.StringField(21)
  tags = _messages.MessageField('TagsValue', 22)
  telemetryConfig = _messages.MessageField('TelemetryConfig', 23)
  tier = _messages.EnumField('TierValueValuesEnum', 24)
  uid = _messages.StringField(25)
  updateTime = _messages.StringField(26)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for SetIamPolicy method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the resource. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used:paths: "bindings, etag"
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartMigrationRequest(_messages.Message):
  r"""Request message for DataprocMetastore.StartMigration.

  Fields:
    migrationExecution: Required. The configuration details for the migration.
    requestId: Optional. A request ID. Specify a unique request ID to allow
      the server to ignore the request if it has completed. The server will
      ignore subsequent requests that provide a duplicate request ID for at
      least 60 minutes after the first request.For example, if an initial
      request times out, followed by another request with the same request ID,
      the server ignores the second request to prevent the creation of
      duplicate commitments.The request ID must be a valid UUID
      (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A
      zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  """

  migrationExecution = _messages.MessageField('MigrationExecution', 1)
  requestId = _messages.StringField(2)


class Status(_messages.Message):
  r"""The Status type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by gRPC (https://github.com/grpc). Each Status message contains three
  pieces of data: error code, error message, and error details.You can find
  out more about this error model and how to work with it in the API Design
  Guide (https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StatusProto(_messages.Message):
  r"""Wire-format for a Status object

  Fields:
    canonicalCode: copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional int32 canonical_code = 6;
    code: Numeric code drawn from the space specified below. Often, this is
      the canonical error space, and code is drawn from
      google3/util/task/codes.proto copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional int32 code = 1;
    message: Detail message copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional string message = 3;
    messageSet: message_set associates an arbitrary proto message with the
      status. copybara:strip_begin(b/383363683) copybara:strip_end_and_replace
      optional proto2.bridge.MessageSet message_set = 5;
    space: copybara:strip_begin(b/383363683) Space to which this status
      belongs copybara:strip_end_and_replace optional string space = 2; //
      Space to which this status belongs
  """

  canonicalCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  code = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  message = _messages.StringField(3)
  messageSet = _messages.MessageField('MessageSet', 4)
  space = _messages.StringField(5)


class TelemetryConfig(_messages.Message):
  r"""Telemetry Configuration for the Dataproc Metastore service.

  Enums:
    LogFormatValueValuesEnum: Optional. The output format of the Dataproc
      Metastore service's logs.

  Fields:
    logFormat: Optional. The output format of the Dataproc Metastore service's
      logs.
  """

  class LogFormatValueValuesEnum(_messages.Enum):
    r"""Optional. The output format of the Dataproc Metastore service's logs.

    Values:
      LOG_FORMAT_UNSPECIFIED: The LOG_FORMAT is not set.
      LEGACY: Logging output uses the legacy textPayload format.
      JSON: Logging output uses the jsonPayload format.
    """
    LOG_FORMAT_UNSPECIFIED = 0
    LEGACY = 1
    JSON = 2

  logFormat = _messages.EnumField('LogFormatValueValuesEnum', 1)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for TestIamPermissions method.

  Fields:
    permissions: The set of permissions to check for the resource. Permissions
      with wildcards (such as * or storage.*) are not allowed. For more
      information see IAM Overview
      (https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for TestIamPermissions method.

  Fields:
    permissions: A subset of TestPermissionsRequest.permissions that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    MetastoreProjectsLocationsFederationsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    MetastoreProjectsLocationsServicesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    MetastoreProjectsLocationsServicesBackupsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    MetastoreProjectsLocationsServicesDatabasesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    MetastoreProjectsLocationsServicesDatabasesTablesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
