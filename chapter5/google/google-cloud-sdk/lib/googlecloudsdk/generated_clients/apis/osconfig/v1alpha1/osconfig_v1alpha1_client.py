"""Generated client library for osconfig version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.osconfig.v1alpha1 import osconfig_v1alpha1_messages as messages


class OsconfigV1alpha1(base_api.BaseApiClient):
  """Generated client library for service osconfig version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://osconfig.googleapis.com/'
  MTLS_BASE_URL = ''

  _PACKAGE = 'osconfig'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/compute']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'OsconfigV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new osconfig handle."""
    url = url or self.BASE_URL
    super(OsconfigV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.folders_assignments = self.FoldersAssignmentsService(self)
    self.folders_osConfigs = self.FoldersOsConfigsService(self)
    self.folders = self.FoldersService(self)
    self.organizations_assignments = self.OrganizationsAssignmentsService(self)
    self.organizations_osConfigs = self.OrganizationsOsConfigsService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_assignments = self.ProjectsAssignmentsService(self)
    self.projects_osConfigs = self.ProjectsOsConfigsService(self)
    self.projects_patchJobs_instanceDetails = self.ProjectsPatchJobsInstanceDetailsService(self)
    self.projects_patchJobs = self.ProjectsPatchJobsService(self)
    self.projects_zones_instances = self.ProjectsZonesInstancesService(self)
    self.projects_zones = self.ProjectsZonesService(self)
    self.projects = self.ProjectsService(self)

  class FoldersAssignmentsService(base_api.BaseApiService):
    """Service class for the folders_assignments resource."""

    _NAME = 'folders_assignments'

    def __init__(self, client):
      super(OsconfigV1alpha1.FoldersAssignmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an OS Config Assignment.

      Args:
        request: (OsconfigFoldersAssignmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Assignment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/folders/{foldersId}/assignments',
        http_method='POST',
        method_id='osconfig.folders.assignments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/assignments',
        request_field='assignment',
        request_type_name='OsconfigFoldersAssignmentsCreateRequest',
        response_type_name='Assignment',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an OS Config Assignment.

      Args:
        request: (OsconfigFoldersAssignmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/folders/{foldersId}/assignments/{assignmentsId}',
        http_method='DELETE',
        method_id='osconfig.folders.assignments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='OsconfigFoldersAssignmentsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get an OS Config Assignment.

      Args:
        request: (OsconfigFoldersAssignmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Assignment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/folders/{foldersId}/assignments/{assignmentsId}',
        http_method='GET',
        method_id='osconfig.folders.assignments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='OsconfigFoldersAssignmentsGetRequest',
        response_type_name='Assignment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Get a page of OS Config Assignments.

      Args:
        request: (OsconfigFoldersAssignmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAssignmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/folders/{foldersId}/assignments',
        http_method='GET',
        method_id='osconfig.folders.assignments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/assignments',
        request_field='',
        request_type_name='OsconfigFoldersAssignmentsListRequest',
        response_type_name='ListAssignmentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update an OS Config Assignment.

      Args:
        request: (OsconfigFoldersAssignmentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Assignment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/folders/{foldersId}/assignments/{assignmentsId}',
        http_method='PATCH',
        method_id='osconfig.folders.assignments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='assignment',
        request_type_name='OsconfigFoldersAssignmentsPatchRequest',
        response_type_name='Assignment',
        supports_download=False,
    )

  class FoldersOsConfigsService(base_api.BaseApiService):
    """Service class for the folders_osConfigs resource."""

    _NAME = 'folders_osConfigs'

    def __init__(self, client):
      super(OsconfigV1alpha1.FoldersOsConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an OsConfig.

      Args:
        request: (OsconfigFoldersOsConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OsConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/folders/{foldersId}/osConfigs',
        http_method='POST',
        method_id='osconfig.folders.osConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/osConfigs',
        request_field='osConfig',
        request_type_name='OsconfigFoldersOsConfigsCreateRequest',
        response_type_name='OsConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an OsConfig.

      Args:
        request: (OsconfigFoldersOsConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/folders/{foldersId}/osConfigs/{osConfigsId}',
        http_method='DELETE',
        method_id='osconfig.folders.osConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='OsconfigFoldersOsConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get an OsConfig.

      Args:
        request: (OsconfigFoldersOsConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OsConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/folders/{foldersId}/osConfigs/{osConfigsId}',
        http_method='GET',
        method_id='osconfig.folders.osConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='OsconfigFoldersOsConfigsGetRequest',
        response_type_name='OsConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Get a page of OsConfigs.

      Args:
        request: (OsconfigFoldersOsConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOsConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/folders/{foldersId}/osConfigs',
        http_method='GET',
        method_id='osconfig.folders.osConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/osConfigs',
        request_field='',
        request_type_name='OsconfigFoldersOsConfigsListRequest',
        response_type_name='ListOsConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update an OsConfig.

      Args:
        request: (OsconfigFoldersOsConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OsConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/folders/{foldersId}/osConfigs/{osConfigsId}',
        http_method='PATCH',
        method_id='osconfig.folders.osConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='osConfig',
        request_type_name='OsconfigFoldersOsConfigsPatchRequest',
        response_type_name='OsConfig',
        supports_download=False,
    )

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(OsconfigV1alpha1.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for an OsConfig or an OS Config Assignment.
Returns NOT_FOUND error if the OsConfig does not exist. Returns an empty
policy if the resource exists but does not have a policy set.

      Args:
        request: (OsconfigFoldersGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/folders/{foldersId}:getIamPolicy',
        http_method='POST',
        method_id='osconfig.folders.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='OsconfigFoldersGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy for an OsConfig or an OS Config Assignment.
Replaces any existing policy.

      Args:
        request: (OsconfigFoldersSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/folders/{foldersId}:setIamPolicy',
        http_method='POST',
        method_id='osconfig.folders.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='OsconfigFoldersSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Test the access control policy for an OsConfig or an OS Config Assignment.

      Args:
        request: (OsconfigFoldersTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/folders/{foldersId}:testIamPermissions',
        http_method='POST',
        method_id='osconfig.folders.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='OsconfigFoldersTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class OrganizationsAssignmentsService(base_api.BaseApiService):
    """Service class for the organizations_assignments resource."""

    _NAME = 'organizations_assignments'

    def __init__(self, client):
      super(OsconfigV1alpha1.OrganizationsAssignmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an OS Config Assignment.

      Args:
        request: (OsconfigOrganizationsAssignmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Assignment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/assignments',
        http_method='POST',
        method_id='osconfig.organizations.assignments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/assignments',
        request_field='assignment',
        request_type_name='OsconfigOrganizationsAssignmentsCreateRequest',
        response_type_name='Assignment',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an OS Config Assignment.

      Args:
        request: (OsconfigOrganizationsAssignmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/assignments/{assignmentsId}',
        http_method='DELETE',
        method_id='osconfig.organizations.assignments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='OsconfigOrganizationsAssignmentsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get an OS Config Assignment.

      Args:
        request: (OsconfigOrganizationsAssignmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Assignment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/assignments/{assignmentsId}',
        http_method='GET',
        method_id='osconfig.organizations.assignments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='OsconfigOrganizationsAssignmentsGetRequest',
        response_type_name='Assignment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Get a page of OS Config Assignments.

      Args:
        request: (OsconfigOrganizationsAssignmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAssignmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/assignments',
        http_method='GET',
        method_id='osconfig.organizations.assignments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/assignments',
        request_field='',
        request_type_name='OsconfigOrganizationsAssignmentsListRequest',
        response_type_name='ListAssignmentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update an OS Config Assignment.

      Args:
        request: (OsconfigOrganizationsAssignmentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Assignment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/assignments/{assignmentsId}',
        http_method='PATCH',
        method_id='osconfig.organizations.assignments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='assignment',
        request_type_name='OsconfigOrganizationsAssignmentsPatchRequest',
        response_type_name='Assignment',
        supports_download=False,
    )

  class OrganizationsOsConfigsService(base_api.BaseApiService):
    """Service class for the organizations_osConfigs resource."""

    _NAME = 'organizations_osConfigs'

    def __init__(self, client):
      super(OsconfigV1alpha1.OrganizationsOsConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an OsConfig.

      Args:
        request: (OsconfigOrganizationsOsConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OsConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/osConfigs',
        http_method='POST',
        method_id='osconfig.organizations.osConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/osConfigs',
        request_field='osConfig',
        request_type_name='OsconfigOrganizationsOsConfigsCreateRequest',
        response_type_name='OsConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an OsConfig.

      Args:
        request: (OsconfigOrganizationsOsConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/osConfigs/{osConfigsId}',
        http_method='DELETE',
        method_id='osconfig.organizations.osConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='OsconfigOrganizationsOsConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get an OsConfig.

      Args:
        request: (OsconfigOrganizationsOsConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OsConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/osConfigs/{osConfigsId}',
        http_method='GET',
        method_id='osconfig.organizations.osConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='OsconfigOrganizationsOsConfigsGetRequest',
        response_type_name='OsConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Get a page of OsConfigs.

      Args:
        request: (OsconfigOrganizationsOsConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOsConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/osConfigs',
        http_method='GET',
        method_id='osconfig.organizations.osConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/osConfigs',
        request_field='',
        request_type_name='OsconfigOrganizationsOsConfigsListRequest',
        response_type_name='ListOsConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update an OsConfig.

      Args:
        request: (OsconfigOrganizationsOsConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OsConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/osConfigs/{osConfigsId}',
        http_method='PATCH',
        method_id='osconfig.organizations.osConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='osConfig',
        request_type_name='OsconfigOrganizationsOsConfigsPatchRequest',
        response_type_name='OsConfig',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(OsconfigV1alpha1.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for an OsConfig or an OS Config Assignment.
Returns NOT_FOUND error if the OsConfig does not exist. Returns an empty
policy if the resource exists but does not have a policy set.

      Args:
        request: (OsconfigOrganizationsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}:getIamPolicy',
        http_method='POST',
        method_id='osconfig.organizations.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='OsconfigOrganizationsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy for an OsConfig or an OS Config Assignment.
Replaces any existing policy.

      Args:
        request: (OsconfigOrganizationsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}:setIamPolicy',
        http_method='POST',
        method_id='osconfig.organizations.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='OsconfigOrganizationsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Test the access control policy for an OsConfig or an OS Config Assignment.

      Args:
        request: (OsconfigOrganizationsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}:testIamPermissions',
        http_method='POST',
        method_id='osconfig.organizations.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='OsconfigOrganizationsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsAssignmentsService(base_api.BaseApiService):
    """Service class for the projects_assignments resource."""

    _NAME = 'projects_assignments'

    def __init__(self, client):
      super(OsconfigV1alpha1.ProjectsAssignmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an OS Config Assignment.

      Args:
        request: (OsconfigProjectsAssignmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Assignment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/assignments',
        http_method='POST',
        method_id='osconfig.projects.assignments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/assignments',
        request_field='assignment',
        request_type_name='OsconfigProjectsAssignmentsCreateRequest',
        response_type_name='Assignment',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an OS Config Assignment.

      Args:
        request: (OsconfigProjectsAssignmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/assignments/{assignmentsId}',
        http_method='DELETE',
        method_id='osconfig.projects.assignments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='OsconfigProjectsAssignmentsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get an OS Config Assignment.

      Args:
        request: (OsconfigProjectsAssignmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Assignment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/assignments/{assignmentsId}',
        http_method='GET',
        method_id='osconfig.projects.assignments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='OsconfigProjectsAssignmentsGetRequest',
        response_type_name='Assignment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Get a page of OS Config Assignments.

      Args:
        request: (OsconfigProjectsAssignmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAssignmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/assignments',
        http_method='GET',
        method_id='osconfig.projects.assignments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/assignments',
        request_field='',
        request_type_name='OsconfigProjectsAssignmentsListRequest',
        response_type_name='ListAssignmentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update an OS Config Assignment.

      Args:
        request: (OsconfigProjectsAssignmentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Assignment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/assignments/{assignmentsId}',
        http_method='PATCH',
        method_id='osconfig.projects.assignments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='assignment',
        request_type_name='OsconfigProjectsAssignmentsPatchRequest',
        response_type_name='Assignment',
        supports_download=False,
    )

  class ProjectsOsConfigsService(base_api.BaseApiService):
    """Service class for the projects_osConfigs resource."""

    _NAME = 'projects_osConfigs'

    def __init__(self, client):
      super(OsconfigV1alpha1.ProjectsOsConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an OsConfig.

      Args:
        request: (OsconfigProjectsOsConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OsConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/osConfigs',
        http_method='POST',
        method_id='osconfig.projects.osConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/osConfigs',
        request_field='osConfig',
        request_type_name='OsconfigProjectsOsConfigsCreateRequest',
        response_type_name='OsConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an OsConfig.

      Args:
        request: (OsconfigProjectsOsConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/osConfigs/{osConfigsId}',
        http_method='DELETE',
        method_id='osconfig.projects.osConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='OsconfigProjectsOsConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get an OsConfig.

      Args:
        request: (OsconfigProjectsOsConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OsConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/osConfigs/{osConfigsId}',
        http_method='GET',
        method_id='osconfig.projects.osConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='OsconfigProjectsOsConfigsGetRequest',
        response_type_name='OsConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Get a page of OsConfigs.

      Args:
        request: (OsconfigProjectsOsConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOsConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/osConfigs',
        http_method='GET',
        method_id='osconfig.projects.osConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/osConfigs',
        request_field='',
        request_type_name='OsconfigProjectsOsConfigsListRequest',
        response_type_name='ListOsConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update an OsConfig.

      Args:
        request: (OsconfigProjectsOsConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OsConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/osConfigs/{osConfigsId}',
        http_method='PATCH',
        method_id='osconfig.projects.osConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='osConfig',
        request_type_name='OsconfigProjectsOsConfigsPatchRequest',
        response_type_name='OsConfig',
        supports_download=False,
    )

  class ProjectsPatchJobsInstanceDetailsService(base_api.BaseApiService):
    """Service class for the projects_patchJobs_instanceDetails resource."""

    _NAME = 'projects_patchJobs_instanceDetails'

    def __init__(self, client):
      super(OsconfigV1alpha1.ProjectsPatchJobsInstanceDetailsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Get a page of instances' details for a given patch job.

      Args:
        request: (OsconfigProjectsPatchJobsInstanceDetailsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPatchJobInstanceDetailsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/patchJobs/{patchJobsId}/instanceDetails',
        http_method='GET',
        method_id='osconfig.projects.patchJobs.instanceDetails.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/instanceDetails',
        request_field='',
        request_type_name='OsconfigProjectsPatchJobsInstanceDetailsListRequest',
        response_type_name='ListPatchJobInstanceDetailsResponse',
        supports_download=False,
    )

  class ProjectsPatchJobsService(base_api.BaseApiService):
    """Service class for the projects_patchJobs resource."""

    _NAME = 'projects_patchJobs'

    def __init__(self, client):
      super(OsconfigV1alpha1.ProjectsPatchJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancel a patch job. The patch job must be active. Canceled patch jobs.
cannot be restarted.

      Args:
        request: (OsconfigProjectsPatchJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PatchJob) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/patchJobs/{patchJobsId}:cancel',
        http_method='POST',
        method_id='osconfig.projects.patchJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelPatchJobRequest',
        request_type_name='OsconfigProjectsPatchJobsCancelRequest',
        response_type_name='PatchJob',
        supports_download=False,
    )

    def Execute(self, request, global_params=None):
      r"""Patch GCE instances by creating and running a PatchJob.

      Args:
        request: (OsconfigProjectsPatchJobsExecuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PatchJob) The response message.
      """
      config = self.GetMethodConfig('Execute')
      return self._RunMethod(
          config, request, global_params=global_params)

    Execute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/patchJobs:execute',
        http_method='POST',
        method_id='osconfig.projects.patchJobs.execute',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/patchJobs:execute',
        request_field='executePatchJobRequest',
        request_type_name='OsconfigProjectsPatchJobsExecuteRequest',
        response_type_name='PatchJob',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get the patch job. This can be used to track the progress of an.
ongoing patch job or review the details of completed jobs.

      Args:
        request: (OsconfigProjectsPatchJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PatchJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/patchJobs/{patchJobsId}',
        http_method='GET',
        method_id='osconfig.projects.patchJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='OsconfigProjectsPatchJobsGetRequest',
        response_type_name='PatchJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Get a page of patch jobs.

      Args:
        request: (OsconfigProjectsPatchJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPatchJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/patchJobs',
        http_method='GET',
        method_id='osconfig.projects.patchJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/patchJobs',
        request_field='',
        request_type_name='OsconfigProjectsPatchJobsListRequest',
        response_type_name='ListPatchJobsResponse',
        supports_download=False,
    )

  class ProjectsZonesInstancesService(base_api.BaseApiService):
    """Service class for the projects_zones_instances resource."""

    _NAME = 'projects_zones_instances'

    def __init__(self, client):
      super(OsconfigV1alpha1.ProjectsZonesInstancesService, self).__init__(client)
      self._upload_configs = {
          }

    def LookupConfigs(self, request, global_params=None):
      r"""Lookup the configs that are assigned to an instance. This lookup.
will merge all configs that are assigned to the instance resolving
conflicts as necessary.
This is usually called by the agent running on the instance but can be
called directly to see what configs
This

      Args:
        request: (OsconfigProjectsZonesInstancesLookupConfigsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LookupConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('LookupConfigs')
      return self._RunMethod(
          config, request, global_params=global_params)

    LookupConfigs.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/zones/{zonesId}/instances/{instancesId}:lookupConfigs',
        http_method='POST',
        method_id='osconfig.projects.zones.instances.lookupConfigs',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:lookupConfigs',
        request_field='lookupConfigsRequest',
        request_type_name='OsconfigProjectsZonesInstancesLookupConfigsRequest',
        response_type_name='LookupConfigsResponse',
        supports_download=False,
    )

    def ReportPatchJobInstanceDetails(self, request, global_params=None):
      r"""Endpoint used by the agent to report back its state during a patch.
job. This endpoint will also return the patch job's state and
configurations that the agent needs to know in order to run or stop
patching.

This endpoint is only used by the agent. Using it in other ways may
affect the state of the active patch job and prevent the patches from
being correctly applied to this instance.

      Args:
        request: (OsconfigProjectsZonesInstancesReportPatchJobInstanceDetailsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ReportPatchJobInstanceDetailsResponse) The response message.
      """
      config = self.GetMethodConfig('ReportPatchJobInstanceDetails')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReportPatchJobInstanceDetails.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/zones/{zonesId}/instances/{instancesId}:reportPatchJobInstanceDetails',
        http_method='POST',
        method_id='osconfig.projects.zones.instances.reportPatchJobInstanceDetails',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:reportPatchJobInstanceDetails',
        request_field='reportPatchJobInstanceDetailsRequest',
        request_type_name='OsconfigProjectsZonesInstancesReportPatchJobInstanceDetailsRequest',
        response_type_name='ReportPatchJobInstanceDetailsResponse',
        supports_download=False,
    )

  class ProjectsZonesService(base_api.BaseApiService):
    """Service class for the projects_zones resource."""

    _NAME = 'projects_zones'

    def __init__(self, client):
      super(OsconfigV1alpha1.ProjectsZonesService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(OsconfigV1alpha1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for an OsConfig or an OS Config Assignment.
Returns NOT_FOUND error if the OsConfig does not exist. Returns an empty
policy if the resource exists but does not have a policy set.

      Args:
        request: (OsconfigProjectsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}:getIamPolicy',
        http_method='POST',
        method_id='osconfig.projects.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='OsconfigProjectsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy for an OsConfig or an OS Config Assignment.
Replaces any existing policy.

      Args:
        request: (OsconfigProjectsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}:setIamPolicy',
        http_method='POST',
        method_id='osconfig.projects.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='OsconfigProjectsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Test the access control policy for an OsConfig or an OS Config Assignment.

      Args:
        request: (OsconfigProjectsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}:testIamPermissions',
        http_method='POST',
        method_id='osconfig.projects.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='OsconfigProjectsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )
