"""Generated message classes for osconfig version v2alpha.

OS management tools that can be used for patch management, patch compliance,
and configuration management on VM instances.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'osconfig'


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ErrorInfo(_messages.Message):
  r"""Describes the cause of the error with structured details. Example of an
  error when contacting the "pubsub.googleapis.com" API when it is not
  enabled: { "reason": "API_DISABLED" "domain": "googleapis.com" "metadata": {
  "resource": "projects/123", "service": "pubsub.googleapis.com" } } This
  response indicates that the pubsub.googleapis.com API is not enabled.
  Example of an error that is returned when attempting to create a Spanner
  instance in a region that is out of stock: { "reason": "STOCKOUT" "domain":
  "spanner.googleapis.com", "metadata": { "availableRegions": "us-central1,us-
  east2" } }

  Messages:
    MetadataValue: Additional structured details about this error. Keys must
      match a regular expression of `a-z+` but should ideally be
      lowerCamelCase. Also, they must be limited to 64 characters in length.
      When identifying the current value of an exceeded limit, the units
      should be contained in the key, not the value. For example, rather than
      `{"instanceLimit": "100/request"}`, should be returned as,
      `{"instanceLimitPerRequest": "100"}`, if the client exceeds the number
      of instances that can be created in a single (batch) request.

  Fields:
    domain: The logical grouping to which the "reason" belongs. The error
      domain is typically the registered service name of the tool or product
      that generates the error. Example: "pubsub.googleapis.com". If the error
      is generated by some common infrastructure, the error domain must be a
      globally unique value that identifies the infrastructure. For Google API
      infrastructure, the error domain is "googleapis.com".
    metadata: Additional structured details about this error. Keys must match
      a regular expression of `a-z+` but should ideally be lowerCamelCase.
      Also, they must be limited to 64 characters in length. When identifying
      the current value of an exceeded limit, the units should be contained in
      the key, not the value. For example, rather than `{"instanceLimit":
      "100/request"}`, should be returned as, `{"instanceLimitPerRequest":
      "100"}`, if the client exceeds the number of instances that can be
      created in a single (batch) request.
    reason: The reason of the error. This is a constant value that identifies
      the proximate cause of the error. Error reasons are unique within a
      particular domain of errors. This should be at most 63 characters and
      match a regular expression of `A-Z+[A-Z0-9]`, which represents
      UPPER_SNAKE_CASE.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Additional structured details about this error. Keys must match a
    regular expression of `a-z+` but should ideally be lowerCamelCase. Also,
    they must be limited to 64 characters in length. When identifying the
    current value of an exceeded limit, the units should be contained in the
    key, not the value. For example, rather than `{"instanceLimit":
    "100/request"}`, should be returned as, `{"instanceLimitPerRequest":
    "100"}`, if the client exceeds the number of instances that can be created
    in a single (batch) request.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  domain = _messages.StringField(1)
  metadata = _messages.MessageField('MetadataValue', 2)
  reason = _messages.StringField(3)


class FixedOrPercent(_messages.Message):
  r"""Message encapsulating a value that can be either absolute ("fixed") or
  relative ("percent") to a value.

  Fields:
    fixed: Specifies a fixed value.
    percent: Specifies the relative value defined as a percentage, which will
      be multiplied by a reference value.
  """

  fixed = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  percent = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudOsconfigInternalV2OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudOsconfigInternalV2alphaOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudOsconfigInternalV2alphaSetUpPolicyOrchestratorConfigRequest(_messages.Message):
  r"""Request to setup PolicyOrchestrator configuration."""


class GoogleCloudOsconfigInternalV2alphaValidatePolicyOrchestratorConfigResponse(_messages.Message):
  r"""Response to validate PolicyOrchestrator configuration.

  Fields:
    configErrors: description of invalid or missing configuration elements
    configured: indicator which shows if all configuration elements needed for
      correct work of policy orchestrator are set up.
  """

  configErrors = _messages.MessageField('ErrorInfo', 1, repeated=True)
  configured = _messages.BooleanField(2)


class GoogleCloudOsconfigInternalV2betaOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudOsconfigInternalV2mainOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudOsconfigV1OSPolicyAssignmentOperationMetadata(_messages.Message):
  r"""OS policy assignment operation metadata provided by OS policy assignment
  API methods that return long running operations.

  Enums:
    ApiMethodValueValuesEnum: The OS policy assignment API method.
    RolloutStateValueValuesEnum: State of the rollout

  Fields:
    apiMethod: The OS policy assignment API method.
    osPolicyAssignment: Reference to the `OSPolicyAssignment` API resource.
      Format: `projects/{project_number}/locations/{location}/osPolicyAssignme
      nts/{os_policy_assignment_id@revision_id}`
    rolloutStartTime: Rollout start time
    rolloutState: State of the rollout
    rolloutUpdateTime: Rollout update time
  """

  class ApiMethodValueValuesEnum(_messages.Enum):
    r"""The OS policy assignment API method.

    Values:
      API_METHOD_UNSPECIFIED: Invalid value
      CREATE: Create OS policy assignment API method
      UPDATE: Update OS policy assignment API method
      DELETE: Delete OS policy assignment API method
    """
    API_METHOD_UNSPECIFIED = 0
    CREATE = 1
    UPDATE = 2
    DELETE = 3

  class RolloutStateValueValuesEnum(_messages.Enum):
    r"""State of the rollout

    Values:
      ROLLOUT_STATE_UNSPECIFIED: Invalid value
      IN_PROGRESS: The rollout is in progress.
      CANCELLING: The rollout is being cancelled.
      CANCELLED: The rollout is cancelled.
      SUCCEEDED: The rollout has completed successfully.
    """
    ROLLOUT_STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    CANCELLING = 2
    CANCELLED = 3
    SUCCEEDED = 4

  apiMethod = _messages.EnumField('ApiMethodValueValuesEnum', 1)
  osPolicyAssignment = _messages.StringField(2)
  rolloutStartTime = _messages.StringField(3)
  rolloutState = _messages.EnumField('RolloutStateValueValuesEnum', 4)
  rolloutUpdateTime = _messages.StringField(5)


class GoogleCloudOsconfigV2OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudOsconfigV2alphaAgentPolicy(_messages.Message):
  r"""Configuration for the agent."""


class GoogleCloudOsconfigV2alphaAgentPolicyAssignment(_messages.Message):
  r"""Message describing AgentPolicyAssignment object

  Messages:
    AgentPoliciesValue: Required. Map from agent (eg: "cloudops") to its
      policy configuration. The key is the name of the agent.

  Fields:
    agentPolicies: Required. Map from agent (eg: "cloudops") to its policy
      configuration. The key is the name of the agent.
    createTime: Output only. When the AgentPolicyAssignment was created.
    description: Optional. Agent policy assignment description.
    etag: Output only. The etag for this assignment.
    instanceSelectors: Optional. Selector to target VMs for an assignment.
      There is a logical "AND" between instance_selectors.
    name: Immutable. Identifier. Name of resource, in format: `projects/{proje
      ct_number}/locations/{location}/agentPolicyAssignments/{agent_policy_ass
      ignment_id}`
    updateTime: Output only. When the AgentPolicyAssignment was most recently
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AgentPoliciesValue(_messages.Message):
    r"""Required. Map from agent (eg: "cloudops") to its policy configuration.
    The key is the name of the agent.

    Messages:
      AdditionalProperty: An additional property for a AgentPoliciesValue
        object.

    Fields:
      additionalProperties: Additional properties of type AgentPoliciesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AgentPoliciesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudOsconfigV2alphaAgentPolicy attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudOsconfigV2alphaAgentPolicy', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  agentPolicies = _messages.MessageField('AgentPoliciesValue', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  instanceSelectors = _messages.MessageField('GoogleCloudOsconfigV2alphaAgentPolicyAssignmentInstanceSelector', 5, repeated=True)
  name = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class GoogleCloudOsconfigV2alphaAgentPolicyAssignmentInstanceSelector(_messages.Message):
  r"""Selector to target VMs for an assignment.

  Fields:
    labelSelector: Optional. Labels within the LabelSelector are OR'd.
  """

  labelSelector = _messages.MessageField('GoogleCloudOsconfigV2alphaAgentPolicyAssignmentLabelSelector', 1)


class GoogleCloudOsconfigV2alphaAgentPolicyAssignmentLabelSelector(_messages.Message):
  r"""A LabelSelector is applicable for a VM only iff it matches all labels in
  the LabelSelector.

  Messages:
    IncludedLabelsValue: Optional. Labels as key value pairs. A VM should
      contain all the pairs specified in this map to be selected; Labels
      within the LabelSelector are OR'ed.

  Fields:
    includedLabels: Optional. Labels as key value pairs. A VM should contain
      all the pairs specified in this map to be selected; Labels within the
      LabelSelector are OR'ed.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class IncludedLabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs. A VM should contain all the pairs
    specified in this map to be selected; Labels within the LabelSelector are
    OR'ed.

    Messages:
      AdditionalProperty: An additional property for a IncludedLabelsValue
        object.

    Fields:
      additionalProperties: Additional properties of type IncludedLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a IncludedLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  includedLabels = _messages.MessageField('IncludedLabelsValue', 1)


class GoogleCloudOsconfigV2alphaAgentPolicyAssignmentOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudOsconfigV2alphaListAgentPolicyAssignmentsResponse(_messages.Message):
  r"""Message for response to listing AgentPolicyAssignments

  Fields:
    agentPolicyAssignments: The list of AgentPolicyAssignment
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  agentPolicyAssignments = _messages.MessageField('GoogleCloudOsconfigV2alphaAgentPolicyAssignment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudOsconfigV2alphaListPolicyOrchestratorsResponse(_messages.Message):
  r"""Response for the list policy orchestrator resources.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    policyOrchestrators: The policy orchestrators for the specified parent
      resource.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  policyOrchestrators = _messages.MessageField('GoogleCloudOsconfigV2alphaPolicyOrchestrator', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudOsconfigV2alphaOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudOsconfigV2alphaOrchestratedResource(_messages.Message):
  r"""Represents a resource that is being orchestrated by the policy
  orchestrator.

  Fields:
    id: Optional. ID of the resource to be used while generating set of
      affected resources. For UPSERT action the value is auto-generated during
      PolicyOrchestrator creation when not set. When the value is set it
      should following next restrictions: * Must contain only lowercase
      letters, numbers, and hyphens. * Must start with a letter. * Must be
      between 1-63 characters. * Must end with a number or a letter. * Must be
      unique within the project. For DELETE action, ID must be specified
      explicitly during PolicyOrchestrator creation.
    osPolicyAssignmentV1Payload: Optional. OSPolicyAssignment resource to be
      created, updated or deleted. Name field is ignored and replace with a
      generated value. With this field set, orchestrator will perform actions
      on
      `project/{project}/locations/{zone}/osPolicyAssignments/{resource_id}`
      resources, where `project` and `zone` pairs come from the expanded
      scope, and `resource_id` comes from the `resource_id` field of
      orchestrator resource.
  """

  id = _messages.StringField(1)
  osPolicyAssignmentV1Payload = _messages.MessageField('OSPolicyAssignment', 2)


class GoogleCloudOsconfigV2alphaOrchestrationScope(_messages.Message):
  r"""Defines a set of selectors which drive which resources are in scope of
  policy orchestration.

  Fields:
    selectors: Optional. Selectors of the orchestration scope. There is a
      logical AND between each selector defined. When there is no explicit
      `ResourceHierarchySelector` selector specified, the scope is by default
      bounded to the parent of the policy orchestrator resource.
  """

  selectors = _messages.MessageField('GoogleCloudOsconfigV2alphaOrchestrationScopeSelector', 1, repeated=True)


class GoogleCloudOsconfigV2alphaOrchestrationScopeLocationSelector(_messages.Message):
  r"""Selector containing locations in scope.

  Fields:
    includedLocations: Optional. Names of the locations in scope. Format: `us-
      central1-a`
  """

  includedLocations = _messages.StringField(1, repeated=True)


class GoogleCloudOsconfigV2alphaOrchestrationScopeResourceHierarchySelector(_messages.Message):
  r"""Selector containing Cloud Resource Manager resource hierarchy nodes.

  Fields:
    includedFolders: Optional. Names of the folders in scope. Format:
      `folders/{folder_id}`
    includedProjects: Optional. Names of the projects in scope. Format:
      `projects/{project_number}`
  """

  includedFolders = _messages.StringField(1, repeated=True)
  includedProjects = _messages.StringField(2, repeated=True)


class GoogleCloudOsconfigV2alphaOrchestrationScopeSelector(_messages.Message):
  r"""Selector for the resources in scope of orchestration.

  Fields:
    locationSelector: Selector for selecting locations.
    resourceHierarchySelector: Selector for selecting resource hierarchy.
  """

  locationSelector = _messages.MessageField('GoogleCloudOsconfigV2alphaOrchestrationScopeLocationSelector', 1)
  resourceHierarchySelector = _messages.MessageField('GoogleCloudOsconfigV2alphaOrchestrationScopeResourceHierarchySelector', 2)


class GoogleCloudOsconfigV2alphaPolicyOrchestrator(_messages.Message):
  r"""PolicyOrchestrator helps managing project+zone level policy resources
  (e.g. OS Policy Assignments), by providing tools to create, update and
  delete them across projects and locations, at scale. Policy orchestrator
  functions as an endless loop. Each iteration orchestrator computes a set of
  resources that should be affected, then progressively applies changes to
  them. If for some reason this set of resources changes over time (e.g. new
  projects are added), the future loop iterations will address that.
  Orchestrator can either upsert or delete policy resources. For more details,
  see the description of the `action`, and `orchestrated_resource` fields.
  Note that policy orchestrator do not "manage" the resources it creates.
  Every iteration is independent and only minimal history of past actions is
  retained (apart from Cloud Logging). If orchestrator gets deleted, it does
  not affect the resources it created in the past. Those will remain where
  they were. Same applies if projects are removed from the orchestrator's
  scope.

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    action: Required. Action to be done by the orchestrator in
      `projects/{project_id}/zones/{zone_id}` locations defined by the
      `orchestration_scope`. Allowed values: - `UPSERT` - Orchestrator will
      create or update target resources. - `DELETE` - Orchestrator will delete
      target resources, if they exist
    createTime: Output only. Timestamp when the policy orchestrator resource
      was created.
    description: Optional. Freeform text describing the purpose of the
      resource.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and may be sent on update and delete requests to
      ensure the client has an up-to-date value before proceeding.
    labels: Optional. Labels as key value pairs
    name: Immutable. Identifier. In form of * `organizations/{organization_id}
      /locations/global/policyOrchestrators/{orchestrator_id}` * `folders/{fol
      der_id}/locations/global/policyOrchestrators/{orchestrator_id}` * `proje
      cts/{project_id_or_number}/locations/global/policyOrchestrators/{orchest
      rator_id}`
    orchestratedResource: Required. Resource to be orchestrated by the policy
      orchestrator.
    orchestrationScope: Optional. Defines scope for the orchestration, in
      context of the enclosing PolicyOrchestrator resource. Scope is expanded
      into a list of pairs, in which the rollout action will take place.
      Expansion starts with a Folder resource parenting the PolicyOrchestrator
      resource: - All the descendant projects are listed. - List of project is
      cross joined with a list of all available zones. - Resulting list of
      pairs is filtered according to the selectors.
    orchestrationState: Output only. State of the orchestration.
    reconciling: Output only. Set to true, if the there are ongoing changes
      being applied by the orchestrator.
    state: Optional. State of the orchestrator. Can be updated to change
      orchestrator behaviour. Allowed values: - `ACTIVE` - orchestrator is
      actively looking for actions to be taken. - `STOPPED` - orchestrator
      won't make any changes. Note: There might be more states added in the
      future. We use string here instead of an enum, to avoid the need of
      propagating new states to all the client code.
    updateTime: Output only. Timestamp when the policy orchestrator resource
      was last modified.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  action = _messages.StringField(1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  orchestratedResource = _messages.MessageField('GoogleCloudOsconfigV2alphaOrchestratedResource', 7)
  orchestrationScope = _messages.MessageField('GoogleCloudOsconfigV2alphaOrchestrationScope', 8)
  orchestrationState = _messages.MessageField('GoogleCloudOsconfigV2alphaPolicyOrchestratorOrchestrationState', 9)
  reconciling = _messages.BooleanField(10)
  state = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class GoogleCloudOsconfigV2alphaPolicyOrchestratorIterationState(_messages.Message):
  r"""Describes the state of a single iteration of the orchestrator.

  Enums:
    StateValueValuesEnum: Output only. State of the iteration.

  Fields:
    error: Output only. Error thrown in the wave iteration.
    failedActions: Output only. Number of orchestration actions which failed
      so far. For more details, query the Cloud Logs.
    finishTime: Output only. Finish time of the wave iteration.
    iterationId: Output only. Unique identifier of the iteration.
    performedActions: Output only. Overall number of actions done by the
      orchestrator so far.
    progress: Output only. An estimated percentage of the progress. Number
      between 0 and 100.
    rolloutResource: Output only. Handle to the Progressive Rollouts API
      rollout resource, which contains detailed information about a particular
      orchestration iteration.
    startTime: Output only. Start time of the wave iteration.
    state: Output only. State of the iteration.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the iteration.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      PROCESSING: Iteration is in progress.
      COMPLETED: Iteration completed, with all actions being successful.
      FAILED: Iteration completed, with failures.
      CANCELLED: Iteration was explicitly cancelled.
      UNKNOWN: Impossible to determine current state of the iteration.
    """
    STATE_UNSPECIFIED = 0
    PROCESSING = 1
    COMPLETED = 2
    FAILED = 3
    CANCELLED = 4
    UNKNOWN = 5

  error = _messages.MessageField('Status', 1)
  failedActions = _messages.IntegerField(2)
  finishTime = _messages.StringField(3)
  iterationId = _messages.StringField(4)
  performedActions = _messages.IntegerField(5)
  progress = _messages.FloatField(6, variant=_messages.Variant.FLOAT)
  rolloutResource = _messages.StringField(7)
  startTime = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)


class GoogleCloudOsconfigV2alphaPolicyOrchestratorOrchestrationState(_messages.Message):
  r"""Describes the state of the orchestration process.

  Fields:
    currentIterationState: Output only. Current Wave iteration state.
    previousIterationState: Output only. Previous Wave iteration state.
  """

  currentIterationState = _messages.MessageField('GoogleCloudOsconfigV2alphaPolicyOrchestratorIterationState', 1)
  previousIterationState = _messages.MessageField('GoogleCloudOsconfigV2alphaPolicyOrchestratorIterationState', 2)


class GoogleCloudOsconfigV2betaOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudOsconfigV2mainAgentPolicyAssignmentOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudOsconfigV2mainOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MessageSet(_messages.Message):
  r"""This is proto2's version of MessageSet. DEPRECATED: DO NOT USE FOR NEW
  FIELDS. If you are using editions or proto2, please make your own extendable
  messages for your use case. If you are using proto3, please use `Any`
  instead. MessageSet was the implementation of extensions for proto1. When
  proto2 was introduced, extensions were implemented as a first-class feature.
  This schema for MessageSet was meant to be a "bridge" solution to migrate
  MessageSet-bearing messages from proto1 to proto2. This schema has been
  open-sourced only to facilitate the migration of Google products with
  MessageSet-bearing messages to open-source environments.
  """



class OSPolicy(_messages.Message):
  r"""An OS policy defines the desired state configuration for a VM.

  Enums:
    ModeValueValuesEnum: Required. Policy mode

  Fields:
    allowNoResourceGroupMatch: This flag determines the OS policy compliance
      status when none of the resource groups within the policy are applicable
      for a VM. Set this value to `true` if the policy needs to be reported as
      compliant even if the policy has nothing to validate or enforce.
    description: Policy description. Length of the description is limited to
      1024 characters.
    fingerprint: Output only. OSPolicy fingerprint (checksum)
    id: Required. The id of the OS policy with the following restrictions: *
      Must contain only lowercase letters, numbers, and hyphens. * Must start
      with a letter. * Must be between 1-63 characters. * Must end with a
      number or a letter. * Must be unique within the assignment.
    mode: Required. Policy mode
    resourceGroups: Required. List of resource groups for the policy. For a
      particular VM, resource groups are evaluated in the order specified and
      the first resource group that is applicable is selected and the rest are
      ignored. If none of the resource groups are applicable for a VM, the VM
      is considered to be non-compliant w.r.t this policy. This behavior can
      be toggled by the flag `allow_no_resource_group_match`
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Required. Policy mode

    Values:
      MODE_UNSPECIFIED: Invalid mode
      VALIDATION: This mode checks if the configuration resources in the
        policy are in their desired state. No actions are performed if they
        are not in the desired state. This mode is used for reporting
        purposes.
      ENFORCEMENT: This mode checks if the configuration resources in the
        policy are in their desired state, and if not, enforces the desired
        state.
    """
    MODE_UNSPECIFIED = 0
    VALIDATION = 1
    ENFORCEMENT = 2

  allowNoResourceGroupMatch = _messages.BooleanField(1)
  description = _messages.StringField(2)
  fingerprint = _messages.StringField(3)
  id = _messages.StringField(4)
  mode = _messages.EnumField('ModeValueValuesEnum', 5)
  resourceGroups = _messages.MessageField('OSPolicyResourceGroup', 6, repeated=True)


class OSPolicyAssignment(_messages.Message):
  r"""OS policy assignment is an API resource that is used to apply a set of
  OS policies to a dynamically targeted group of Compute Engine VM instances.
  An OS policy is used to define the desired state configuration for a Compute
  Engine VM instance through a set of configuration resources that provide
  capabilities such as installing or removing software packages, or executing
  a script. For more information about the OS policy resource definitions and
  examples, see [OS policy and OS policy
  assignment](https://cloud.google.com/compute/docs/os-configuration-
  management/working-with-os-policies).

  Enums:
    RolloutStateValueValuesEnum: Output only. OS policy assignment rollout
      state

  Fields:
    baseline: Output only. Indicates that this revision has been successfully
      rolled out in this zone and new VMs will be assigned OS policies from
      this revision. For a given OS policy assignment, there is only one
      revision with a value of `true` for this field.
    deleted: Output only. Indicates that this revision deletes the OS policy
      assignment.
    description: OS policy assignment description. Length of the description
      is limited to 1024 characters.
    etag: The etag for this OS policy assignment. If this is provided on
      update, it must match the server's etag.
    instanceFilter: Required. Filter to select VMs.
    name: Resource name. Format: `projects/{project_number}/locations/{locatio
      n}/osPolicyAssignments/{os_policy_assignment_id}` This field is ignored
      when you create an OS policy assignment.
    osPolicies: Required. List of OS policies to be applied to the VMs.
    reconciling: Output only. Indicates that reconciliation is in progress for
      the revision. This value is `true` when the `rollout_state` is one of: *
      IN_PROGRESS * CANCELLING
    revisionCreateTime: Output only. The timestamp that the revision was
      created.
    revisionId: Output only. The assignment revision ID A new revision is
      committed whenever a rollout is triggered for a OS policy assignment
    rollout: Required. Rollout to deploy the OS policy assignment. A rollout
      is triggered in the following situations: 1) OSPolicyAssignment is
      created. 2) OSPolicyAssignment is updated and the update contains
      changes to one of the following fields: - instance_filter - os_policies
      3) OSPolicyAssignment is deleted.
    rolloutState: Output only. OS policy assignment rollout state
    uid: Output only. Server generated unique id for the OS policy assignment
      resource.
  """

  class RolloutStateValueValuesEnum(_messages.Enum):
    r"""Output only. OS policy assignment rollout state

    Values:
      ROLLOUT_STATE_UNSPECIFIED: Invalid value
      IN_PROGRESS: The rollout is in progress.
      CANCELLING: The rollout is being cancelled.
      CANCELLED: The rollout is cancelled.
      SUCCEEDED: The rollout has completed successfully.
    """
    ROLLOUT_STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    CANCELLING = 2
    CANCELLED = 3
    SUCCEEDED = 4

  baseline = _messages.BooleanField(1)
  deleted = _messages.BooleanField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  instanceFilter = _messages.MessageField('OSPolicyAssignmentInstanceFilter', 5)
  name = _messages.StringField(6)
  osPolicies = _messages.MessageField('OSPolicy', 7, repeated=True)
  reconciling = _messages.BooleanField(8)
  revisionCreateTime = _messages.StringField(9)
  revisionId = _messages.StringField(10)
  rollout = _messages.MessageField('OSPolicyAssignmentRollout', 11)
  rolloutState = _messages.EnumField('RolloutStateValueValuesEnum', 12)
  uid = _messages.StringField(13)


class OSPolicyAssignmentInstanceFilter(_messages.Message):
  r"""Filters to select target VMs for an assignment. If more than one filter
  criteria is specified below, a VM will be selected if and only if it
  satisfies all of them.

  Fields:
    all: Target all VMs in the project. If true, no other criteria is
      permitted.
    exclusionLabels: List of label sets used for VM exclusion. If the list has
      more than one label set, the VM is excluded if any of the label sets are
      applicable for the VM.
    inclusionLabels: List of label sets used for VM inclusion. If the list has
      more than one `LabelSet`, the VM is included if any of the label sets
      are applicable for the VM.
    inventories: List of inventories to select VMs. A VM is selected if its
      inventory data matches at least one of the following inventories.
  """

  all = _messages.BooleanField(1)
  exclusionLabels = _messages.MessageField('OSPolicyAssignmentLabelSet', 2, repeated=True)
  inclusionLabels = _messages.MessageField('OSPolicyAssignmentLabelSet', 3, repeated=True)
  inventories = _messages.MessageField('OSPolicyAssignmentInstanceFilterInventory', 4, repeated=True)


class OSPolicyAssignmentInstanceFilterInventory(_messages.Message):
  r"""VM inventory details.

  Fields:
    osShortName: Required. The OS short name
    osVersion: The OS version Prefix matches are supported if asterisk(*) is
      provided as the last character. For example, to match all versions with
      a major version of `7`, specify the following value for this field `7.*`
      An empty string matches all OS versions.
  """

  osShortName = _messages.StringField(1)
  osVersion = _messages.StringField(2)


class OSPolicyAssignmentLabelSet(_messages.Message):
  r"""Message representing label set. * A label is a key value pair set for a
  VM. * A LabelSet is a set of labels. * Labels within a LabelSet are ANDed.
  In other words, a LabelSet is applicable for a VM only if it matches all the
  labels in the LabelSet. * Example: A LabelSet with 2 labels: `env=prod` and
  `type=webserver` will only be applicable for those VMs with both labels
  present.

  Messages:
    LabelsValue: Labels are identified by key/value pairs in this map. A VM
      should contain all the key/value pairs specified in this map to be
      selected.

  Fields:
    labels: Labels are identified by key/value pairs in this map. A VM should
      contain all the key/value pairs specified in this map to be selected.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels are identified by key/value pairs in this map. A VM should
    contain all the key/value pairs specified in this map to be selected.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)


class OSPolicyAssignmentOperationMetadata(_messages.Message):
  r"""OS policy assignment operation metadata provided by OS policy assignment
  API methods that return long running operations.

  Enums:
    ApiMethodValueValuesEnum: The OS policy assignment API method.
    RolloutStateValueValuesEnum: State of the rollout

  Fields:
    apiMethod: The OS policy assignment API method.
    osPolicyAssignment: Reference to the `OSPolicyAssignment` API resource.
      Format: `projects/{project_number}/locations/{location}/osPolicyAssignme
      nts/{os_policy_assignment_id@revision_id}`
    rolloutStartTime: Rollout start time
    rolloutState: State of the rollout
    rolloutUpdateTime: Rollout update time
  """

  class ApiMethodValueValuesEnum(_messages.Enum):
    r"""The OS policy assignment API method.

    Values:
      API_METHOD_UNSPECIFIED: Invalid value
      CREATE: Create OS policy assignment API method
      UPDATE: Update OS policy assignment API method
      DELETE: Delete OS policy assignment API method
    """
    API_METHOD_UNSPECIFIED = 0
    CREATE = 1
    UPDATE = 2
    DELETE = 3

  class RolloutStateValueValuesEnum(_messages.Enum):
    r"""State of the rollout

    Values:
      ROLLOUT_STATE_UNSPECIFIED: Invalid value
      IN_PROGRESS: The rollout is in progress.
      CANCELLING: The rollout is being cancelled.
      CANCELLED: The rollout is cancelled.
      SUCCEEDED: The rollout has completed successfully.
    """
    ROLLOUT_STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    CANCELLING = 2
    CANCELLED = 3
    SUCCEEDED = 4

  apiMethod = _messages.EnumField('ApiMethodValueValuesEnum', 1)
  osPolicyAssignment = _messages.StringField(2)
  rolloutStartTime = _messages.StringField(3)
  rolloutState = _messages.EnumField('RolloutStateValueValuesEnum', 4)
  rolloutUpdateTime = _messages.StringField(5)


class OSPolicyAssignmentRollout(_messages.Message):
  r"""Message to configure the rollout at the zonal level for the OS policy
  assignment.

  Fields:
    disruptionBudget: Required. The maximum number (or percentage) of VMs per
      zone to disrupt at any given moment.
    minWaitDuration: Required. This determines the minimum duration of time to
      wait after the configuration changes are applied through the current
      rollout. A VM continues to count towards the `disruption_budget` at
      least until this duration of time has passed after configuration changes
      are applied.
  """

  disruptionBudget = _messages.MessageField('FixedOrPercent', 1)
  minWaitDuration = _messages.StringField(2)


class OSPolicyInventoryFilter(_messages.Message):
  r"""Filtering criteria to select VMs based on inventory details.

  Fields:
    osShortName: Required. The OS short name
    osVersion: The OS version Prefix matches are supported if asterisk(*) is
      provided as the last character. For example, to match all versions with
      a major version of `7`, specify the following value for this field `7.*`
      An empty string matches all OS versions.
  """

  osShortName = _messages.StringField(1)
  osVersion = _messages.StringField(2)


class OSPolicyResource(_messages.Message):
  r"""An OS policy resource is used to define the desired state configuration
  and provides a specific functionality like installing/removing packages,
  executing a script etc. The system ensures that resources are always in
  their desired state by taking necessary actions if they have drifted from
  their desired state.

  Fields:
    exec_: Exec resource
    file: File resource
    id: Required. The id of the resource with the following restrictions: *
      Must contain only lowercase letters, numbers, and hyphens. * Must start
      with a letter. * Must be between 1-63 characters. * Must end with a
      number or a letter. * Must be unique within the OS policy.
    pkg: Package resource
    repository: Package repository resource
  """

  exec_ = _messages.MessageField('OSPolicyResourceExecResource', 1)
  file = _messages.MessageField('OSPolicyResourceFileResource', 2)
  id = _messages.StringField(3)
  pkg = _messages.MessageField('OSPolicyResourcePackageResource', 4)
  repository = _messages.MessageField('OSPolicyResourceRepositoryResource', 5)


class OSPolicyResourceExecResource(_messages.Message):
  r"""A resource that allows executing scripts on the VM. The `ExecResource`
  has 2 stages: `validate` and `enforce` and both stages accept a script as an
  argument to execute. When the `ExecResource` is applied by the agent, it
  first executes the script in the `validate` stage. The `validate` stage can
  signal that the `ExecResource` is already in the desired state by returning
  an exit code of `100`. If the `ExecResource` is not in the desired state, it
  should return an exit code of `101`. Any other exit code returned by this
  stage is considered an error. If the `ExecResource` is not in the desired
  state based on the exit code from the `validate` stage, the agent proceeds
  to execute the script from the `enforce` stage. If the `ExecResource` is
  already in the desired state, the `enforce` stage will not be run. Similar
  to `validate` stage, the `enforce` stage should return an exit code of `100`
  to indicate that the resource in now in its desired state. Any other exit
  code is considered an error. NOTE: An exit code of `100` was chosen over `0`
  (and `101` vs `1`) to have an explicit indicator of `in desired state`, `not
  in desired state` and errors. Because, for example, Powershell will always
  return an exit code of `0` unless an `exit` statement is provided in the
  script. So, for reasons of consistency and being explicit, exit codes `100`
  and `101` were chosen.

  Fields:
    enforce: What to run to bring this resource into the desired state. An
      exit code of 100 indicates "success", any other exit code indicates a
      failure running enforce.
    validate: Required. What to run to validate this resource is in the
      desired state. An exit code of 100 indicates "in desired state", and
      exit code of 101 indicates "not in desired state". Any other exit code
      indicates a failure running validate.
  """

  enforce = _messages.MessageField('OSPolicyResourceExecResourceExec', 1)
  validate = _messages.MessageField('OSPolicyResourceExecResourceExec', 2)


class OSPolicyResourceExecResourceExec(_messages.Message):
  r"""A file or script to execute.

  Enums:
    InterpreterValueValuesEnum: Required. The script interpreter to use.

  Fields:
    args: Optional arguments to pass to the source during execution.
    file: A remote or local file.
    interpreter: Required. The script interpreter to use.
    outputFilePath: Only recorded for enforce Exec. Path to an output file
      (that is created by this Exec) whose content will be recorded in
      OSPolicyResourceCompliance after a successful run. Absence or failure to
      read this file will result in this ExecResource being non-compliant.
      Output file size is limited to 500K bytes.
    script: An inline script. The size of the script is limited to 32KiB.
  """

  class InterpreterValueValuesEnum(_messages.Enum):
    r"""Required. The script interpreter to use.

    Values:
      INTERPRETER_UNSPECIFIED: Invalid value, the request will return
        validation error.
      NONE: If an interpreter is not specified, the source is executed
        directly. This execution, without an interpreter, only succeeds for
        executables and scripts that have shebang lines.
      SHELL: Indicates that the script runs with `/bin/sh` on Linux and
        `cmd.exe` on Windows.
      POWERSHELL: Indicates that the script runs with PowerShell.
    """
    INTERPRETER_UNSPECIFIED = 0
    NONE = 1
    SHELL = 2
    POWERSHELL = 3

  args = _messages.StringField(1, repeated=True)
  file = _messages.MessageField('OSPolicyResourceFile', 2)
  interpreter = _messages.EnumField('InterpreterValueValuesEnum', 3)
  outputFilePath = _messages.StringField(4)
  script = _messages.StringField(5)


class OSPolicyResourceFile(_messages.Message):
  r"""A remote or local file.

  Fields:
    allowInsecure: Defaults to false. When false, files are subject to
      validations based on the file type: Remote: A checksum must be
      specified. Cloud Storage: An object generation number must be specified.
    gcs: A Cloud Storage object.
    localPath: A local path within the VM to use.
    remote: A generic remote file.
  """

  allowInsecure = _messages.BooleanField(1)
  gcs = _messages.MessageField('OSPolicyResourceFileGcs', 2)
  localPath = _messages.StringField(3)
  remote = _messages.MessageField('OSPolicyResourceFileRemote', 4)


class OSPolicyResourceFileGcs(_messages.Message):
  r"""Specifies a file available as a Cloud Storage Object.

  Fields:
    bucket: Required. Bucket of the Cloud Storage object.
    generation: Generation number of the Cloud Storage object.
    object: Required. Name of the Cloud Storage object.
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)


class OSPolicyResourceFileRemote(_messages.Message):
  r"""Specifies a file available via some URI.

  Fields:
    sha256Checksum: SHA256 checksum of the remote file.
    uri: Required. URI from which to fetch the object. It should contain both
      the protocol and path following the format `{protocol}://{location}`.
  """

  sha256Checksum = _messages.StringField(1)
  uri = _messages.StringField(2)


class OSPolicyResourceFileResource(_messages.Message):
  r"""A resource that manages the state of a file.

  Enums:
    StateValueValuesEnum: Required. Desired state of the file.

  Fields:
    content: A a file with this content. The size of the content is limited to
      32KiB.
    file: A remote or local source.
    path: Required. The absolute path of the file within the VM.
    permissions: Consists of three octal digits which represent, in order, the
      permissions of the owner, group, and other users for the file (similarly
      to the numeric mode used in the linux chmod utility). Each digit
      represents a three bit number with the 4 bit corresponding to the read
      permissions, the 2 bit corresponds to the write bit, and the one bit
      corresponds to the execute permission. Default behavior is 755. Below
      are some examples of permissions and their associated values: read,
      write, and execute: 7 read and execute: 5 read and write: 6 read only: 4
    state: Required. Desired state of the file.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Required. Desired state of the file.

    Values:
      DESIRED_STATE_UNSPECIFIED: Unspecified is invalid.
      PRESENT: Ensure file at path is present.
      ABSENT: Ensure file at path is absent.
      CONTENTS_MATCH: Ensure the contents of the file at path matches. If the
        file does not exist it will be created.
    """
    DESIRED_STATE_UNSPECIFIED = 0
    PRESENT = 1
    ABSENT = 2
    CONTENTS_MATCH = 3

  content = _messages.StringField(1)
  file = _messages.MessageField('OSPolicyResourceFile', 2)
  path = _messages.StringField(3)
  permissions = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class OSPolicyResourceGroup(_messages.Message):
  r"""Resource groups provide a mechanism to group OS policy resources.
  Resource groups enable OS policy authors to create a single OS policy to be
  applied to VMs running different operating Systems. When the OS policy is
  applied to a target VM, the appropriate resource group within the OS policy
  is selected based on the `OSFilter` specified within the resource group.

  Fields:
    inventoryFilters: List of inventory filters for the resource group. The
      resources in this resource group are applied to the target VM if it
      satisfies at least one of the following inventory filters. For example,
      to apply this resource group to VMs running either `RHEL` or `CentOS`
      operating systems, specify 2 items for the list with following values:
      inventory_filters[0].os_short_name='rhel' and
      inventory_filters[1].os_short_name='centos' If the list is empty, this
      resource group will be applied to the target VM unconditionally.
    resources: Required. List of resources configured for this resource group.
      The resources are executed in the exact order specified here.
  """

  inventoryFilters = _messages.MessageField('OSPolicyInventoryFilter', 1, repeated=True)
  resources = _messages.MessageField('OSPolicyResource', 2, repeated=True)


class OSPolicyResourcePackageResource(_messages.Message):
  r"""A resource that manages a system package.

  Enums:
    DesiredStateValueValuesEnum: Required. The desired state the agent should
      maintain for this package.

  Fields:
    apt: A package managed by Apt.
    deb: A deb package file.
    desiredState: Required. The desired state the agent should maintain for
      this package.
    googet: A package managed by GooGet.
    msi: An MSI package.
    rpm: An rpm package file.
    yum: A package managed by YUM.
    zypper: A package managed by Zypper.
  """

  class DesiredStateValueValuesEnum(_messages.Enum):
    r"""Required. The desired state the agent should maintain for this
    package.

    Values:
      DESIRED_STATE_UNSPECIFIED: Unspecified is invalid.
      INSTALLED: Ensure that the package is installed.
      REMOVED: The agent ensures that the package is not installed and
        uninstalls it if detected.
    """
    DESIRED_STATE_UNSPECIFIED = 0
    INSTALLED = 1
    REMOVED = 2

  apt = _messages.MessageField('OSPolicyResourcePackageResourceAPT', 1)
  deb = _messages.MessageField('OSPolicyResourcePackageResourceDeb', 2)
  desiredState = _messages.EnumField('DesiredStateValueValuesEnum', 3)
  googet = _messages.MessageField('OSPolicyResourcePackageResourceGooGet', 4)
  msi = _messages.MessageField('OSPolicyResourcePackageResourceMSI', 5)
  rpm = _messages.MessageField('OSPolicyResourcePackageResourceRPM', 6)
  yum = _messages.MessageField('OSPolicyResourcePackageResourceYUM', 7)
  zypper = _messages.MessageField('OSPolicyResourcePackageResourceZypper', 8)


class OSPolicyResourcePackageResourceAPT(_messages.Message):
  r"""A package managed by APT. - install: `apt-get update && apt-get -y
  install [name]` - remove: `apt-get -y remove [name]`

  Fields:
    name: Required. Package name.
  """

  name = _messages.StringField(1)


class OSPolicyResourcePackageResourceDeb(_messages.Message):
  r"""A deb package file. dpkg packages only support INSTALLED state.

  Fields:
    pullDeps: Whether dependencies should also be installed. - install when
      false: `dpkg -i package` - install when true: `apt-get update && apt-get
      -y install package.deb`
    source: Required. A deb package.
  """

  pullDeps = _messages.BooleanField(1)
  source = _messages.MessageField('OSPolicyResourceFile', 2)


class OSPolicyResourcePackageResourceGooGet(_messages.Message):
  r"""A package managed by GooGet. - install: `googet -noconfirm install
  package` - remove: `googet -noconfirm remove package`

  Fields:
    name: Required. Package name.
  """

  name = _messages.StringField(1)


class OSPolicyResourcePackageResourceMSI(_messages.Message):
  r"""An MSI package. MSI packages only support INSTALLED state.

  Fields:
    properties: Additional properties to use during installation. This should
      be in the format of Property=Setting. Appended to the defaults of
      `ACTION=INSTALL REBOOT=ReallySuppress`.
    source: Required. The MSI package.
  """

  properties = _messages.StringField(1, repeated=True)
  source = _messages.MessageField('OSPolicyResourceFile', 2)


class OSPolicyResourcePackageResourceRPM(_messages.Message):
  r"""An RPM package file. RPM packages only support INSTALLED state.

  Fields:
    pullDeps: Whether dependencies should also be installed. - install when
      false: `rpm --upgrade --replacepkgs package.rpm` - install when true:
      `yum -y install package.rpm` or `zypper -y install package.rpm`
    source: Required. An rpm package.
  """

  pullDeps = _messages.BooleanField(1)
  source = _messages.MessageField('OSPolicyResourceFile', 2)


class OSPolicyResourcePackageResourceYUM(_messages.Message):
  r"""A package managed by YUM. - install: `yum -y install package` - remove:
  `yum -y remove package`

  Fields:
    name: Required. Package name.
  """

  name = _messages.StringField(1)


class OSPolicyResourcePackageResourceZypper(_messages.Message):
  r"""A package managed by Zypper. - install: `zypper -y install package` -
  remove: `zypper -y rm package`

  Fields:
    name: Required. Package name.
  """

  name = _messages.StringField(1)


class OSPolicyResourceRepositoryResource(_messages.Message):
  r"""A resource that manages a package repository.

  Fields:
    apt: An Apt Repository.
    goo: A Goo Repository.
    yum: A Yum Repository.
    zypper: A Zypper Repository.
  """

  apt = _messages.MessageField('OSPolicyResourceRepositoryResourceAptRepository', 1)
  goo = _messages.MessageField('OSPolicyResourceRepositoryResourceGooRepository', 2)
  yum = _messages.MessageField('OSPolicyResourceRepositoryResourceYumRepository', 3)
  zypper = _messages.MessageField('OSPolicyResourceRepositoryResourceZypperRepository', 4)


class OSPolicyResourceRepositoryResourceAptRepository(_messages.Message):
  r"""Represents a single apt package repository. These will be added to a
  repo file that will be managed at
  `/etc/apt/sources.list.d/google_osconfig.list`.

  Enums:
    ArchiveTypeValueValuesEnum: Required. Type of archive files in this
      repository.

  Fields:
    archiveType: Required. Type of archive files in this repository.
    components: Required. List of components for this repository. Must contain
      at least one item.
    distribution: Required. Distribution of this repository.
    gpgKey: URI of the key file for this repository. The agent maintains a
      keyring at `/etc/apt/trusted.gpg.d/osconfig_agent_managed.gpg`.
    uri: Required. URI for this repository.
  """

  class ArchiveTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of archive files in this repository.

    Values:
      ARCHIVE_TYPE_UNSPECIFIED: Unspecified is invalid.
      DEB: Deb indicates that the archive contains binary files.
      DEB_SRC: Deb-src indicates that the archive contains source files.
    """
    ARCHIVE_TYPE_UNSPECIFIED = 0
    DEB = 1
    DEB_SRC = 2

  archiveType = _messages.EnumField('ArchiveTypeValueValuesEnum', 1)
  components = _messages.StringField(2, repeated=True)
  distribution = _messages.StringField(3)
  gpgKey = _messages.StringField(4)
  uri = _messages.StringField(5)


class OSPolicyResourceRepositoryResourceGooRepository(_messages.Message):
  r"""Represents a Goo package repository. These are added to a repo file that
  is managed at `C:/ProgramData/GooGet/repos/google_osconfig.repo`.

  Fields:
    name: Required. The name of the repository.
    url: Required. The url of the repository.
  """

  name = _messages.StringField(1)
  url = _messages.StringField(2)


class OSPolicyResourceRepositoryResourceYumRepository(_messages.Message):
  r"""Represents a single yum package repository. These are added to a repo
  file that is managed at `/etc/yum.repos.d/google_osconfig.repo`.

  Fields:
    baseUrl: Required. The location of the repository directory.
    displayName: The display name of the repository.
    gpgKeys: URIs of GPG keys.
    id: Required. A one word, unique name for this repository. This is the
      `repo id` in the yum config file and also the `display_name` if
      `display_name` is omitted. This id is also used as the unique identifier
      when checking for resource conflicts.
  """

  baseUrl = _messages.StringField(1)
  displayName = _messages.StringField(2)
  gpgKeys = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)


class OSPolicyResourceRepositoryResourceZypperRepository(_messages.Message):
  r"""Represents a single zypper package repository. These are added to a repo
  file that is managed at `/etc/zypp/repos.d/google_osconfig.repo`.

  Fields:
    baseUrl: Required. The location of the repository directory.
    displayName: The display name of the repository.
    gpgKeys: URIs of GPG keys.
    id: Required. A one word, unique name for this repository. This is the
      `repo id` in the zypper config file and also the `display_name` if
      `display_name` is omitted. This id is also used as the unique identifier
      when checking for GuestPolicy conflicts.
  """

  baseUrl = _messages.StringField(1)
  displayName = _messages.StringField(2)
  gpgKeys = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OsconfigFoldersLocationsGetRequest(_messages.Message):
  r"""A OsconfigFoldersLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class OsconfigFoldersLocationsGlobalPolicyOrchestratorsCreateRequest(_messages.Message):
  r"""A OsconfigFoldersLocationsGlobalPolicyOrchestratorsCreateRequest object.

  Fields:
    googleCloudOsconfigV2alphaPolicyOrchestrator: A
      GoogleCloudOsconfigV2alphaPolicyOrchestrator resource to be passed as
      the request body.
    parent: Required. The parent resource name in the form of: *
      `organizations/{organization_id}/locations/global` *
      `folders/{folder_id}/locations/global` *
      `projects/{project_id_or_number}/locations/global`
    policyOrchestratorId: Required. The logical identifier of the policy
      orchestrator, with the following restrictions: * Must contain only
      lowercase letters, numbers, and hyphens. * Must start with a letter. *
      Must be between 1-63 characters. * Must end with a number or a letter. *
      Must be unique within the parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  googleCloudOsconfigV2alphaPolicyOrchestrator = _messages.MessageField('GoogleCloudOsconfigV2alphaPolicyOrchestrator', 1)
  parent = _messages.StringField(2, required=True)
  policyOrchestratorId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class OsconfigFoldersLocationsGlobalPolicyOrchestratorsDeleteRequest(_messages.Message):
  r"""A OsconfigFoldersLocationsGlobalPolicyOrchestratorsDeleteRequest object.

  Fields:
    etag: Optional. The current etag of the policy orchestrator. If an etag is
      provided and does not match the current etag of the policy orchestrator,
      deletion will be blocked and an ABORTED error will be returned.
    name: Required. Name of the resource to be deleted.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class OsconfigFoldersLocationsGlobalPolicyOrchestratorsGetRequest(_messages.Message):
  r"""A OsconfigFoldersLocationsGlobalPolicyOrchestratorsGetRequest object.

  Fields:
    name: Required. The resource name.
  """

  name = _messages.StringField(1, required=True)


class OsconfigFoldersLocationsGlobalPolicyOrchestratorsListRequest(_messages.Message):
  r"""A OsconfigFoldersLocationsGlobalPolicyOrchestratorsListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent resource name.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class OsconfigFoldersLocationsGlobalPolicyOrchestratorsPatchRequest(_messages.Message):
  r"""A OsconfigFoldersLocationsGlobalPolicyOrchestratorsPatchRequest object.

  Fields:
    googleCloudOsconfigV2alphaPolicyOrchestrator: A
      GoogleCloudOsconfigV2alphaPolicyOrchestrator resource to be passed as
      the request body.
    name: Immutable. Identifier. In form of * `organizations/{organization_id}
      /locations/global/policyOrchestrators/{orchestrator_id}` * `folders/{fol
      der_id}/locations/global/policyOrchestrators/{orchestrator_id}` * `proje
      cts/{project_id_or_number}/locations/global/policyOrchestrators/{orchest
      rator_id}`
    updateMask: Optional. The list of fields to merge into the existing policy
      orchestrator. A special ["*"] field mask can be used to simply replace
      the entire resource. Otherwise, for all paths referenced in the mask,
      following merge rules are used: * output only fields are ignored, *
      primitive fields are replaced, * repeated fields are replaced, * map
      fields are merged key by key, * message fields are cleared if not set in
      the request, otherwise they are merged recursively (in particular -
      message fields set to an empty message has no side effects) If field
      mask (or its paths) is not specified, it is automatically inferred from
      the request using following rules: * primitive fields are listed, if set
      to a non-default value (as there is no way to distinguish between
      default and unset value), * map and repeated fields are listed, *
      `google.protobuf.Any` fields are listed, * other message fields are
      traversed recursively. Note: implicit mask does not allow clearing
      fields.
  """

  googleCloudOsconfigV2alphaPolicyOrchestrator = _messages.MessageField('GoogleCloudOsconfigV2alphaPolicyOrchestrator', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class OsconfigFoldersLocationsGlobalPolicyOrchestratorsSetUpConfigRequest(_messages.Message):
  r"""A OsconfigFoldersLocationsGlobalPolicyOrchestratorsSetUpConfigRequest
  object.

  Fields:
    googleCloudOsconfigInternalV2alphaSetUpPolicyOrchestratorConfigRequest: A
      GoogleCloudOsconfigInternalV2alphaSetUpPolicyOrchestratorConfigRequest
      resource to be passed as the request body.
    parent: Required. Parent for which the configuration is set up. Could be
      in form of: * `organizations/{organization_id}/locations/global` *
      `folders/{folder_id}/locations/global` *
      `projects/{project_id_or_number}/locations/global`
  """

  googleCloudOsconfigInternalV2alphaSetUpPolicyOrchestratorConfigRequest = _messages.MessageField('GoogleCloudOsconfigInternalV2alphaSetUpPolicyOrchestratorConfigRequest', 1)
  parent = _messages.StringField(2, required=True)


class OsconfigFoldersLocationsGlobalPolicyOrchestratorsValidateConfigRequest(_messages.Message):
  r"""A OsconfigFoldersLocationsGlobalPolicyOrchestratorsValidateConfigRequest
  object.

  Fields:
    parent: Required. Parent for which the configuration is validated. Could
      be in form of: * `organizations/{organization_id}/locations/global` *
      `folders/{folder_id}/locations/global` *
      `projects/{project_id_or_number}/locations/global`
  """

  parent = _messages.StringField(1, required=True)


class OsconfigFoldersLocationsListRequest(_messages.Message):
  r"""A OsconfigFoldersLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  includeUnrevealedLocations = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)


class OsconfigFoldersLocationsOperationsCancelRequest(_messages.Message):
  r"""A OsconfigFoldersLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class OsconfigFoldersLocationsOperationsDeleteRequest(_messages.Message):
  r"""A OsconfigFoldersLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class OsconfigFoldersLocationsOperationsGetRequest(_messages.Message):
  r"""A OsconfigFoldersLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class OsconfigFoldersLocationsOperationsListRequest(_messages.Message):
  r"""A OsconfigFoldersLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class OsconfigOrganizationsLocationsGetRequest(_messages.Message):
  r"""A OsconfigOrganizationsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsCreateRequest(_messages.Message):
  r"""A OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsCreateRequest
  object.

  Fields:
    googleCloudOsconfigV2alphaPolicyOrchestrator: A
      GoogleCloudOsconfigV2alphaPolicyOrchestrator resource to be passed as
      the request body.
    parent: Required. The parent resource name in the form of: *
      `organizations/{organization_id}/locations/global` *
      `folders/{folder_id}/locations/global` *
      `projects/{project_id_or_number}/locations/global`
    policyOrchestratorId: Required. The logical identifier of the policy
      orchestrator, with the following restrictions: * Must contain only
      lowercase letters, numbers, and hyphens. * Must start with a letter. *
      Must be between 1-63 characters. * Must end with a number or a letter. *
      Must be unique within the parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  googleCloudOsconfigV2alphaPolicyOrchestrator = _messages.MessageField('GoogleCloudOsconfigV2alphaPolicyOrchestrator', 1)
  parent = _messages.StringField(2, required=True)
  policyOrchestratorId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsDeleteRequest(_messages.Message):
  r"""A OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsDeleteRequest
  object.

  Fields:
    etag: Optional. The current etag of the policy orchestrator. If an etag is
      provided and does not match the current etag of the policy orchestrator,
      deletion will be blocked and an ABORTED error will be returned.
    name: Required. Name of the resource to be deleted.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsGetRequest(_messages.Message):
  r"""A OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsGetRequest
  object.

  Fields:
    name: Required. The resource name.
  """

  name = _messages.StringField(1, required=True)


class OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsListRequest(_messages.Message):
  r"""A OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsListRequest
  object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent resource name.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsPatchRequest(_messages.Message):
  r"""A OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsPatchRequest
  object.

  Fields:
    googleCloudOsconfigV2alphaPolicyOrchestrator: A
      GoogleCloudOsconfigV2alphaPolicyOrchestrator resource to be passed as
      the request body.
    name: Immutable. Identifier. In form of * `organizations/{organization_id}
      /locations/global/policyOrchestrators/{orchestrator_id}` * `folders/{fol
      der_id}/locations/global/policyOrchestrators/{orchestrator_id}` * `proje
      cts/{project_id_or_number}/locations/global/policyOrchestrators/{orchest
      rator_id}`
    updateMask: Optional. The list of fields to merge into the existing policy
      orchestrator. A special ["*"] field mask can be used to simply replace
      the entire resource. Otherwise, for all paths referenced in the mask,
      following merge rules are used: * output only fields are ignored, *
      primitive fields are replaced, * repeated fields are replaced, * map
      fields are merged key by key, * message fields are cleared if not set in
      the request, otherwise they are merged recursively (in particular -
      message fields set to an empty message has no side effects) If field
      mask (or its paths) is not specified, it is automatically inferred from
      the request using following rules: * primitive fields are listed, if set
      to a non-default value (as there is no way to distinguish between
      default and unset value), * map and repeated fields are listed, *
      `google.protobuf.Any` fields are listed, * other message fields are
      traversed recursively. Note: implicit mask does not allow clearing
      fields.
  """

  googleCloudOsconfigV2alphaPolicyOrchestrator = _messages.MessageField('GoogleCloudOsconfigV2alphaPolicyOrchestrator', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsSetUpConfigRequest(_messages.Message):
  r"""A
  OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsSetUpConfigRequest
  object.

  Fields:
    googleCloudOsconfigInternalV2alphaSetUpPolicyOrchestratorConfigRequest: A
      GoogleCloudOsconfigInternalV2alphaSetUpPolicyOrchestratorConfigRequest
      resource to be passed as the request body.
    parent: Required. Parent for which the configuration is set up. Could be
      in form of: * `organizations/{organization_id}/locations/global` *
      `folders/{folder_id}/locations/global` *
      `projects/{project_id_or_number}/locations/global`
  """

  googleCloudOsconfigInternalV2alphaSetUpPolicyOrchestratorConfigRequest = _messages.MessageField('GoogleCloudOsconfigInternalV2alphaSetUpPolicyOrchestratorConfigRequest', 1)
  parent = _messages.StringField(2, required=True)


class OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsValidateConfigRequest(_messages.Message):
  r"""A
  OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsValidateConfigRequest
  object.

  Fields:
    parent: Required. Parent for which the configuration is validated. Could
      be in form of: * `organizations/{organization_id}/locations/global` *
      `folders/{folder_id}/locations/global` *
      `projects/{project_id_or_number}/locations/global`
  """

  parent = _messages.StringField(1, required=True)


class OsconfigOrganizationsLocationsListRequest(_messages.Message):
  r"""A OsconfigOrganizationsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  includeUnrevealedLocations = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)


class OsconfigOrganizationsLocationsOperationsCancelRequest(_messages.Message):
  r"""A OsconfigOrganizationsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class OsconfigOrganizationsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A OsconfigOrganizationsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class OsconfigOrganizationsLocationsOperationsGetRequest(_messages.Message):
  r"""A OsconfigOrganizationsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class OsconfigOrganizationsLocationsOperationsListRequest(_messages.Message):
  r"""A OsconfigOrganizationsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class OsconfigProjectsLocationsAgentPolicyAssignmentsCreateRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsAgentPolicyAssignmentsCreateRequest object.

  Fields:
    agentPolicyAssignmentId: Required. Id of the requesting object If auto-
      generating Id server-side, remove this field and
      agent_policy_assignment_id from the method_signature of Create RPC
    googleCloudOsconfigV2alphaAgentPolicyAssignment: A
      GoogleCloudOsconfigV2alphaAgentPolicyAssignment resource to be passed as
      the request body.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  agentPolicyAssignmentId = _messages.StringField(1)
  googleCloudOsconfigV2alphaAgentPolicyAssignment = _messages.MessageField('GoogleCloudOsconfigV2alphaAgentPolicyAssignment', 2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class OsconfigProjectsLocationsAgentPolicyAssignmentsDeleteRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsAgentPolicyAssignmentsDeleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class OsconfigProjectsLocationsAgentPolicyAssignmentsGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsAgentPolicyAssignmentsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsAgentPolicyAssignmentsListRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsAgentPolicyAssignmentsListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListAgentPolicyAssignmentsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class OsconfigProjectsLocationsAgentPolicyAssignmentsPatchRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsAgentPolicyAssignmentsPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, updating an agent policy
      assignment that does not exist will result in a new agent policy
      assignment being created. In this situation, `update_mask` is ignored.
    googleCloudOsconfigV2alphaAgentPolicyAssignment: A
      GoogleCloudOsconfigV2alphaAgentPolicyAssignment resource to be passed as
      the request body.
    name: Immutable. Identifier. Name of resource, in format: `projects/{proje
      ct_number}/locations/{location}/agentPolicyAssignments/{agent_policy_ass
      ignment_id}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the AgentPolicyAssignment resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  allowMissing = _messages.BooleanField(1)
  googleCloudOsconfigV2alphaAgentPolicyAssignment = _messages.MessageField('GoogleCloudOsconfigV2alphaAgentPolicyAssignment', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)


class OsconfigProjectsLocationsGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsGlobalPolicyOrchestratorsCreateRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsGlobalPolicyOrchestratorsCreateRequest
  object.

  Fields:
    googleCloudOsconfigV2alphaPolicyOrchestrator: A
      GoogleCloudOsconfigV2alphaPolicyOrchestrator resource to be passed as
      the request body.
    parent: Required. The parent resource name in the form of: *
      `organizations/{organization_id}/locations/global` *
      `folders/{folder_id}/locations/global` *
      `projects/{project_id_or_number}/locations/global`
    policyOrchestratorId: Required. The logical identifier of the policy
      orchestrator, with the following restrictions: * Must contain only
      lowercase letters, numbers, and hyphens. * Must start with a letter. *
      Must be between 1-63 characters. * Must end with a number or a letter. *
      Must be unique within the parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  googleCloudOsconfigV2alphaPolicyOrchestrator = _messages.MessageField('GoogleCloudOsconfigV2alphaPolicyOrchestrator', 1)
  parent = _messages.StringField(2, required=True)
  policyOrchestratorId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class OsconfigProjectsLocationsGlobalPolicyOrchestratorsDeleteRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsGlobalPolicyOrchestratorsDeleteRequest
  object.

  Fields:
    etag: Optional. The current etag of the policy orchestrator. If an etag is
      provided and does not match the current etag of the policy orchestrator,
      deletion will be blocked and an ABORTED error will be returned.
    name: Required. Name of the resource to be deleted.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class OsconfigProjectsLocationsGlobalPolicyOrchestratorsGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsGlobalPolicyOrchestratorsGetRequest object.

  Fields:
    name: Required. The resource name.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsGlobalPolicyOrchestratorsListRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsGlobalPolicyOrchestratorsListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent resource name.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class OsconfigProjectsLocationsGlobalPolicyOrchestratorsPatchRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsGlobalPolicyOrchestratorsPatchRequest object.

  Fields:
    googleCloudOsconfigV2alphaPolicyOrchestrator: A
      GoogleCloudOsconfigV2alphaPolicyOrchestrator resource to be passed as
      the request body.
    name: Immutable. Identifier. In form of * `organizations/{organization_id}
      /locations/global/policyOrchestrators/{orchestrator_id}` * `folders/{fol
      der_id}/locations/global/policyOrchestrators/{orchestrator_id}` * `proje
      cts/{project_id_or_number}/locations/global/policyOrchestrators/{orchest
      rator_id}`
    updateMask: Optional. The list of fields to merge into the existing policy
      orchestrator. A special ["*"] field mask can be used to simply replace
      the entire resource. Otherwise, for all paths referenced in the mask,
      following merge rules are used: * output only fields are ignored, *
      primitive fields are replaced, * repeated fields are replaced, * map
      fields are merged key by key, * message fields are cleared if not set in
      the request, otherwise they are merged recursively (in particular -
      message fields set to an empty message has no side effects) If field
      mask (or its paths) is not specified, it is automatically inferred from
      the request using following rules: * primitive fields are listed, if set
      to a non-default value (as there is no way to distinguish between
      default and unset value), * map and repeated fields are listed, *
      `google.protobuf.Any` fields are listed, * other message fields are
      traversed recursively. Note: implicit mask does not allow clearing
      fields.
  """

  googleCloudOsconfigV2alphaPolicyOrchestrator = _messages.MessageField('GoogleCloudOsconfigV2alphaPolicyOrchestrator', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class OsconfigProjectsLocationsGlobalPolicyOrchestratorsSetUpConfigRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsGlobalPolicyOrchestratorsSetUpConfigRequest
  object.

  Fields:
    googleCloudOsconfigInternalV2alphaSetUpPolicyOrchestratorConfigRequest: A
      GoogleCloudOsconfigInternalV2alphaSetUpPolicyOrchestratorConfigRequest
      resource to be passed as the request body.
    parent: Required. Parent for which the configuration is set up. Could be
      in form of: * `organizations/{organization_id}/locations/global` *
      `folders/{folder_id}/locations/global` *
      `projects/{project_id_or_number}/locations/global`
  """

  googleCloudOsconfigInternalV2alphaSetUpPolicyOrchestratorConfigRequest = _messages.MessageField('GoogleCloudOsconfigInternalV2alphaSetUpPolicyOrchestratorConfigRequest', 1)
  parent = _messages.StringField(2, required=True)


class OsconfigProjectsLocationsGlobalPolicyOrchestratorsValidateConfigRequest(_messages.Message):
  r"""A
  OsconfigProjectsLocationsGlobalPolicyOrchestratorsValidateConfigRequest
  object.

  Fields:
    parent: Required. Parent for which the configuration is validated. Could
      be in form of: * `organizations/{organization_id}/locations/global` *
      `folders/{folder_id}/locations/global` *
      `projects/{project_id_or_number}/locations/global`
  """

  parent = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsListRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  includeUnrevealedLocations = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)


class OsconfigProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class OsconfigProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StatusProto(_messages.Message):
  r"""Wire-format for a Status object

  Fields:
    canonicalCode: copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional int32 canonical_code = 6;
    code: Numeric code drawn from the space specified below. Often, this is
      the canonical error space, and code is drawn from
      google3/util/task/codes.proto copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional int32 code = 1;
    message: Detail message copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional string message = 3;
    messageSet: message_set associates an arbitrary proto message with the
      status. copybara:strip_begin(b/383363683) copybara:strip_end_and_replace
      optional proto2.bridge.MessageSet message_set = 5;
    space: copybara:strip_begin(b/383363683) Space to which this status
      belongs copybara:strip_end_and_replace optional string space = 2; //
      Space to which this status belongs
  """

  canonicalCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  code = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  message = _messages.StringField(3)
  messageSet = _messages.MessageField('MessageSet', 4)
  space = _messages.StringField(5)


encoding.AddCustomJsonFieldMapping(
    OSPolicyResource, 'exec_', 'exec')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
