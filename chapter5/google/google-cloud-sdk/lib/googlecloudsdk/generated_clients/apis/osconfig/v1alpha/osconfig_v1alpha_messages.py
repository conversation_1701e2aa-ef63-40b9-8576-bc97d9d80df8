"""Generated message classes for osconfig version v1alpha.

OS management tools that can be used for patch management, patch compliance,
and configuration management on VM instances.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'osconfig'


class CVSSv3(_messages.Message):
  r"""Common Vulnerability Scoring System version 3. For details, see
  https://www.first.org/cvss/specification-document

  Enums:
    AttackComplexityValueValuesEnum: This metric describes the conditions
      beyond the attacker's control that must exist in order to exploit the
      vulnerability.
    AttackVectorValueValuesEnum: This metric reflects the context by which
      vulnerability exploitation is possible.
    AvailabilityImpactValueValuesEnum: This metric measures the impact to the
      availability of the impacted component resulting from a successfully
      exploited vulnerability.
    ConfidentialityImpactValueValuesEnum: This metric measures the impact to
      the confidentiality of the information resources managed by a software
      component due to a successfully exploited vulnerability.
    IntegrityImpactValueValuesEnum: This metric measures the impact to
      integrity of a successfully exploited vulnerability.
    PrivilegesRequiredValueValuesEnum: This metric describes the level of
      privileges an attacker must possess before successfully exploiting the
      vulnerability.
    ScopeValueValuesEnum: The Scope metric captures whether a vulnerability in
      one vulnerable component impacts resources in components beyond its
      security scope.
    UserInteractionValueValuesEnum: This metric captures the requirement for a
      human user, other than the attacker, to participate in the successful
      compromise of the vulnerable component.

  Fields:
    attackComplexity: This metric describes the conditions beyond the
      attacker's control that must exist in order to exploit the
      vulnerability.
    attackVector: This metric reflects the context by which vulnerability
      exploitation is possible.
    availabilityImpact: This metric measures the impact to the availability of
      the impacted component resulting from a successfully exploited
      vulnerability.
    baseScore: The base score is a function of the base metric scores.
      https://www.first.org/cvss/specification-document#Base-Metrics
    confidentialityImpact: This metric measures the impact to the
      confidentiality of the information resources managed by a software
      component due to a successfully exploited vulnerability.
    exploitabilityScore: The Exploitability sub-score equation is derived from
      the Base Exploitability metrics.
      https://www.first.org/cvss/specification-document#2-1-Exploitability-
      Metrics
    impactScore: The Impact sub-score equation is derived from the Base Impact
      metrics.
    integrityImpact: This metric measures the impact to integrity of a
      successfully exploited vulnerability.
    privilegesRequired: This metric describes the level of privileges an
      attacker must possess before successfully exploiting the vulnerability.
    scope: The Scope metric captures whether a vulnerability in one vulnerable
      component impacts resources in components beyond its security scope.
    userInteraction: This metric captures the requirement for a human user,
      other than the attacker, to participate in the successful compromise of
      the vulnerable component.
  """

  class AttackComplexityValueValuesEnum(_messages.Enum):
    r"""This metric describes the conditions beyond the attacker's control
    that must exist in order to exploit the vulnerability.

    Values:
      ATTACK_COMPLEXITY_UNSPECIFIED: Invalid value.
      ATTACK_COMPLEXITY_LOW: Specialized access conditions or extenuating
        circumstances do not exist. An attacker can expect repeatable success
        when attacking the vulnerable component.
      ATTACK_COMPLEXITY_HIGH: A successful attack depends on conditions beyond
        the attacker's control. That is, a successful attack cannot be
        accomplished at will, but requires the attacker to invest in some
        measurable amount of effort in preparation or execution against the
        vulnerable component before a successful attack can be expected.
    """
    ATTACK_COMPLEXITY_UNSPECIFIED = 0
    ATTACK_COMPLEXITY_LOW = 1
    ATTACK_COMPLEXITY_HIGH = 2

  class AttackVectorValueValuesEnum(_messages.Enum):
    r"""This metric reflects the context by which vulnerability exploitation
    is possible.

    Values:
      ATTACK_VECTOR_UNSPECIFIED: Invalid value.
      ATTACK_VECTOR_NETWORK: The vulnerable component is bound to the network
        stack and the set of possible attackers extends beyond the other
        options listed below, up to and including the entire Internet.
      ATTACK_VECTOR_ADJACENT: The vulnerable component is bound to the network
        stack, but the attack is limited at the protocol level to a logically
        adjacent topology.
      ATTACK_VECTOR_LOCAL: The vulnerable component is not bound to the
        network stack and the attacker's path is via read/write/execute
        capabilities.
      ATTACK_VECTOR_PHYSICAL: The attack requires the attacker to physically
        touch or manipulate the vulnerable component.
    """
    ATTACK_VECTOR_UNSPECIFIED = 0
    ATTACK_VECTOR_NETWORK = 1
    ATTACK_VECTOR_ADJACENT = 2
    ATTACK_VECTOR_LOCAL = 3
    ATTACK_VECTOR_PHYSICAL = 4

  class AvailabilityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to the availability of the impacted
    component resulting from a successfully exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class ConfidentialityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to the confidentiality of the
    information resources managed by a software component due to a
    successfully exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class IntegrityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to integrity of a successfully
    exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class PrivilegesRequiredValueValuesEnum(_messages.Enum):
    r"""This metric describes the level of privileges an attacker must possess
    before successfully exploiting the vulnerability.

    Values:
      PRIVILEGES_REQUIRED_UNSPECIFIED: Invalid value.
      PRIVILEGES_REQUIRED_NONE: The attacker is unauthorized prior to attack,
        and therefore does not require any access to settings or files of the
        vulnerable system to carry out an attack.
      PRIVILEGES_REQUIRED_LOW: The attacker requires privileges that provide
        basic user capabilities that could normally affect only settings and
        files owned by a user. Alternatively, an attacker with Low privileges
        has the ability to access only non-sensitive resources.
      PRIVILEGES_REQUIRED_HIGH: The attacker requires privileges that provide
        significant (e.g., administrative) control over the vulnerable
        component allowing access to component-wide settings and files.
    """
    PRIVILEGES_REQUIRED_UNSPECIFIED = 0
    PRIVILEGES_REQUIRED_NONE = 1
    PRIVILEGES_REQUIRED_LOW = 2
    PRIVILEGES_REQUIRED_HIGH = 3

  class ScopeValueValuesEnum(_messages.Enum):
    r"""The Scope metric captures whether a vulnerability in one vulnerable
    component impacts resources in components beyond its security scope.

    Values:
      SCOPE_UNSPECIFIED: Invalid value.
      SCOPE_UNCHANGED: An exploited vulnerability can only affect resources
        managed by the same security authority.
      SCOPE_CHANGED: An exploited vulnerability can affect resources beyond
        the security scope managed by the security authority of the vulnerable
        component.
    """
    SCOPE_UNSPECIFIED = 0
    SCOPE_UNCHANGED = 1
    SCOPE_CHANGED = 2

  class UserInteractionValueValuesEnum(_messages.Enum):
    r"""This metric captures the requirement for a human user, other than the
    attacker, to participate in the successful compromise of the vulnerable
    component.

    Values:
      USER_INTERACTION_UNSPECIFIED: Invalid value.
      USER_INTERACTION_NONE: The vulnerable system can be exploited without
        interaction from any user.
      USER_INTERACTION_REQUIRED: Successful exploitation of this vulnerability
        requires a user to take some action before the vulnerability can be
        exploited.
    """
    USER_INTERACTION_UNSPECIFIED = 0
    USER_INTERACTION_NONE = 1
    USER_INTERACTION_REQUIRED = 2

  attackComplexity = _messages.EnumField('AttackComplexityValueValuesEnum', 1)
  attackVector = _messages.EnumField('AttackVectorValueValuesEnum', 2)
  availabilityImpact = _messages.EnumField('AvailabilityImpactValueValuesEnum', 3)
  baseScore = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  confidentialityImpact = _messages.EnumField('ConfidentialityImpactValueValuesEnum', 5)
  exploitabilityScore = _messages.FloatField(6, variant=_messages.Variant.FLOAT)
  impactScore = _messages.FloatField(7, variant=_messages.Variant.FLOAT)
  integrityImpact = _messages.EnumField('IntegrityImpactValueValuesEnum', 8)
  privilegesRequired = _messages.EnumField('PrivilegesRequiredValueValuesEnum', 9)
  scope = _messages.EnumField('ScopeValueValuesEnum', 10)
  userInteraction = _messages.EnumField('UserInteractionValueValuesEnum', 11)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Date(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class FixedOrPercent(_messages.Message):
  r"""Message encapsulating a value that can be either absolute ("fixed") or
  relative ("percent") to a value.

  Fields:
    fixed: Specifies a fixed value.
    percent: Specifies the relative value defined as a percentage, which will
      be multiplied by a reference value.
  """

  fixed = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  percent = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudOsconfigV1OSPolicyAssignmentOperationMetadata(_messages.Message):
  r"""OS policy assignment operation metadata provided by OS policy assignment
  API methods that return long running operations.

  Enums:
    ApiMethodValueValuesEnum: The OS policy assignment API method.
    RolloutStateValueValuesEnum: State of the rollout

  Fields:
    apiMethod: The OS policy assignment API method.
    osPolicyAssignment: Reference to the `OSPolicyAssignment` API resource.
      Format: `projects/{project_number}/locations/{location}/osPolicyAssignme
      nts/{os_policy_assignment_id@revision_id}`
    rolloutStartTime: Rollout start time
    rolloutState: State of the rollout
    rolloutUpdateTime: Rollout update time
  """

  class ApiMethodValueValuesEnum(_messages.Enum):
    r"""The OS policy assignment API method.

    Values:
      API_METHOD_UNSPECIFIED: Invalid value
      CREATE: Create OS policy assignment API method
      UPDATE: Update OS policy assignment API method
      DELETE: Delete OS policy assignment API method
    """
    API_METHOD_UNSPECIFIED = 0
    CREATE = 1
    UPDATE = 2
    DELETE = 3

  class RolloutStateValueValuesEnum(_messages.Enum):
    r"""State of the rollout

    Values:
      ROLLOUT_STATE_UNSPECIFIED: Invalid value
      IN_PROGRESS: The rollout is in progress.
      CANCELLING: The rollout is being cancelled.
      CANCELLED: The rollout is cancelled.
      SUCCEEDED: The rollout has completed successfully.
    """
    ROLLOUT_STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    CANCELLING = 2
    CANCELLED = 3
    SUCCEEDED = 4

  apiMethod = _messages.EnumField('ApiMethodValueValuesEnum', 1)
  osPolicyAssignment = _messages.StringField(2)
  rolloutStartTime = _messages.StringField(3)
  rolloutState = _messages.EnumField('RolloutStateValueValuesEnum', 4)
  rolloutUpdateTime = _messages.StringField(5)


class GoogleCloudOsconfigV2OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudOsconfigV2betaOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class InstanceOSPoliciesCompliance(_messages.Message):
  r"""This API resource represents the OS policies compliance data for a
  Compute Engine virtual machine (VM) instance at a given point in time. A
  Compute Engine VM can have multiple OS policy assignments, and each
  assignment can have multiple OS policies. As a result, multiple OS policies
  could be applied to a single VM. You can use this API resource to determine
  both the compliance state of your VM as well as the compliance state of an
  individual OS policy. For more information, see [View
  compliance](https://cloud.google.com/compute/docs/os-configuration-
  management/view-compliance).

  Enums:
    StateValueValuesEnum: Output only. Compliance state of the VM.

  Fields:
    detailedState: Output only. Detailed compliance state of the VM. This
      field is populated only when compliance state is `UNKNOWN`. It may
      contain one of the following values: * `no-compliance-data`: Compliance
      data is not available for this VM. * `no-agent-detected`: OS Config
      agent is not detected for this VM. * `config-not-supported-by-agent`:
      The version of the OS Config agent running on this VM does not support
      configuration management. * `inactive`: VM is not running. * `internal-
      service-errors`: There were internal service errors encountered while
      enforcing compliance. * `agent-errors`: OS config agent encountered
      errors while enforcing compliance.
    detailedStateReason: Output only. The reason for the `detailed_state` of
      the VM (if any).
    instance: Output only. The Compute Engine VM instance name.
    lastComplianceCheckTime: Output only. Timestamp of the last compliance
      check for the VM.
    lastComplianceRunId: Output only. Unique identifier for the last
      compliance run. This id will be logged by the OS config agent during a
      compliance run and can be used for debugging and tracing purpose.
    name: Output only. The `InstanceOSPoliciesCompliance` API resource name.
      Format: `projects/{project_number}/locations/{location}/instanceOSPolici
      esCompliances/{instance_id}`
    osPolicyCompliances: Output only. Compliance data for each `OSPolicy` that
      is applied to the VM.
    state: Output only. Compliance state of the VM.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Compliance state of the VM.

    Values:
      OS_POLICY_COMPLIANCE_STATE_UNSPECIFIED: Default value. This value is
        unused.
      COMPLIANT: Compliant state.
      NON_COMPLIANT: Non-compliant state
      UNKNOWN: Unknown compliance state.
      NO_OS_POLICIES_APPLICABLE: No applicable OS policies were found for the
        instance. This state is only applicable to the instance.
    """
    OS_POLICY_COMPLIANCE_STATE_UNSPECIFIED = 0
    COMPLIANT = 1
    NON_COMPLIANT = 2
    UNKNOWN = 3
    NO_OS_POLICIES_APPLICABLE = 4

  detailedState = _messages.StringField(1)
  detailedStateReason = _messages.StringField(2)
  instance = _messages.StringField(3)
  lastComplianceCheckTime = _messages.StringField(4)
  lastComplianceRunId = _messages.StringField(5)
  name = _messages.StringField(6)
  osPolicyCompliances = _messages.MessageField('InstanceOSPoliciesComplianceOSPolicyCompliance', 7, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 8)


class InstanceOSPoliciesComplianceOSPolicyCompliance(_messages.Message):
  r"""Compliance data for an OS policy

  Enums:
    StateValueValuesEnum: Compliance state of the OS policy.

  Fields:
    osPolicyAssignment: Reference to the `OSPolicyAssignment` API resource
      that the `OSPolicy` belongs to. Format: `projects/{project_number}/locat
      ions/{location}/osPolicyAssignments/{os_policy_assignment_id@revision_id
      }`
    osPolicyId: The OS policy id
    osPolicyResourceCompliances: Compliance data for each `OSPolicyResource`
      that is applied to the VM.
    state: Compliance state of the OS policy.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Compliance state of the OS policy.

    Values:
      OS_POLICY_COMPLIANCE_STATE_UNSPECIFIED: Default value. This value is
        unused.
      COMPLIANT: Compliant state.
      NON_COMPLIANT: Non-compliant state
      UNKNOWN: Unknown compliance state.
      NO_OS_POLICIES_APPLICABLE: No applicable OS policies were found for the
        instance. This state is only applicable to the instance.
    """
    OS_POLICY_COMPLIANCE_STATE_UNSPECIFIED = 0
    COMPLIANT = 1
    NON_COMPLIANT = 2
    UNKNOWN = 3
    NO_OS_POLICIES_APPLICABLE = 4

  osPolicyAssignment = _messages.StringField(1)
  osPolicyId = _messages.StringField(2)
  osPolicyResourceCompliances = _messages.MessageField('OSPolicyResourceCompliance', 3, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class Inventory(_messages.Message):
  r"""This API resource represents the available inventory data for a Compute
  Engine virtual machine (VM) instance at a given point in time. You can use
  this API resource to determine the inventory data of your VM. For more
  information, see [Information provided by OS inventory
  management](https://cloud.google.com/compute/docs/instances/os-inventory-
  management#data-collected).

  Messages:
    ItemsValue: Output only. Inventory items related to the VM keyed by an
      opaque unique identifier for each inventory item. The identifier is
      unique to each distinct and addressable inventory item and will change,
      when there is a new package version.

  Fields:
    items: Output only. Inventory items related to the VM keyed by an opaque
      unique identifier for each inventory item. The identifier is unique to
      each distinct and addressable inventory item and will change, when there
      is a new package version.
    name: Output only. The `Inventory` API resource name. Format: `projects/{p
      roject_number}/locations/{location}/instances/{instance_id}/inventory`
    osInfo: Output only. Base level operating system information for the VM.
    updateTime: Output only. Timestamp of the last reported inventory for the
      VM.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ItemsValue(_messages.Message):
    r"""Output only. Inventory items related to the VM keyed by an opaque
    unique identifier for each inventory item. The identifier is unique to
    each distinct and addressable inventory item and will change, when there
    is a new package version.

    Messages:
      AdditionalProperty: An additional property for a ItemsValue object.

    Fields:
      additionalProperties: Additional properties of type ItemsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ItemsValue object.

      Fields:
        key: Name of the additional property.
        value: A InventoryItem attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('InventoryItem', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  items = _messages.MessageField('ItemsValue', 1)
  name = _messages.StringField(2)
  osInfo = _messages.MessageField('InventoryOsInfo', 3)
  updateTime = _messages.StringField(4)


class InventoryItem(_messages.Message):
  r"""A single piece of inventory on a VM.

  Enums:
    OriginTypeValueValuesEnum: The origin of this inventory item.
    TypeValueValuesEnum: The specific type of inventory, correlating to its
      specific details.

  Fields:
    availablePackage: Software package available to be installed on the VM
      instance.
    createTime: When this inventory item was first detected.
    id: Identifier for this item, unique across items for this VM.
    installedPackage: Software package present on the VM instance.
    originType: The origin of this inventory item.
    type: The specific type of inventory, correlating to its specific details.
    updateTime: When this inventory item was last modified.
  """

  class OriginTypeValueValuesEnum(_messages.Enum):
    r"""The origin of this inventory item.

    Values:
      ORIGIN_TYPE_UNSPECIFIED: Invalid. An origin type must be specified.
      INVENTORY_REPORT: This inventory item was discovered as the result of
        the agent reporting inventory via the reporting API.
    """
    ORIGIN_TYPE_UNSPECIFIED = 0
    INVENTORY_REPORT = 1

  class TypeValueValuesEnum(_messages.Enum):
    r"""The specific type of inventory, correlating to its specific details.

    Values:
      TYPE_UNSPECIFIED: Invalid. A type must be specified.
      INSTALLED_PACKAGE: This represents a package that is installed on the
        VM.
      AVAILABLE_PACKAGE: This represents an update that is available for a
        package.
    """
    TYPE_UNSPECIFIED = 0
    INSTALLED_PACKAGE = 1
    AVAILABLE_PACKAGE = 2

  availablePackage = _messages.MessageField('InventorySoftwarePackage', 1)
  createTime = _messages.StringField(2)
  id = _messages.StringField(3)
  installedPackage = _messages.MessageField('InventorySoftwarePackage', 4)
  originType = _messages.EnumField('OriginTypeValueValuesEnum', 5)
  type = _messages.EnumField('TypeValueValuesEnum', 6)
  updateTime = _messages.StringField(7)


class InventoryOsInfo(_messages.Message):
  r"""Operating system information for the VM.

  Fields:
    architecture: The system architecture of the operating system.
    hostname: The VM hostname.
    kernelRelease: The kernel release of the operating system.
    kernelVersion: The kernel version of the operating system.
    longName: The operating system long name. For example 'Debian GNU/Linux 9'
      or 'Microsoft Window Server 2019 Datacenter'.
    osconfigAgentVersion: The current version of the OS Config agent running
      on the VM.
    shortName: The operating system short name. For example, 'windows' or
      'debian'.
    version: The version of the operating system.
  """

  architecture = _messages.StringField(1)
  hostname = _messages.StringField(2)
  kernelRelease = _messages.StringField(3)
  kernelVersion = _messages.StringField(4)
  longName = _messages.StringField(5)
  osconfigAgentVersion = _messages.StringField(6)
  shortName = _messages.StringField(7)
  version = _messages.StringField(8)


class InventorySoftwarePackage(_messages.Message):
  r"""Software package information of the operating system.

  Fields:
    aptPackage: Details of an APT package. For details about the apt package
      manager, see https://wiki.debian.org/Apt.
    cosPackage: Details of a COS package.
    googetPackage: Details of a Googet package. For details about the googet
      package manager, see https://github.com/google/googet.
    qfePackage: Details of a Windows Quick Fix engineering package. See
      https://docs.microsoft.com/en-
      us/windows/win32/cimwin32prov/win32-quickfixengineering for info in
      Windows Quick Fix Engineering.
    windowsApplication: Details of Windows Application.
    wuaPackage: Details of a Windows Update package. See
      https://docs.microsoft.com/en-us/windows/win32/api/_wua/ for information
      about Windows Update.
    yumPackage: Yum package info. For details about the yum package manager,
      see https://access.redhat.com/documentation/en-
      us/red_hat_enterprise_linux/6/html/deployment_guide/ch-yum.
    zypperPackage: Details of a Zypper package. For details about the Zypper
      package manager, see https://en.opensuse.org/SDB:Zypper_manual.
    zypperPatch: Details of a Zypper patch. For details about the Zypper
      package manager, see https://en.opensuse.org/SDB:Zypper_manual.
  """

  aptPackage = _messages.MessageField('InventoryVersionedPackage', 1)
  cosPackage = _messages.MessageField('InventoryVersionedPackage', 2)
  googetPackage = _messages.MessageField('InventoryVersionedPackage', 3)
  qfePackage = _messages.MessageField('InventoryWindowsQuickFixEngineeringPackage', 4)
  windowsApplication = _messages.MessageField('InventoryWindowsApplication', 5)
  wuaPackage = _messages.MessageField('InventoryWindowsUpdatePackage', 6)
  yumPackage = _messages.MessageField('InventoryVersionedPackage', 7)
  zypperPackage = _messages.MessageField('InventoryVersionedPackage', 8)
  zypperPatch = _messages.MessageField('InventoryZypperPatch', 9)


class InventoryVersionedPackage(_messages.Message):
  r"""Information related to the a standard versioned package. This includes
  package info for APT, Yum, Zypper, and Googet package managers.

  Fields:
    architecture: The system architecture this package is intended for.
    packageName: The name of the package.
    version: The version of the package.
  """

  architecture = _messages.StringField(1)
  packageName = _messages.StringField(2)
  version = _messages.StringField(3)


class InventoryWindowsApplication(_messages.Message):
  r"""Contains information about a Windows application that is retrieved from
  the Windows Registry. For more information about these fields, see:
  https://docs.microsoft.com/en-us/windows/win32/msi/uninstall-registry-key

  Fields:
    displayName: The name of the application or product.
    displayVersion: The version of the product or application in string
      format.
    helpLink: The internet address for technical support.
    installDate: The last time this product received service. The value of
      this property is replaced each time a patch is applied or removed from
      the product or the command-line option is used to repair the product.
    publisher: The name of the manufacturer for the product or application.
  """

  displayName = _messages.StringField(1)
  displayVersion = _messages.StringField(2)
  helpLink = _messages.StringField(3)
  installDate = _messages.MessageField('Date', 4)
  publisher = _messages.StringField(5)


class InventoryWindowsQuickFixEngineeringPackage(_messages.Message):
  r"""Information related to a Quick Fix Engineering package. Fields are taken
  from Windows QuickFixEngineering Interface and match the source names:
  https://docs.microsoft.com/en-
  us/windows/win32/cimwin32prov/win32-quickfixengineering

  Fields:
    caption: A short textual description of the QFE update.
    description: A textual description of the QFE update.
    hotFixId: Unique identifier associated with a particular QFE update.
    installTime: Date that the QFE update was installed. Mapped from
      installed_on field.
  """

  caption = _messages.StringField(1)
  description = _messages.StringField(2)
  hotFixId = _messages.StringField(3)
  installTime = _messages.StringField(4)


class InventoryWindowsUpdatePackage(_messages.Message):
  r"""Details related to a Windows Update package. Field data and names are
  taken from Windows Update API IUpdate Interface:
  https://docs.microsoft.com/en-us/windows/win32/api/_wua/ Descriptive fields
  like title, and description are localized based on the locale of the VM
  being updated.

  Fields:
    categories: The categories that are associated with this update package.
    description: The localized description of the update package.
    kbArticleIds: A collection of Microsoft Knowledge Base article IDs that
      are associated with the update package.
    lastDeploymentChangeTime: The last published date of the update, in (UTC)
      date and time.
    moreInfoUrls: A collection of URLs that provide more information about the
      update package.
    revisionNumber: The revision number of this update package.
    supportUrl: A hyperlink to the language-specific support information for
      the update.
    title: The localized title of the update package.
    updateId: Gets the identifier of an update package. Stays the same across
      revisions.
  """

  categories = _messages.MessageField('InventoryWindowsUpdatePackageWindowsUpdateCategory', 1, repeated=True)
  description = _messages.StringField(2)
  kbArticleIds = _messages.StringField(3, repeated=True)
  lastDeploymentChangeTime = _messages.StringField(4)
  moreInfoUrls = _messages.StringField(5, repeated=True)
  revisionNumber = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  supportUrl = _messages.StringField(7)
  title = _messages.StringField(8)
  updateId = _messages.StringField(9)


class InventoryWindowsUpdatePackageWindowsUpdateCategory(_messages.Message):
  r"""Categories specified by the Windows Update.

  Fields:
    id: The identifier of the windows update category.
    name: The name of the windows update category.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2)


class InventoryZypperPatch(_messages.Message):
  r"""Details related to a Zypper Patch.

  Fields:
    category: The category of the patch.
    patchName: The name of the patch.
    severity: The severity specified for this patch
    summary: Any summary information provided about this patch.
  """

  category = _messages.StringField(1)
  patchName = _messages.StringField(2)
  severity = _messages.StringField(3)
  summary = _messages.StringField(4)


class ListInstanceOSPoliciesCompliancesResponse(_messages.Message):
  r"""A response message for listing OS policies compliance data for all
  Compute Engine VMs in the given location.

  Fields:
    instanceOsPoliciesCompliances: List of instance OS policies compliance
      objects.
    nextPageToken: The pagination token to retrieve the next page of instance
      OS policies compliance objects.
  """

  instanceOsPoliciesCompliances = _messages.MessageField('InstanceOSPoliciesCompliance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListInventoriesResponse(_messages.Message):
  r"""A response message for listing inventory data for all VMs in a specified
  location.

  Fields:
    inventories: List of inventory objects.
    nextPageToken: The pagination token to retrieve the next page of inventory
      objects.
  """

  inventories = _messages.MessageField('Inventory', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOSPolicyAssignmentReportsResponse(_messages.Message):
  r"""A response message for listing OS Policy assignment reports including
  the page of results and page token.

  Fields:
    nextPageToken: The pagination token to retrieve the next page of OS policy
      assignment report objects.
    osPolicyAssignmentReports: List of OS policy assignment reports.
  """

  nextPageToken = _messages.StringField(1)
  osPolicyAssignmentReports = _messages.MessageField('OSPolicyAssignmentReport', 2, repeated=True)


class ListOSPolicyAssignmentRevisionsResponse(_messages.Message):
  r"""A response message for listing all revisions for a OS policy assignment.

  Fields:
    nextPageToken: The pagination token to retrieve the next page of OS policy
      assignment revisions.
    osPolicyAssignments: The OS policy assignment revisions
  """

  nextPageToken = _messages.StringField(1)
  osPolicyAssignments = _messages.MessageField('OSPolicyAssignment', 2, repeated=True)


class ListOSPolicyAssignmentsResponse(_messages.Message):
  r"""A response message for listing all assignments under given parent.

  Fields:
    nextPageToken: The pagination token to retrieve the next page of OS policy
      assignments.
    osPolicyAssignments: The list of assignments
  """

  nextPageToken = _messages.StringField(1)
  osPolicyAssignments = _messages.MessageField('OSPolicyAssignment', 2, repeated=True)


class ListVulnerabilityReportsResponse(_messages.Message):
  r"""A response message for listing vulnerability reports for all VM
  instances in the specified location.

  Fields:
    nextPageToken: The pagination token to retrieve the next page of
      vulnerabilityReports object.
    vulnerabilityReports: List of vulnerabilityReport objects.
  """

  nextPageToken = _messages.StringField(1)
  vulnerabilityReports = _messages.MessageField('VulnerabilityReport', 2, repeated=True)


class MessageSet(_messages.Message):
  r"""This is proto2's version of MessageSet. DEPRECATED: DO NOT USE FOR NEW
  FIELDS. If you are using editions or proto2, please make your own extendable
  messages for your use case. If you are using proto3, please use `Any`
  instead. MessageSet was the implementation of extensions for proto1. When
  proto2 was introduced, extensions were implemented as a first-class feature.
  This schema for MessageSet was meant to be a "bridge" solution to migrate
  MessageSet-bearing messages from proto1 to proto2. This schema has been
  open-sourced only to facilitate the migration of Google products with
  MessageSet-bearing messages to open-source environments.
  """



class OSPolicy(_messages.Message):
  r"""An OS policy defines the desired state configuration for a VM.

  Enums:
    ModeValueValuesEnum: Required. Policy mode

  Fields:
    allowNoResourceGroupMatch: This flag determines the OS policy compliance
      status when none of the resource groups within the policy are applicable
      for a VM. Set this value to `true` if the policy needs to be reported as
      compliant even if the policy has nothing to validate or enforce.
    description: Policy description. Length of the description is limited to
      1024 characters.
    id: Required. The id of the OS policy with the following restrictions: *
      Must contain only lowercase letters, numbers, and hyphens. * Must start
      with a letter. * Must be between 1-63 characters. * Must end with a
      number or a letter. * Must be unique within the assignment.
    mode: Required. Policy mode
    resourceGroups: Required. List of resource groups for the policy. For a
      particular VM, resource groups are evaluated in the order specified and
      the first resource group that is applicable is selected and the rest are
      ignored. If none of the resource groups are applicable for a VM, the VM
      is considered to be non-compliant w.r.t this policy. This behavior can
      be toggled by the flag `allow_no_resource_group_match`
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Required. Policy mode

    Values:
      MODE_UNSPECIFIED: Invalid mode
      VALIDATION: This mode checks if the configuration resources in the
        policy are in their desired state. No actions are performed if they
        are not in the desired state. This mode is used for reporting
        purposes.
      ENFORCEMENT: This mode checks if the configuration resources in the
        policy are in their desired state, and if not, enforces the desired
        state.
    """
    MODE_UNSPECIFIED = 0
    VALIDATION = 1
    ENFORCEMENT = 2

  allowNoResourceGroupMatch = _messages.BooleanField(1)
  description = _messages.StringField(2)
  id = _messages.StringField(3)
  mode = _messages.EnumField('ModeValueValuesEnum', 4)
  resourceGroups = _messages.MessageField('OSPolicyResourceGroup', 5, repeated=True)


class OSPolicyAssignment(_messages.Message):
  r"""OS policy assignment is an API resource that is used to apply a set of
  OS policies to a dynamically targeted group of Compute Engine VM instances.
  An OS policy is used to define the desired state configuration for a Compute
  Engine VM instance through a set of configuration resources that provide
  capabilities such as installing or removing software packages, or executing
  a script. For more information, see [OS policy and OS policy
  assignment](https://cloud.google.com/compute/docs/os-configuration-
  management/working-with-os-policies).

  Enums:
    RolloutStateValueValuesEnum: Output only. OS policy assignment rollout
      state

  Fields:
    baseline: Output only. Indicates that this revision has been successfully
      rolled out in this zone and new VMs will be assigned OS policies from
      this revision. For a given OS policy assignment, there is only one
      revision with a value of `true` for this field.
    deleted: Output only. Indicates that this revision deletes the OS policy
      assignment.
    description: OS policy assignment description. Length of the description
      is limited to 1024 characters.
    etag: The etag for this OS policy assignment. If this is provided on
      update, it must match the server's etag.
    instanceFilter: Required. Filter to select VMs.
    name: Resource name. Format: `projects/{project_number}/locations/{locatio
      n}/osPolicyAssignments/{os_policy_assignment_id}` This field is ignored
      when you create an OS policy assignment.
    osPolicies: Required. List of OS policies to be applied to the VMs.
    reconciling: Output only. Indicates that reconciliation is in progress for
      the revision. This value is `true` when the `rollout_state` is one of: *
      IN_PROGRESS * CANCELLING
    revisionCreateTime: Output only. The timestamp that the revision was
      created.
    revisionId: Output only. The assignment revision ID A new revision is
      committed whenever a rollout is triggered for a OS policy assignment
    rollout: Required. Rollout to deploy the OS policy assignment. A rollout
      is triggered in the following situations: 1) OSPolicyAssignment is
      created. 2) OSPolicyAssignment is updated and the update contains
      changes to one of the following fields: - instance_filter - os_policies
      3) OSPolicyAssignment is deleted.
    rolloutState: Output only. OS policy assignment rollout state
    uid: Output only. Server generated unique id for the OS policy assignment
      resource.
  """

  class RolloutStateValueValuesEnum(_messages.Enum):
    r"""Output only. OS policy assignment rollout state

    Values:
      ROLLOUT_STATE_UNSPECIFIED: Invalid value
      IN_PROGRESS: The rollout is in progress.
      CANCELLING: The rollout is being cancelled.
      CANCELLED: The rollout is cancelled.
      SUCCEEDED: The rollout has completed successfully.
    """
    ROLLOUT_STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    CANCELLING = 2
    CANCELLED = 3
    SUCCEEDED = 4

  baseline = _messages.BooleanField(1)
  deleted = _messages.BooleanField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  instanceFilter = _messages.MessageField('OSPolicyAssignmentInstanceFilter', 5)
  name = _messages.StringField(6)
  osPolicies = _messages.MessageField('OSPolicy', 7, repeated=True)
  reconciling = _messages.BooleanField(8)
  revisionCreateTime = _messages.StringField(9)
  revisionId = _messages.StringField(10)
  rollout = _messages.MessageField('OSPolicyAssignmentRollout', 11)
  rolloutState = _messages.EnumField('RolloutStateValueValuesEnum', 12)
  uid = _messages.StringField(13)


class OSPolicyAssignmentInstanceFilter(_messages.Message):
  r"""Filters to select target VMs for an assignment. If more than one filter
  criteria is specified below, a VM will be selected if and only if it
  satisfies all of them.

  Fields:
    all: Target all VMs in the project. If true, no other criteria is
      permitted.
    exclusionLabels: List of label sets used for VM exclusion. If the list has
      more than one label set, the VM is excluded if any of the label sets are
      applicable for the VM.
    inclusionLabels: List of label sets used for VM inclusion. If the list has
      more than one `LabelSet`, the VM is included if any of the label sets
      are applicable for the VM.
    inventories: List of inventories to select VMs. A VM is selected if its
      inventory data matches at least one of the following inventories.
    osShortNames: Deprecated. Use the `inventories` field instead. A VM is
      selected if it's OS short name matches with any of the values provided
      in this list.
  """

  all = _messages.BooleanField(1)
  exclusionLabels = _messages.MessageField('OSPolicyAssignmentLabelSet', 2, repeated=True)
  inclusionLabels = _messages.MessageField('OSPolicyAssignmentLabelSet', 3, repeated=True)
  inventories = _messages.MessageField('OSPolicyAssignmentInstanceFilterInventory', 4, repeated=True)
  osShortNames = _messages.StringField(5, repeated=True)


class OSPolicyAssignmentInstanceFilterInventory(_messages.Message):
  r"""VM inventory details.

  Fields:
    osShortName: Required. The OS short name
    osVersion: The OS version Prefix matches are supported if asterisk(*) is
      provided as the last character. For example, to match all versions with
      a major version of `7`, specify the following value for this field `7.*`
      An empty string matches all OS versions.
  """

  osShortName = _messages.StringField(1)
  osVersion = _messages.StringField(2)


class OSPolicyAssignmentLabelSet(_messages.Message):
  r"""Message representing label set. * A label is a key value pair set for a
  VM. * A LabelSet is a set of labels. * Labels within a LabelSet are ANDed.
  In other words, a LabelSet is applicable for a VM only if it matches all the
  labels in the LabelSet. * Example: A LabelSet with 2 labels: `env=prod` and
  `type=webserver` will only be applicable for those VMs with both labels
  present.

  Messages:
    LabelsValue: Labels are identified by key/value pairs in this map. A VM
      should contain all the key/value pairs specified in this map to be
      selected.

  Fields:
    labels: Labels are identified by key/value pairs in this map. A VM should
      contain all the key/value pairs specified in this map to be selected.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels are identified by key/value pairs in this map. A VM should
    contain all the key/value pairs specified in this map to be selected.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)


class OSPolicyAssignmentOperationMetadata(_messages.Message):
  r"""OS policy assignment operation metadata provided by OS policy assignment
  API methods that return long running operations.

  Enums:
    ApiMethodValueValuesEnum: The OS policy assignment API method.
    RolloutStateValueValuesEnum: State of the rollout

  Fields:
    apiMethod: The OS policy assignment API method.
    osPolicyAssignment: Reference to the `OSPolicyAssignment` API resource.
      Format: `projects/{project_number}/locations/{location}/osPolicyAssignme
      nts/{os_policy_assignment_id@revision_id}`
    rolloutStartTime: Rollout start time
    rolloutState: State of the rollout
    rolloutUpdateTime: Rollout update time
  """

  class ApiMethodValueValuesEnum(_messages.Enum):
    r"""The OS policy assignment API method.

    Values:
      API_METHOD_UNSPECIFIED: Invalid value
      CREATE: Create OS policy assignment API method
      UPDATE: Update OS policy assignment API method
      DELETE: Delete OS policy assignment API method
    """
    API_METHOD_UNSPECIFIED = 0
    CREATE = 1
    UPDATE = 2
    DELETE = 3

  class RolloutStateValueValuesEnum(_messages.Enum):
    r"""State of the rollout

    Values:
      ROLLOUT_STATE_UNSPECIFIED: Invalid value
      IN_PROGRESS: The rollout is in progress.
      CANCELLING: The rollout is being cancelled.
      CANCELLED: The rollout is cancelled.
      SUCCEEDED: The rollout has completed successfully.
    """
    ROLLOUT_STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    CANCELLING = 2
    CANCELLED = 3
    SUCCEEDED = 4

  apiMethod = _messages.EnumField('ApiMethodValueValuesEnum', 1)
  osPolicyAssignment = _messages.StringField(2)
  rolloutStartTime = _messages.StringField(3)
  rolloutState = _messages.EnumField('RolloutStateValueValuesEnum', 4)
  rolloutUpdateTime = _messages.StringField(5)


class OSPolicyAssignmentReport(_messages.Message):
  r"""A report of the OS policy assignment status for a given instance.

  Fields:
    instance: The Compute Engine VM instance name.
    lastRunId: Unique identifier of the last attempted run to apply the OS
      policies associated with this assignment on the VM. This ID is logged by
      the OS Config agent while applying the OS policies associated with this
      assignment on the VM. NOTE: If the service is unable to successfully
      connect to the agent for this run, then this id will not be available in
      the agent logs.
    name: The `OSPolicyAssignmentReport` API resource name. Format: `projects/
      {project_number}/locations/{location}/instances/{instance_id}/osPolicyAs
      signments/{os_policy_assignment_id}/report`
    osPolicyAssignment: Reference to the `OSPolicyAssignment` API resource
      that the `OSPolicy` belongs to. Format: `projects/{project_number}/locat
      ions/{location}/osPolicyAssignments/{os_policy_assignment_id@revision_id
      }`
    osPolicyCompliances: Compliance data for each `OSPolicy` that is applied
      to the VM.
    updateTime: Timestamp for when the report was last generated.
  """

  instance = _messages.StringField(1)
  lastRunId = _messages.StringField(2)
  name = _messages.StringField(3)
  osPolicyAssignment = _messages.StringField(4)
  osPolicyCompliances = _messages.MessageField('OSPolicyAssignmentReportOSPolicyCompliance', 5, repeated=True)
  updateTime = _messages.StringField(6)


class OSPolicyAssignmentReportOSPolicyCompliance(_messages.Message):
  r"""Compliance data for an OS policy

  Enums:
    ComplianceStateValueValuesEnum: The compliance state of the OS policy.

  Fields:
    complianceState: The compliance state of the OS policy.
    complianceStateReason: The reason for the OS policy to be in an unknown
      compliance state. This field is always populated when `compliance_state`
      is `UNKNOWN`. If populated, the field can contain one of the following
      values: * `vm-not-running`: The VM was not running. * `os-policies-not-
      supported-by-agent`: The version of the OS Config agent running on the
      VM does not support running OS policies. * `no-agent-detected`: The OS
      Config agent is not detected for the VM. * `resource-execution-errors`:
      The OS Config agent encountered errors while executing one or more
      resources in the policy. See `os_policy_resource_compliances` for
      details. * `task-timeout`: The task sent to the agent to apply the
      policy timed out. * `unexpected-agent-state`: The OS Config agent did
      not report the final status of the task that attempted to apply the
      policy. Instead, the agent unexpectedly started working on a different
      task. This mostly happens when the agent or VM unexpectedly restarts
      while applying OS policies. * `internal-service-errors`: Internal
      service errors were encountered while attempting to apply the policy. *
      `os-policy-execution-pending`: OS policy was assigned to the given VM,
      but was not executed yet. Typically this is a transient condition that
      will go away after the next policy execution cycle.
    osPolicyId: The OS policy id
    osPolicyResourceCompliances: Compliance data for each resource within the
      policy that is applied to the VM.
  """

  class ComplianceStateValueValuesEnum(_messages.Enum):
    r"""The compliance state of the OS policy.

    Values:
      UNKNOWN: The policy is in an unknown compliance state. Refer to the
        field `compliance_state_reason` to learn the exact reason for the
        policy to be in this compliance state.
      COMPLIANT: Policy is compliant. The policy is compliant if all the
        underlying resources are also compliant.
      NON_COMPLIANT: Policy is non-compliant. The policy is non-compliant if
        one or more underlying resources are non-compliant.
    """
    UNKNOWN = 0
    COMPLIANT = 1
    NON_COMPLIANT = 2

  complianceState = _messages.EnumField('ComplianceStateValueValuesEnum', 1)
  complianceStateReason = _messages.StringField(2)
  osPolicyId = _messages.StringField(3)
  osPolicyResourceCompliances = _messages.MessageField('OSPolicyAssignmentReportOSPolicyComplianceOSPolicyResourceCompliance', 4, repeated=True)


class OSPolicyAssignmentReportOSPolicyComplianceOSPolicyResourceCompliance(_messages.Message):
  r"""Compliance data for an OS policy resource.

  Enums:
    ComplianceStateValueValuesEnum: The compliance state of the resource.

  Fields:
    complianceState: The compliance state of the resource.
    complianceStateReason: A reason for the resource to be in the given
      compliance state. This field is always populated when `compliance_state`
      is `UNKNOWN`. The following values are supported when `compliance_state
      == UNKNOWN` * `execution-errors`: Errors were encountered by the agent
      while executing the resource and the compliance state couldn't be
      determined. * `execution-skipped-by-agent`: Resource execution was
      skipped by the agent because errors were encountered while executing
      prior resources in the OS policy. * `os-policy-execution-attempt-
      failed`: The execution of the OS policy containing this resource failed
      and the compliance state couldn't be determined. * `os-policy-execution-
      pending`: OS policy that owns this resource was assigned to the given
      VM, but was not executed yet.
    configSteps: Ordered list of configuration completed by the agent for the
      OS policy resource.
    execResourceOutput: ExecResource specific output.
    osPolicyResourceId: The ID of the OS policy resource.
  """

  class ComplianceStateValueValuesEnum(_messages.Enum):
    r"""The compliance state of the resource.

    Values:
      UNKNOWN: The resource is in an unknown compliance state. To get more
        details about why the policy is in this state, review the output of
        the `compliance_state_reason` field.
      COMPLIANT: Resource is compliant.
      NON_COMPLIANT: Resource is non-compliant.
    """
    UNKNOWN = 0
    COMPLIANT = 1
    NON_COMPLIANT = 2

  complianceState = _messages.EnumField('ComplianceStateValueValuesEnum', 1)
  complianceStateReason = _messages.StringField(2)
  configSteps = _messages.MessageField('OSPolicyAssignmentReportOSPolicyComplianceOSPolicyResourceComplianceOSPolicyResourceConfigStep', 3, repeated=True)
  execResourceOutput = _messages.MessageField('OSPolicyAssignmentReportOSPolicyComplianceOSPolicyResourceComplianceExecResourceOutput', 4)
  osPolicyResourceId = _messages.StringField(5)


class OSPolicyAssignmentReportOSPolicyComplianceOSPolicyResourceComplianceExecResourceOutput(_messages.Message):
  r"""ExecResource specific output.

  Fields:
    enforcementOutput: Output from enforcement phase output file (if run).
      Output size is limited to 100K bytes.
  """

  enforcementOutput = _messages.BytesField(1)


class OSPolicyAssignmentReportOSPolicyComplianceOSPolicyResourceComplianceOSPolicyResourceConfigStep(_messages.Message):
  r"""Step performed by the OS Config agent for configuring an `OSPolicy`
  resource to its desired state.

  Enums:
    TypeValueValuesEnum: Configuration step type.

  Fields:
    errorMessage: An error message recorded during the execution of this step.
      Only populated if errors were encountered during this step execution.
    type: Configuration step type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Configuration step type.

    Values:
      TYPE_UNSPECIFIED: Default value. This value is unused.
      VALIDATION: Checks for resource conflicts such as schema errors.
      DESIRED_STATE_CHECK: Checks the current status of the desired state for
        a resource.
      DESIRED_STATE_ENFORCEMENT: Enforces the desired state for a resource
        that is not in desired state.
      DESIRED_STATE_CHECK_POST_ENFORCEMENT: Re-checks the status of the
        desired state. This check is done for a resource after the enforcement
        of all OS policies. This step is used to determine the final desired
        state status for the resource. It accounts for any resources that
        might have drifted from their desired state due to side effects from
        executing other resources.
    """
    TYPE_UNSPECIFIED = 0
    VALIDATION = 1
    DESIRED_STATE_CHECK = 2
    DESIRED_STATE_ENFORCEMENT = 3
    DESIRED_STATE_CHECK_POST_ENFORCEMENT = 4

  errorMessage = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class OSPolicyAssignmentRollout(_messages.Message):
  r"""Message to configure the rollout at the zonal level for the OS policy
  assignment.

  Fields:
    disruptionBudget: Required. The maximum number (or percentage) of VMs per
      zone to disrupt at any given moment.
    minWaitDuration: Required. This determines the minimum duration of time to
      wait after the configuration changes are applied through the current
      rollout. A VM continues to count towards the `disruption_budget` at
      least until this duration of time has passed after configuration changes
      are applied.
  """

  disruptionBudget = _messages.MessageField('FixedOrPercent', 1)
  minWaitDuration = _messages.StringField(2)


class OSPolicyInventoryFilter(_messages.Message):
  r"""Filtering criteria to select VMs based on inventory details.

  Fields:
    osShortName: Required. The OS short name
    osVersion: The OS version Prefix matches are supported if asterisk(*) is
      provided as the last character. For example, to match all versions with
      a major version of `7`, specify the following value for this field `7.*`
      An empty string matches all OS versions.
  """

  osShortName = _messages.StringField(1)
  osVersion = _messages.StringField(2)


class OSPolicyOSFilter(_messages.Message):
  r"""Filtering criteria to select VMs based on OS details.

  Fields:
    osShortName: This should match OS short name emitted by the OS inventory
      agent. An empty value matches any OS.
    osVersion: This value should match the version emitted by the OS inventory
      agent. Prefix matches are supported if asterisk(*) is provided as the
      last character. For example, to match all versions with a major version
      of `7`, specify the following value for this field `7.*`
  """

  osShortName = _messages.StringField(1)
  osVersion = _messages.StringField(2)


class OSPolicyResource(_messages.Message):
  r"""An OS policy resource is used to define the desired state configuration
  and provides a specific functionality like installing/removing packages,
  executing a script etc. The system ensures that resources are always in
  their desired state by taking necessary actions if they have drifted from
  their desired state.

  Fields:
    exec_: Exec resource
    file: File resource
    id: Required. The id of the resource with the following restrictions: *
      Must contain only lowercase letters, numbers, and hyphens. * Must start
      with a letter. * Must be between 1-63 characters. * Must end with a
      number or a letter. * Must be unique within the OS policy.
    pkg: Package resource
    repository: Package repository resource
  """

  exec_ = _messages.MessageField('OSPolicyResourceExecResource', 1)
  file = _messages.MessageField('OSPolicyResourceFileResource', 2)
  id = _messages.StringField(3)
  pkg = _messages.MessageField('OSPolicyResourcePackageResource', 4)
  repository = _messages.MessageField('OSPolicyResourceRepositoryResource', 5)


class OSPolicyResourceCompliance(_messages.Message):
  r"""Compliance data for an OS policy resource.

  Enums:
    StateValueValuesEnum: Compliance state of the OS policy resource.

  Fields:
    configSteps: Ordered list of configuration steps taken by the agent for
      the OS policy resource.
    execResourceOutput: ExecResource specific output.
    osPolicyResourceId: The id of the OS policy resource.
    state: Compliance state of the OS policy resource.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Compliance state of the OS policy resource.

    Values:
      OS_POLICY_COMPLIANCE_STATE_UNSPECIFIED: Default value. This value is
        unused.
      COMPLIANT: Compliant state.
      NON_COMPLIANT: Non-compliant state
      UNKNOWN: Unknown compliance state.
      NO_OS_POLICIES_APPLICABLE: No applicable OS policies were found for the
        instance. This state is only applicable to the instance.
    """
    OS_POLICY_COMPLIANCE_STATE_UNSPECIFIED = 0
    COMPLIANT = 1
    NON_COMPLIANT = 2
    UNKNOWN = 3
    NO_OS_POLICIES_APPLICABLE = 4

  configSteps = _messages.MessageField('OSPolicyResourceConfigStep', 1, repeated=True)
  execResourceOutput = _messages.MessageField('OSPolicyResourceComplianceExecResourceOutput', 2)
  osPolicyResourceId = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class OSPolicyResourceComplianceExecResourceOutput(_messages.Message):
  r"""ExecResource specific output.

  Fields:
    enforcementOutput: Output from Enforcement phase output file (if run).
      Output size is limited to 100K bytes.
  """

  enforcementOutput = _messages.BytesField(1)


class OSPolicyResourceConfigStep(_messages.Message):
  r"""Step performed by the OS Config agent for configuring an
  `OSPolicyResource` to its desired state.

  Enums:
    OutcomeValueValuesEnum: Outcome of the configuration step.
    TypeValueValuesEnum: Configuration step type.

  Fields:
    errorMessage: An error message recorded during the execution of this step.
      Only populated when outcome is FAILED.
    outcome: Outcome of the configuration step.
    type: Configuration step type.
  """

  class OutcomeValueValuesEnum(_messages.Enum):
    r"""Outcome of the configuration step.

    Values:
      OUTCOME_UNSPECIFIED: Default value. This value is unused.
      SUCCEEDED: The step succeeded.
      FAILED: The step failed.
    """
    OUTCOME_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""Configuration step type.

    Values:
      TYPE_UNSPECIFIED: Default value. This value is unused.
      VALIDATION: Validation to detect resource conflicts, schema errors, etc.
      DESIRED_STATE_CHECK: Check the current desired state status of the
        resource.
      DESIRED_STATE_ENFORCEMENT: Enforce the desired state for a resource that
        is not in desired state.
      DESIRED_STATE_CHECK_POST_ENFORCEMENT: Re-check desired state status for
        a resource after enforcement of all resources in the current
        configuration run. This step is used to determine the final desired
        state status for the resource. It accounts for any resources that
        might have drifted from their desired state due to side effects from
        configuring other resources during the current configuration run.
    """
    TYPE_UNSPECIFIED = 0
    VALIDATION = 1
    DESIRED_STATE_CHECK = 2
    DESIRED_STATE_ENFORCEMENT = 3
    DESIRED_STATE_CHECK_POST_ENFORCEMENT = 4

  errorMessage = _messages.StringField(1)
  outcome = _messages.EnumField('OutcomeValueValuesEnum', 2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class OSPolicyResourceExecResource(_messages.Message):
  r"""A resource that allows executing scripts on the VM. The `ExecResource`
  has 2 stages: `validate` and `enforce` and both stages accept a script as an
  argument to execute. When the `ExecResource` is applied by the agent, it
  first executes the script in the `validate` stage. The `validate` stage can
  signal that the `ExecResource` is already in the desired state by returning
  an exit code of `100`. If the `ExecResource` is not in the desired state, it
  should return an exit code of `101`. Any other exit code returned by this
  stage is considered an error. If the `ExecResource` is not in the desired
  state based on the exit code from the `validate` stage, the agent proceeds
  to execute the script from the `enforce` stage. If the `ExecResource` is
  already in the desired state, the `enforce` stage will not be run. Similar
  to `validate` stage, the `enforce` stage should return an exit code of `100`
  to indicate that the resource in now in its desired state. Any other exit
  code is considered an error. NOTE: An exit code of `100` was chosen over `0`
  (and `101` vs `1`) to have an explicit indicator of `in desired state`, `not
  in desired state` and errors. Because, for example, Powershell will always
  return an exit code of `0` unless an `exit` statement is provided in the
  script. So, for reasons of consistency and being explicit, exit codes `100`
  and `101` were chosen.

  Fields:
    enforce: What to run to bring this resource into the desired state. An
      exit code of 100 indicates "success", any other exit code indicates a
      failure running enforce.
    validate: Required. What to run to validate this resource is in the
      desired state. An exit code of 100 indicates "in desired state", and
      exit code of 101 indicates "not in desired state". Any other exit code
      indicates a failure running validate.
  """

  enforce = _messages.MessageField('OSPolicyResourceExecResourceExec', 1)
  validate = _messages.MessageField('OSPolicyResourceExecResourceExec', 2)


class OSPolicyResourceExecResourceExec(_messages.Message):
  r"""A file or script to execute.

  Enums:
    InterpreterValueValuesEnum: Required. The script interpreter to use.

  Fields:
    args: Optional arguments to pass to the source during execution.
    file: A remote or local file.
    interpreter: Required. The script interpreter to use.
    outputFilePath: Only recorded for enforce Exec. Path to an output file
      (that is created by this Exec) whose content will be recorded in
      OSPolicyResourceCompliance after a successful run. Absence or failure to
      read this file will result in this ExecResource being non-compliant.
      Output file size is limited to 100K bytes.
    script: An inline script. The size of the script is limited to 32KiB.
  """

  class InterpreterValueValuesEnum(_messages.Enum):
    r"""Required. The script interpreter to use.

    Values:
      INTERPRETER_UNSPECIFIED: Invalid value, the request will return
        validation error.
      NONE: If an interpreter is not specified, the source is executed
        directly. This execution, without an interpreter, only succeeds for
        executables and scripts that have shebang lines.
      SHELL: Indicates that the script runs with `/bin/sh` on Linux and
        `cmd.exe` on Windows.
      POWERSHELL: Indicates that the script runs with PowerShell.
    """
    INTERPRETER_UNSPECIFIED = 0
    NONE = 1
    SHELL = 2
    POWERSHELL = 3

  args = _messages.StringField(1, repeated=True)
  file = _messages.MessageField('OSPolicyResourceFile', 2)
  interpreter = _messages.EnumField('InterpreterValueValuesEnum', 3)
  outputFilePath = _messages.StringField(4)
  script = _messages.StringField(5)


class OSPolicyResourceFile(_messages.Message):
  r"""A remote or local file.

  Fields:
    allowInsecure: Defaults to false. When false, files are subject to
      validations based on the file type: Remote: A checksum must be
      specified. Cloud Storage: An object generation number must be specified.
    gcs: A Cloud Storage object.
    localPath: A local path within the VM to use.
    remote: A generic remote file.
  """

  allowInsecure = _messages.BooleanField(1)
  gcs = _messages.MessageField('OSPolicyResourceFileGcs', 2)
  localPath = _messages.StringField(3)
  remote = _messages.MessageField('OSPolicyResourceFileRemote', 4)


class OSPolicyResourceFileGcs(_messages.Message):
  r"""Specifies a file available as a Cloud Storage Object.

  Fields:
    bucket: Required. Bucket of the Cloud Storage object.
    generation: Generation number of the Cloud Storage object.
    object: Required. Name of the Cloud Storage object.
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)


class OSPolicyResourceFileRemote(_messages.Message):
  r"""Specifies a file available via some URI.

  Fields:
    sha256Checksum: SHA256 checksum of the remote file.
    uri: Required. URI from which to fetch the object. It should contain both
      the protocol and path following the format `{protocol}://{location}`.
  """

  sha256Checksum = _messages.StringField(1)
  uri = _messages.StringField(2)


class OSPolicyResourceFileResource(_messages.Message):
  r"""A resource that manages the state of a file.

  Enums:
    StateValueValuesEnum: Required. Desired state of the file.

  Fields:
    content: A a file with this content. The size of the content is limited to
      32KiB.
    file: A remote or local source.
    path: Required. The absolute path of the file within the VM.
    permissions: Consists of three octal digits which represent, in order, the
      permissions of the owner, group, and other users for the file (similarly
      to the numeric mode used in the linux chmod utility). Each digit
      represents a three bit number with the 4 bit corresponding to the read
      permissions, the 2 bit corresponds to the write bit, and the one bit
      corresponds to the execute permission. Default behavior is 755. Below
      are some examples of permissions and their associated values: read,
      write, and execute: 7 read and execute: 5 read and write: 6 read only: 4
    state: Required. Desired state of the file.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Required. Desired state of the file.

    Values:
      DESIRED_STATE_UNSPECIFIED: Unspecified is invalid.
      PRESENT: Ensure file at path is present.
      ABSENT: Ensure file at path is absent.
      CONTENTS_MATCH: Ensure the contents of the file at path matches. If the
        file does not exist it will be created.
    """
    DESIRED_STATE_UNSPECIFIED = 0
    PRESENT = 1
    ABSENT = 2
    CONTENTS_MATCH = 3

  content = _messages.StringField(1)
  file = _messages.MessageField('OSPolicyResourceFile', 2)
  path = _messages.StringField(3)
  permissions = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class OSPolicyResourceGroup(_messages.Message):
  r"""Resource groups provide a mechanism to group OS policy resources.
  Resource groups enable OS policy authors to create a single OS policy to be
  applied to VMs running different operating Systems. When the OS policy is
  applied to a target VM, the appropriate resource group within the OS policy
  is selected based on the `OSFilter` specified within the resource group.

  Fields:
    inventoryFilters: List of inventory filters for the resource group. The
      resources in this resource group are applied to the target VM if it
      satisfies at least one of the following inventory filters. For example,
      to apply this resource group to VMs running either `RHEL` or `CentOS`
      operating systems, specify 2 items for the list with following values:
      inventory_filters[0].os_short_name='rhel' and
      inventory_filters[1].os_short_name='centos' If the list is empty, this
      resource group will be applied to the target VM unconditionally.
    osFilter: Deprecated. Use the `inventory_filters` field instead. Used to
      specify the OS filter for a resource group
    resources: Required. List of resources configured for this resource group.
      The resources are executed in the exact order specified here.
  """

  inventoryFilters = _messages.MessageField('OSPolicyInventoryFilter', 1, repeated=True)
  osFilter = _messages.MessageField('OSPolicyOSFilter', 2)
  resources = _messages.MessageField('OSPolicyResource', 3, repeated=True)


class OSPolicyResourcePackageResource(_messages.Message):
  r"""A resource that manages a system package.

  Enums:
    DesiredStateValueValuesEnum: Required. The desired state the agent should
      maintain for this package.

  Fields:
    apt: A package managed by Apt.
    deb: A deb package file.
    desiredState: Required. The desired state the agent should maintain for
      this package.
    googet: A package managed by GooGet.
    msi: An MSI package.
    rpm: An rpm package file.
    yum: A package managed by YUM.
    zypper: A package managed by Zypper.
  """

  class DesiredStateValueValuesEnum(_messages.Enum):
    r"""Required. The desired state the agent should maintain for this
    package.

    Values:
      DESIRED_STATE_UNSPECIFIED: Unspecified is invalid.
      INSTALLED: Ensure that the package is installed.
      REMOVED: The agent ensures that the package is not installed and
        uninstalls it if detected.
    """
    DESIRED_STATE_UNSPECIFIED = 0
    INSTALLED = 1
    REMOVED = 2

  apt = _messages.MessageField('OSPolicyResourcePackageResourceAPT', 1)
  deb = _messages.MessageField('OSPolicyResourcePackageResourceDeb', 2)
  desiredState = _messages.EnumField('DesiredStateValueValuesEnum', 3)
  googet = _messages.MessageField('OSPolicyResourcePackageResourceGooGet', 4)
  msi = _messages.MessageField('OSPolicyResourcePackageResourceMSI', 5)
  rpm = _messages.MessageField('OSPolicyResourcePackageResourceRPM', 6)
  yum = _messages.MessageField('OSPolicyResourcePackageResourceYUM', 7)
  zypper = _messages.MessageField('OSPolicyResourcePackageResourceZypper', 8)


class OSPolicyResourcePackageResourceAPT(_messages.Message):
  r"""A package managed by APT. - install: `apt-get update && apt-get -y
  install [name]` - remove: `apt-get -y remove [name]`

  Fields:
    name: Required. Package name.
  """

  name = _messages.StringField(1)


class OSPolicyResourcePackageResourceDeb(_messages.Message):
  r"""A deb package file. dpkg packages only support INSTALLED state.

  Fields:
    pullDeps: Whether dependencies should also be installed. - install when
      false: `dpkg -i package` - install when true: `apt-get update && apt-get
      -y install package.deb`
    source: Required. A deb package.
  """

  pullDeps = _messages.BooleanField(1)
  source = _messages.MessageField('OSPolicyResourceFile', 2)


class OSPolicyResourcePackageResourceGooGet(_messages.Message):
  r"""A package managed by GooGet. - install: `googet -noconfirm install
  package` - remove: `googet -noconfirm remove package`

  Fields:
    name: Required. Package name.
  """

  name = _messages.StringField(1)


class OSPolicyResourcePackageResourceMSI(_messages.Message):
  r"""An MSI package. MSI packages only support INSTALLED state.

  Fields:
    properties: Additional properties to use during installation. This should
      be in the format of Property=Setting. Appended to the defaults of
      `ACTION=INSTALL REBOOT=ReallySuppress`.
    source: Required. The MSI package.
  """

  properties = _messages.StringField(1, repeated=True)
  source = _messages.MessageField('OSPolicyResourceFile', 2)


class OSPolicyResourcePackageResourceRPM(_messages.Message):
  r"""An RPM package file. RPM packages only support INSTALLED state.

  Fields:
    pullDeps: Whether dependencies should also be installed. - install when
      false: `rpm --upgrade --replacepkgs package.rpm` - install when true:
      `yum -y install package.rpm` or `zypper -y install package.rpm`
    source: Required. An rpm package.
  """

  pullDeps = _messages.BooleanField(1)
  source = _messages.MessageField('OSPolicyResourceFile', 2)


class OSPolicyResourcePackageResourceYUM(_messages.Message):
  r"""A package managed by YUM. - install: `yum -y install package` - remove:
  `yum -y remove package`

  Fields:
    name: Required. Package name.
  """

  name = _messages.StringField(1)


class OSPolicyResourcePackageResourceZypper(_messages.Message):
  r"""A package managed by Zypper. - install: `zypper -y install package` -
  remove: `zypper -y rm package`

  Fields:
    name: Required. Package name.
  """

  name = _messages.StringField(1)


class OSPolicyResourceRepositoryResource(_messages.Message):
  r"""A resource that manages a package repository.

  Fields:
    apt: An Apt Repository.
    goo: A Goo Repository.
    yum: A Yum Repository.
    zypper: A Zypper Repository.
  """

  apt = _messages.MessageField('OSPolicyResourceRepositoryResourceAptRepository', 1)
  goo = _messages.MessageField('OSPolicyResourceRepositoryResourceGooRepository', 2)
  yum = _messages.MessageField('OSPolicyResourceRepositoryResourceYumRepository', 3)
  zypper = _messages.MessageField('OSPolicyResourceRepositoryResourceZypperRepository', 4)


class OSPolicyResourceRepositoryResourceAptRepository(_messages.Message):
  r"""Represents a single apt package repository. These will be added to a
  repo file that will be managed at
  `/etc/apt/sources.list.d/google_osconfig.list`.

  Enums:
    ArchiveTypeValueValuesEnum: Required. Type of archive files in this
      repository.

  Fields:
    archiveType: Required. Type of archive files in this repository.
    components: Required. List of components for this repository. Must contain
      at least one item.
    distribution: Required. Distribution of this repository.
    gpgKey: URI of the key file for this repository. The agent maintains a
      keyring at `/etc/apt/trusted.gpg.d/osconfig_agent_managed.gpg`.
    uri: Required. URI for this repository.
  """

  class ArchiveTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of archive files in this repository.

    Values:
      ARCHIVE_TYPE_UNSPECIFIED: Unspecified is invalid.
      DEB: Deb indicates that the archive contains binary files.
      DEB_SRC: Deb-src indicates that the archive contains source files.
    """
    ARCHIVE_TYPE_UNSPECIFIED = 0
    DEB = 1
    DEB_SRC = 2

  archiveType = _messages.EnumField('ArchiveTypeValueValuesEnum', 1)
  components = _messages.StringField(2, repeated=True)
  distribution = _messages.StringField(3)
  gpgKey = _messages.StringField(4)
  uri = _messages.StringField(5)


class OSPolicyResourceRepositoryResourceGooRepository(_messages.Message):
  r"""Represents a Goo package repository. These are added to a repo file that
  is managed at `C:/ProgramData/GooGet/repos/google_osconfig.repo`.

  Fields:
    name: Required. The name of the repository.
    url: Required. The url of the repository.
  """

  name = _messages.StringField(1)
  url = _messages.StringField(2)


class OSPolicyResourceRepositoryResourceYumRepository(_messages.Message):
  r"""Represents a single yum package repository. These are added to a repo
  file that is managed at `/etc/yum.repos.d/google_osconfig.repo`.

  Fields:
    baseUrl: Required. The location of the repository directory.
    displayName: The display name of the repository.
    gpgKeys: URIs of GPG keys.
    id: Required. A one word, unique name for this repository. This is the
      `repo id` in the yum config file and also the `display_name` if
      `display_name` is omitted. This id is also used as the unique identifier
      when checking for resource conflicts.
  """

  baseUrl = _messages.StringField(1)
  displayName = _messages.StringField(2)
  gpgKeys = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)


class OSPolicyResourceRepositoryResourceZypperRepository(_messages.Message):
  r"""Represents a single zypper package repository. These are added to a repo
  file that is managed at `/etc/zypp/repos.d/google_osconfig.repo`.

  Fields:
    baseUrl: Required. The location of the repository directory.
    displayName: The display name of the repository.
    gpgKeys: URIs of GPG keys.
    id: Required. A one word, unique name for this repository. This is the
      `repo id` in the zypper config file and also the `display_name` if
      `display_name` is omitted. This id is also used as the unique identifier
      when checking for GuestPolicy conflicts.
  """

  baseUrl = _messages.StringField(1)
  displayName = _messages.StringField(2)
  gpgKeys = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OsconfigProjectsLocationsInstanceOSPoliciesCompliancesGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsInstanceOSPoliciesCompliancesGetRequest
  object.

  Fields:
    name: Required. API resource name for instance OS policies compliance
      resource. Format: `projects/{project}/locations/{location}/instanceOSPol
      iciesCompliances/{instance}` For `{project}`, either Compute Engine
      project-number or project-id can be provided. For `{instance}`, either
      Compute Engine VM instance-id or instance-name can be provided.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsInstanceOSPoliciesCompliancesListRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsInstanceOSPoliciesCompliancesListRequest
  object.

  Fields:
    filter: If provided, this field specifies the criteria that must be met by
      a `InstanceOSPoliciesCompliance` API resource to be included in the
      response.
    pageSize: The maximum number of results to return.
    pageToken: A pagination token returned from a previous call to
      `ListInstanceOSPoliciesCompliances` that indicates where this listing
      should continue from.
    parent: Required. The parent resource name. Format:
      `projects/{project}/locations/{location}` For `{project}`, either
      Compute Engine project-number or project-id can be provided.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class OsconfigProjectsLocationsInstancesInventoriesGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsInstancesInventoriesGetRequest object.

  Enums:
    ViewValueValuesEnum: Inventory view indicating what information should be
      included in the inventory resource. If unspecified, the default view is
      BASIC.

  Fields:
    name: Required. API resource name for inventory resource. Format:
      `projects/{project}/locations/{location}/instances/{instance}/inventory`
      For `{project}`, either `project-number` or `project-id` can be
      provided. For `{instance}`, either Compute Engine `instance-id` or
      `instance-name` can be provided.
    view: Inventory view indicating what information should be included in the
      inventory resource. If unspecified, the default view is BASIC.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Inventory view indicating what information should be included in the
    inventory resource. If unspecified, the default view is BASIC.

    Values:
      INVENTORY_VIEW_UNSPECIFIED: The default value. The API defaults to the
        BASIC view.
      BASIC: Returns the basic inventory information that includes `os_info`.
      FULL: Returns all fields.
    """
    INVENTORY_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class OsconfigProjectsLocationsInstancesInventoriesListRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsInstancesInventoriesListRequest object.

  Enums:
    ViewValueValuesEnum: Inventory view indicating what information should be
      included in the inventory resource. If unspecified, the default view is
      BASIC.

  Fields:
    filter: If provided, this field specifies the criteria that must be met by
      a `Inventory` API resource to be included in the response.
    pageSize: The maximum number of results to return.
    pageToken: A pagination token returned from a previous call to
      `ListInventories` that indicates where this listing should continue
      from.
    parent: Required. The parent resource name. Format:
      `projects/{project}/locations/{location}/instances/-` For `{project}`,
      either `project-number` or `project-id` can be provided.
    view: Inventory view indicating what information should be included in the
      inventory resource. If unspecified, the default view is BASIC.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Inventory view indicating what information should be included in the
    inventory resource. If unspecified, the default view is BASIC.

    Values:
      INVENTORY_VIEW_UNSPECIFIED: The default value. The API defaults to the
        BASIC view.
      BASIC: Returns the basic inventory information that includes `os_info`.
      FULL: Returns all fields.
    """
    INVENTORY_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class OsconfigProjectsLocationsInstancesOsPolicyAssignmentsReportsGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsInstancesOsPolicyAssignmentsReportsGetRequest
  object.

  Fields:
    name: Required. API resource name for OS policy assignment report. Format:
      `/projects/{project}/locations/{location}/instances/{instance}/osPolicyA
      ssignments/{assignment}/report` For `{project}`, either `project-number`
      or `project-id` can be provided. For `{instance_id}`, either Compute
      Engine `instance-id` or `instance-name` can be provided. For
      `{assignment_id}`, the OSPolicyAssignment id must be provided.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsInstancesOsPolicyAssignmentsReportsListRequest(_messages.Message):
  r"""A
  OsconfigProjectsLocationsInstancesOsPolicyAssignmentsReportsListRequest
  object.

  Fields:
    filter: If provided, this field specifies the criteria that must be met by
      the `OSPolicyAssignmentReport` API resource that is included in the
      response.
    pageSize: The maximum number of results to return.
    pageToken: A pagination token returned from a previous call to the
      `ListOSPolicyAssignmentReports` method that indicates where this listing
      should continue from.
    parent: Required. The parent resource name. Format: `projects/{project}/lo
      cations/{location}/instances/{instance}/osPolicyAssignments/{assignment}
      /reports` For `{project}`, either `project-number` or `project-id` can
      be provided. For `{instance}`, either `instance-name`, `instance-id`, or
      `-` can be provided. If '-' is provided, the response will include
      OSPolicyAssignmentReports for all instances in the project/location. For
      `{assignment}`, either `assignment-id` or `-` can be provided. If '-' is
      provided, the response will include OSPolicyAssignmentReports for all
      OSPolicyAssignments in the project/location. Either {instance} or
      {assignment} must be `-`. For example: `projects/{project}/locations/{lo
      cation}/instances/{instance}/osPolicyAssignments/-/reports` returns all
      reports for the instance `projects/{project}/locations/{location}/instan
      ces/-/osPolicyAssignments/{assignment-id}/reports` returns all the
      reports for the given assignment across all instances. `projects/{projec
      t}/locations/{location}/instances/-/osPolicyAssignments/-/reports`
      returns all the reports for all assignments across all instances.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class OsconfigProjectsLocationsInstancesVulnerabilityReportsGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsInstancesVulnerabilityReportsGetRequest
  object.

  Fields:
    name: Required. API resource name for vulnerability resource. Format: `pro
      jects/{project}/locations/{location}/instances/{instance}/vulnerabilityR
      eport` For `{project}`, either `project-number` or `project-id` can be
      provided. For `{instance}`, either Compute Engine `instance-id` or
      `instance-name` can be provided.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsInstancesVulnerabilityReportsListRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsInstancesVulnerabilityReportsListRequest
  object.

  Fields:
    filter: This field supports filtering by the severity level for the
      vulnerability. For a list of severity levels, see [Severity levels for
      vulnerabilities](https://cloud.google.com/container-
      analysis/docs/container-scanning-
      overview#severity_levels_for_vulnerabilities). The filter field follows
      the rules described in the [AIP-160](https://google.aip.dev/160)
      guidelines as follows: + **Filter for a specific severity type**: you
      can list reports that contain vulnerabilities that are classified as
      medium by specifying `vulnerabilities.details.severity:MEDIUM`. +
      **Filter for a range of severities** : you can list reports that have
      vulnerabilities that are classified as critical or high by specifying
      `vulnerabilities.details.severity:HIGH OR
      vulnerabilities.details.severity:CRITICAL`
    pageSize: The maximum number of results to return.
    pageToken: A pagination token returned from a previous call to
      `ListVulnerabilityReports` that indicates where this listing should
      continue from.
    parent: Required. The parent resource name. Format:
      `projects/{project}/locations/{location}/instances/-` For `{project}`,
      either `project-number` or `project-id` can be provided.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class OsconfigProjectsLocationsOsPolicyAssignmentsCreateRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsCreateRequest object.

  Fields:
    oSPolicyAssignment: A OSPolicyAssignment resource to be passed as the
      request body.
    osPolicyAssignmentId: Required. The logical name of the OS policy
      assignment in the project with the following restrictions: * Must
      contain only lowercase letters, numbers, and hyphens. * Must start with
      a letter. * Must be between 1-63 characters. * Must end with a number or
      a letter. * Must be unique within the project.
    parent: Required. The parent resource name in the form:
      projects/{project}/locations/{location}
    requestId: Optional. A unique identifier for this request. Restricted to
      36 ASCII characters. A random UUID is recommended. This request is only
      idempotent if a `request_id` is provided.
  """

  oSPolicyAssignment = _messages.MessageField('OSPolicyAssignment', 1)
  osPolicyAssignmentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class OsconfigProjectsLocationsOsPolicyAssignmentsDeleteRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsDeleteRequest object.

  Fields:
    name: Required. The name of the OS policy assignment to be deleted
    requestId: Optional. A unique identifier for this request. Restricted to
      36 ASCII characters. A random UUID is recommended. This request is only
      idempotent if a `request_id` is provided.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class OsconfigProjectsLocationsOsPolicyAssignmentsGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsGetRequest object.

  Fields:
    name: Required. The resource name of OS policy assignment. Format: `projec
      ts/{project}/locations/{location}/osPolicyAssignments/{os_policy_assignm
      ent}@{revisionId}`
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsOsPolicyAssignmentsListRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsListRequest object.

  Fields:
    pageSize: The maximum number of assignments to return.
    pageToken: A pagination token returned from a previous call to
      `ListOSPolicyAssignments` that indicates where this listing should
      continue from.
    parent: Required. The parent resource name.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OsconfigProjectsLocationsOsPolicyAssignmentsListRevisionsRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsListRevisionsRequest
  object.

  Fields:
    name: Required. The name of the OS policy assignment to list revisions
      for.
    pageSize: The maximum number of revisions to return.
    pageToken: A pagination token returned from a previous call to
      `ListOSPolicyAssignmentRevisions` that indicates where this listing
      should continue from.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class OsconfigProjectsLocationsOsPolicyAssignmentsOperationsCancelRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsOperationsCancelRequest
  object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class OsconfigProjectsLocationsOsPolicyAssignmentsOperationsGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsOperationsGetRequest
  object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsOsPolicyAssignmentsPatchRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the OS policy assignment is
      not found, a new OS policy assignment will be created. In this
      situation, `update_mask` is ignored.
    name: Resource name. Format: `projects/{project_number}/locations/{locatio
      n}/osPolicyAssignments/{os_policy_assignment_id}` This field is ignored
      when you create an OS policy assignment.
    oSPolicyAssignment: A OSPolicyAssignment resource to be passed as the
      request body.
    requestId: Optional. A unique identifier for this request. Restricted to
      36 ASCII characters. A random UUID is recommended. This request is only
      idempotent if a `request_id` is provided.
    updateMask: Optional. Field mask that controls which fields of the
      assignment should be updated.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  oSPolicyAssignment = _messages.MessageField('OSPolicyAssignment', 3)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StatusProto(_messages.Message):
  r"""Wire-format for a Status object

  Fields:
    canonicalCode: copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional int32 canonical_code = 6;
    code: Numeric code drawn from the space specified below. Often, this is
      the canonical error space, and code is drawn from
      google3/util/task/codes.proto copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional int32 code = 1;
    message: Detail message copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional string message = 3;
    messageSet: message_set associates an arbitrary proto message with the
      status. copybara:strip_begin(b/383363683) copybara:strip_end_and_replace
      optional proto2.bridge.MessageSet message_set = 5;
    space: copybara:strip_begin(b/383363683) Space to which this status
      belongs copybara:strip_end_and_replace optional string space = 2; //
      Space to which this status belongs
  """

  canonicalCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  code = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  message = _messages.StringField(3)
  messageSet = _messages.MessageField('MessageSet', 4)
  space = _messages.StringField(5)


class VulnerabilityReport(_messages.Message):
  r"""This API resource represents the vulnerability report for a specified
  Compute Engine virtual machine (VM) instance at a given point in time. For
  more information, see [Vulnerability
  reports](https://cloud.google.com/compute/docs/instances/os-inventory-
  management#vulnerability-reports).

  Fields:
    name: Output only. The `vulnerabilityReport` API resource name. Format: `p
      rojects/{project_number}/locations/{location}/instances/{instance_id}/vu
      lnerabilityReport`
    updateTime: Output only. The timestamp for when the last vulnerability
      report was generated for the VM.
    vulnerabilities: Output only. List of vulnerabilities affecting the VM.
  """

  name = _messages.StringField(1)
  updateTime = _messages.StringField(2)
  vulnerabilities = _messages.MessageField('VulnerabilityReportVulnerability', 3, repeated=True)


class VulnerabilityReportVulnerability(_messages.Message):
  r"""A vulnerability affecting the VM instance.

  Fields:
    availableInventoryItemIds: Corresponds to the `AVAILABLE_PACKAGE`
      inventory item on the VM. If the vulnerability report was not updated
      after the VM inventory update, these values might not display in VM
      inventory. If there is no available fix, the field is empty. The
      `inventory_item` value specifies the latest `SoftwarePackage` available
      to the VM that fixes the vulnerability.
    createTime: The timestamp for when the vulnerability was first detected.
    details: Contains metadata as per the upstream feed of the operating
      system and NVD.
    installedInventoryItemIds: Corresponds to the `INSTALLED_PACKAGE`
      inventory item on the VM. This field displays the inventory items
      affected by this vulnerability. If the vulnerability report was not
      updated after the VM inventory update, these values might not display in
      VM inventory. For some distros, this field may be empty.
    items: List of items affected by the vulnerability.
    updateTime: The timestamp for when the vulnerability was last modified.
  """

  availableInventoryItemIds = _messages.StringField(1, repeated=True)
  createTime = _messages.StringField(2)
  details = _messages.MessageField('VulnerabilityReportVulnerabilityDetails', 3)
  installedInventoryItemIds = _messages.StringField(4, repeated=True)
  items = _messages.MessageField('VulnerabilityReportVulnerabilityItem', 5, repeated=True)
  updateTime = _messages.StringField(6)


class VulnerabilityReportVulnerabilityDetails(_messages.Message):
  r"""Contains metadata information for the vulnerability. This information is
  collected from the upstream feed of the operating system.

  Fields:
    cve: The CVE of the vulnerability. CVE cannot be empty and the combination
      of should be unique across vulnerabilities for a VM.
    cvssV2Score: The CVSS V2 score of this vulnerability. CVSS V2 score is on
      a scale of 0 - 10 where 0 indicates low severity and 10 indicates high
      severity.
    cvssV3: The full description of the CVSSv3 for this vulnerability from
      NVD.
    description: The note or description describing the vulnerability from the
      distro.
    references: Corresponds to the references attached to the
      `VulnerabilityDetails`.
    severity: Assigned severity/impact ranking from the distro.
  """

  cve = _messages.StringField(1)
  cvssV2Score = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  cvssV3 = _messages.MessageField('CVSSv3', 3)
  description = _messages.StringField(4)
  references = _messages.MessageField('VulnerabilityReportVulnerabilityDetailsReference', 5, repeated=True)
  severity = _messages.StringField(6)


class VulnerabilityReportVulnerabilityDetailsReference(_messages.Message):
  r"""A reference for this vulnerability.

  Fields:
    source: The source of the reference e.g. NVD.
    url: The url of the reference.
  """

  source = _messages.StringField(1)
  url = _messages.StringField(2)


class VulnerabilityReportVulnerabilityItem(_messages.Message):
  r"""OS inventory item that is affected by a vulnerability or fixed as a
  result of a vulnerability.

  Fields:
    availableInventoryItemId: Corresponds to the `AVAILABLE_PACKAGE` inventory
      item on the VM. If the vulnerability report was not updated after the VM
      inventory update, these values might not display in VM inventory. If
      there is no available fix, the field is empty. The `inventory_item`
      value specifies the latest `SoftwarePackage` available to the VM that
      fixes the vulnerability.
    fixedCpeUri: The recommended [CPE
      URI](https://cpe.mitre.org/specification/) update that contains a fix
      for this vulnerability.
    installedInventoryItemId: Corresponds to the `INSTALLED_PACKAGE` inventory
      item on the VM. This field displays the inventory items affected by this
      vulnerability. If the vulnerability report was not updated after the VM
      inventory update, these values might not display in VM inventory. For
      some operating systems, this field might be empty.
    upstreamFix: The upstream OS patch, packages or KB that fixes the
      vulnerability.
  """

  availableInventoryItemId = _messages.StringField(1)
  fixedCpeUri = _messages.StringField(2)
  installedInventoryItemId = _messages.StringField(3)
  upstreamFix = _messages.StringField(4)


encoding.AddCustomJsonFieldMapping(
    OSPolicyResource, 'exec_', 'exec')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
