"""Generated client library for osconfig version v2."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.osconfig.v2 import osconfig_v2_messages as messages


class OsconfigV2(base_api.BaseApiClient):
  """Generated client library for service osconfig version v2."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://osconfig.googleapis.com/'
  MTLS_BASE_URL = 'https://osconfig.mtls.googleapis.com/'

  _PACKAGE = 'osconfig'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v2'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'OsconfigV2'
  _URL_VERSION = 'v2'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new osconfig handle."""
    url = url or self.BASE_URL
    super(OsconfigV2, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.folders_locations_global_policyOrchestrators = self.FoldersLocationsGlobalPolicyOrchestratorsService(self)
    self.folders_locations_global = self.FoldersLocationsGlobalService(self)
    self.folders_locations_operations = self.FoldersLocationsOperationsService(self)
    self.folders_locations = self.FoldersLocationsService(self)
    self.folders = self.FoldersService(self)
    self.organizations_locations_global_policyOrchestrators = self.OrganizationsLocationsGlobalPolicyOrchestratorsService(self)
    self.organizations_locations_global = self.OrganizationsLocationsGlobalService(self)
    self.organizations_locations_operations = self.OrganizationsLocationsOperationsService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_locations_global_policyOrchestrators = self.ProjectsLocationsGlobalPolicyOrchestratorsService(self)
    self.projects_locations_global = self.ProjectsLocationsGlobalService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class FoldersLocationsGlobalPolicyOrchestratorsService(base_api.BaseApiService):
    """Service class for the folders_locations_global_policyOrchestrators resource."""

    _NAME = 'folders_locations_global_policyOrchestrators'

    def __init__(self, client):
      super(OsconfigV2.FoldersLocationsGlobalPolicyOrchestratorsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new policy orchestrator under the given folder resource. `name` field of the given orchestrator are ignored and instead replaced by a product of `parent` and `policy_orchestrator_id`. Orchestrator state field might be only set to `ACTIVE`, `STOPPED` or omitted (in which case, the created resource will be in `ACTIVE` state anyway).

      Args:
        request: (OsconfigFoldersLocationsGlobalPolicyOrchestratorsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/global/policyOrchestrators',
        http_method='POST',
        method_id='osconfig.folders.locations.global.policyOrchestrators.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['policyOrchestratorId', 'requestId'],
        relative_path='v2/{+parent}/policyOrchestrators',
        request_field='googleCloudOsconfigV2PolicyOrchestrator',
        request_type_name='OsconfigFoldersLocationsGlobalPolicyOrchestratorsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing policy orchestrator resource, parented by a folder.

      Args:
        request: (OsconfigFoldersLocationsGlobalPolicyOrchestratorsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/global/policyOrchestrators/{policyOrchestratorsId}',
        http_method='DELETE',
        method_id='osconfig.folders.locations.global.policyOrchestrators.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='OsconfigFoldersLocationsGlobalPolicyOrchestratorsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves an existing policy orchestrator, parented by a folder.

      Args:
        request: (OsconfigFoldersLocationsGlobalPolicyOrchestratorsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudOsconfigV2PolicyOrchestrator) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/global/policyOrchestrators/{policyOrchestratorsId}',
        http_method='GET',
        method_id='osconfig.folders.locations.global.policyOrchestrators.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='OsconfigFoldersLocationsGlobalPolicyOrchestratorsGetRequest',
        response_type_name='GoogleCloudOsconfigV2PolicyOrchestrator',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the policy orchestrators under the given parent folder resource.

      Args:
        request: (OsconfigFoldersLocationsGlobalPolicyOrchestratorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudOsconfigV2ListPolicyOrchestratorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/global/policyOrchestrators',
        http_method='GET',
        method_id='osconfig.folders.locations.global.policyOrchestrators.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/policyOrchestrators',
        request_field='',
        request_type_name='OsconfigFoldersLocationsGlobalPolicyOrchestratorsListRequest',
        response_type_name='GoogleCloudOsconfigV2ListPolicyOrchestratorsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing policy orchestrator, parented by a folder.

      Args:
        request: (OsconfigFoldersLocationsGlobalPolicyOrchestratorsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/global/policyOrchestrators/{policyOrchestratorsId}',
        http_method='PATCH',
        method_id='osconfig.folders.locations.global.policyOrchestrators.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudOsconfigV2PolicyOrchestrator',
        request_type_name='OsconfigFoldersLocationsGlobalPolicyOrchestratorsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class FoldersLocationsGlobalService(base_api.BaseApiService):
    """Service class for the folders_locations_global resource."""

    _NAME = 'folders_locations_global'

    def __init__(self, client):
      super(OsconfigV2.FoldersLocationsGlobalService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersLocationsOperationsService(base_api.BaseApiService):
    """Service class for the folders_locations_operations resource."""

    _NAME = 'folders_locations_operations'

    def __init__(self, client):
      super(OsconfigV2.FoldersLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (OsconfigFoldersLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='osconfig.folders.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='OsconfigFoldersLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (OsconfigFoldersLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='osconfig.folders.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='OsconfigFoldersLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (OsconfigFoldersLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='osconfig.folders.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='OsconfigFoldersLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (OsconfigFoldersLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='osconfig.folders.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/operations',
        request_field='',
        request_type_name='OsconfigFoldersLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class FoldersLocationsService(base_api.BaseApiService):
    """Service class for the folders_locations resource."""

    _NAME = 'folders_locations'

    def __init__(self, client):
      super(OsconfigV2.FoldersLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(OsconfigV2.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsLocationsGlobalPolicyOrchestratorsService(base_api.BaseApiService):
    """Service class for the organizations_locations_global_policyOrchestrators resource."""

    _NAME = 'organizations_locations_global_policyOrchestrators'

    def __init__(self, client):
      super(OsconfigV2.OrganizationsLocationsGlobalPolicyOrchestratorsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new policy orchestrator under the given organizations resource. `name` field of the given orchestrator are ignored and instead replaced by a product of `parent` and `policy_orchestrator_id`. Orchestrator state field might be only set to `ACTIVE`, `STOPPED` or omitted (in which case, the created resource will be in `ACTIVE` state anyway).

      Args:
        request: (OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/global/policyOrchestrators',
        http_method='POST',
        method_id='osconfig.organizations.locations.global.policyOrchestrators.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['policyOrchestratorId', 'requestId'],
        relative_path='v2/{+parent}/policyOrchestrators',
        request_field='googleCloudOsconfigV2PolicyOrchestrator',
        request_type_name='OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing policy orchestrator resource, parented by an organization.

      Args:
        request: (OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/global/policyOrchestrators/{policyOrchestratorsId}',
        http_method='DELETE',
        method_id='osconfig.organizations.locations.global.policyOrchestrators.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves an existing policy orchestrator, parented by an organization.

      Args:
        request: (OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudOsconfigV2PolicyOrchestrator) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/global/policyOrchestrators/{policyOrchestratorsId}',
        http_method='GET',
        method_id='osconfig.organizations.locations.global.policyOrchestrators.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsGetRequest',
        response_type_name='GoogleCloudOsconfigV2PolicyOrchestrator',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the policy orchestrators under the given parent organization resource.

      Args:
        request: (OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudOsconfigV2ListPolicyOrchestratorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/global/policyOrchestrators',
        http_method='GET',
        method_id='osconfig.organizations.locations.global.policyOrchestrators.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/policyOrchestrators',
        request_field='',
        request_type_name='OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsListRequest',
        response_type_name='GoogleCloudOsconfigV2ListPolicyOrchestratorsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing policy orchestrator, parented by an organization.

      Args:
        request: (OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/global/policyOrchestrators/{policyOrchestratorsId}',
        http_method='PATCH',
        method_id='osconfig.organizations.locations.global.policyOrchestrators.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudOsconfigV2PolicyOrchestrator',
        request_type_name='OsconfigOrganizationsLocationsGlobalPolicyOrchestratorsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsLocationsGlobalService(base_api.BaseApiService):
    """Service class for the organizations_locations_global resource."""

    _NAME = 'organizations_locations_global'

    def __init__(self, client):
      super(OsconfigV2.OrganizationsLocationsGlobalService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_operations resource."""

    _NAME = 'organizations_locations_operations'

    def __init__(self, client):
      super(OsconfigV2.OrganizationsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (OsconfigOrganizationsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='osconfig.organizations.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='OsconfigOrganizationsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (OsconfigOrganizationsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='osconfig.organizations.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='OsconfigOrganizationsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (OsconfigOrganizationsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='osconfig.organizations.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='OsconfigOrganizationsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (OsconfigOrganizationsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='osconfig.organizations.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/operations',
        request_field='',
        request_type_name='OsconfigOrganizationsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(OsconfigV2.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(OsconfigV2.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsGlobalPolicyOrchestratorsService(base_api.BaseApiService):
    """Service class for the projects_locations_global_policyOrchestrators resource."""

    _NAME = 'projects_locations_global_policyOrchestrators'

    def __init__(self, client):
      super(OsconfigV2.ProjectsLocationsGlobalPolicyOrchestratorsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new policy orchestrator under the given project resource. `name` field of the given orchestrator are ignored and instead replaced by a product of `parent` and `policy_orchestrator_id`. Orchestrator state field might be only set to `ACTIVE`, `STOPPED` or omitted (in which case, the created resource will be in `ACTIVE` state anyway).

      Args:
        request: (OsconfigProjectsLocationsGlobalPolicyOrchestratorsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/global/policyOrchestrators',
        http_method='POST',
        method_id='osconfig.projects.locations.global.policyOrchestrators.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['policyOrchestratorId', 'requestId'],
        relative_path='v2/{+parent}/policyOrchestrators',
        request_field='googleCloudOsconfigV2PolicyOrchestrator',
        request_type_name='OsconfigProjectsLocationsGlobalPolicyOrchestratorsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing policy orchestrator resource, parented by a project.

      Args:
        request: (OsconfigProjectsLocationsGlobalPolicyOrchestratorsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/global/policyOrchestrators/{policyOrchestratorsId}',
        http_method='DELETE',
        method_id='osconfig.projects.locations.global.policyOrchestrators.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='OsconfigProjectsLocationsGlobalPolicyOrchestratorsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves an existing policy orchestrator, parented by a project.

      Args:
        request: (OsconfigProjectsLocationsGlobalPolicyOrchestratorsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudOsconfigV2PolicyOrchestrator) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/global/policyOrchestrators/{policyOrchestratorsId}',
        http_method='GET',
        method_id='osconfig.projects.locations.global.policyOrchestrators.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='OsconfigProjectsLocationsGlobalPolicyOrchestratorsGetRequest',
        response_type_name='GoogleCloudOsconfigV2PolicyOrchestrator',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the policy orchestrators under the given parent project resource.

      Args:
        request: (OsconfigProjectsLocationsGlobalPolicyOrchestratorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudOsconfigV2ListPolicyOrchestratorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/global/policyOrchestrators',
        http_method='GET',
        method_id='osconfig.projects.locations.global.policyOrchestrators.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/policyOrchestrators',
        request_field='',
        request_type_name='OsconfigProjectsLocationsGlobalPolicyOrchestratorsListRequest',
        response_type_name='GoogleCloudOsconfigV2ListPolicyOrchestratorsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing policy orchestrator, parented by a project.

      Args:
        request: (OsconfigProjectsLocationsGlobalPolicyOrchestratorsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/global/policyOrchestrators/{policyOrchestratorsId}',
        http_method='PATCH',
        method_id='osconfig.projects.locations.global.policyOrchestrators.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudOsconfigV2PolicyOrchestrator',
        request_type_name='OsconfigProjectsLocationsGlobalPolicyOrchestratorsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsGlobalService(base_api.BaseApiService):
    """Service class for the projects_locations_global resource."""

    _NAME = 'projects_locations_global'

    def __init__(self, client):
      super(OsconfigV2.ProjectsLocationsGlobalService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(OsconfigV2.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (OsconfigProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='osconfig.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='OsconfigProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (OsconfigProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='osconfig.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='OsconfigProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (OsconfigProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='osconfig.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='OsconfigProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (OsconfigProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='osconfig.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/operations',
        request_field='',
        request_type_name='OsconfigProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(OsconfigV2.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(OsconfigV2.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
