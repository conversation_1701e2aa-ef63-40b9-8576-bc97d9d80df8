"""Generated message classes for osconfig version v1beta.

OS management tools that can be used for patch management, patch compliance,
and configuration management on VM instances.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'osconfig'


class AptRepository(_messages.Message):
  r"""Represents a single Apt package repository. This repository is added to
  a repo file that is stored at
  `/etc/apt/sources.list.d/google_osconfig.list`.

  Enums:
    ArchiveTypeValueValuesEnum: Type of archive files in this repository. The
      default behavior is DEB.

  Fields:
    archiveType: Type of archive files in this repository. The default
      behavior is DEB.
    components: Required. List of components for this repository. Must contain
      at least one item.
    distribution: Required. Distribution of this repository.
    gpgKey: URI of the key file for this repository. The agent maintains a
      keyring at `/etc/apt/trusted.gpg.d/osconfig_agent_managed.gpg`
      containing all the keys in any applied guest policy.
    uri: Required. URI for this repository.
  """

  class ArchiveTypeValueValuesEnum(_messages.Enum):
    r"""Type of archive files in this repository. The default behavior is DEB.

    Values:
      ARCHIVE_TYPE_UNSPECIFIED: Unspecified.
      DEB: DEB indicates that the archive contains binary files.
      DEB_SRC: DEB_SRC indicates that the archive contains source files.
    """
    ARCHIVE_TYPE_UNSPECIFIED = 0
    DEB = 1
    DEB_SRC = 2

  archiveType = _messages.EnumField('ArchiveTypeValueValuesEnum', 1)
  components = _messages.StringField(2, repeated=True)
  distribution = _messages.StringField(3)
  gpgKey = _messages.StringField(4)
  uri = _messages.StringField(5)


class AptSettings(_messages.Message):
  r"""Apt patching is completed by executing `apt-get update && apt-get
  upgrade`. Additional options can be set to control how this is executed.

  Enums:
    TypeValueValuesEnum: By changing the type to DIST, the patching is
      performed using `apt-get dist-upgrade` instead.

  Fields:
    excludes: List of packages to exclude from update. These packages will be
      excluded
    exclusivePackages: An exclusive list of packages to be updated. These are
      the only packages that will be updated. If these packages are not
      installed, they will be ignored. This field cannot be specified with any
      other patch configuration fields.
    type: By changing the type to DIST, the patching is performed using `apt-
      get dist-upgrade` instead.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""By changing the type to DIST, the patching is performed using `apt-get
    dist-upgrade` instead.

    Values:
      TYPE_UNSPECIFIED: By default, upgrade will be performed.
      DIST: Runs `apt-get dist-upgrade`.
      UPGRADE: Runs `apt-get upgrade`.
    """
    TYPE_UNSPECIFIED = 0
    DIST = 1
    UPGRADE = 2

  excludes = _messages.StringField(1, repeated=True)
  exclusivePackages = _messages.StringField(2, repeated=True)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class Assignment(_messages.Message):
  r"""An assignment represents the group or groups of VM instances that the
  policy applies to. If an assignment is empty, it applies to all VM
  instances. Otherwise, the targeted VM instances must meet all the criteria
  specified. So if both labels and zones are specified, the policy applies to
  VM instances with those labels and in those zones.

  Fields:
    groupLabels: Targets instances matching at least one of these label sets.
      This allows an assignment to target disparate groups, for example
      "env=prod or env=staging".
    instanceNamePrefixes: Targets VM instances whose name starts with one of
      these prefixes. Like labels, this is another way to group VM instances
      when targeting configs, for example prefix="prod-". Only supported for
      project-level policies.
    instances: Targets any of the instances specified. Instances are specified
      by their URI in the form `zones/[ZONE]/instances/[INSTANCE_NAME]`.
      Instance targeting is uncommon and is supported to facilitate the
      management of changes by the instance or to target specific VM instances
      for development and testing. Only supported for project-level policies
      and must reference instances within this project.
    osTypes: Targets VM instances matching at least one of the following OS
      types. VM instances must match all supplied criteria for a given OsType
      to be included.
    zones: Targets instances in any of these zones. Leave empty to target
      instances in any zone. Zonal targeting is uncommon and is supported to
      facilitate the management of changes by zone.
  """

  groupLabels = _messages.MessageField('AssignmentGroupLabel', 1, repeated=True)
  instanceNamePrefixes = _messages.StringField(2, repeated=True)
  instances = _messages.StringField(3, repeated=True)
  osTypes = _messages.MessageField('AssignmentOsType', 4, repeated=True)
  zones = _messages.StringField(5, repeated=True)


class AssignmentGroupLabel(_messages.Message):
  r"""Represents a group of VM intances that can be identified as having all
  these labels, for example "env=prod and app=web".

  Messages:
    LabelsValue: Google Compute Engine instance labels that must be present
      for an instance to be included in this assignment group.

  Fields:
    labels: Google Compute Engine instance labels that must be present for an
      instance to be included in this assignment group.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Google Compute Engine instance labels that must be present for an
    instance to be included in this assignment group.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)


class AssignmentOsType(_messages.Message):
  r"""Defines the criteria for selecting VM Instances by OS type.

  Fields:
    osArchitecture: Targets VM instances with OS Inventory enabled and having
      the following OS architecture.
    osShortName: Targets VM instances with OS Inventory enabled and having the
      following OS short name, for example "debian" or "windows".
    osVersion: Targets VM instances with OS Inventory enabled and having the
      following following OS version.
  """

  osArchitecture = _messages.StringField(1)
  osShortName = _messages.StringField(2)
  osVersion = _messages.StringField(3)


class CancelPatchJobRequest(_messages.Message):
  r"""Message for canceling a patch job."""


class EffectiveGuestPolicy(_messages.Message):
  r"""The effective guest policy that applies to a VM instance.

  Fields:
    packageRepositories: List of package repository configurations assigned to
      the VM instance.
    packages: List of package configurations assigned to the VM instance.
    softwareRecipes: List of recipes assigned to the VM instance.
  """

  packageRepositories = _messages.MessageField('EffectiveGuestPolicySourcedPackageRepository', 1, repeated=True)
  packages = _messages.MessageField('EffectiveGuestPolicySourcedPackage', 2, repeated=True)
  softwareRecipes = _messages.MessageField('EffectiveGuestPolicySourcedSoftwareRecipe', 3, repeated=True)


class EffectiveGuestPolicySourcedPackage(_messages.Message):
  r"""A guest policy package including its source.

  Fields:
    package: A software package to configure on the VM instance.
    source: Name of the guest policy providing this config.
  """

  package = _messages.MessageField('Package', 1)
  source = _messages.StringField(2)


class EffectiveGuestPolicySourcedPackageRepository(_messages.Message):
  r"""A guest policy package repository including its source.

  Fields:
    packageRepository: A software package repository to configure on the VM
      instance.
    source: Name of the guest policy providing this config.
  """

  packageRepository = _messages.MessageField('PackageRepository', 1)
  source = _messages.StringField(2)


class EffectiveGuestPolicySourcedSoftwareRecipe(_messages.Message):
  r"""A guest policy recipe including its source.

  Fields:
    softwareRecipe: A software recipe to configure on the VM instance.
    source: Name of the guest policy providing this config.
  """

  softwareRecipe = _messages.MessageField('SoftwareRecipe', 1)
  source = _messages.StringField(2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ExecStep(_messages.Message):
  r"""A step that runs an executable for a PatchJob.

  Fields:
    linuxExecStepConfig: The ExecStepConfig for all Linux VMs targeted by the
      PatchJob.
    windowsExecStepConfig: The ExecStepConfig for all Windows VMs targeted by
      the PatchJob.
  """

  linuxExecStepConfig = _messages.MessageField('ExecStepConfig', 1)
  windowsExecStepConfig = _messages.MessageField('ExecStepConfig', 2)


class ExecStepConfig(_messages.Message):
  r"""Common configurations for an ExecStep.

  Enums:
    InterpreterValueValuesEnum: The script interpreter to use to run the
      script. If no interpreter is specified the script will be executed
      directly, which will likely only succeed for scripts with [shebang
      lines] (https://en.wikipedia.org/wiki/Shebang_\(Unix\)).

  Fields:
    allowedSuccessCodes: Defaults to [0]. A list of possible return values
      that the execution can return to indicate a success.
    gcsObject: A Google Cloud Storage object containing the executable.
    interpreter: The script interpreter to use to run the script. If no
      interpreter is specified the script will be executed directly, which
      will likely only succeed for scripts with [shebang lines]
      (https://en.wikipedia.org/wiki/Shebang_\(Unix\)).
    localPath: An absolute path to the executable on the VM.
  """

  class InterpreterValueValuesEnum(_messages.Enum):
    r"""The script interpreter to use to run the script. If no interpreter is
    specified the script will be executed directly, which will likely only
    succeed for scripts with [shebang lines]
    (https://en.wikipedia.org/wiki/Shebang_\(Unix\)).

    Values:
      INTERPRETER_UNSPECIFIED: If the interpreter is not specified, the value
        defaults to `NONE`.
      NONE: Indicates that the file is run as follows on each operating
        system: + For Linux VMs, the file is ran as an executable and the
        interpreter might be parsed from the [shebang
        line](https://wikipedia.org/wiki/Shebang_(Unix)) of the file. + For
        Windows VM, this value is not supported.
      SHELL: Indicates that the file is run with `/bin/sh` on Linux and `cmd`
        on Windows.
      POWERSHELL: Indicates that the file is run with PowerShell.
    """
    INTERPRETER_UNSPECIFIED = 0
    NONE = 1
    SHELL = 2
    POWERSHELL = 3

  allowedSuccessCodes = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  gcsObject = _messages.MessageField('GcsObject', 2)
  interpreter = _messages.EnumField('InterpreterValueValuesEnum', 3)
  localPath = _messages.StringField(4)


class ExecutePatchJobRequest(_messages.Message):
  r"""A request message to initiate patching across Compute Engine instances.

  Fields:
    description: Description of the patch job. Length of the description is
      limited to 1024 characters.
    displayName: Display name for this patch job. This does not have to be
      unique.
    dryRun: If this patch is a dry-run only, instances are contacted but will
      do nothing.
    duration: Duration of the patch job. After the duration ends, the patch
      job times out.
    instanceFilter: Required. Instances to patch, either explicitly or
      filtered by some criteria such as zone or labels.
    patchConfig: Patch configuration being applied. If omitted, instances are
      patched using the default configurations.
    rollout: Rollout strategy of the patch job.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  dryRun = _messages.BooleanField(3)
  duration = _messages.StringField(4)
  instanceFilter = _messages.MessageField('PatchInstanceFilter', 5)
  patchConfig = _messages.MessageField('PatchConfig', 6)
  rollout = _messages.MessageField('PatchRollout', 7)


class FixedOrPercent(_messages.Message):
  r"""Message encapsulating a value that can be either absolute ("fixed") or
  relative ("percent") to a value.

  Fields:
    fixed: Specifies a fixed value.
    percent: Specifies the relative value defined as a percentage, which will
      be multiplied by a reference value.
  """

  fixed = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  percent = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GcsObject(_messages.Message):
  r"""Google Cloud Storage object representation.

  Fields:
    bucket: Required. Bucket of the Google Cloud Storage object.
    generationNumber: Required. Generation number of the Google Cloud Storage
      object. This is used to ensure that the ExecStep specified by this
      PatchJob does not change.
    object: Required. Name of the Google Cloud Storage object.
  """

  bucket = _messages.StringField(1)
  generationNumber = _messages.IntegerField(2)
  object = _messages.StringField(3)


class GooRepository(_messages.Message):
  r"""Represents a Goo package repository. These is added to a repo file that
  is stored at C:/ProgramData/GooGet/repos/google_osconfig.repo.

  Fields:
    name: Required. The name of the repository.
    url: Required. The url of the repository.
  """

  name = _messages.StringField(1)
  url = _messages.StringField(2)


class GooSettings(_messages.Message):
  r"""Googet patching is performed by running `googet update`."""


class GoogleCloudOsconfigV1OSPolicyAssignmentOperationMetadata(_messages.Message):
  r"""OS policy assignment operation metadata provided by OS policy assignment
  API methods that return long running operations.

  Enums:
    ApiMethodValueValuesEnum: The OS policy assignment API method.
    RolloutStateValueValuesEnum: State of the rollout

  Fields:
    apiMethod: The OS policy assignment API method.
    osPolicyAssignment: Reference to the `OSPolicyAssignment` API resource.
      Format: `projects/{project_number}/locations/{location}/osPolicyAssignme
      nts/{os_policy_assignment_id@revision_id}`
    rolloutStartTime: Rollout start time
    rolloutState: State of the rollout
    rolloutUpdateTime: Rollout update time
  """

  class ApiMethodValueValuesEnum(_messages.Enum):
    r"""The OS policy assignment API method.

    Values:
      API_METHOD_UNSPECIFIED: Invalid value
      CREATE: Create OS policy assignment API method
      UPDATE: Update OS policy assignment API method
      DELETE: Delete OS policy assignment API method
    """
    API_METHOD_UNSPECIFIED = 0
    CREATE = 1
    UPDATE = 2
    DELETE = 3

  class RolloutStateValueValuesEnum(_messages.Enum):
    r"""State of the rollout

    Values:
      ROLLOUT_STATE_UNSPECIFIED: Invalid value
      IN_PROGRESS: The rollout is in progress.
      CANCELLING: The rollout is being cancelled.
      CANCELLED: The rollout is cancelled.
      SUCCEEDED: The rollout has completed successfully.
    """
    ROLLOUT_STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    CANCELLING = 2
    CANCELLED = 3
    SUCCEEDED = 4

  apiMethod = _messages.EnumField('ApiMethodValueValuesEnum', 1)
  osPolicyAssignment = _messages.StringField(2)
  rolloutStartTime = _messages.StringField(3)
  rolloutState = _messages.EnumField('RolloutStateValueValuesEnum', 4)
  rolloutUpdateTime = _messages.StringField(5)


class GoogleCloudOsconfigV2OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudOsconfigV2betaOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GuestPolicy(_messages.Message):
  r"""An OS Config resource representing a guest configuration policy. These
  policies represent the desired state for VM instance guest environments
  including packages to install or remove, package repository configurations,
  and software to install.

  Fields:
    assignment: Required. Specifies the VM instances that are assigned to this
      policy. This allows you to target sets or groups of VM instances by
      different parameters such as labels, names, OS, or zones. If left empty,
      all VM instances underneath this policy are targeted. At the same level
      in the resource hierarchy (that is within a project), the service
      prevents the creation of multiple policies that conflict with each
      other. For more information, see how the service [handles assignment
      conflicts](/compute/docs/os-config-management/create-guest-
      policy#handle-conflicts).
    createTime: Output only. Time this guest policy was created.
    description: Description of the guest policy. Length of the description is
      limited to 1024 characters.
    etag: The etag for this guest policy. If this is provided on update, it
      must match the server's etag.
    name: Required. Unique name of the resource in this project using one of
      the following forms:
      `projects/{project_number}/guestPolicies/{guest_policy_id}`.
    packageRepositories: A list of package repositories to configure on the VM
      instance. This is done before any other configs are applied so they can
      use these repos. Package repositories are only configured if the
      corresponding package manager(s) are available.
    packages: The software packages to be managed by this policy.
    recipes: A list of Recipes to install on the VM instance.
    updateTime: Output only. Last time this guest policy was updated.
  """

  assignment = _messages.MessageField('Assignment', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  name = _messages.StringField(5)
  packageRepositories = _messages.MessageField('PackageRepository', 6, repeated=True)
  packages = _messages.MessageField('Package', 7, repeated=True)
  recipes = _messages.MessageField('SoftwareRecipe', 8, repeated=True)
  updateTime = _messages.StringField(9)


class ListGuestPoliciesResponse(_messages.Message):
  r"""A response message for listing guest policies.

  Fields:
    guestPolicies: The list of GuestPolicies.
    nextPageToken: A pagination token that can be used to get the next page of
      guest policies.
  """

  guestPolicies = _messages.MessageField('GuestPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListPatchDeploymentsResponse(_messages.Message):
  r"""A response message for listing patch deployments.

  Fields:
    nextPageToken: A pagination token that can be used to get the next page of
      patch deployments.
    patchDeployments: The list of patch deployments.
  """

  nextPageToken = _messages.StringField(1)
  patchDeployments = _messages.MessageField('PatchDeployment', 2, repeated=True)


class ListPatchJobInstanceDetailsResponse(_messages.Message):
  r"""A response message for listing the instances details for a patch job.

  Fields:
    nextPageToken: A pagination token that can be used to get the next page of
      results.
    patchJobInstanceDetails: A list of instance status.
  """

  nextPageToken = _messages.StringField(1)
  patchJobInstanceDetails = _messages.MessageField('PatchJobInstanceDetails', 2, repeated=True)


class ListPatchJobsResponse(_messages.Message):
  r"""A response message for listing patch jobs.

  Fields:
    nextPageToken: A pagination token that can be used to get the next page of
      results.
    patchJobs: The list of patch jobs.
  """

  nextPageToken = _messages.StringField(1)
  patchJobs = _messages.MessageField('PatchJob', 2, repeated=True)


class LookupEffectiveGuestPolicyRequest(_messages.Message):
  r"""A request message for getting the effective guest policy assigned to the
  instance.

  Fields:
    osArchitecture: Architecture of OS running on the instance. The OS Config
      agent only provides this field for targeting if OS Inventory is enabled
      for that instance.
    osShortName: Short name of the OS running on the instance. The OS Config
      agent only provides this field for targeting if OS Inventory is enabled
      for that instance.
    osVersion: Version of the OS running on the instance. The OS Config agent
      only provides this field for targeting if OS Inventory is enabled for
      that VM instance.
  """

  osArchitecture = _messages.StringField(1)
  osShortName = _messages.StringField(2)
  osVersion = _messages.StringField(3)


class MessageSet(_messages.Message):
  r"""This is proto2's version of MessageSet. DEPRECATED: DO NOT USE FOR NEW
  FIELDS. If you are using editions or proto2, please make your own extendable
  messages for your use case. If you are using proto3, please use `Any`
  instead. MessageSet was the implementation of extensions for proto1. When
  proto2 was introduced, extensions were implemented as a first-class feature.
  This schema for MessageSet was meant to be a "bridge" solution to migrate
  MessageSet-bearing messages from proto1 to proto2. This schema has been
  open-sourced only to facilitate the migration of Google products with
  MessageSet-bearing messages to open-source environments.
  """



class MonthlySchedule(_messages.Message):
  r"""Represents a monthly schedule. An example of a valid monthly schedule is
  "on the third Tuesday of the month" or "on the 15th of the month".

  Fields:
    monthDay: Required. One day of the month. 1-31 indicates the 1st to the
      31st day. -1 indicates the last day of the month. Months without the
      target day will be skipped. For example, a schedule to run "every month
      on the 31st" will not run in February, April, June, etc.
    weekDayOfMonth: Required. Week day in a month.
  """

  monthDay = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  weekDayOfMonth = _messages.MessageField('WeekDayOfMonth', 2)


class OSPolicyAssignmentOperationMetadata(_messages.Message):
  r"""OS policy assignment operation metadata provided by OS policy assignment
  API methods that return long running operations.

  Enums:
    ApiMethodValueValuesEnum: The OS policy assignment API method.
    RolloutStateValueValuesEnum: State of the rollout

  Fields:
    apiMethod: The OS policy assignment API method.
    osPolicyAssignment: Reference to the `OSPolicyAssignment` API resource.
      Format: `projects/{project_number}/locations/{location}/osPolicyAssignme
      nts/{os_policy_assignment_id@revision_id}`
    rolloutStartTime: Rollout start time
    rolloutState: State of the rollout
    rolloutUpdateTime: Rollout update time
  """

  class ApiMethodValueValuesEnum(_messages.Enum):
    r"""The OS policy assignment API method.

    Values:
      API_METHOD_UNSPECIFIED: Invalid value
      CREATE: Create OS policy assignment API method
      UPDATE: Update OS policy assignment API method
      DELETE: Delete OS policy assignment API method
    """
    API_METHOD_UNSPECIFIED = 0
    CREATE = 1
    UPDATE = 2
    DELETE = 3

  class RolloutStateValueValuesEnum(_messages.Enum):
    r"""State of the rollout

    Values:
      ROLLOUT_STATE_UNSPECIFIED: Invalid value
      IN_PROGRESS: The rollout is in progress.
      CANCELLING: The rollout is being cancelled.
      CANCELLED: The rollout is cancelled.
      SUCCEEDED: The rollout has completed successfully.
    """
    ROLLOUT_STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    CANCELLING = 2
    CANCELLED = 3
    SUCCEEDED = 4

  apiMethod = _messages.EnumField('ApiMethodValueValuesEnum', 1)
  osPolicyAssignment = _messages.StringField(2)
  rolloutStartTime = _messages.StringField(3)
  rolloutState = _messages.EnumField('RolloutStateValueValuesEnum', 4)
  rolloutUpdateTime = _messages.StringField(5)


class OneTimeSchedule(_messages.Message):
  r"""Sets the time for a one time patch deployment. Timestamp is in
  [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.

  Fields:
    executeTime: Required. The desired patch job execution time.
  """

  executeTime = _messages.StringField(1)


class OsconfigProjectsGuestPoliciesCreateRequest(_messages.Message):
  r"""A OsconfigProjectsGuestPoliciesCreateRequest object.

  Fields:
    guestPolicy: A GuestPolicy resource to be passed as the request body.
    guestPolicyId: Required. The logical name of the guest policy in the
      project with the following restrictions: * Must contain only lowercase
      letters, numbers, and hyphens. * Must start with a letter. * Must be
      between 1-63 characters. * Must end with a number or a letter. * Must be
      unique within the project.
    parent: Required. The resource name of the parent using one of the
      following forms: `projects/{project_number}`.
  """

  guestPolicy = _messages.MessageField('GuestPolicy', 1)
  guestPolicyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OsconfigProjectsGuestPoliciesDeleteRequest(_messages.Message):
  r"""A OsconfigProjectsGuestPoliciesDeleteRequest object.

  Fields:
    name: Required. The resource name of the guest policy using one of the
      following forms:
      `projects/{project_number}/guestPolicies/{guest_policy_id}`.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsGuestPoliciesGetRequest(_messages.Message):
  r"""A OsconfigProjectsGuestPoliciesGetRequest object.

  Fields:
    name: Required. The resource name of the guest policy using one of the
      following forms:
      `projects/{project_number}/guestPolicies/{guest_policy_id}`.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsGuestPoliciesListRequest(_messages.Message):
  r"""A OsconfigProjectsGuestPoliciesListRequest object.

  Fields:
    pageSize: The maximum number of guest policies to return.
    pageToken: A pagination token returned from a previous call to
      `ListGuestPolicies` that indicates where this listing should continue
      from.
    parent: Required. The resource name of the parent using one of the
      following forms: `projects/{project_number}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OsconfigProjectsGuestPoliciesPatchRequest(_messages.Message):
  r"""A OsconfigProjectsGuestPoliciesPatchRequest object.

  Fields:
    guestPolicy: A GuestPolicy resource to be passed as the request body.
    name: Required. Unique name of the resource in this project using one of
      the following forms:
      `projects/{project_number}/guestPolicies/{guest_policy_id}`.
    updateMask: Field mask that controls which fields of the guest policy
      should be updated.
  """

  guestPolicy = _messages.MessageField('GuestPolicy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class OsconfigProjectsPatchDeploymentsCreateRequest(_messages.Message):
  r"""A OsconfigProjectsPatchDeploymentsCreateRequest object.

  Fields:
    parent: Required. The project to apply this patch deployment to in the
      form `projects/*`.
    patchDeployment: A PatchDeployment resource to be passed as the request
      body.
    patchDeploymentId: Required. A name for the patch deployment in the
      project. When creating a name the following rules apply: * Must contain
      only lowercase letters, numbers, and hyphens. * Must start with a
      letter. * Must be between 1-63 characters. * Must end with a number or a
      letter. * Must be unique within the project.
  """

  parent = _messages.StringField(1, required=True)
  patchDeployment = _messages.MessageField('PatchDeployment', 2)
  patchDeploymentId = _messages.StringField(3)


class OsconfigProjectsPatchDeploymentsDeleteRequest(_messages.Message):
  r"""A OsconfigProjectsPatchDeploymentsDeleteRequest object.

  Fields:
    name: Required. The resource name of the patch deployment in the form
      `projects/*/patchDeployments/*`.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsPatchDeploymentsGetRequest(_messages.Message):
  r"""A OsconfigProjectsPatchDeploymentsGetRequest object.

  Fields:
    name: Required. The resource name of the patch deployment in the form
      `projects/*/patchDeployments/*`.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsPatchDeploymentsListRequest(_messages.Message):
  r"""A OsconfigProjectsPatchDeploymentsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of patch deployments to return.
      Default is 100.
    pageToken: Optional. A pagination token returned from a previous call to
      ListPatchDeployments that indicates where this listing should continue
      from.
    parent: Required. The resource name of the parent in the form
      `projects/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OsconfigProjectsPatchDeploymentsPatchRequest(_messages.Message):
  r"""A OsconfigProjectsPatchDeploymentsPatchRequest object.

  Fields:
    name: Unique name for the patch deployment resource in a project. The
      patch deployment name is in the form:
      `projects/{project_id}/patchDeployments/{patch_deployment_id}`. This
      field is ignored when you create a new patch deployment.
    patchDeployment: A PatchDeployment resource to be passed as the request
      body.
    updateMask: Optional. Field mask that controls which fields of the patch
      deployment should be updated.
  """

  name = _messages.StringField(1, required=True)
  patchDeployment = _messages.MessageField('PatchDeployment', 2)
  updateMask = _messages.StringField(3)


class OsconfigProjectsPatchDeploymentsPauseRequest(_messages.Message):
  r"""A OsconfigProjectsPatchDeploymentsPauseRequest object.

  Fields:
    name: Required. The resource name of the patch deployment in the form
      `projects/*/patchDeployments/*`.
    pausePatchDeploymentRequest: A PausePatchDeploymentRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  pausePatchDeploymentRequest = _messages.MessageField('PausePatchDeploymentRequest', 2)


class OsconfigProjectsPatchDeploymentsResumeRequest(_messages.Message):
  r"""A OsconfigProjectsPatchDeploymentsResumeRequest object.

  Fields:
    name: Required. The resource name of the patch deployment in the form
      `projects/*/patchDeployments/*`.
    resumePatchDeploymentRequest: A ResumePatchDeploymentRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  resumePatchDeploymentRequest = _messages.MessageField('ResumePatchDeploymentRequest', 2)


class OsconfigProjectsPatchJobsCancelRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsCancelRequest object.

  Fields:
    cancelPatchJobRequest: A CancelPatchJobRequest resource to be passed as
      the request body.
    name: Required. Name of the patch in the form `projects/*/patchJobs/*`
  """

  cancelPatchJobRequest = _messages.MessageField('CancelPatchJobRequest', 1)
  name = _messages.StringField(2, required=True)


class OsconfigProjectsPatchJobsExecuteRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsExecuteRequest object.

  Fields:
    executePatchJobRequest: A ExecutePatchJobRequest resource to be passed as
      the request body.
    parent: Required. The project in which to run this patch in the form
      `projects/*`
  """

  executePatchJobRequest = _messages.MessageField('ExecutePatchJobRequest', 1)
  parent = _messages.StringField(2, required=True)


class OsconfigProjectsPatchJobsGetRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsGetRequest object.

  Fields:
    name: Required. Name of the patch in the form `projects/*/patchJobs/*`
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsPatchJobsInstanceDetailsListRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsInstanceDetailsListRequest object.

  Fields:
    filter: A filter expression that filters results listed in the response.
      This field supports filtering results by instance zone, name, state, or
      `failure_reason`.
    pageSize: The maximum number of instance details records to return.
      Default is 100.
    pageToken: A pagination token returned from a previous call that indicates
      where this listing should continue from.
    parent: Required. The parent for the instances are in the form of
      `projects/*/patchJobs/*`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class OsconfigProjectsPatchJobsListRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsListRequest object.

  Fields:
    filter: If provided, this field specifies the criteria that must be met by
      patch jobs to be included in the response. Currently, filtering is only
      available on the patch_deployment field.
    pageSize: The maximum number of instance status to return.
    pageToken: A pagination token returned from a previous call that indicates
      where this listing should continue from.
    parent: Required. In the form of `projects/*`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class OsconfigProjectsZonesInstancesLookupEffectiveGuestPolicyRequest(_messages.Message):
  r"""A OsconfigProjectsZonesInstancesLookupEffectiveGuestPolicyRequest
  object.

  Fields:
    instance: Required. The VM instance whose policies are being looked up.
    lookupEffectiveGuestPolicyRequest: A LookupEffectiveGuestPolicyRequest
      resource to be passed as the request body.
  """

  instance = _messages.StringField(1, required=True)
  lookupEffectiveGuestPolicyRequest = _messages.MessageField('LookupEffectiveGuestPolicyRequest', 2)


class Package(_messages.Message):
  r"""Package is a reference to the software package to be installed or
  removed. The agent on the VM instance uses the system package manager to
  apply the config. These are the commands that the agent uses to install or
  remove packages. Apt install: `apt-get update && apt-get -y install package1
  package2 package3` remove: `apt-get -y remove package1 package2 package3`
  Yum install: `yum -y install package1 package2 package3` remove: `yum -y
  remove package1 package2 package3` Zypper install: `zypper install package1
  package2 package3` remove: `zypper rm package1 package2` Googet install:
  `googet -noconfirm install package1 package2 package3` remove: `googet
  -noconfirm remove package1 package2 package3`

  Enums:
    DesiredStateValueValuesEnum: The desired_state the agent should maintain
      for this package. The default is to ensure the package is installed.
    ManagerValueValuesEnum: Type of package manager that can be used to
      install this package. If a system does not have the package manager, the
      package is not installed or removed no error message is returned. By
      default, or if you specify `ANY`, the agent attempts to install and
      remove this package using the default package manager. This is useful
      when creating a policy that applies to different types of systems. The
      default behavior is ANY.

  Fields:
    desiredState: The desired_state the agent should maintain for this
      package. The default is to ensure the package is installed.
    manager: Type of package manager that can be used to install this package.
      If a system does not have the package manager, the package is not
      installed or removed no error message is returned. By default, or if you
      specify `ANY`, the agent attempts to install and remove this package
      using the default package manager. This is useful when creating a policy
      that applies to different types of systems. The default behavior is ANY.
    name: Required. The name of the package. A package is uniquely identified
      for conflict validation by checking the package name and the manager(s)
      that the package targets.
  """

  class DesiredStateValueValuesEnum(_messages.Enum):
    r"""The desired_state the agent should maintain for this package. The
    default is to ensure the package is installed.

    Values:
      DESIRED_STATE_UNSPECIFIED: The default is to ensure the package is
        installed.
      INSTALLED: The agent ensures that the package is installed.
      UPDATED: The agent ensures that the package is installed and
        periodically checks for and install any updates.
      REMOVED: The agent ensures that the package is not installed and
        uninstall it if detected.
    """
    DESIRED_STATE_UNSPECIFIED = 0
    INSTALLED = 1
    UPDATED = 2
    REMOVED = 3

  class ManagerValueValuesEnum(_messages.Enum):
    r"""Type of package manager that can be used to install this package. If a
    system does not have the package manager, the package is not installed or
    removed no error message is returned. By default, or if you specify `ANY`,
    the agent attempts to install and remove this package using the default
    package manager. This is useful when creating a policy that applies to
    different types of systems. The default behavior is ANY.

    Values:
      MANAGER_UNSPECIFIED: The default behavior is ANY.
      ANY: Apply this package config using the default system package manager.
      APT: Apply this package config only if Apt is available on the system.
      YUM: Apply this package config only if Yum is available on the system.
      ZYPPER: Apply this package config only if Zypper is available on the
        system.
      GOO: Apply this package config only if GooGet is available on the
        system.
    """
    MANAGER_UNSPECIFIED = 0
    ANY = 1
    APT = 2
    YUM = 3
    ZYPPER = 4
    GOO = 5

  desiredState = _messages.EnumField('DesiredStateValueValuesEnum', 1)
  manager = _messages.EnumField('ManagerValueValuesEnum', 2)
  name = _messages.StringField(3)


class PackageRepository(_messages.Message):
  r"""A package repository.

  Fields:
    apt: An Apt Repository.
    goo: A Goo Repository.
    yum: A Yum Repository.
    zypper: A Zypper Repository.
  """

  apt = _messages.MessageField('AptRepository', 1)
  goo = _messages.MessageField('GooRepository', 2)
  yum = _messages.MessageField('YumRepository', 3)
  zypper = _messages.MessageField('ZypperRepository', 4)


class PatchConfig(_messages.Message):
  r"""Patch configuration specifications. Contains details on how to apply the
  patch(es) to a VM instance.

  Enums:
    RebootConfigValueValuesEnum: Post-patch reboot settings.

  Fields:
    apt: Apt update settings. Use this setting to override the default `apt`
      patch rules.
    goo: Goo update settings. Use this setting to override the default `goo`
      patch rules.
    migInstancesAllowed: Allows the patch job to run on Managed instance
      groups (MIGs).
    postStep: The `ExecStep` to run after the patch update.
    preStep: The `ExecStep` to run before the patch update.
    rebootConfig: Post-patch reboot settings.
    windowsUpdate: Windows update settings. Use this override the default
      windows patch rules.
    yum: Yum update settings. Use this setting to override the default `yum`
      patch rules.
    zypper: Zypper update settings. Use this setting to override the default
      `zypper` patch rules.
  """

  class RebootConfigValueValuesEnum(_messages.Enum):
    r"""Post-patch reboot settings.

    Values:
      REBOOT_CONFIG_UNSPECIFIED: The default behavior is DEFAULT.
      DEFAULT: The agent decides if a reboot is necessary by checking signals
        such as registry keys on Windows or `/var/run/reboot-required` on APT
        based systems. On RPM based systems, a set of core system package
        install times are compared with system boot time.
      ALWAYS: Always reboot the machine after the update completes.
      NEVER: Never reboot the machine after the update completes.
    """
    REBOOT_CONFIG_UNSPECIFIED = 0
    DEFAULT = 1
    ALWAYS = 2
    NEVER = 3

  apt = _messages.MessageField('AptSettings', 1)
  goo = _messages.MessageField('GooSettings', 2)
  migInstancesAllowed = _messages.BooleanField(3)
  postStep = _messages.MessageField('ExecStep', 4)
  preStep = _messages.MessageField('ExecStep', 5)
  rebootConfig = _messages.EnumField('RebootConfigValueValuesEnum', 6)
  windowsUpdate = _messages.MessageField('WindowsUpdateSettings', 7)
  yum = _messages.MessageField('YumSettings', 8)
  zypper = _messages.MessageField('ZypperSettings', 9)


class PatchDeployment(_messages.Message):
  r"""Patch deployments are configurations that individual patch jobs use to
  complete a patch. These configurations include instance filter, package
  repository settings, and a schedule. For more information about creating and
  managing patch deployments, see [Scheduling patch
  jobs](https://cloud.google.com/compute/docs/os-patch-management/schedule-
  patch-jobs).

  Enums:
    StateValueValuesEnum: Output only. Current state of the patch deployment.

  Fields:
    createTime: Output only. Time the patch deployment was created. Timestamp
      is in [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
    description: Optional. Description of the patch deployment. Length of the
      description is limited to 1024 characters.
    duration: Optional. Duration of the patch. After the duration ends, the
      patch times out.
    instanceFilter: Required. VM instances to patch.
    lastExecuteTime: Output only. The last time a patch job was started by
      this deployment. Timestamp is in
      [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
    name: Unique name for the patch deployment resource in a project. The
      patch deployment name is in the form:
      `projects/{project_id}/patchDeployments/{patch_deployment_id}`. This
      field is ignored when you create a new patch deployment.
    oneTimeSchedule: Required. Schedule a one-time execution.
    patchConfig: Optional. Patch configuration that is applied.
    recurringSchedule: Required. Schedule recurring executions.
    rollout: Optional. Rollout strategy of the patch job.
    state: Output only. Current state of the patch deployment.
    updateTime: Output only. Time the patch deployment was last updated.
      Timestamp is in [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text
      format.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the patch deployment.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      ACTIVE: Active value means that patch deployment generates Patch Jobs.
      PAUSED: Paused value means that patch deployment does not generate Patch
        jobs. Requires user action to move in and out from this state.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    PAUSED = 2

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  duration = _messages.StringField(3)
  instanceFilter = _messages.MessageField('PatchInstanceFilter', 4)
  lastExecuteTime = _messages.StringField(5)
  name = _messages.StringField(6)
  oneTimeSchedule = _messages.MessageField('OneTimeSchedule', 7)
  patchConfig = _messages.MessageField('PatchConfig', 8)
  recurringSchedule = _messages.MessageField('RecurringSchedule', 9)
  rollout = _messages.MessageField('PatchRollout', 10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  updateTime = _messages.StringField(12)


class PatchInstanceFilter(_messages.Message):
  r"""A filter to target VM instances for patching. The targeted VMs must meet
  all criteria specified. So if both labels and zones are specified, the patch
  job targets only VMs with those labels and in those zones.

  Fields:
    all: Target all VM instances in the project. If true, no other criteria is
      permitted.
    groupLabels: Targets VM instances matching at least one of these label
      sets. This allows targeting of disparate groups, for example "env=prod
      or env=staging".
    instanceNamePrefixes: Targets VMs whose name starts with one of these
      prefixes. Similar to labels, this is another way to group VMs when
      targeting configs, for example prefix="prod-".
    instances: Targets any of the VM instances specified. Instances are
      specified by their URI in the form
      `zones/[ZONE]/instances/[INSTANCE_NAME]`,
      `projects/[PROJECT_ID]/zones/[ZONE]/instances/[INSTANCE_NAME]`, or `http
      s://www.googleapis.com/compute/v1/projects/[PROJECT_ID]/zones/[ZONE]/ins
      tances/[INSTANCE_NAME]`
    zones: Targets VM instances in ANY of these zones. Leave empty to target
      VM instances in any zone.
  """

  all = _messages.BooleanField(1)
  groupLabels = _messages.MessageField('PatchInstanceFilterGroupLabel', 2, repeated=True)
  instanceNamePrefixes = _messages.StringField(3, repeated=True)
  instances = _messages.StringField(4, repeated=True)
  zones = _messages.StringField(5, repeated=True)


class PatchInstanceFilterGroupLabel(_messages.Message):
  r"""Represents a group of VMs that can be identified as having all these
  labels, for example "env=prod and app=web".

  Messages:
    LabelsValue: Compute Engine instance labels that must be present for a VM
      instance to be targeted by this filter.

  Fields:
    labels: Compute Engine instance labels that must be present for a VM
      instance to be targeted by this filter.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Compute Engine instance labels that must be present for a VM instance
    to be targeted by this filter.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)


class PatchJob(_messages.Message):
  r"""A high level representation of a patch job that is either in progress or
  has completed. Instance details are not included in the job. To paginate
  through instance details, use `ListPatchJobInstanceDetails`. For more
  information about patch jobs, see [Creating patch
  jobs](https://cloud.google.com/compute/docs/os-patch-management/create-
  patch-job).

  Enums:
    StateValueValuesEnum: The current state of the PatchJob.

  Fields:
    createTime: Time this patch job was created.
    description: Description of the patch job. Length of the description is
      limited to 1024 characters.
    displayName: Display name for this patch job. This is not a unique
      identifier.
    dryRun: If this patch job is a dry run, the agent reports that it has
      finished without running any updates on the VM instance.
    duration: Duration of the patch job. After the duration ends, the patch
      job times out.
    errorMessage: If this patch job failed, this message provides information
      about the failure.
    instanceDetailsSummary: Summary of instance details.
    instanceFilter: Instances to patch.
    name: Unique identifier for this patch job in the form
      `projects/*/patchJobs/*`
    patchConfig: Patch configuration being applied.
    patchDeployment: Output only. Name of the patch deployment that created
      this patch job.
    percentComplete: Reflects the overall progress of the patch job in the
      range of 0.0 being no progress to 100.0 being complete.
    rollout: Rollout strategy being applied.
    state: The current state of the PatchJob.
    updateTime: Last time this patch job was updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the PatchJob.

    Values:
      STATE_UNSPECIFIED: State must be specified.
      STARTED: The patch job was successfully initiated.
      INSTANCE_LOOKUP: The patch job is looking up instances to run the patch
        on.
      PATCHING: Instances are being patched.
      SUCCEEDED: Patch job completed successfully.
      COMPLETED_WITH_ERRORS: Patch job completed but there were errors.
      CANCELED: The patch job was canceled.
      TIMED_OUT: The patch job timed out.
    """
    STATE_UNSPECIFIED = 0
    STARTED = 1
    INSTANCE_LOOKUP = 2
    PATCHING = 3
    SUCCEEDED = 4
    COMPLETED_WITH_ERRORS = 5
    CANCELED = 6
    TIMED_OUT = 7

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  dryRun = _messages.BooleanField(4)
  duration = _messages.StringField(5)
  errorMessage = _messages.StringField(6)
  instanceDetailsSummary = _messages.MessageField('PatchJobInstanceDetailsSummary', 7)
  instanceFilter = _messages.MessageField('PatchInstanceFilter', 8)
  name = _messages.StringField(9)
  patchConfig = _messages.MessageField('PatchConfig', 10)
  patchDeployment = _messages.StringField(11)
  percentComplete = _messages.FloatField(12)
  rollout = _messages.MessageField('PatchRollout', 13)
  state = _messages.EnumField('StateValueValuesEnum', 14)
  updateTime = _messages.StringField(15)


class PatchJobInstanceDetails(_messages.Message):
  r"""Patch details for a VM instance. For more information about reviewing VM
  instance details, see [Listing all VM instance details for a specific patch
  job](https://cloud.google.com/compute/docs/os-patch-management/manage-patch-
  jobs#list-instance-details).

  Enums:
    StateValueValuesEnum: Current state of instance patch.

  Fields:
    attemptCount: The number of times the agent that the agent attempts to
      apply the patch.
    failureReason: If the patch fails, this field provides the reason.
    instanceSystemId: The unique identifier for the instance. This identifier
      is defined by the server.
    name: The instance name in the form `projects/*/zones/*/instances/*`
    state: Current state of instance patch.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Current state of instance patch.

    Values:
      PATCH_STATE_UNSPECIFIED: Unspecified.
      PENDING: The instance is not yet notified.
      INACTIVE: Instance is inactive and cannot be patched.
      NOTIFIED: The instance is notified that it should be patched.
      STARTED: The instance has started the patching process.
      DOWNLOADING_PATCHES: The instance is downloading patches.
      APPLYING_PATCHES: The instance is applying patches.
      REBOOTING: The instance is rebooting.
      SUCCEEDED: The instance has completed applying patches.
      SUCCEEDED_REBOOT_REQUIRED: The instance has completed applying patches
        but a reboot is required.
      FAILED: The instance has failed to apply the patch.
      ACKED: The instance acked the notification and will start shortly.
      TIMED_OUT: The instance exceeded the time out while applying the patch.
      RUNNING_PRE_PATCH_STEP: The instance is running the pre-patch step.
      RUNNING_POST_PATCH_STEP: The instance is running the post-patch step.
      NO_AGENT_DETECTED: The service could not detect the presence of the
        agent. Check to ensure that the agent is installed, running, and able
        to communicate with the service.
    """
    PATCH_STATE_UNSPECIFIED = 0
    PENDING = 1
    INACTIVE = 2
    NOTIFIED = 3
    STARTED = 4
    DOWNLOADING_PATCHES = 5
    APPLYING_PATCHES = 6
    REBOOTING = 7
    SUCCEEDED = 8
    SUCCEEDED_REBOOT_REQUIRED = 9
    FAILED = 10
    ACKED = 11
    TIMED_OUT = 12
    RUNNING_PRE_PATCH_STEP = 13
    RUNNING_POST_PATCH_STEP = 14
    NO_AGENT_DETECTED = 15

  attemptCount = _messages.IntegerField(1)
  failureReason = _messages.StringField(2)
  instanceSystemId = _messages.StringField(3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class PatchJobInstanceDetailsSummary(_messages.Message):
  r"""A summary of the current patch state across all instances that this
  patch job affects. Contains counts of instances in different states. These
  states map to `InstancePatchState`. List patch job instance details to see
  the specific states of each instance.

  Fields:
    ackedInstanceCount: Number of instances that have acked and will start
      shortly.
    applyingPatchesInstanceCount: Number of instances that are applying
      patches.
    downloadingPatchesInstanceCount: Number of instances that are downloading
      patches.
    failedInstanceCount: Number of instances that failed.
    inactiveInstanceCount: Number of instances that are inactive.
    noAgentDetectedInstanceCount: Number of instances that do not appear to be
      running the agent. Check to ensure that the agent is installed, running,
      and able to communicate with the service.
    notifiedInstanceCount: Number of instances notified about patch job.
    pendingInstanceCount: Number of instances pending patch job.
    postPatchStepInstanceCount: Number of instances that are running the post-
      patch step.
    prePatchStepInstanceCount: Number of instances that are running the pre-
      patch step.
    rebootingInstanceCount: Number of instances rebooting.
    startedInstanceCount: Number of instances that have started.
    succeededInstanceCount: Number of instances that have completed
      successfully.
    succeededRebootRequiredInstanceCount: Number of instances that require
      reboot.
    timedOutInstanceCount: Number of instances that exceeded the time out
      while applying the patch.
  """

  ackedInstanceCount = _messages.IntegerField(1)
  applyingPatchesInstanceCount = _messages.IntegerField(2)
  downloadingPatchesInstanceCount = _messages.IntegerField(3)
  failedInstanceCount = _messages.IntegerField(4)
  inactiveInstanceCount = _messages.IntegerField(5)
  noAgentDetectedInstanceCount = _messages.IntegerField(6)
  notifiedInstanceCount = _messages.IntegerField(7)
  pendingInstanceCount = _messages.IntegerField(8)
  postPatchStepInstanceCount = _messages.IntegerField(9)
  prePatchStepInstanceCount = _messages.IntegerField(10)
  rebootingInstanceCount = _messages.IntegerField(11)
  startedInstanceCount = _messages.IntegerField(12)
  succeededInstanceCount = _messages.IntegerField(13)
  succeededRebootRequiredInstanceCount = _messages.IntegerField(14)
  timedOutInstanceCount = _messages.IntegerField(15)


class PatchRollout(_messages.Message):
  r"""Patch rollout configuration specifications. Contains details on the
  concurrency control when applying patch(es) to all targeted VMs.

  Enums:
    ModeValueValuesEnum: Mode of the patch rollout.

  Fields:
    disruptionBudget: The maximum number (or percentage) of VMs per zone to
      disrupt at any given moment. The number of VMs calculated from
      multiplying the percentage by the total number of VMs in a zone is
      rounded up. During patching, a VM is considered disrupted from the time
      the agent is notified to begin until patching has completed. This
      disruption time includes the time to complete reboot and any post-patch
      steps. A VM contributes to the disruption budget if its patching
      operation fails either when applying the patches, running pre or post
      patch steps, or if it fails to respond with a success notification
      before timing out. VMs that are not running or do not have an active
      agent do not count toward this disruption budget. For zone-by-zone
      rollouts, if the disruption budget in a zone is exceeded, the patch job
      stops, because continuing to the next zone requires completion of the
      patch process in the previous zone. For example, if the disruption
      budget has a fixed value of `10`, and 8 VMs fail to patch in the current
      zone, the patch job continues to patch 2 VMs at a time until the zone is
      completed. When that zone is completed successfully, patching begins
      with 10 VMs at a time in the next zone. If 10 VMs in the next zone fail
      to patch, the patch job stops.
    mode: Mode of the patch rollout.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Mode of the patch rollout.

    Values:
      MODE_UNSPECIFIED: Mode must be specified.
      ZONE_BY_ZONE: Patches are applied one zone at a time. The patch job
        begins in the region with the lowest number of targeted VMs. Within
        the region, patching begins in the zone with the lowest number of
        targeted VMs. If multiple regions (or zones within a region) have the
        same number of targeted VMs, a tie-breaker is achieved by sorting the
        regions or zones in alphabetical order.
      CONCURRENT_ZONES: Patches are applied to VMs in all zones at the same
        time.
    """
    MODE_UNSPECIFIED = 0
    ZONE_BY_ZONE = 1
    CONCURRENT_ZONES = 2

  disruptionBudget = _messages.MessageField('FixedOrPercent', 1)
  mode = _messages.EnumField('ModeValueValuesEnum', 2)


class PausePatchDeploymentRequest(_messages.Message):
  r"""A request message for pausing a patch deployment."""


class RecurringSchedule(_messages.Message):
  r"""Sets the time for recurring patch deployments.

  Enums:
    FrequencyValueValuesEnum: Required. The frequency unit of this recurring
      schedule.

  Fields:
    endTime: Optional. The end time at which a recurring patch deployment
      schedule is no longer active.
    frequency: Required. The frequency unit of this recurring schedule.
    lastExecuteTime: Output only. The time the last patch job ran
      successfully.
    monthly: Required. Schedule with monthly executions.
    nextExecuteTime: Output only. The time the next patch job is scheduled to
      run.
    startTime: Optional. The time that the recurring schedule becomes
      effective. Defaults to `create_time` of the patch deployment.
    timeOfDay: Required. Time of the day to run a recurring deployment.
    timeZone: Required. Defines the time zone that `time_of_day` is relative
      to. The rules for daylight saving time are determined by the chosen time
      zone.
    weekly: Required. Schedule with weekly executions.
  """

  class FrequencyValueValuesEnum(_messages.Enum):
    r"""Required. The frequency unit of this recurring schedule.

    Values:
      FREQUENCY_UNSPECIFIED: Invalid. A frequency must be specified.
      WEEKLY: Indicates that the frequency of recurrence should be expressed
        in terms of weeks.
      MONTHLY: Indicates that the frequency of recurrence should be expressed
        in terms of months.
      DAILY: Indicates that the frequency of recurrence should be expressed in
        terms of days.
    """
    FREQUENCY_UNSPECIFIED = 0
    WEEKLY = 1
    MONTHLY = 2
    DAILY = 3

  endTime = _messages.StringField(1)
  frequency = _messages.EnumField('FrequencyValueValuesEnum', 2)
  lastExecuteTime = _messages.StringField(3)
  monthly = _messages.MessageField('MonthlySchedule', 4)
  nextExecuteTime = _messages.StringField(5)
  startTime = _messages.StringField(6)
  timeOfDay = _messages.MessageField('TimeOfDay', 7)
  timeZone = _messages.MessageField('TimeZone', 8)
  weekly = _messages.MessageField('WeeklySchedule', 9)


class ResumePatchDeploymentRequest(_messages.Message):
  r"""A request message for resuming a patch deployment."""


class SoftwareRecipe(_messages.Message):
  r"""A software recipe is a set of instructions for installing and
  configuring a piece of software. It consists of a set of artifacts that are
  downloaded, and a set of steps that install, configure, and/or update the
  software. Recipes support installing and updating software from artifacts in
  the following formats: Zip archive, Tar archive, Windows MSI, Debian
  package, and RPM package. Additionally, recipes support executing a script
  (either defined in a file or directly in this api) in bash, sh, cmd, and
  powershell. Updating a software recipe If a recipe is assigned to an
  instance and there is a recipe with the same name but a lower version
  already installed and the assigned state of the recipe is `UPDATED`, then
  the recipe is updated to the new version. Script Working Directories Each
  script or execution step is run in its own temporary directory which is
  deleted after completing the step.

  Enums:
    DesiredStateValueValuesEnum: Default is INSTALLED. The desired state the
      agent should maintain for this recipe. INSTALLED: The software recipe is
      installed on the instance but won't be updated to new versions. UPDATED:
      The software recipe is installed on the instance. The recipe is updated
      to a higher version, if a higher version of the recipe is assigned to
      this instance. REMOVE: Remove is unsupported for software recipes and
      attempts to create or update a recipe to the REMOVE state is rejected.

  Fields:
    artifacts: Resources available to be used in the steps in the recipe.
    desiredState: Default is INSTALLED. The desired state the agent should
      maintain for this recipe. INSTALLED: The software recipe is installed on
      the instance but won't be updated to new versions. UPDATED: The software
      recipe is installed on the instance. The recipe is updated to a higher
      version, if a higher version of the recipe is assigned to this instance.
      REMOVE: Remove is unsupported for software recipes and attempts to
      create or update a recipe to the REMOVE state is rejected.
    installSteps: Actions to be taken for installing this recipe. On failure
      it stops executing steps and does not attempt another installation. Any
      steps taken (including partially completed steps) are not rolled back.
    name: Required. Unique identifier for the recipe. Only one recipe with a
      given name is installed on an instance. Names are also used to identify
      resources which helps to determine whether guest policies have
      conflicts. This means that requests to create multiple recipes with the
      same name and version are rejected since they could potentially have
      conflicting assignments.
    updateSteps: Actions to be taken for updating this recipe. On failure it
      stops executing steps and does not attempt another update for this
      recipe. Any steps taken (including partially completed steps) are not
      rolled back.
    version: The version of this software recipe. Version can be up to 4
      period separated numbers (e.g. ***********).
  """

  class DesiredStateValueValuesEnum(_messages.Enum):
    r"""Default is INSTALLED. The desired state the agent should maintain for
    this recipe. INSTALLED: The software recipe is installed on the instance
    but won't be updated to new versions. UPDATED: The software recipe is
    installed on the instance. The recipe is updated to a higher version, if a
    higher version of the recipe is assigned to this instance. REMOVE: Remove
    is unsupported for software recipes and attempts to create or update a
    recipe to the REMOVE state is rejected.

    Values:
      DESIRED_STATE_UNSPECIFIED: The default is to ensure the package is
        installed.
      INSTALLED: The agent ensures that the package is installed.
      UPDATED: The agent ensures that the package is installed and
        periodically checks for and install any updates.
      REMOVED: The agent ensures that the package is not installed and
        uninstall it if detected.
    """
    DESIRED_STATE_UNSPECIFIED = 0
    INSTALLED = 1
    UPDATED = 2
    REMOVED = 3

  artifacts = _messages.MessageField('SoftwareRecipeArtifact', 1, repeated=True)
  desiredState = _messages.EnumField('DesiredStateValueValuesEnum', 2)
  installSteps = _messages.MessageField('SoftwareRecipeStep', 3, repeated=True)
  name = _messages.StringField(4)
  updateSteps = _messages.MessageField('SoftwareRecipeStep', 5, repeated=True)
  version = _messages.StringField(6)


class SoftwareRecipeArtifact(_messages.Message):
  r"""Specifies a resource to be used in the recipe.

  Fields:
    allowInsecure: Defaults to false. When false, recipes are subject to
      validations based on the artifact type: Remote: A checksum must be
      specified, and only protocols with transport-layer security are
      permitted. GCS: An object generation number must be specified.
    gcs: A Google Cloud Storage artifact.
    id: Required. Id of the artifact, which the installation and update steps
      of this recipe can reference. Artifacts in a recipe cannot have the same
      id.
    remote: A generic remote artifact.
  """

  allowInsecure = _messages.BooleanField(1)
  gcs = _messages.MessageField('SoftwareRecipeArtifactGcs', 2)
  id = _messages.StringField(3)
  remote = _messages.MessageField('SoftwareRecipeArtifactRemote', 4)


class SoftwareRecipeArtifactGcs(_messages.Message):
  r"""Specifies an artifact available as a Google Cloud Storage object.

  Fields:
    bucket: Bucket of the Google Cloud Storage object. Given an example URL:
      `https://storage.googleapis.com/my-bucket/foo/bar#1234567` this value
      would be `my-bucket`.
    generation: Must be provided if allow_insecure is false. Generation number
      of the Google Cloud Storage object. `https://storage.googleapis.com/my-
      bucket/foo/bar#1234567` this value would be `1234567`.
    object: Name of the Google Cloud Storage object. As specified [here]
      (https://cloud.google.com/storage/docs/naming#objectnames) Given an
      example URL: `https://storage.googleapis.com/my-bucket/foo/bar#1234567`
      this value would be `foo/bar`.
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)


class SoftwareRecipeArtifactRemote(_messages.Message):
  r"""Specifies an artifact available via some URI.

  Fields:
    checksum: Must be provided if `allow_insecure` is `false`. SHA256 checksum
      in hex format, to compare to the checksum of the artifact. If the
      checksum is not empty and it doesn't match the artifact then the recipe
      installation fails before running any of the steps.
    uri: URI from which to fetch the object. It should contain both the
      protocol and path following the format {protocol}://{location}.
  """

  checksum = _messages.StringField(1)
  uri = _messages.StringField(2)


class SoftwareRecipeStep(_messages.Message):
  r"""An action that can be taken as part of installing or updating a recipe.

  Fields:
    archiveExtraction: Extracts an archive into the specified directory.
    dpkgInstallation: Installs a deb file via dpkg.
    fileCopy: Copies a file onto the instance.
    fileExec: Executes an artifact or local file.
    msiInstallation: Installs an MSI file.
    rpmInstallation: Installs an rpm file via the rpm utility.
    scriptRun: Runs commands in a shell.
  """

  archiveExtraction = _messages.MessageField('SoftwareRecipeStepExtractArchive', 1)
  dpkgInstallation = _messages.MessageField('SoftwareRecipeStepInstallDpkg', 2)
  fileCopy = _messages.MessageField('SoftwareRecipeStepCopyFile', 3)
  fileExec = _messages.MessageField('SoftwareRecipeStepExecFile', 4)
  msiInstallation = _messages.MessageField('SoftwareRecipeStepInstallMsi', 5)
  rpmInstallation = _messages.MessageField('SoftwareRecipeStepInstallRpm', 6)
  scriptRun = _messages.MessageField('SoftwareRecipeStepRunScript', 7)


class SoftwareRecipeStepCopyFile(_messages.Message):
  r"""Copies the artifact to the specified path on the instance.

  Fields:
    artifactId: Required. The id of the relevant artifact in the recipe.
    destination: Required. The absolute path on the instance to put the file.
    overwrite: Whether to allow this step to overwrite existing files. If this
      is false and the file already exists the file is not overwritten and the
      step is considered a success. Defaults to false.
    permissions: Consists of three octal digits which represent, in order, the
      permissions of the owner, group, and other users for the file (similarly
      to the numeric mode used in the linux chmod utility). Each digit
      represents a three bit number with the 4 bit corresponding to the read
      permissions, the 2 bit corresponds to the write bit, and the one bit
      corresponds to the execute permission. Default behavior is 755. Below
      are some examples of permissions and their associated values: read,
      write, and execute: 7 read and execute: 5 read and write: 6 read only: 4
  """

  artifactId = _messages.StringField(1)
  destination = _messages.StringField(2)
  overwrite = _messages.BooleanField(3)
  permissions = _messages.StringField(4)


class SoftwareRecipeStepExecFile(_messages.Message):
  r"""Executes an artifact or local file.

  Fields:
    allowedExitCodes: Defaults to [0]. A list of possible return values that
      the program can return to indicate a success.
    args: Arguments to be passed to the provided executable.
    artifactId: The id of the relevant artifact in the recipe.
    localPath: The absolute path of the file on the local filesystem.
  """

  allowedExitCodes = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  args = _messages.StringField(2, repeated=True)
  artifactId = _messages.StringField(3)
  localPath = _messages.StringField(4)


class SoftwareRecipeStepExtractArchive(_messages.Message):
  r"""Extracts an archive of the type specified in the specified directory.

  Enums:
    TypeValueValuesEnum: Required. The type of the archive to extract.

  Fields:
    artifactId: Required. The id of the relevant artifact in the recipe.
    destination: Directory to extract archive to. Defaults to `/` on Linux or
      `C:\` on Windows.
    type: Required. The type of the archive to extract.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of the archive to extract.

    Values:
      ARCHIVE_TYPE_UNSPECIFIED: Indicates that the archive type isn't
        specified.
      TAR: Indicates that the archive is a tar archive with no encryption.
      TAR_GZIP: Indicates that the archive is a tar archive with gzip
        encryption.
      TAR_BZIP: Indicates that the archive is a tar archive with bzip
        encryption.
      TAR_LZMA: Indicates that the archive is a tar archive with lzma
        encryption.
      TAR_XZ: Indicates that the archive is a tar archive with xz encryption.
      ZIP: Indicates that the archive is a zip archive.
    """
    ARCHIVE_TYPE_UNSPECIFIED = 0
    TAR = 1
    TAR_GZIP = 2
    TAR_BZIP = 3
    TAR_LZMA = 4
    TAR_XZ = 5
    ZIP = 6

  artifactId = _messages.StringField(1)
  destination = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class SoftwareRecipeStepInstallDpkg(_messages.Message):
  r"""Installs a deb via dpkg.

  Fields:
    artifactId: Required. The id of the relevant artifact in the recipe.
  """

  artifactId = _messages.StringField(1)


class SoftwareRecipeStepInstallMsi(_messages.Message):
  r"""Installs an MSI file.

  Fields:
    allowedExitCodes: Return codes that indicate that the software installed
      or updated successfully. Behaviour defaults to [0]
    artifactId: Required. The id of the relevant artifact in the recipe.
    flags: The flags to use when installing the MSI defaults to ["/i"] (i.e.
      the install flag).
  """

  allowedExitCodes = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  artifactId = _messages.StringField(2)
  flags = _messages.StringField(3, repeated=True)


class SoftwareRecipeStepInstallRpm(_messages.Message):
  r"""Installs an rpm file via the rpm utility.

  Fields:
    artifactId: Required. The id of the relevant artifact in the recipe.
  """

  artifactId = _messages.StringField(1)


class SoftwareRecipeStepRunScript(_messages.Message):
  r"""Runs a script through an interpreter.

  Enums:
    InterpreterValueValuesEnum: The script interpreter to use to run the
      script. If no interpreter is specified the script is executed directly,
      which likely only succeed for scripts with [shebang
      lines](https://en.wikipedia.org/wiki/Shebang_\(Unix\)).

  Fields:
    allowedExitCodes: Return codes that indicate that the software installed
      or updated successfully. Behaviour defaults to [0]
    interpreter: The script interpreter to use to run the script. If no
      interpreter is specified the script is executed directly, which likely
      only succeed for scripts with [shebang
      lines](https://en.wikipedia.org/wiki/Shebang_\(Unix\)).
    script: Required. The shell script to be executed.
  """

  class InterpreterValueValuesEnum(_messages.Enum):
    r"""The script interpreter to use to run the script. If no interpreter is
    specified the script is executed directly, which likely only succeed for
    scripts with [shebang
    lines](https://en.wikipedia.org/wiki/Shebang_\(Unix\)).

    Values:
      INTERPRETER_UNSPECIFIED: Default value for ScriptType.
      SHELL: Indicates that the script is run with `/bin/sh` on Linux and
        `cmd` on windows.
      POWERSHELL: Indicates that the script is run with powershell.
    """
    INTERPRETER_UNSPECIFIED = 0
    SHELL = 1
    POWERSHELL = 2

  allowedExitCodes = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  interpreter = _messages.EnumField('InterpreterValueValuesEnum', 2)
  script = _messages.StringField(3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StatusProto(_messages.Message):
  r"""Wire-format for a Status object

  Fields:
    canonicalCode: copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional int32 canonical_code = 6;
    code: Numeric code drawn from the space specified below. Often, this is
      the canonical error space, and code is drawn from
      google3/util/task/codes.proto copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional int32 code = 1;
    message: Detail message copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional string message = 3;
    messageSet: message_set associates an arbitrary proto message with the
      status. copybara:strip_begin(b/383363683) copybara:strip_end_and_replace
      optional proto2.bridge.MessageSet message_set = 5;
    space: copybara:strip_begin(b/383363683) Space to which this status
      belongs copybara:strip_end_and_replace optional string space = 2; //
      Space to which this status belongs
  """

  canonicalCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  code = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  message = _messages.StringField(3)
  messageSet = _messages.MessageField('MessageSet', 4)
  space = _messages.StringField(5)


class TimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of a day in 24 hour format. Must be greater than or equal to
      0 and typically must be less than or equal to 23. An API may choose to
      allow the value "24:00:00" for scenarios like business closing time.
    minutes: Minutes of an hour. Must be greater than or equal to 0 and less
      than or equal to 59.
    nanos: Fractions of seconds, in nanoseconds. Must be greater than or equal
      to 0 and less than or equal to 999,999,999.
    seconds: Seconds of a minute. Must be greater than or equal to 0 and
      typically must be less than or equal to 59. An API may allow the value
      60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class TimeZone(_messages.Message):
  r"""Represents a time zone from the [IANA Time Zone
  Database](https://www.iana.org/time-zones).

  Fields:
    id: IANA Time Zone Database time zone. For example "America/New_York".
    version: Optional. IANA Time Zone Database version number. For example
      "2019a".
  """

  id = _messages.StringField(1)
  version = _messages.StringField(2)


class WeekDayOfMonth(_messages.Message):
  r"""Represents one week day in a month. An example is "the 4th Sunday".

  Enums:
    DayOfWeekValueValuesEnum: Required. A day of the week.

  Fields:
    dayOfWeek: Required. A day of the week.
    dayOffset: Optional. Represents the number of days before or after the
      given week day of month that the patch deployment is scheduled for. For
      example if `week_ordinal` and `day_of_week` values point to the second
      day of the month and this `day_offset` value is set to `3`, the patch
      deployment takes place three days after the second Tuesday of the month.
      If this value is negative, for example -5, the patches are deployed five
      days before before the second Tuesday of the month. Allowed values are
      in range [-30, 30].
    weekOrdinal: Required. Week number in a month. 1-4 indicates the 1st to
      4th week of the month. -1 indicates the last week of the month.
  """

  class DayOfWeekValueValuesEnum(_messages.Enum):
    r"""Required. A day of the week.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  dayOfWeek = _messages.EnumField('DayOfWeekValueValuesEnum', 1)
  dayOffset = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  weekOrdinal = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class WeeklySchedule(_messages.Message):
  r"""Represents a weekly schedule.

  Enums:
    DayOfWeekValueValuesEnum: Required. Day of the week.

  Fields:
    dayOfWeek: Required. Day of the week.
  """

  class DayOfWeekValueValuesEnum(_messages.Enum):
    r"""Required. Day of the week.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  dayOfWeek = _messages.EnumField('DayOfWeekValueValuesEnum', 1)


class WindowsUpdateSettings(_messages.Message):
  r"""Windows patching is performed using the Windows Update Agent.

  Enums:
    ClassificationsValueListEntryValuesEnum:

  Fields:
    classifications: Only apply updates of these windows update
      classifications. If empty, all updates are applied.
    excludes: List of KBs to exclude from update.
    exclusivePatches: An exclusive list of kbs to be updated. These are the
      only patches that will be updated. This field must not be used with
      other patch configurations.
  """

  class ClassificationsValueListEntryValuesEnum(_messages.Enum):
    r"""ClassificationsValueListEntryValuesEnum enum type.

    Values:
      CLASSIFICATION_UNSPECIFIED: Invalid. If classifications are included,
        they must be specified.
      CRITICAL: "A widely released fix for a specific problem that addresses a
        critical, non-security-related bug." [1]
      SECURITY: "A widely released fix for a product-specific, security-
        related vulnerability. Security vulnerabilities are rated by their
        severity. The severity rating is indicated in the Microsoft security
        bulletin as critical, important, moderate, or low." [1]
      DEFINITION: "A widely released and frequent software update that
        contains additions to a product's definition database. Definition
        databases are often used to detect objects that have specific
        attributes, such as malicious code, phishing websites, or junk mail."
        [1]
      DRIVER: "Software that controls the input and output of a device." [1]
      FEATURE_PACK: "New product functionality that is first distributed
        outside the context of a product release and that is typically
        included in the next full product release." [1]
      SERVICE_PACK: "A tested, cumulative set of all hotfixes, security
        updates, critical updates, and updates. Additionally, service packs
        may contain additional fixes for problems that are found internally
        since the release of the product. Service packs my also contain a
        limited number of customer-requested design changes or features." [1]
      TOOL: "A utility or feature that helps complete a task or set of tasks."
        [1]
      UPDATE_ROLLUP: "A tested, cumulative set of hotfixes, security updates,
        critical updates, and updates that are packaged together for easy
        deployment. A rollup generally targets a specific area, such as
        security, or a component of a product, such as Internet Information
        Services (IIS)." [1]
      UPDATE: "A widely released fix for a specific problem. An update
        addresses a noncritical, non-security-related bug." [1]
    """
    CLASSIFICATION_UNSPECIFIED = 0
    CRITICAL = 1
    SECURITY = 2
    DEFINITION = 3
    DRIVER = 4
    FEATURE_PACK = 5
    SERVICE_PACK = 6
    TOOL = 7
    UPDATE_ROLLUP = 8
    UPDATE = 9

  classifications = _messages.EnumField('ClassificationsValueListEntryValuesEnum', 1, repeated=True)
  excludes = _messages.StringField(2, repeated=True)
  exclusivePatches = _messages.StringField(3, repeated=True)


class YumRepository(_messages.Message):
  r"""Represents a single Yum package repository. This repository is added to
  a repo file that is stored at `/etc/yum.repos.d/google_osconfig.repo`.

  Fields:
    baseUrl: Required. The location of the repository directory.
    displayName: The display name of the repository.
    gpgKeys: URIs of GPG keys.
    id: Required. A one word, unique name for this repository. This is the
      `repo id` in the Yum config file and also the `display_name` if
      `display_name` is omitted. This id is also used as the unique identifier
      when checking for guest policy conflicts.
  """

  baseUrl = _messages.StringField(1)
  displayName = _messages.StringField(2)
  gpgKeys = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)


class YumSettings(_messages.Message):
  r"""Yum patching is performed by executing `yum update`. Additional options
  can be set to control how this is executed. Note that not all settings are
  supported on all platforms.

  Fields:
    excludes: List of packages to exclude from update. These packages are
      excluded by using the yum `--exclude` flag.
    exclusivePackages: An exclusive list of packages to be updated. These are
      the only packages that will be updated. If these packages are not
      installed, they will be ignored. This field must not be specified with
      any other patch configuration fields.
    minimal: Will cause patch to run `yum update-minimal` instead.
    security: Adds the `--security` flag to `yum update`. Not supported on all
      platforms.
  """

  excludes = _messages.StringField(1, repeated=True)
  exclusivePackages = _messages.StringField(2, repeated=True)
  minimal = _messages.BooleanField(3)
  security = _messages.BooleanField(4)


class ZypperRepository(_messages.Message):
  r"""Represents a single Zypper package repository. This repository is added
  to a repo file that is stored at `/etc/zypp/repos.d/google_osconfig.repo`.

  Fields:
    baseUrl: Required. The location of the repository directory.
    displayName: The display name of the repository.
    gpgKeys: URIs of GPG keys.
    id: Required. A one word, unique name for this repository. This is the
      `repo id` in the zypper config file and also the `display_name` if
      `display_name` is omitted. This id is also used as the unique identifier
      when checking for guest policy conflicts.
  """

  baseUrl = _messages.StringField(1)
  displayName = _messages.StringField(2)
  gpgKeys = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)


class ZypperSettings(_messages.Message):
  r"""Zypper patching is performed by running `zypper patch`. See also
  https://en.opensuse.org/SDB:Zypper_manual.

  Fields:
    categories: Install only patches with these categories. Common categories
      include security, recommended, and feature.
    excludes: List of patches to exclude from update.
    exclusivePatches: An exclusive list of patches to be updated. These are
      the only patches that will be installed using 'zypper patch patch:'
      command. This field must not be used with any other patch configuration
      fields.
    severities: Install only patches with these severities. Common severities
      include critical, important, moderate, and low.
    withOptional: Adds the `--with-optional` flag to `zypper patch`.
    withUpdate: Adds the `--with-update` flag, to `zypper patch`.
  """

  categories = _messages.StringField(1, repeated=True)
  excludes = _messages.StringField(2, repeated=True)
  exclusivePatches = _messages.StringField(3, repeated=True)
  severities = _messages.StringField(4, repeated=True)
  withOptional = _messages.BooleanField(5)
  withUpdate = _messages.BooleanField(6)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
