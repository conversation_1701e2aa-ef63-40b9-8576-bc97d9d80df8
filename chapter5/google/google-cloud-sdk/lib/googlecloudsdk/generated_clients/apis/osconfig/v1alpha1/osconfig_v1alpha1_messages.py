"""Generated message classes for osconfig version v1alpha1.

OS management tools that can be used for patch management, patch compliance,
and configuration management on VM instances.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'osconfig'


class AptPackageConfig(_messages.Message):
  r"""A list of packages to install, remove, and their repos for a given
  package manager type.

  Fields:
    packageInstalls: Packages to install. apt-get update && apt-get -y install
      package1 package2 package3
    packageRemovals: Packages to remove. apt-get -y remove package1 package2
      package3
    repositories: Package repositories to configure in the package manager.
      The instance likely already has some defaults set and duplicates are
      acceptable but ignored.
  """

  packageInstalls = _messages.MessageField('Package', 1, repeated=True)
  packageRemovals = _messages.MessageField('Package', 2, repeated=True)
  repositories = _messages.MessageField('AptRepository', 3, repeated=True)


class AptRepository(_messages.Message):
  r"""Represents a single apt package repository. These will be added to a
  repo file that will be managed at
  /etc/apt/sources.list.d/google_osconfig.list

  Enums:
    ArchiveTypeValueValuesEnum: Type of archive files in this repository.
      Unspecified will default to DEB.

  Fields:
    archiveType: Type of archive files in this repository. Unspecified will
      default to DEB.
    components: List of components for this repository. Must contain at least
      one item.
    distribution: Distribution of this repository.
    keyUri: Optional. URI of the key file for this repository. The agent will
      ensure that this key has been downloaded.
    uri: URI for this repository.
  """

  class ArchiveTypeValueValuesEnum(_messages.Enum):
    r"""Type of archive files in this repository. Unspecified will default to
    DEB.

    Values:
      ARCHIVE_TYPE_UNSPECIFIED: Unspecified.
      DEB: Deb.
      DEB_SRC: Deb-src.
    """
    ARCHIVE_TYPE_UNSPECIFIED = 0
    DEB = 1
    DEB_SRC = 2

  archiveType = _messages.EnumField('ArchiveTypeValueValuesEnum', 1)
  components = _messages.StringField(2, repeated=True)
  distribution = _messages.StringField(3)
  keyUri = _messages.StringField(4)
  uri = _messages.StringField(5)


class AptSettings(_messages.Message):
  r"""Apt patching will be performed by executing `apt-get update && apt-get
  upgrade`. Additional options can be set to control how this is executed.

  Enums:
    TypeValueValuesEnum: Optional. By changing the type to DIST, the patching
      will be performed using `apt-get dist-upgrade` instead.

  Fields:
    excludes: List of packages to exclude from update.
    exclusivePackages: An exclusive list of packages to be updated. These are
      the only packages that will be updated. If these packages are not
      installed, they will be ignored. This field cannot be specified with any
      other patch configuration fields.
    type: Optional. By changing the type to DIST, the patching will be
      performed using `apt-get dist-upgrade` instead.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. By changing the type to DIST, the patching will be performed
    using `apt-get dist-upgrade` instead.

    Values:
      TYPE_UNSPECIFIED: By default, upgrade will be performed.
      DIST: Runs `apt-get dist-upgrade`.
      UPGRADE: Runs `apt-get upgrade`.
    """
    TYPE_UNSPECIFIED = 0
    DIST = 1
    UPGRADE = 2

  excludes = _messages.StringField(1, repeated=True)
  exclusivePackages = _messages.StringField(2, repeated=True)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class Assignment(_messages.Message):
  r"""An Assignment maps configurations to instance guest environments.

  Messages:
    LabelsValue: Represents cloud resource labels.

  Fields:
    createTime: Output only. Time this Assignment was created.
    description: Description of the Assignment. Length of the description is
      limited to 1024 characters.
    expression: Optional. A [CEL](https://github.com/google/cel-spec)
      expression used to filter instances when determining which configs
      apply. If omitted, the OsConfigs specified in this assignment will apply
      to all instances under this resource.
    labels: Represents cloud resource labels.
    name: Identifying name for this Assignment.
    osConfigs: List of OsConfigs to configure on the instances. These are
      relative resource names of OsConfigs. For example
      'organizations/1234/osConfigs/foo' or 'projects/12345/osConfigs/foo'. If
      an OsConfig referenced here is deleted it will be ignored when instances
      lookup their configs.
    updateTime: Output only. Last time this Assignment was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Represents cloud resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  expression = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  osConfigs = _messages.StringField(6, repeated=True)
  updateTime = _messages.StringField(7)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs.  If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted.  Example Policy with
  multiple AuditConfigs:      {       "audit_configs": [         {
  "service": "allServices"           "audit_log_configs": [             {
  "log_type": "DATA_READ",               "exempted_members": [
  "user:<EMAIL>"               ]             },             {
  "log_type": "DATA_WRITE",             },             {
  "log_type": "ADMIN_READ",             }           ]         },         {
  "service": "sampleservice.googleapis.com"           "audit_log_configs": [
  {               "log_type": "DATA_READ",             },             {
  "log_type": "DATA_WRITE",               "exempted_members": [
  "user:<EMAIL>"               ]             }           ]         }
  ]     }  For sampleservice, this policy enables DATA_READ, DATA_WRITE and
  ADMIN_READ logging. It <NAME_EMAIL> from DATA_READ logging,
  and <EMAIL> from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example:
  {       "audit_log_configs": [         {           "log_type": "DATA_READ",
  "exempted_members": [             "user:<EMAIL>"           ]
  },         {           "log_type": "DATA_WRITE",         }       ]     }
  This enables 'DATA_READ' and 'DATA_WRITE' logging, while exempting
  <EMAIL> from DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members` with a `role`.

  Fields:
    condition: The condition that is associated with this binding. NOTE: An
      unsatisfied condition will not allow user access via current binding.
      Different bindings, including their conditions, are examined
      independently.
    members: Specifies the identities requesting access for a Cloud Platform
      resource. `members` can have the following values:  * `allUsers`: A
      special identifier that represents anyone who is    on the internet;
      with or without a Google account.  * `allAuthenticatedUsers`: A special
      identifier that represents anyone    who is authenticated with a Google
      account or a service account.  * `user:{emailid}`: An email address that
      represents a specific Google    account. For example,
      `<EMAIL>` .   * `serviceAccount:{emailid}`: An email address
      that represents a service    account. For example, `my-other-
      <EMAIL>`.  * `group:{emailid}`: An email address
      that represents a Google group.    For example, `<EMAIL>`.  *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding.  *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus    unique identifier) representing a service account that has been
      recently    deleted. For example,    `my-other-
      <EMAIL>?uid=123456789012345678901`.    If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding.  * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique    identifier) representing a Google group
      that has been recently    deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If    the group is
      recovered, this value reverts to `group:{emailid}` and the    recovered
      group retains the role in the binding.   * `domain:{domain}`: The G
      Suite domain (primary) that represents all the    users of that domain.
      For example, `google.com` or `example.com`.
    role: Role that is assigned to `members`. For example, `roles/viewer`,
      `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelPatchJobRequest(_messages.Message):
  r"""Message for canceling a patch job."""


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance:      service Foo {
  rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty);     }  The
  JSON representation for `Empty` is empty JSON object `{}`.
  """



class ExecutePatchJobRequest(_messages.Message):
  r"""A request message to initiate patching across GCE instances.

  Fields:
    description: Description of the PatchJob. Length of the description is
      limited to 1024 characters.
    displayName: Display name for this patch job. This does not have to be
      unique.
    dryRun: Should this patch be a dry-run only.  Instances will be contacted,
      but they will do nothing.
    duration: Optional. Duration of the patch job. After the duration ends,
      the patch job will time out.
    filter: Required. There must be at least one instance to patch for this
      job to succeed. This is the same filter used when listing compute
      instances.
    patchConfig: Optional. Patch configuration being applied. If omitted,
      instances will be patched using the default configurations.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  dryRun = _messages.BooleanField(3)
  duration = _messages.StringField(4)
  filter = _messages.StringField(5)
  patchConfig = _messages.MessageField('PatchConfig', 6)


class Expr(_messages.Message):
  r"""Represents an expression text. Example:      title: "User account
  presence"     description: "Determines whether the request has a user
  account"     expression: "size(request.user) > 0"

  Fields:
    description: An optional description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.  The application context of the containing message
      determines which well-known feature set of CEL is supported.
    location: An optional string indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: An optional title for the expression, i.e. a short string
      describing its purpose. This can be used e.g. in UIs which allow to
      enter the expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`. This field is only used by Cloud IAM.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The policy format version to be
      returned.  Valid values are 0, 1, and 3. Requests specifying an invalid
      value will be rejected.  Requests for policies with any conditional
      bindings must specify version 3. Policies without any conditional
      bindings may specify any valid value or leave the field unset.
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GooPackageConfig(_messages.Message):
  r"""A list of packages to install, remove, and their repos for a given
  package manager type.

  Fields:
    packageInstalls: Packages to install. googet -noconfirm install package1
      package2 package3
    packageRemovals: Packages to remove. googet -noconfirm remove package1
      package2 package3
    repositories: Package repositories to configure in the package manager.
      The instance likely already has some defaults set and duplicates are
      acceptable but ignored.
  """

  packageInstalls = _messages.MessageField('Package', 1, repeated=True)
  packageRemovals = _messages.MessageField('Package', 2, repeated=True)
  repositories = _messages.MessageField('GooRepository', 3, repeated=True)


class GooRepository(_messages.Message):
  r"""Represents a Goo package repository. These will be added to a repo file
  that will be managed at C:/ProgramData/GooGet/repos/google_osconfig.repo

  Fields:
    name: The name of the repository.
    url: The url of the repository.
  """

  name = _messages.StringField(1)
  url = _messages.StringField(2)


class GooSettings(_messages.Message):
  r"""Googet patching is performed by running `googet update`."""


class ListAssignmentsResponse(_messages.Message):
  r"""A response message for listing Assignments.

  Fields:
    assignments: The list of Assignments.
    nextPageToken: A pagination token that can be used to get the next page of
      Assignments.
  """

  assignments = _messages.MessageField('Assignment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOsConfigsResponse(_messages.Message):
  r"""A response message for listing OsConfigs.

  Fields:
    nextPageToken: A pagination token that can be used to get the next page of
      OsConfigs.
    osConfigs: The list of OsConfigs.
  """

  nextPageToken = _messages.StringField(1)
  osConfigs = _messages.MessageField('OsConfig', 2, repeated=True)


class ListPatchJobInstanceDetailsResponse(_messages.Message):
  r"""A response message for listing the instances' details for a patch job.

  Fields:
    nextPageToken: A pagination token that can be used to get the next page of
      results.
    patchJobInstanceDetails: The list of instance status.
  """

  nextPageToken = _messages.StringField(1)
  patchJobInstanceDetails = _messages.MessageField('PatchJobInstanceDetails', 2, repeated=True)


class ListPatchJobsResponse(_messages.Message):
  r"""A response message for listing patch jobs.

  Fields:
    nextPageToken: A pagination token that can be used to get the next page of
      results.
    patchJobs: The list of patch jobs.
  """

  nextPageToken = _messages.StringField(1)
  patchJobs = _messages.MessageField('PatchJob', 2, repeated=True)


class LookupConfigsRequest(_messages.Message):
  r"""A request message for getting the configs assigned to the instance.

  Enums:
    ConfigTypesValueListEntryValuesEnum:

  Fields:
    configTypes: Types of configuration system the instance is using. Only
      configs relevant to these configuration types will be returned.
    osInfo: Optional. OS info about the instance that can be used to filter
      its configs. If none is provided, the API will return the configs for
      this instance regardless of its OS.
  """

  class ConfigTypesValueListEntryValuesEnum(_messages.Enum):
    r"""ConfigTypesValueListEntryValuesEnum enum type.

    Values:
      CONFIG_TYPE_UNSPECIFIED: <no description>
      APT: <no description>
      YUM: <no description>
      GOO: <no description>
      WINDOWS_UPDATE: <no description>
      ZYPPER: <no description>
    """
    CONFIG_TYPE_UNSPECIFIED = 0
    APT = 1
    YUM = 2
    GOO = 3
    WINDOWS_UPDATE = 4
    ZYPPER = 5

  configTypes = _messages.EnumField('ConfigTypesValueListEntryValuesEnum', 1, repeated=True)
  osInfo = _messages.MessageField('LookupConfigsRequestOsInfo', 2)


class LookupConfigsRequestOsInfo(_messages.Message):
  r"""Guest information provided to service by agent when requesting
  configurations.

  Fields:
    osArchitecture: Architecture of the OS. Optional.
    osKernel: OS kernel name. Optional.
    osLongName: OS long name. Optional.
    osShortName: OS short name. Optional.
    osVersion: OS version. Optional.
  """

  osArchitecture = _messages.StringField(1)
  osKernel = _messages.StringField(2)
  osLongName = _messages.StringField(3)
  osShortName = _messages.StringField(4)
  osVersion = _messages.StringField(5)


class LookupConfigsResponse(_messages.Message):
  r"""Response with assigned configs for the instance.

  Fields:
    apt: Configs for apt.
    goo: Configs for windows.
    windowsUpdate: Configs for Windows Update.
    yum: Configs for yum.
    zypper: Configs for Zypper.
  """

  apt = _messages.MessageField('AptPackageConfig', 1)
  goo = _messages.MessageField('GooPackageConfig', 2)
  windowsUpdate = _messages.MessageField('WindowsUpdateConfig', 3)
  yum = _messages.MessageField('YumPackageConfig', 4)
  zypper = _messages.MessageField('ZypperPackageConfig', 5)


class OsConfig(_messages.Message):
  r"""An OS Config resource describing a set of OS configs that should be set
  on a group of instances.

  Messages:
    LabelsValue: Represents cloud resource labels.

  Fields:
    apt: Optional package manager configurations for apt.
    createTime: Output only. Time this OsConfig was created.
    description: Description of the OsConfig. Length of the description is
      limited to 1024 characters.
    goo: Optional package manager configurations for windows.
    labels: Represents cloud resource labels.
    name: Identifying name for this OsConfig.
    updateTime: Output only. Last time this OsConfig was updated.
    windowsUpdate: Optional Windows Update configurations.
    yum: Optional package manager configurations for yum.
    zypper: Optional package manager configuration for zypper.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Represents cloud resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  apt = _messages.MessageField('AptPackageConfig', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  goo = _messages.MessageField('GooPackageConfig', 4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  updateTime = _messages.StringField(7)
  windowsUpdate = _messages.MessageField('WindowsUpdateConfig', 8)
  yum = _messages.MessageField('YumPackageConfig', 9)
  zypper = _messages.MessageField('ZypperPackageConfig', 10)


class OsconfigFoldersAssignmentsCreateRequest(_messages.Message):
  r"""A OsconfigFoldersAssignmentsCreateRequest object.

  Fields:
    assignment: A Assignment resource to be passed as the request body.
    parent: The resource name of the parent.
  """

  assignment = _messages.MessageField('Assignment', 1)
  parent = _messages.StringField(2, required=True)


class OsconfigFoldersAssignmentsDeleteRequest(_messages.Message):
  r"""A OsconfigFoldersAssignmentsDeleteRequest object.

  Fields:
    name: The resource name of the Assignment.
  """

  name = _messages.StringField(1, required=True)


class OsconfigFoldersAssignmentsGetRequest(_messages.Message):
  r"""A OsconfigFoldersAssignmentsGetRequest object.

  Fields:
    name: The resource name of the Assignment.
  """

  name = _messages.StringField(1, required=True)


class OsconfigFoldersAssignmentsListRequest(_messages.Message):
  r"""A OsconfigFoldersAssignmentsListRequest object.

  Fields:
    pageSize: The maximum number of Assignments to return.
    pageToken: A pagination token returned from a previous call to
      ListAssignments that indicates where this listing should continue from.
      This field is optional.
    parent: The resource name of the parent.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OsconfigFoldersAssignmentsPatchRequest(_messages.Message):
  r"""A OsconfigFoldersAssignmentsPatchRequest object.

  Fields:
    assignment: A Assignment resource to be passed as the request body.
    name: The resource name of the Assignment.
    updateMask: Field mask that controls which fields of the Assignment should
      be updated.
  """

  assignment = _messages.MessageField('Assignment', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class OsconfigFoldersGetIamPolicyRequest(_messages.Message):
  r"""A OsconfigFoldersGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See the operation documentation for the appropriate value for this
      field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class OsconfigFoldersOsConfigsCreateRequest(_messages.Message):
  r"""A OsconfigFoldersOsConfigsCreateRequest object.

  Fields:
    osConfig: A OsConfig resource to be passed as the request body.
    parent: The resource name of the parent.
  """

  osConfig = _messages.MessageField('OsConfig', 1)
  parent = _messages.StringField(2, required=True)


class OsconfigFoldersOsConfigsDeleteRequest(_messages.Message):
  r"""A OsconfigFoldersOsConfigsDeleteRequest object.

  Fields:
    name: The resource name of the OsConfig.
  """

  name = _messages.StringField(1, required=True)


class OsconfigFoldersOsConfigsGetRequest(_messages.Message):
  r"""A OsconfigFoldersOsConfigsGetRequest object.

  Fields:
    name: The resource name of the OsConfig.
  """

  name = _messages.StringField(1, required=True)


class OsconfigFoldersOsConfigsListRequest(_messages.Message):
  r"""A OsconfigFoldersOsConfigsListRequest object.

  Fields:
    pageSize: The maximum number of OsConfigs to return.
    pageToken: A pagination token returned from a previous call to
      ListOsConfigs that indicates where this listing should continue from.
      This field is optional.
    parent: The resource name of the parent.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OsconfigFoldersOsConfigsPatchRequest(_messages.Message):
  r"""A OsconfigFoldersOsConfigsPatchRequest object.

  Fields:
    name: The resource name of the OsConfig.
    osConfig: A OsConfig resource to be passed as the request body.
    updateMask: Field mask that controls which fields of the OsConfig should
      be updated.
  """

  name = _messages.StringField(1, required=True)
  osConfig = _messages.MessageField('OsConfig', 2)
  updateMask = _messages.StringField(3)


class OsconfigFoldersSetIamPolicyRequest(_messages.Message):
  r"""A OsconfigFoldersSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See the operation documentation for the appropriate value for this
      field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class OsconfigFoldersTestIamPermissionsRequest(_messages.Message):
  r"""A OsconfigFoldersTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See the operation documentation for the appropriate value for
      this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class OsconfigOrganizationsAssignmentsCreateRequest(_messages.Message):
  r"""A OsconfigOrganizationsAssignmentsCreateRequest object.

  Fields:
    assignment: A Assignment resource to be passed as the request body.
    parent: The resource name of the parent.
  """

  assignment = _messages.MessageField('Assignment', 1)
  parent = _messages.StringField(2, required=True)


class OsconfigOrganizationsAssignmentsDeleteRequest(_messages.Message):
  r"""A OsconfigOrganizationsAssignmentsDeleteRequest object.

  Fields:
    name: The resource name of the Assignment.
  """

  name = _messages.StringField(1, required=True)


class OsconfigOrganizationsAssignmentsGetRequest(_messages.Message):
  r"""A OsconfigOrganizationsAssignmentsGetRequest object.

  Fields:
    name: The resource name of the Assignment.
  """

  name = _messages.StringField(1, required=True)


class OsconfigOrganizationsAssignmentsListRequest(_messages.Message):
  r"""A OsconfigOrganizationsAssignmentsListRequest object.

  Fields:
    pageSize: The maximum number of Assignments to return.
    pageToken: A pagination token returned from a previous call to
      ListAssignments that indicates where this listing should continue from.
      This field is optional.
    parent: The resource name of the parent.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OsconfigOrganizationsAssignmentsPatchRequest(_messages.Message):
  r"""A OsconfigOrganizationsAssignmentsPatchRequest object.

  Fields:
    assignment: A Assignment resource to be passed as the request body.
    name: The resource name of the Assignment.
    updateMask: Field mask that controls which fields of the Assignment should
      be updated.
  """

  assignment = _messages.MessageField('Assignment', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class OsconfigOrganizationsGetIamPolicyRequest(_messages.Message):
  r"""A OsconfigOrganizationsGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See the operation documentation for the appropriate value for this
      field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class OsconfigOrganizationsOsConfigsCreateRequest(_messages.Message):
  r"""A OsconfigOrganizationsOsConfigsCreateRequest object.

  Fields:
    osConfig: A OsConfig resource to be passed as the request body.
    parent: The resource name of the parent.
  """

  osConfig = _messages.MessageField('OsConfig', 1)
  parent = _messages.StringField(2, required=True)


class OsconfigOrganizationsOsConfigsDeleteRequest(_messages.Message):
  r"""A OsconfigOrganizationsOsConfigsDeleteRequest object.

  Fields:
    name: The resource name of the OsConfig.
  """

  name = _messages.StringField(1, required=True)


class OsconfigOrganizationsOsConfigsGetRequest(_messages.Message):
  r"""A OsconfigOrganizationsOsConfigsGetRequest object.

  Fields:
    name: The resource name of the OsConfig.
  """

  name = _messages.StringField(1, required=True)


class OsconfigOrganizationsOsConfigsListRequest(_messages.Message):
  r"""A OsconfigOrganizationsOsConfigsListRequest object.

  Fields:
    pageSize: The maximum number of OsConfigs to return.
    pageToken: A pagination token returned from a previous call to
      ListOsConfigs that indicates where this listing should continue from.
      This field is optional.
    parent: The resource name of the parent.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OsconfigOrganizationsOsConfigsPatchRequest(_messages.Message):
  r"""A OsconfigOrganizationsOsConfigsPatchRequest object.

  Fields:
    name: The resource name of the OsConfig.
    osConfig: A OsConfig resource to be passed as the request body.
    updateMask: Field mask that controls which fields of the OsConfig should
      be updated.
  """

  name = _messages.StringField(1, required=True)
  osConfig = _messages.MessageField('OsConfig', 2)
  updateMask = _messages.StringField(3)


class OsconfigOrganizationsSetIamPolicyRequest(_messages.Message):
  r"""A OsconfigOrganizationsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See the operation documentation for the appropriate value for this
      field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class OsconfigOrganizationsTestIamPermissionsRequest(_messages.Message):
  r"""A OsconfigOrganizationsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See the operation documentation for the appropriate value for
      this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class OsconfigProjectsAssignmentsCreateRequest(_messages.Message):
  r"""A OsconfigProjectsAssignmentsCreateRequest object.

  Fields:
    assignment: A Assignment resource to be passed as the request body.
    parent: The resource name of the parent.
  """

  assignment = _messages.MessageField('Assignment', 1)
  parent = _messages.StringField(2, required=True)


class OsconfigProjectsAssignmentsDeleteRequest(_messages.Message):
  r"""A OsconfigProjectsAssignmentsDeleteRequest object.

  Fields:
    name: The resource name of the Assignment.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsAssignmentsGetRequest(_messages.Message):
  r"""A OsconfigProjectsAssignmentsGetRequest object.

  Fields:
    name: The resource name of the Assignment.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsAssignmentsListRequest(_messages.Message):
  r"""A OsconfigProjectsAssignmentsListRequest object.

  Fields:
    pageSize: The maximum number of Assignments to return.
    pageToken: A pagination token returned from a previous call to
      ListAssignments that indicates where this listing should continue from.
      This field is optional.
    parent: The resource name of the parent.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OsconfigProjectsAssignmentsPatchRequest(_messages.Message):
  r"""A OsconfigProjectsAssignmentsPatchRequest object.

  Fields:
    assignment: A Assignment resource to be passed as the request body.
    name: The resource name of the Assignment.
    updateMask: Field mask that controls which fields of the Assignment should
      be updated.
  """

  assignment = _messages.MessageField('Assignment', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class OsconfigProjectsGetIamPolicyRequest(_messages.Message):
  r"""A OsconfigProjectsGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See the operation documentation for the appropriate value for this
      field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class OsconfigProjectsOsConfigsCreateRequest(_messages.Message):
  r"""A OsconfigProjectsOsConfigsCreateRequest object.

  Fields:
    osConfig: A OsConfig resource to be passed as the request body.
    parent: The resource name of the parent.
  """

  osConfig = _messages.MessageField('OsConfig', 1)
  parent = _messages.StringField(2, required=True)


class OsconfigProjectsOsConfigsDeleteRequest(_messages.Message):
  r"""A OsconfigProjectsOsConfigsDeleteRequest object.

  Fields:
    name: The resource name of the OsConfig.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsOsConfigsGetRequest(_messages.Message):
  r"""A OsconfigProjectsOsConfigsGetRequest object.

  Fields:
    name: The resource name of the OsConfig.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsOsConfigsListRequest(_messages.Message):
  r"""A OsconfigProjectsOsConfigsListRequest object.

  Fields:
    pageSize: The maximum number of OsConfigs to return.
    pageToken: A pagination token returned from a previous call to
      ListOsConfigs that indicates where this listing should continue from.
      This field is optional.
    parent: The resource name of the parent.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OsconfigProjectsOsConfigsPatchRequest(_messages.Message):
  r"""A OsconfigProjectsOsConfigsPatchRequest object.

  Fields:
    name: The resource name of the OsConfig.
    osConfig: A OsConfig resource to be passed as the request body.
    updateMask: Field mask that controls which fields of the OsConfig should
      be updated.
  """

  name = _messages.StringField(1, required=True)
  osConfig = _messages.MessageField('OsConfig', 2)
  updateMask = _messages.StringField(3)


class OsconfigProjectsPatchJobsCancelRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsCancelRequest object.

  Fields:
    cancelPatchJobRequest: A CancelPatchJobRequest resource to be passed as
      the request body.
    name: Name of the patch in the form `projects/*/patchJobs/*`
  """

  cancelPatchJobRequest = _messages.MessageField('CancelPatchJobRequest', 1)
  name = _messages.StringField(2, required=True)


class OsconfigProjectsPatchJobsExecuteRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsExecuteRequest object.

  Fields:
    executePatchJobRequest: A ExecutePatchJobRequest resource to be passed as
      the request body.
    parent: The project in which to run this patch in the form `projects/*`
  """

  executePatchJobRequest = _messages.MessageField('ExecutePatchJobRequest', 1)
  parent = _messages.StringField(2, required=True)


class OsconfigProjectsPatchJobsGetRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsGetRequest object.

  Fields:
    name: Name of the patch in the form `projects/*/patchJobs/*`
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsPatchJobsInstanceDetailsListRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsInstanceDetailsListRequest object.

  Fields:
    filter: A filter expression that filters results listed in the response.
      Supports filtering results by instance zone, name, state, or
      failure_reason.
    pageSize: The maximum number of instance details records to return.
    pageToken: A pagination token returned from a previous call that indicates
      where this listing should continue from. This field is optional.
    parent: In the form of `projects/*/patchJobs/*`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class OsconfigProjectsPatchJobsListRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsListRequest object.

  Fields:
    pageSize: The maximum number of instance status to return.
    pageToken: A pagination token returned from a previous call that indicates
      where this listing should continue from. This field is optional.
    parent: In the form of `projects/*`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OsconfigProjectsSetIamPolicyRequest(_messages.Message):
  r"""A OsconfigProjectsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See the operation documentation for the appropriate value for this
      field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class OsconfigProjectsTestIamPermissionsRequest(_messages.Message):
  r"""A OsconfigProjectsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See the operation documentation for the appropriate value for
      this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class OsconfigProjectsZonesInstancesLookupConfigsRequest(_messages.Message):
  r"""A OsconfigProjectsZonesInstancesLookupConfigsRequest object.

  Fields:
    lookupConfigsRequest: A LookupConfigsRequest resource to be passed as the
      request body.
    resource: The resource name for the instance.
  """

  lookupConfigsRequest = _messages.MessageField('LookupConfigsRequest', 1)
  resource = _messages.StringField(2, required=True)


class OsconfigProjectsZonesInstancesReportPatchJobInstanceDetailsRequest(_messages.Message):
  r"""A OsconfigProjectsZonesInstancesReportPatchJobInstanceDetailsRequest
  object.

  Fields:
    reportPatchJobInstanceDetailsRequest: A
      ReportPatchJobInstanceDetailsRequest resource to be passed as the
      request body.
    resource: The instance reporting its status in the form
      `projects/*/zones/*/instances/*`
  """

  reportPatchJobInstanceDetailsRequest = _messages.MessageField('ReportPatchJobInstanceDetailsRequest', 1)
  resource = _messages.StringField(2, required=True)


class Package(_messages.Message):
  r"""Package is a reference to the actual package to be installed or removed.

  Fields:
    name: The name of the package.
  """

  name = _messages.StringField(1)


class PatchConfig(_messages.Message):
  r"""Patch configuration specifications. Contains details on specifically how
  to apply the patch(es) to an instance.

  Enums:
    RebootConfigValueValuesEnum: Optional. Post-patch reboot settings.

  Fields:
    apt: Apt update settings. Use this override the default apt patch rules.
    goo: Goo update settings. Use this override the default goo patch rules.
    rebootConfig: Optional. Post-patch reboot settings.
    retryStrategy: Optional. Retry strategy can be defined to have the agent
      retry patching during the window if patching fails. If omitted, the
      agent will use its default retry strategy.
    windowsUpdate: Windows update settings. Use this override the default
      windows patch rules.
    yum: Yum update settings. Use this override the default yum patch rules.
    zypper: Zypper update settings. Use this override the default zypper patch
      rules.
  """

  class RebootConfigValueValuesEnum(_messages.Enum):
    r"""Optional. Post-patch reboot settings.

    Values:
      REBOOT_CONFIG_UNSPECIFIED: The default behavior is DEFAULT.
      DEFAULT: The agent will decide if a reboot is necessary by checking well
        known signals such as registry keys or `/var/run/reboot-required`.
      ALWAYS: Always reboot the machine after the update has completed.
      NEVER: Never reboot the machine after the update has completed.
    """
    REBOOT_CONFIG_UNSPECIFIED = 0
    DEFAULT = 1
    ALWAYS = 2
    NEVER = 3

  apt = _messages.MessageField('AptSettings', 1)
  goo = _messages.MessageField('GooSettings', 2)
  rebootConfig = _messages.EnumField('RebootConfigValueValuesEnum', 3)
  retryStrategy = _messages.MessageField('RetryStrategy', 4)
  windowsUpdate = _messages.MessageField('WindowsUpdateSettings', 5)
  yum = _messages.MessageField('YumSettings', 6)
  zypper = _messages.MessageField('ZypperSettings', 7)


class PatchJob(_messages.Message):
  r"""A high level representation of a patch job that is either in progress or
  has completed.  Instances' details are not included in the job. To paginate
  through instance details, use ListPatchJobInstanceDetails.

  Enums:
    StateValueValuesEnum: The current state of the PatchJob.

  Fields:
    createTime: Time this PatchJob was created.
    description: Description of the patch job. Length of the description is
      limited to 1024 characters.
    displayName: Display name for this patch job. This is not a unique
      identifier.
    dryRun: If this patch job is a dry run, the agent will report that it has
      finished without running any updates on the VM.
    duration: Duration of the patch job. After the duration ends, the patch
      job will time out.
    errorMessage: If this patch job failed, this message will provide
      information about the failure.
    filter: There must be at least one instance to patch for this job to
      succeed. This is the same filter used when listing compute instances.
    instanceDetailsSummary: Summary of instance details.
    name: Unique identifier for this patch job in the form
      `projects/*/patchJobs/*`
    patchConfig: Patch configuration being applied.
    percentComplete: Reflects the overall progress of the patch job in the
      range of 0.0 being no progress to 100.0 being complete.
    state: The current state of the PatchJob.
    updateTime: Last time this PatchJob was updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the PatchJob.

    Values:
      STATE_UNSPECIFIED: State must be specified.
      STARTED: The patch job was successfully initiated.
      INSTANCE_LOOKUP: The patch job is looking up instances to run the patch
        on.
      PATCHING: Instances are being patched.
      SUCCEEDED: Patch job completed successfully.
      COMPLETED_WITH_ERRORS: Patch job completed but there were errors.
      CANCELED: The patch job was canceled.
      TIMED_OUT: The patch job has timed out.
    """
    STATE_UNSPECIFIED = 0
    STARTED = 1
    INSTANCE_LOOKUP = 2
    PATCHING = 3
    SUCCEEDED = 4
    COMPLETED_WITH_ERRORS = 5
    CANCELED = 6
    TIMED_OUT = 7

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  dryRun = _messages.BooleanField(4)
  duration = _messages.StringField(5)
  errorMessage = _messages.StringField(6)
  filter = _messages.StringField(7)
  instanceDetailsSummary = _messages.MessageField('PatchJobInstanceDetailsSummary', 8)
  name = _messages.StringField(9)
  patchConfig = _messages.MessageField('PatchConfig', 10)
  percentComplete = _messages.FloatField(11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  updateTime = _messages.StringField(13)


class PatchJobInstanceDetails(_messages.Message):
  r"""Patch details of an instance.

  Enums:
    StateValueValuesEnum: Current state of instance patch.

  Fields:
    attemptCount: Number of times the agent attempted to apply the patch.
    failureReason: If the patch has failed, this is the reason.
    instanceSystemId: The unique, system-generated identifier for the
      instance.
    name: The instance name in the form `projects/*/zones/*/instances/*`
    state: Current state of instance patch.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Current state of instance patch.

    Values:
      PATCH_STATE_UNSPECIFIED: Unspecified.
      PENDING: The instance has not been notified yet.
      INACTIVE: Instance is inactive and cannot be patched.
      NOTIFIED: The instance has been notified that it should patch.
      STARTED: The instance has started the patching process.
      DOWNLOADING_PATCHES: The instance is downloading patches.
      APPLYING_PATCHES: The instance is applying patches.
      REBOOTING: The instance is rebooting.
      SUCCEEDED: The instance has completed applying patches.
      SUCCEEDED_REBOOT_REQUIRED: The instance has completed applying patches
        but a reboot is required.
      FAILED: The instance has failed to apply the patch.
      ACKED: The instance acked the notification and will start shortly.
      TIMED_OUT: The instance exceeded the time out while applying the patch.
    """
    PATCH_STATE_UNSPECIFIED = 0
    PENDING = 1
    INACTIVE = 2
    NOTIFIED = 3
    STARTED = 4
    DOWNLOADING_PATCHES = 5
    APPLYING_PATCHES = 6
    REBOOTING = 7
    SUCCEEDED = 8
    SUCCEEDED_REBOOT_REQUIRED = 9
    FAILED = 10
    ACKED = 11
    TIMED_OUT = 12

  attemptCount = _messages.IntegerField(1)
  failureReason = _messages.StringField(2)
  instanceSystemId = _messages.StringField(3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class PatchJobInstanceDetailsSummary(_messages.Message):
  r"""A summary of the current patch state across all instances this patch job
  affects. Contains counts of instances in different states. These states map
  to InstancePatchState. List patch job instance details to see the specific
  states of each instance.

  Fields:
    instancesAcked: Number of instances that have acked and will start
      shortly.
    instancesApplyingPatches: Number of instances that are applying patches.
    instancesDownloadingPatches: Number of instances that are downloading
      patches.
    instancesFailed: Number of instances that failed.
    instancesInactive: Number of instances that are inactive.
    instancesNotified: Number of instances notified about patch job.
    instancesPending: Number of instances pending patch job.
    instancesRebooting: Number of instances rebooting.
    instancesStarted: Number of instances that have started.
    instancesSucceeded: Number of instances that have completed successfully.
    instancesSucceededRebootRequired: Number of instances that require reboot.
    instancesTimedOut: Number of instances that exceeded the time out while
      applying the patch.
  """

  instancesAcked = _messages.IntegerField(1)
  instancesApplyingPatches = _messages.IntegerField(2)
  instancesDownloadingPatches = _messages.IntegerField(3)
  instancesFailed = _messages.IntegerField(4)
  instancesInactive = _messages.IntegerField(5)
  instancesNotified = _messages.IntegerField(6)
  instancesPending = _messages.IntegerField(7)
  instancesRebooting = _messages.IntegerField(8)
  instancesStarted = _messages.IntegerField(9)
  instancesSucceeded = _messages.IntegerField(10)
  instancesSucceededRebootRequired = _messages.IntegerField(11)
  instancesTimedOut = _messages.IntegerField(12)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources.   A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members` to a single `role`.
  Members can be user accounts, service accounts, Google groups, and domains
  (such as G Suite). A `role` is a named list of permissions; each `role` can
  be an IAM predefined role or a user-created custom role.  Optionally, a
  `binding` can specify a `condition`, which is a logical expression that
  allows access to a resource only if the expression evaluates to `true`. A
  condition can add constraints based on attributes of the request, the
  resource, or both.  **JSON example:**      {       "bindings": [         {
  "role": "roles/resourcemanager.organizationAdmin",           "members": [
  "user:<EMAIL>",             "group:<EMAIL>",
  "domain:google.com",             "serviceAccount:my-project-
  <EMAIL>"           ]         },         {
  "role": "roles/resourcemanager.organizationViewer",           "members":
  ["user:<EMAIL>"],           "condition": {             "title":
  "expirable access",             "description": "Does not grant access after
  Sep 2020",             "expression": "request.time <
  timestamp('2020-10-01T00:00:00.000Z')",           }         }       ],
  "etag": "BwWWja0YfJA=",       "version": 3     }  **YAML example:**
  bindings:     - members:       - user:<EMAIL>       -
  group:<EMAIL>       - domain:google.com       -
  serviceAccount:<EMAIL>       role:
  roles/resourcemanager.organizationAdmin     - members:       -
  user:<EMAIL>       role: roles/resourcemanager.organizationViewer
  condition:         title: expirable access         description: Does not
  grant access after Sep 2020         expression: request.time <
  timestamp('2020-10-01T00:00:00.000Z')     - etag: BwWWja0YfJA=     -
  version: 3  For a description of IAM and its features, see the [IAM
  documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members` to a `role`. Optionally, may
      specify a `condition` that determines how and when the `bindings` are
      applied. Each of the `bindings` must contain at least one member.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy.  **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy.  Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected.  Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations:  * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy   that includes conditions  **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost.  If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset.
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ReportPatchJobInstanceDetailsRequest(_messages.Message):
  r"""Request to report the patch status for an instance.

  Enums:
    StateValueValuesEnum: State of current patch execution on the instance.

  Fields:
    attemptCount: Number of times the agent attempted to apply the patch.
    failureReason: Reason for failure.
    instanceIdToken: This is the GCE instance identity token described in
      https://cloud.google.com/compute/docs/instances/verifying-instance-
      identity where the audience is 'osconfig.googleapis.com' and the format
      is 'full'.
    instanceSystemId: Required. The unique, system-generated identifier for
      the instance.  This is the unchangeable, auto-generated ID assigned to
      the instance upon creation. This is needed here because GCE instance
      names are not tombstoned; it is possible to delete an instance and
      create a new one with the same name; this provides a mechanism for this
      API to identify distinct instances in this case.
    patchJob: Unique identifier of the patch job this request applies to.
    state: State of current patch execution on the instance.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of current patch execution on the instance.

    Values:
      PATCH_STATE_UNSPECIFIED: Unspecified.
      PENDING: The instance has not been notified yet.
      INACTIVE: Instance is inactive and cannot be patched.
      NOTIFIED: The instance has been notified that it should patch.
      STARTED: The instance has started the patching process.
      DOWNLOADING_PATCHES: The instance is downloading patches.
      APPLYING_PATCHES: The instance is applying patches.
      REBOOTING: The instance is rebooting.
      SUCCEEDED: The instance has completed applying patches.
      SUCCEEDED_REBOOT_REQUIRED: The instance has completed applying patches
        but a reboot is required.
      FAILED: The instance has failed to apply the patch.
      ACKED: The instance acked the notification and will start shortly.
      TIMED_OUT: The instance exceeded the time out while applying the patch.
    """
    PATCH_STATE_UNSPECIFIED = 0
    PENDING = 1
    INACTIVE = 2
    NOTIFIED = 3
    STARTED = 4
    DOWNLOADING_PATCHES = 5
    APPLYING_PATCHES = 6
    REBOOTING = 7
    SUCCEEDED = 8
    SUCCEEDED_REBOOT_REQUIRED = 9
    FAILED = 10
    ACKED = 11
    TIMED_OUT = 12

  attemptCount = _messages.IntegerField(1)
  failureReason = _messages.StringField(2)
  instanceIdToken = _messages.StringField(3)
  instanceSystemId = _messages.StringField(4)
  patchJob = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class ReportPatchJobInstanceDetailsResponse(_messages.Message):
  r"""Response from reporting instance patch details. Includes information the
  agent needs to continue or stop patching.

  Enums:
    PatchJobStateValueValuesEnum: State of the overall patch. If the patch is
      no longer active, the agent should not begin a new patch step.

  Fields:
    dryRun: If this patch job is a dry run, the agent will report its status
      as it goes through the motions but won't actually run any updates or
      perform any reboots.
    patchConfig: Patch configuration the agent should apply.
    patchJob: Unique identifier for the current patch job.
    patchJobState: State of the overall patch. If the patch is no longer
      active, the agent should not begin a new patch step.
  """

  class PatchJobStateValueValuesEnum(_messages.Enum):
    r"""State of the overall patch. If the patch is no longer active, the
    agent should not begin a new patch step.

    Values:
      PATCH_JOB_STATE_UNSPECIFIED: Unspecified is invalid.
      ACTIVE: The patch job is running. Instances will continue to run patch
        job steps.
      COMPLETED: The patch job is complete.
    """
    PATCH_JOB_STATE_UNSPECIFIED = 0
    ACTIVE = 1
    COMPLETED = 2

  dryRun = _messages.BooleanField(1)
  patchConfig = _messages.MessageField('PatchConfig', 2)
  patchJob = _messages.StringField(3)
  patchJobState = _messages.EnumField('PatchJobStateValueValuesEnum', 4)


class RetryStrategy(_messages.Message):
  r"""The strategy for retrying failed patches during the patch window.

  Fields:
    enabled: If true, the agent will continue to try and patch until the
      window has ended.
  """

  enabled = _messages.BooleanField(1)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Cloud Platform services (such as Projects)
      might reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: paths: "bindings, etag"
      This field is only used by Cloud IAM.
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as '*' or 'storage.*') are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class WindowsUpdateConfig(_messages.Message):
  r"""Configuration settings for the Windows update.

  Fields:
    windowsUpdateServerUri: Optional URI of Windows update server. This sets
      the registry value `WUServer` under
      `HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate`.
  """

  windowsUpdateServerUri = _messages.StringField(1)


class WindowsUpdateSettings(_messages.Message):
  r"""Windows patching is performed using the Windows Update Agent.

  Enums:
    ClassificationsValueListEntryValuesEnum:

  Fields:
    classifications: Only apply updates of these windows update
      classifications. If empty, all updates will be applied.
    excludes: List of KBs to exclude from update.
    exclusivePatches: An exclusive list of kbs to be updated. These are the
      only patches that will be updated. This field must not be used with
      other patch configurations.
  """

  class ClassificationsValueListEntryValuesEnum(_messages.Enum):
    r"""ClassificationsValueListEntryValuesEnum enum type.

    Values:
      CLASSIFICATION_UNSPECIFIED: <no description>
      CRITICAL: <no description>
      SECURITY: <no description>
      DEFINITION: <no description>
      DRIVER: <no description>
      FEATURE_PACK: <no description>
      SERVICE_PACK: <no description>
      TOOL: <no description>
      UPDATE_ROLLUP: <no description>
      UPDATE: <no description>
    """
    CLASSIFICATION_UNSPECIFIED = 0
    CRITICAL = 1
    SECURITY = 2
    DEFINITION = 3
    DRIVER = 4
    FEATURE_PACK = 5
    SERVICE_PACK = 6
    TOOL = 7
    UPDATE_ROLLUP = 8
    UPDATE = 9

  classifications = _messages.EnumField('ClassificationsValueListEntryValuesEnum', 1, repeated=True)
  excludes = _messages.StringField(2, repeated=True)
  exclusivePatches = _messages.StringField(3, repeated=True)


class YumPackageConfig(_messages.Message):
  r"""A list of packages to install, remove, and their repos for a given
  package manager type.

  Fields:
    packageInstalls: Packages to install. yum -y install package1 package2
      package3
    packageRemovals: Packages to remove. yum -y remove package1 package2
      package3
    repositories: Package repositories to configure in the package manager.
      The instance likely already has some defaults set and duplicates are
      acceptable but ignored.
  """

  packageInstalls = _messages.MessageField('Package', 1, repeated=True)
  packageRemovals = _messages.MessageField('Package', 2, repeated=True)
  repositories = _messages.MessageField('YumRepository', 3, repeated=True)


class YumRepository(_messages.Message):
  r"""Represents a single yum package repository. These will be added to a
  repo file that will be managed a /etc/yum.repos.d/google_osconfig.repo

  Fields:
    baseUrl: Required. The location of the repository directory.
    displayName: Optional. If omitted, the id will be used for the name.
    gpgKeys: Optional. URIs of GPG keys.
    id: Required. A one word, unique name for this repository. This will be
      the `repo id` in the yum config file and also the `display_name` if
      `display_name` is omitted.
  """

  baseUrl = _messages.StringField(1)
  displayName = _messages.StringField(2)
  gpgKeys = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)


class YumSettings(_messages.Message):
  r"""Yum patching will be performed by executing `yum update`. Additional
  options can be set to control how this is executed.  Note that not all
  settings are supported on all platforms.

  Fields:
    excludes: List of packages to exclude from update. These packages will be
      excluded by using the yum `--exclude` field.
    exclusivePackages: An exclusive list of packages to be updated. These are
      the only packages that will be updated. If these packages are not
      installed, they will be ignored. This field must not be specified with
      any other patch configuration fields.
    minimal: Optional. Will cause patch to run `yum update-minimal` instead.
    security: Optional. Adds the `--security` flag to `yum update`. Not
      supported on all platforms.
  """

  excludes = _messages.StringField(1, repeated=True)
  exclusivePackages = _messages.StringField(2, repeated=True)
  minimal = _messages.BooleanField(3)
  security = _messages.BooleanField(4)


class ZypperPackageConfig(_messages.Message):
  r"""A list of packages to install, remove, and their repos for a given
  package manager type.

  Fields:
    packageInstalls: Packages to install. zypper install package1 package2
      package3
    packageRemovals: Packages to remove. zypper rm package1 package2
    repositories: Package repositories to configure in the package manager.
      The instance likely already has some defaults set and duplicates are
      acceptable but ignored.
  """

  packageInstalls = _messages.MessageField('Package', 1, repeated=True)
  packageRemovals = _messages.MessageField('Package', 2, repeated=True)
  repositories = _messages.MessageField('ZypperRepository', 3, repeated=True)


class ZypperRepository(_messages.Message):
  r"""Represents a single zypper package repository. These will be added to a
  repo file that will be managed a /etc/zypp/repos.d/google_osconfig.repo

  Fields:
    baseUrl: Required. The location of the repository directory.
    displayName: Optional. If omitted, the id will be used for the name.
    gpgKeys: Optional. URIs of GPG keys.
    id: Required. A one word, unique name for this repository. This will be
      the `repo id` in the zypper config file and also the `display_name` if
      `display_name` is omitted.
  """

  baseUrl = _messages.StringField(1)
  displayName = _messages.StringField(2)
  gpgKeys = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)


class ZypperSettings(_messages.Message):
  r"""Zypper patching is performed by running `zypper patch`. See also
  https://en.opensuse.org/SDB:Zypper_manual.

  Fields:
    categories: Optional. Install only patches with these categories. Common
      categories include security, recommended, and feature.
    excludes: List of patches to exclude from update.
    exclusivePatches: An exclusive list of patches to be updated. These are
      the only patches that will be installed using 'zypper patch
      patch:<patch_name>' command. This field must not be used with any other
      patch configuration fields.
    severities: Optional. Install only patches with these severities. Common
      severities include critical, important, moderate, and low.
    withOptional: Optional. Adds the `--with-optional` flag to `zypper patch`.
    withUpdate: Optional. Adds the `--with-update` flag, to `zypper patch`.
  """

  categories = _messages.StringField(1, repeated=True)
  excludes = _messages.StringField(2, repeated=True)
  exclusivePatches = _messages.StringField(3, repeated=True)
  severities = _messages.StringField(4, repeated=True)
  withOptional = _messages.BooleanField(5)
  withUpdate = _messages.BooleanField(6)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
