"""Generated message classes for osconfig version v1.

OS management tools that can be used for patch management, patch compliance,
and configuration management on VM instances.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'osconfig'


class AptSettings(_messages.Message):
  r"""Apt patching is completed by executing `apt-get update && apt-get
  upgrade`. Additional options can be set to control how this is executed.

  Enums:
    TypeValueValuesEnum: By changing the type to DIST, the patching is
      performed using `apt-get dist-upgrade` instead.

  Fields:
    excludes: List of packages to exclude from update. These packages will be
      excluded
    exclusivePackages: An exclusive list of packages to be updated. These are
      the only packages that will be updated. If these packages are not
      installed, they will be ignored. This field cannot be specified with any
      other patch configuration fields.
    type: By changing the type to DIST, the patching is performed using `apt-
      get dist-upgrade` instead.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""By changing the type to DIST, the patching is performed using `apt-get
    dist-upgrade` instead.

    Values:
      TYPE_UNSPECIFIED: By default, upgrade will be performed.
      DIST: Runs `apt-get dist-upgrade`.
      UPGRADE: Runs `apt-get upgrade`.
    """
    TYPE_UNSPECIFIED = 0
    DIST = 1
    UPGRADE = 2

  excludes = _messages.StringField(1, repeated=True)
  exclusivePackages = _messages.StringField(2, repeated=True)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class CVSSv3(_messages.Message):
  r"""Common Vulnerability Scoring System version 3. For details, see
  https://www.first.org/cvss/specification-document

  Enums:
    AttackComplexityValueValuesEnum: This metric describes the conditions
      beyond the attacker's control that must exist in order to exploit the
      vulnerability.
    AttackVectorValueValuesEnum: This metric reflects the context by which
      vulnerability exploitation is possible.
    AvailabilityImpactValueValuesEnum: This metric measures the impact to the
      availability of the impacted component resulting from a successfully
      exploited vulnerability.
    ConfidentialityImpactValueValuesEnum: This metric measures the impact to
      the confidentiality of the information resources managed by a software
      component due to a successfully exploited vulnerability.
    IntegrityImpactValueValuesEnum: This metric measures the impact to
      integrity of a successfully exploited vulnerability.
    PrivilegesRequiredValueValuesEnum: This metric describes the level of
      privileges an attacker must possess before successfully exploiting the
      vulnerability.
    ScopeValueValuesEnum: The Scope metric captures whether a vulnerability in
      one vulnerable component impacts resources in components beyond its
      security scope.
    UserInteractionValueValuesEnum: This metric captures the requirement for a
      human user, other than the attacker, to participate in the successful
      compromise of the vulnerable component.

  Fields:
    attackComplexity: This metric describes the conditions beyond the
      attacker's control that must exist in order to exploit the
      vulnerability.
    attackVector: This metric reflects the context by which vulnerability
      exploitation is possible.
    availabilityImpact: This metric measures the impact to the availability of
      the impacted component resulting from a successfully exploited
      vulnerability.
    baseScore: The base score is a function of the base metric scores.
      https://www.first.org/cvss/specification-document#Base-Metrics
    confidentialityImpact: This metric measures the impact to the
      confidentiality of the information resources managed by a software
      component due to a successfully exploited vulnerability.
    exploitabilityScore: The Exploitability sub-score equation is derived from
      the Base Exploitability metrics.
      https://www.first.org/cvss/specification-document#2-1-Exploitability-
      Metrics
    impactScore: The Impact sub-score equation is derived from the Base Impact
      metrics.
    integrityImpact: This metric measures the impact to integrity of a
      successfully exploited vulnerability.
    privilegesRequired: This metric describes the level of privileges an
      attacker must possess before successfully exploiting the vulnerability.
    scope: The Scope metric captures whether a vulnerability in one vulnerable
      component impacts resources in components beyond its security scope.
    userInteraction: This metric captures the requirement for a human user,
      other than the attacker, to participate in the successful compromise of
      the vulnerable component.
  """

  class AttackComplexityValueValuesEnum(_messages.Enum):
    r"""This metric describes the conditions beyond the attacker's control
    that must exist in order to exploit the vulnerability.

    Values:
      ATTACK_COMPLEXITY_UNSPECIFIED: Invalid value.
      ATTACK_COMPLEXITY_LOW: Specialized access conditions or extenuating
        circumstances do not exist. An attacker can expect repeatable success
        when attacking the vulnerable component.
      ATTACK_COMPLEXITY_HIGH: A successful attack depends on conditions beyond
        the attacker's control. That is, a successful attack cannot be
        accomplished at will, but requires the attacker to invest in some
        measurable amount of effort in preparation or execution against the
        vulnerable component before a successful attack can be expected.
    """
    ATTACK_COMPLEXITY_UNSPECIFIED = 0
    ATTACK_COMPLEXITY_LOW = 1
    ATTACK_COMPLEXITY_HIGH = 2

  class AttackVectorValueValuesEnum(_messages.Enum):
    r"""This metric reflects the context by which vulnerability exploitation
    is possible.

    Values:
      ATTACK_VECTOR_UNSPECIFIED: Invalid value.
      ATTACK_VECTOR_NETWORK: The vulnerable component is bound to the network
        stack and the set of possible attackers extends beyond the other
        options listed below, up to and including the entire Internet.
      ATTACK_VECTOR_ADJACENT: The vulnerable component is bound to the network
        stack, but the attack is limited at the protocol level to a logically
        adjacent topology.
      ATTACK_VECTOR_LOCAL: The vulnerable component is not bound to the
        network stack and the attacker's path is via read/write/execute
        capabilities.
      ATTACK_VECTOR_PHYSICAL: The attack requires the attacker to physically
        touch or manipulate the vulnerable component.
    """
    ATTACK_VECTOR_UNSPECIFIED = 0
    ATTACK_VECTOR_NETWORK = 1
    ATTACK_VECTOR_ADJACENT = 2
    ATTACK_VECTOR_LOCAL = 3
    ATTACK_VECTOR_PHYSICAL = 4

  class AvailabilityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to the availability of the impacted
    component resulting from a successfully exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class ConfidentialityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to the confidentiality of the
    information resources managed by a software component due to a
    successfully exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class IntegrityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to integrity of a successfully
    exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class PrivilegesRequiredValueValuesEnum(_messages.Enum):
    r"""This metric describes the level of privileges an attacker must possess
    before successfully exploiting the vulnerability.

    Values:
      PRIVILEGES_REQUIRED_UNSPECIFIED: Invalid value.
      PRIVILEGES_REQUIRED_NONE: The attacker is unauthorized prior to attack,
        and therefore does not require any access to settings or files of the
        vulnerable system to carry out an attack.
      PRIVILEGES_REQUIRED_LOW: The attacker requires privileges that provide
        basic user capabilities that could normally affect only settings and
        files owned by a user. Alternatively, an attacker with Low privileges
        has the ability to access only non-sensitive resources.
      PRIVILEGES_REQUIRED_HIGH: The attacker requires privileges that provide
        significant (e.g., administrative) control over the vulnerable
        component allowing access to component-wide settings and files.
    """
    PRIVILEGES_REQUIRED_UNSPECIFIED = 0
    PRIVILEGES_REQUIRED_NONE = 1
    PRIVILEGES_REQUIRED_LOW = 2
    PRIVILEGES_REQUIRED_HIGH = 3

  class ScopeValueValuesEnum(_messages.Enum):
    r"""The Scope metric captures whether a vulnerability in one vulnerable
    component impacts resources in components beyond its security scope.

    Values:
      SCOPE_UNSPECIFIED: Invalid value.
      SCOPE_UNCHANGED: An exploited vulnerability can only affect resources
        managed by the same security authority.
      SCOPE_CHANGED: An exploited vulnerability can affect resources beyond
        the security scope managed by the security authority of the vulnerable
        component.
    """
    SCOPE_UNSPECIFIED = 0
    SCOPE_UNCHANGED = 1
    SCOPE_CHANGED = 2

  class UserInteractionValueValuesEnum(_messages.Enum):
    r"""This metric captures the requirement for a human user, other than the
    attacker, to participate in the successful compromise of the vulnerable
    component.

    Values:
      USER_INTERACTION_UNSPECIFIED: Invalid value.
      USER_INTERACTION_NONE: The vulnerable system can be exploited without
        interaction from any user.
      USER_INTERACTION_REQUIRED: Successful exploitation of this vulnerability
        requires a user to take some action before the vulnerability can be
        exploited.
    """
    USER_INTERACTION_UNSPECIFIED = 0
    USER_INTERACTION_NONE = 1
    USER_INTERACTION_REQUIRED = 2

  attackComplexity = _messages.EnumField('AttackComplexityValueValuesEnum', 1)
  attackVector = _messages.EnumField('AttackVectorValueValuesEnum', 2)
  availabilityImpact = _messages.EnumField('AvailabilityImpactValueValuesEnum', 3)
  baseScore = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  confidentialityImpact = _messages.EnumField('ConfidentialityImpactValueValuesEnum', 5)
  exploitabilityScore = _messages.FloatField(6, variant=_messages.Variant.FLOAT)
  impactScore = _messages.FloatField(7, variant=_messages.Variant.FLOAT)
  integrityImpact = _messages.EnumField('IntegrityImpactValueValuesEnum', 8)
  privilegesRequired = _messages.EnumField('PrivilegesRequiredValueValuesEnum', 9)
  scope = _messages.EnumField('ScopeValueValuesEnum', 10)
  userInteraction = _messages.EnumField('UserInteractionValueValuesEnum', 11)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CancelPatchJobRequest(_messages.Message):
  r"""Message for canceling a patch job."""


class Date(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ExecStep(_messages.Message):
  r"""A step that runs an executable for a PatchJob.

  Fields:
    linuxExecStepConfig: The ExecStepConfig for all Linux VMs targeted by the
      PatchJob.
    windowsExecStepConfig: The ExecStepConfig for all Windows VMs targeted by
      the PatchJob.
  """

  linuxExecStepConfig = _messages.MessageField('ExecStepConfig', 1)
  windowsExecStepConfig = _messages.MessageField('ExecStepConfig', 2)


class ExecStepConfig(_messages.Message):
  r"""Common configurations for an ExecStep.

  Enums:
    InterpreterValueValuesEnum: The script interpreter to use to run the
      script. If no interpreter is specified the script will be executed
      directly, which will likely only succeed for scripts with [shebang
      lines] (https://en.wikipedia.org/wiki/Shebang_\(Unix\)).

  Fields:
    allowedSuccessCodes: Defaults to [0]. A list of possible return values
      that the execution can return to indicate a success.
    gcsObject: A Cloud Storage object containing the executable.
    interpreter: The script interpreter to use to run the script. If no
      interpreter is specified the script will be executed directly, which
      will likely only succeed for scripts with [shebang lines]
      (https://en.wikipedia.org/wiki/Shebang_\(Unix\)).
    localPath: An absolute path to the executable on the VM.
  """

  class InterpreterValueValuesEnum(_messages.Enum):
    r"""The script interpreter to use to run the script. If no interpreter is
    specified the script will be executed directly, which will likely only
    succeed for scripts with [shebang lines]
    (https://en.wikipedia.org/wiki/Shebang_\(Unix\)).

    Values:
      INTERPRETER_UNSPECIFIED: If the interpreter is not specified, the value
        defaults to `NONE`.
      NONE: Indicates that the file is run as follows on each operating
        system: + For Linux VMs, the file is ran as an executable and the
        interpreter might be parsed from the [shebang
        line](https://wikipedia.org/wiki/Shebang_(Unix)) of the file. + For
        Windows VM, this value is not supported.
      SHELL: Indicates that the file is run with `/bin/sh` on Linux and `cmd`
        on Windows.
      POWERSHELL: Indicates that the file is run with PowerShell.
    """
    INTERPRETER_UNSPECIFIED = 0
    NONE = 1
    SHELL = 2
    POWERSHELL = 3

  allowedSuccessCodes = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  gcsObject = _messages.MessageField('GcsObject', 2)
  interpreter = _messages.EnumField('InterpreterValueValuesEnum', 3)
  localPath = _messages.StringField(4)


class ExecutePatchJobRequest(_messages.Message):
  r"""A request message to initiate patching across Compute Engine instances.

  Fields:
    description: Description of the patch job. Length of the description is
      limited to 1024 characters.
    displayName: Display name for this patch job. This does not have to be
      unique.
    dryRun: If this patch is a dry-run only, instances are contacted but will
      do nothing.
    duration: Duration of the patch job. After the duration ends, the patch
      job times out.
    instanceFilter: Required. Instances to patch, either explicitly or
      filtered by some criteria such as zone or labels.
    patchConfig: Patch configuration being applied. If omitted, instances are
      patched using the default configurations.
    rollout: Rollout strategy of the patch job.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  dryRun = _messages.BooleanField(3)
  duration = _messages.StringField(4)
  instanceFilter = _messages.MessageField('PatchInstanceFilter', 5)
  patchConfig = _messages.MessageField('PatchConfig', 6)
  rollout = _messages.MessageField('PatchRollout', 7)


class FixedOrPercent(_messages.Message):
  r"""Message encapsulating a value that can be either absolute ("fixed") or
  relative ("percent") to a value.

  Fields:
    fixed: Specifies a fixed value.
    percent: Specifies the relative value defined as a percentage, which will
      be multiplied by a reference value.
  """

  fixed = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  percent = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GcsObject(_messages.Message):
  r"""Cloud Storage object representation.

  Fields:
    bucket: Required. Bucket of the Cloud Storage object.
    generationNumber: Required. Generation number of the Cloud Storage object.
      This is used to ensure that the ExecStep specified by this PatchJob does
      not change.
    object: Required. Name of the Cloud Storage object.
  """

  bucket = _messages.StringField(1)
  generationNumber = _messages.IntegerField(2)
  object = _messages.StringField(3)


class GooSettings(_messages.Message):
  r"""Googet patching is performed by running `googet update`."""


class GoogleCloudOsconfigV1OSPolicyAssignmentOperationMetadata(_messages.Message):
  r"""OS policy assignment operation metadata provided by OS policy assignment
  API methods that return long running operations.

  Enums:
    ApiMethodValueValuesEnum: The OS policy assignment API method.
    RolloutStateValueValuesEnum: State of the rollout

  Fields:
    apiMethod: The OS policy assignment API method.
    osPolicyAssignment: Reference to the `OSPolicyAssignment` API resource.
      Format: `projects/{project_number}/locations/{location}/osPolicyAssignme
      nts/{os_policy_assignment_id@revision_id}`
    rolloutStartTime: Rollout start time
    rolloutState: State of the rollout
    rolloutUpdateTime: Rollout update time
  """

  class ApiMethodValueValuesEnum(_messages.Enum):
    r"""The OS policy assignment API method.

    Values:
      API_METHOD_UNSPECIFIED: Invalid value
      CREATE: Create OS policy assignment API method
      UPDATE: Update OS policy assignment API method
      DELETE: Delete OS policy assignment API method
    """
    API_METHOD_UNSPECIFIED = 0
    CREATE = 1
    UPDATE = 2
    DELETE = 3

  class RolloutStateValueValuesEnum(_messages.Enum):
    r"""State of the rollout

    Values:
      ROLLOUT_STATE_UNSPECIFIED: Invalid value
      IN_PROGRESS: The rollout is in progress.
      CANCELLING: The rollout is being cancelled.
      CANCELLED: The rollout is cancelled.
      SUCCEEDED: The rollout has completed successfully.
    """
    ROLLOUT_STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    CANCELLING = 2
    CANCELLED = 3
    SUCCEEDED = 4

  apiMethod = _messages.EnumField('ApiMethodValueValuesEnum', 1)
  osPolicyAssignment = _messages.StringField(2)
  rolloutStartTime = _messages.StringField(3)
  rolloutState = _messages.EnumField('RolloutStateValueValuesEnum', 4)
  rolloutUpdateTime = _messages.StringField(5)


class GoogleCloudOsconfigV2OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudOsconfigV2betaOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Inventory(_messages.Message):
  r"""This API resource represents the available inventory data for a Compute
  Engine virtual machine (VM) instance at a given point in time. You can use
  this API resource to determine the inventory data of your VM. For more
  information, see [Information provided by OS inventory
  management](https://cloud.google.com/compute/docs/instances/os-inventory-
  management#data-collected).

  Messages:
    ItemsValue: Inventory items related to the VM keyed by an opaque unique
      identifier for each inventory item. The identifier is unique to each
      distinct and addressable inventory item and will change, when there is a
      new package version.

  Fields:
    items: Inventory items related to the VM keyed by an opaque unique
      identifier for each inventory item. The identifier is unique to each
      distinct and addressable inventory item and will change, when there is a
      new package version.
    name: Output only. The `Inventory` API resource name. Format: `projects/{p
      roject_number}/locations/{location}/instances/{instance_id}/inventory`
    osInfo: Base level operating system information for the VM.
    updateTime: Output only. Timestamp of the last reported inventory for the
      VM.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ItemsValue(_messages.Message):
    r"""Inventory items related to the VM keyed by an opaque unique identifier
    for each inventory item. The identifier is unique to each distinct and
    addressable inventory item and will change, when there is a new package
    version.

    Messages:
      AdditionalProperty: An additional property for a ItemsValue object.

    Fields:
      additionalProperties: Additional properties of type ItemsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ItemsValue object.

      Fields:
        key: Name of the additional property.
        value: A InventoryItem attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('InventoryItem', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  items = _messages.MessageField('ItemsValue', 1)
  name = _messages.StringField(2)
  osInfo = _messages.MessageField('InventoryOsInfo', 3)
  updateTime = _messages.StringField(4)


class InventoryItem(_messages.Message):
  r"""A single piece of inventory on a VM.

  Enums:
    OriginTypeValueValuesEnum: The origin of this inventory item.
    TypeValueValuesEnum: The specific type of inventory, correlating to its
      specific details.

  Fields:
    availablePackage: Software package available to be installed on the VM
      instance.
    createTime: When this inventory item was first detected.
    id: Identifier for this item, unique across items for this VM.
    installedPackage: Software package present on the VM instance.
    originType: The origin of this inventory item.
    type: The specific type of inventory, correlating to its specific details.
    updateTime: When this inventory item was last modified.
  """

  class OriginTypeValueValuesEnum(_messages.Enum):
    r"""The origin of this inventory item.

    Values:
      ORIGIN_TYPE_UNSPECIFIED: Invalid. An origin type must be specified.
      INVENTORY_REPORT: This inventory item was discovered as the result of
        the agent reporting inventory via the reporting API.
    """
    ORIGIN_TYPE_UNSPECIFIED = 0
    INVENTORY_REPORT = 1

  class TypeValueValuesEnum(_messages.Enum):
    r"""The specific type of inventory, correlating to its specific details.

    Values:
      TYPE_UNSPECIFIED: Invalid. A type must be specified.
      INSTALLED_PACKAGE: This represents a package that is installed on the
        VM.
      AVAILABLE_PACKAGE: This represents an update that is available for a
        package.
    """
    TYPE_UNSPECIFIED = 0
    INSTALLED_PACKAGE = 1
    AVAILABLE_PACKAGE = 2

  availablePackage = _messages.MessageField('InventorySoftwarePackage', 1)
  createTime = _messages.StringField(2)
  id = _messages.StringField(3)
  installedPackage = _messages.MessageField('InventorySoftwarePackage', 4)
  originType = _messages.EnumField('OriginTypeValueValuesEnum', 5)
  type = _messages.EnumField('TypeValueValuesEnum', 6)
  updateTime = _messages.StringField(7)


class InventoryOsInfo(_messages.Message):
  r"""Operating system information for the VM.

  Fields:
    architecture: The system architecture of the operating system.
    hostname: The VM hostname.
    kernelRelease: The kernel release of the operating system.
    kernelVersion: The kernel version of the operating system.
    longName: The operating system long name. For example 'Debian GNU/Linux 9'
      or 'Microsoft Window Server 2019 Datacenter'.
    osconfigAgentVersion: The current version of the OS Config agent running
      on the VM.
    shortName: The operating system short name. For example, 'windows' or
      'debian'.
    version: The version of the operating system.
  """

  architecture = _messages.StringField(1)
  hostname = _messages.StringField(2)
  kernelRelease = _messages.StringField(3)
  kernelVersion = _messages.StringField(4)
  longName = _messages.StringField(5)
  osconfigAgentVersion = _messages.StringField(6)
  shortName = _messages.StringField(7)
  version = _messages.StringField(8)


class InventorySoftwarePackage(_messages.Message):
  r"""Software package information of the operating system.

  Fields:
    aptPackage: Details of an APT package. For details about the apt package
      manager, see https://wiki.debian.org/Apt.
    cosPackage: Details of a COS package.
    googetPackage: Details of a Googet package. For details about the googet
      package manager, see https://github.com/google/googet.
    qfePackage: Details of a Windows Quick Fix engineering package. See
      https://docs.microsoft.com/en-
      us/windows/win32/cimwin32prov/win32-quickfixengineering for info in
      Windows Quick Fix Engineering.
    windowsApplication: Details of Windows Application.
    wuaPackage: Details of a Windows Update package. See
      https://docs.microsoft.com/en-us/windows/win32/api/_wua/ for information
      about Windows Update.
    yumPackage: Yum package info. For details about the yum package manager,
      see https://access.redhat.com/documentation/en-
      us/red_hat_enterprise_linux/6/html/deployment_guide/ch-yum.
    zypperPackage: Details of a Zypper package. For details about the Zypper
      package manager, see https://en.opensuse.org/SDB:Zypper_manual.
    zypperPatch: Details of a Zypper patch. For details about the Zypper
      package manager, see https://en.opensuse.org/SDB:Zypper_manual.
  """

  aptPackage = _messages.MessageField('InventoryVersionedPackage', 1)
  cosPackage = _messages.MessageField('InventoryVersionedPackage', 2)
  googetPackage = _messages.MessageField('InventoryVersionedPackage', 3)
  qfePackage = _messages.MessageField('InventoryWindowsQuickFixEngineeringPackage', 4)
  windowsApplication = _messages.MessageField('InventoryWindowsApplication', 5)
  wuaPackage = _messages.MessageField('InventoryWindowsUpdatePackage', 6)
  yumPackage = _messages.MessageField('InventoryVersionedPackage', 7)
  zypperPackage = _messages.MessageField('InventoryVersionedPackage', 8)
  zypperPatch = _messages.MessageField('InventoryZypperPatch', 9)


class InventoryVersionedPackage(_messages.Message):
  r"""Information related to the a standard versioned package. This includes
  package info for APT, Yum, Zypper, and Googet package managers.

  Fields:
    architecture: The system architecture this package is intended for.
    packageName: The name of the package.
    version: The version of the package.
  """

  architecture = _messages.StringField(1)
  packageName = _messages.StringField(2)
  version = _messages.StringField(3)


class InventoryWindowsApplication(_messages.Message):
  r"""Contains information about a Windows application that is retrieved from
  the Windows Registry. For more information about these fields, see:
  https://docs.microsoft.com/en-us/windows/win32/msi/uninstall-registry-key

  Fields:
    displayName: The name of the application or product.
    displayVersion: The version of the product or application in string
      format.
    helpLink: The internet address for technical support.
    installDate: The last time this product received service. The value of
      this property is replaced each time a patch is applied or removed from
      the product or the command-line option is used to repair the product.
    publisher: The name of the manufacturer for the product or application.
  """

  displayName = _messages.StringField(1)
  displayVersion = _messages.StringField(2)
  helpLink = _messages.StringField(3)
  installDate = _messages.MessageField('Date', 4)
  publisher = _messages.StringField(5)


class InventoryWindowsQuickFixEngineeringPackage(_messages.Message):
  r"""Information related to a Quick Fix Engineering package. Fields are taken
  from Windows QuickFixEngineering Interface and match the source names:
  https://docs.microsoft.com/en-
  us/windows/win32/cimwin32prov/win32-quickfixengineering

  Fields:
    caption: A short textual description of the QFE update.
    description: A textual description of the QFE update.
    hotFixId: Unique identifier associated with a particular QFE update.
    installTime: Date that the QFE update was installed. Mapped from
      installed_on field.
  """

  caption = _messages.StringField(1)
  description = _messages.StringField(2)
  hotFixId = _messages.StringField(3)
  installTime = _messages.StringField(4)


class InventoryWindowsUpdatePackage(_messages.Message):
  r"""Details related to a Windows Update package. Field data and names are
  taken from Windows Update API IUpdate Interface:
  https://docs.microsoft.com/en-us/windows/win32/api/_wua/ Descriptive fields
  like title, and description are localized based on the locale of the VM
  being updated.

  Fields:
    categories: The categories that are associated with this update package.
    description: The localized description of the update package.
    kbArticleIds: A collection of Microsoft Knowledge Base article IDs that
      are associated with the update package.
    lastDeploymentChangeTime: The last published date of the update, in (UTC)
      date and time.
    moreInfoUrls: A collection of URLs that provide more information about the
      update package.
    revisionNumber: The revision number of this update package.
    supportUrl: A hyperlink to the language-specific support information for
      the update.
    title: The localized title of the update package.
    updateId: Gets the identifier of an update package. Stays the same across
      revisions.
  """

  categories = _messages.MessageField('InventoryWindowsUpdatePackageWindowsUpdateCategory', 1, repeated=True)
  description = _messages.StringField(2)
  kbArticleIds = _messages.StringField(3, repeated=True)
  lastDeploymentChangeTime = _messages.StringField(4)
  moreInfoUrls = _messages.StringField(5, repeated=True)
  revisionNumber = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  supportUrl = _messages.StringField(7)
  title = _messages.StringField(8)
  updateId = _messages.StringField(9)


class InventoryWindowsUpdatePackageWindowsUpdateCategory(_messages.Message):
  r"""Categories specified by the Windows Update.

  Fields:
    id: The identifier of the windows update category.
    name: The name of the windows update category.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2)


class InventoryZypperPatch(_messages.Message):
  r"""Details related to a Zypper Patch.

  Fields:
    category: The category of the patch.
    patchName: The name of the patch.
    severity: The severity specified for this patch
    summary: Any summary information provided about this patch.
  """

  category = _messages.StringField(1)
  patchName = _messages.StringField(2)
  severity = _messages.StringField(3)
  summary = _messages.StringField(4)


class ListInventoriesResponse(_messages.Message):
  r"""A response message for listing inventory data for all VMs in a specified
  location.

  Fields:
    inventories: List of inventory objects.
    nextPageToken: The pagination token to retrieve the next page of inventory
      objects.
  """

  inventories = _messages.MessageField('Inventory', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOSPolicyAssignmentReportsResponse(_messages.Message):
  r"""A response message for listing OS Policy assignment reports including
  the page of results and page token.

  Fields:
    nextPageToken: The pagination token to retrieve the next page of OS policy
      assignment report objects.
    osPolicyAssignmentReports: List of OS policy assignment reports.
  """

  nextPageToken = _messages.StringField(1)
  osPolicyAssignmentReports = _messages.MessageField('OSPolicyAssignmentReport', 2, repeated=True)


class ListOSPolicyAssignmentRevisionsResponse(_messages.Message):
  r"""A response message for listing all revisions for a OS policy assignment.

  Fields:
    nextPageToken: The pagination token to retrieve the next page of OS policy
      assignment revisions.
    osPolicyAssignments: The OS policy assignment revisions
  """

  nextPageToken = _messages.StringField(1)
  osPolicyAssignments = _messages.MessageField('OSPolicyAssignment', 2, repeated=True)


class ListOSPolicyAssignmentsResponse(_messages.Message):
  r"""A response message for listing all assignments under given parent.

  Fields:
    nextPageToken: The pagination token to retrieve the next page of OS policy
      assignments.
    osPolicyAssignments: The list of assignments
  """

  nextPageToken = _messages.StringField(1)
  osPolicyAssignments = _messages.MessageField('OSPolicyAssignment', 2, repeated=True)


class ListPatchDeploymentsResponse(_messages.Message):
  r"""A response message for listing patch deployments.

  Fields:
    nextPageToken: A pagination token that can be used to get the next page of
      patch deployments.
    patchDeployments: The list of patch deployments.
  """

  nextPageToken = _messages.StringField(1)
  patchDeployments = _messages.MessageField('PatchDeployment', 2, repeated=True)


class ListPatchJobInstanceDetailsResponse(_messages.Message):
  r"""A response message for listing the instances details for a patch job.

  Fields:
    nextPageToken: A pagination token that can be used to get the next page of
      results.
    patchJobInstanceDetails: A list of instance status.
  """

  nextPageToken = _messages.StringField(1)
  patchJobInstanceDetails = _messages.MessageField('PatchJobInstanceDetails', 2, repeated=True)


class ListPatchJobsResponse(_messages.Message):
  r"""A response message for listing patch jobs.

  Fields:
    nextPageToken: A pagination token that can be used to get the next page of
      results.
    patchJobs: The list of patch jobs.
  """

  nextPageToken = _messages.StringField(1)
  patchJobs = _messages.MessageField('PatchJob', 2, repeated=True)


class ListVulnerabilityReportsResponse(_messages.Message):
  r"""A response message for listing vulnerability reports for all VM
  instances in the specified location.

  Fields:
    nextPageToken: The pagination token to retrieve the next page of
      vulnerabilityReports object.
    vulnerabilityReports: List of vulnerabilityReport objects.
  """

  nextPageToken = _messages.StringField(1)
  vulnerabilityReports = _messages.MessageField('VulnerabilityReport', 2, repeated=True)


class MessageSet(_messages.Message):
  r"""This is proto2's version of MessageSet. DEPRECATED: DO NOT USE FOR NEW
  FIELDS. If you are using editions or proto2, please make your own extendable
  messages for your use case. If you are using proto3, please use `Any`
  instead. MessageSet was the implementation of extensions for proto1. When
  proto2 was introduced, extensions were implemented as a first-class feature.
  This schema for MessageSet was meant to be a "bridge" solution to migrate
  MessageSet-bearing messages from proto1 to proto2. This schema has been
  open-sourced only to facilitate the migration of Google products with
  MessageSet-bearing messages to open-source environments.
  """



class MonthlySchedule(_messages.Message):
  r"""Represents a monthly schedule. An example of a valid monthly schedule is
  "on the third Tuesday of the month" or "on the 15th of the month".

  Fields:
    monthDay: Required. One day of the month. 1-31 indicates the 1st to the
      31st day. -1 indicates the last day of the month. Months without the
      target day will be skipped. For example, a schedule to run "every month
      on the 31st" will not run in February, April, June, etc.
    weekDayOfMonth: Required. Week day in a month.
  """

  monthDay = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  weekDayOfMonth = _messages.MessageField('WeekDayOfMonth', 2)


class OSPolicy(_messages.Message):
  r"""An OS policy defines the desired state configuration for a VM.

  Enums:
    ModeValueValuesEnum: Required. Policy mode

  Fields:
    allowNoResourceGroupMatch: This flag determines the OS policy compliance
      status when none of the resource groups within the policy are applicable
      for a VM. Set this value to `true` if the policy needs to be reported as
      compliant even if the policy has nothing to validate or enforce.
    description: Policy description. Length of the description is limited to
      1024 characters.
    id: Required. The id of the OS policy with the following restrictions: *
      Must contain only lowercase letters, numbers, and hyphens. * Must start
      with a letter. * Must be between 1-63 characters. * Must end with a
      number or a letter. * Must be unique within the assignment.
    mode: Required. Policy mode
    resourceGroups: Required. List of resource groups for the policy. For a
      particular VM, resource groups are evaluated in the order specified and
      the first resource group that is applicable is selected and the rest are
      ignored. If none of the resource groups are applicable for a VM, the VM
      is considered to be non-compliant w.r.t this policy. This behavior can
      be toggled by the flag `allow_no_resource_group_match`
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Required. Policy mode

    Values:
      MODE_UNSPECIFIED: Invalid mode
      VALIDATION: This mode checks if the configuration resources in the
        policy are in their desired state. No actions are performed if they
        are not in the desired state. This mode is used for reporting
        purposes.
      ENFORCEMENT: This mode checks if the configuration resources in the
        policy are in their desired state, and if not, enforces the desired
        state.
    """
    MODE_UNSPECIFIED = 0
    VALIDATION = 1
    ENFORCEMENT = 2

  allowNoResourceGroupMatch = _messages.BooleanField(1)
  description = _messages.StringField(2)
  id = _messages.StringField(3)
  mode = _messages.EnumField('ModeValueValuesEnum', 4)
  resourceGroups = _messages.MessageField('OSPolicyResourceGroup', 5, repeated=True)


class OSPolicyAssignment(_messages.Message):
  r"""OS policy assignment is an API resource that is used to apply a set of
  OS policies to a dynamically targeted group of Compute Engine VM instances.
  An OS policy is used to define the desired state configuration for a Compute
  Engine VM instance through a set of configuration resources that provide
  capabilities such as installing or removing software packages, or executing
  a script. For more information about the OS policy resource definitions and
  examples, see [OS policy and OS policy
  assignment](https://cloud.google.com/compute/docs/os-configuration-
  management/working-with-os-policies).

  Enums:
    RolloutStateValueValuesEnum: Output only. OS policy assignment rollout
      state

  Fields:
    baseline: Output only. Indicates that this revision has been successfully
      rolled out in this zone and new VMs will be assigned OS policies from
      this revision. For a given OS policy assignment, there is only one
      revision with a value of `true` for this field.
    deleted: Output only. Indicates that this revision deletes the OS policy
      assignment.
    description: OS policy assignment description. Length of the description
      is limited to 1024 characters.
    etag: The etag for this OS policy assignment. If this is provided on
      update, it must match the server's etag.
    instanceFilter: Required. Filter to select VMs.
    name: Resource name. Format: `projects/{project_number}/locations/{locatio
      n}/osPolicyAssignments/{os_policy_assignment_id}` This field is ignored
      when you create an OS policy assignment.
    osPolicies: Required. List of OS policies to be applied to the VMs.
    reconciling: Output only. Indicates that reconciliation is in progress for
      the revision. This value is `true` when the `rollout_state` is one of: *
      IN_PROGRESS * CANCELLING
    revisionCreateTime: Output only. The timestamp that the revision was
      created.
    revisionId: Output only. The assignment revision ID A new revision is
      committed whenever a rollout is triggered for a OS policy assignment
    rollout: Required. Rollout to deploy the OS policy assignment. A rollout
      is triggered in the following situations: 1) OSPolicyAssignment is
      created. 2) OSPolicyAssignment is updated and the update contains
      changes to one of the following fields: - instance_filter - os_policies
      3) OSPolicyAssignment is deleted.
    rolloutState: Output only. OS policy assignment rollout state
    uid: Output only. Server generated unique id for the OS policy assignment
      resource.
  """

  class RolloutStateValueValuesEnum(_messages.Enum):
    r"""Output only. OS policy assignment rollout state

    Values:
      ROLLOUT_STATE_UNSPECIFIED: Invalid value
      IN_PROGRESS: The rollout is in progress.
      CANCELLING: The rollout is being cancelled.
      CANCELLED: The rollout is cancelled.
      SUCCEEDED: The rollout has completed successfully.
    """
    ROLLOUT_STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    CANCELLING = 2
    CANCELLED = 3
    SUCCEEDED = 4

  baseline = _messages.BooleanField(1)
  deleted = _messages.BooleanField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  instanceFilter = _messages.MessageField('OSPolicyAssignmentInstanceFilter', 5)
  name = _messages.StringField(6)
  osPolicies = _messages.MessageField('OSPolicy', 7, repeated=True)
  reconciling = _messages.BooleanField(8)
  revisionCreateTime = _messages.StringField(9)
  revisionId = _messages.StringField(10)
  rollout = _messages.MessageField('OSPolicyAssignmentRollout', 11)
  rolloutState = _messages.EnumField('RolloutStateValueValuesEnum', 12)
  uid = _messages.StringField(13)


class OSPolicyAssignmentInstanceFilter(_messages.Message):
  r"""Filters to select target VMs for an assignment. If more than one filter
  criteria is specified below, a VM will be selected if and only if it
  satisfies all of them.

  Fields:
    all: Target all VMs in the project. If true, no other criteria is
      permitted.
    exclusionLabels: List of label sets used for VM exclusion. If the list has
      more than one label set, the VM is excluded if any of the label sets are
      applicable for the VM.
    inclusionLabels: List of label sets used for VM inclusion. If the list has
      more than one `LabelSet`, the VM is included if any of the label sets
      are applicable for the VM.
    inventories: List of inventories to select VMs. A VM is selected if its
      inventory data matches at least one of the following inventories.
  """

  all = _messages.BooleanField(1)
  exclusionLabels = _messages.MessageField('OSPolicyAssignmentLabelSet', 2, repeated=True)
  inclusionLabels = _messages.MessageField('OSPolicyAssignmentLabelSet', 3, repeated=True)
  inventories = _messages.MessageField('OSPolicyAssignmentInstanceFilterInventory', 4, repeated=True)


class OSPolicyAssignmentInstanceFilterInventory(_messages.Message):
  r"""VM inventory details.

  Fields:
    osShortName: Required. The OS short name
    osVersion: The OS version Prefix matches are supported if asterisk(*) is
      provided as the last character. For example, to match all versions with
      a major version of `7`, specify the following value for this field `7.*`
      An empty string matches all OS versions.
  """

  osShortName = _messages.StringField(1)
  osVersion = _messages.StringField(2)


class OSPolicyAssignmentLabelSet(_messages.Message):
  r"""Message representing label set. * A label is a key value pair set for a
  VM. * A LabelSet is a set of labels. * Labels within a LabelSet are ANDed.
  In other words, a LabelSet is applicable for a VM only if it matches all the
  labels in the LabelSet. * Example: A LabelSet with 2 labels: `env=prod` and
  `type=webserver` will only be applicable for those VMs with both labels
  present.

  Messages:
    LabelsValue: Labels are identified by key/value pairs in this map. A VM
      should contain all the key/value pairs specified in this map to be
      selected.

  Fields:
    labels: Labels are identified by key/value pairs in this map. A VM should
      contain all the key/value pairs specified in this map to be selected.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels are identified by key/value pairs in this map. A VM should
    contain all the key/value pairs specified in this map to be selected.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)


class OSPolicyAssignmentOperationMetadata(_messages.Message):
  r"""OS policy assignment operation metadata provided by OS policy assignment
  API methods that return long running operations.

  Enums:
    ApiMethodValueValuesEnum: The OS policy assignment API method.
    RolloutStateValueValuesEnum: State of the rollout

  Fields:
    apiMethod: The OS policy assignment API method.
    osPolicyAssignment: Reference to the `OSPolicyAssignment` API resource.
      Format: `projects/{project_number}/locations/{location}/osPolicyAssignme
      nts/{os_policy_assignment_id@revision_id}`
    rolloutStartTime: Rollout start time
    rolloutState: State of the rollout
    rolloutUpdateTime: Rollout update time
  """

  class ApiMethodValueValuesEnum(_messages.Enum):
    r"""The OS policy assignment API method.

    Values:
      API_METHOD_UNSPECIFIED: Invalid value
      CREATE: Create OS policy assignment API method
      UPDATE: Update OS policy assignment API method
      DELETE: Delete OS policy assignment API method
    """
    API_METHOD_UNSPECIFIED = 0
    CREATE = 1
    UPDATE = 2
    DELETE = 3

  class RolloutStateValueValuesEnum(_messages.Enum):
    r"""State of the rollout

    Values:
      ROLLOUT_STATE_UNSPECIFIED: Invalid value
      IN_PROGRESS: The rollout is in progress.
      CANCELLING: The rollout is being cancelled.
      CANCELLED: The rollout is cancelled.
      SUCCEEDED: The rollout has completed successfully.
    """
    ROLLOUT_STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    CANCELLING = 2
    CANCELLED = 3
    SUCCEEDED = 4

  apiMethod = _messages.EnumField('ApiMethodValueValuesEnum', 1)
  osPolicyAssignment = _messages.StringField(2)
  rolloutStartTime = _messages.StringField(3)
  rolloutState = _messages.EnumField('RolloutStateValueValuesEnum', 4)
  rolloutUpdateTime = _messages.StringField(5)


class OSPolicyAssignmentReport(_messages.Message):
  r"""A report of the OS policy assignment status for a given instance.

  Fields:
    instance: The Compute Engine VM instance name.
    lastRunId: Unique identifier of the last attempted run to apply the OS
      policies associated with this assignment on the VM. This ID is logged by
      the OS Config agent while applying the OS policies associated with this
      assignment on the VM. NOTE: If the service is unable to successfully
      connect to the agent for this run, then this id will not be available in
      the agent logs.
    name: The `OSPolicyAssignmentReport` API resource name. Format: `projects/
      {project_number}/locations/{location}/instances/{instance_id}/osPolicyAs
      signments/{os_policy_assignment_id}/report`
    osPolicyAssignment: Reference to the `OSPolicyAssignment` API resource
      that the `OSPolicy` belongs to. Format: `projects/{project_number}/locat
      ions/{location}/osPolicyAssignments/{os_policy_assignment_id@revision_id
      }`
    osPolicyCompliances: Compliance data for each `OSPolicy` that is applied
      to the VM.
    updateTime: Timestamp for when the report was last generated.
  """

  instance = _messages.StringField(1)
  lastRunId = _messages.StringField(2)
  name = _messages.StringField(3)
  osPolicyAssignment = _messages.StringField(4)
  osPolicyCompliances = _messages.MessageField('OSPolicyAssignmentReportOSPolicyCompliance', 5, repeated=True)
  updateTime = _messages.StringField(6)


class OSPolicyAssignmentReportOSPolicyCompliance(_messages.Message):
  r"""Compliance data for an OS policy

  Enums:
    ComplianceStateValueValuesEnum: The compliance state of the OS policy.

  Fields:
    complianceState: The compliance state of the OS policy.
    complianceStateReason: The reason for the OS policy to be in an unknown
      compliance state. This field is always populated when `compliance_state`
      is `UNKNOWN`. If populated, the field can contain one of the following
      values: * `vm-not-running`: The VM was not running. * `os-policies-not-
      supported-by-agent`: The version of the OS Config agent running on the
      VM does not support running OS policies. * `no-agent-detected`: The OS
      Config agent is not detected for the VM. * `resource-execution-errors`:
      The OS Config agent encountered errors while executing one or more
      resources in the policy. See `os_policy_resource_compliances` for
      details. * `task-timeout`: The task sent to the agent to apply the
      policy timed out. * `unexpected-agent-state`: The OS Config agent did
      not report the final status of the task that attempted to apply the
      policy. Instead, the agent unexpectedly started working on a different
      task. This mostly happens when the agent or VM unexpectedly restarts
      while applying OS policies. * `internal-service-errors`: Internal
      service errors were encountered while attempting to apply the policy. *
      `os-policy-execution-pending`: OS policy was assigned to the given VM,
      but was not executed yet. Typically this is a transient condition that
      will go away after the next policy execution cycle.
    osPolicyId: The OS policy id
    osPolicyResourceCompliances: Compliance data for each resource within the
      policy that is applied to the VM.
  """

  class ComplianceStateValueValuesEnum(_messages.Enum):
    r"""The compliance state of the OS policy.

    Values:
      UNKNOWN: The policy is in an unknown compliance state. Refer to the
        field `compliance_state_reason` to learn the exact reason for the
        policy to be in this compliance state.
      COMPLIANT: Policy is compliant. The policy is compliant if all the
        underlying resources are also compliant.
      NON_COMPLIANT: Policy is non-compliant. The policy is non-compliant if
        one or more underlying resources are non-compliant.
    """
    UNKNOWN = 0
    COMPLIANT = 1
    NON_COMPLIANT = 2

  complianceState = _messages.EnumField('ComplianceStateValueValuesEnum', 1)
  complianceStateReason = _messages.StringField(2)
  osPolicyId = _messages.StringField(3)
  osPolicyResourceCompliances = _messages.MessageField('OSPolicyAssignmentReportOSPolicyComplianceOSPolicyResourceCompliance', 4, repeated=True)


class OSPolicyAssignmentReportOSPolicyComplianceOSPolicyResourceCompliance(_messages.Message):
  r"""Compliance data for an OS policy resource.

  Enums:
    ComplianceStateValueValuesEnum: The compliance state of the resource.

  Fields:
    complianceState: The compliance state of the resource.
    complianceStateReason: A reason for the resource to be in the given
      compliance state. This field is always populated when `compliance_state`
      is `UNKNOWN`. The following values are supported when `compliance_state
      == UNKNOWN` * `execution-errors`: Errors were encountered by the agent
      while executing the resource and the compliance state couldn't be
      determined. * `execution-skipped-by-agent`: Resource execution was
      skipped by the agent because errors were encountered while executing
      prior resources in the OS policy. * `os-policy-execution-attempt-
      failed`: The execution of the OS policy containing this resource failed
      and the compliance state couldn't be determined. * `os-policy-execution-
      pending`: OS policy that owns this resource was assigned to the given
      VM, but was not executed yet.
    configSteps: Ordered list of configuration completed by the agent for the
      OS policy resource.
    execResourceOutput: ExecResource specific output.
    osPolicyResourceId: The ID of the OS policy resource.
  """

  class ComplianceStateValueValuesEnum(_messages.Enum):
    r"""The compliance state of the resource.

    Values:
      UNKNOWN: The resource is in an unknown compliance state. To get more
        details about why the policy is in this state, review the output of
        the `compliance_state_reason` field.
      COMPLIANT: Resource is compliant.
      NON_COMPLIANT: Resource is non-compliant.
    """
    UNKNOWN = 0
    COMPLIANT = 1
    NON_COMPLIANT = 2

  complianceState = _messages.EnumField('ComplianceStateValueValuesEnum', 1)
  complianceStateReason = _messages.StringField(2)
  configSteps = _messages.MessageField('OSPolicyAssignmentReportOSPolicyComplianceOSPolicyResourceComplianceOSPolicyResourceConfigStep', 3, repeated=True)
  execResourceOutput = _messages.MessageField('OSPolicyAssignmentReportOSPolicyComplianceOSPolicyResourceComplianceExecResourceOutput', 4)
  osPolicyResourceId = _messages.StringField(5)


class OSPolicyAssignmentReportOSPolicyComplianceOSPolicyResourceComplianceExecResourceOutput(_messages.Message):
  r"""ExecResource specific output.

  Fields:
    enforcementOutput: Output from enforcement phase output file (if run).
      Output size is limited to 100K bytes.
  """

  enforcementOutput = _messages.BytesField(1)


class OSPolicyAssignmentReportOSPolicyComplianceOSPolicyResourceComplianceOSPolicyResourceConfigStep(_messages.Message):
  r"""Step performed by the OS Config agent for configuring an `OSPolicy`
  resource to its desired state.

  Enums:
    TypeValueValuesEnum: Configuration step type.

  Fields:
    errorMessage: An error message recorded during the execution of this step.
      Only populated if errors were encountered during this step execution.
    type: Configuration step type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Configuration step type.

    Values:
      TYPE_UNSPECIFIED: Default value. This value is unused.
      VALIDATION: Checks for resource conflicts such as schema errors.
      DESIRED_STATE_CHECK: Checks the current status of the desired state for
        a resource.
      DESIRED_STATE_ENFORCEMENT: Enforces the desired state for a resource
        that is not in desired state.
      DESIRED_STATE_CHECK_POST_ENFORCEMENT: Re-checks the status of the
        desired state. This check is done for a resource after the enforcement
        of all OS policies. This step is used to determine the final desired
        state status for the resource. It accounts for any resources that
        might have drifted from their desired state due to side effects from
        executing other resources.
    """
    TYPE_UNSPECIFIED = 0
    VALIDATION = 1
    DESIRED_STATE_CHECK = 2
    DESIRED_STATE_ENFORCEMENT = 3
    DESIRED_STATE_CHECK_POST_ENFORCEMENT = 4

  errorMessage = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class OSPolicyAssignmentRollout(_messages.Message):
  r"""Message to configure the rollout at the zonal level for the OS policy
  assignment.

  Fields:
    disruptionBudget: Required. The maximum number (or percentage) of VMs per
      zone to disrupt at any given moment.
    minWaitDuration: Required. This determines the minimum duration of time to
      wait after the configuration changes are applied through the current
      rollout. A VM continues to count towards the `disruption_budget` at
      least until this duration of time has passed after configuration changes
      are applied.
  """

  disruptionBudget = _messages.MessageField('FixedOrPercent', 1)
  minWaitDuration = _messages.StringField(2)


class OSPolicyInventoryFilter(_messages.Message):
  r"""Filtering criteria to select VMs based on inventory details.

  Fields:
    osShortName: Required. The OS short name
    osVersion: The OS version Prefix matches are supported if asterisk(*) is
      provided as the last character. For example, to match all versions with
      a major version of `7`, specify the following value for this field `7.*`
      An empty string matches all OS versions.
  """

  osShortName = _messages.StringField(1)
  osVersion = _messages.StringField(2)


class OSPolicyResource(_messages.Message):
  r"""An OS policy resource is used to define the desired state configuration
  and provides a specific functionality like installing/removing packages,
  executing a script etc. The system ensures that resources are always in
  their desired state by taking necessary actions if they have drifted from
  their desired state.

  Fields:
    exec_: Exec resource
    file: File resource
    id: Required. The id of the resource with the following restrictions: *
      Must contain only lowercase letters, numbers, and hyphens. * Must start
      with a letter. * Must be between 1-63 characters. * Must end with a
      number or a letter. * Must be unique within the OS policy.
    pkg: Package resource
    repository: Package repository resource
  """

  exec_ = _messages.MessageField('OSPolicyResourceExecResource', 1)
  file = _messages.MessageField('OSPolicyResourceFileResource', 2)
  id = _messages.StringField(3)
  pkg = _messages.MessageField('OSPolicyResourcePackageResource', 4)
  repository = _messages.MessageField('OSPolicyResourceRepositoryResource', 5)


class OSPolicyResourceExecResource(_messages.Message):
  r"""A resource that allows executing scripts on the VM. The `ExecResource`
  has 2 stages: `validate` and `enforce` and both stages accept a script as an
  argument to execute. When the `ExecResource` is applied by the agent, it
  first executes the script in the `validate` stage. The `validate` stage can
  signal that the `ExecResource` is already in the desired state by returning
  an exit code of `100`. If the `ExecResource` is not in the desired state, it
  should return an exit code of `101`. Any other exit code returned by this
  stage is considered an error. If the `ExecResource` is not in the desired
  state based on the exit code from the `validate` stage, the agent proceeds
  to execute the script from the `enforce` stage. If the `ExecResource` is
  already in the desired state, the `enforce` stage will not be run. Similar
  to `validate` stage, the `enforce` stage should return an exit code of `100`
  to indicate that the resource in now in its desired state. Any other exit
  code is considered an error. NOTE: An exit code of `100` was chosen over `0`
  (and `101` vs `1`) to have an explicit indicator of `in desired state`, `not
  in desired state` and errors. Because, for example, Powershell will always
  return an exit code of `0` unless an `exit` statement is provided in the
  script. So, for reasons of consistency and being explicit, exit codes `100`
  and `101` were chosen.

  Fields:
    enforce: What to run to bring this resource into the desired state. An
      exit code of 100 indicates "success", any other exit code indicates a
      failure running enforce.
    validate: Required. What to run to validate this resource is in the
      desired state. An exit code of 100 indicates "in desired state", and
      exit code of 101 indicates "not in desired state". Any other exit code
      indicates a failure running validate.
  """

  enforce = _messages.MessageField('OSPolicyResourceExecResourceExec', 1)
  validate = _messages.MessageField('OSPolicyResourceExecResourceExec', 2)


class OSPolicyResourceExecResourceExec(_messages.Message):
  r"""A file or script to execute.

  Enums:
    InterpreterValueValuesEnum: Required. The script interpreter to use.

  Fields:
    args: Optional arguments to pass to the source during execution.
    file: A remote or local file.
    interpreter: Required. The script interpreter to use.
    outputFilePath: Only recorded for enforce Exec. Path to an output file
      (that is created by this Exec) whose content will be recorded in
      OSPolicyResourceCompliance after a successful run. Absence or failure to
      read this file will result in this ExecResource being non-compliant.
      Output file size is limited to 500K bytes.
    script: An inline script. The size of the script is limited to 32KiB.
  """

  class InterpreterValueValuesEnum(_messages.Enum):
    r"""Required. The script interpreter to use.

    Values:
      INTERPRETER_UNSPECIFIED: Invalid value, the request will return
        validation error.
      NONE: If an interpreter is not specified, the source is executed
        directly. This execution, without an interpreter, only succeeds for
        executables and scripts that have shebang lines.
      SHELL: Indicates that the script runs with `/bin/sh` on Linux and
        `cmd.exe` on Windows.
      POWERSHELL: Indicates that the script runs with PowerShell.
    """
    INTERPRETER_UNSPECIFIED = 0
    NONE = 1
    SHELL = 2
    POWERSHELL = 3

  args = _messages.StringField(1, repeated=True)
  file = _messages.MessageField('OSPolicyResourceFile', 2)
  interpreter = _messages.EnumField('InterpreterValueValuesEnum', 3)
  outputFilePath = _messages.StringField(4)
  script = _messages.StringField(5)


class OSPolicyResourceFile(_messages.Message):
  r"""A remote or local file.

  Fields:
    allowInsecure: Defaults to false. When false, files are subject to
      validations based on the file type: Remote: A checksum must be
      specified. Cloud Storage: An object generation number must be specified.
    gcs: A Cloud Storage object.
    localPath: A local path within the VM to use.
    remote: A generic remote file.
  """

  allowInsecure = _messages.BooleanField(1)
  gcs = _messages.MessageField('OSPolicyResourceFileGcs', 2)
  localPath = _messages.StringField(3)
  remote = _messages.MessageField('OSPolicyResourceFileRemote', 4)


class OSPolicyResourceFileGcs(_messages.Message):
  r"""Specifies a file available as a Cloud Storage Object.

  Fields:
    bucket: Required. Bucket of the Cloud Storage object.
    generation: Generation number of the Cloud Storage object.
    object: Required. Name of the Cloud Storage object.
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)


class OSPolicyResourceFileRemote(_messages.Message):
  r"""Specifies a file available via some URI.

  Fields:
    sha256Checksum: SHA256 checksum of the remote file.
    uri: Required. URI from which to fetch the object. It should contain both
      the protocol and path following the format `{protocol}://{location}`.
  """

  sha256Checksum = _messages.StringField(1)
  uri = _messages.StringField(2)


class OSPolicyResourceFileResource(_messages.Message):
  r"""A resource that manages the state of a file.

  Enums:
    StateValueValuesEnum: Required. Desired state of the file.

  Fields:
    content: A a file with this content. The size of the content is limited to
      32KiB.
    file: A remote or local source.
    path: Required. The absolute path of the file within the VM.
    permissions: Consists of three octal digits which represent, in order, the
      permissions of the owner, group, and other users for the file (similarly
      to the numeric mode used in the linux chmod utility). Each digit
      represents a three bit number with the 4 bit corresponding to the read
      permissions, the 2 bit corresponds to the write bit, and the one bit
      corresponds to the execute permission. Default behavior is 755. Below
      are some examples of permissions and their associated values: read,
      write, and execute: 7 read and execute: 5 read and write: 6 read only: 4
    state: Required. Desired state of the file.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Required. Desired state of the file.

    Values:
      DESIRED_STATE_UNSPECIFIED: Unspecified is invalid.
      PRESENT: Ensure file at path is present.
      ABSENT: Ensure file at path is absent.
      CONTENTS_MATCH: Ensure the contents of the file at path matches. If the
        file does not exist it will be created.
    """
    DESIRED_STATE_UNSPECIFIED = 0
    PRESENT = 1
    ABSENT = 2
    CONTENTS_MATCH = 3

  content = _messages.StringField(1)
  file = _messages.MessageField('OSPolicyResourceFile', 2)
  path = _messages.StringField(3)
  permissions = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class OSPolicyResourceGroup(_messages.Message):
  r"""Resource groups provide a mechanism to group OS policy resources.
  Resource groups enable OS policy authors to create a single OS policy to be
  applied to VMs running different operating Systems. When the OS policy is
  applied to a target VM, the appropriate resource group within the OS policy
  is selected based on the `OSFilter` specified within the resource group.

  Fields:
    inventoryFilters: List of inventory filters for the resource group. The
      resources in this resource group are applied to the target VM if it
      satisfies at least one of the following inventory filters. For example,
      to apply this resource group to VMs running either `RHEL` or `CentOS`
      operating systems, specify 2 items for the list with following values:
      inventory_filters[0].os_short_name='rhel' and
      inventory_filters[1].os_short_name='centos' If the list is empty, this
      resource group will be applied to the target VM unconditionally.
    resources: Required. List of resources configured for this resource group.
      The resources are executed in the exact order specified here.
  """

  inventoryFilters = _messages.MessageField('OSPolicyInventoryFilter', 1, repeated=True)
  resources = _messages.MessageField('OSPolicyResource', 2, repeated=True)


class OSPolicyResourcePackageResource(_messages.Message):
  r"""A resource that manages a system package.

  Enums:
    DesiredStateValueValuesEnum: Required. The desired state the agent should
      maintain for this package.

  Fields:
    apt: A package managed by Apt.
    deb: A deb package file.
    desiredState: Required. The desired state the agent should maintain for
      this package.
    googet: A package managed by GooGet.
    msi: An MSI package.
    rpm: An rpm package file.
    yum: A package managed by YUM.
    zypper: A package managed by Zypper.
  """

  class DesiredStateValueValuesEnum(_messages.Enum):
    r"""Required. The desired state the agent should maintain for this
    package.

    Values:
      DESIRED_STATE_UNSPECIFIED: Unspecified is invalid.
      INSTALLED: Ensure that the package is installed.
      REMOVED: The agent ensures that the package is not installed and
        uninstalls it if detected.
    """
    DESIRED_STATE_UNSPECIFIED = 0
    INSTALLED = 1
    REMOVED = 2

  apt = _messages.MessageField('OSPolicyResourcePackageResourceAPT', 1)
  deb = _messages.MessageField('OSPolicyResourcePackageResourceDeb', 2)
  desiredState = _messages.EnumField('DesiredStateValueValuesEnum', 3)
  googet = _messages.MessageField('OSPolicyResourcePackageResourceGooGet', 4)
  msi = _messages.MessageField('OSPolicyResourcePackageResourceMSI', 5)
  rpm = _messages.MessageField('OSPolicyResourcePackageResourceRPM', 6)
  yum = _messages.MessageField('OSPolicyResourcePackageResourceYUM', 7)
  zypper = _messages.MessageField('OSPolicyResourcePackageResourceZypper', 8)


class OSPolicyResourcePackageResourceAPT(_messages.Message):
  r"""A package managed by APT. - install: `apt-get update && apt-get -y
  install [name]` - remove: `apt-get -y remove [name]`

  Fields:
    name: Required. Package name.
  """

  name = _messages.StringField(1)


class OSPolicyResourcePackageResourceDeb(_messages.Message):
  r"""A deb package file. dpkg packages only support INSTALLED state.

  Fields:
    pullDeps: Whether dependencies should also be installed. - install when
      false: `dpkg -i package` - install when true: `apt-get update && apt-get
      -y install package.deb`
    source: Required. A deb package.
  """

  pullDeps = _messages.BooleanField(1)
  source = _messages.MessageField('OSPolicyResourceFile', 2)


class OSPolicyResourcePackageResourceGooGet(_messages.Message):
  r"""A package managed by GooGet. - install: `googet -noconfirm install
  package` - remove: `googet -noconfirm remove package`

  Fields:
    name: Required. Package name.
  """

  name = _messages.StringField(1)


class OSPolicyResourcePackageResourceMSI(_messages.Message):
  r"""An MSI package. MSI packages only support INSTALLED state.

  Fields:
    properties: Additional properties to use during installation. This should
      be in the format of Property=Setting. Appended to the defaults of
      `ACTION=INSTALL REBOOT=ReallySuppress`.
    source: Required. The MSI package.
  """

  properties = _messages.StringField(1, repeated=True)
  source = _messages.MessageField('OSPolicyResourceFile', 2)


class OSPolicyResourcePackageResourceRPM(_messages.Message):
  r"""An RPM package file. RPM packages only support INSTALLED state.

  Fields:
    pullDeps: Whether dependencies should also be installed. - install when
      false: `rpm --upgrade --replacepkgs package.rpm` - install when true:
      `yum -y install package.rpm` or `zypper -y install package.rpm`
    source: Required. An rpm package.
  """

  pullDeps = _messages.BooleanField(1)
  source = _messages.MessageField('OSPolicyResourceFile', 2)


class OSPolicyResourcePackageResourceYUM(_messages.Message):
  r"""A package managed by YUM. - install: `yum -y install package` - remove:
  `yum -y remove package`

  Fields:
    name: Required. Package name.
  """

  name = _messages.StringField(1)


class OSPolicyResourcePackageResourceZypper(_messages.Message):
  r"""A package managed by Zypper. - install: `zypper -y install package` -
  remove: `zypper -y rm package`

  Fields:
    name: Required. Package name.
  """

  name = _messages.StringField(1)


class OSPolicyResourceRepositoryResource(_messages.Message):
  r"""A resource that manages a package repository.

  Fields:
    apt: An Apt Repository.
    goo: A Goo Repository.
    yum: A Yum Repository.
    zypper: A Zypper Repository.
  """

  apt = _messages.MessageField('OSPolicyResourceRepositoryResourceAptRepository', 1)
  goo = _messages.MessageField('OSPolicyResourceRepositoryResourceGooRepository', 2)
  yum = _messages.MessageField('OSPolicyResourceRepositoryResourceYumRepository', 3)
  zypper = _messages.MessageField('OSPolicyResourceRepositoryResourceZypperRepository', 4)


class OSPolicyResourceRepositoryResourceAptRepository(_messages.Message):
  r"""Represents a single apt package repository. These will be added to a
  repo file that will be managed at
  `/etc/apt/sources.list.d/google_osconfig.list`.

  Enums:
    ArchiveTypeValueValuesEnum: Required. Type of archive files in this
      repository.

  Fields:
    archiveType: Required. Type of archive files in this repository.
    components: Required. List of components for this repository. Must contain
      at least one item.
    distribution: Required. Distribution of this repository.
    gpgKey: URI of the key file for this repository. The agent maintains a
      keyring at `/etc/apt/trusted.gpg.d/osconfig_agent_managed.gpg`.
    uri: Required. URI for this repository.
  """

  class ArchiveTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of archive files in this repository.

    Values:
      ARCHIVE_TYPE_UNSPECIFIED: Unspecified is invalid.
      DEB: Deb indicates that the archive contains binary files.
      DEB_SRC: Deb-src indicates that the archive contains source files.
    """
    ARCHIVE_TYPE_UNSPECIFIED = 0
    DEB = 1
    DEB_SRC = 2

  archiveType = _messages.EnumField('ArchiveTypeValueValuesEnum', 1)
  components = _messages.StringField(2, repeated=True)
  distribution = _messages.StringField(3)
  gpgKey = _messages.StringField(4)
  uri = _messages.StringField(5)


class OSPolicyResourceRepositoryResourceGooRepository(_messages.Message):
  r"""Represents a Goo package repository. These are added to a repo file that
  is managed at `C:/ProgramData/GooGet/repos/google_osconfig.repo`.

  Fields:
    name: Required. The name of the repository.
    url: Required. The url of the repository.
  """

  name = _messages.StringField(1)
  url = _messages.StringField(2)


class OSPolicyResourceRepositoryResourceYumRepository(_messages.Message):
  r"""Represents a single yum package repository. These are added to a repo
  file that is managed at `/etc/yum.repos.d/google_osconfig.repo`.

  Fields:
    baseUrl: Required. The location of the repository directory.
    displayName: The display name of the repository.
    gpgKeys: URIs of GPG keys.
    id: Required. A one word, unique name for this repository. This is the
      `repo id` in the yum config file and also the `display_name` if
      `display_name` is omitted. This id is also used as the unique identifier
      when checking for resource conflicts.
  """

  baseUrl = _messages.StringField(1)
  displayName = _messages.StringField(2)
  gpgKeys = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)


class OSPolicyResourceRepositoryResourceZypperRepository(_messages.Message):
  r"""Represents a single zypper package repository. These are added to a repo
  file that is managed at `/etc/zypp/repos.d/google_osconfig.repo`.

  Fields:
    baseUrl: Required. The location of the repository directory.
    displayName: The display name of the repository.
    gpgKeys: URIs of GPG keys.
    id: Required. A one word, unique name for this repository. This is the
      `repo id` in the zypper config file and also the `display_name` if
      `display_name` is omitted. This id is also used as the unique identifier
      when checking for GuestPolicy conflicts.
  """

  baseUrl = _messages.StringField(1)
  displayName = _messages.StringField(2)
  gpgKeys = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)


class OneTimeSchedule(_messages.Message):
  r"""Sets the time for a one time patch deployment. Timestamp is in
  [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.

  Fields:
    executeTime: Required. The desired patch job execution time.
  """

  executeTime = _messages.StringField(1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OsconfigProjectsLocationsGlobalGetProjectFeatureSettingsRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsGlobalGetProjectFeatureSettingsRequest
  object.

  Fields:
    name: Required. Name specifies the URL for the ProjectFeatureSettings
      resource: projects/project_id/locations/global/projectFeatureSettings.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsGlobalUpdateProjectFeatureSettingsRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsGlobalUpdateProjectFeatureSettingsRequest
  object.

  Fields:
    name: Required. Immutable. Name specifies the URL for the
      ProjectFeatureSettings resource:
      projects/project_id/locations/global/projectFeatureSettings.
    projectFeatureSettings: A ProjectFeatureSettings resource to be passed as
      the request body.
    updateMask: Optional. Field mask that controls which fields of the
      ProjectFeatureSettings should be updated.
  """

  name = _messages.StringField(1, required=True)
  projectFeatureSettings = _messages.MessageField('ProjectFeatureSettings', 2)
  updateMask = _messages.StringField(3)


class OsconfigProjectsLocationsInstancesInventoriesGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsInstancesInventoriesGetRequest object.

  Enums:
    ViewValueValuesEnum: Inventory view indicating what information should be
      included in the inventory resource. If unspecified, the default view is
      BASIC.

  Fields:
    name: Required. API resource name for inventory resource. Format:
      `projects/{project}/locations/{location}/instances/{instance}/inventory`
      For `{project}`, either `project-number` or `project-id` can be
      provided. For `{instance}`, either Compute Engine `instance-id` or
      `instance-name` can be provided.
    view: Inventory view indicating what information should be included in the
      inventory resource. If unspecified, the default view is BASIC.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Inventory view indicating what information should be included in the
    inventory resource. If unspecified, the default view is BASIC.

    Values:
      INVENTORY_VIEW_UNSPECIFIED: The default value. The API defaults to the
        BASIC view.
      BASIC: Returns the basic inventory information that includes `os_info`.
      FULL: Returns all fields.
    """
    INVENTORY_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class OsconfigProjectsLocationsInstancesInventoriesListRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsInstancesInventoriesListRequest object.

  Enums:
    ViewValueValuesEnum: Inventory view indicating what information should be
      included in the inventory resource. If unspecified, the default view is
      BASIC.

  Fields:
    filter: If provided, this field specifies the criteria that must be met by
      a `Inventory` API resource to be included in the response.
    pageSize: The maximum number of results to return.
    pageToken: A pagination token returned from a previous call to
      `ListInventories` that indicates where this listing should continue
      from.
    parent: Required. The parent resource name. Format:
      `projects/{project}/locations/{location}/instances/-` For `{project}`,
      either `project-number` or `project-id` can be provided.
    view: Inventory view indicating what information should be included in the
      inventory resource. If unspecified, the default view is BASIC.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Inventory view indicating what information should be included in the
    inventory resource. If unspecified, the default view is BASIC.

    Values:
      INVENTORY_VIEW_UNSPECIFIED: The default value. The API defaults to the
        BASIC view.
      BASIC: Returns the basic inventory information that includes `os_info`.
      FULL: Returns all fields.
    """
    INVENTORY_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class OsconfigProjectsLocationsInstancesOsPolicyAssignmentsReportsGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsInstancesOsPolicyAssignmentsReportsGetRequest
  object.

  Fields:
    name: Required. API resource name for OS policy assignment report. Format:
      `/projects/{project}/locations/{location}/instances/{instance}/osPolicyA
      ssignments/{assignment}/report` For `{project}`, either `project-number`
      or `project-id` can be provided. For `{instance_id}`, either Compute
      Engine `instance-id` or `instance-name` can be provided. For
      `{assignment_id}`, the OSPolicyAssignment id must be provided.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsInstancesOsPolicyAssignmentsReportsListRequest(_messages.Message):
  r"""A
  OsconfigProjectsLocationsInstancesOsPolicyAssignmentsReportsListRequest
  object.

  Fields:
    filter: If provided, this field specifies the criteria that must be met by
      the `OSPolicyAssignmentReport` API resource that is included in the
      response.
    pageSize: The maximum number of results to return.
    pageToken: A pagination token returned from a previous call to the
      `ListOSPolicyAssignmentReports` method that indicates where this listing
      should continue from.
    parent: Required. The parent resource name. Format: `projects/{project}/lo
      cations/{location}/instances/{instance}/osPolicyAssignments/{assignment}
      /reports` For `{project}`, either `project-number` or `project-id` can
      be provided. For `{instance}`, either `instance-name`, `instance-id`, or
      `-` can be provided. If '-' is provided, the response will include
      OSPolicyAssignmentReports for all instances in the project/location. For
      `{assignment}`, either `assignment-id` or `-` can be provided. If '-' is
      provided, the response will include OSPolicyAssignmentReports for all
      OSPolicyAssignments in the project/location. Either {instance} or
      {assignment} must be `-`. For example: `projects/{project}/locations/{lo
      cation}/instances/{instance}/osPolicyAssignments/-/reports` returns all
      reports for the instance `projects/{project}/locations/{location}/instan
      ces/-/osPolicyAssignments/{assignment-id}/reports` returns all the
      reports for the given assignment across all instances. `projects/{projec
      t}/locations/{location}/instances/-/osPolicyAssignments/-/reports`
      returns all the reports for all assignments across all instances.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class OsconfigProjectsLocationsInstancesVulnerabilityReportsGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsInstancesVulnerabilityReportsGetRequest
  object.

  Fields:
    name: Required. API resource name for vulnerability resource. Format: `pro
      jects/{project}/locations/{location}/instances/{instance}/vulnerabilityR
      eport` For `{project}`, either `project-number` or `project-id` can be
      provided. For `{instance}`, either Compute Engine `instance-id` or
      `instance-name` can be provided.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsInstancesVulnerabilityReportsListRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsInstancesVulnerabilityReportsListRequest
  object.

  Fields:
    filter: This field supports filtering by the severity level for the
      vulnerability. For a list of severity levels, see [Severity levels for
      vulnerabilities](https://cloud.google.com/container-
      analysis/docs/container-scanning-
      overview#severity_levels_for_vulnerabilities). The filter field follows
      the rules described in the [AIP-160](https://google.aip.dev/160)
      guidelines as follows: + **Filter for a specific severity type**: you
      can list reports that contain vulnerabilities that are classified as
      medium by specifying `vulnerabilities.details.severity:MEDIUM`. +
      **Filter for a range of severities** : you can list reports that have
      vulnerabilities that are classified as critical or high by specifying
      `vulnerabilities.details.severity:HIGH OR
      vulnerabilities.details.severity:CRITICAL`
    pageSize: The maximum number of results to return.
    pageToken: A pagination token returned from a previous call to
      `ListVulnerabilityReports` that indicates where this listing should
      continue from.
    parent: Required. The parent resource name. Format:
      `projects/{project}/locations/{location}/instances/-` For `{project}`,
      either `project-number` or `project-id` can be provided.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class OsconfigProjectsLocationsOsPolicyAssignmentsCreateRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsCreateRequest object.

  Fields:
    oSPolicyAssignment: A OSPolicyAssignment resource to be passed as the
      request body.
    osPolicyAssignmentId: Required. The logical name of the OS policy
      assignment in the project with the following restrictions: * Must
      contain only lowercase letters, numbers, and hyphens. * Must start with
      a letter. * Must be between 1-63 characters. * Must end with a number or
      a letter. * Must be unique within the project.
    parent: Required. The parent resource name in the form:
      projects/{project}/locations/{location}. Note: Specify the zone of your
      VMs as the location.
    requestId: Optional. A unique identifier for this request. Restricted to
      36 ASCII characters. A random UUID is recommended. This request is only
      idempotent if a `request_id` is provided.
  """

  oSPolicyAssignment = _messages.MessageField('OSPolicyAssignment', 1)
  osPolicyAssignmentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class OsconfigProjectsLocationsOsPolicyAssignmentsDeleteRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsDeleteRequest object.

  Fields:
    name: Required. The name of the OS policy assignment to be deleted
    requestId: Optional. A unique identifier for this request. Restricted to
      36 ASCII characters. A random UUID is recommended. This request is only
      idempotent if a `request_id` is provided.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class OsconfigProjectsLocationsOsPolicyAssignmentsGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsGetRequest object.

  Fields:
    name: Required. The resource name of OS policy assignment. Format: `projec
      ts/{project}/locations/{location}/osPolicyAssignments/{os_policy_assignm
      ent}@{revisionId}`
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsOsPolicyAssignmentsListRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsListRequest object.

  Fields:
    pageSize: The maximum number of assignments to return.
    pageToken: A pagination token returned from a previous call to
      `ListOSPolicyAssignments` that indicates where this listing should
      continue from.
    parent: Required. The parent resource name.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OsconfigProjectsLocationsOsPolicyAssignmentsListRevisionsRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsListRevisionsRequest
  object.

  Fields:
    name: Required. The name of the OS policy assignment to list revisions
      for.
    pageSize: The maximum number of revisions to return.
    pageToken: A pagination token returned from a previous call to
      `ListOSPolicyAssignmentRevisions` that indicates where this listing
      should continue from.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class OsconfigProjectsLocationsOsPolicyAssignmentsOperationsCancelRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsOperationsCancelRequest
  object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class OsconfigProjectsLocationsOsPolicyAssignmentsOperationsGetRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsOperationsGetRequest
  object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsLocationsOsPolicyAssignmentsPatchRequest(_messages.Message):
  r"""A OsconfigProjectsLocationsOsPolicyAssignmentsPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the OS policy assignment is
      not found, a new OS policy assignment will be created. In this
      situation, `update_mask` is ignored.
    name: Resource name. Format: `projects/{project_number}/locations/{locatio
      n}/osPolicyAssignments/{os_policy_assignment_id}` This field is ignored
      when you create an OS policy assignment.
    oSPolicyAssignment: A OSPolicyAssignment resource to be passed as the
      request body.
    requestId: Optional. A unique identifier for this request. Restricted to
      36 ASCII characters. A random UUID is recommended. This request is only
      idempotent if a `request_id` is provided.
    updateMask: Optional. Field mask that controls which fields of the
      assignment should be updated.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  oSPolicyAssignment = _messages.MessageField('OSPolicyAssignment', 3)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)


class OsconfigProjectsPatchDeploymentsCreateRequest(_messages.Message):
  r"""A OsconfigProjectsPatchDeploymentsCreateRequest object.

  Fields:
    parent: Required. The project to apply this patch deployment to in the
      form `projects/*`.
    patchDeployment: A PatchDeployment resource to be passed as the request
      body.
    patchDeploymentId: Required. A name for the patch deployment in the
      project. When creating a name the following rules apply: * Must contain
      only lowercase letters, numbers, and hyphens. * Must start with a
      letter. * Must be between 1-63 characters. * Must end with a number or a
      letter. * Must be unique within the project.
  """

  parent = _messages.StringField(1, required=True)
  patchDeployment = _messages.MessageField('PatchDeployment', 2)
  patchDeploymentId = _messages.StringField(3)


class OsconfigProjectsPatchDeploymentsDeleteRequest(_messages.Message):
  r"""A OsconfigProjectsPatchDeploymentsDeleteRequest object.

  Fields:
    name: Required. The resource name of the patch deployment in the form
      `projects/*/patchDeployments/*`.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsPatchDeploymentsGetRequest(_messages.Message):
  r"""A OsconfigProjectsPatchDeploymentsGetRequest object.

  Fields:
    name: Required. The resource name of the patch deployment in the form
      `projects/*/patchDeployments/*`.
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsPatchDeploymentsListRequest(_messages.Message):
  r"""A OsconfigProjectsPatchDeploymentsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of patch deployments to return.
      Default is 100.
    pageToken: Optional. A pagination token returned from a previous call to
      ListPatchDeployments that indicates where this listing should continue
      from.
    parent: Required. The resource name of the parent in the form
      `projects/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class OsconfigProjectsPatchDeploymentsPatchRequest(_messages.Message):
  r"""A OsconfigProjectsPatchDeploymentsPatchRequest object.

  Fields:
    name: Unique name for the patch deployment resource in a project. The
      patch deployment name is in the form:
      `projects/{project_id}/patchDeployments/{patch_deployment_id}`. This
      field is ignored when you create a new patch deployment.
    patchDeployment: A PatchDeployment resource to be passed as the request
      body.
    updateMask: Optional. Field mask that controls which fields of the patch
      deployment should be updated.
  """

  name = _messages.StringField(1, required=True)
  patchDeployment = _messages.MessageField('PatchDeployment', 2)
  updateMask = _messages.StringField(3)


class OsconfigProjectsPatchDeploymentsPauseRequest(_messages.Message):
  r"""A OsconfigProjectsPatchDeploymentsPauseRequest object.

  Fields:
    name: Required. The resource name of the patch deployment in the form
      `projects/*/patchDeployments/*`.
    pausePatchDeploymentRequest: A PausePatchDeploymentRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  pausePatchDeploymentRequest = _messages.MessageField('PausePatchDeploymentRequest', 2)


class OsconfigProjectsPatchDeploymentsResumeRequest(_messages.Message):
  r"""A OsconfigProjectsPatchDeploymentsResumeRequest object.

  Fields:
    name: Required. The resource name of the patch deployment in the form
      `projects/*/patchDeployments/*`.
    resumePatchDeploymentRequest: A ResumePatchDeploymentRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  resumePatchDeploymentRequest = _messages.MessageField('ResumePatchDeploymentRequest', 2)


class OsconfigProjectsPatchJobsCancelRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsCancelRequest object.

  Fields:
    cancelPatchJobRequest: A CancelPatchJobRequest resource to be passed as
      the request body.
    name: Required. Name of the patch in the form `projects/*/patchJobs/*`
  """

  cancelPatchJobRequest = _messages.MessageField('CancelPatchJobRequest', 1)
  name = _messages.StringField(2, required=True)


class OsconfigProjectsPatchJobsExecuteRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsExecuteRequest object.

  Fields:
    executePatchJobRequest: A ExecutePatchJobRequest resource to be passed as
      the request body.
    parent: Required. The project in which to run this patch in the form
      `projects/*`
  """

  executePatchJobRequest = _messages.MessageField('ExecutePatchJobRequest', 1)
  parent = _messages.StringField(2, required=True)


class OsconfigProjectsPatchJobsGetRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsGetRequest object.

  Fields:
    name: Required. Name of the patch in the form `projects/*/patchJobs/*`
  """

  name = _messages.StringField(1, required=True)


class OsconfigProjectsPatchJobsInstanceDetailsListRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsInstanceDetailsListRequest object.

  Fields:
    filter: A filter expression that filters results listed in the response.
      This field supports filtering results by instance zone, name, state, or
      `failure_reason`.
    pageSize: The maximum number of instance details records to return.
      Default is 100.
    pageToken: A pagination token returned from a previous call that indicates
      where this listing should continue from.
    parent: Required. The parent for the instances are in the form of
      `projects/*/patchJobs/*`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class OsconfigProjectsPatchJobsListRequest(_messages.Message):
  r"""A OsconfigProjectsPatchJobsListRequest object.

  Fields:
    filter: If provided, this field specifies the criteria that must be met by
      patch jobs to be included in the response. Currently, filtering is only
      available on the patch_deployment field.
    pageSize: The maximum number of instance status to return.
    pageToken: A pagination token returned from a previous call that indicates
      where this listing should continue from.
    parent: Required. In the form of `projects/*`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class PatchConfig(_messages.Message):
  r"""Patch configuration specifications. Contains details on how to apply the
  patch(es) to a VM instance.

  Enums:
    RebootConfigValueValuesEnum: Post-patch reboot settings.

  Fields:
    apt: Apt update settings. Use this setting to override the default `apt`
      patch rules.
    goo: Goo update settings. Use this setting to override the default `goo`
      patch rules.
    migInstancesAllowed: Allows the patch job to run on Managed instance
      groups (MIGs).
    postStep: The `ExecStep` to run after the patch update.
    preStep: The `ExecStep` to run before the patch update.
    rebootConfig: Post-patch reboot settings.
    windowsUpdate: Windows update settings. Use this override the default
      windows patch rules.
    yum: Yum update settings. Use this setting to override the default `yum`
      patch rules.
    zypper: Zypper update settings. Use this setting to override the default
      `zypper` patch rules.
  """

  class RebootConfigValueValuesEnum(_messages.Enum):
    r"""Post-patch reboot settings.

    Values:
      REBOOT_CONFIG_UNSPECIFIED: The default behavior is DEFAULT.
      DEFAULT: The agent decides if a reboot is necessary by checking signals
        such as registry keys on Windows or `/var/run/reboot-required` on APT
        based systems. On RPM based systems, a set of core system package
        install times are compared with system boot time.
      ALWAYS: Always reboot the machine after the update completes.
      NEVER: Never reboot the machine after the update completes.
    """
    REBOOT_CONFIG_UNSPECIFIED = 0
    DEFAULT = 1
    ALWAYS = 2
    NEVER = 3

  apt = _messages.MessageField('AptSettings', 1)
  goo = _messages.MessageField('GooSettings', 2)
  migInstancesAllowed = _messages.BooleanField(3)
  postStep = _messages.MessageField('ExecStep', 4)
  preStep = _messages.MessageField('ExecStep', 5)
  rebootConfig = _messages.EnumField('RebootConfigValueValuesEnum', 6)
  windowsUpdate = _messages.MessageField('WindowsUpdateSettings', 7)
  yum = _messages.MessageField('YumSettings', 8)
  zypper = _messages.MessageField('ZypperSettings', 9)


class PatchDeployment(_messages.Message):
  r"""Patch deployments are configurations that individual patch jobs use to
  complete a patch. These configurations include instance filter, package
  repository settings, and a schedule. For more information about creating and
  managing patch deployments, see [Scheduling patch
  jobs](https://cloud.google.com/compute/docs/os-patch-management/schedule-
  patch-jobs).

  Enums:
    StateValueValuesEnum: Output only. Current state of the patch deployment.

  Fields:
    createTime: Output only. Time the patch deployment was created. Timestamp
      is in [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
    description: Optional. Description of the patch deployment. Length of the
      description is limited to 1024 characters.
    duration: Optional. Duration of the patch. After the duration ends, the
      patch times out.
    instanceFilter: Required. VM instances to patch.
    lastExecuteTime: Output only. The last time a patch job was started by
      this deployment. Timestamp is in
      [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
    name: Unique name for the patch deployment resource in a project. The
      patch deployment name is in the form:
      `projects/{project_id}/patchDeployments/{patch_deployment_id}`. This
      field is ignored when you create a new patch deployment.
    oneTimeSchedule: Required. Schedule a one-time execution.
    patchConfig: Optional. Patch configuration that is applied.
    recurringSchedule: Required. Schedule recurring executions.
    rollout: Optional. Rollout strategy of the patch job.
    state: Output only. Current state of the patch deployment.
    updateTime: Output only. Time the patch deployment was last updated.
      Timestamp is in [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text
      format.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the patch deployment.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      ACTIVE: Active value means that patch deployment generates Patch Jobs.
      PAUSED: Paused value means that patch deployment does not generate Patch
        jobs. Requires user action to move in and out from this state.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    PAUSED = 2

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  duration = _messages.StringField(3)
  instanceFilter = _messages.MessageField('PatchInstanceFilter', 4)
  lastExecuteTime = _messages.StringField(5)
  name = _messages.StringField(6)
  oneTimeSchedule = _messages.MessageField('OneTimeSchedule', 7)
  patchConfig = _messages.MessageField('PatchConfig', 8)
  recurringSchedule = _messages.MessageField('RecurringSchedule', 9)
  rollout = _messages.MessageField('PatchRollout', 10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  updateTime = _messages.StringField(12)


class PatchInstanceFilter(_messages.Message):
  r"""A filter to target VM instances for patching. The targeted VMs must meet
  all criteria specified. So if both labels and zones are specified, the patch
  job targets only VMs with those labels and in those zones.

  Fields:
    all: Target all VM instances in the project. If true, no other criteria is
      permitted.
    groupLabels: Targets VM instances matching ANY of these GroupLabels. This
      allows targeting of disparate groups of VM instances.
    instanceNamePrefixes: Targets VMs whose name starts with one of these
      prefixes. Similar to labels, this is another way to group VMs when
      targeting configs, for example prefix="prod-".
    instances: Targets any of the VM instances specified. Instances are
      specified by their URI in the form
      `zones/[ZONE]/instances/[INSTANCE_NAME]`,
      `projects/[PROJECT_ID]/zones/[ZONE]/instances/[INSTANCE_NAME]`, or `http
      s://www.googleapis.com/compute/v1/projects/[PROJECT_ID]/zones/[ZONE]/ins
      tances/[INSTANCE_NAME]`
    zones: Targets VM instances in ANY of these zones. Leave empty to target
      VM instances in any zone.
  """

  all = _messages.BooleanField(1)
  groupLabels = _messages.MessageField('PatchInstanceFilterGroupLabel', 2, repeated=True)
  instanceNamePrefixes = _messages.StringField(3, repeated=True)
  instances = _messages.StringField(4, repeated=True)
  zones = _messages.StringField(5, repeated=True)


class PatchInstanceFilterGroupLabel(_messages.Message):
  r"""Targets a group of VM instances by using their [assigned
  labels](https://cloud.google.com/compute/docs/labeling-resources). Labels
  are key-value pairs. A `GroupLabel` is a combination of labels that is used
  to target VMs for a patch job. For example, a patch job can target VMs that
  have the following `GroupLabel`: `{"env":"test", "app":"web"}`. This means
  that the patch job is applied to VMs that have both the labels `env=test`
  and `app=web`.

  Messages:
    LabelsValue: Compute Engine instance labels that must be present for a VM
      instance to be targeted by this filter.

  Fields:
    labels: Compute Engine instance labels that must be present for a VM
      instance to be targeted by this filter.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Compute Engine instance labels that must be present for a VM instance
    to be targeted by this filter.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)


class PatchJob(_messages.Message):
  r"""A high level representation of a patch job that is either in progress or
  has completed. Instance details are not included in the job. To paginate
  through instance details, use `ListPatchJobInstanceDetails`. For more
  information about patch jobs, see [Creating patch
  jobs](https://cloud.google.com/compute/docs/os-patch-management/create-
  patch-job).

  Enums:
    StateValueValuesEnum: The current state of the PatchJob.

  Fields:
    createTime: Time this patch job was created.
    description: Description of the patch job. Length of the description is
      limited to 1024 characters.
    displayName: Display name for this patch job. This is not a unique
      identifier.
    dryRun: If this patch job is a dry run, the agent reports that it has
      finished without running any updates on the VM instance.
    duration: Duration of the patch job. After the duration ends, the patch
      job times out.
    errorMessage: If this patch job failed, this message provides information
      about the failure.
    instanceDetailsSummary: Summary of instance details.
    instanceFilter: Instances to patch.
    name: Unique identifier for this patch job in the form
      `projects/*/patchJobs/*`
    patchConfig: Patch configuration being applied.
    patchDeployment: Output only. Name of the patch deployment that created
      this patch job.
    percentComplete: Reflects the overall progress of the patch job in the
      range of 0.0 being no progress to 100.0 being complete.
    rollout: Rollout strategy being applied.
    state: The current state of the PatchJob.
    updateTime: Last time this patch job was updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the PatchJob.

    Values:
      STATE_UNSPECIFIED: State must be specified.
      STARTED: The patch job was successfully initiated.
      INSTANCE_LOOKUP: The patch job is looking up instances to run the patch
        on.
      PATCHING: Instances are being patched.
      SUCCEEDED: Patch job completed successfully.
      COMPLETED_WITH_ERRORS: Patch job completed but there were errors.
      CANCELED: The patch job was canceled.
      TIMED_OUT: The patch job timed out.
    """
    STATE_UNSPECIFIED = 0
    STARTED = 1
    INSTANCE_LOOKUP = 2
    PATCHING = 3
    SUCCEEDED = 4
    COMPLETED_WITH_ERRORS = 5
    CANCELED = 6
    TIMED_OUT = 7

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  dryRun = _messages.BooleanField(4)
  duration = _messages.StringField(5)
  errorMessage = _messages.StringField(6)
  instanceDetailsSummary = _messages.MessageField('PatchJobInstanceDetailsSummary', 7)
  instanceFilter = _messages.MessageField('PatchInstanceFilter', 8)
  name = _messages.StringField(9)
  patchConfig = _messages.MessageField('PatchConfig', 10)
  patchDeployment = _messages.StringField(11)
  percentComplete = _messages.FloatField(12)
  rollout = _messages.MessageField('PatchRollout', 13)
  state = _messages.EnumField('StateValueValuesEnum', 14)
  updateTime = _messages.StringField(15)


class PatchJobInstanceDetails(_messages.Message):
  r"""Patch details for a VM instance. For more information about reviewing VM
  instance details, see [Listing all VM instance details for a specific patch
  job](https://cloud.google.com/compute/docs/os-patch-management/manage-patch-
  jobs#list-instance-details).

  Enums:
    StateValueValuesEnum: Current state of instance patch.

  Fields:
    attemptCount: The number of times the agent that the agent attempts to
      apply the patch.
    failureReason: If the patch fails, this field provides the reason.
    instanceSystemId: The unique identifier for the instance. This identifier
      is defined by the server.
    name: The instance name in the form `projects/*/zones/*/instances/*`
    state: Current state of instance patch.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Current state of instance patch.

    Values:
      PATCH_STATE_UNSPECIFIED: Unspecified.
      PENDING: The instance is not yet notified.
      INACTIVE: Instance is inactive and cannot be patched.
      NOTIFIED: The instance is notified that it should be patched.
      STARTED: The instance has started the patching process.
      DOWNLOADING_PATCHES: The instance is downloading patches.
      APPLYING_PATCHES: The instance is applying patches.
      REBOOTING: The instance is rebooting.
      SUCCEEDED: The instance has completed applying patches.
      SUCCEEDED_REBOOT_REQUIRED: The instance has completed applying patches
        but a reboot is required.
      FAILED: The instance has failed to apply the patch.
      ACKED: The instance acked the notification and will start shortly.
      TIMED_OUT: The instance exceeded the time out while applying the patch.
      RUNNING_PRE_PATCH_STEP: The instance is running the pre-patch step.
      RUNNING_POST_PATCH_STEP: The instance is running the post-patch step.
      NO_AGENT_DETECTED: The service could not detect the presence of the
        agent. Check to ensure that the agent is installed, running, and able
        to communicate with the service.
    """
    PATCH_STATE_UNSPECIFIED = 0
    PENDING = 1
    INACTIVE = 2
    NOTIFIED = 3
    STARTED = 4
    DOWNLOADING_PATCHES = 5
    APPLYING_PATCHES = 6
    REBOOTING = 7
    SUCCEEDED = 8
    SUCCEEDED_REBOOT_REQUIRED = 9
    FAILED = 10
    ACKED = 11
    TIMED_OUT = 12
    RUNNING_PRE_PATCH_STEP = 13
    RUNNING_POST_PATCH_STEP = 14
    NO_AGENT_DETECTED = 15

  attemptCount = _messages.IntegerField(1)
  failureReason = _messages.StringField(2)
  instanceSystemId = _messages.StringField(3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class PatchJobInstanceDetailsSummary(_messages.Message):
  r"""A summary of the current patch state across all instances that this
  patch job affects. Contains counts of instances in different states. These
  states map to `InstancePatchState`. List patch job instance details to see
  the specific states of each instance.

  Fields:
    ackedInstanceCount: Number of instances that have acked and will start
      shortly.
    applyingPatchesInstanceCount: Number of instances that are applying
      patches.
    downloadingPatchesInstanceCount: Number of instances that are downloading
      patches.
    failedInstanceCount: Number of instances that failed.
    inactiveInstanceCount: Number of instances that are inactive.
    noAgentDetectedInstanceCount: Number of instances that do not appear to be
      running the agent. Check to ensure that the agent is installed, running,
      and able to communicate with the service.
    notifiedInstanceCount: Number of instances notified about patch job.
    pendingInstanceCount: Number of instances pending patch job.
    postPatchStepInstanceCount: Number of instances that are running the post-
      patch step.
    prePatchStepInstanceCount: Number of instances that are running the pre-
      patch step.
    rebootingInstanceCount: Number of instances rebooting.
    startedInstanceCount: Number of instances that have started.
    succeededInstanceCount: Number of instances that have completed
      successfully.
    succeededRebootRequiredInstanceCount: Number of instances that require
      reboot.
    timedOutInstanceCount: Number of instances that exceeded the time out
      while applying the patch.
  """

  ackedInstanceCount = _messages.IntegerField(1)
  applyingPatchesInstanceCount = _messages.IntegerField(2)
  downloadingPatchesInstanceCount = _messages.IntegerField(3)
  failedInstanceCount = _messages.IntegerField(4)
  inactiveInstanceCount = _messages.IntegerField(5)
  noAgentDetectedInstanceCount = _messages.IntegerField(6)
  notifiedInstanceCount = _messages.IntegerField(7)
  pendingInstanceCount = _messages.IntegerField(8)
  postPatchStepInstanceCount = _messages.IntegerField(9)
  prePatchStepInstanceCount = _messages.IntegerField(10)
  rebootingInstanceCount = _messages.IntegerField(11)
  startedInstanceCount = _messages.IntegerField(12)
  succeededInstanceCount = _messages.IntegerField(13)
  succeededRebootRequiredInstanceCount = _messages.IntegerField(14)
  timedOutInstanceCount = _messages.IntegerField(15)


class PatchRollout(_messages.Message):
  r"""Patch rollout configuration specifications. Contains details on the
  concurrency control when applying patch(es) to all targeted VMs.

  Enums:
    ModeValueValuesEnum: Mode of the patch rollout.

  Fields:
    disruptionBudget: The maximum number (or percentage) of VMs per zone to
      disrupt at any given moment. The number of VMs calculated from
      multiplying the percentage by the total number of VMs in a zone is
      rounded up. During patching, a VM is considered disrupted from the time
      the agent is notified to begin until patching has completed. This
      disruption time includes the time to complete reboot and any post-patch
      steps. A VM contributes to the disruption budget if its patching
      operation fails either when applying the patches, running pre or post
      patch steps, or if it fails to respond with a success notification
      before timing out. VMs that are not running or do not have an active
      agent do not count toward this disruption budget. For zone-by-zone
      rollouts, if the disruption budget in a zone is exceeded, the patch job
      stops, because continuing to the next zone requires completion of the
      patch process in the previous zone. For example, if the disruption
      budget has a fixed value of `10`, and 8 VMs fail to patch in the current
      zone, the patch job continues to patch 2 VMs at a time until the zone is
      completed. When that zone is completed successfully, patching begins
      with 10 VMs at a time in the next zone. If 10 VMs in the next zone fail
      to patch, the patch job stops.
    mode: Mode of the patch rollout.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Mode of the patch rollout.

    Values:
      MODE_UNSPECIFIED: Mode must be specified.
      ZONE_BY_ZONE: Patches are applied one zone at a time. The patch job
        begins in the region with the lowest number of targeted VMs. Within
        the region, patching begins in the zone with the lowest number of
        targeted VMs. If multiple regions (or zones within a region) have the
        same number of targeted VMs, a tie-breaker is achieved by sorting the
        regions or zones in alphabetical order.
      CONCURRENT_ZONES: Patches are applied to VMs in all zones at the same
        time.
    """
    MODE_UNSPECIFIED = 0
    ZONE_BY_ZONE = 1
    CONCURRENT_ZONES = 2

  disruptionBudget = _messages.MessageField('FixedOrPercent', 1)
  mode = _messages.EnumField('ModeValueValuesEnum', 2)


class PausePatchDeploymentRequest(_messages.Message):
  r"""A request message for pausing a patch deployment."""


class ProjectFeatureSettings(_messages.Message):
  r"""ProjectFeatureSettings represents the VM Manager feature settings in a
  project. For more information, see Enable full VM Manager functionality.

  Enums:
    PatchAndConfigFeatureSetValueValuesEnum: Set PatchAndConfigFeatureSet for
      the project.

  Fields:
    name: Required. Immutable. Name specifies the URL for the
      ProjectFeatureSettings resource:
      projects/project_id/locations/global/projectFeatureSettings.
    patchAndConfigFeatureSet: Set PatchAndConfigFeatureSet for the project.
  """

  class PatchAndConfigFeatureSetValueValuesEnum(_messages.Enum):
    r"""Set PatchAndConfigFeatureSet for the project.

    Values:
      PATCH_AND_CONFIG_FEATURE_SET_UNSPECIFIED: Not specified placeholder
      OSCONFIG_B: Enables only the basic set of VM Manager features in the
        project.
      OSCONFIG_C: Enables all VM Manager features in the project.
    """
    PATCH_AND_CONFIG_FEATURE_SET_UNSPECIFIED = 0
    OSCONFIG_B = 1
    OSCONFIG_C = 2

  name = _messages.StringField(1)
  patchAndConfigFeatureSet = _messages.EnumField('PatchAndConfigFeatureSetValueValuesEnum', 2)


class RecurringSchedule(_messages.Message):
  r"""Sets the time for recurring patch deployments.

  Enums:
    FrequencyValueValuesEnum: Required. The frequency unit of this recurring
      schedule.

  Fields:
    endTime: Optional. The end time at which a recurring patch deployment
      schedule is no longer active.
    frequency: Required. The frequency unit of this recurring schedule.
    lastExecuteTime: Output only. The time the last patch job ran
      successfully.
    monthly: Required. Schedule with monthly executions.
    nextExecuteTime: Output only. The time the next patch job is scheduled to
      run.
    startTime: Optional. The time that the recurring schedule becomes
      effective. Defaults to `create_time` of the patch deployment.
    timeOfDay: Required. Time of the day to run a recurring deployment.
    timeZone: Required. Defines the time zone that `time_of_day` is relative
      to. The rules for daylight saving time are determined by the chosen time
      zone.
    weekly: Required. Schedule with weekly executions.
  """

  class FrequencyValueValuesEnum(_messages.Enum):
    r"""Required. The frequency unit of this recurring schedule.

    Values:
      FREQUENCY_UNSPECIFIED: Invalid. A frequency must be specified.
      WEEKLY: Indicates that the frequency of recurrence should be expressed
        in terms of weeks.
      MONTHLY: Indicates that the frequency of recurrence should be expressed
        in terms of months.
      DAILY: Indicates that the frequency of recurrence should be expressed in
        terms of days.
    """
    FREQUENCY_UNSPECIFIED = 0
    WEEKLY = 1
    MONTHLY = 2
    DAILY = 3

  endTime = _messages.StringField(1)
  frequency = _messages.EnumField('FrequencyValueValuesEnum', 2)
  lastExecuteTime = _messages.StringField(3)
  monthly = _messages.MessageField('MonthlySchedule', 4)
  nextExecuteTime = _messages.StringField(5)
  startTime = _messages.StringField(6)
  timeOfDay = _messages.MessageField('TimeOfDay', 7)
  timeZone = _messages.MessageField('TimeZone', 8)
  weekly = _messages.MessageField('WeeklySchedule', 9)


class ResumePatchDeploymentRequest(_messages.Message):
  r"""A request message for resuming a patch deployment."""


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StatusProto(_messages.Message):
  r"""Wire-format for a Status object

  Fields:
    canonicalCode: copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional int32 canonical_code = 6;
    code: Numeric code drawn from the space specified below. Often, this is
      the canonical error space, and code is drawn from
      google3/util/task/codes.proto copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional int32 code = 1;
    message: Detail message copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional string message = 3;
    messageSet: message_set associates an arbitrary proto message with the
      status. copybara:strip_begin(b/383363683) copybara:strip_end_and_replace
      optional proto2.bridge.MessageSet message_set = 5;
    space: copybara:strip_begin(b/383363683) Space to which this status
      belongs copybara:strip_end_and_replace optional string space = 2; //
      Space to which this status belongs
  """

  canonicalCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  code = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  message = _messages.StringField(3)
  messageSet = _messages.MessageField('MessageSet', 4)
  space = _messages.StringField(5)


class TimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of a day in 24 hour format. Must be greater than or equal to
      0 and typically must be less than or equal to 23. An API may choose to
      allow the value "24:00:00" for scenarios like business closing time.
    minutes: Minutes of an hour. Must be greater than or equal to 0 and less
      than or equal to 59.
    nanos: Fractions of seconds, in nanoseconds. Must be greater than or equal
      to 0 and less than or equal to 999,999,999.
    seconds: Seconds of a minute. Must be greater than or equal to 0 and
      typically must be less than or equal to 59. An API may allow the value
      60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class TimeZone(_messages.Message):
  r"""Represents a time zone from the [IANA Time Zone
  Database](https://www.iana.org/time-zones).

  Fields:
    id: IANA Time Zone Database time zone. For example "America/New_York".
    version: Optional. IANA Time Zone Database version number. For example
      "2019a".
  """

  id = _messages.StringField(1)
  version = _messages.StringField(2)


class VulnerabilityReport(_messages.Message):
  r"""This API resource represents the vulnerability report for a specified
  Compute Engine virtual machine (VM) instance at a given point in time. For
  more information, see [Vulnerability
  reports](https://cloud.google.com/compute/docs/instances/os-inventory-
  management#vulnerability-reports).

  Fields:
    name: Output only. The `vulnerabilityReport` API resource name. Format: `p
      rojects/{project_number}/locations/{location}/instances/{instance_id}/vu
      lnerabilityReport`
    updateTime: Output only. The timestamp for when the last vulnerability
      report was generated for the VM.
    vulnerabilities: Output only. List of vulnerabilities affecting the VM.
  """

  name = _messages.StringField(1)
  updateTime = _messages.StringField(2)
  vulnerabilities = _messages.MessageField('VulnerabilityReportVulnerability', 3, repeated=True)


class VulnerabilityReportVulnerability(_messages.Message):
  r"""A vulnerability affecting the VM instance.

  Fields:
    availableInventoryItemIds: Corresponds to the `AVAILABLE_PACKAGE`
      inventory item on the VM. If the vulnerability report was not updated
      after the VM inventory update, these values might not display in VM
      inventory. If there is no available fix, the field is empty. The
      `inventory_item` value specifies the latest `SoftwarePackage` available
      to the VM that fixes the vulnerability.
    createTime: The timestamp for when the vulnerability was first detected.
    details: Contains metadata as per the upstream feed of the operating
      system and NVD.
    installedInventoryItemIds: Corresponds to the `INSTALLED_PACKAGE`
      inventory item on the VM. This field displays the inventory items
      affected by this vulnerability. If the vulnerability report was not
      updated after the VM inventory update, these values might not display in
      VM inventory. For some distros, this field may be empty.
    items: List of items affected by the vulnerability.
    updateTime: The timestamp for when the vulnerability was last modified.
  """

  availableInventoryItemIds = _messages.StringField(1, repeated=True)
  createTime = _messages.StringField(2)
  details = _messages.MessageField('VulnerabilityReportVulnerabilityDetails', 3)
  installedInventoryItemIds = _messages.StringField(4, repeated=True)
  items = _messages.MessageField('VulnerabilityReportVulnerabilityItem', 5, repeated=True)
  updateTime = _messages.StringField(6)


class VulnerabilityReportVulnerabilityDetails(_messages.Message):
  r"""Contains metadata information for the vulnerability. This information is
  collected from the upstream feed of the operating system.

  Fields:
    cve: The CVE of the vulnerability. CVE cannot be empty and the combination
      of should be unique across vulnerabilities for a VM.
    cvssV2Score: The CVSS V2 score of this vulnerability. CVSS V2 score is on
      a scale of 0 - 10 where 0 indicates low severity and 10 indicates high
      severity.
    cvssV3: The full description of the CVSSv3 for this vulnerability from
      NVD.
    description: The note or description describing the vulnerability from the
      distro.
    references: Corresponds to the references attached to the
      `VulnerabilityDetails`.
    severity: Assigned severity/impact ranking from the distro.
  """

  cve = _messages.StringField(1)
  cvssV2Score = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  cvssV3 = _messages.MessageField('CVSSv3', 3)
  description = _messages.StringField(4)
  references = _messages.MessageField('VulnerabilityReportVulnerabilityDetailsReference', 5, repeated=True)
  severity = _messages.StringField(6)


class VulnerabilityReportVulnerabilityDetailsReference(_messages.Message):
  r"""A reference for this vulnerability.

  Fields:
    source: The source of the reference e.g. NVD.
    url: The url of the reference.
  """

  source = _messages.StringField(1)
  url = _messages.StringField(2)


class VulnerabilityReportVulnerabilityItem(_messages.Message):
  r"""OS inventory item that is affected by a vulnerability or fixed as a
  result of a vulnerability.

  Fields:
    availableInventoryItemId: Corresponds to the `AVAILABLE_PACKAGE` inventory
      item on the VM. If the vulnerability report was not updated after the VM
      inventory update, these values might not display in VM inventory. If
      there is no available fix, the field is empty. The `inventory_item`
      value specifies the latest `SoftwarePackage` available to the VM that
      fixes the vulnerability.
    fixedCpeUri: The recommended [CPE
      URI](https://cpe.mitre.org/specification/) update that contains a fix
      for this vulnerability.
    installedInventoryItemId: Corresponds to the `INSTALLED_PACKAGE` inventory
      item on the VM. This field displays the inventory items affected by this
      vulnerability. If the vulnerability report was not updated after the VM
      inventory update, these values might not display in VM inventory. For
      some operating systems, this field might be empty.
    upstreamFix: The upstream OS patch, packages or KB that fixes the
      vulnerability.
  """

  availableInventoryItemId = _messages.StringField(1)
  fixedCpeUri = _messages.StringField(2)
  installedInventoryItemId = _messages.StringField(3)
  upstreamFix = _messages.StringField(4)


class WeekDayOfMonth(_messages.Message):
  r"""Represents one week day in a month. An example is "the 4th Sunday".

  Enums:
    DayOfWeekValueValuesEnum: Required. A day of the week.

  Fields:
    dayOfWeek: Required. A day of the week.
    dayOffset: Optional. Represents the number of days before or after the
      given week day of month that the patch deployment is scheduled for. For
      example if `week_ordinal` and `day_of_week` values point to the second
      Tuesday of the month and the `day_offset` value is set to `3`, patch
      deployment takes place three days after the second Tuesday of the month.
      If this value is negative, for example -5, patches are deployed five
      days before the second Tuesday of the month. Allowed values are in range
      [-30, 30].
    weekOrdinal: Required. Week number in a month. 1-4 indicates the 1st to
      4th week of the month. -1 indicates the last week of the month.
  """

  class DayOfWeekValueValuesEnum(_messages.Enum):
    r"""Required. A day of the week.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  dayOfWeek = _messages.EnumField('DayOfWeekValueValuesEnum', 1)
  dayOffset = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  weekOrdinal = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class WeeklySchedule(_messages.Message):
  r"""Represents a weekly schedule.

  Enums:
    DayOfWeekValueValuesEnum: Required. Day of the week.

  Fields:
    dayOfWeek: Required. Day of the week.
  """

  class DayOfWeekValueValuesEnum(_messages.Enum):
    r"""Required. Day of the week.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  dayOfWeek = _messages.EnumField('DayOfWeekValueValuesEnum', 1)


class WindowsUpdateSettings(_messages.Message):
  r"""Windows patching is performed using the Windows Update Agent.

  Enums:
    ClassificationsValueListEntryValuesEnum:

  Fields:
    classifications: Only apply updates of these windows update
      classifications. If empty, all updates are applied.
    excludes: List of KBs to exclude from update.
    exclusivePatches: An exclusive list of kbs to be updated. These are the
      only patches that will be updated. This field must not be used with
      other patch configurations.
  """

  class ClassificationsValueListEntryValuesEnum(_messages.Enum):
    r"""ClassificationsValueListEntryValuesEnum enum type.

    Values:
      CLASSIFICATION_UNSPECIFIED: Invalid. If classifications are included,
        they must be specified.
      CRITICAL: "A widely released fix for a specific problem that addresses a
        critical, non-security-related bug." [1]
      SECURITY: "A widely released fix for a product-specific, security-
        related vulnerability. Security vulnerabilities are rated by their
        severity. The severity rating is indicated in the Microsoft security
        bulletin as critical, important, moderate, or low." [1]
      DEFINITION: "A widely released and frequent software update that
        contains additions to a product's definition database. Definition
        databases are often used to detect objects that have specific
        attributes, such as malicious code, phishing websites, or junk mail."
        [1]
      DRIVER: "Software that controls the input and output of a device." [1]
      FEATURE_PACK: "New product functionality that is first distributed
        outside the context of a product release and that is typically
        included in the next full product release." [1]
      SERVICE_PACK: "A tested, cumulative set of all hotfixes, security
        updates, critical updates, and updates. Additionally, service packs
        may contain additional fixes for problems that are found internally
        since the release of the product. Service packs my also contain a
        limited number of customer-requested design changes or features." [1]
      TOOL: "A utility or feature that helps complete a task or set of tasks."
        [1]
      UPDATE_ROLLUP: "A tested, cumulative set of hotfixes, security updates,
        critical updates, and updates that are packaged together for easy
        deployment. A rollup generally targets a specific area, such as
        security, or a component of a product, such as Internet Information
        Services (IIS)." [1]
      UPDATE: "A widely released fix for a specific problem. An update
        addresses a noncritical, non-security-related bug." [1]
    """
    CLASSIFICATION_UNSPECIFIED = 0
    CRITICAL = 1
    SECURITY = 2
    DEFINITION = 3
    DRIVER = 4
    FEATURE_PACK = 5
    SERVICE_PACK = 6
    TOOL = 7
    UPDATE_ROLLUP = 8
    UPDATE = 9

  classifications = _messages.EnumField('ClassificationsValueListEntryValuesEnum', 1, repeated=True)
  excludes = _messages.StringField(2, repeated=True)
  exclusivePatches = _messages.StringField(3, repeated=True)


class YumSettings(_messages.Message):
  r"""Yum patching is performed by executing `yum update`. Additional options
  can be set to control how this is executed. Note that not all settings are
  supported on all platforms.

  Fields:
    excludes: List of packages to exclude from update. These packages are
      excluded by using the yum `--exclude` flag.
    exclusivePackages: An exclusive list of packages to be updated. These are
      the only packages that will be updated. If these packages are not
      installed, they will be ignored. This field must not be specified with
      any other patch configuration fields.
    minimal: Will cause patch to run `yum update-minimal` instead.
    security: Adds the `--security` flag to `yum update`. Not supported on all
      platforms.
  """

  excludes = _messages.StringField(1, repeated=True)
  exclusivePackages = _messages.StringField(2, repeated=True)
  minimal = _messages.BooleanField(3)
  security = _messages.BooleanField(4)


class ZypperSettings(_messages.Message):
  r"""Zypper patching is performed by running `zypper patch`. See also
  https://en.opensuse.org/SDB:Zypper_manual.

  Fields:
    categories: Install only patches with these categories. Common categories
      include security, recommended, and feature.
    excludes: List of patches to exclude from update.
    exclusivePatches: An exclusive list of patches to be updated. These are
      the only patches that will be installed using 'zypper patch patch:'
      command. This field must not be used with any other patch configuration
      fields.
    severities: Install only patches with these severities. Common severities
      include critical, important, moderate, and low.
    withOptional: Adds the `--with-optional` flag to `zypper patch`.
    withUpdate: Adds the `--with-update` flag, to `zypper patch`.
  """

  categories = _messages.StringField(1, repeated=True)
  excludes = _messages.StringField(2, repeated=True)
  exclusivePatches = _messages.StringField(3, repeated=True)
  severities = _messages.StringField(4, repeated=True)
  withOptional = _messages.BooleanField(5)
  withUpdate = _messages.BooleanField(6)


encoding.AddCustomJsonFieldMapping(
    OSPolicyResource, 'exec_', 'exec')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
