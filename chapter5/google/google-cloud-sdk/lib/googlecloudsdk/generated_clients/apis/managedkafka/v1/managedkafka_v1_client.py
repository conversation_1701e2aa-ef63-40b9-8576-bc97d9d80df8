"""Generated client library for managedkafka version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.managedkafka.v1 import managedkafka_v1_messages as messages


class ManagedkafkaV1(base_api.BaseApiClient):
  """Generated client library for service managedkafka version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://managedkafka.googleapis.com/'
  MTLS_BASE_URL = 'https://managedkafka.mtls.googleapis.com/'

  _PACKAGE = 'managedkafka'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'ManagedkafkaV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new managedkafka handle."""
    url = url or self.BASE_URL
    super(ManagedkafkaV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_clusters_acls = self.ProjectsLocationsClustersAclsService(self)
    self.projects_locations_clusters_consumerGroups = self.ProjectsLocationsClustersConsumerGroupsService(self)
    self.projects_locations_clusters_topics = self.ProjectsLocationsClustersTopicsService(self)
    self.projects_locations_clusters = self.ProjectsLocationsClustersService(self)
    self.projects_locations_connectClusters_connectors = self.ProjectsLocationsConnectClustersConnectorsService(self)
    self.projects_locations_connectClusters = self.ProjectsLocationsConnectClustersService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_schemaRegistries_compatibility = self.ProjectsLocationsSchemaRegistriesCompatibilityService(self)
    self.projects_locations_schemaRegistries_config = self.ProjectsLocationsSchemaRegistriesConfigService(self)
    self.projects_locations_schemaRegistries_contexts_compatibility = self.ProjectsLocationsSchemaRegistriesContextsCompatibilityService(self)
    self.projects_locations_schemaRegistries_contexts_config = self.ProjectsLocationsSchemaRegistriesContextsConfigService(self)
    self.projects_locations_schemaRegistries_contexts_mode = self.ProjectsLocationsSchemaRegistriesContextsModeService(self)
    self.projects_locations_schemaRegistries_contexts_schemas_subjects = self.ProjectsLocationsSchemaRegistriesContextsSchemasSubjectsService(self)
    self.projects_locations_schemaRegistries_contexts_schemas_types = self.ProjectsLocationsSchemaRegistriesContextsSchemasTypesService(self)
    self.projects_locations_schemaRegistries_contexts_schemas_versions = self.ProjectsLocationsSchemaRegistriesContextsSchemasVersionsService(self)
    self.projects_locations_schemaRegistries_contexts_schemas = self.ProjectsLocationsSchemaRegistriesContextsSchemasService(self)
    self.projects_locations_schemaRegistries_contexts_subjects_versions_referencedby = self.ProjectsLocationsSchemaRegistriesContextsSubjectsVersionsReferencedbyService(self)
    self.projects_locations_schemaRegistries_contexts_subjects_versions = self.ProjectsLocationsSchemaRegistriesContextsSubjectsVersionsService(self)
    self.projects_locations_schemaRegistries_contexts_subjects = self.ProjectsLocationsSchemaRegistriesContextsSubjectsService(self)
    self.projects_locations_schemaRegistries_contexts = self.ProjectsLocationsSchemaRegistriesContextsService(self)
    self.projects_locations_schemaRegistries_mode = self.ProjectsLocationsSchemaRegistriesModeService(self)
    self.projects_locations_schemaRegistries_schemas_subjects = self.ProjectsLocationsSchemaRegistriesSchemasSubjectsService(self)
    self.projects_locations_schemaRegistries_schemas_types = self.ProjectsLocationsSchemaRegistriesSchemasTypesService(self)
    self.projects_locations_schemaRegistries_schemas_versions = self.ProjectsLocationsSchemaRegistriesSchemasVersionsService(self)
    self.projects_locations_schemaRegistries_schemas = self.ProjectsLocationsSchemaRegistriesSchemasService(self)
    self.projects_locations_schemaRegistries_subjects_versions_referencedby = self.ProjectsLocationsSchemaRegistriesSubjectsVersionsReferencedbyService(self)
    self.projects_locations_schemaRegistries_subjects_versions = self.ProjectsLocationsSchemaRegistriesSubjectsVersionsService(self)
    self.projects_locations_schemaRegistries_subjects = self.ProjectsLocationsSchemaRegistriesSubjectsService(self)
    self.projects_locations_schemaRegistries = self.ProjectsLocationsSchemaRegistriesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsClustersAclsService(base_api.BaseApiService):
    """Service class for the projects_locations_clusters_acls resource."""

    _NAME = 'projects_locations_clusters_acls'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsClustersAclsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddAclEntry(self, request, global_params=None):
      r"""Incremental update: Adds an acl entry to an acl. Creates the acl if it does not exist yet.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersAclsAddAclEntryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AddAclEntryResponse) The response message.
      """
      config = self.GetMethodConfig('AddAclEntry')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddAclEntry.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/acls/{aclsId}:addAclEntry',
        http_method='POST',
        method_id='managedkafka.projects.locations.clusters.acls.addAclEntry',
        ordered_params=['acl'],
        path_params=['acl'],
        query_params=[],
        relative_path='v1/{+acl}:addAclEntry',
        request_field='aclEntry',
        request_type_name='ManagedkafkaProjectsLocationsClustersAclsAddAclEntryRequest',
        response_type_name='AddAclEntryResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new acl in the given project, location, and cluster.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersAclsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Acl) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/acls',
        http_method='POST',
        method_id='managedkafka.projects.locations.clusters.acls.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['aclId'],
        relative_path='v1/{+parent}/acls',
        request_field='acl',
        request_type_name='ManagedkafkaProjectsLocationsClustersAclsCreateRequest',
        response_type_name='Acl',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an acl.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersAclsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/acls/{aclsId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.clusters.acls.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsClustersAclsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the properties of a single acl.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersAclsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Acl) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/acls/{aclsId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.clusters.acls.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsClustersAclsGetRequest',
        response_type_name='Acl',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the acls in a given cluster.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersAclsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAclsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/acls',
        http_method='GET',
        method_id='managedkafka.projects.locations.clusters.acls.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/acls',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsClustersAclsListRequest',
        response_type_name='ListAclsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the properties of a single acl.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersAclsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Acl) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/acls/{aclsId}',
        http_method='PATCH',
        method_id='managedkafka.projects.locations.clusters.acls.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='acl',
        request_type_name='ManagedkafkaProjectsLocationsClustersAclsPatchRequest',
        response_type_name='Acl',
        supports_download=False,
    )

    def RemoveAclEntry(self, request, global_params=None):
      r"""Incremental update: Removes an acl entry from an acl. Deletes the acl if its acl entries become empty (i.e. if the removed entry was the last one in the acl).

      Args:
        request: (ManagedkafkaProjectsLocationsClustersAclsRemoveAclEntryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RemoveAclEntryResponse) The response message.
      """
      config = self.GetMethodConfig('RemoveAclEntry')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveAclEntry.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/acls/{aclsId}:removeAclEntry',
        http_method='POST',
        method_id='managedkafka.projects.locations.clusters.acls.removeAclEntry',
        ordered_params=['acl'],
        path_params=['acl'],
        query_params=[],
        relative_path='v1/{+acl}:removeAclEntry',
        request_field='aclEntry',
        request_type_name='ManagedkafkaProjectsLocationsClustersAclsRemoveAclEntryRequest',
        response_type_name='RemoveAclEntryResponse',
        supports_download=False,
    )

  class ProjectsLocationsClustersConsumerGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_clusters_consumerGroups resource."""

    _NAME = 'projects_locations_clusters_consumerGroups'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsClustersConsumerGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a single consumer group.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersConsumerGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/consumerGroups/{consumerGroupsId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.clusters.consumerGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsClustersConsumerGroupsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the properties of a single consumer group.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersConsumerGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsumerGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/consumerGroups/{consumerGroupsId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.clusters.consumerGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsClustersConsumerGroupsGetRequest',
        response_type_name='ConsumerGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the consumer groups in a given cluster.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersConsumerGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConsumerGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/consumerGroups',
        http_method='GET',
        method_id='managedkafka.projects.locations.clusters.consumerGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/consumerGroups',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsClustersConsumerGroupsListRequest',
        response_type_name='ListConsumerGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the properties of a single consumer group.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersConsumerGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsumerGroup) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/consumerGroups/{consumerGroupsId}',
        http_method='PATCH',
        method_id='managedkafka.projects.locations.clusters.consumerGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='consumerGroup',
        request_type_name='ManagedkafkaProjectsLocationsClustersConsumerGroupsPatchRequest',
        response_type_name='ConsumerGroup',
        supports_download=False,
    )

  class ProjectsLocationsClustersTopicsService(base_api.BaseApiService):
    """Service class for the projects_locations_clusters_topics resource."""

    _NAME = 'projects_locations_clusters_topics'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsClustersTopicsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new topic in a given project and location.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersTopicsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Topic) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/topics',
        http_method='POST',
        method_id='managedkafka.projects.locations.clusters.topics.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['topicId'],
        relative_path='v1/{+parent}/topics',
        request_field='topic',
        request_type_name='ManagedkafkaProjectsLocationsClustersTopicsCreateRequest',
        response_type_name='Topic',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single topic.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersTopicsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/topics/{topicsId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.clusters.topics.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsClustersTopicsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the properties of a single topic.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersTopicsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Topic) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/topics/{topicsId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.clusters.topics.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsClustersTopicsGetRequest',
        response_type_name='Topic',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the topics in a given cluster.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersTopicsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTopicsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/topics',
        http_method='GET',
        method_id='managedkafka.projects.locations.clusters.topics.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/topics',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsClustersTopicsListRequest',
        response_type_name='ListTopicsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the properties of a single topic.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersTopicsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Topic) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/topics/{topicsId}',
        http_method='PATCH',
        method_id='managedkafka.projects.locations.clusters.topics.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='topic',
        request_type_name='ManagedkafkaProjectsLocationsClustersTopicsPatchRequest',
        response_type_name='Topic',
        supports_download=False,
    )

  class ProjectsLocationsClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_clusters resource."""

    _NAME = 'projects_locations_clusters'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new cluster in a given project and location.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters',
        http_method='POST',
        method_id='managedkafka.projects.locations.clusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['clusterId', 'requestId'],
        relative_path='v1/{+parent}/clusters',
        request_field='cluster',
        request_type_name='ManagedkafkaProjectsLocationsClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single cluster.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.clusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsClustersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the properties of a single cluster.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Cluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.clusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsClustersGetRequest',
        response_type_name='Cluster',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the clusters in a given project and location.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters',
        http_method='GET',
        method_id='managedkafka.projects.locations.clusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/clusters',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsClustersListRequest',
        response_type_name='ListClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the properties of a single cluster.

      Args:
        request: (ManagedkafkaProjectsLocationsClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}',
        http_method='PATCH',
        method_id='managedkafka.projects.locations.clusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='cluster',
        request_type_name='ManagedkafkaProjectsLocationsClustersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsConnectClustersConnectorsService(base_api.BaseApiService):
    """Service class for the projects_locations_connectClusters_connectors resource."""

    _NAME = 'projects_locations_connectClusters_connectors'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsConnectClustersConnectorsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new connector in a given Connect cluster.

      Args:
        request: (ManagedkafkaProjectsLocationsConnectClustersConnectorsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Connector) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/connectClusters/{connectClustersId}/connectors',
        http_method='POST',
        method_id='managedkafka.projects.locations.connectClusters.connectors.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['connectorId'],
        relative_path='v1/{+parent}/connectors',
        request_field='connector',
        request_type_name='ManagedkafkaProjectsLocationsConnectClustersConnectorsCreateRequest',
        response_type_name='Connector',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a connector.

      Args:
        request: (ManagedkafkaProjectsLocationsConnectClustersConnectorsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/connectClusters/{connectClustersId}/connectors/{connectorsId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.connectClusters.connectors.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsConnectClustersConnectorsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the properties of a single connector.

      Args:
        request: (ManagedkafkaProjectsLocationsConnectClustersConnectorsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Connector) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/connectClusters/{connectClustersId}/connectors/{connectorsId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.connectClusters.connectors.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsConnectClustersConnectorsGetRequest',
        response_type_name='Connector',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the connectors in a given Connect cluster.

      Args:
        request: (ManagedkafkaProjectsLocationsConnectClustersConnectorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConnectorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/connectClusters/{connectClustersId}/connectors',
        http_method='GET',
        method_id='managedkafka.projects.locations.connectClusters.connectors.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/connectors',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsConnectClustersConnectorsListRequest',
        response_type_name='ListConnectorsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the properties of a connector.

      Args:
        request: (ManagedkafkaProjectsLocationsConnectClustersConnectorsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Connector) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/connectClusters/{connectClustersId}/connectors/{connectorsId}',
        http_method='PATCH',
        method_id='managedkafka.projects.locations.connectClusters.connectors.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='connector',
        request_type_name='ManagedkafkaProjectsLocationsConnectClustersConnectorsPatchRequest',
        response_type_name='Connector',
        supports_download=False,
    )

    def Pause(self, request, global_params=None):
      r"""Pauses the connector and its tasks.

      Args:
        request: (ManagedkafkaProjectsLocationsConnectClustersConnectorsPauseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PauseConnectorResponse) The response message.
      """
      config = self.GetMethodConfig('Pause')
      return self._RunMethod(
          config, request, global_params=global_params)

    Pause.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/connectClusters/{connectClustersId}/connectors/{connectorsId}:pause',
        http_method='POST',
        method_id='managedkafka.projects.locations.connectClusters.connectors.pause',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:pause',
        request_field='pauseConnectorRequest',
        request_type_name='ManagedkafkaProjectsLocationsConnectClustersConnectorsPauseRequest',
        response_type_name='PauseConnectorResponse',
        supports_download=False,
    )

    def Restart(self, request, global_params=None):
      r"""Restarts the connector.

      Args:
        request: (ManagedkafkaProjectsLocationsConnectClustersConnectorsRestartRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RestartConnectorResponse) The response message.
      """
      config = self.GetMethodConfig('Restart')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restart.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/connectClusters/{connectClustersId}/connectors/{connectorsId}:restart',
        http_method='POST',
        method_id='managedkafka.projects.locations.connectClusters.connectors.restart',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:restart',
        request_field='restartConnectorRequest',
        request_type_name='ManagedkafkaProjectsLocationsConnectClustersConnectorsRestartRequest',
        response_type_name='RestartConnectorResponse',
        supports_download=False,
    )

    def Resume(self, request, global_params=None):
      r"""Resumes the connector and its tasks.

      Args:
        request: (ManagedkafkaProjectsLocationsConnectClustersConnectorsResumeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResumeConnectorResponse) The response message.
      """
      config = self.GetMethodConfig('Resume')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resume.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/connectClusters/{connectClustersId}/connectors/{connectorsId}:resume',
        http_method='POST',
        method_id='managedkafka.projects.locations.connectClusters.connectors.resume',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:resume',
        request_field='resumeConnectorRequest',
        request_type_name='ManagedkafkaProjectsLocationsConnectClustersConnectorsResumeRequest',
        response_type_name='ResumeConnectorResponse',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stops the connector.

      Args:
        request: (ManagedkafkaProjectsLocationsConnectClustersConnectorsStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (StopConnectorResponse) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/connectClusters/{connectClustersId}/connectors/{connectorsId}:stop',
        http_method='POST',
        method_id='managedkafka.projects.locations.connectClusters.connectors.stop',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:stop',
        request_field='stopConnectorRequest',
        request_type_name='ManagedkafkaProjectsLocationsConnectClustersConnectorsStopRequest',
        response_type_name='StopConnectorResponse',
        supports_download=False,
    )

  class ProjectsLocationsConnectClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_connectClusters resource."""

    _NAME = 'projects_locations_connectClusters'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsConnectClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Kafka Connect cluster in a given project and location.

      Args:
        request: (ManagedkafkaProjectsLocationsConnectClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/connectClusters',
        http_method='POST',
        method_id='managedkafka.projects.locations.connectClusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['connectClusterId', 'requestId'],
        relative_path='v1/{+parent}/connectClusters',
        request_field='connectCluster',
        request_type_name='ManagedkafkaProjectsLocationsConnectClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Connect cluster.

      Args:
        request: (ManagedkafkaProjectsLocationsConnectClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/connectClusters/{connectClustersId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.connectClusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsConnectClustersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the properties of a single Kafka Connect cluster.

      Args:
        request: (ManagedkafkaProjectsLocationsConnectClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConnectCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/connectClusters/{connectClustersId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.connectClusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsConnectClustersGetRequest',
        response_type_name='ConnectCluster',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Kafka Connect clusters in a given project and location.

      Args:
        request: (ManagedkafkaProjectsLocationsConnectClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConnectClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/connectClusters',
        http_method='GET',
        method_id='managedkafka.projects.locations.connectClusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/connectClusters',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsConnectClustersListRequest',
        response_type_name='ListConnectClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the properties of a single Kafka Connect cluster.

      Args:
        request: (ManagedkafkaProjectsLocationsConnectClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/connectClusters/{connectClustersId}',
        http_method='PATCH',
        method_id='managedkafka.projects.locations.connectClusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='connectCluster',
        request_type_name='ManagedkafkaProjectsLocationsConnectClustersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (ManagedkafkaProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='managedkafka.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='ManagedkafkaProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (ManagedkafkaProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (ManagedkafkaProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (ManagedkafkaProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='managedkafka.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesCompatibilityService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_compatibility resource."""

    _NAME = 'projects_locations_schemaRegistries_compatibility'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesCompatibilityService, self).__init__(client)
      self._upload_configs = {
          }

    def CheckCompatibility(self, request, global_params=None):
      r"""Check compatibility of a schema with all versions or a specific version of a subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesCompatibilityCheckCompatibilityRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CheckCompatibilityResponse) The response message.
      """
      config = self.GetMethodConfig('CheckCompatibility')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckCompatibility.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/compatibility/{compatibilityId}',
        http_method='POST',
        method_id='managedkafka.projects.locations.schemaRegistries.compatibility.checkCompatibility',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='checkCompatibilityRequest',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesCompatibilityCheckCompatibilityRequest',
        response_type_name='CheckCompatibilityResponse',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesConfigService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_config resource."""

    _NAME = 'projects_locations_schemaRegistries_config'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesConfigService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Delete schema config for a subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesConfigDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaConfig) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/config/{configId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.schemaRegistries.config.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesConfigDeleteRequest',
        response_type_name='SchemaConfig',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get schema config at global level or for a subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesConfigGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/config/{configId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.config.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['defaultToGlobal'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesConfigGetRequest',
        response_type_name='SchemaConfig',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Update config at global level or for a subject. Creates a SchemaSubject-level SchemaConfig if it does not exist.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesConfigUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaConfig) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/config/{configId}',
        http_method='PUT',
        method_id='managedkafka.projects.locations.schemaRegistries.config.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='updateSchemaConfigRequest',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesConfigUpdateRequest',
        response_type_name='SchemaConfig',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesContextsCompatibilityService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_contexts_compatibility resource."""

    _NAME = 'projects_locations_schemaRegistries_contexts_compatibility'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesContextsCompatibilityService, self).__init__(client)
      self._upload_configs = {
          }

    def CheckCompatibility(self, request, global_params=None):
      r"""Check compatibility of a schema with all versions or a specific version of a subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsCompatibilityCheckCompatibilityRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CheckCompatibilityResponse) The response message.
      """
      config = self.GetMethodConfig('CheckCompatibility')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckCompatibility.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/compatibility/{compatibilityId}',
        http_method='POST',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.compatibility.checkCompatibility',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='checkCompatibilityRequest',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsCompatibilityCheckCompatibilityRequest',
        response_type_name='CheckCompatibilityResponse',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesContextsConfigService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_contexts_config resource."""

    _NAME = 'projects_locations_schemaRegistries_contexts_config'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesContextsConfigService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Delete schema config for a subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsConfigDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaConfig) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/config/{configId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.config.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsConfigDeleteRequest',
        response_type_name='SchemaConfig',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get schema config at global level or for a subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsConfigGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/config/{configId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.config.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['defaultToGlobal'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsConfigGetRequest',
        response_type_name='SchemaConfig',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Update config at global level or for a subject. Creates a SchemaSubject-level SchemaConfig if it does not exist.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsConfigUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaConfig) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/config/{configId}',
        http_method='PUT',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.config.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='updateSchemaConfigRequest',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsConfigUpdateRequest',
        response_type_name='SchemaConfig',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesContextsModeService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_contexts_mode resource."""

    _NAME = 'projects_locations_schemaRegistries_contexts_mode'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesContextsModeService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Delete schema mode for a subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsModeDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaMode) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/mode/{modeId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.mode.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsModeDeleteRequest',
        response_type_name='SchemaMode',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get mode at global level or for a subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsModeGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaMode) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/mode/{modeId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.mode.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsModeGetRequest',
        response_type_name='SchemaMode',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Update mode at global level or for a subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsModeUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaMode) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/mode/{modeId}',
        http_method='PUT',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.mode.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='updateSchemaModeRequest',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsModeUpdateRequest',
        response_type_name='SchemaMode',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesContextsSchemasSubjectsService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_contexts_schemas_subjects resource."""

    _NAME = 'projects_locations_schemaRegistries_contexts_schemas_subjects'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesContextsSchemasSubjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List subjects which reference a particular schema id. The response will be an array of subject names.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasSubjectsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/schemas/{schemasId}/subjects',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.schemas.subjects.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['deleted', 'subject'],
        relative_path='v1/{+parent}/subjects',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasSubjectsListRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesContextsSchemasTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_contexts_schemas_types resource."""

    _NAME = 'projects_locations_schemaRegistries_contexts_schemas_types'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesContextsSchemasTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List the supported schema types. The response will be an array of schema types.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/schemas/types',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.schemas.types.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/schemas/types',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasTypesListRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesContextsSchemasVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_contexts_schemas_versions resource."""

    _NAME = 'projects_locations_schemaRegistries_contexts_schemas_versions'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesContextsSchemasVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List the schema versions for the given schema id. The response will be an array of subject-version pairs as: [{"subject":"subject1", "version":1}, {"subject":"subject2", "version":2}].

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/schemas/{schemasId}/versions',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.schemas.versions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['deleted', 'subject'],
        relative_path='v1/{+parent}/versions',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasVersionsListRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesContextsSchemasService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_contexts_schemas resource."""

    _NAME = 'projects_locations_schemaRegistries_contexts_schemas'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesContextsSchemasService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get the schema for the given schema id.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Schema) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/schemas/{schemasId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.schemas.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['subject'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasGetRequest',
        response_type_name='Schema',
        supports_download=False,
    )

    def GetSchema(self, request, global_params=None):
      r"""Get the schema string for the given schema id. The response will be the schema string.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasGetSchemaRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('GetSchema')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSchema.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/schemas/{schemasId}/schema',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.schemas.getSchema',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['subject'],
        relative_path='v1/{+name}/schema',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasGetSchemaRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesContextsSubjectsVersionsReferencedbyService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_contexts_subjects_versions_referencedby resource."""

    _NAME = 'projects_locations_schemaRegistries_contexts_subjects_versions_referencedby'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesContextsSubjectsVersionsReferencedbyService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Get a list of IDs of schemas that reference the schema with the given subject and version.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsReferencedbyListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/subjects/{subjectsId}/versions/{versionsId}/referencedby',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.subjects.versions.referencedby.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/referencedby',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsReferencedbyListRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesContextsSubjectsVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_contexts_subjects_versions resource."""

    _NAME = 'projects_locations_schemaRegistries_contexts_subjects_versions'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesContextsSubjectsVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Register a new version under a given subject with the given schema.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CreateVersionResponse) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/subjects/{subjectsId}/versions',
        http_method='POST',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.subjects.versions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/versions',
        request_field='createVersionRequest',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsCreateRequest',
        response_type_name='CreateVersionResponse',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a version of a subject. The response will be the deleted version id.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/subjects/{subjectsId}/versions/{versionsId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.subjects.versions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['permanent'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsDeleteRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a versioned schema (schema with subject/version) of a subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaVersion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/subjects/{subjectsId}/versions/{versionsId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.subjects.versions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['deleted'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsGetRequest',
        response_type_name='SchemaVersion',
        supports_download=False,
    )

    def GetSchema(self, request, global_params=None):
      r"""Get the schema string only for a version of a subject. The response will be the schema string.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsGetSchemaRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('GetSchema')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSchema.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/subjects/{subjectsId}/versions/{versionsId}/schema',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.subjects.versions.getSchema',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['deleted'],
        relative_path='v1/{+name}/schema',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsGetSchemaRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Get all versions of a subject. The response will be an array of versions of the subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/subjects/{subjectsId}/versions',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.subjects.versions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['deleted'],
        relative_path='v1/{+parent}/versions',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsListRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesContextsSubjectsService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_contexts_subjects resource."""

    _NAME = 'projects_locations_schemaRegistries_contexts_subjects'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesContextsSubjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Delete a subject. The response will be an array of versions of the deleted subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/subjects/{subjectsId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.subjects.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['permanent'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsDeleteRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List subjects in the schema registry. The response will be an array of subject names.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/subjects',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.subjects.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['deleted', 'subjectPrefix'],
        relative_path='v1/{+parent}/subjects',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsListRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def LookupVersion(self, request, global_params=None):
      r"""Lookup a schema under the specified subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsLookupVersionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaVersion) The response message.
      """
      config = self.GetMethodConfig('LookupVersion')
      return self._RunMethod(
          config, request, global_params=global_params)

    LookupVersion.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}/subjects/{subjectsId}',
        http_method='POST',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.subjects.lookupVersion',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}',
        request_field='lookupVersionRequest',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsLookupVersionRequest',
        response_type_name='SchemaVersion',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesContextsService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_contexts resource."""

    _NAME = 'projects_locations_schemaRegistries_contexts'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesContextsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get the context.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Context) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts/{contextsId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsGetRequest',
        response_type_name='Context',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List contexts for a schema registry.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesContextsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/contexts',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.contexts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/contexts',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesContextsListRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesModeService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_mode resource."""

    _NAME = 'projects_locations_schemaRegistries_mode'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesModeService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Delete schema mode for a subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesModeDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaMode) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/mode/{modeId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.schemaRegistries.mode.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesModeDeleteRequest',
        response_type_name='SchemaMode',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get mode at global level or for a subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesModeGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaMode) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/mode/{modeId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.mode.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesModeGetRequest',
        response_type_name='SchemaMode',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Update mode at global level or for a subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesModeUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaMode) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/mode/{modeId}',
        http_method='PUT',
        method_id='managedkafka.projects.locations.schemaRegistries.mode.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='updateSchemaModeRequest',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesModeUpdateRequest',
        response_type_name='SchemaMode',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesSchemasSubjectsService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_schemas_subjects resource."""

    _NAME = 'projects_locations_schemaRegistries_schemas_subjects'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesSchemasSubjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List subjects which reference a particular schema id. The response will be an array of subject names.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesSchemasSubjectsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/schemas/{schemasId}/subjects',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.schemas.subjects.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['deleted', 'subject'],
        relative_path='v1/{+parent}/subjects',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesSchemasSubjectsListRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesSchemasTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_schemas_types resource."""

    _NAME = 'projects_locations_schemaRegistries_schemas_types'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesSchemasTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List the supported schema types. The response will be an array of schema types.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesSchemasTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/schemas/types',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.schemas.types.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/schemas/types',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesSchemasTypesListRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesSchemasVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_schemas_versions resource."""

    _NAME = 'projects_locations_schemaRegistries_schemas_versions'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesSchemasVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List the schema versions for the given schema id. The response will be an array of subject-version pairs as: [{"subject":"subject1", "version":1}, {"subject":"subject2", "version":2}].

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesSchemasVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/schemas/{schemasId}/versions',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.schemas.versions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['deleted', 'subject'],
        relative_path='v1/{+parent}/versions',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesSchemasVersionsListRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesSchemasService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_schemas resource."""

    _NAME = 'projects_locations_schemaRegistries_schemas'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesSchemasService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get the schema for the given schema id.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesSchemasGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Schema) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/schemas/{schemasId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.schemas.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['subject'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesSchemasGetRequest',
        response_type_name='Schema',
        supports_download=False,
    )

    def GetSchema(self, request, global_params=None):
      r"""Get the schema string for the given schema id. The response will be the schema string.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesSchemasGetSchemaRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('GetSchema')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSchema.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/schemas/{schemasId}/schema',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.schemas.getSchema',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['subject'],
        relative_path='v1/{+name}/schema',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesSchemasGetSchemaRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesSubjectsVersionsReferencedbyService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_subjects_versions_referencedby resource."""

    _NAME = 'projects_locations_schemaRegistries_subjects_versions_referencedby'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesSubjectsVersionsReferencedbyService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Get a list of IDs of schemas that reference the schema with the given subject and version.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsReferencedbyListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/subjects/{subjectsId}/versions/{versionsId}/referencedby',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.subjects.versions.referencedby.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/referencedby',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsReferencedbyListRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesSubjectsVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_subjects_versions resource."""

    _NAME = 'projects_locations_schemaRegistries_subjects_versions'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesSubjectsVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Register a new version under a given subject with the given schema.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CreateVersionResponse) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/subjects/{subjectsId}/versions',
        http_method='POST',
        method_id='managedkafka.projects.locations.schemaRegistries.subjects.versions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/versions',
        request_field='createVersionRequest',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsCreateRequest',
        response_type_name='CreateVersionResponse',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a version of a subject. The response will be the deleted version id.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/subjects/{subjectsId}/versions/{versionsId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.schemaRegistries.subjects.versions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['permanent'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsDeleteRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a versioned schema (schema with subject/version) of a subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaVersion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/subjects/{subjectsId}/versions/{versionsId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.subjects.versions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['deleted'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsGetRequest',
        response_type_name='SchemaVersion',
        supports_download=False,
    )

    def GetSchema(self, request, global_params=None):
      r"""Get the schema string only for a version of a subject. The response will be the schema string.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsGetSchemaRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('GetSchema')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSchema.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/subjects/{subjectsId}/versions/{versionsId}/schema',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.subjects.versions.getSchema',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['deleted'],
        relative_path='v1/{+name}/schema',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsGetSchemaRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Get all versions of a subject. The response will be an array of versions of the subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/subjects/{subjectsId}/versions',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.subjects.versions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['deleted'],
        relative_path='v1/{+parent}/versions',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsListRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesSubjectsService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries_subjects resource."""

    _NAME = 'projects_locations_schemaRegistries_subjects'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesSubjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Delete a subject. The response will be an array of versions of the deleted subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/subjects/{subjectsId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.schemaRegistries.subjects.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['permanent'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsDeleteRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List subjects in the schema registry. The response will be an array of subject names.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/subjects',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.subjects.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['deleted', 'subjectPrefix'],
        relative_path='v1/{+parent}/subjects',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsListRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def LookupVersion(self, request, global_params=None):
      r"""Lookup a schema under the specified subject.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsLookupVersionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaVersion) The response message.
      """
      config = self.GetMethodConfig('LookupVersion')
      return self._RunMethod(
          config, request, global_params=global_params)

    LookupVersion.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}/subjects/{subjectsId}',
        http_method='POST',
        method_id='managedkafka.projects.locations.schemaRegistries.subjects.lookupVersion',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}',
        request_field='lookupVersionRequest',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsLookupVersionRequest',
        response_type_name='SchemaVersion',
        supports_download=False,
    )

  class ProjectsLocationsSchemaRegistriesService(base_api.BaseApiService):
    """Service class for the projects_locations_schemaRegistries resource."""

    _NAME = 'projects_locations_schemaRegistries'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsSchemaRegistriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a schema registry instance.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaRegistry) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries',
        http_method='POST',
        method_id='managedkafka.projects.locations.schemaRegistries.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/schemaRegistries',
        request_field='createSchemaRegistryRequest',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesCreateRequest',
        response_type_name='SchemaRegistry',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a schema registry instance.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}',
        http_method='DELETE',
        method_id='managedkafka.projects.locations.schemaRegistries.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get the schema registry instance.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SchemaRegistry) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries/{schemaRegistriesId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesGetRequest',
        response_type_name='SchemaRegistry',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List schema registries.

      Args:
        request: (ManagedkafkaProjectsLocationsSchemaRegistriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSchemaRegistriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schemaRegistries',
        http_method='GET',
        method_id='managedkafka.projects.locations.schemaRegistries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/schemaRegistries',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsSchemaRegistriesListRequest',
        response_type_name='ListSchemaRegistriesResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (ManagedkafkaProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='managedkafka.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (ManagedkafkaProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='managedkafka.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='ManagedkafkaProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(ManagedkafkaV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
