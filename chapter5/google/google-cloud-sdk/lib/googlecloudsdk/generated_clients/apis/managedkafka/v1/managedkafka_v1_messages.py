"""Generated message classes for managedkafka version v1.

Manage Apache Kafka clusters and resources.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'managedkafka'


class AccessConfig(_messages.Message):
  r"""The configuration of access to the Kafka cluster.

  Fields:
    networkConfigs: Required. Virtual Private Cloud (VPC) networks that must
      be granted direct access to the Kafka cluster. Minimum of 1 network is
      required. Maximum 10 networks can be specified.
  """

  networkConfigs = _messages.MessageField('NetworkConfig', 1, repeated=True)


class Acl(_messages.Message):
  r"""Represents the set of ACLs for a given Kafka Resource Pattern, which
  consists of resource_type, resource_name and pattern_type.

  Fields:
    aclEntries: Required. The ACL entries that apply to the resource pattern.
      The maximum number of allowed entries 100.
    etag: Optional. `etag` is used for concurrency control. An `etag` is
      returned in the response to `GetAcl` and `CreateAcl`. Callers are
      required to put that etag in the request to `UpdateAcl` to ensure that
      their change will be applied to the same version of the acl that exists
      in the Kafka Cluster. A terminal 'T' character in the etag indicates
      that the AclEntries were truncated; more entries for the Acl exist on
      the Kafka Cluster, but can't be returned in the Acl due to repeated
      field limits.
    name: Identifier. The name for the acl. Represents a single Resource
      Pattern. Structured like:
      projects/{project}/locations/{location}/clusters/{cluster}/acls/{acl_id}
      The structure of `acl_id` defines the Resource Pattern (resource_type,
      resource_name, pattern_type) of the acl. `acl_id` is structured like one
      of the following: For acls on the cluster: `cluster` For acls on a
      single resource within the cluster: `topic/{resource_name}`
      `consumerGroup/{resource_name}` `transactionalId/{resource_name}` For
      acls on all resources that match a prefix:
      `topicPrefixed/{resource_name}` `consumerGroupPrefixed/{resource_name}`
      `transactionalIdPrefixed/{resource_name}` For acls on all resources of a
      given type (i.e. the wildcard literal "*"): `allTopics` (represents
      `topic/*`) `allConsumerGroups` (represents `consumerGroup/*`)
      `allTransactionalIds` (represents `transactionalId/*`)
    patternType: Output only. The ACL pattern type derived from the name. One
      of: LITERAL, PREFIXED.
    resourceName: Output only. The ACL resource name derived from the name.
      For cluster resource_type, this is always "kafka-cluster". Can be the
      wildcard literal "*".
    resourceType: Output only. The ACL resource type derived from the name.
      One of: CLUSTER, TOPIC, GROUP, TRANSACTIONAL_ID.
  """

  aclEntries = _messages.MessageField('AclEntry', 1, repeated=True)
  etag = _messages.StringField(2)
  name = _messages.StringField(3)
  patternType = _messages.StringField(4)
  resourceName = _messages.StringField(5)
  resourceType = _messages.StringField(6)


class AclEntry(_messages.Message):
  r"""Represents the access granted for a given Resource Pattern in an ACL.

  Fields:
    host: Required. The host. Must be set to "*" for Managed Service for
      Apache Kafka.
    operation: Required. The operation type. Allowed values are (case
      insensitive): ALL, READ, WRITE, CREATE, DELETE, ALTER, DESCRIBE,
      CLUSTER_ACTION, DESCRIBE_CONFIGS, ALTER_CONFIGS, and IDEMPOTENT_WRITE.
      See https://kafka.apache.org/documentation/#operations_resources_and_pro
      tocols for valid combinations of resource_type and operation for
      different Kafka API requests.
    permissionType: Required. The permission type. Accepted values are (case
      insensitive): ALLOW, DENY.
    principal: Required. The principal. Specified as Google Cloud account,
      with the Kafka StandardAuthorizer prefix "User:". For example:
      "User:<EMAIL>". Can be
      the wildcard "User:*" to refer to all users.
  """

  host = _messages.StringField(1)
  operation = _messages.StringField(2)
  permissionType = _messages.StringField(3)
  principal = _messages.StringField(4)


class AddAclEntryResponse(_messages.Message):
  r"""Response for AddAclEntry.

  Fields:
    acl: The updated acl.
    aclCreated: Whether the acl was created as a result of adding the acl
      entry.
  """

  acl = _messages.MessageField('Acl', 1)
  aclCreated = _messages.BooleanField(2)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CapacityConfig(_messages.Message):
  r"""A capacity configuration of a Kafka cluster.

  Fields:
    memoryBytes: Required. The memory to provision for the cluster in bytes.
      The CPU:memory ratio (vCPU:GiB) must be between 1:1 and 1:8. Minimum:
      ********** (3 GiB).
    vcpuCount: Required. The number of vCPUs to provision for the cluster.
      Minimum: 3.
  """

  memoryBytes = _messages.IntegerField(1)
  vcpuCount = _messages.IntegerField(2)


class CertificateAuthorityServiceConfig(_messages.Message):
  r"""A configuration for the Google Certificate Authority Service.

  Fields:
    caPool: Required. The name of the CA pool to pull CA certificates from.
      Structured like:
      projects/{project}/locations/{location}/caPools/{ca_pool}. The CA pool
      does not need to be in the same project or location as the Kafka
      cluster.
  """

  caPool = _messages.StringField(1)


class CheckCompatibilityRequest(_messages.Message):
  r"""Request for CheckCompatibility.

  Enums:
    SchemaTypeValueValuesEnum: Optional. The schema type of the schema.

  Fields:
    references: Optional. The schema references used by the schema.
    schema: Required. The schema payload
    schemaType: Optional. The schema type of the schema.
    verbose: Optional. If true, the response will contain the compatibility
      check result with reasons for failed checks. The default is false.
  """

  class SchemaTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The schema type of the schema.

    Values:
      SCHEMA_TYPE_UNSPECIFIED: No schema type. The default will be AVRO.
      AVRO: Avro schema type.
      JSON: JSON schema type.
      PROTOBUF: Protobuf schema type.
    """
    SCHEMA_TYPE_UNSPECIFIED = 0
    AVRO = 1
    JSON = 2
    PROTOBUF = 3

  references = _messages.MessageField('SchemaReference', 1, repeated=True)
  schema = _messages.StringField(2)
  schemaType = _messages.EnumField('SchemaTypeValueValuesEnum', 3)
  verbose = _messages.BooleanField(4)


class CheckCompatibilityResponse(_messages.Message):
  r"""Response for CheckCompatibility.

  Fields:
    is_compatible: The compatibility check result. If true, the schema is
      compatible with the resource.
    messages: Failure reasons if verbose = true.
  """

  is_compatible = _messages.BooleanField(1)
  messages = _messages.StringField(2, repeated=True)


class Cluster(_messages.Message):
  r"""An Apache Kafka cluster deployed in a location.

  Enums:
    StateValueValuesEnum: Output only. The current state of the cluster.

  Messages:
    BrokersPerZoneValue: Output only. Only populated when FULL view is
      requested. The number of brokers per zone.
    LabelsValue: Optional. Labels as key value pairs.

  Fields:
    brokersPerZone: Output only. Only populated when FULL view is requested.
      The number of brokers per zone.
    capacityConfig: Required. Capacity configuration for the Kafka cluster.
    createTime: Output only. The time when the cluster was created.
    gcpConfig: Required. Configuration properties for a Kafka cluster deployed
      to Google Cloud Platform.
    kafkaVersion: Output only. Only populated when FULL view is requested. The
      Kafka version of the cluster.
    labels: Optional. Labels as key value pairs.
    name: Identifier. The name of the cluster. Structured like:
      projects/{project_number}/locations/{location}/clusters/{cluster_id}
    rebalanceConfig: Optional. Rebalance configuration for the Kafka cluster.
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    state: Output only. The current state of the cluster.
    tlsConfig: Optional. TLS configuration for the Kafka cluster.
    updateOptions: Optional. UpdateOptions represents options that control how
      updates to the cluster are applied.
    updateTime: Output only. The time when the cluster was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the cluster.

    Values:
      STATE_UNSPECIFIED: A state was not specified.
      CREATING: The cluster is being created.
      ACTIVE: The cluster is active.
      DELETING: The cluster is being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BrokersPerZoneValue(_messages.Message):
    r"""Output only. Only populated when FULL view is requested. The number of
    brokers per zone.

    Messages:
      AdditionalProperty: An additional property for a BrokersPerZoneValue
        object.

    Fields:
      additionalProperties: Additional properties of type BrokersPerZoneValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BrokersPerZoneValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  brokersPerZone = _messages.MessageField('BrokersPerZoneValue', 1)
  capacityConfig = _messages.MessageField('CapacityConfig', 2)
  createTime = _messages.StringField(3)
  gcpConfig = _messages.MessageField('GcpConfig', 4)
  kafkaVersion = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  rebalanceConfig = _messages.MessageField('RebalanceConfig', 8)
  satisfiesPzi = _messages.BooleanField(9)
  satisfiesPzs = _messages.BooleanField(10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  tlsConfig = _messages.MessageField('TlsConfig', 12)
  updateOptions = _messages.MessageField('UpdateOptions', 13)
  updateTime = _messages.StringField(14)


class ConnectAccessConfig(_messages.Message):
  r"""The configuration of access to the Kafka Connect cluster.

  Fields:
    networkConfigs: Required. Virtual Private Cloud (VPC) networks that must
      be granted direct access to the Kafka Connect cluster. Minimum of 1
      network is required. Maximum 10 networks can be specified.
  """

  networkConfigs = _messages.MessageField('ConnectNetworkConfig', 1, repeated=True)


class ConnectCluster(_messages.Message):
  r"""An Apache Kafka Connect cluster deployed in a location.

  Enums:
    StateValueValuesEnum: Output only. The current state of the cluster.

  Messages:
    ConfigValue: Optional. Configurations for the worker that are overridden
      from the defaults. The key of the map is a Kafka Connect worker property
      name, for example: `exactly.once.source.support`.
    LabelsValue: Optional. Labels as key value pairs.

  Fields:
    capacityConfig: Required. Capacity configuration for the Kafka Connect
      cluster.
    config: Optional. Configurations for the worker that are overridden from
      the defaults. The key of the map is a Kafka Connect worker property
      name, for example: `exactly.once.source.support`.
    createTime: Output only. The time when the cluster was created.
    gcpConfig: Required. Configuration properties for a Kafka Connect cluster
      deployed to Google Cloud Platform.
    kafkaCluster: Required. Immutable. The name of the Kafka cluster this
      Kafka Connect cluster is attached to. Structured like:
      projects/{project}/locations/{location}/clusters/{cluster}
    labels: Optional. Labels as key value pairs.
    name: Identifier. The name of the Kafka Connect cluster. Structured like:
      projects/{project_number}/locations/{location}/connectClusters/{connect_
      cluster_id}
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    state: Output only. The current state of the cluster.
    updateTime: Output only. The time when the cluster was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the cluster.

    Values:
      STATE_UNSPECIFIED: A state was not specified.
      CREATING: The cluster is being created.
      ACTIVE: The cluster is active.
      DELETING: The cluster is being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConfigValue(_messages.Message):
    r"""Optional. Configurations for the worker that are overridden from the
    defaults. The key of the map is a Kafka Connect worker property name, for
    example: `exactly.once.source.support`.

    Messages:
      AdditionalProperty: An additional property for a ConfigValue object.

    Fields:
      additionalProperties: Additional properties of type ConfigValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  capacityConfig = _messages.MessageField('CapacityConfig', 1)
  config = _messages.MessageField('ConfigValue', 2)
  createTime = _messages.StringField(3)
  gcpConfig = _messages.MessageField('ConnectGcpConfig', 4)
  kafkaCluster = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  satisfiesPzi = _messages.BooleanField(8)
  satisfiesPzs = _messages.BooleanField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  updateTime = _messages.StringField(11)


class ConnectGcpConfig(_messages.Message):
  r"""Configuration properties for a Kafka Connect cluster deployed to Google
  Cloud Platform.

  Fields:
    accessConfig: Required. Access configuration for the Kafka Connect
      cluster.
    secretPaths: Optional. Secrets to load into workers. Exact SecretVersions
      from Secret Manager must be provided -- aliases are not supported. Up to
      32 secrets may be loaded into one cluster. Format:
      projects//secrets//versions/
  """

  accessConfig = _messages.MessageField('ConnectAccessConfig', 1)
  secretPaths = _messages.StringField(2, repeated=True)


class ConnectNetworkConfig(_messages.Message):
  r"""The configuration of a Virtual Private Cloud (VPC) network that can
  access the Kafka Connect cluster.

  Fields:
    additionalSubnets: Optional. Additional subnets may be specified. They may
      be in another region, but must be in the same VPC network. The Connect
      workers can communicate with network endpoints in either the primary or
      additional subnets.
    dnsDomainNames: Optional. Additional DNS domain names from the subnet's
      network to be made visible to the Connect Cluster. When using
      MirrorMaker2, it's necessary to add the bootstrap address's dns domain
      name of the target cluster to make it visible to the connector. For
      example: my-kafka-cluster.us-central1.managedkafka.my-project.cloud.goog
    primarySubnet: Required. VPC subnet to make available to the Kafka Connect
      cluster. Structured like:
      projects/{project}/regions/{region}/subnetworks/{subnet_id} It is used
      to create a Private Service Connect (PSC) interface for the Kafka
      Connect workers. It must be located in the same region as the Kafka
      Connect cluster. The CIDR range of the subnet must be within the IPv4
      address ranges for private networks, as specified in RFC 1918. The
      primary subnet CIDR range must have a minimum size of /22 (1024
      addresses).
  """

  additionalSubnets = _messages.StringField(1, repeated=True)
  dnsDomainNames = _messages.StringField(2, repeated=True)
  primarySubnet = _messages.StringField(3)


class Connector(_messages.Message):
  r"""A Kafka Connect connector in a given ConnectCluster.

  Enums:
    StateValueValuesEnum: Output only. The current state of the connector.

  Messages:
    ConfigsValue: Optional. Connector config as keys/values. The keys of the
      map are connector property names, for example: `connector.class`,
      `tasks.max`, `key.converter`.

  Fields:
    configs: Optional. Connector config as keys/values. The keys of the map
      are connector property names, for example: `connector.class`,
      `tasks.max`, `key.converter`.
    name: Identifier. The name of the connector. Structured like: projects/{pr
      oject}/locations/{location}/connectClusters/{connect_cluster}/connectors
      /{connector}
    state: Output only. The current state of the connector.
    taskRestartPolicy: Optional. Restarts the individual tasks of a Connector.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the connector.

    Values:
      STATE_UNSPECIFIED: A state was not specified.
      UNASSIGNED: The connector is not assigned to any tasks, usually
        transient.
      RUNNING: The connector is running.
      PAUSED: The connector has been paused.
      FAILED: The connector has failed. See logs for why.
      RESTARTING: The connector is restarting.
      STOPPED: The connector has been stopped.
    """
    STATE_UNSPECIFIED = 0
    UNASSIGNED = 1
    RUNNING = 2
    PAUSED = 3
    FAILED = 4
    RESTARTING = 5
    STOPPED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConfigsValue(_messages.Message):
    r"""Optional. Connector config as keys/values. The keys of the map are
    connector property names, for example: `connector.class`, `tasks.max`,
    `key.converter`.

    Messages:
      AdditionalProperty: An additional property for a ConfigsValue object.

    Fields:
      additionalProperties: Additional properties of type ConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  configs = _messages.MessageField('ConfigsValue', 1)
  name = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  taskRestartPolicy = _messages.MessageField('TaskRetryPolicy', 4)


class ConsumerGroup(_messages.Message):
  r"""A Kafka consumer group in a given cluster.

  Messages:
    TopicsValue: Optional. Metadata for this consumer group for all topics it
      has metadata for. The key of the map is a topic name, structured like: p
      rojects/{project}/locations/{location}/clusters/{cluster}/topics/{topic}

  Fields:
    name: Identifier. The name of the consumer group. The `consumer_group`
      segment is used when connecting directly to the cluster. Structured
      like: projects/{project}/locations/{location}/clusters/{cluster}/consume
      rGroups/{consumer_group}
    topics: Optional. Metadata for this consumer group for all topics it has
      metadata for. The key of the map is a topic name, structured like: proje
      cts/{project}/locations/{location}/clusters/{cluster}/topics/{topic}
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TopicsValue(_messages.Message):
    r"""Optional. Metadata for this consumer group for all topics it has
    metadata for. The key of the map is a topic name, structured like:
    projects/{project}/locations/{location}/clusters/{cluster}/topics/{topic}

    Messages:
      AdditionalProperty: An additional property for a TopicsValue object.

    Fields:
      additionalProperties: Additional properties of type TopicsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TopicsValue object.

      Fields:
        key: Name of the additional property.
        value: A ConsumerTopicMetadata attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ConsumerTopicMetadata', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  name = _messages.StringField(1)
  topics = _messages.MessageField('TopicsValue', 2)


class ConsumerPartitionMetadata(_messages.Message):
  r"""Metadata for a consumer group corresponding to a specific partition.

  Fields:
    metadata: Optional. The associated metadata for this partition, or empty
      if it does not exist.
    offset: Required. The current offset for this partition, or 0 if no offset
      has been committed.
  """

  metadata = _messages.StringField(1)
  offset = _messages.IntegerField(2)


class ConsumerTopicMetadata(_messages.Message):
  r"""Metadata for a consumer group corresponding to a specific topic.

  Messages:
    PartitionsValue: Optional. Metadata for this consumer group and topic for
      all partition indexes it has metadata for.

  Fields:
    partitions: Optional. Metadata for this consumer group and topic for all
      partition indexes it has metadata for.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PartitionsValue(_messages.Message):
    r"""Optional. Metadata for this consumer group and topic for all partition
    indexes it has metadata for.

    Messages:
      AdditionalProperty: An additional property for a PartitionsValue object.

    Fields:
      additionalProperties: Additional properties of type PartitionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PartitionsValue object.

      Fields:
        key: Name of the additional property.
        value: A ConsumerPartitionMetadata attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ConsumerPartitionMetadata', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  partitions = _messages.MessageField('PartitionsValue', 1)


class Context(_messages.Message):
  r"""Context represents an independent schema grouping in a schema registry
  instance.

  Fields:
    name: Identifier. The name of the context. Structured like: `projects/{pro
      ject}/locations/{location}/schemaRegistries/{schema_registry}/contexts/{
      context}` The context name {context} can contain the following: * Up to
      255 characters. * Allowed characters: letters (uppercase or lowercase),
      numbers, and the following special characters: `.`, `-`, `_`, `+`, `%`,
      and `~`.
    subjects: Optional. The subjects of the context.
  """

  name = _messages.StringField(1)
  subjects = _messages.StringField(2, repeated=True)


class CreateSchemaRegistryRequest(_messages.Message):
  r"""Request to create a schema registry instance.

  Fields:
    schemaRegistry: Required. The schema registry instance to create. The name
      field is ignored.
    schemaRegistryId: Required. The schema registry instance ID to use for
      this schema registry. The ID must contain only letters (a-z, A-Z),
      numbers (0-9), and underscores (-). The maximum length is 63 characters.
      The ID must not start with a number.
  """

  schemaRegistry = _messages.MessageField('SchemaRegistry', 1)
  schemaRegistryId = _messages.StringField(2)


class CreateVersionRequest(_messages.Message):
  r"""Request for CreateVersion.

  Enums:
    SchemaTypeValueValuesEnum: Optional. The type of the schema. It is
      optional. If not specified, the schema type will be AVRO.

  Fields:
    id: Optional. The schema ID of the schema. If not specified, the schema ID
      will be generated by the server. If the schema ID is specified, it must
      not be used by an existing schema that is different from the schema to
      be created.
    normalize: Optional. If true, the schema will be normalized before being
      stored. The default is false.
    references: Optional. The schema references used by the schema.
    schema: Required. The schema payload
    schemaType: Optional. The type of the schema. It is optional. If not
      specified, the schema type will be AVRO.
    version: Optional. The version to create. It is optional. If not
      specified, the version will be created with the max version ID of the
      subject increased by 1. If the version ID is specified, it will be used
      as the new version ID and must not be used by an existing version of the
      subject.
  """

  class SchemaTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of the schema. It is optional. If not specified,
    the schema type will be AVRO.

    Values:
      SCHEMA_TYPE_UNSPECIFIED: No schema type. The default will be AVRO.
      AVRO: Avro schema type.
      JSON: JSON schema type.
      PROTOBUF: Protobuf schema type.
    """
    SCHEMA_TYPE_UNSPECIFIED = 0
    AVRO = 1
    JSON = 2
    PROTOBUF = 3

  id = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  normalize = _messages.BooleanField(2)
  references = _messages.MessageField('SchemaReference', 3, repeated=True)
  schema = _messages.StringField(4)
  schemaType = _messages.EnumField('SchemaTypeValueValuesEnum', 5)
  version = _messages.IntegerField(6, variant=_messages.Variant.INT32)


class CreateVersionResponse(_messages.Message):
  r"""Response for CreateVersion.

  Fields:
    id: The unique identifier of the schema created.
  """

  id = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GcpConfig(_messages.Message):
  r"""Configuration properties for a Kafka cluster deployed to Google Cloud
  Platform.

  Fields:
    accessConfig: Required. Access configuration for the Kafka cluster.
    kmsKey: Optional. Immutable. The Cloud KMS Key name to use for encryption.
      The key must be located in the same region as the cluster and cannot be
      changed. Structured like: projects/{project}/locations/{location}/keyRin
      gs/{key_ring}/cryptoKeys/{crypto_key}.
  """

  accessConfig = _messages.MessageField('AccessConfig', 1)
  kmsKey = _messages.StringField(2)


class HttpBody(_messages.Message):
  r"""Message that represents an arbitrary HTTP body. It should only be used
  for payload formats that can't be represented as JSON, such as raw binary or
  an HTML page. This message can be used both in streaming and non-streaming
  API methods in the request as well as the response. It can be used as a top-
  level request field, which is convenient if one wants to extract parameters
  from either the URL or HTTP template into the request fields and also want
  access to the raw HTTP body. Example: message GetResourceRequest { // A
  unique request id. string request_id = 1; // The raw HTTP body is bound to
  this field. google.api.HttpBody http_body = 2; } service ResourceService {
  rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc
  UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); }
  Example with streaming methods: service CaldavService { rpc
  GetCalendar(stream google.api.HttpBody) returns (stream
  google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns
  (stream google.api.HttpBody); } Use of this type only changes how the
  request and response bodies are handled, all other features will continue to
  work unchanged.

  Messages:
    ExtensionsValueListEntry: A ExtensionsValueListEntry object.

  Fields:
    contentType: The HTTP Content-Type header value specifying the content
      type of the body.
    data: The HTTP request/response body as raw binary.
    extensions: Application specific response metadata. Must be set in the
      first response for streaming APIs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtensionsValueListEntry(_messages.Message):
    r"""A ExtensionsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        ExtensionsValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtensionsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  contentType = _messages.StringField(1)
  data = _messages.BytesField(2)
  extensions = _messages.MessageField('ExtensionsValueListEntry', 3, repeated=True)


class ListAclsResponse(_messages.Message):
  r"""Response for ListAcls.

  Fields:
    acls: The list of acls in the requested parent. The order of the acls is
      unspecified.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page of results. If this field is omitted, there are no more
      results.
  """

  acls = _messages.MessageField('Acl', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListClustersResponse(_messages.Message):
  r"""Response for ListClusters.

  Fields:
    clusters: The list of Clusters in the requested parent.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page of results. If this field is omitted, there are no more
      results.
    unreachable: Locations that could not be reached.
  """

  clusters = _messages.MessageField('Cluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListConnectClustersResponse(_messages.Message):
  r"""Response for ListConnectClusters.

  Fields:
    connectClusters: The list of Connect clusters in the requested parent.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page of results. If this field is omitted, there are no more
      results.
    unreachable: Locations that could not be reached.
  """

  connectClusters = _messages.MessageField('ConnectCluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListConnectorsResponse(_messages.Message):
  r"""Response for ListConnectors.

  Fields:
    connectors: The list of connectors in the requested parent.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page of results. If this field is omitted, there are no more
      results.
  """

  connectors = _messages.MessageField('Connector', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListConsumerGroupsResponse(_messages.Message):
  r"""Response for ListConsumerGroups.

  Fields:
    consumerGroups: The list of consumer group in the requested parent. The
      order of the consumer groups is unspecified.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page of results. If this field is omitted, there are no more
      results.
  """

  consumerGroups = _messages.MessageField('ConsumerGroup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListSchemaRegistriesResponse(_messages.Message):
  r"""Request for ListSchemaRegistries.

  Fields:
    schemaRegistries: The schema registry instances.
  """

  schemaRegistries = _messages.MessageField('SchemaRegistry', 1, repeated=True)


class ListTopicsResponse(_messages.Message):
  r"""Response for ListTopics.

  Fields:
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page of results. If this field is omitted, there are no more
      results.
    topics: The list of topics in the requested parent. The order of the
      topics is unspecified.
  """

  nextPageToken = _messages.StringField(1)
  topics = _messages.MessageField('Topic', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LookupVersionRequest(_messages.Message):
  r"""Request for LookupVersion.

  Enums:
    SchemaTypeValueValuesEnum: Optional. The schema type of the schema.

  Fields:
    deleted: Optional. If true, soft-deleted versions will be included in
      lookup, no matter if the subject is active or soft-deleted. If false,
      soft-deleted versions will be excluded. The default is false.
    normalize: Optional. If true, the schema will be normalized before being
      looked up. The default is false.
    references: Optional. The schema references used by the schema.
    schema: Required. The schema payload
    schemaType: Optional. The schema type of the schema.
  """

  class SchemaTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The schema type of the schema.

    Values:
      SCHEMA_TYPE_UNSPECIFIED: No schema type. The default will be AVRO.
      AVRO: Avro schema type.
      JSON: JSON schema type.
      PROTOBUF: Protobuf schema type.
    """
    SCHEMA_TYPE_UNSPECIFIED = 0
    AVRO = 1
    JSON = 2
    PROTOBUF = 3

  deleted = _messages.BooleanField(1)
  normalize = _messages.BooleanField(2)
  references = _messages.MessageField('SchemaReference', 3, repeated=True)
  schema = _messages.StringField(4)
  schemaType = _messages.EnumField('SchemaTypeValueValuesEnum', 5)


class ManagedkafkaProjectsLocationsClustersAclsAddAclEntryRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersAclsAddAclEntryRequest object.

  Fields:
    acl: Required. The name of the acl to add the acl entry to. Structured
      like: `projects/{project}/locations/{location}/clusters/{cluster}/acls/{
      acl_id}`. The structure of `acl_id` defines the Resource Pattern
      (resource_type, resource_name, pattern_type) of the acl. See `Acl.name`
      for details.
    aclEntry: A AclEntry resource to be passed as the request body.
  """

  acl = _messages.StringField(1, required=True)
  aclEntry = _messages.MessageField('AclEntry', 2)


class ManagedkafkaProjectsLocationsClustersAclsCreateRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersAclsCreateRequest object.

  Fields:
    acl: A Acl resource to be passed as the request body.
    aclId: Required. The ID to use for the acl, which will become the final
      component of the acl's name. The structure of `acl_id` defines the
      Resource Pattern (resource_type, resource_name, pattern_type) of the
      acl. `acl_id` is structured like one of the following: For acls on the
      cluster: `cluster` For acls on a single resource within the cluster:
      `topic/{resource_name}` `consumerGroup/{resource_name}`
      `transactionalId/{resource_name}` For acls on all resources that match a
      prefix: `topicPrefixed/{resource_name}`
      `consumerGroupPrefixed/{resource_name}`
      `transactionalIdPrefixed/{resource_name}` For acls on all resources of a
      given type (i.e. the wildcard literal "*"): `allTopics` (represents
      `topic/*`) `allConsumerGroups` (represents `consumerGroup/*`)
      `allTransactionalIds` (represents `transactionalId/*`)
    parent: Required. The parent cluster in which to create the acl.
      Structured like
      `projects/{project}/locations/{location}/clusters/{cluster}`.
  """

  acl = _messages.MessageField('Acl', 1)
  aclId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ManagedkafkaProjectsLocationsClustersAclsDeleteRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersAclsDeleteRequest object.

  Fields:
    name: Required. The name of the acl to delete. Structured like: `projects/
      {project}/locations/{location}/clusters/{cluster}/acls/{acl_id}`. The
      structure of `acl_id` defines the Resource Pattern (resource_type,
      resource_name, pattern_type) of the acl. See `Acl.name` for details.
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsClustersAclsGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersAclsGetRequest object.

  Fields:
    name: Required. The name of the acl to return. Structured like: `projects/
      {project}/locations/{location}/clusters/{cluster}/acls/{acl_id}`. The
      structure of `acl_id` defines the Resource Pattern (resource_type,
      resource_name, pattern_type) of the acl. See `Acl.name` for details.
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsClustersAclsListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersAclsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of acls to return. The service may
      return fewer than this value. If unset or zero, all acls for the parent
      is returned.
    pageToken: Optional. A page token, received from a previous `ListAcls`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListAcls` must match the call that
      provided the page token.
    parent: Required. The parent cluster whose acls are to be listed.
      Structured like
      `projects/{project}/locations/{location}/clusters/{cluster}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ManagedkafkaProjectsLocationsClustersAclsPatchRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersAclsPatchRequest object.

  Fields:
    acl: A Acl resource to be passed as the request body.
    name: Identifier. The name for the acl. Represents a single Resource
      Pattern. Structured like:
      projects/{project}/locations/{location}/clusters/{cluster}/acls/{acl_id}
      The structure of `acl_id` defines the Resource Pattern (resource_type,
      resource_name, pattern_type) of the acl. `acl_id` is structured like one
      of the following: For acls on the cluster: `cluster` For acls on a
      single resource within the cluster: `topic/{resource_name}`
      `consumerGroup/{resource_name}` `transactionalId/{resource_name}` For
      acls on all resources that match a prefix:
      `topicPrefixed/{resource_name}` `consumerGroupPrefixed/{resource_name}`
      `transactionalIdPrefixed/{resource_name}` For acls on all resources of a
      given type (i.e. the wildcard literal "*"): `allTopics` (represents
      `topic/*`) `allConsumerGroups` (represents `consumerGroup/*`)
      `allTransactionalIds` (represents `transactionalId/*`)
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Acl resource by the update. The fields specified in
      the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask.
  """

  acl = _messages.MessageField('Acl', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ManagedkafkaProjectsLocationsClustersAclsRemoveAclEntryRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersAclsRemoveAclEntryRequest object.

  Fields:
    acl: Required. The name of the acl to remove the acl entry from.
      Structured like: `projects/{project}/locations/{location}/clusters/{clus
      ter}/acls/{acl_id}`. The structure of `acl_id` defines the Resource
      Pattern (resource_type, resource_name, pattern_type) of the acl. See
      `Acl.name` for details.
    aclEntry: A AclEntry resource to be passed as the request body.
  """

  acl = _messages.StringField(1, required=True)
  aclEntry = _messages.MessageField('AclEntry', 2)


class ManagedkafkaProjectsLocationsClustersConsumerGroupsDeleteRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersConsumerGroupsDeleteRequest
  object.

  Fields:
    name: Required. The name of the consumer group to delete. `projects/{proje
      ct}/locations/{location}/clusters/{cluster}/consumerGroups/{consumerGrou
      p}`.
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsClustersConsumerGroupsGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersConsumerGroupsGetRequest object.

  Fields:
    name: Required. The name of the consumer group whose configuration to
      return. `projects/{project}/locations/{location}/clusters/{cluster}/cons
      umerGroups/{consumerGroup}`.
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsClustersConsumerGroupsListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersConsumerGroupsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of consumer groups to return. The
      service may return fewer than this value. If unset or zero, all consumer
      groups for the parent is returned.
    pageToken: Optional. A page token, received from a previous
      `ListConsumerGroups` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListConsumerGroups`
      must match the call that provided the page token.
    parent: Required. The parent cluster whose consumer groups are to be
      listed. Structured like
      `projects/{project}/locations/{location}/clusters/{cluster}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ManagedkafkaProjectsLocationsClustersConsumerGroupsPatchRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersConsumerGroupsPatchRequest
  object.

  Fields:
    consumerGroup: A ConsumerGroup resource to be passed as the request body.
    name: Identifier. The name of the consumer group. The `consumer_group`
      segment is used when connecting directly to the cluster. Structured
      like: projects/{project}/locations/{location}/clusters/{cluster}/consume
      rGroups/{consumer_group}
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the ConsumerGroup resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. The mask is
      required and a value of * will update all fields.
  """

  consumerGroup = _messages.MessageField('ConsumerGroup', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ManagedkafkaProjectsLocationsClustersCreateRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersCreateRequest object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    clusterId: Required. The ID to use for the cluster, which will become the
      final component of the cluster's name. The ID must be 1-63 characters
      long, and match the regular expression `[a-z]([-a-z0-9]*[a-z0-9])?` to
      comply with RFC 1035. This value is structured like: `my-cluster-id`.
    parent: Required. The parent region in which to create the cluster.
      Structured like `projects/{project}/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID to avoid duplication of requests. If a request times
      out or fails, retrying with the same ID allows the server to recognize
      the previous attempt. For at least 60 minutes, the server ignores
      duplicate requests bearing the same ID. For example, consider a
      situation where you make an initial request and the request times out.
      If you make the request again with the same request ID within 60 minutes
      of the last request, the server checks if an original operation with the
      same request ID was received. If so, the server ignores the second
      request. The request ID must be a valid UUID. A zero UUID is not
      supported (00000000-0000-0000-0000-000000000000).
  """

  cluster = _messages.MessageField('Cluster', 1)
  clusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class ManagedkafkaProjectsLocationsClustersDeleteRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersDeleteRequest object.

  Fields:
    name: Required. The name of the cluster to delete.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID to avoid duplication of requests. If a request times
      out or fails, retrying with the same ID allows the server to recognize
      the previous attempt. For at least 60 minutes, the server ignores
      duplicate requests bearing the same ID. For example, consider a
      situation where you make an initial request and the request times out.
      If you make the request again with the same request ID within 60 minutes
      of the last request, the server checks if an original operation with the
      same request ID was received. If so, the server ignores the second
      request. The request ID must be a valid UUID. A zero UUID is not
      supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class ManagedkafkaProjectsLocationsClustersGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersGetRequest object.

  Enums:
    ViewValueValuesEnum: Optional. Specifies which parts of the Cluster
      resource should be returned in the response.

  Fields:
    name: Required. The name of the cluster whose configuration to return.
    view: Optional. Specifies which parts of the Cluster resource should be
      returned in the response.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies which parts of the Cluster resource should be
    returned in the response.

    Values:
      CLUSTER_VIEW_UNSPECIFIED: The default / unset value. The API will
        default to the BASIC view.
      CLUSTER_VIEW_BASIC: Include the basic metadata of the Cluster. This is
        the default value (for both ListClusters and GetCluster).
      CLUSTER_VIEW_FULL: Include everything, including data fetched from the
        Kafka cluster source of truth.
    """
    CLUSTER_VIEW_UNSPECIFIED = 0
    CLUSTER_VIEW_BASIC = 1
    CLUSTER_VIEW_FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class ManagedkafkaProjectsLocationsClustersListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersListRequest object.

  Fields:
    filter: Optional. Filter expression for the result.
    orderBy: Optional. Order by fields for the result.
    pageSize: Optional. The maximum number of clusters to return. The service
      may return fewer than this value. If unspecified, server will pick an
      appropriate default.
    pageToken: Optional. A page token, received from a previous `ListClusters`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListClusters` must match the call that
      provided the page token.
    parent: Required. The parent location whose clusters are to be listed.
      Structured like `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ManagedkafkaProjectsLocationsClustersPatchRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersPatchRequest object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    name: Identifier. The name of the cluster. Structured like:
      projects/{project_number}/locations/{location}/clusters/{cluster_id}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID to avoid duplication of requests. If a request times
      out or fails, retrying with the same ID allows the server to recognize
      the previous attempt. For at least 60 minutes, the server ignores
      duplicate requests bearing the same ID. For example, consider a
      situation where you make an initial request and the request times out.
      If you make the request again with the same request ID within 60 minutes
      of the last request, the server checks if an original operation with the
      same request ID was received. If so, the server ignores the second
      request. The request ID must be a valid UUID. A zero UUID is not
      supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the cluster resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. The mask is required and
      a value of * will update all fields.
  """

  cluster = _messages.MessageField('Cluster', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class ManagedkafkaProjectsLocationsClustersTopicsCreateRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersTopicsCreateRequest object.

  Fields:
    parent: Required. The parent cluster in which to create the topic.
      Structured like
      `projects/{project}/locations/{location}/clusters/{cluster}`.
    topic: A Topic resource to be passed as the request body.
    topicId: Required. The ID to use for the topic, which will become the
      final component of the topic's name. This value is structured like: `my-
      topic-name`.
  """

  parent = _messages.StringField(1, required=True)
  topic = _messages.MessageField('Topic', 2)
  topicId = _messages.StringField(3)


class ManagedkafkaProjectsLocationsClustersTopicsDeleteRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersTopicsDeleteRequest object.

  Fields:
    name: Required. The name of the topic to delete. `projects/{project}/locat
      ions/{location}/clusters/{cluster}/topics/{topic}`.
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsClustersTopicsGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersTopicsGetRequest object.

  Fields:
    name: Required. The name of the topic whose configuration to return.
      Structured like: projects/{project}/locations/{location}/clusters/{clust
      er}/topics/{topic}.
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsClustersTopicsListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersTopicsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of topics to return. The service
      may return fewer than this value. If unset or zero, all topics for the
      parent is returned.
    pageToken: Optional. A page token, received from a previous `ListTopics`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListTopics` must match the call that
      provided the page token.
    parent: Required. The parent cluster whose topics are to be listed.
      Structured like
      `projects/{project}/locations/{location}/clusters/{cluster}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ManagedkafkaProjectsLocationsClustersTopicsPatchRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsClustersTopicsPatchRequest object.

  Fields:
    name: Identifier. The name of the topic. The `topic` segment is used when
      connecting directly to the cluster. Structured like: projects/{project}/
      locations/{location}/clusters/{cluster}/topics/{topic}
    topic: A Topic resource to be passed as the request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Topic resource by the update. The fields specified in
      the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. The mask is required and
      a value of * will update all fields.
  """

  name = _messages.StringField(1, required=True)
  topic = _messages.MessageField('Topic', 2)
  updateMask = _messages.StringField(3)


class ManagedkafkaProjectsLocationsConnectClustersConnectorsCreateRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsConnectClustersConnectorsCreateRequest
  object.

  Fields:
    connector: A Connector resource to be passed as the request body.
    connectorId: Required. The ID to use for the connector, which will become
      the final component of the connector's name. The ID must be 1-63
      characters long, and match the regular expression
      `[a-z]([-a-z0-9]*[a-z0-9])?` to comply with RFC 1035. This value is
      structured like: `my-connector-id`.
    parent: Required. The parent Connect cluster in which to create the
      connector. Structured like `projects/{project}/locations/{location}/conn
      ectClusters/{connect_cluster_id}`.
  """

  connector = _messages.MessageField('Connector', 1)
  connectorId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ManagedkafkaProjectsLocationsConnectClustersConnectorsDeleteRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsConnectClustersConnectorsDeleteRequest
  object.

  Fields:
    name: Required. The name of the connector to delete. Structured like: proj
      ects/{project}/locations/{location}/connectClusters/{connectCluster}/con
      nectors/{connector}
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsConnectClustersConnectorsGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsConnectClustersConnectorsGetRequest
  object.

  Fields:
    name: Required. The name of the connector whose configuration to return.
      Structured like: projects/{project}/locations/{location}/connectClusters
      /{connectCluster}/connectors/{connector}
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsConnectClustersConnectorsListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsConnectClustersConnectorsListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of connectors to return. The
      service may return fewer than this value. If unspecified, server will
      pick an appropriate default.
    pageToken: Optional. A page token, received from a previous
      `ListConnectors` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListConnectors` must
      match the call that provided the page token.
    parent: Required. The parent Connect cluster whose connectors are to be
      listed. Structured like `projects/{project}/locations/{location}/connect
      Clusters/{connect_cluster_id}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ManagedkafkaProjectsLocationsConnectClustersConnectorsPatchRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsConnectClustersConnectorsPatchRequest
  object.

  Fields:
    connector: A Connector resource to be passed as the request body.
    name: Identifier. The name of the connector. Structured like: projects/{pr
      oject}/locations/{location}/connectClusters/{connect_cluster}/connectors
      /{connector}
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the cluster resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. The mask is required and
      a value of * will update all fields.
  """

  connector = _messages.MessageField('Connector', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ManagedkafkaProjectsLocationsConnectClustersConnectorsPauseRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsConnectClustersConnectorsPauseRequest
  object.

  Fields:
    name: Required. The name of the connector to pause. Structured like: proje
      cts/{project}/locations/{location}/connectClusters/{connectCluster}/conn
      ectors/{connector}
    pauseConnectorRequest: A PauseConnectorRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  pauseConnectorRequest = _messages.MessageField('PauseConnectorRequest', 2)


class ManagedkafkaProjectsLocationsConnectClustersConnectorsRestartRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsConnectClustersConnectorsRestartRequest
  object.

  Fields:
    name: Required. The name of the connector to restart. Structured like: pro
      jects/{project}/locations/{location}/connectClusters/{connectCluster}/co
      nnectors/{connector}
    restartConnectorRequest: A RestartConnectorRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  restartConnectorRequest = _messages.MessageField('RestartConnectorRequest', 2)


class ManagedkafkaProjectsLocationsConnectClustersConnectorsResumeRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsConnectClustersConnectorsResumeRequest
  object.

  Fields:
    name: Required. The name of the connector to pause. Structured like: proje
      cts/{project}/locations/{location}/connectClusters/{connectCluster}/conn
      ectors/{connector}
    resumeConnectorRequest: A ResumeConnectorRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  resumeConnectorRequest = _messages.MessageField('ResumeConnectorRequest', 2)


class ManagedkafkaProjectsLocationsConnectClustersConnectorsStopRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsConnectClustersConnectorsStopRequest
  object.

  Fields:
    name: Required. The name of the connector to stop. Structured like: projec
      ts/{project}/locations/{location}/connectClusters/{connectCluster}/conne
      ctors/{connector}
    stopConnectorRequest: A StopConnectorRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  stopConnectorRequest = _messages.MessageField('StopConnectorRequest', 2)


class ManagedkafkaProjectsLocationsConnectClustersCreateRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsConnectClustersCreateRequest object.

  Fields:
    connectCluster: A ConnectCluster resource to be passed as the request
      body.
    connectClusterId: Required. The ID to use for the Connect cluster, which
      will become the final component of the cluster's name. The ID must be
      1-63 characters long, and match the regular expression
      `[a-z]([-a-z0-9]*[a-z0-9])?` to comply with RFC 1035. This value is
      structured like: `my-cluster-id`.
    parent: Required. The parent project/location in which to create the Kafka
      Connect cluster. Structured like
      `projects/{project}/locations/{location}/`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID to avoid duplication of requests. If a request times
      out or fails, retrying with the same ID allows the server to recognize
      the previous attempt. For at least 60 minutes, the server ignores
      duplicate requests bearing the same ID. For example, consider a
      situation where you make an initial request and the request times out.
      If you make the request again with the same request ID within 60 minutes
      of the last request, the server checks if an original operation with the
      same request ID was received. If so, the server ignores the second
      request. The request ID must be a valid UUID. A zero UUID is not
      supported (00000000-0000-0000-0000-000000000000).
  """

  connectCluster = _messages.MessageField('ConnectCluster', 1)
  connectClusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class ManagedkafkaProjectsLocationsConnectClustersDeleteRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsConnectClustersDeleteRequest object.

  Fields:
    name: Required. The name of the Kafka Connect cluster to delete.
      Structured like `projects/{project}/locations/{location}/connectClusters
      /{connect_cluster_id}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID to avoid duplication of requests. If a request times
      out or fails, retrying with the same ID allows the server to recognize
      the previous attempt. For at least 60 minutes, the server ignores
      duplicate requests bearing the same ID. For example, consider a
      situation where you make an initial request and the request times out.
      If you make the request again with the same request ID within 60 minutes
      of the last request, the server checks if an original operation with the
      same request ID was received. If so, the server ignores the second
      request. The request ID must be a valid UUID. A zero UUID is not
      supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class ManagedkafkaProjectsLocationsConnectClustersGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsConnectClustersGetRequest object.

  Fields:
    name: Required. The name of the Kafka Connect cluster whose configuration
      to return. Structured like `projects/{project}/locations/{location}/conn
      ectClusters/{connect_cluster_id}`.
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsConnectClustersListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsConnectClustersListRequest object.

  Fields:
    filter: Optional. Filter expression for the result.
    orderBy: Optional. Order by fields for the result.
    pageSize: Optional. The maximum number of Connect clusters to return. The
      service may return fewer than this value. If unspecified, server will
      pick an appropriate default.
    pageToken: Optional. A page token, received from a previous
      `ListConnectClusters` call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      `ListConnectClusters` must match the call that provided the page token.
    parent: Required. The parent project/location whose Connect clusters are
      to be listed. Structured like `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ManagedkafkaProjectsLocationsConnectClustersPatchRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsConnectClustersPatchRequest object.

  Fields:
    connectCluster: A ConnectCluster resource to be passed as the request
      body.
    name: Identifier. The name of the Kafka Connect cluster. Structured like:
      projects/{project_number}/locations/{location}/connectClusters/{connect_
      cluster_id}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID to avoid duplication of requests. If a request times
      out or fails, retrying with the same ID allows the server to recognize
      the previous attempt. For at least 60 minutes, the server ignores
      duplicate requests bearing the same ID. For example, consider a
      situation where you make an initial request and the request times out.
      If you make the request again with the same request ID within 60 minutes
      of the last request, the server checks if an original operation with the
      same request ID was received. If so, the server ignores the second
      request. The request ID must be a valid UUID. A zero UUID is not
      supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the cluster resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. The mask is required and
      a value of * will update all fields.
  """

  connectCluster = _messages.MessageField('ConnectCluster', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class ManagedkafkaProjectsLocationsGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class ManagedkafkaProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ManagedkafkaProjectsLocationsSchemaRegistriesCompatibilityCheckCompatibilityRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesCompatibilityCheckCompati
  bilityRequest object.

  Fields:
    checkCompatibilityRequest: A CheckCompatibilityRequest resource to be
      passed as the request body.
    name: Required. The name of the resource to check compatibility for. The
      format is either of following: * projects/{project}/locations/{location}
      /schemaRegistries/{schema_registry}/compatibility/subjects/*/versions:
      Check compatibility with one or more versions of the specified subject.
      * projects/{project}/locations/{location}/schemaRegistries/{schema_regis
      try}/compatibility/subjects/{subject}/versions/{version}: Check
      compatibility with a specific version of the subject.
  """

  checkCompatibilityRequest = _messages.MessageField('CheckCompatibilityRequest', 1)
  name = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesConfigDeleteRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesConfigDeleteRequest
  object.

  Fields:
    name: Required. The resource name of subject to delete the config for. The
      format is * projects/{project}/locations/{location}/schemaRegistries/{sc
      hema_registry}/config/{subject}
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesConfigGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesConfigGetRequest object.

  Fields:
    defaultToGlobal: Optional. If true, the config will fall back to the
      config at the global level if no subject level config is found.
    name: Required. The resource name to get the config for. It can be either
      of following: * projects/{project}/locations/{location}/schemaRegistries
      /{schema_registry}/config: Get config at global level. * projects/{proje
      ct}/locations/{location}/schemaRegistries/{schema_registry}/config/{subj
      ect}: Get config for a specific subject.
  """

  defaultToGlobal = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesConfigUpdateRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesConfigUpdateRequest
  object.

  Fields:
    name: Required. The resource name to update the config for. It can be
      either of following: * projects/{project}/locations/{location}/schemaReg
      istries/{schema_registry}/config: Update config at global level. * proje
      cts/{project}/locations/{location}/schemaRegistries/{schema_registry}/co
      nfig/{subject}: Update config for a specific subject.
    updateSchemaConfigRequest: A UpdateSchemaConfigRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateSchemaConfigRequest = _messages.MessageField('UpdateSchemaConfigRequest', 2)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsCompatibilityCheckCompatibilityRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsCompatibilityChec
  kCompatibilityRequest object.

  Fields:
    checkCompatibilityRequest: A CheckCompatibilityRequest resource to be
      passed as the request body.
    name: Required. The name of the resource to check compatibility for. The
      format is either of following: * projects/{project}/locations/{location}
      /schemaRegistries/{schema_registry}/compatibility/subjects/*/versions:
      Check compatibility with one or more versions of the specified subject.
      * projects/{project}/locations/{location}/schemaRegistries/{schema_regis
      try}/compatibility/subjects/{subject}/versions/{version}: Check
      compatibility with a specific version of the subject.
  """

  checkCompatibilityRequest = _messages.MessageField('CheckCompatibilityRequest', 1)
  name = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsConfigDeleteRequest(_messages.Message):
  r"""A
  ManagedkafkaProjectsLocationsSchemaRegistriesContextsConfigDeleteRequest
  object.

  Fields:
    name: Required. The resource name of subject to delete the config for. The
      format is * projects/{project}/locations/{location}/schemaRegistries/{sc
      hema_registry}/config/{subject}
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsConfigGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsConfigGetRequest
  object.

  Fields:
    defaultToGlobal: Optional. If true, the config will fall back to the
      config at the global level if no subject level config is found.
    name: Required. The resource name to get the config for. It can be either
      of following: * projects/{project}/locations/{location}/schemaRegistries
      /{schema_registry}/config: Get config at global level. * projects/{proje
      ct}/locations/{location}/schemaRegistries/{schema_registry}/config/{subj
      ect}: Get config for a specific subject.
  """

  defaultToGlobal = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsConfigUpdateRequest(_messages.Message):
  r"""A
  ManagedkafkaProjectsLocationsSchemaRegistriesContextsConfigUpdateRequest
  object.

  Fields:
    name: Required. The resource name to update the config for. It can be
      either of following: * projects/{project}/locations/{location}/schemaReg
      istries/{schema_registry}/config: Update config at global level. * proje
      cts/{project}/locations/{location}/schemaRegistries/{schema_registry}/co
      nfig/{subject}: Update config for a specific subject.
    updateSchemaConfigRequest: A UpdateSchemaConfigRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateSchemaConfigRequest = _messages.MessageField('UpdateSchemaConfigRequest', 2)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsGetRequest
  object.

  Fields:
    name: Required. The name of the context to return. Structured like: `proje
      cts/{project}/locations/{location}/schemaRegistries/{schema_registry}/co
      ntexts/{context}`
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsListRequest
  object.

  Fields:
    parent: Required. The parent of the contexts. Structured like: `projects/{
      project}/locations/{location}/schemaRegistries/{schema_registry}`
  """

  parent = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsModeDeleteRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsModeDeleteRequest
  object.

  Fields:
    name: Required. The resource name of subject to delete the mode for. The
      format is * projects/{project}/locations/{location}/schemaRegistries/{sc
      hema_registry}/mode/{subject} * projects/{project}/locations/{location}/
      schemaRegistries/{schema_registry}/contexts/{context}/mode/{subject}
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsModeGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsModeGetRequest
  object.

  Fields:
    name: Required. The resource name of the mode. The format is * projects/{p
      roject}/locations/{location}/schemaRegistries/{schema_registry}/mode/{su
      bject}: mode for a schema registry, or * projects/{project}/locations/{l
      ocation}/schemaRegistries/{schema_registry}/contexts/{context}/mode/{sub
      ject}: mode for a specific subject in a specific context
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsModeUpdateRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsModeUpdateRequest
  object.

  Fields:
    name: Required. The resource name of the mode. The format is * projects/{p
      roject}/locations/{location}/schemaRegistries/{schema_registry}/mode/{su
      bject}: mode for a schema registry, or * projects/{project}/locations/{l
      ocation}/schemaRegistries/{schema_registry}/contexts/{context}/mode/{sub
      ject}: mode for a specific subject in a specific context
    updateSchemaModeRequest: A UpdateSchemaModeRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateSchemaModeRequest = _messages.MessageField('UpdateSchemaModeRequest', 2)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasGetRequest
  object.

  Fields:
    name: Required. The name of the schema to return. Structured like: `projec
      ts/{project}/locations/{location}/schemaRegistries/{schema_registry}/sch
      emas/ids/{schema}`
    subject: Optional. Used to limit the search for the schema ID to a
      specific subject, otherwise the schema ID will be searched for in all
      subjects in the given specified context.
  """

  name = _messages.StringField(1, required=True)
  subject = _messages.StringField(2)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasGetSchemaRequest(_messages.Message):
  r"""A
  ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasGetSchemaRequest
  object.

  Fields:
    name: Required. The name of the schema to return. Structured like: `projec
      ts/{project}/locations/{location}/schemaRegistries/{schema_registry}/sch
      emas/ids/{schema}`
    subject: Optional. Used to limit the search for the schema ID to a
      specific subject, otherwise the schema ID will be searched for in all
      subjects in the given specified context.
  """

  name = _messages.StringField(1, required=True)
  subject = _messages.StringField(2)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasSubjectsListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasSubjectsLi
  stRequest object.

  Fields:
    deleted: Optional. If true, the response will include soft-deleted
      subjects. The default is false.
    parent: Required. The schema resource whose associated subjects are to be
      listed. Structured like: `projects/{project}/locations/{location}/schema
      Registries/{schema_registry}/schemas/ids/{schema}` or `projects/{project
      }/locations/{location}/schemaRegistries/{schema_registry}/contexts/{cont
      ext}/schemas/ids/{schema}`
    subject: Optional. The subject to filter the subjects by.
  """

  deleted = _messages.BooleanField(1)
  parent = _messages.StringField(2, required=True)
  subject = _messages.StringField(3)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasTypesListRequest(_messages.Message):
  r"""A
  ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasTypesListRequest
  object.

  Fields:
    parent: Required. The parent schema registry whose schema types are to be
      listed. Structured like: `projects/{project}/locations/{location}/schema
      Registries/{schema_registry}`
  """

  parent = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasVersionsListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsSchemasVersionsLi
  stRequest object.

  Fields:
    deleted: Optional. If true, the response will include soft-deleted
      versions of the schema, even if the subject is soft-deleted. The default
      is false.
    parent: Required. The schema whose schema versions are to be listed.
      Structured like: `projects/{project}/locations/{location}/schemaRegistri
      es/{schema_registry}/schemas/ids/{schema}` or `projects/{project}/locati
      ons/{location}/schemaRegistries/{schema_registry}/contexts/{context}/sch
      emas/ids/{schema}`
    subject: Optional. The subject to filter the subjects by.
  """

  deleted = _messages.BooleanField(1)
  parent = _messages.StringField(2, required=True)
  subject = _messages.StringField(3)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsDeleteRequest(_messages.Message):
  r"""A
  ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsDeleteRequest
  object.

  Fields:
    name: Required. The name of the subject to delete. Structured like: `proje
      cts/{project}/locations/{location}/schemaRegistries/{schema_registry}/su
      bjects/{subject}` or `projects/{project}/locations/{location}/schemaRegi
      stries/{schema_registry}/contexts/{context}/subjects/{subject}`
    permanent: Optional. If true, the subject and all associated metadata
      including the schema ID will be deleted permanently. Otherwise, only the
      subject is soft-deleted. The default is false. Soft-deleted subjects can
      still be searched in ListSubjects API call with deleted=true query
      parameter. A soft-delete of a subject must be performed before a hard-
      delete.
  """

  name = _messages.StringField(1, required=True)
  permanent = _messages.BooleanField(2)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsListRequest(_messages.Message):
  r"""A
  ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsListRequest
  object.

  Fields:
    deleted: Optional. If true, the response will include soft-deleted
      subjects. The default is false.
    parent: Required. The parent schema registry/context whose subjects are to
      be listed. Structured like: `projects/{project}/locations/{location}/sch
      emaRegistries/{schema_registry}` or `projects/{project}/locations/{locat
      ion}/schemaRegistries/{schema_registry}/contexts/{context}`
    subjectPrefix: Optional. The context to filter the subjects by, in the
      format of `:.{context}:`. If unset, all subjects in the registry are
      returned. Set to empty string or add as '?subjectPrefix=' at the end of
      this request to list subjects in the default context.
  """

  deleted = _messages.BooleanField(1)
  parent = _messages.StringField(2, required=True)
  subjectPrefix = _messages.StringField(3)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsLookupVersionRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsLookupVer
  sionRequest object.

  Fields:
    lookupVersionRequest: A LookupVersionRequest resource to be passed as the
      request body.
    parent: Required. The subject to lookup the schema in. Structured like: `p
      rojects/{project}/locations/{location}/schemaRegistries/{schema_registry
      }/subjects/{subject}` or `projects/{project}/locations/{location}/schema
      Registries/{schema_registry}/contexts/{context}/subjects/{subject}`
  """

  lookupVersionRequest = _messages.MessageField('LookupVersionRequest', 1)
  parent = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsCreateRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsC
  reateRequest object.

  Fields:
    createVersionRequest: A CreateVersionRequest resource to be passed as the
      request body.
    parent: Required. The subject to create the version for. Structured like:
      `projects/{project}/locations/{location}/schemaRegistries/{schema_regist
      ry}/subjects/{subject}` or `projects/{project}/locations/{location}/sche
      maRegistries/{schema_registry}/contexts/{context}/subjects/{subject}`
  """

  createVersionRequest = _messages.MessageField('CreateVersionRequest', 1)
  parent = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsDeleteRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsD
  eleteRequest object.

  Fields:
    name: Required. The name of the subject version to delete. Structured
      like: `projects/{project}/locations/{location}/schemaRegistries/{schema_
      registry}/subjects/{subject}/versions/{version}` or `projects/{project}/
      locations/{location}/schemaRegistries/{schema_registry}/contexts/{contex
      t}/subjects/{subject}/versions/{version}`
    permanent: Optional. If true, both the version and the referenced schema
      ID will be permanently deleted. The default is false. If false, the
      version will be deleted but the schema ID will be retained. Soft-deleted
      versions can still be searched in ListVersions API call with
      deleted=true query parameter. A soft-delete of a version must be
      performed before a hard-delete.
  """

  name = _messages.StringField(1, required=True)
  permanent = _messages.BooleanField(2)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsG
  etRequest object.

  Fields:
    deleted: Optional. If true, no matter if the subject/version is soft-
      deleted or not, it returns the version details. If false, it returns
      NOT_FOUND error if the subject/version is soft-deleted. The default is
      false.
    name: Required. The name of the subject to return versions. Structured
      like: `projects/{project}/locations/{location}/schemaRegistries/{schema_
      registry}/subjects/{subject}/versions/{version}` or `projects/{project}/
      locations/{location}/schemaRegistries/{schema_registry}/contexts/{contex
      t}/subjects/{subject}/versions/{version}`
  """

  deleted = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsGetSchemaRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsG
  etSchemaRequest object.

  Fields:
    deleted: Optional. If true, no matter if the subject/version is soft-
      deleted or not, it returns the version details. If false, it returns
      NOT_FOUND error if the subject/version is soft-deleted. The default is
      false.
    name: Required. The name of the subject to return versions. Structured
      like: `projects/{project}/locations/{location}/schemaRegistries/{schema_
      registry}/subjects/{subject}/versions/{version}` or `projects/{project}/
      locations/{location}/schemaRegistries/{schema_registry}/contexts/{contex
      t}/subjects/{subject}/versions/{version}`
  """

  deleted = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsL
  istRequest object.

  Fields:
    deleted: Optional. If true, the response will include soft-deleted
      versions of an active or soft-deleted subject. The default is false.
    parent: Required. The subject whose versions are to be listed. Structured
      like: `projects/{project}/locations/{location}/schemaRegistries/{schema_
      registry}/subjects/{subject}` or `projects/{project}/locations/{location
      }/schemaRegistries/{schema_registry}/contexts/{context}/subjects/{subjec
      t}`
  """

  deleted = _messages.BooleanField(1)
  parent = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsReferencedbyListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesContextsSubjectsVersionsR
  eferencedbyListRequest object.

  Fields:
    parent: Required. The version to list referenced by. Structured like: `pro
      jects/{project}/locations/{location}/schemaRegistries/{schema_registry}/
      subjects/{subject}/versions/{version}` or `projects/{project}/locations/
      {location}/schemaRegistries/{schema_registry}/contexts/{context}/subject
      s/{subject}/versions/{version}`
  """

  parent = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesCreateRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesCreateRequest object.

  Fields:
    createSchemaRegistryRequest: A CreateSchemaRegistryRequest resource to be
      passed as the request body.
    parent: Required. The parent whose schema registry instance is to be
      created. Structured like: `projects/{project}/locations/{location}`
  """

  createSchemaRegistryRequest = _messages.MessageField('CreateSchemaRegistryRequest', 1)
  parent = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesDeleteRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesDeleteRequest object.

  Fields:
    name: Required. The name of the schema registry instance to delete.
      Structured like: `projects/{project}/locations/{location}/schemaRegistri
      es/{schema_registry}`
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesGetRequest object.

  Fields:
    name: Required. The name of the schema registry instance to return.
      Structured like: `projects/{project}/locations/{location}/schemaRegistri
      es/{schema_registry}`
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesListRequest object.

  Fields:
    parent: Required. The parent whose schema registry instances are to be
      listed. Structured like: `projects/{project}/locations/{location}`
  """

  parent = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesModeDeleteRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesModeDeleteRequest object.

  Fields:
    name: Required. The resource name of subject to delete the mode for. The
      format is * projects/{project}/locations/{location}/schemaRegistries/{sc
      hema_registry}/mode/{subject} * projects/{project}/locations/{location}/
      schemaRegistries/{schema_registry}/contexts/{context}/mode/{subject}
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesModeGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesModeGetRequest object.

  Fields:
    name: Required. The resource name of the mode. The format is * projects/{p
      roject}/locations/{location}/schemaRegistries/{schema_registry}/mode/{su
      bject}: mode for a schema registry, or * projects/{project}/locations/{l
      ocation}/schemaRegistries/{schema_registry}/contexts/{context}/mode/{sub
      ject}: mode for a specific subject in a specific context
  """

  name = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesModeUpdateRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesModeUpdateRequest object.

  Fields:
    name: Required. The resource name of the mode. The format is * projects/{p
      roject}/locations/{location}/schemaRegistries/{schema_registry}/mode/{su
      bject}: mode for a schema registry, or * projects/{project}/locations/{l
      ocation}/schemaRegistries/{schema_registry}/contexts/{context}/mode/{sub
      ject}: mode for a specific subject in a specific context
    updateSchemaModeRequest: A UpdateSchemaModeRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateSchemaModeRequest = _messages.MessageField('UpdateSchemaModeRequest', 2)


class ManagedkafkaProjectsLocationsSchemaRegistriesSchemasGetRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesSchemasGetRequest object.

  Fields:
    name: Required. The name of the schema to return. Structured like: `projec
      ts/{project}/locations/{location}/schemaRegistries/{schema_registry}/sch
      emas/ids/{schema}`
    subject: Optional. Used to limit the search for the schema ID to a
      specific subject, otherwise the schema ID will be searched for in all
      subjects in the given specified context.
  """

  name = _messages.StringField(1, required=True)
  subject = _messages.StringField(2)


class ManagedkafkaProjectsLocationsSchemaRegistriesSchemasGetSchemaRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesSchemasGetSchemaRequest
  object.

  Fields:
    name: Required. The name of the schema to return. Structured like: `projec
      ts/{project}/locations/{location}/schemaRegistries/{schema_registry}/sch
      emas/ids/{schema}`
    subject: Optional. Used to limit the search for the schema ID to a
      specific subject, otherwise the schema ID will be searched for in all
      subjects in the given specified context.
  """

  name = _messages.StringField(1, required=True)
  subject = _messages.StringField(2)


class ManagedkafkaProjectsLocationsSchemaRegistriesSchemasSubjectsListRequest(_messages.Message):
  r"""A
  ManagedkafkaProjectsLocationsSchemaRegistriesSchemasSubjectsListRequest
  object.

  Fields:
    deleted: Optional. If true, the response will include soft-deleted
      subjects. The default is false.
    parent: Required. The schema resource whose associated subjects are to be
      listed. Structured like: `projects/{project}/locations/{location}/schema
      Registries/{schema_registry}/schemas/ids/{schema}` or `projects/{project
      }/locations/{location}/schemaRegistries/{schema_registry}/contexts/{cont
      ext}/schemas/ids/{schema}`
    subject: Optional. The subject to filter the subjects by.
  """

  deleted = _messages.BooleanField(1)
  parent = _messages.StringField(2, required=True)
  subject = _messages.StringField(3)


class ManagedkafkaProjectsLocationsSchemaRegistriesSchemasTypesListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesSchemasTypesListRequest
  object.

  Fields:
    parent: Required. The parent schema registry whose schema types are to be
      listed. Structured like: `projects/{project}/locations/{location}/schema
      Registries/{schema_registry}`
  """

  parent = _messages.StringField(1, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesSchemasVersionsListRequest(_messages.Message):
  r"""A
  ManagedkafkaProjectsLocationsSchemaRegistriesSchemasVersionsListRequest
  object.

  Fields:
    deleted: Optional. If true, the response will include soft-deleted
      versions of the schema, even if the subject is soft-deleted. The default
      is false.
    parent: Required. The schema whose schema versions are to be listed.
      Structured like: `projects/{project}/locations/{location}/schemaRegistri
      es/{schema_registry}/schemas/ids/{schema}` or `projects/{project}/locati
      ons/{location}/schemaRegistries/{schema_registry}/contexts/{context}/sch
      emas/ids/{schema}`
    subject: Optional. The subject to filter the subjects by.
  """

  deleted = _messages.BooleanField(1)
  parent = _messages.StringField(2, required=True)
  subject = _messages.StringField(3)


class ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsDeleteRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsDeleteRequest
  object.

  Fields:
    name: Required. The name of the subject to delete. Structured like: `proje
      cts/{project}/locations/{location}/schemaRegistries/{schema_registry}/su
      bjects/{subject}` or `projects/{project}/locations/{location}/schemaRegi
      stries/{schema_registry}/contexts/{context}/subjects/{subject}`
    permanent: Optional. If true, the subject and all associated metadata
      including the schema ID will be deleted permanently. Otherwise, only the
      subject is soft-deleted. The default is false. Soft-deleted subjects can
      still be searched in ListSubjects API call with deleted=true query
      parameter. A soft-delete of a subject must be performed before a hard-
      delete.
  """

  name = _messages.StringField(1, required=True)
  permanent = _messages.BooleanField(2)


class ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsListRequest
  object.

  Fields:
    deleted: Optional. If true, the response will include soft-deleted
      subjects. The default is false.
    parent: Required. The parent schema registry/context whose subjects are to
      be listed. Structured like: `projects/{project}/locations/{location}/sch
      emaRegistries/{schema_registry}` or `projects/{project}/locations/{locat
      ion}/schemaRegistries/{schema_registry}/contexts/{context}`
    subjectPrefix: Optional. The context to filter the subjects by, in the
      format of `:.{context}:`. If unset, all subjects in the registry are
      returned. Set to empty string or add as '?subjectPrefix=' at the end of
      this request to list subjects in the default context.
  """

  deleted = _messages.BooleanField(1)
  parent = _messages.StringField(2, required=True)
  subjectPrefix = _messages.StringField(3)


class ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsLookupVersionRequest(_messages.Message):
  r"""A
  ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsLookupVersionRequest
  object.

  Fields:
    lookupVersionRequest: A LookupVersionRequest resource to be passed as the
      request body.
    parent: Required. The subject to lookup the schema in. Structured like: `p
      rojects/{project}/locations/{location}/schemaRegistries/{schema_registry
      }/subjects/{subject}` or `projects/{project}/locations/{location}/schema
      Registries/{schema_registry}/contexts/{context}/subjects/{subject}`
  """

  lookupVersionRequest = _messages.MessageField('LookupVersionRequest', 1)
  parent = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsCreateRequest(_messages.Message):
  r"""A
  ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsCreateRequest
  object.

  Fields:
    createVersionRequest: A CreateVersionRequest resource to be passed as the
      request body.
    parent: Required. The subject to create the version for. Structured like:
      `projects/{project}/locations/{location}/schemaRegistries/{schema_regist
      ry}/subjects/{subject}` or `projects/{project}/locations/{location}/sche
      maRegistries/{schema_registry}/contexts/{context}/subjects/{subject}`
  """

  createVersionRequest = _messages.MessageField('CreateVersionRequest', 1)
  parent = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsDeleteRequest(_messages.Message):
  r"""A
  ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsDeleteRequest
  object.

  Fields:
    name: Required. The name of the subject version to delete. Structured
      like: `projects/{project}/locations/{location}/schemaRegistries/{schema_
      registry}/subjects/{subject}/versions/{version}` or `projects/{project}/
      locations/{location}/schemaRegistries/{schema_registry}/contexts/{contex
      t}/subjects/{subject}/versions/{version}`
    permanent: Optional. If true, both the version and the referenced schema
      ID will be permanently deleted. The default is false. If false, the
      version will be deleted but the schema ID will be retained. Soft-deleted
      versions can still be searched in ListVersions API call with
      deleted=true query parameter. A soft-delete of a version must be
      performed before a hard-delete.
  """

  name = _messages.StringField(1, required=True)
  permanent = _messages.BooleanField(2)


class ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsGetRequest(_messages.Message):
  r"""A
  ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsGetRequest
  object.

  Fields:
    deleted: Optional. If true, no matter if the subject/version is soft-
      deleted or not, it returns the version details. If false, it returns
      NOT_FOUND error if the subject/version is soft-deleted. The default is
      false.
    name: Required. The name of the subject to return versions. Structured
      like: `projects/{project}/locations/{location}/schemaRegistries/{schema_
      registry}/subjects/{subject}/versions/{version}` or `projects/{project}/
      locations/{location}/schemaRegistries/{schema_registry}/contexts/{contex
      t}/subjects/{subject}/versions/{version}`
  """

  deleted = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsGetSchemaRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsGetSchema
  Request object.

  Fields:
    deleted: Optional. If true, no matter if the subject/version is soft-
      deleted or not, it returns the version details. If false, it returns
      NOT_FOUND error if the subject/version is soft-deleted. The default is
      false.
    name: Required. The name of the subject to return versions. Structured
      like: `projects/{project}/locations/{location}/schemaRegistries/{schema_
      registry}/subjects/{subject}/versions/{version}` or `projects/{project}/
      locations/{location}/schemaRegistries/{schema_registry}/contexts/{contex
      t}/subjects/{subject}/versions/{version}`
  """

  deleted = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsListRequest(_messages.Message):
  r"""A
  ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsListRequest
  object.

  Fields:
    deleted: Optional. If true, the response will include soft-deleted
      versions of an active or soft-deleted subject. The default is false.
    parent: Required. The subject whose versions are to be listed. Structured
      like: `projects/{project}/locations/{location}/schemaRegistries/{schema_
      registry}/subjects/{subject}` or `projects/{project}/locations/{location
      }/schemaRegistries/{schema_registry}/contexts/{context}/subjects/{subjec
      t}`
  """

  deleted = _messages.BooleanField(1)
  parent = _messages.StringField(2, required=True)


class ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsReferencedbyListRequest(_messages.Message):
  r"""A ManagedkafkaProjectsLocationsSchemaRegistriesSubjectsVersionsReference
  dbyListRequest object.

  Fields:
    parent: Required. The version to list referenced by. Structured like: `pro
      jects/{project}/locations/{location}/schemaRegistries/{schema_registry}/
      subjects/{subject}/versions/{version}` or `projects/{project}/locations/
      {location}/schemaRegistries/{schema_registry}/contexts/{context}/subject
      s/{subject}/versions/{version}`
  """

  parent = _messages.StringField(1, required=True)


class NetworkConfig(_messages.Message):
  r"""The configuration of a Virtual Private Cloud (VPC) network that can
  access the Kafka cluster.

  Fields:
    subnet: Required. Name of the VPC subnet in which to create Private
      Service Connect (PSC) endpoints for the Kafka brokers and bootstrap
      address. Structured like:
      projects/{project}/regions/{region}/subnetworks/{subnet_id} The subnet
      must be located in the same region as the Kafka cluster. The project may
      differ. Multiple subnets from the same parent network must not be
      specified.
  """

  subnet = _messages.StringField(1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class PauseConnectorRequest(_messages.Message):
  r"""Request for PauseConnector."""


class PauseConnectorResponse(_messages.Message):
  r"""Response for PauseConnector."""


class RebalanceConfig(_messages.Message):
  r"""Defines rebalancing behavior of a Kafka cluster.

  Enums:
    ModeValueValuesEnum: Optional. The rebalance behavior for the cluster.
      When not specified, defaults to `NO_REBALANCE`.

  Fields:
    mode: Optional. The rebalance behavior for the cluster. When not
      specified, defaults to `NO_REBALANCE`.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Optional. The rebalance behavior for the cluster. When not specified,
    defaults to `NO_REBALANCE`.

    Values:
      MODE_UNSPECIFIED: A mode was not specified. Do not use.
      NO_REBALANCE: Do not rebalance automatically.
      AUTO_REBALANCE_ON_SCALE_UP: Automatically rebalance topic partitions
        among brokers when the cluster is scaled up.
    """
    MODE_UNSPECIFIED = 0
    NO_REBALANCE = 1
    AUTO_REBALANCE_ON_SCALE_UP = 2

  mode = _messages.EnumField('ModeValueValuesEnum', 1)


class RemoveAclEntryResponse(_messages.Message):
  r"""Response for RemoveAclEntry.

  Fields:
    acl: The updated acl. Returned if the removed acl entry was not the last
      entry in the acl.
    aclDeleted: Returned with value true if the removed acl entry was the last
      entry in the acl, resulting in acl deletion.
  """

  acl = _messages.MessageField('Acl', 1)
  aclDeleted = _messages.BooleanField(2)


class RestartConnectorRequest(_messages.Message):
  r"""Request for RestartConnector."""


class RestartConnectorResponse(_messages.Message):
  r"""Response for RestartConnector."""


class ResumeConnectorRequest(_messages.Message):
  r"""Request for ResumeConnector."""


class ResumeConnectorResponse(_messages.Message):
  r"""Response for ResumeConnector."""


class Schema(_messages.Message):
  r"""Schema for a Kafka message.

  Enums:
    SchemaTypeValueValuesEnum: Optional. The schema type of the schema.

  Fields:
    references: Optional. The schema references used by the schema.
    schema: The schema payload.
    schemaType: Optional. The schema type of the schema.
  """

  class SchemaTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The schema type of the schema.

    Values:
      SCHEMA_TYPE_UNSPECIFIED: No schema type. The default will be AVRO.
      AVRO: Avro schema type.
      JSON: JSON schema type.
      PROTOBUF: Protobuf schema type.
    """
    SCHEMA_TYPE_UNSPECIFIED = 0
    AVRO = 1
    JSON = 2
    PROTOBUF = 3

  references = _messages.MessageField('SchemaReference', 1, repeated=True)
  schema = _messages.StringField(2)
  schemaType = _messages.EnumField('SchemaTypeValueValuesEnum', 3)


class SchemaConfig(_messages.Message):
  r"""SchemaConfig represents configuration for a schema registry or a
  specific subject.

  Enums:
    CompatibilityValueValuesEnum: Required. The compatibility type of the
      schema. The default value is BACKWARD. If unset in a SchemaSubject-level
      SchemaConfig, defaults to the global value. If unset in a
      SchemaRegistry-level SchemaConfig, reverts to the default value.

  Fields:
    alias: Optional. The subject to which this subject is an alias of. Only
      applicable for subject config.
    compatibility: Required. The compatibility type of the schema. The default
      value is BACKWARD. If unset in a SchemaSubject-level SchemaConfig,
      defaults to the global value. If unset in a SchemaRegistry-level
      SchemaConfig, reverts to the default value.
    normalize: Optional. If true, the schema will be normalized before being
      stored or looked up. The default is false. If unset in a SchemaSubject-
      level SchemaConfig, the global value will be used. If unset in a
      SchemaRegistry-level SchemaConfig, reverts to the default value.
  """

  class CompatibilityValueValuesEnum(_messages.Enum):
    r"""Required. The compatibility type of the schema. The default value is
    BACKWARD. If unset in a SchemaSubject-level SchemaConfig, defaults to the
    global value. If unset in a SchemaRegistry-level SchemaConfig, reverts to
    the default value.

    Values:
      NONE: No compatibility check.
      BACKWARD: Backwards compatible with the most recent version.
      BACKWARD_TRANSITIVE: Backwards compatible with all previous versions.
      FORWARD: Forwards compatible with the most recent version.
      FORWARD_TRANSITIVE: Forwards compatible with all previous versions.
      FULL: Backwards and forwards compatible with the most recent version.
      FULL_TRANSITIVE: Backwards and forwards compatible with all previous
        versions.
    """
    NONE = 0
    BACKWARD = 1
    BACKWARD_TRANSITIVE = 2
    FORWARD = 3
    FORWARD_TRANSITIVE = 4
    FULL = 5
    FULL_TRANSITIVE = 6

  alias = _messages.StringField(1)
  compatibility = _messages.EnumField('CompatibilityValueValuesEnum', 2)
  normalize = _messages.BooleanField(3)


class SchemaMode(_messages.Message):
  r"""SchemaMode represents the mode of a schema registry or a specific
  subject. Four modes are supported: * NONE: deprecated. This was the default
  mode for a subject, but now the default is unset (which means use the global
  schema registry setting) * READONLY: The schema registry is in read-only
  mode. * READWRITE: The schema registry is in read-write mode, which allows
  limited write operations on the schema. * IMPORT: The schema registry is in
  import mode, which allows more editing operations on the schema for data
  importing purposes.

  Enums:
    ModeValueValuesEnum: Required. The mode type of a schema registry
      (READWRITE by default) or of a subject (unset by default, which means
      use the global schema registry setting).

  Fields:
    mode: Required. The mode type of a schema registry (READWRITE by default)
      or of a subject (unset by default, which means use the global schema
      registry setting).
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Required. The mode type of a schema registry (READWRITE by default) or
    of a subject (unset by default, which means use the global schema registry
    setting).

    Values:
      NONE: The default / unset value. The subject mode is NONE/unset by
        default, which means use the global schema registry mode. This should
        not be used for setting the mode.
      READONLY: READONLY mode.
      READWRITE: READWRITE mode.
      IMPORT: IMPORT mode.
    """
    NONE = 0
    READONLY = 1
    READWRITE = 2
    IMPORT = 3

  mode = _messages.EnumField('ModeValueValuesEnum', 1)


class SchemaReference(_messages.Message):
  r"""SchemaReference is a reference to a schema.

  Fields:
    name: Required. The name of the reference.
    subject: Required. The subject of the reference.
    version: Required. The version of the reference.
  """

  name = _messages.StringField(1)
  subject = _messages.StringField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class SchemaRegistry(_messages.Message):
  r"""SchemaRegistry is a schema registry instance.

  Fields:
    contexts: Output only. The contexts of the schema registry instance.
    name: Identifier. The name of the schema registry instance. Structured
      like: `projects/{project}/locations/{location}/schemaRegistries/{schema_
      registry}` The instance name {schema_registry} can contain the
      following: * Up to 255 characters. * Letters (uppercase or lowercase),
      numbers, and underscores.
  """

  contexts = _messages.StringField(1, repeated=True)
  name = _messages.StringField(2)


class SchemaVersion(_messages.Message):
  r"""Version of a schema.

  Enums:
    SchemaTypeValueValuesEnum: Optional. The schema type of the schema.

  Fields:
    id: Required. The schema ID.
    references: Optional. The schema references used by the schema.
    schema: Required. The schema payload.
    schemaType: Optional. The schema type of the schema.
    subject: Required. The subject of the version.
    version: Required. The version ID
  """

  class SchemaTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The schema type of the schema.

    Values:
      SCHEMA_TYPE_UNSPECIFIED: No schema type. The default will be AVRO.
      AVRO: Avro schema type.
      JSON: JSON schema type.
      PROTOBUF: Protobuf schema type.
    """
    SCHEMA_TYPE_UNSPECIFIED = 0
    AVRO = 1
    JSON = 2
    PROTOBUF = 3

  id = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  references = _messages.MessageField('SchemaReference', 2, repeated=True)
  schema = _messages.StringField(3)
  schemaType = _messages.EnumField('SchemaTypeValueValuesEnum', 4)
  subject = _messages.StringField(5)
  version = _messages.IntegerField(6, variant=_messages.Variant.INT32)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StopConnectorRequest(_messages.Message):
  r"""Request for StopConnector."""


class StopConnectorResponse(_messages.Message):
  r"""Response for StopConnector."""


class TaskRetryPolicy(_messages.Message):
  r"""Task Retry Policy is implemented on a best-effort basis. Retry delay
  will be exponential based on provided minimum and maximum backoffs.
  https://en.wikipedia.org/wiki/Exponential_backoff. Note that the delay
  between consecutive task restarts may not always precisely match the
  configured settings. This can happen when the ConnectCluster is in
  rebalancing state or if the ConnectCluster is unresponsive etc. The default
  values for minimum and maximum backoffs are 60 seconds and 30 minutes
  respectively.

  Fields:
    maximumBackoff: Optional. The maximum amount of time to wait before
      retrying a failed task. This sets an upper bound for the backoff delay.
    minimumBackoff: Optional. The minimum amount of time to wait before
      retrying a failed task. This sets a lower bound for the backoff delay.
  """

  maximumBackoff = _messages.StringField(1)
  minimumBackoff = _messages.StringField(2)


class TlsConfig(_messages.Message):
  r"""The TLS configuration for the Kafka cluster.

  Fields:
    sslPrincipalMappingRules: Optional. A list of rules for mapping from SSL
      principal names to short names. These are applied in order by Kafka.
      Refer to the Apache Kafka documentation for
      `ssl.principal.mapping.rules` for the precise formatting details and
      syntax. Example:
      "RULE:^CN=(.*?),OU=ServiceUsers.*$/$<EMAIL>/,DEFAULT" This is a
      static Kafka broker configuration. Setting or modifying this field will
      trigger a rolling restart of the Kafka brokers to apply the change. An
      empty string means no rules are applied (Kafka default).
    trustConfig: Optional. The configuration of the broker truststore. If
      specified, clients can use mTLS for authentication.
  """

  sslPrincipalMappingRules = _messages.StringField(1)
  trustConfig = _messages.MessageField('TrustConfig', 2)


class Topic(_messages.Message):
  r"""A Kafka topic in a given cluster.

  Messages:
    ConfigsValue: Optional. Configurations for the topic that are overridden
      from the cluster defaults. The key of the map is a Kafka topic property
      name, for example: `cleanup.policy`, `compression.type`.

  Fields:
    configs: Optional. Configurations for the topic that are overridden from
      the cluster defaults. The key of the map is a Kafka topic property name,
      for example: `cleanup.policy`, `compression.type`.
    name: Identifier. The name of the topic. The `topic` segment is used when
      connecting directly to the cluster. Structured like: projects/{project}/
      locations/{location}/clusters/{cluster}/topics/{topic}
    partitionCount: Required. The number of partitions this topic has. The
      partition count can only be increased, not decreased. Please note that
      if partitions are increased for a topic that has a key, the partitioning
      logic or the ordering of the messages will be affected.
    replicationFactor: Required. Immutable. The number of replicas of each
      partition. A replication factor of 3 is recommended for high
      availability.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConfigsValue(_messages.Message):
    r"""Optional. Configurations for the topic that are overridden from the
    cluster defaults. The key of the map is a Kafka topic property name, for
    example: `cleanup.policy`, `compression.type`.

    Messages:
      AdditionalProperty: An additional property for a ConfigsValue object.

    Fields:
      additionalProperties: Additional properties of type ConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  configs = _messages.MessageField('ConfigsValue', 1)
  name = _messages.StringField(2)
  partitionCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  replicationFactor = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class TrustConfig(_messages.Message):
  r"""Sources of CA certificates to install in the broker's truststore.

  Fields:
    casConfigs: Optional. Configuration for the Google Certificate Authority
      Service. Maximum 10.
  """

  casConfigs = _messages.MessageField('CertificateAuthorityServiceConfig', 1, repeated=True)


class UpdateOptions(_messages.Message):
  r"""UpdateOptions specifies options that influence how a cluster update is
  applied. These options control the behavior of the update process, rather
  than defining the desired end-state of a cluster.

  Fields:
    allowBrokerDownscaleOnClusterUpscale: Optional. If true, allows an update
      operation that increases the total vCPU and/or memory allocation of the
      cluster to significantly decrease the per-broker vCPU and/or memory
      allocation. This can result in reduced performance and availability. By
      default, the update operation will fail if an upscale request results in
      a vCPU or memory allocation for the brokers that is smaller than 90% of
      the current broker size.
  """

  allowBrokerDownscaleOnClusterUpscale = _messages.BooleanField(1)


class UpdateSchemaConfigRequest(_messages.Message):
  r"""Request for updating schema config. On a SchemaSubject-level
  SchemaConfig, an unset field will be removed from the SchemaConfig.

  Enums:
    CompatibilityValueValuesEnum: Required. The compatibility type of the
      schemas. Cannot be unset for a SchemaRegistry-level SchemaConfig. If
      unset on a SchemaSubject-level SchemaConfig, removes the compatibility
      field for the SchemaConfig.

  Fields:
    compatibility: Required. The compatibility type of the schemas. Cannot be
      unset for a SchemaRegistry-level SchemaConfig. If unset on a
      SchemaSubject-level SchemaConfig, removes the compatibility field for
      the SchemaConfig.
    normalize: Optional. If true, the schema will be normalized before being
      stored or looked up. The default is false. Cannot be unset for a
      SchemaRegistry-level SchemaConfig. If unset on a SchemaSubject-level
      SchemaConfig, removes the normalize field for the SchemaConfig.
  """

  class CompatibilityValueValuesEnum(_messages.Enum):
    r"""Required. The compatibility type of the schemas. Cannot be unset for a
    SchemaRegistry-level SchemaConfig. If unset on a SchemaSubject-level
    SchemaConfig, removes the compatibility field for the SchemaConfig.

    Values:
      NONE: No compatibility check.
      BACKWARD: Backwards compatible with the most recent version.
      BACKWARD_TRANSITIVE: Backwards compatible with all previous versions.
      FORWARD: Forwards compatible with the most recent version.
      FORWARD_TRANSITIVE: Forwards compatible with all previous versions.
      FULL: Backwards and forwards compatible with the most recent version.
      FULL_TRANSITIVE: Backwards and forwards compatible with all previous
        versions.
    """
    NONE = 0
    BACKWARD = 1
    BACKWARD_TRANSITIVE = 2
    FORWARD = 3
    FORWARD_TRANSITIVE = 4
    FULL = 5
    FULL_TRANSITIVE = 6

  compatibility = _messages.EnumField('CompatibilityValueValuesEnum', 1)
  normalize = _messages.BooleanField(2)


class UpdateSchemaModeRequest(_messages.Message):
  r"""Request for updating schema registry or subject mode.

  Enums:
    ModeValueValuesEnum: Required. The mode type.

  Fields:
    mode: Required. The mode type.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Required. The mode type.

    Values:
      NONE: The default / unset value. The subject mode is NONE/unset by
        default, which means use the global schema registry mode. This should
        not be used for setting the mode.
      READONLY: READONLY mode.
      READWRITE: READWRITE mode.
      IMPORT: IMPORT mode.
    """
    NONE = 0
    READONLY = 1
    READWRITE = 2
    IMPORT = 3

  mode = _messages.EnumField('ModeValueValuesEnum', 1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
