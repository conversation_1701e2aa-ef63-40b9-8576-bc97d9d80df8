"""Generated client library for edgenetwork version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.edgenetwork.v1alpha1 import edgenetwork_v1alpha1_messages as messages


class EdgenetworkV1alpha1(base_api.BaseApiClient):
  """Generated client library for service edgenetwork version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://edgenetwork.googleapis.com/'
  MTLS_BASE_URL = 'https://edgenetwork.mtls.googleapis.com/'

  _PACKAGE = 'edgenetwork'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'EdgenetworkV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new edgenetwork handle."""
    url = url or self.BASE_URL
    super(EdgenetworkV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_zones_interconnectAttachments = self.ProjectsLocationsZonesInterconnectAttachmentsService(self)
    self.projects_locations_zones_interconnects = self.ProjectsLocationsZonesInterconnectsService(self)
    self.projects_locations_zones_networks = self.ProjectsLocationsZonesNetworksService(self)
    self.projects_locations_zones_routers = self.ProjectsLocationsZonesRoutersService(self)
    self.projects_locations_zones_routes = self.ProjectsLocationsZonesRoutesService(self)
    self.projects_locations_zones_subnets = self.ProjectsLocationsZonesSubnetsService(self)
    self.projects_locations_zones = self.ProjectsLocationsZonesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(EdgenetworkV1alpha1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (EdgenetworkProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='edgenetwork.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='EdgenetworkProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (EdgenetworkProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='edgenetwork.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (EdgenetworkProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='edgenetwork.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (EdgenetworkProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='edgenetwork.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsZonesInterconnectAttachmentsService(base_api.BaseApiService):
    """Service class for the projects_locations_zones_interconnectAttachments resource."""

    _NAME = 'projects_locations_zones_interconnectAttachments'

    def __init__(self, client):
      super(EdgenetworkV1alpha1.ProjectsLocationsZonesInterconnectAttachmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new InterconnectAttachment in a given project and location.

      Args:
        request: (EdgenetworkProjectsLocationsZonesInterconnectAttachmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/interconnectAttachments',
        http_method='POST',
        method_id='edgenetwork.projects.locations.zones.interconnectAttachments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['interconnectAttachmentId', 'requestId'],
        relative_path='v1alpha1/{+parent}/interconnectAttachments',
        request_field='interconnectAttachment',
        request_type_name='EdgenetworkProjectsLocationsZonesInterconnectAttachmentsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single InterconnectAttachment.

      Args:
        request: (EdgenetworkProjectsLocationsZonesInterconnectAttachmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/interconnectAttachments/{interconnectAttachmentsId}',
        http_method='DELETE',
        method_id='edgenetwork.projects.locations.zones.interconnectAttachments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesInterconnectAttachmentsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single InterconnectAttachment.

      Args:
        request: (EdgenetworkProjectsLocationsZonesInterconnectAttachmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (InterconnectAttachment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/interconnectAttachments/{interconnectAttachmentsId}',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.interconnectAttachments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesInterconnectAttachmentsGetRequest',
        response_type_name='InterconnectAttachment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists InterconnectAttachments in a given project and location.

      Args:
        request: (EdgenetworkProjectsLocationsZonesInterconnectAttachmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInterconnectAttachmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/interconnectAttachments',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.interconnectAttachments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/interconnectAttachments',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesInterconnectAttachmentsListRequest',
        response_type_name='ListInterconnectAttachmentsResponse',
        supports_download=False,
    )

  class ProjectsLocationsZonesInterconnectsService(base_api.BaseApiService):
    """Service class for the projects_locations_zones_interconnects resource."""

    _NAME = 'projects_locations_zones_interconnects'

    def __init__(self, client):
      super(EdgenetworkV1alpha1.ProjectsLocationsZonesInterconnectsService, self).__init__(client)
      self._upload_configs = {
          }

    def Diagnose(self, request, global_params=None):
      r"""Get the diagnostics of a single interconnect resource.

      Args:
        request: (EdgenetworkProjectsLocationsZonesInterconnectsDiagnoseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DiagnoseInterconnectResponse) The response message.
      """
      config = self.GetMethodConfig('Diagnose')
      return self._RunMethod(
          config, request, global_params=global_params)

    Diagnose.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/interconnects/{interconnectsId}:diagnose',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.interconnects.diagnose',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:diagnose',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesInterconnectsDiagnoseRequest',
        response_type_name='DiagnoseInterconnectResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Interconnect.

      Args:
        request: (EdgenetworkProjectsLocationsZonesInterconnectsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Interconnect) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/interconnects/{interconnectsId}',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.interconnects.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesInterconnectsGetRequest',
        response_type_name='Interconnect',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Interconnects in a given project and location.

      Args:
        request: (EdgenetworkProjectsLocationsZonesInterconnectsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInterconnectsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/interconnects',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.interconnects.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/interconnects',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesInterconnectsListRequest',
        response_type_name='ListInterconnectsResponse',
        supports_download=False,
    )

  class ProjectsLocationsZonesNetworksService(base_api.BaseApiService):
    """Service class for the projects_locations_zones_networks resource."""

    _NAME = 'projects_locations_zones_networks'

    def __init__(self, client):
      super(EdgenetworkV1alpha1.ProjectsLocationsZonesNetworksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Network in a given project and location.

      Args:
        request: (EdgenetworkProjectsLocationsZonesNetworksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/networks',
        http_method='POST',
        method_id='edgenetwork.projects.locations.zones.networks.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['networkId', 'requestId'],
        relative_path='v1alpha1/{+parent}/networks',
        request_field='network',
        request_type_name='EdgenetworkProjectsLocationsZonesNetworksCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Network.

      Args:
        request: (EdgenetworkProjectsLocationsZonesNetworksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/networks/{networksId}',
        http_method='DELETE',
        method_id='edgenetwork.projects.locations.zones.networks.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesNetworksDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Diagnose(self, request, global_params=None):
      r"""Get the diagnostics of a single network resource.

      Args:
        request: (EdgenetworkProjectsLocationsZonesNetworksDiagnoseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DiagnoseNetworkResponse) The response message.
      """
      config = self.GetMethodConfig('Diagnose')
      return self._RunMethod(
          config, request, global_params=global_params)

    Diagnose.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/networks/{networksId}:diagnose',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.networks.diagnose',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:diagnose',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesNetworksDiagnoseRequest',
        response_type_name='DiagnoseNetworkResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Network.

      Args:
        request: (EdgenetworkProjectsLocationsZonesNetworksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Network) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/networks/{networksId}',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.networks.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesNetworksGetRequest',
        response_type_name='Network',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Networks in a given project and location.

      Args:
        request: (EdgenetworkProjectsLocationsZonesNetworksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNetworksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/networks',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.networks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/networks',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesNetworksListRequest',
        response_type_name='ListNetworksResponse',
        supports_download=False,
    )

  class ProjectsLocationsZonesRoutersService(base_api.BaseApiService):
    """Service class for the projects_locations_zones_routers resource."""

    _NAME = 'projects_locations_zones_routers'

    def __init__(self, client):
      super(EdgenetworkV1alpha1.ProjectsLocationsZonesRoutersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Router in a given project and location.

      Args:
        request: (EdgenetworkProjectsLocationsZonesRoutersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/routers',
        http_method='POST',
        method_id='edgenetwork.projects.locations.zones.routers.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'routerId'],
        relative_path='v1alpha1/{+parent}/routers',
        request_field='router',
        request_type_name='EdgenetworkProjectsLocationsZonesRoutersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Router.

      Args:
        request: (EdgenetworkProjectsLocationsZonesRoutersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/routers/{routersId}',
        http_method='DELETE',
        method_id='edgenetwork.projects.locations.zones.routers.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesRoutersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Diagnose(self, request, global_params=None):
      r"""Get the diagnostics of a single router resource.

      Args:
        request: (EdgenetworkProjectsLocationsZonesRoutersDiagnoseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DiagnoseRouterResponse) The response message.
      """
      config = self.GetMethodConfig('Diagnose')
      return self._RunMethod(
          config, request, global_params=global_params)

    Diagnose.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/routers/{routersId}:diagnose',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.routers.diagnose',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:diagnose',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesRoutersDiagnoseRequest',
        response_type_name='DiagnoseRouterResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Router.

      Args:
        request: (EdgenetworkProjectsLocationsZonesRoutersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Router) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/routers/{routersId}',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.routers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesRoutersGetRequest',
        response_type_name='Router',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Routers in a given project and location.

      Args:
        request: (EdgenetworkProjectsLocationsZonesRoutersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRoutersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/routers',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.routers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/routers',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesRoutersListRequest',
        response_type_name='ListRoutersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Router.

      Args:
        request: (EdgenetworkProjectsLocationsZonesRoutersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/routers/{routersId}',
        http_method='PATCH',
        method_id='edgenetwork.projects.locations.zones.routers.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='router',
        request_type_name='EdgenetworkProjectsLocationsZonesRoutersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsZonesRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_zones_routes resource."""

    _NAME = 'projects_locations_zones_routes'

    def __init__(self, client):
      super(EdgenetworkV1alpha1.ProjectsLocationsZonesRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Route, in a given project and location.

      Args:
        request: (EdgenetworkProjectsLocationsZonesRoutesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/routes',
        http_method='POST',
        method_id='edgenetwork.projects.locations.zones.routes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'routeId'],
        relative_path='v1alpha1/{+parent}/routes',
        request_field='route',
        request_type_name='EdgenetworkProjectsLocationsZonesRoutesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Route.

      Args:
        request: (EdgenetworkProjectsLocationsZonesRoutesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/routes/{routesId}',
        http_method='DELETE',
        method_id='edgenetwork.projects.locations.zones.routes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesRoutesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Route.

      Args:
        request: (EdgenetworkProjectsLocationsZonesRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Route) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/routes/{routesId}',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.routes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesRoutesGetRequest',
        response_type_name='Route',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Routes in a given project and location.

      Args:
        request: (EdgenetworkProjectsLocationsZonesRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/routes',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.routes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/routes',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesRoutesListRequest',
        response_type_name='ListRoutesResponse',
        supports_download=False,
    )

  class ProjectsLocationsZonesSubnetsService(base_api.BaseApiService):
    """Service class for the projects_locations_zones_subnets resource."""

    _NAME = 'projects_locations_zones_subnets'

    def __init__(self, client):
      super(EdgenetworkV1alpha1.ProjectsLocationsZonesSubnetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Subnet in a given project and location.

      Args:
        request: (EdgenetworkProjectsLocationsZonesSubnetsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/subnets',
        http_method='POST',
        method_id='edgenetwork.projects.locations.zones.subnets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'subnetId'],
        relative_path='v1alpha1/{+parent}/subnets',
        request_field='subnet',
        request_type_name='EdgenetworkProjectsLocationsZonesSubnetsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Subnet.

      Args:
        request: (EdgenetworkProjectsLocationsZonesSubnetsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/subnets/{subnetsId}',
        http_method='DELETE',
        method_id='edgenetwork.projects.locations.zones.subnets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesSubnetsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Subnet.

      Args:
        request: (EdgenetworkProjectsLocationsZonesSubnetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Subnet) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/subnets/{subnetsId}',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.subnets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesSubnetsGetRequest',
        response_type_name='Subnet',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Subnets in a given project and location.

      Args:
        request: (EdgenetworkProjectsLocationsZonesSubnetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSubnetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/subnets',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.subnets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/subnets',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesSubnetsListRequest',
        response_type_name='ListSubnetsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Subnet.

      Args:
        request: (EdgenetworkProjectsLocationsZonesSubnetsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}/subnets/{subnetsId}',
        http_method='PATCH',
        method_id='edgenetwork.projects.locations.zones.subnets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='subnet',
        request_type_name='EdgenetworkProjectsLocationsZonesSubnetsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsZonesService(base_api.BaseApiService):
    """Service class for the projects_locations_zones resource."""

    _NAME = 'projects_locations_zones'

    def __init__(self, client):
      super(EdgenetworkV1alpha1.ProjectsLocationsZonesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Deprecated: not implemented. Gets details of a single Zone.

      Args:
        request: (EdgenetworkProjectsLocationsZonesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Zone) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesGetRequest',
        response_type_name='Zone',
        supports_download=False,
    )

    def Initialize(self, request, global_params=None):
      r"""InitializeZone will initialize resources for a zone in a project.

      Args:
        request: (EdgenetworkProjectsLocationsZonesInitializeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (InitializeZoneResponse) The response message.
      """
      config = self.GetMethodConfig('Initialize')
      return self._RunMethod(
          config, request, global_params=global_params)

    Initialize.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones/{zonesId}:initialize',
        http_method='POST',
        method_id='edgenetwork.projects.locations.zones.initialize',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:initialize',
        request_field='initializeZoneRequest',
        request_type_name='EdgenetworkProjectsLocationsZonesInitializeRequest',
        response_type_name='InitializeZoneResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Deprecated: not implemented. Lists Zones in a given project and location.

      Args:
        request: (EdgenetworkProjectsLocationsZonesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListZonesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/zones',
        http_method='GET',
        method_id='edgenetwork.projects.locations.zones.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/zones',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsZonesListRequest',
        response_type_name='ListZonesResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(EdgenetworkV1alpha1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (EdgenetworkProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='edgenetwork.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (EdgenetworkProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='edgenetwork.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/locations',
        request_field='',
        request_type_name='EdgenetworkProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(EdgenetworkV1alpha1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
