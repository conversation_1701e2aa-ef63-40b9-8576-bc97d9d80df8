"""Generated message classes for edgenetwork version v1.

Network management API for Distributed Cloud Edge.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'edgenetwork'


class Bgp(_messages.Message):
  r"""BGP information specific to this router.

  Fields:
    asn: Locally assigned BGP ASN.
    keepaliveIntervalInSeconds: The interval in seconds between BGP keepalive
      messages that are sent to the peer. Default is 20 with value between 20
      and 60.
  """

  asn = _messages.IntegerField(1, variant=_messages.Variant.UINT32)
  keepaliveIntervalInSeconds = _messages.IntegerField(2, variant=_messages.Variant.UINT32)


class BgpPeer(_messages.Message):
  r"""BGPPeer defines the peer side layer-3 information for building the BGP
  session.

  Fields:
    interface: Name of the RouterInterface the BGP peer is associated with.
    interfaceIpv4Cidr: IP range of the interface within Google.
    interfaceIpv6Cidr: IPv6 range of the interface within Google.
    localAsn: Output only. Local BGP Autonomous System Number (ASN). This
      field is ST_NOT_REQUIRED because it stores private ASNs, which are
      meaningless outside the zone in which they are being used.
    name: Name of this BGP peer. Unique within the Zones resource.
    peerAsn: Peer BGP Autonomous System Number (ASN). Each BGP interface may
      use a different value.
    peerIpv4Cidr: IP range of the BGP interface outside Google.
    peerIpv6Cidr: IPv6 range of the BGP interface outside Google.
  """

  interface = _messages.StringField(1)
  interfaceIpv4Cidr = _messages.StringField(2)
  interfaceIpv6Cidr = _messages.StringField(3)
  localAsn = _messages.IntegerField(4, variant=_messages.Variant.UINT32)
  name = _messages.StringField(5)
  peerAsn = _messages.IntegerField(6, variant=_messages.Variant.UINT32)
  peerIpv4Cidr = _messages.StringField(7)
  peerIpv6Cidr = _messages.StringField(8)


class BgpPeerStatus(_messages.Message):
  r"""Status of a BGP peer.

  Enums:
    StatusValueValuesEnum: The current status of BGP.

  Fields:
    ipAddress: IP address of the local BGP interface.
    name: Name of this BGP peer. Unique within the Routers resource.
    peerIpAddress: IP address of the remote BGP interface.
    prefixCounter: A collection of counts for prefixes.
    state: BGP state as specified in RFC1771.
    status: The current status of BGP.
    uptime: Time this session has been up. Format: 14 years, 51 weeks, 6 days,
      23 hours, 59 minutes, 59 seconds
    uptimeSeconds: Time this session has been up, in seconds.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""The current status of BGP.

    Values:
      UNKNOWN: The default status indicating BGP session is in unknown state.
      UP: The UP status indicating BGP session is established.
      DOWN: The DOWN state indicating BGP session is not established yet.
    """
    UNKNOWN = 0
    UP = 1
    DOWN = 2

  ipAddress = _messages.StringField(1)
  name = _messages.StringField(2)
  peerIpAddress = _messages.StringField(3)
  prefixCounter = _messages.MessageField('PrefixCounter', 4)
  state = _messages.StringField(5)
  status = _messages.EnumField('StatusValueValuesEnum', 6)
  uptime = _messages.StringField(7)
  uptimeSeconds = _messages.IntegerField(8)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class DiagnoseInterconnectResponse(_messages.Message):
  r"""DiagnoseInterconnectResponse contains the current diagnostics for a
  specific interconnect.

  Fields:
    result: The network status of a specific interconnect.
    updateTime: The time when the interconnect diagnostics was last updated.
  """

  result = _messages.MessageField('InterconnectDiagnostics', 1)
  updateTime = _messages.StringField(2)


class DiagnoseNetworkResponse(_messages.Message):
  r"""DiagnoseNetworkResponse contains the current status for a specific
  network.

  Fields:
    result: The network status of a specific network.
    updateTime: The time when the network status was last updated.
  """

  result = _messages.MessageField('NetworkStatus', 1)
  updateTime = _messages.StringField(2)


class DiagnoseRouterResponse(_messages.Message):
  r"""DiagnoseRouterResponse contains the current status for a specific
  router.

  Fields:
    result: The network status of a specific router.
    updateTime: The time when the router status was last updated.
  """

  result = _messages.MessageField('RouterStatus', 1)
  updateTime = _messages.StringField(2)


class EdgenetworkProjectsLocationsGetRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class EdgenetworkProjectsLocationsListRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class EdgenetworkProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class EdgenetworkProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class EdgenetworkProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class EdgenetworkProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class EdgenetworkProjectsLocationsZonesGetRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class EdgenetworkProjectsLocationsZonesInitializeRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesInitializeRequest object.

  Fields:
    initializeZoneRequest: A InitializeZoneRequest resource to be passed as
      the request body.
    name: Required. The name of the zone resource.
  """

  initializeZoneRequest = _messages.MessageField('InitializeZoneRequest', 1)
  name = _messages.StringField(2, required=True)


class EdgenetworkProjectsLocationsZonesInterconnectAttachmentsCreateRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesInterconnectAttachmentsCreateRequest
  object.

  Fields:
    interconnectAttachment: A InterconnectAttachment resource to be passed as
      the request body.
    interconnectAttachmentId: Required. Id of the requesting object If auto-
      generating Id server-side, remove this field and
      interconnect_attachment_id from the method_signature of Create RPC
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  interconnectAttachment = _messages.MessageField('InterconnectAttachment', 1)
  interconnectAttachmentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class EdgenetworkProjectsLocationsZonesInterconnectAttachmentsDeleteRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesInterconnectAttachmentsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class EdgenetworkProjectsLocationsZonesInterconnectAttachmentsGetRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesInterconnectAttachmentsGetRequest
  object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class EdgenetworkProjectsLocationsZonesInterconnectAttachmentsListRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesInterconnectAttachmentsListRequest
  object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListInterconnectAttachmentsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EdgenetworkProjectsLocationsZonesInterconnectsDiagnoseRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesInterconnectsDiagnoseRequest object.

  Fields:
    name: Required. The name of the interconnect resource.
  """

  name = _messages.StringField(1, required=True)


class EdgenetworkProjectsLocationsZonesInterconnectsGetRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesInterconnectsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class EdgenetworkProjectsLocationsZonesInterconnectsListRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesInterconnectsListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListInterconnectsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EdgenetworkProjectsLocationsZonesListRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListZonesRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EdgenetworkProjectsLocationsZonesNetworksCreateRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesNetworksCreateRequest object.

  Fields:
    network: A Network resource to be passed as the request body.
    networkId: Required. Id of the requesting object If auto-generating Id
      server-side, remove this field and network_id from the method_signature
      of Create RPC
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  network = _messages.MessageField('Network', 1)
  networkId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class EdgenetworkProjectsLocationsZonesNetworksDeleteRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesNetworksDeleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class EdgenetworkProjectsLocationsZonesNetworksDiagnoseRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesNetworksDiagnoseRequest object.

  Fields:
    name: Required. The name of the network resource.
  """

  name = _messages.StringField(1, required=True)


class EdgenetworkProjectsLocationsZonesNetworksGetRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesNetworksGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class EdgenetworkProjectsLocationsZonesNetworksListRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesNetworksListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListNetworksRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EdgenetworkProjectsLocationsZonesRoutersCreateRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesRoutersCreateRequest object.

  Fields:
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    router: A Router resource to be passed as the request body.
    routerId: Required. Id of the requesting object If auto-generating Id
      server-side, remove this field and router_id from the method_signature
      of Create RPC
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  router = _messages.MessageField('Router', 3)
  routerId = _messages.StringField(4)


class EdgenetworkProjectsLocationsZonesRoutersDeleteRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesRoutersDeleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class EdgenetworkProjectsLocationsZonesRoutersDiagnoseRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesRoutersDiagnoseRequest object.

  Fields:
    name: Required. The name of the router resource.
  """

  name = _messages.StringField(1, required=True)


class EdgenetworkProjectsLocationsZonesRoutersGetRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesRoutersGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class EdgenetworkProjectsLocationsZonesRoutersListRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesRoutersListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListRoutersRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EdgenetworkProjectsLocationsZonesRoutersPatchRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesRoutersPatchRequest object.

  Fields:
    name: Required. The canonical resource name of the router.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    router: A Router resource to be passed as the request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Router resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  router = _messages.MessageField('Router', 3)
  updateMask = _messages.StringField(4)


class EdgenetworkProjectsLocationsZonesSubnetsCreateRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesSubnetsCreateRequest object.

  Fields:
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    subnet: A Subnet resource to be passed as the request body.
    subnetId: Required. Id of the requesting object If auto-generating Id
      server-side, remove this field and subnet_id from the method_signature
      of Create RPC
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  subnet = _messages.MessageField('Subnet', 3)
  subnetId = _messages.StringField(4)


class EdgenetworkProjectsLocationsZonesSubnetsDeleteRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesSubnetsDeleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class EdgenetworkProjectsLocationsZonesSubnetsGetRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesSubnetsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class EdgenetworkProjectsLocationsZonesSubnetsListRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesSubnetsListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListSubnetsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EdgenetworkProjectsLocationsZonesSubnetsPatchRequest(_messages.Message):
  r"""A EdgenetworkProjectsLocationsZonesSubnetsPatchRequest object.

  Fields:
    name: Required. The canonical resource name of the subnet.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    subnet: A Subnet resource to be passed as the request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Subnet resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  subnet = _messages.MessageField('Subnet', 3)
  updateMask = _messages.StringField(4)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class InitializeZoneRequest(_messages.Message):
  r"""Message for initializing a specified zone"""


class InitializeZoneResponse(_messages.Message):
  r"""The response of initializing a zone"""


class Interconnect(_messages.Message):
  r"""Message describing Interconnect object

  Enums:
    InterconnectTypeValueValuesEnum: Optional. Type of interconnect, which
      takes only the value 'DEDICATED' for now.
    RemotePeeringNetworkTypeValueValuesEnum: Optional. The remote peering
      network type of the interconnect. It is required when peering separation
      is enabled.

  Messages:
    LabelsValue: Labels associated with this resource.

  Fields:
    createTime: Output only. The time when the subnet was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    deviceCloudResourceName: Output only. Cloud resource name of the switch
      device.
    interconnectType: Optional. Type of interconnect, which takes only the
      value 'DEDICATED' for now.
    labels: Labels associated with this resource.
    name: Required. The canonical resource name of the interconnect.
    physicalPorts: Output only. Physical ports (e.g., TenGigE0/0/0/1) that
      form the interconnect.
    remotePeeringNetworkType: Optional. The remote peering network type of the
      interconnect. It is required when peering separation is enabled.
    updateTime: Output only. The time when the subnet was last updated.
    uuid: Output only. Unique identifier for the link.
  """

  class InterconnectTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Type of interconnect, which takes only the value 'DEDICATED'
    for now.

    Values:
      INTERCONNECT_TYPE_UNSPECIFIED: Unspecified.
      DEDICATED: Dedicated Interconnect.
    """
    INTERCONNECT_TYPE_UNSPECIFIED = 0
    DEDICATED = 1

  class RemotePeeringNetworkTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The remote peering network type of the interconnect. It is
    required when peering separation is enabled.

    Values:
      REMOTE_PEERING_NETWORK_TYPE_UNSPECIFIED: Unspecified.
      REMOTE_PEERING_NETWORK_TYPE_CUSTOMER_INTERNAL: Customer's trusted
        internal network.
      REMOTE_PEERING_NETWORK_TYPE_CUSTOMER_INTERNET: Customer's untrust
        network that has internet access.
    """
    REMOTE_PEERING_NETWORK_TYPE_UNSPECIFIED = 0
    REMOTE_PEERING_NETWORK_TYPE_CUSTOMER_INTERNAL = 1
    REMOTE_PEERING_NETWORK_TYPE_CUSTOMER_INTERNET = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  deviceCloudResourceName = _messages.StringField(3)
  interconnectType = _messages.EnumField('InterconnectTypeValueValuesEnum', 4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  physicalPorts = _messages.StringField(7, repeated=True)
  remotePeeringNetworkType = _messages.EnumField('RemotePeeringNetworkTypeValueValuesEnum', 8)
  updateTime = _messages.StringField(9)
  uuid = _messages.StringField(10)


class InterconnectAttachment(_messages.Message):
  r"""Message describing InterconnectAttachment object

  Enums:
    PeeringTypeValueValuesEnum: Optional. The remote peering network type of
      the underlying interconnect. It is required when peering separation is
      enabled.
    StateValueValuesEnum: Output only. Current stage of the resource to the
      device by config push.

  Messages:
    LabelsValue: Labels associated with this resource.

  Fields:
    createTime: Output only. The time when the interconnect attachment was
      created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    interconnect: Required. The canonical name of underlying Interconnect
      object that this attachment's traffic will traverse through. The name is
      in the form of `projects/{project}/locations/{location}/zones/{zone}/int
      erconnects/{interconnect}`.
    labels: Labels associated with this resource.
    mtu: IP (L3) MTU value of the virtual edge cloud. Valid values are: 1500
      and 9000. Default to 1500 if not set.
    name: Required. The canonical resource name of the interconnect
      attachment.
    network: Optional. The canonical Network name in the form of
      `projects/{project}/locations/{location}/zones/{zone}/networks/{network}
      `.
    peeringType: Optional. The remote peering network type of the underlying
      interconnect. It is required when peering separation is enabled.
    state: Output only. Current stage of the resource to the device by config
      push.
    updateTime: Output only. The time when the interconnect attachment was
      last updated.
    vlanId: Required. VLAN id provided by user. Must be site-wise unique.
  """

  class PeeringTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The remote peering network type of the underlying
    interconnect. It is required when peering separation is enabled.

    Values:
      REMOTE_PEERING_NETWORK_TYPE_UNSPECIFIED: Unspecified.
      REMOTE_PEERING_NETWORK_TYPE_CUSTOMER_INTERNAL: Customer's trusted
        internal network.
      REMOTE_PEERING_NETWORK_TYPE_CUSTOMER_INTERNET: Customer's untrust
        network that has internet access.
    """
    REMOTE_PEERING_NETWORK_TYPE_UNSPECIFIED = 0
    REMOTE_PEERING_NETWORK_TYPE_CUSTOMER_INTERNAL = 1
    REMOTE_PEERING_NETWORK_TYPE_CUSTOMER_INTERNET = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current stage of the resource to the device by config
    push.

    Values:
      STATE_UNKNOWN: Unspecified state.
      STATE_PENDING: The resource is being prepared to be applied to the rack.
      STATE_PROVISIONING: The resource has started being applied to the rack.
      STATE_RUNNING: The resource has been pushed to the rack.
      STATE_SUSPENDED: The resource failed to push to the rack.
      STATE_DELETING: The resource is under deletion.
    """
    STATE_UNKNOWN = 0
    STATE_PENDING = 1
    STATE_PROVISIONING = 2
    STATE_RUNNING = 3
    STATE_SUSPENDED = 4
    STATE_DELETING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  interconnect = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  mtu = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  name = _messages.StringField(6)
  network = _messages.StringField(7)
  peeringType = _messages.EnumField('PeeringTypeValueValuesEnum', 8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  updateTime = _messages.StringField(10)
  vlanId = _messages.IntegerField(11, variant=_messages.Variant.INT32)


class InterconnectDiagnostics(_messages.Message):
  r"""Diagnostics information about interconnect, contains detailed and
  current technical information about Google's side of the connection.

  Fields:
    linkLayerAddresses: A list of LinkLayerAddress, describing the ip address
      and corresponding link-layer address of the neighbors for this
      interconnect.
    links: A list of LinkStatus objects, used to describe the status for each
      link on the Interconnect.
    macAddress: The MAC address of the Interconnect's bundle interface.
  """

  linkLayerAddresses = _messages.MessageField('LinkLayerAddress', 1, repeated=True)
  links = _messages.MessageField('LinkStatus', 2, repeated=True)
  macAddress = _messages.StringField(3)


class Interface(_messages.Message):
  r"""Router Interface defines the GDCE zone side layer-3 information for
  building the BGP session.

  Fields:
    ipv4Cidr: IP address and range of the interface.
    ipv6Cidr: IPv6 address and range of the interface.
    linkedInterconnectAttachment: The canonical name of the linked
      Interconnect attachment.
    loopbackIpAddresses: Create loopback interface in the router when
      specified. The number of IP addresses must match the number of TOR
      devices.
    name: Name of this interface entry. Unique within the Zones resource.
    subnetwork: The canonical name of the subnetwork resource that this
      interface belongs to.
  """

  ipv4Cidr = _messages.StringField(1)
  ipv6Cidr = _messages.StringField(2)
  linkedInterconnectAttachment = _messages.StringField(3)
  loopbackIpAddresses = _messages.StringField(4, repeated=True)
  name = _messages.StringField(5)
  subnetwork = _messages.StringField(6)


class LinkLACPStatus(_messages.Message):
  r"""Describing the status of a LACP link.

  Enums:
    StateValueValuesEnum: The state of a LACP link.

  Fields:
    aggregatable: A true value indicates that the participant will allow the
      link to be used as part of the aggregate. A false value indicates the
      link should be used as an individual link.
    collecting: If true, the participant is collecting incoming frames on the
      link, otherwise false
    distributing: When true, the participant is distributing outgoing frames;
      when false, distribution is disabled
    googleSystemId: System ID of the port on Google's side of the LACP
      exchange.
    neighborSystemId: System ID of the port on the neighbor's side of the LACP
      exchange.
    state: The state of a LACP link.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of a LACP link.

    Values:
      UNKNOWN: The default state indicating state is in unknown state.
      ACTIVE: The link is configured and active within the bundle.
      DETACHED: The link is not configured within the bundle, this means the
        rest of the object should be empty.
    """
    UNKNOWN = 0
    ACTIVE = 1
    DETACHED = 2

  aggregatable = _messages.BooleanField(1)
  collecting = _messages.BooleanField(2)
  distributing = _messages.BooleanField(3)
  googleSystemId = _messages.StringField(4)
  neighborSystemId = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class LinkLLDPStatus(_messages.Message):
  r"""Describing a LLDP link.

  Fields:
    peerChassisId: The peer chassis component of the endpoint identifier
      associated with the transmitting LLDP agent.
    peerChassisIdType: The format and source of the peer chassis identifier
      string.
    peerPortId: The port component of the endpoint identifier associated with
      the transmitting LLDP agent. If the specified port is an IEEE 802.3
      Repeater port, then this TLV is optional.
    peerPortIdType: The format and source of the peer port identifier string.
    peerSystemDescription: The textual description of the network entity of
      LLDP peer.
    peerSystemName: The peer system's administratively assigned name.
  """

  peerChassisId = _messages.StringField(1)
  peerChassisIdType = _messages.StringField(2)
  peerPortId = _messages.StringField(3)
  peerPortIdType = _messages.StringField(4)
  peerSystemDescription = _messages.StringField(5)
  peerSystemName = _messages.StringField(6)


class LinkLayerAddress(_messages.Message):
  r"""LinkLayerAddress contains an IP address and corresponding link-layer
  address.

  Fields:
    ipAddress: The IP address of this neighbor.
    macAddress: The MAC address of this neighbor.
  """

  ipAddress = _messages.StringField(1)
  macAddress = _messages.StringField(2)


class LinkStatus(_messages.Message):
  r"""Describing the status for each link on the Interconnect.

  Fields:
    circuitId: The unique ID for this link assigned during turn up by Google.
    lacpStatus: Describing the state of a LACP link.
    lldpStatuses: A list of LinkLLDPStatus objects, used to describe LLDP
      status of each peer for each link on the Interconnect.
    packetCounts: Packet counts specific statistics for this link.
  """

  circuitId = _messages.StringField(1)
  lacpStatus = _messages.MessageField('LinkLACPStatus', 2)
  lldpStatuses = _messages.MessageField('LinkLLDPStatus', 3, repeated=True)
  packetCounts = _messages.MessageField('PacketCounts', 4)


class ListInterconnectAttachmentsResponse(_messages.Message):
  r"""Message for response to listing InterconnectAttachments

  Fields:
    interconnectAttachments: The list of InterconnectAttachment
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  interconnectAttachments = _messages.MessageField('InterconnectAttachment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListInterconnectsResponse(_messages.Message):
  r"""Message for response to listing Interconnects

  Fields:
    interconnects: The list of Interconnect
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  interconnects = _messages.MessageField('Interconnect', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListNetworksResponse(_messages.Message):
  r"""Message for response to listing Networks

  Fields:
    networks: The list of Network
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  networks = _messages.MessageField('Network', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListRoutersResponse(_messages.Message):
  r"""Message for response to listing Routers

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    routers: The list of Router
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  routers = _messages.MessageField('Router', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListSubnetsResponse(_messages.Message):
  r"""Message for response to listing Subnets

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    subnets: The list of Subnet
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  subnets = _messages.MessageField('Subnet', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListZonesResponse(_messages.Message):
  r"""Deprecated: not implemented. Message for response to listing Zones

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
    zones: The list of Zone
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  zones = _messages.MessageField('Zone', 3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Network(_messages.Message):
  r"""Message describing Network object

  Messages:
    LabelsValue: Labels associated with this resource.

  Fields:
    createTime: Output only. The time when the network was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    labels: Labels associated with this resource.
    mtu: IP (L3) MTU value of the network. Valid values are: 1500 and 9000.
      Default to 1500 if not set.
    name: Required. The canonical resource name of the network.
    updateTime: Output only. The time when the network was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  mtu = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class NetworkStatus(_messages.Message):
  r"""NetworkStatus has a list of status for the subnets under the current
  network.

  Enums:
    MacsecStatusInternalLinksValueValuesEnum: The MACsec status of internal
      links.

  Fields:
    macsecStatusInternalLinks: The MACsec status of internal links.
    subnetStatus: A list of status for the subnets under the current network.
  """

  class MacsecStatusInternalLinksValueValuesEnum(_messages.Enum):
    r"""The MACsec status of internal links.

    Values:
      MACSEC_STATUS_UNSPECIFIED: MACsec status not specified, likely due to
        missing metrics.
      SECURE: All relevant links have at least one MACsec session up.
      UNSECURE: At least one relevant link does not have any MACsec sessions
        up.
    """
    MACSEC_STATUS_UNSPECIFIED = 0
    SECURE = 1
    UNSECURE = 2

  macsecStatusInternalLinks = _messages.EnumField('MacsecStatusInternalLinksValueValuesEnum', 1)
  subnetStatus = _messages.MessageField('SubnetStatus', 2, repeated=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class PacketCounts(_messages.Message):
  r"""Containing a collection of interface-related statistics objects.

  Fields:
    inboundDiscards: The number of inbound packets that were chosen to be
      discarded even though no errors had been detected to prevent their being
      deliverable.
    inboundErrors: The number of inbound packets that contained errors.
    inboundUnicast: The number of packets that are delivered.
    outboundDiscards: The number of outbound packets that were chosen to be
      discarded even though no errors had been detected to prevent their being
      transmitted.
    outboundErrors: The number of outbound packets that could not be
      transmitted because of errors.
    outboundUnicast: The total number of packets that are requested be
      transmitted.
  """

  inboundDiscards = _messages.IntegerField(1)
  inboundErrors = _messages.IntegerField(2)
  inboundUnicast = _messages.IntegerField(3)
  outboundDiscards = _messages.IntegerField(4)
  outboundErrors = _messages.IntegerField(5)
  outboundUnicast = _messages.IntegerField(6)


class PrefixCounter(_messages.Message):
  r"""PrefixCounter contains a collection of prefixes related counts.

  Fields:
    advertised: Number of prefixes advertised.
    denied: Number of prefixes denied.
    received: Number of prefixes received.
    sent: Number of prefixes sent.
    suppressed: Number of prefixes suppressed.
    withdrawn: Number of prefixes withdrawn.
  """

  advertised = _messages.IntegerField(1)
  denied = _messages.IntegerField(2)
  received = _messages.IntegerField(3)
  sent = _messages.IntegerField(4)
  suppressed = _messages.IntegerField(5)
  withdrawn = _messages.IntegerField(6)


class Router(_messages.Message):
  r"""Message describing Router object

  Enums:
    StateValueValuesEnum: Output only. Current stage of the resource to the
      device by config push.

  Messages:
    LabelsValue: Labels associated with this resource.

  Fields:
    bgp: BGP information specific to this router.
    bgpPeer: BGP peers.
    createTime: Output only. The time when the router was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    interface: Router interfaces.
    labels: Labels associated with this resource.
    name: Required. The canonical resource name of the router.
    network: Required. The canonical name of the network to which this router
      belongs. The name is in the form of `projects/{project}/locations/{locat
      ion}/zones/{zone}/networks/{network}`.
    routeAdvertisements: Optional. A list of CIDRs in IP/Length format to
      advertise northbound as static routes from this router.
    state: Output only. Current stage of the resource to the device by config
      push.
    updateTime: Output only. The time when the router was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current stage of the resource to the device by config
    push.

    Values:
      STATE_UNKNOWN: Unspecified state.
      STATE_PENDING: The resource is being prepared to be applied to the rack.
      STATE_PROVISIONING: The resource has started being applied to the rack.
      STATE_RUNNING: The resource has been pushed to the rack.
      STATE_SUSPENDED: The resource failed to push to the rack.
      STATE_DELETING: The resource is under deletion.
    """
    STATE_UNKNOWN = 0
    STATE_PENDING = 1
    STATE_PROVISIONING = 2
    STATE_RUNNING = 3
    STATE_SUSPENDED = 4
    STATE_DELETING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bgp = _messages.MessageField('Bgp', 1)
  bgpPeer = _messages.MessageField('BgpPeer', 2, repeated=True)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  interface = _messages.MessageField('Interface', 5, repeated=True)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  network = _messages.StringField(8)
  routeAdvertisements = _messages.StringField(9, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  updateTime = _messages.StringField(11)


class RouterStatus(_messages.Message):
  r"""Describing the current status of a router.

  Fields:
    bgpPeerStatus: A list of BgpPeerStatus objects, describing all BGP peers
      related to this router.
    network: The canonical name of the network to which this router belongs.
  """

  bgpPeerStatus = _messages.MessageField('BgpPeerStatus', 1, repeated=True)
  network = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Subnet(_messages.Message):
  r"""Message describing Subnet object

  Enums:
    BondingTypeValueValuesEnum: Optional. A bonding type in the subnet
      creation specifies whether a VLAN being created will be present on
      Bonded or Non-Bonded or Both port types. In addition, this flag is to be
      used to set the specific network configuration which clusters can then
      use for their workloads based on the bonding choice.
    StateValueValuesEnum: Output only. Current stage of the resource to the
      device by config push.

  Messages:
    LabelsValue: Labels associated with this resource.

  Fields:
    bondingType: Optional. A bonding type in the subnet creation specifies
      whether a VLAN being created will be present on Bonded or Non-Bonded or
      Both port types. In addition, this flag is to be used to set the
      specific network configuration which clusters can then use for their
      workloads based on the bonding choice.
    createTime: Output only. The time when the subnet was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    ipv4Cidr: The ranges of ipv4 addresses that are owned by this subnetwork.
    ipv6Cidr: The ranges of ipv6 addresses that are owned by this subnetwork.
    labels: Labels associated with this resource.
    name: Required. The canonical resource name of the subnet.
    network: Required. The network that this subnetwork belongs to.
    state: Output only. Current stage of the resource to the device by config
      push.
    updateTime: Output only. The time when the subnet was last updated.
    vlanId: Optional. VLAN id provided by user. If not specified we assign one
      automatically.
  """

  class BondingTypeValueValuesEnum(_messages.Enum):
    r"""Optional. A bonding type in the subnet creation specifies whether a
    VLAN being created will be present on Bonded or Non-Bonded or Both port
    types. In addition, this flag is to be used to set the specific network
    configuration which clusters can then use for their workloads based on the
    bonding choice.

    Values:
      BONDING_TYPE_UNSPECIFIED: Unspecified Bonding type will be unspecified
        by default and if the user chooses to not specify a bonding type at
        time of creating the VLAN. This will be treated as mixed bonding where
        the VLAN will have both bonded and non-bonded connectivity to
        machines.
      BONDED: Multi homed.
      NON_BONDED: Single homed.
    """
    BONDING_TYPE_UNSPECIFIED = 0
    BONDED = 1
    NON_BONDED = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current stage of the resource to the device by config
    push.

    Values:
      STATE_UNKNOWN: Unspecified state.
      STATE_PENDING: The resource is being prepared to be applied to the rack.
      STATE_PROVISIONING: The resource has started being applied to the rack.
      STATE_RUNNING: The resource has been pushed to the rack.
      STATE_SUSPENDED: The resource failed to push to the rack.
      STATE_DELETING: The resource is under deletion.
    """
    STATE_UNKNOWN = 0
    STATE_PENDING = 1
    STATE_PROVISIONING = 2
    STATE_RUNNING = 3
    STATE_SUSPENDED = 4
    STATE_DELETING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bondingType = _messages.EnumField('BondingTypeValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  ipv4Cidr = _messages.StringField(4, repeated=True)
  ipv6Cidr = _messages.StringField(5, repeated=True)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  network = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  updateTime = _messages.StringField(10)
  vlanId = _messages.IntegerField(11, variant=_messages.Variant.INT32)


class SubnetStatus(_messages.Message):
  r"""SubnetStatus contains detailed and current technical information about
  this subnet resource.

  Fields:
    linkLayerAddresses: A list of LinkLayerAddress, describing the ip address
      and corresponding link-layer address of the neighbors for this subnet.
    macAddress: BVI MAC address.
    name: The name of CCFE subnet resource.
  """

  linkLayerAddresses = _messages.MessageField('LinkLayerAddress', 1, repeated=True)
  macAddress = _messages.StringField(2)
  name = _messages.StringField(3)


class Zone(_messages.Message):
  r"""A Google Edge Cloud zone.

  Messages:
    LabelsValue: Deprecated: not implemented. Labels as key value pairs.

  Fields:
    createTime: Output only. The time when the zone was created.
    labels: Deprecated: not implemented. Labels as key value pairs.
    layoutName: Deprecated: not implemented. The deployment layout type.
    name: Required. The resource name of the zone.
    updateTime: Output only. The time when the zone was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Deprecated: not implemented. Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  layoutName = _messages.StringField(3)
  name = _messages.StringField(4)
  updateTime = _messages.StringField(5)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
