"""Generated message classes for gkeonprem version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'gkeonprem'


class Authorization(_messages.Message):
  r"""Authorization defines the On-Prem cluster authorization configuration to
  bootstrap onto the admin cluster.

  Fields:
    adminUsers: For VMware and bare metal user clusters, users will be granted
      the cluster-admin role on the cluster, which provides full
      administrative access to the cluster. For bare metal admin clusters,
      users will be granted the cluster-view role, which limits users to read-
      only access.
  """

  adminUsers = _messages.MessageField('ClusterUser', 1, repeated=True)


class BareMetalAdminApiServerArgument(_messages.Message):
  r"""BareMetalAdminApiServerArgument represents an arg name->value pair. Only
  a subset of customized flags are supported. Please refer to the API server
  documentation below to know the exact format:
  https://kubernetes.io/docs/reference/command-line-tools-reference/kube-
  apiserver/

  Fields:
    argument: Required. The argument name as it appears on the API Server
      command line please make sure to remove the leading dashes.
    value: Required. The value of the arg as it will be passed to the API
      Server command line.
  """

  argument = _messages.StringField(1)
  value = _messages.StringField(2)


class BareMetalAdminCluster(_messages.Message):
  r"""Resource that represents a bare metal admin cluster.

  Enums:
    StateValueValuesEnum: Output only. The current state of the bare metal
      admin cluster.

  Messages:
    AnnotationsValue: Annotations on the bare metal admin cluster. This field
      has the same restrictions as Kubernetes annotations. The total size of
      all keys and values combined is limited to 256k. Key can have 2
      segments: prefix (optional) and name (required), separated by a slash
      (/). Prefix must be a DNS subdomain. Name must be 63 characters or less,
      begin and end with alphanumerics, with dashes (-), underscores (_), dots
      (.), and alphanumerics between.

  Fields:
    annotations: Annotations on the bare metal admin cluster. This field has
      the same restrictions as Kubernetes annotations. The total size of all
      keys and values combined is limited to 256k. Key can have 2 segments:
      prefix (optional) and name (required), separated by a slash (/). Prefix
      must be a DNS subdomain. Name must be 63 characters or less, begin and
      end with alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.
    bareMetalVersion: The Anthos clusters on bare metal version for the bare
      metal admin cluster.
    binaryAuthorization: Binary Authorization related configurations.
    clusterOperations: Cluster operations configuration.
    controlPlane: Control plane configuration.
    createTime: Output only. The time at which this bare metal admin cluster
      was created.
    deleteTime: Output only. The time at which this bare metal admin cluster
      was deleted. If the resource is not deleted, this must be empty
    description: A human readable description of this bare metal admin
      cluster.
    endpoint: Output only. The IP address name of bare metal admin cluster's
      API server.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding. Allows clients to
      perform consistent read-modify-writes through optimistic concurrency
      control.
    fleet: Output only. Fleet configuration for the cluster.
    loadBalancer: Load balancer configuration.
    localName: Output only. The object name of the bare metal cluster custom
      resource. This field is used to support conflicting names when enrolling
      existing clusters to the API. When used as a part of cluster enrollment,
      this field will differ from the ID in the resource name. For new
      clusters, this field will match the user provided cluster name and be
      visible in the last component of the resource name. It is not
      modifiable. All users should use this name to access their cluster using
      gkectl or kubectl and should expect to see the local name when viewing
      admin cluster controller logs.
    maintenanceConfig: Maintenance configuration.
    maintenanceStatus: Output only. MaintenanceStatus representing state of
      maintenance.
    name: Immutable. The bare metal admin cluster resource name.
    networkConfig: Network configuration.
    nodeAccessConfig: Node access related configurations.
    nodeConfig: Workload node configuration.
    osEnvironmentConfig: OS environment related configurations.
    proxy: Proxy configuration.
    reconciling: Output only. If set, there are currently changes in flight to
      the bare metal Admin Cluster.
    securityConfig: Security related configuration.
    state: Output only. The current state of the bare metal admin cluster.
    status: Output only. ResourceStatus representing detailed cluster status.
    storage: Storage configuration.
    uid: Output only. The unique identifier of the bare metal admin cluster.
    updateTime: Output only. The time at which this bare metal admin cluster
      was last updated.
    validationCheck: Output only. ValidationCheck representing the result of
      the preflight check.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the bare metal admin cluster.

    Values:
      STATE_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the cluster is being
        created.
      RUNNING: The RUNNING state indicates the cluster has been created and is
        fully usable.
      RECONCILING: The RECONCILING state indicates that the cluster is being
        updated. It remains available, but potentially with degraded
        performance.
      STOPPING: The STOPPING state indicates the cluster is being deleted.
      ERROR: The ERROR state indicates the cluster is in a broken
        unrecoverable state.
      DEGRADED: The DEGRADED state indicates the cluster requires user action
        to restore full functionality.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RECONCILING = 3
    STOPPING = 4
    ERROR = 5
    DEGRADED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations on the bare metal admin cluster. This field has the same
    restrictions as Kubernetes annotations. The total size of all keys and
    values combined is limited to 256k. Key can have 2 segments: prefix
    (optional) and name (required), separated by a slash (/). Prefix must be a
    DNS subdomain. Name must be 63 characters or less, begin and end with
    alphanumerics, with dashes (-), underscores (_), dots (.), and
    alphanumerics between.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  bareMetalVersion = _messages.StringField(2)
  binaryAuthorization = _messages.MessageField('BinaryAuthorization', 3)
  clusterOperations = _messages.MessageField('BareMetalAdminClusterOperationsConfig', 4)
  controlPlane = _messages.MessageField('BareMetalAdminControlPlaneConfig', 5)
  createTime = _messages.StringField(6)
  deleteTime = _messages.StringField(7)
  description = _messages.StringField(8)
  endpoint = _messages.StringField(9)
  etag = _messages.StringField(10)
  fleet = _messages.MessageField('Fleet', 11)
  loadBalancer = _messages.MessageField('BareMetalAdminLoadBalancerConfig', 12)
  localName = _messages.StringField(13)
  maintenanceConfig = _messages.MessageField('BareMetalAdminMaintenanceConfig', 14)
  maintenanceStatus = _messages.MessageField('BareMetalAdminMaintenanceStatus', 15)
  name = _messages.StringField(16)
  networkConfig = _messages.MessageField('BareMetalAdminNetworkConfig', 17)
  nodeAccessConfig = _messages.MessageField('BareMetalAdminNodeAccessConfig', 18)
  nodeConfig = _messages.MessageField('BareMetalAdminWorkloadNodeConfig', 19)
  osEnvironmentConfig = _messages.MessageField('BareMetalAdminOsEnvironmentConfig', 20)
  proxy = _messages.MessageField('BareMetalAdminProxyConfig', 21)
  reconciling = _messages.BooleanField(22)
  securityConfig = _messages.MessageField('BareMetalAdminSecurityConfig', 23)
  state = _messages.EnumField('StateValueValuesEnum', 24)
  status = _messages.MessageField('ResourceStatus', 25)
  storage = _messages.MessageField('BareMetalAdminStorageConfig', 26)
  uid = _messages.StringField(27)
  updateTime = _messages.StringField(28)
  validationCheck = _messages.MessageField('ValidationCheck', 29)


class BareMetalAdminClusterOperationsConfig(_messages.Message):
  r"""BareMetalAdminClusterOperationsConfig specifies the admin cluster's
  observability infrastructure.

  Fields:
    enableApplicationLogs: Whether collection of application logs/metrics
      should be enabled (in addition to system logs/metrics).
  """

  enableApplicationLogs = _messages.BooleanField(1)


class BareMetalAdminControlPlaneConfig(_messages.Message):
  r"""BareMetalAdminControlPlaneConfig specifies the control plane
  configuration.

  Fields:
    apiServerArgs: Customizes the default API server args. Only a subset of
      customized flags are supported. Please refer to the API server
      documentation below to know the exact format:
      https://kubernetes.io/docs/reference/command-line-tools-reference/kube-
      apiserver/
    controlPlaneNodePoolConfig: Required. Configures the node pool running the
      control plane. If specified the corresponding NodePool will be created
      for the cluster's control plane. The NodePool will have the same name
      and namespace as the cluster.
  """

  apiServerArgs = _messages.MessageField('BareMetalAdminApiServerArgument', 1, repeated=True)
  controlPlaneNodePoolConfig = _messages.MessageField('BareMetalAdminControlPlaneNodePoolConfig', 2)


class BareMetalAdminControlPlaneNodePoolConfig(_messages.Message):
  r"""BareMetalAdminControlPlaneNodePoolConfig specifies the control plane
  node pool configuration. We have a control plane specific node pool config
  so that we can flexible about supporting control plane specific fields in
  the future.

  Fields:
    nodePoolConfig: Required. The generic configuration for a node pool
      running the control plane.
  """

  nodePoolConfig = _messages.MessageField('BareMetalNodePoolConfig', 1)


class BareMetalAdminDrainedMachine(_messages.Message):
  r"""BareMetalAdminDrainedMachine represents the machines that are drained.

  Fields:
    nodeIp: Drained machine IP address.
  """

  nodeIp = _messages.StringField(1)


class BareMetalAdminDrainingMachine(_messages.Message):
  r"""BareMetalAdminDrainingMachine represents the machines that are currently
  draining.

  Fields:
    nodeIp: Draining machine IP address.
    podCount: The count of pods yet to drain.
  """

  nodeIp = _messages.StringField(1)
  podCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class BareMetalAdminIslandModeCidrConfig(_messages.Message):
  r"""BareMetalAdminIslandModeCidrConfig specifies the cluster CIDR
  configuration while running in island mode.

  Fields:
    podAddressCidrBlocks: Required. All pods in the cluster are assigned an
      RFC1918 IPv4 address from these ranges. This field cannot be changed
      after creation.
    serviceAddressCidrBlocks: Required. All services in the cluster are
      assigned an RFC1918 IPv4 address from these ranges. This field cannot be
      changed after creation.
  """

  podAddressCidrBlocks = _messages.StringField(1, repeated=True)
  serviceAddressCidrBlocks = _messages.StringField(2, repeated=True)


class BareMetalAdminLoadBalancerConfig(_messages.Message):
  r"""BareMetalAdminLoadBalancerConfig specifies the load balancer
  configuration.

  Fields:
    manualLbConfig: Manually configured load balancers.
    portConfig: Configures the ports that the load balancer will listen on.
    vipConfig: The VIPs used by the load balancer.
  """

  manualLbConfig = _messages.MessageField('BareMetalAdminManualLbConfig', 1)
  portConfig = _messages.MessageField('BareMetalAdminPortConfig', 2)
  vipConfig = _messages.MessageField('BareMetalAdminVipConfig', 3)


class BareMetalAdminMachineDrainStatus(_messages.Message):
  r"""BareMetalAdminMachineDrainStatus represents the status of bare metal
  node machines that are undergoing drain operations.

  Fields:
    drainedMachines: The list of drained machines.
    drainingMachines: The list of draning machines.
  """

  drainedMachines = _messages.MessageField('BareMetalAdminDrainedMachine', 1, repeated=True)
  drainingMachines = _messages.MessageField('BareMetalAdminDrainingMachine', 2, repeated=True)


class BareMetalAdminMaintenanceConfig(_messages.Message):
  r"""BareMetalAdminMaintenanceConfig specifies configurations to put bare
  metal Admin cluster CRs nodes in and out of maintenance.

  Fields:
    maintenanceAddressCidrBlocks: Required. All IPv4 address from these ranges
      will be placed into maintenance mode. Nodes in maintenance mode will be
      cordoned and drained. When both of these are true, the
      "baremetal.cluster.gke.io/maintenance" annotation will be set on the
      node resource.
  """

  maintenanceAddressCidrBlocks = _messages.StringField(1, repeated=True)


class BareMetalAdminMaintenanceStatus(_messages.Message):
  r"""BareMetalAdminMaintenanceStatus represents the maintenance status for
  bare metal Admin cluster CR's nodes.

  Fields:
    machineDrainStatus: Represents the status of draining and drained machine
      nodes. This is used to show the progress of cluster upgrade.
  """

  machineDrainStatus = _messages.MessageField('BareMetalAdminMachineDrainStatus', 1)


class BareMetalAdminManualLbConfig(_messages.Message):
  r"""BareMetalAdminManualLbConfig represents configuration parameters for a
  manual load balancer.

  Fields:
    enabled: Whether manual load balancing is enabled.
  """

  enabled = _messages.BooleanField(1)


class BareMetalAdminNetworkConfig(_messages.Message):
  r"""BareMetalAdminNetworkConfig specifies the cluster network configuration.

  Fields:
    islandModeCidr: Configuration for Island mode CIDR.
  """

  islandModeCidr = _messages.MessageField('BareMetalAdminIslandModeCidrConfig', 1)


class BareMetalAdminNodeAccessConfig(_messages.Message):
  r"""Specifies the node access related settings for the bare metal admin
  cluster.

  Fields:
    loginUser: Required. LoginUser is the user name used to access node
      machines. It defaults to "root" if not set.
  """

  loginUser = _messages.StringField(1)


class BareMetalAdminOsEnvironmentConfig(_messages.Message):
  r"""Specifies operating system operation settings for cluster provisioning.

  Fields:
    packageRepoExcluded: Whether the package repo should be added when
      initializing bare metal machines.
  """

  packageRepoExcluded = _messages.BooleanField(1)


class BareMetalAdminPortConfig(_messages.Message):
  r"""BareMetalAdminPortConfig is the specification of load balancer ports.

  Fields:
    controlPlaneLoadBalancerPort: The port that control plane hosted load
      balancers will listen on.
  """

  controlPlaneLoadBalancerPort = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class BareMetalAdminProxyConfig(_messages.Message):
  r"""BareMetalAdminProxyConfig specifies the cluster proxy configuration.

  Fields:
    noProxy: A list of IPs, hostnames, and domains that should skip the proxy.
      Examples: ["127.0.0.1", "example.com", ".corp", "localhost"].
    uri: Required. Specifies the address of your proxy server. Examples:
      `http://domain` WARNING: Do not provide credentials in the format
      `http://(username:password@)domain` these will be rejected by the
      server.
  """

  noProxy = _messages.StringField(1, repeated=True)
  uri = _messages.StringField(2)


class BareMetalAdminSecurityConfig(_messages.Message):
  r"""Specifies the security related settings for the bare metal admin
  cluster.

  Fields:
    authorization: Configures user access to the admin cluster.
  """

  authorization = _messages.MessageField('Authorization', 1)


class BareMetalAdminStorageConfig(_messages.Message):
  r"""BareMetalAdminStorageConfig specifies the cluster storage configuration.

  Fields:
    lvpNodeMountsConfig: Required. Specifies the config for local
      PersistentVolumes backed by mounted node disks. These disks need to be
      formatted and mounted by the user, which can be done before or after
      cluster creation.
    lvpShareConfig: Required. Specifies the config for local PersistentVolumes
      backed by subdirectories in a shared filesystem. These subdirectores are
      automatically created during cluster creation.
  """

  lvpNodeMountsConfig = _messages.MessageField('BareMetalLvpConfig', 1)
  lvpShareConfig = _messages.MessageField('BareMetalLvpShareConfig', 2)


class BareMetalAdminVipConfig(_messages.Message):
  r"""BareMetalAdminVipConfig for bare metal load balancer configurations.

  Fields:
    controlPlaneVip: The VIP which you previously set aside for the Kubernetes
      API of this bare metal admin cluster.
  """

  controlPlaneVip = _messages.StringField(1)


class BareMetalAdminWorkloadNodeConfig(_messages.Message):
  r"""BareMetalAdminWorkloadNodeConfig specifies the workload node
  configurations.

  Fields:
    maxPodsPerNode: The maximum number of pods a node can run. The size of the
      CIDR range assigned to the node will be derived from this parameter. By
      default 110 Pods are created per Node. Upper bound is 250 for both HA
      and non-HA admin cluster. Lower bound is 64 for non-HA admin cluster and
      32 for HA admin cluster.
  """

  maxPodsPerNode = _messages.IntegerField(1)


class BareMetalApiServerArgument(_messages.Message):
  r"""Represents an arg name->value pair. Only a subset of customized flags
  are supported. For the exact format, refer to the [API server
  documentation](https://kubernetes.io/docs/reference/command-line-tools-
  reference/kube-apiserver/).

  Fields:
    argument: Required. The argument name as it appears on the API Server
      command line, make sure to remove the leading dashes.
    value: Required. The value of the arg as it will be passed to the API
      Server command line.
  """

  argument = _messages.StringField(1)
  value = _messages.StringField(2)


class BareMetalBgpLbConfig(_messages.Message):
  r"""BareMetalBgpLbConfig represents configuration parameters for a Border
  Gateway Protocol (BGP) load balancer.

  Fields:
    addressPools: Required. AddressPools is a list of non-overlapping IP pools
      used by load balancer typed services. All addresses must be routable to
      load balancer nodes. IngressVIP must be included in the pools.
    asn: Required. BGP autonomous system number (ASN) of the cluster. This
      field can be updated after cluster creation.
    bgpPeerConfigs: Required. The list of BGP peers that the cluster will
      connect to. At least one peer must be configured for each control plane
      node. Control plane nodes will connect to these peers to advertise the
      control plane VIP. The Services load balancer also uses these peers by
      default. This field can be updated after cluster creation.
    loadBalancerNodePoolConfig: Specifies the node pool running data plane
      load balancing. L2 connectivity is required among nodes in this pool. If
      missing, the control plane node pool is used for data plane load
      balancing.
  """

  addressPools = _messages.MessageField('BareMetalLoadBalancerAddressPool', 1, repeated=True)
  asn = _messages.IntegerField(2)
  bgpPeerConfigs = _messages.MessageField('BareMetalBgpPeerConfig', 3, repeated=True)
  loadBalancerNodePoolConfig = _messages.MessageField('BareMetalLoadBalancerNodePoolConfig', 4)


class BareMetalBgpPeerConfig(_messages.Message):
  r"""BareMetalBgpPeerConfig represents configuration parameters for a Border
  Gateway Protocol (BGP) peer.

  Fields:
    asn: Required. BGP autonomous system number (ASN) for the network that
      contains the external peer device.
    controlPlaneNodes: The IP address of the control plane node that connects
      to the external peer. If you don't specify any control plane nodes, all
      control plane nodes can connect to the external peer. If you specify one
      or more IP addresses, only the nodes specified participate in peering
      sessions.
    ipAddress: Required. The IP address of the external peer device.
  """

  asn = _messages.IntegerField(1)
  controlPlaneNodes = _messages.StringField(2, repeated=True)
  ipAddress = _messages.StringField(3)


class BareMetalCluster(_messages.Message):
  r"""Resource that represents a bare metal user cluster.

  Enums:
    StateValueValuesEnum: Output only. The current state of the bare metal
      user cluster.

  Messages:
    AnnotationsValue: Annotations on the bare metal user cluster. This field
      has the same restrictions as Kubernetes annotations. The total size of
      all keys and values combined is limited to 256k. Key can have 2
      segments: prefix (optional) and name (required), separated by a slash
      (/). Prefix must be a DNS subdomain. Name must be 63 characters or less,
      begin and end with alphanumerics, with dashes (-), underscores (_), dots
      (.), and alphanumerics between.

  Fields:
    adminClusterMembership: Required. The admin cluster this bare metal user
      cluster belongs to. This is the full resource name of the admin
      cluster's fleet membership.
    adminClusterName: Output only. The resource name of the bare metal admin
      cluster managing this user cluster.
    annotations: Annotations on the bare metal user cluster. This field has
      the same restrictions as Kubernetes annotations. The total size of all
      keys and values combined is limited to 256k. Key can have 2 segments:
      prefix (optional) and name (required), separated by a slash (/). Prefix
      must be a DNS subdomain. Name must be 63 characters or less, begin and
      end with alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.
    bareMetalVersion: Required. The Anthos clusters on bare metal version for
      your user cluster.
    binaryAuthorization: Binary Authorization related configurations.
    clusterOperations: Cluster operations configuration.
    controlPlane: Required. Control plane configuration.
    createTime: Output only. The time when the bare metal user cluster was
      created.
    deleteTime: Output only. The time when the bare metal user cluster was
      deleted. If the resource is not deleted, this must be empty
    description: A human readable description of this bare metal user cluster.
    endpoint: Output only. The IP address of the bare metal user cluster's API
      server.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and may be sent on update and delete requests to
      ensure the client has an up-to-date value before proceeding. Allows
      clients to perform consistent read-modify-writes through optimistic
      concurrency control.
    fleet: Output only. Fleet configuration for the cluster.
    loadBalancer: Required. Load balancer configuration.
    localName: Output only. The object name of the bare metal user cluster
      custom resource on the associated admin cluster. This field is used to
      support conflicting names when enrolling existing clusters to the API.
      When used as a part of cluster enrollment, this field will differ from
      the name in the resource name. For new clusters, this field will match
      the user provided cluster name and be visible in the last component of
      the resource name. It is not modifiable. When the local name and cluster
      name differ, the local name is used in the admin cluster controller
      logs. You use the cluster name when accessing the cluster using bmctl
      and kubectl.
    localNamespace: Output only. The namespace of the cluster.
    maintenanceConfig: Maintenance configuration.
    maintenanceStatus: Output only. Status of on-going maintenance tasks.
    name: Immutable. The bare metal user cluster resource name.
    networkConfig: Required. Network configuration.
    nodeAccessConfig: Node access related configurations.
    nodeConfig: Workload node configuration.
    osEnvironmentConfig: OS environment related configurations.
    proxy: Proxy configuration.
    reconciling: Output only. If set, there are currently changes in flight to
      the bare metal user cluster.
    securityConfig: Security related setting configuration.
    state: Output only. The current state of the bare metal user cluster.
    status: Output only. Detailed cluster status.
    storage: Required. Storage configuration.
    uid: Output only. The unique identifier of the bare metal user cluster.
    updateTime: Output only. The time when the bare metal user cluster was
      last updated.
    upgradePolicy: The cluster upgrade policy.
    validationCheck: Output only. The result of the preflight check.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the bare metal user cluster.

    Values:
      STATE_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the cluster is being
        created.
      RUNNING: The RUNNING state indicates the cluster has been created and is
        fully usable.
      RECONCILING: The RECONCILING state indicates that the cluster is being
        updated. It remains available, but potentially with degraded
        performance.
      STOPPING: The STOPPING state indicates the cluster is being deleted.
      ERROR: The ERROR state indicates the cluster is in a broken
        unrecoverable state.
      DEGRADED: The DEGRADED state indicates the cluster requires user action
        to restore full functionality.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RECONCILING = 3
    STOPPING = 4
    ERROR = 5
    DEGRADED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations on the bare metal user cluster. This field has the same
    restrictions as Kubernetes annotations. The total size of all keys and
    values combined is limited to 256k. Key can have 2 segments: prefix
    (optional) and name (required), separated by a slash (/). Prefix must be a
    DNS subdomain. Name must be 63 characters or less, begin and end with
    alphanumerics, with dashes (-), underscores (_), dots (.), and
    alphanumerics between.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  adminClusterMembership = _messages.StringField(1)
  adminClusterName = _messages.StringField(2)
  annotations = _messages.MessageField('AnnotationsValue', 3)
  bareMetalVersion = _messages.StringField(4)
  binaryAuthorization = _messages.MessageField('BinaryAuthorization', 5)
  clusterOperations = _messages.MessageField('BareMetalClusterOperationsConfig', 6)
  controlPlane = _messages.MessageField('BareMetalControlPlaneConfig', 7)
  createTime = _messages.StringField(8)
  deleteTime = _messages.StringField(9)
  description = _messages.StringField(10)
  endpoint = _messages.StringField(11)
  etag = _messages.StringField(12)
  fleet = _messages.MessageField('Fleet', 13)
  loadBalancer = _messages.MessageField('BareMetalLoadBalancerConfig', 14)
  localName = _messages.StringField(15)
  localNamespace = _messages.StringField(16)
  maintenanceConfig = _messages.MessageField('BareMetalMaintenanceConfig', 17)
  maintenanceStatus = _messages.MessageField('BareMetalMaintenanceStatus', 18)
  name = _messages.StringField(19)
  networkConfig = _messages.MessageField('BareMetalNetworkConfig', 20)
  nodeAccessConfig = _messages.MessageField('BareMetalNodeAccessConfig', 21)
  nodeConfig = _messages.MessageField('BareMetalWorkloadNodeConfig', 22)
  osEnvironmentConfig = _messages.MessageField('BareMetalOsEnvironmentConfig', 23)
  proxy = _messages.MessageField('BareMetalProxyConfig', 24)
  reconciling = _messages.BooleanField(25)
  securityConfig = _messages.MessageField('BareMetalSecurityConfig', 26)
  state = _messages.EnumField('StateValueValuesEnum', 27)
  status = _messages.MessageField('ResourceStatus', 28)
  storage = _messages.MessageField('BareMetalStorageConfig', 29)
  uid = _messages.StringField(30)
  updateTime = _messages.StringField(31)
  upgradePolicy = _messages.MessageField('BareMetalClusterUpgradePolicy', 32)
  validationCheck = _messages.MessageField('ValidationCheck', 33)


class BareMetalClusterOperationsConfig(_messages.Message):
  r"""Specifies the bare metal user cluster's observability infrastructure.

  Fields:
    enableApplicationLogs: Whether collection of application logs/metrics
      should be enabled (in addition to system logs/metrics).
  """

  enableApplicationLogs = _messages.BooleanField(1)


class BareMetalClusterUpgradePolicy(_messages.Message):
  r"""BareMetalClusterUpgradePolicy defines the cluster upgrade policy.

  Enums:
    PolicyValueValuesEnum: Specifies which upgrade policy to use.

  Fields:
    controlPlaneOnly: Controls whether upgrade applies to only the control
      plane.
    pause: Output only. Pause is used to show the upgrade pause status. It's
      view only for now.
    policy: Specifies which upgrade policy to use.
  """

  class PolicyValueValuesEnum(_messages.Enum):
    r"""Specifies which upgrade policy to use.

    Values:
      NODE_POOL_POLICY_UNSPECIFIED: No upgrade policy selected.
      SERIAL: Upgrade worker node pools sequentially.
      CONCURRENT: Upgrade all worker node pools in parallel.
    """
    NODE_POOL_POLICY_UNSPECIFIED = 0
    SERIAL = 1
    CONCURRENT = 2

  controlPlaneOnly = _messages.BooleanField(1)
  pause = _messages.BooleanField(2)
  policy = _messages.EnumField('PolicyValueValuesEnum', 3)


class BareMetalControlPlaneConfig(_messages.Message):
  r"""Specifies the control plane configuration.

  Fields:
    apiServerArgs: Customizes the default API server args. Only a subset of
      customized flags are supported. For the exact format, refer to the [API
      server documentation](https://kubernetes.io/docs/reference/command-line-
      tools-reference/kube-apiserver/).
    controlPlaneNodePoolConfig: Required. Configures the node pool running the
      control plane.
  """

  apiServerArgs = _messages.MessageField('BareMetalApiServerArgument', 1, repeated=True)
  controlPlaneNodePoolConfig = _messages.MessageField('BareMetalControlPlaneNodePoolConfig', 2)


class BareMetalControlPlaneNodePoolConfig(_messages.Message):
  r"""Specifies the control plane node pool configuration.

  Fields:
    nodePoolConfig: Required. The generic configuration for a node pool
      running the control plane.
  """

  nodePoolConfig = _messages.MessageField('BareMetalNodePoolConfig', 1)


class BareMetalDrainedMachine(_messages.Message):
  r"""Represents a machine that is currently drained.

  Fields:
    nodeIp: Drained machine IP address.
  """

  nodeIp = _messages.StringField(1)


class BareMetalDrainingMachine(_messages.Message):
  r"""Represents a machine that is currently draining.

  Fields:
    nodeIp: Draining machine IP address.
    podCount: The count of pods yet to drain.
  """

  nodeIp = _messages.StringField(1)
  podCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class BareMetalIslandModeCidrConfig(_messages.Message):
  r"""Specifies the cluster CIDR configuration while running in island mode.

  Fields:
    podAddressCidrBlocks: Required. All pods in the cluster are assigned an
      RFC1918 IPv4 address from these ranges. This field cannot be changed
      after creation.
    serviceAddressCidrBlocks: Required. All services in the cluster are
      assigned an RFC1918 IPv4 address from these ranges. This field is
      mutable after creation starting with version 1.15.
  """

  podAddressCidrBlocks = _messages.StringField(1, repeated=True)
  serviceAddressCidrBlocks = _messages.StringField(2, repeated=True)


class BareMetalKubeletConfig(_messages.Message):
  r"""KubeletConfig defines the modifiable kubelet configurations for bare
  metal machines. Note: this list includes fields supported in GKE (see
  https://cloud.google.com/kubernetes-engine/docs/how-to/node-system-
  config#kubelet-options).

  Fields:
    registryBurst: The maximum size of bursty pulls, temporarily allows pulls
      to burst to this number, while still not exceeding registry_pull_qps.
      The value must not be a negative number. Updating this field may impact
      scalability by changing the amount of traffic produced by image pulls.
      Defaults to 10.
    registryPullQps: The limit of registry pulls per second. Setting this
      value to 0 means no limit. Updating this field may impact scalability by
      changing the amount of traffic produced by image pulls. Defaults to 5.
    serializeImagePullsDisabled: Prevents the Kubelet from pulling multiple
      images at a time. We recommend *not* changing the default value on nodes
      that run docker daemon with version < 1.9 or an Another Union File
      System (Aufs) storage backend. Issue
      https://github.com/kubernetes/kubernetes/issues/10959 has more details.
  """

  registryBurst = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  registryPullQps = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  serializeImagePullsDisabled = _messages.BooleanField(3)


class BareMetalLoadBalancerAddressPool(_messages.Message):
  r"""Represents an IP pool used by the load balancer.

  Fields:
    addresses: Required. The addresses that are part of this pool. Each
      address must be either in the CIDR form (*******/24) or range form
      (*******-*******).
    avoidBuggyIps: If true, avoid using IPs ending in .0 or .255. This avoids
      buggy consumer devices mistakenly dropping IPv4 traffic for those
      special IP addresses.
    manualAssign: If true, prevent IP addresses from being automatically
      assigned.
    pool: Required. The name of the address pool.
  """

  addresses = _messages.StringField(1, repeated=True)
  avoidBuggyIps = _messages.BooleanField(2)
  manualAssign = _messages.BooleanField(3)
  pool = _messages.StringField(4)


class BareMetalLoadBalancerConfig(_messages.Message):
  r"""Specifies the load balancer configuration.

  Fields:
    bgpLbConfig: Configuration for BGP typed load balancers. When set
      network_config.advanced_networking is automatically set to true.
    manualLbConfig: Manually configured load balancers.
    metalLbConfig: Configuration for MetalLB load balancers.
    portConfig: Configures the ports that the load balancer will listen on.
    vipConfig: The VIPs used by the load balancer.
  """

  bgpLbConfig = _messages.MessageField('BareMetalBgpLbConfig', 1)
  manualLbConfig = _messages.MessageField('BareMetalManualLbConfig', 2)
  metalLbConfig = _messages.MessageField('BareMetalMetalLbConfig', 3)
  portConfig = _messages.MessageField('BareMetalPortConfig', 4)
  vipConfig = _messages.MessageField('BareMetalVipConfig', 5)


class BareMetalLoadBalancerNodePoolConfig(_messages.Message):
  r"""Specifies the load balancer's node pool configuration.

  Fields:
    nodePoolConfig: The generic configuration for a node pool running a load
      balancer.
  """

  nodePoolConfig = _messages.MessageField('BareMetalNodePoolConfig', 1)


class BareMetalLvpConfig(_messages.Message):
  r"""Specifies the configs for local persistent volumes (PVs).

  Fields:
    path: Required. The host machine path.
    storageClass: Required. The StorageClass name that PVs will be created
      with.
  """

  path = _messages.StringField(1)
  storageClass = _messages.StringField(2)


class BareMetalLvpShareConfig(_messages.Message):
  r"""Specifies the configs for local persistent volumes under a shared file
  system.

  Fields:
    lvpConfig: Required. Defines the machine path and storage class for the
      LVP Share.
    sharedPathPvCount: The number of subdirectories to create under path.
  """

  lvpConfig = _messages.MessageField('BareMetalLvpConfig', 1)
  sharedPathPvCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class BareMetalMachineDrainStatus(_messages.Message):
  r"""Represents the status of node machines that are undergoing drain
  operations.

  Fields:
    drainedMachines: The list of drained machines.
    drainingMachines: The list of draning machines.
  """

  drainedMachines = _messages.MessageField('BareMetalDrainedMachine', 1, repeated=True)
  drainingMachines = _messages.MessageField('BareMetalDrainingMachine', 2, repeated=True)


class BareMetalMaintenanceConfig(_messages.Message):
  r"""Specifies configurations to put bare metal nodes in and out of
  maintenance.

  Fields:
    maintenanceAddressCidrBlocks: Required. All IPv4 address from these ranges
      will be placed into maintenance mode. Nodes in maintenance mode will be
      cordoned and drained. When both of these are true, the
      "baremetal.cluster.gke.io/maintenance" annotation will be set on the
      node resource.
  """

  maintenanceAddressCidrBlocks = _messages.StringField(1, repeated=True)


class BareMetalMaintenanceStatus(_messages.Message):
  r"""Represents the maintenance status of the bare metal user cluster.

  Fields:
    machineDrainStatus: The maintenance status of node machines.
  """

  machineDrainStatus = _messages.MessageField('BareMetalMachineDrainStatus', 1)


class BareMetalManualLbConfig(_messages.Message):
  r"""Represents configuration parameters for a manual load balancer.

  Fields:
    enabled: Whether manual load balancing is enabled.
  """

  enabled = _messages.BooleanField(1)


class BareMetalMetalLbConfig(_messages.Message):
  r"""Represents configuration parameters for a MetalLB load balancer.

  Fields:
    addressPools: Required. AddressPools is a list of non-overlapping IP pools
      used by load balancer typed services. All addresses must be routable to
      load balancer nodes. IngressVIP must be included in the pools.
    loadBalancerNodePoolConfig: Specifies the node pool running the load
      balancer. L2 connectivity is required among nodes in this pool. If
      missing, the control plane node pool is used as the load balancer pool.
  """

  addressPools = _messages.MessageField('BareMetalLoadBalancerAddressPool', 1, repeated=True)
  loadBalancerNodePoolConfig = _messages.MessageField('BareMetalLoadBalancerNodePoolConfig', 2)


class BareMetalMultipleNetworkInterfacesConfig(_messages.Message):
  r"""Specifies the multiple networking interfaces cluster configuration.

  Fields:
    enabled: Whether to enable multiple network interfaces for your pods. When
      set network_config.advanced_networking is automatically set to true.
  """

  enabled = _messages.BooleanField(1)


class BareMetalNetworkConfig(_messages.Message):
  r"""Specifies the cluster network configuration.

  Fields:
    advancedNetworking: Enables the use of advanced Anthos networking
      features, such as Bundled Load Balancing with BGP or the egress NAT
      gateway. Setting configuration for advanced networking features will
      automatically set this flag.
    islandModeCidr: Configuration for island mode CIDR. In an island-mode
      network, nodes have unique IP addresses, but pods don't have unique
      addresses across clusters. This doesn't cause problems because pods in
      one cluster never directly communicate with pods in another cluster.
      Instead, there are gateways that mediate between a pod in one cluster
      and a pod in another cluster.
    multipleNetworkInterfacesConfig: Configuration for multiple network
      interfaces.
    srIovConfig: Configuration for SR-IOV.
  """

  advancedNetworking = _messages.BooleanField(1)
  islandModeCidr = _messages.MessageField('BareMetalIslandModeCidrConfig', 2)
  multipleNetworkInterfacesConfig = _messages.MessageField('BareMetalMultipleNetworkInterfacesConfig', 3)
  srIovConfig = _messages.MessageField('BareMetalSrIovConfig', 4)


class BareMetalNodeAccessConfig(_messages.Message):
  r"""Specifies the node access related settings for the bare metal user
  cluster.

  Fields:
    loginUser: LoginUser is the user name used to access node machines. It
      defaults to "root" if not set.
  """

  loginUser = _messages.StringField(1)


class BareMetalNodeConfig(_messages.Message):
  r"""BareMetalNodeConfig lists machine addresses to access Nodes.

  Messages:
    LabelsValue: The labels assigned to this node. An object containing a list
      of key/value pairs. The labels here, unioned with the labels set on
      BareMetalNodePoolConfig are the set of labels that will be applied to
      the node. If there are any conflicts, the BareMetalNodeConfig labels
      take precedence. Example: { "name": "wrench", "mass": "1.3kg", "count":
      "3" }.

  Fields:
    labels: The labels assigned to this node. An object containing a list of
      key/value pairs. The labels here, unioned with the labels set on
      BareMetalNodePoolConfig are the set of labels that will be applied to
      the node. If there are any conflicts, the BareMetalNodeConfig labels
      take precedence. Example: { "name": "wrench", "mass": "1.3kg", "count":
      "3" }.
    nodeIp: The default IPv4 address for SSH access and Kubernetes node.
      Example: ***********
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels assigned to this node. An object containing a list of
    key/value pairs. The labels here, unioned with the labels set on
    BareMetalNodePoolConfig are the set of labels that will be applied to the
    node. If there are any conflicts, the BareMetalNodeConfig labels take
    precedence. Example: { "name": "wrench", "mass": "1.3kg", "count": "3" }.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  nodeIp = _messages.StringField(2)


class BareMetalNodePool(_messages.Message):
  r"""Resource that represents a bare metal node pool.

  Enums:
    StateValueValuesEnum: Output only. The current state of the bare metal
      node pool.

  Messages:
    AnnotationsValue: Annotations on the bare metal node pool. This field has
      the same restrictions as Kubernetes annotations. The total size of all
      keys and values combined is limited to 256k. Key can have 2 segments:
      prefix (optional) and name (required), separated by a slash (/). Prefix
      must be a DNS subdomain. Name must be 63 characters or less, begin and
      end with alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.

  Fields:
    annotations: Annotations on the bare metal node pool. This field has the
      same restrictions as Kubernetes annotations. The total size of all keys
      and values combined is limited to 256k. Key can have 2 segments: prefix
      (optional) and name (required), separated by a slash (/). Prefix must be
      a DNS subdomain. Name must be 63 characters or less, begin and end with
      alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.
    bareMetalVersion: Specifies node pool version. The field is used to
      upgrade the nodepool to the specified version. When specified during
      node pool creation, the maximum allowed version skew between cluster and
      nodepool is 1 minor version. When the field is not specified during
      nodepool creation, the nodepool is created at the cluster version.
    createTime: Output only. The time at which this bare metal node pool was
      created.
    deleteTime: Output only. The time at which this bare metal node pool was
      deleted. If the resource is not deleted, this must be empty
    displayName: The display name for the bare metal node pool.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding. Allows clients to
      perform consistent read-modify-writes through optimistic concurrency
      control.
    name: Immutable. The bare metal node pool resource name.
    nodePoolConfig: Required. Node pool configuration.
    reconciling: Output only. If set, there are currently changes in flight to
      the bare metal node pool.
    state: Output only. The current state of the bare metal node pool.
    status: Output only. ResourceStatus representing the detailed node pool
      status.
    uid: Output only. The unique identifier of the bare metal node pool.
    updateTime: Output only. The time at which this bare metal node pool was
      last updated.
    upgradePolicy: The worker node pool upgrade policy.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the bare metal node pool.

    Values:
      STATE_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the bare metal node pool
        is being created.
      RUNNING: The RUNNING state indicates the bare metal node pool has been
        created and is fully usable.
      RECONCILING: The RECONCILING state indicates that the bare metal node
        pool is being updated. It remains available, but potentially with
        degraded performance.
      STOPPING: The STOPPING state indicates the bare metal node pool is being
        deleted.
      ERROR: The ERROR state indicates the bare metal node pool is in a broken
        unrecoverable state.
      DEGRADED: The DEGRADED state indicates the bare metal node pool requires
        user action to restore full functionality.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RECONCILING = 3
    STOPPING = 4
    ERROR = 5
    DEGRADED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations on the bare metal node pool. This field has the same
    restrictions as Kubernetes annotations. The total size of all keys and
    values combined is limited to 256k. Key can have 2 segments: prefix
    (optional) and name (required), separated by a slash (/). Prefix must be a
    DNS subdomain. Name must be 63 characters or less, begin and end with
    alphanumerics, with dashes (-), underscores (_), dots (.), and
    alphanumerics between.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  bareMetalVersion = _messages.StringField(2)
  createTime = _messages.StringField(3)
  deleteTime = _messages.StringField(4)
  displayName = _messages.StringField(5)
  etag = _messages.StringField(6)
  name = _messages.StringField(7)
  nodePoolConfig = _messages.MessageField('BareMetalNodePoolConfig', 8)
  reconciling = _messages.BooleanField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  status = _messages.MessageField('ResourceStatus', 11)
  uid = _messages.StringField(12)
  updateTime = _messages.StringField(13)
  upgradePolicy = _messages.MessageField('BareMetalNodePoolUpgradePolicy', 14)


class BareMetalNodePoolConfig(_messages.Message):
  r"""BareMetalNodePoolConfig describes the configuration of all nodes within
  a given bare metal node pool.

  Enums:
    OperatingSystemValueValuesEnum: Specifies the nodes operating system
      (default: LINUX).

  Messages:
    LabelsValue: The labels assigned to nodes of this node pool. An object
      containing a list of key/value pairs. Example: { "name": "wrench",
      "mass": "1.3kg", "count": "3" }.

  Fields:
    kubeletConfig: The modifiable kubelet configurations for the bare metal
      machines.
    labels: The labels assigned to nodes of this node pool. An object
      containing a list of key/value pairs. Example: { "name": "wrench",
      "mass": "1.3kg", "count": "3" }.
    nodeConfigs: Required. The list of machine addresses in the bare metal
      node pool.
    operatingSystem: Specifies the nodes operating system (default: LINUX).
    taints: The initial taints assigned to nodes of this node pool.
  """

  class OperatingSystemValueValuesEnum(_messages.Enum):
    r"""Specifies the nodes operating system (default: LINUX).

    Values:
      OPERATING_SYSTEM_UNSPECIFIED: No operating system runtime selected.
      LINUX: Linux operating system.
    """
    OPERATING_SYSTEM_UNSPECIFIED = 0
    LINUX = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels assigned to nodes of this node pool. An object containing a
    list of key/value pairs. Example: { "name": "wrench", "mass": "1.3kg",
    "count": "3" }.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  kubeletConfig = _messages.MessageField('BareMetalKubeletConfig', 1)
  labels = _messages.MessageField('LabelsValue', 2)
  nodeConfigs = _messages.MessageField('BareMetalNodeConfig', 3, repeated=True)
  operatingSystem = _messages.EnumField('OperatingSystemValueValuesEnum', 4)
  taints = _messages.MessageField('NodeTaint', 5, repeated=True)


class BareMetalNodePoolUpgradePolicy(_messages.Message):
  r"""BareMetalNodePoolUpgradePolicy defines the node pool upgrade policy.

  Fields:
    independent: Specify the intent to upgrade the node pool with or without
      the control plane upgrade. Defaults to false i.e. upgrade the node pool
      with control plane upgrade. Set this to true to upgrade or downgrade the
      node pool independently from the control plane.
    parallelUpgradeConfig: The parallel upgrade settings for worker node
      pools.
  """

  independent = _messages.BooleanField(1)
  parallelUpgradeConfig = _messages.MessageField('BareMetalParallelUpgradeConfig', 2)


class BareMetalOsEnvironmentConfig(_messages.Message):
  r"""Specifies operating system settings for cluster provisioning.

  Fields:
    packageRepoExcluded: Whether the package repo should not be included when
      initializing bare metal machines.
  """

  packageRepoExcluded = _messages.BooleanField(1)


class BareMetalParallelUpgradeConfig(_messages.Message):
  r"""BareMetalParallelUpgradeConfig defines the parallel upgrade settings for
  worker node pools.

  Fields:
    concurrentNodes: The maximum number of nodes that can be upgraded at once.
    minimumAvailableNodes: The minimum number of nodes that should be healthy
      and available during an upgrade. If set to the default value of 0, it is
      possible that none of the nodes will be available during an upgrade.
  """

  concurrentNodes = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minimumAvailableNodes = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class BareMetalPortConfig(_messages.Message):
  r"""Specifies load balancer ports for the bare metal user cluster.

  Fields:
    controlPlaneLoadBalancerPort: The port that control plane hosted load
      balancers will listen on.
  """

  controlPlaneLoadBalancerPort = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class BareMetalProxyConfig(_messages.Message):
  r"""Specifies the cluster proxy configuration.

  Fields:
    noProxy: A list of IPs, hostnames, and domains that should skip the proxy.
      Examples: ["127.0.0.1", "example.com", ".corp", "localhost"].
    uri: Required. Specifies the address of your proxy server. Examples:
      `http://domain` Do not provide credentials in the format
      `http://(username:password@)domain` these will be rejected by the
      server.
  """

  noProxy = _messages.StringField(1, repeated=True)
  uri = _messages.StringField(2)


class BareMetalSecurityConfig(_messages.Message):
  r"""Specifies the security related settings for the bare metal user cluster.

  Fields:
    authorization: Configures user access to the user cluster.
  """

  authorization = _messages.MessageField('Authorization', 1)


class BareMetalSrIovConfig(_messages.Message):
  r"""Specifies the SR-IOV networking operator config.

  Fields:
    enabled: Whether to install the SR-IOV operator.
  """

  enabled = _messages.BooleanField(1)


class BareMetalStandaloneApiServerArgument(_messages.Message):
  r"""Represents an arg name->value pair. Only a subset of customized flags
  are supported. For the exact format, refer to the [API server
  documentation](https://kubernetes.io/docs/reference/command-line-tools-
  reference/kube-apiserver/).

  Fields:
    argument: Required. The argument name as it appears on the API Server
      command line, make sure to remove the leading dashes.
    value: Required. The value of the arg as it will be passed to the API
      Server command line.
  """

  argument = _messages.StringField(1)
  value = _messages.StringField(2)


class BareMetalStandaloneBgpLbConfig(_messages.Message):
  r"""BareMetalStandaloneBgpLbConfig represents configuration parameters for a
  Border Gateway Protocol (BGP) load balancer.

  Fields:
    addressPools: Required. AddressPools is a list of non-overlapping IP pools
      used by load balancer typed services. All addresses must be routable to
      load balancer nodes. IngressVIP must be included in the pools.
    asn: Required. BGP autonomous system number (ASN) of the cluster. This
      field can be updated after cluster creation.
    bgpPeerConfigs: Required. The list of BGP peers that the cluster will
      connect to. At least one peer must be configured for each control plane
      node. Control plane nodes will connect to these peers to advertise the
      control plane VIP. The Services load balancer also uses these peers by
      default. This field can be updated after cluster creation.
    loadBalancerNodePoolConfig: Specifies the node pool running data plane
      load balancing. L2 connectivity is required among nodes in this pool. If
      missing, the control plane node pool is used for data plane load
      balancing.
  """

  addressPools = _messages.MessageField('BareMetalStandaloneLoadBalancerAddressPool', 1, repeated=True)
  asn = _messages.IntegerField(2)
  bgpPeerConfigs = _messages.MessageField('BareMetalStandaloneBgpPeerConfig', 3, repeated=True)
  loadBalancerNodePoolConfig = _messages.MessageField('BareMetalStandaloneLoadBalancerNodePoolConfig', 4)


class BareMetalStandaloneBgpPeerConfig(_messages.Message):
  r"""BareMetalStandaloneBgpPeerConfig represents configuration parameters for
  a Border Gateway Protocol (BGP) peer.

  Fields:
    asn: Required. BGP autonomous system number (ASN) for the network that
      contains the external peer device.
    controlPlaneNodes: The IP address of the control plane node that connects
      to the external peer. If you don't specify any control plane nodes, all
      control plane nodes can connect to the external peer. If you specify one
      or more IP addresses, only the nodes specified participate in peering
      sessions.
    ipAddress: Required. The IP address of the external peer device.
  """

  asn = _messages.IntegerField(1)
  controlPlaneNodes = _messages.StringField(2, repeated=True)
  ipAddress = _messages.StringField(3)


class BareMetalStandaloneCluster(_messages.Message):
  r"""Resource that represents a bare metal standalone cluster.

  Enums:
    ProfileValueValuesEnum: Profile specifies the installation profile for the
      Anthos bare metal cluster.
    StateValueValuesEnum: Output only. The current state of the bare metal
      standalone cluster.

  Messages:
    AnnotationsValue: Annotations on the bare metal standalone cluster. This
      field has the same restrictions as Kubernetes annotations. The total
      size of all keys and values combined is limited to 256k. Key can have 2
      segments: prefix (optional) and name (required), separated by a slash
      (/). Prefix must be a DNS subdomain. Name must be 63 characters or less,
      begin and end with alphanumerics, with dashes (-), underscores (_), dots
      (.), and alphanumerics between.

  Fields:
    annotations: Annotations on the bare metal standalone cluster. This field
      has the same restrictions as Kubernetes annotations. The total size of
      all keys and values combined is limited to 256k. Key can have 2
      segments: prefix (optional) and name (required), separated by a slash
      (/). Prefix must be a DNS subdomain. Name must be 63 characters or less,
      begin and end with alphanumerics, with dashes (-), underscores (_), dots
      (.), and alphanumerics between.
    bareMetalVersion: Required. The Anthos clusters on bare metal version for
      your standalone cluster.
    binaryAuthorization: Binary Authorization related configurations.
    clusterOperations: Cluster operations configuration.
    controlPlane: Required. Control plane configuration.
    createTime: Output only. The time when the bare metal standalone cluster
      was created.
    deleteTime: Output only. The time when the bare metal standalone cluster
      was deleted. If the resource is not deleted, this must be empty
    description: A human readable description of this bare metal standalone
      cluster.
    endpoint: Output only. The IP address of the bare metal standalone
      cluster's API server.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and may be sent on update and delete requests to
      ensure the client has an up-to-date value before proceeding. Allows
      clients to perform consistent read-modify-writes through optimistic
      concurrency control.
    fleet: Output only. Fleet configuration for the cluster.
    loadBalancer: Required. Load balancer configuration.
    localName: Output only. The object name of the bare metal standalone
      cluster custom resource. This field is used to support conflicting names
      when enrolling existing clusters to the API. When used as a part of
      cluster enrollment, this field will differ from the name in the resource
      name. For new clusters, this field will match the user provided cluster
      name and be visible in the last component of the resource name. It is
      not modifiable. When the local name and cluster name differ, the local
      name is used in the admin cluster controller logs. You use the cluster
      name when accessing the cluster using bmctl and kubectl.
    localNamespace: Output only. The kubernetes namespace where the custom
      resource exists.
    maintenanceConfig: Maintenance configuration.
    maintenanceStatus: Output only. Status of on-going maintenance tasks.
    name: Immutable. The bare metal standalone cluster resource name.
    networkConfig: Required. Network configuration.
    nodeAccessConfig: Node access related configurations.
    nodeConfig: Workload node configuration.
    osEnvironmentConfig: OS environment related configurations.
    profile: Profile specifies the installation profile for the Anthos bare
      metal cluster.
    proxy: Proxy configuration.
    reconciling: Output only. If set, there are currently changes in flight to
      the bare metal standalone cluster.
    securityConfig: Security related setting configuration.
    state: Output only. The current state of the bare metal standalone
      cluster.
    status: Output only. Detailed cluster status.
    storage: Required. Storage configuration.
    uid: Output only. The unique identifier of the bare metal standalone
      cluster.
    updateTime: Output only. The time when the bare metal standalone cluster
      was last updated.
    upgradePolicy: The cluster upgrade policy.
    validationCheck: Output only. The result of the preflight check.
  """

  class ProfileValueValuesEnum(_messages.Enum):
    r"""Profile specifies the installation profile for the Anthos bare metal
    cluster.

    Values:
      DEFAULT: Default is the default installation profile.
      EDGE: Edge profile is tailored for edge deployment.
    """
    DEFAULT = 0
    EDGE = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the bare metal standalone cluster.

    Values:
      STATE_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the cluster is being
        created.
      RUNNING: The RUNNING state indicates the cluster has been created and is
        fully usable.
      RECONCILING: The RECONCILING state indicates that the cluster is being
        updated. It remains available, but potentially with degraded
        performance.
      STOPPING: The STOPPING state indicates the cluster is being deleted.
      ERROR: The ERROR state indicates the cluster is in a broken
        unrecoverable state.
      DEGRADED: The DEGRADED state indicates the cluster requires user action
        to restore full functionality.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RECONCILING = 3
    STOPPING = 4
    ERROR = 5
    DEGRADED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations on the bare metal standalone cluster. This field has the
    same restrictions as Kubernetes annotations. The total size of all keys
    and values combined is limited to 256k. Key can have 2 segments: prefix
    (optional) and name (required), separated by a slash (/). Prefix must be a
    DNS subdomain. Name must be 63 characters or less, begin and end with
    alphanumerics, with dashes (-), underscores (_), dots (.), and
    alphanumerics between.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  bareMetalVersion = _messages.StringField(2)
  binaryAuthorization = _messages.MessageField('BinaryAuthorization', 3)
  clusterOperations = _messages.MessageField('BareMetalStandaloneClusterOperationsConfig', 4)
  controlPlane = _messages.MessageField('BareMetalStandaloneControlPlaneConfig', 5)
  createTime = _messages.StringField(6)
  deleteTime = _messages.StringField(7)
  description = _messages.StringField(8)
  endpoint = _messages.StringField(9)
  etag = _messages.StringField(10)
  fleet = _messages.MessageField('Fleet', 11)
  loadBalancer = _messages.MessageField('BareMetalStandaloneLoadBalancerConfig', 12)
  localName = _messages.StringField(13)
  localNamespace = _messages.StringField(14)
  maintenanceConfig = _messages.MessageField('BareMetalStandaloneMaintenanceConfig', 15)
  maintenanceStatus = _messages.MessageField('BareMetalStandaloneMaintenanceStatus', 16)
  name = _messages.StringField(17)
  networkConfig = _messages.MessageField('BareMetalStandaloneNetworkConfig', 18)
  nodeAccessConfig = _messages.MessageField('BareMetalStandaloneNodeAccessConfig', 19)
  nodeConfig = _messages.MessageField('BareMetalStandaloneWorkloadNodeConfig', 20)
  osEnvironmentConfig = _messages.MessageField('BareMetalStandaloneOsEnvironmentConfig', 21)
  profile = _messages.EnumField('ProfileValueValuesEnum', 22)
  proxy = _messages.MessageField('BareMetalStandaloneProxyConfig', 23)
  reconciling = _messages.BooleanField(24)
  securityConfig = _messages.MessageField('BareMetalStandaloneSecurityConfig', 25)
  state = _messages.EnumField('StateValueValuesEnum', 26)
  status = _messages.MessageField('ResourceStatus', 27)
  storage = _messages.MessageField('BareMetalStandaloneStorageConfig', 28)
  uid = _messages.StringField(29)
  updateTime = _messages.StringField(30)
  upgradePolicy = _messages.MessageField('BareMetalStandaloneClusterUpgradePolicy', 31)
  validationCheck = _messages.MessageField('ValidationCheck', 32)


class BareMetalStandaloneClusterOperationsConfig(_messages.Message):
  r"""Specifies the bare metal standalone cluster's observability
  infrastructure.

  Fields:
    enableApplicationLogs: Whether collection of application logs/metrics
      should be enabled (in addition to system logs/metrics).
  """

  enableApplicationLogs = _messages.BooleanField(1)


class BareMetalStandaloneClusterUpgradePolicy(_messages.Message):
  r"""BareMetalStandaloneClusterUpgradePolicy defines the cluster upgrade
  policy.

  Enums:
    PolicyValueValuesEnum: Specifies which upgrade policy to use.

  Fields:
    controlPlaneOnly: Controls whether upgrade applies to only the control
      plane.
    pause: Output only. Pause is used to show the upgrade pause status. It's
      view only for now.
    policy: Specifies which upgrade policy to use.
  """

  class PolicyValueValuesEnum(_messages.Enum):
    r"""Specifies which upgrade policy to use.

    Values:
      NODE_POOL_POLICY_UNSPECIFIED: No upgrade policy selected.
      SERIAL: Upgrade worker node pools sequentially.
      CONCURRENT: Upgrade all worker node pools in parallel.
    """
    NODE_POOL_POLICY_UNSPECIFIED = 0
    SERIAL = 1
    CONCURRENT = 2

  controlPlaneOnly = _messages.BooleanField(1)
  pause = _messages.BooleanField(2)
  policy = _messages.EnumField('PolicyValueValuesEnum', 3)


class BareMetalStandaloneControlPlaneConfig(_messages.Message):
  r"""Specifies the control plane configuration.

  Fields:
    apiServerArgs: Customizes the default API server args. Only a subset of
      customized flags are supported. For the exact format, refer to the [API
      server documentation](https://kubernetes.io/docs/reference/command-line-
      tools-reference/kube-apiserver/).
    controlPlaneNodePoolConfig: Required. Configures the node pool running the
      control plane.
  """

  apiServerArgs = _messages.MessageField('BareMetalStandaloneApiServerArgument', 1, repeated=True)
  controlPlaneNodePoolConfig = _messages.MessageField('BareMetalStandaloneControlPlaneNodePoolConfig', 2)


class BareMetalStandaloneControlPlaneNodePoolConfig(_messages.Message):
  r"""Specifies the control plane node pool configuration.

  Fields:
    nodePoolConfig: Required. The generic configuration for a node pool
      running the control plane.
  """

  nodePoolConfig = _messages.MessageField('BareMetalNodePoolConfig', 1)


class BareMetalStandaloneDrainedMachine(_messages.Message):
  r"""Represents a machine that is currently drained.

  Fields:
    nodeIp: Drained machine IP address.
  """

  nodeIp = _messages.StringField(1)


class BareMetalStandaloneDrainingMachine(_messages.Message):
  r"""Represents a machine that is currently draining.

  Fields:
    nodeIp: Draining machine IP address.
    podCount: The count of pods yet to drain.
  """

  nodeIp = _messages.StringField(1)
  podCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class BareMetalStandaloneIslandModeCidrConfig(_messages.Message):
  r"""Specifies the cluster CIDR configuration while running in island mode.

  Fields:
    podAddressCidrBlocks: Required. All pods in the cluster are assigned an
      RFC1918 IPv4 address from these ranges. This field cannot be changed
      after creation.
    serviceAddressCidrBlocks: Required. All services in the cluster are
      assigned an RFC1918 IPv4 address from these ranges. This field cannot be
      changed after creation.
  """

  podAddressCidrBlocks = _messages.StringField(1, repeated=True)
  serviceAddressCidrBlocks = _messages.StringField(2, repeated=True)


class BareMetalStandaloneKubeletConfig(_messages.Message):
  r"""KubeletConfig defines the modifiable kubelet configurations for
  baremetal machines. Note: this list includes fields supported in GKE (see
  https://cloud.google.com/kubernetes-engine/docs/how-to/node-system-
  config#kubelet-options).

  Fields:
    registryBurst: The maximum size of bursty pulls, temporarily allows pulls
      to burst to this number, while still not exceeding registry_pull_qps.
      The value must not be a negative number. Updating this field may impact
      scalability by changing the amount of traffic produced by image pulls.
      Defaults to 10.
    registryPullQps: The limit of registry pulls per second. Setting this
      value to 0 means no limit. Updating this field may impact scalability by
      changing the amount of traffic produced by image pulls. Defaults to 5.
    serializeImagePullsDisabled: Prevents the Kubelet from pulling multiple
      images at a time. We recommend *not* changing the default value on nodes
      that run docker daemon with version < 1.9 or an Another Union File
      System (Aufs) storage backend. Issue
      https://github.com/kubernetes/kubernetes/issues/10959 has more details.
  """

  registryBurst = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  registryPullQps = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  serializeImagePullsDisabled = _messages.BooleanField(3)


class BareMetalStandaloneLoadBalancerAddressPool(_messages.Message):
  r"""Represents an IP pool used by the load balancer.

  Fields:
    addresses: Required. The addresses that are part of this pool. Each
      address must be either in the CIDR form (*******/24) or range form
      (*******-*******).
    avoidBuggyIps: If true, avoid using IPs ending in .0 or .255. This avoids
      buggy consumer devices mistakenly dropping IPv4 traffic for those
      special IP addresses.
    manualAssign: If true, prevent IP addresses from being automatically
      assigned.
    pool: Required. The name of the address pool.
  """

  addresses = _messages.StringField(1, repeated=True)
  avoidBuggyIps = _messages.BooleanField(2)
  manualAssign = _messages.BooleanField(3)
  pool = _messages.StringField(4)


class BareMetalStandaloneLoadBalancerConfig(_messages.Message):
  r"""Specifies the load balancer configuration.

  Fields:
    bgpLbConfig: Configuration for BGP typed load balancers.
    manualLbConfig: Manually configured load balancers.
    metalLbConfig: Configuration for MetalLB load balancers.
    portConfig: Configures the ports that the load balancer will listen on.
    vipConfig: The VIPs used by the load balancer.
  """

  bgpLbConfig = _messages.MessageField('BareMetalStandaloneBgpLbConfig', 1)
  manualLbConfig = _messages.MessageField('BareMetalStandaloneManualLbConfig', 2)
  metalLbConfig = _messages.MessageField('BareMetalStandaloneMetalLbConfig', 3)
  portConfig = _messages.MessageField('BareMetalStandalonePortConfig', 4)
  vipConfig = _messages.MessageField('BareMetalStandaloneVipConfig', 5)


class BareMetalStandaloneLoadBalancerNodePoolConfig(_messages.Message):
  r"""Specifies the load balancer's node pool configuration.

  Fields:
    nodePoolConfig: The generic configuration for a node pool running a load
      balancer.
  """

  nodePoolConfig = _messages.MessageField('BareMetalNodePoolConfig', 1)


class BareMetalStandaloneMachineDrainStatus(_messages.Message):
  r"""Represents the status of node machines that are undergoing drain
  operations.

  Fields:
    drainedMachines: The list of drained machines.
    drainingMachines: The list of draning machines.
  """

  drainedMachines = _messages.MessageField('BareMetalStandaloneDrainedMachine', 1, repeated=True)
  drainingMachines = _messages.MessageField('BareMetalStandaloneDrainingMachine', 2, repeated=True)


class BareMetalStandaloneMaintenanceConfig(_messages.Message):
  r"""Specifies configurations to put bare metal nodes in and out of
  maintenance.

  Fields:
    maintenanceAddressCidrBlocks: Required. All IPv4 address from these ranges
      will be placed into maintenance mode. Nodes in maintenance mode will be
      cordoned and drained. When both of these are true, the
      "baremetal.cluster.gke.io/maintenance" annotation will be set on the
      node resource.
  """

  maintenanceAddressCidrBlocks = _messages.StringField(1, repeated=True)


class BareMetalStandaloneMaintenanceStatus(_messages.Message):
  r"""Represents the maintenance status of the bare metal standalone cluster.

  Fields:
    machineDrainStatus: The maintenance status of node machines.
  """

  machineDrainStatus = _messages.MessageField('BareMetalStandaloneMachineDrainStatus', 1)


class BareMetalStandaloneManualLbConfig(_messages.Message):
  r"""Represents configuration parameters for a manual load balancer.

  Fields:
    enabled: Whether manual load balancing is enabled.
  """

  enabled = _messages.BooleanField(1)


class BareMetalStandaloneMetalLbConfig(_messages.Message):
  r"""Represents configuration parameters for a MetalLB load balancer.

  Fields:
    addressPools: Required. AddressPools is a list of non-overlapping IP pools
      used by load balancer typed services. All addresses must be routable to
      load balancer nodes. IngressVIP must be included in the pools.
    loadBalancerNodePoolConfig: Specifies the node pool running the load
      balancer. L2 connectivity is required among nodes in this pool. If
      missing, the control plane node pool is used as the load balancer pool.
  """

  addressPools = _messages.MessageField('BareMetalStandaloneLoadBalancerAddressPool', 1, repeated=True)
  loadBalancerNodePoolConfig = _messages.MessageField('BareMetalStandaloneLoadBalancerNodePoolConfig', 2)


class BareMetalStandaloneNetworkConfig(_messages.Message):
  r"""Specifies the cluster network configuration.

  Fields:
    advancedNetworking: Enables the use of advanced Anthos networking
      features, such as Bundled Load Balancing with BGP or the egress NAT
      gateway. Setting configuration for advanced networking features will
      automatically set this flag.
    islandModeCidr: Configuration for island mode CIDR. In an island-mode
      network, nodes have unique IP addresses, but pods don't have unique
      addresses across clusters. This doesn't cause problems because pods in
      one cluster never directly communicate with pods in another cluster.
      Instead, there are gateways that mediate between a pod in one cluster
      and a pod in another cluster.
    multipleNetworkInterfacesConfig: Configuration for multiple network
      interfaces.
    srIovConfig: Configuration for SR-IOV.
  """

  advancedNetworking = _messages.BooleanField(1)
  islandModeCidr = _messages.MessageField('BareMetalStandaloneIslandModeCidrConfig', 2)
  multipleNetworkInterfacesConfig = _messages.MessageField('BareMetalStandloneMultipleNetworkInterfacesConfig', 3)
  srIovConfig = _messages.MessageField('BareMetalStandaloneSrIovConfig', 4)


class BareMetalStandaloneNodeAccessConfig(_messages.Message):
  r"""Specifies the node access related settings for the bare metal standalone
  cluster.

  Fields:
    loginUser: LoginUser is the user name used to access node machines. It
      defaults to "root" if not set.
  """

  loginUser = _messages.StringField(1)


class BareMetalStandaloneNodeConfig(_messages.Message):
  r"""BareMetalStandaloneNodeConfig lists machine addresses to access Nodes.

  Messages:
    LabelsValue: The labels assigned to this node. An object containing a list
      of key/value pairs. The labels here, unioned with the labels set on
      BareMetalStandaloneNodePoolConfig are the set of labels that will be
      applied to the node. If there are any conflicts, the
      BareMetalStandaloneNodeConfig labels take precedence. Example: { "name":
      "wrench", "mass": "1.3kg", "count": "3" }.

  Fields:
    labels: The labels assigned to this node. An object containing a list of
      key/value pairs. The labels here, unioned with the labels set on
      BareMetalStandaloneNodePoolConfig are the set of labels that will be
      applied to the node. If there are any conflicts, the
      BareMetalStandaloneNodeConfig labels take precedence. Example: { "name":
      "wrench", "mass": "1.3kg", "count": "3" }.
    nodeIp: The default IPv4 address for SSH access and Kubernetes node.
      Example: ***********
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels assigned to this node. An object containing a list of
    key/value pairs. The labels here, unioned with the labels set on
    BareMetalStandaloneNodePoolConfig are the set of labels that will be
    applied to the node. If there are any conflicts, the
    BareMetalStandaloneNodeConfig labels take precedence. Example: { "name":
    "wrench", "mass": "1.3kg", "count": "3" }.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  nodeIp = _messages.StringField(2)


class BareMetalStandaloneNodePool(_messages.Message):
  r"""Resource that represents a bare metal standalone node pool.

  Enums:
    StateValueValuesEnum: Output only. The current state of the bare metal
      standalone node pool.

  Messages:
    AnnotationsValue: Annotations on the bare metal standalone node pool. This
      field has the same restrictions as Kubernetes annotations. The total
      size of all keys and values combined is limited to 256k. Key can have 2
      segments: prefix (optional) and name (required), separated by a slash
      (/). Prefix must be a DNS subdomain. Name must be 63 characters or less,
      begin and end with alphanumerics, with dashes (-), underscores (_), dots
      (.), and alphanumerics between.

  Fields:
    annotations: Annotations on the bare metal standalone node pool. This
      field has the same restrictions as Kubernetes annotations. The total
      size of all keys and values combined is limited to 256k. Key can have 2
      segments: prefix (optional) and name (required), separated by a slash
      (/). Prefix must be a DNS subdomain. Name must be 63 characters or less,
      begin and end with alphanumerics, with dashes (-), underscores (_), dots
      (.), and alphanumerics between.
    createTime: Output only. The time at which this bare metal standalone node
      pool was created.
    deleteTime: Output only. The time at which this bare metal standalone node
      pool was deleted. If the resource is not deleted, this must be empty
    displayName: The display name for the bare metal standalone node pool.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding. Allows clients to
      perform consistent read-modify-writes through optimistic concurrency
      control.
    name: Immutable. The bare metal standalone node pool resource name.
    nodePoolConfig: Required. Node pool configuration.
    reconciling: Output only. If set, there are currently changes in flight to
      the bare metal standalone node pool.
    state: Output only. The current state of the bare metal standalone node
      pool.
    status: Output only. ResourceStatus representing the detailed node pool
      status.
    uid: Output only. The unique identifier of the bare metal standalone node
      pool.
    updateTime: Output only. The time at which this bare metal standalone node
      pool was last updated.
    upgradePolicy: The worker node pool upgrade config.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the bare metal standalone node pool.

    Values:
      STATE_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the bare metal standalone
        node pool is being created.
      RUNNING: The RUNNING state indicates the bare metal standalone node pool
        has been created and is fully usable.
      RECONCILING: The RECONCILING state indicates that the bare metal
        standalone node pool is being updated. It remains available, but
        potentially with degraded performance.
      STOPPING: The STOPPING state indicates the bare metal standalone node
        pool is being deleted.
      ERROR: The ERROR state indicates the bare metal standalone node pool is
        in a broken unrecoverable state.
      DEGRADED: The DEGRADED state indicates the bare metal standalone node
        pool requires user action to restore full functionality.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RECONCILING = 3
    STOPPING = 4
    ERROR = 5
    DEGRADED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations on the bare metal standalone node pool. This field has the
    same restrictions as Kubernetes annotations. The total size of all keys
    and values combined is limited to 256k. Key can have 2 segments: prefix
    (optional) and name (required), separated by a slash (/). Prefix must be a
    DNS subdomain. Name must be 63 characters or less, begin and end with
    alphanumerics, with dashes (-), underscores (_), dots (.), and
    alphanumerics between.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  deleteTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  nodePoolConfig = _messages.MessageField('BareMetalStandaloneNodePoolConfig', 7)
  reconciling = _messages.BooleanField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  status = _messages.MessageField('ResourceStatus', 10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)
  upgradePolicy = _messages.MessageField('BareMetalStandaloneNodePoolUpgradePolicy', 13)


class BareMetalStandaloneNodePoolConfig(_messages.Message):
  r"""BareMetalStandaloneNodePoolConfig describes the configuration of all
  nodes within a given bare metal standalone node pool.

  Enums:
    OperatingSystemValueValuesEnum: Specifies the nodes operating system
      (default: LINUX).

  Messages:
    LabelsValue: The labels assigned to nodes of this node pool. An object
      containing a list of key/value pairs. Example: { "name": "wrench",
      "mass": "1.3kg", "count": "3" }.

  Fields:
    kubeletConfig: The modifiable kubelet configurations for the baremetal
      machines.
    labels: The labels assigned to nodes of this node pool. An object
      containing a list of key/value pairs. Example: { "name": "wrench",
      "mass": "1.3kg", "count": "3" }.
    nodeConfigs: Required. The list of machine addresses in the bare metal
      standalone node pool.
    operatingSystem: Specifies the nodes operating system (default: LINUX).
    taints: The initial taints assigned to nodes of this node pool.
  """

  class OperatingSystemValueValuesEnum(_messages.Enum):
    r"""Specifies the nodes operating system (default: LINUX).

    Values:
      OPERATING_SYSTEM_UNSPECIFIED: No operating system runtime selected.
      LINUX: Linux operating system.
    """
    OPERATING_SYSTEM_UNSPECIFIED = 0
    LINUX = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels assigned to nodes of this node pool. An object containing a
    list of key/value pairs. Example: { "name": "wrench", "mass": "1.3kg",
    "count": "3" }.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  kubeletConfig = _messages.MessageField('BareMetalStandaloneKubeletConfig', 1)
  labels = _messages.MessageField('LabelsValue', 2)
  nodeConfigs = _messages.MessageField('BareMetalStandaloneNodeConfig', 3, repeated=True)
  operatingSystem = _messages.EnumField('OperatingSystemValueValuesEnum', 4)
  taints = _messages.MessageField('NodeTaint', 5, repeated=True)


class BareMetalStandaloneNodePoolUpgradePolicy(_messages.Message):
  r"""BareMetalStandaloneNodePoolUpgradePolicy defines the node pool upgrade
  policy.

  Fields:
    parallelUpgradeConfig: The parallel upgrade settings for worker node
      pools.
  """

  parallelUpgradeConfig = _messages.MessageField('BareMetalStandaloneParallelUpgradeConfig', 1)


class BareMetalStandaloneOsEnvironmentConfig(_messages.Message):
  r"""Specifies operating system operation settings for cluster provisioning.

  Fields:
    packageRepoExcluded: Whether the package repo should be added when
      initializing bare metal machines.
  """

  packageRepoExcluded = _messages.BooleanField(1)


class BareMetalStandaloneParallelUpgradeConfig(_messages.Message):
  r"""BareMetalStandaloneParallelUpgradeConfig defines the parallel upgrade
  settings for worker node pools.

  Fields:
    concurrentNodes: The maximum number of nodes that can be upgraded at once.
    minimumAvailableNodes: The minimum number of nodes that should be healthy
      and available during an upgrade. If set to the default value of 0, it is
      possible that none of the nodes will be available during an upgrade.
  """

  concurrentNodes = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minimumAvailableNodes = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class BareMetalStandalonePortConfig(_messages.Message):
  r"""Specifies load balancer ports for the bare metal standalone cluster.

  Fields:
    controlPlaneLoadBalancerPort: The port that control plane hosted load
      balancers will listen on.
  """

  controlPlaneLoadBalancerPort = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class BareMetalStandaloneProxyConfig(_messages.Message):
  r"""Specifies the cluster proxy configuration.

  Fields:
    noProxy: A list of IPs, hostnames, and domains that should skip the proxy.
      Examples: ["127.0.0.1", "example.com", ".corp", "localhost"].
    uri: Required. Specifies the address of your proxy server. Examples:
      `http://domain` Do not provide credentials in the format
      `http://(username:password@)domain` these will be rejected by the
      server.
  """

  noProxy = _messages.StringField(1, repeated=True)
  uri = _messages.StringField(2)


class BareMetalStandaloneSecurityConfig(_messages.Message):
  r"""Specifies the security related settings for the bare metal standalone
  cluster.

  Fields:
    authorization: Configures user access to the standalone cluster.
  """

  authorization = _messages.MessageField('Authorization', 1)


class BareMetalStandaloneSrIovConfig(_messages.Message):
  r"""Specifies the SR-IOV networking operator config.

  Fields:
    enabled: Whether to install the SR-IOV operator.
  """

  enabled = _messages.BooleanField(1)


class BareMetalStandaloneStorageConfig(_messages.Message):
  r"""BareMetalStandaloneStorageConfig specifies the cluster storage
  configuration.

  Fields:
    lvpNodeMountsConfig: Required. Specifies the config for local
      PersistentVolumes backed by mounted node disks. These disks need to be
      formatted and mounted by the user, which can be done before or after
      cluster creation.
    lvpShareConfig: Required. Specifies the config for local PersistentVolumes
      backed by subdirectories in a shared filesystem. These subdirectores are
      automatically created during cluster creation.
  """

  lvpNodeMountsConfig = _messages.MessageField('BareMetalLvpConfig', 1)
  lvpShareConfig = _messages.MessageField('BareMetalLvpShareConfig', 2)


class BareMetalStandaloneVipConfig(_messages.Message):
  r"""Specifies the VIP config for the bare metal load balancer.

  Fields:
    controlPlaneVip: The VIP which you previously set aside for the Kubernetes
      API of this bare metal standalone cluster.
    ingressVip: The VIP which you previously set aside for ingress traffic
      into this bare metal standalone cluster.
  """

  controlPlaneVip = _messages.StringField(1)
  ingressVip = _messages.StringField(2)


class BareMetalStandaloneWorkloadNodeConfig(_messages.Message):
  r"""Specifies the workload node configurations.

  Fields:
    maxPodsPerNode: The maximum number of pods a node can run. The size of the
      CIDR range assigned to the node will be derived from this parameter.
  """

  maxPodsPerNode = _messages.IntegerField(1)


class BareMetalStandloneMultipleNetworkInterfacesConfig(_messages.Message):
  r"""Specifies the multiple networking interfaces cluster configuration.

  Fields:
    enabled: Whether to enable multiple network interfaces for your pods. When
      set network_config.advanced_networking is automatically set to true.
  """

  enabled = _messages.BooleanField(1)


class BareMetalStorageConfig(_messages.Message):
  r"""BareMetalStorageConfig specifies the cluster storage configuration.

  Fields:
    lvpNodeMountsConfig: Required. Specifies the config for local
      PersistentVolumes backed by mounted node disks. These disks need to be
      formatted and mounted by the user, which can be done before or after
      cluster creation.
    lvpShareConfig: Required. Specifies the config for local PersistentVolumes
      backed by subdirectories in a shared filesystem. These subdirectores are
      automatically created during cluster creation.
  """

  lvpNodeMountsConfig = _messages.MessageField('BareMetalLvpConfig', 1)
  lvpShareConfig = _messages.MessageField('BareMetalLvpShareConfig', 2)


class BareMetalVersionInfo(_messages.Message):
  r"""Contains information about a specific Anthos on bare metal version.

  Fields:
    dependencies: The list of upgrade dependencies for this version.
    hasDependencies: If set, the cluster dependencies (e.g. the admin cluster,
      other user clusters managed by the same admin cluster, version skew
      policy, etc) must be upgraded before this version can be installed or
      upgraded to.
    version: Version number e.g. 1.13.1.
  """

  dependencies = _messages.MessageField('UpgradeDependency', 1, repeated=True)
  hasDependencies = _messages.BooleanField(2)
  version = _messages.StringField(3)


class BareMetalVipConfig(_messages.Message):
  r"""Specifies the VIP config for the bare metal load balancer.

  Fields:
    controlPlaneVip: The VIP which you previously set aside for the Kubernetes
      API of this bare metal user cluster.
    ingressVip: The VIP which you previously set aside for ingress traffic
      into this bare metal user cluster.
  """

  controlPlaneVip = _messages.StringField(1)
  ingressVip = _messages.StringField(2)


class BareMetalWorkloadNodeConfig(_messages.Message):
  r"""Specifies the workload node configurations.

  Enums:
    ContainerRuntimeValueValuesEnum: Specifies which container runtime will be
      used.

  Fields:
    containerRuntime: Specifies which container runtime will be used.
    maxPodsPerNode: The maximum number of pods a node can run. The size of the
      CIDR range assigned to the node will be derived from this parameter.
  """

  class ContainerRuntimeValueValuesEnum(_messages.Enum):
    r"""Specifies which container runtime will be used.

    Values:
      CONTAINER_RUNTIME_UNSPECIFIED: No container runtime selected.
      CONTAINERD: Containerd runtime.
    """
    CONTAINER_RUNTIME_UNSPECIFIED = 0
    CONTAINERD = 1

  containerRuntime = _messages.EnumField('ContainerRuntimeValueValuesEnum', 1)
  maxPodsPerNode = _messages.IntegerField(2)


class BinaryAuthorization(_messages.Message):
  r"""Configuration for Binary Authorization.

  Enums:
    EvaluationModeValueValuesEnum: Mode of operation for binauthz policy
      evaluation. If unspecified, defaults to DISABLED.

  Fields:
    evaluationMode: Mode of operation for binauthz policy evaluation. If
      unspecified, defaults to DISABLED.
  """

  class EvaluationModeValueValuesEnum(_messages.Enum):
    r"""Mode of operation for binauthz policy evaluation. If unspecified,
    defaults to DISABLED.

    Values:
      EVALUATION_MODE_UNSPECIFIED: Default value
      DISABLED: Disable BinaryAuthorization
      PROJECT_SINGLETON_POLICY_ENFORCE: Enforce Kubernetes admission requests
        with BinaryAuthorization using the project's singleton policy.
    """
    EVALUATION_MODE_UNSPECIFIED = 0
    DISABLED = 1
    PROJECT_SINGLETON_POLICY_ENFORCE = 2

  evaluationMode = _messages.EnumField('EvaluationModeValueValuesEnum', 1)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ClusterUser(_messages.Message):
  r"""ClusterUser configures user principals for an RBAC policy.

  Fields:
    username: Required. The name of the user, e.g. `<EMAIL>`.
  """

  username = _messages.StringField(1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EnrollBareMetalAdminClusterRequest(_messages.Message):
  r"""Message for enrolling an existing bare metal admin cluster to the GKE
  on-prem API.

  Fields:
    bareMetalAdminClusterId: User provided OnePlatform identifier that is used
      as part of the resource name. This must be unique among all GKE on-prem
      clusters within a project and location and will return a 409 if the
      cluster already exists. (https://tools.ietf.org/html/rfc1123) format.
    membership: Required. This is the full resource name of this admin
      cluster's fleet membership.
  """

  bareMetalAdminClusterId = _messages.StringField(1)
  membership = _messages.StringField(2)


class EnrollBareMetalClusterRequest(_messages.Message):
  r"""Message for enrolling an existing bare metal cluster to the Anthos On-
  Prem API.

  Fields:
    adminClusterMembership: Required. The admin cluster this bare metal user
      cluster belongs to. This is the full resource name of the admin
      cluster's fleet membership. In the future, references to other resource
      types might be allowed if admin clusters are modeled as their own
      resources.
    bareMetalClusterId: User provided OnePlatform identifier that is used as
      part of the resource name. This must be unique among all bare metal
      clusters within a project and location and will return a 409 if the
      cluster already exists. (https://tools.ietf.org/html/rfc1123) format.
    localName: Optional. The object name of the bare metal cluster custom
      resource on the associated admin cluster. This field is used to support
      conflicting resource names when enrolling existing clusters to the API.
      When not provided, this field will resolve to the bare_metal_cluster_id.
      Otherwise, it must match the object name of the bare metal cluster
      custom resource. It is not modifiable outside / beyond the enrollment
      operation.
    localNamespace: Optional. The namespace of the cluster.
  """

  adminClusterMembership = _messages.StringField(1)
  bareMetalClusterId = _messages.StringField(2)
  localName = _messages.StringField(3)
  localNamespace = _messages.StringField(4)


class EnrollBareMetalNodePoolRequest(_messages.Message):
  r"""Message for enrolling an existing bare metal node pool to the GKE on-
  prem API.

  Fields:
    bareMetalNodePoolId: User provided OnePlatform identifier that is used as
      part of the resource name. (https://tools.ietf.org/html/rfc1123) format.
    validateOnly: If set, only validate the request, but do not actually
      enroll the node pool.
  """

  bareMetalNodePoolId = _messages.StringField(1)
  validateOnly = _messages.BooleanField(2)


class EnrollBareMetalStandaloneClusterRequest(_messages.Message):
  r"""Message for enrolling an existing bare metal standalone cluster to the
  GKE on-prem API.

  Fields:
    bareMetalStandaloneClusterId: User provided OnePlatform identifier that is
      used as part of the resource name. This must be unique among all GKE on-
      prem clusters within a project and location and will return a 409 if the
      cluster already exists. (https://tools.ietf.org/html/rfc1123) format.
    membership: Required. This is the full resource name of this bare metal
      standalone cluster's fleet membership.
  """

  bareMetalStandaloneClusterId = _messages.StringField(1)
  membership = _messages.StringField(2)


class EnrollBareMetalStandaloneNodePoolRequest(_messages.Message):
  r"""Message for enrolling an existing bare metal standalone node pool to the
  GKE on-prem API.

  Fields:
    bareMetalStandaloneNodePoolId: User provided OnePlatform identifier that
      is used as part of the resource name.
      (https://tools.ietf.org/html/rfc1123) format.
    validateOnly: If set, only validate the request, but do not actually
      enroll the node pool.
  """

  bareMetalStandaloneNodePoolId = _messages.StringField(1)
  validateOnly = _messages.BooleanField(2)


class EnrollVmwareAdminClusterRequest(_messages.Message):
  r"""Message for enrolling an existing VMware admin cluster to the GKE on-
  prem API.

  Fields:
    membership: Required. This is the full resource name of this admin
      cluster's fleet membership.
    vmwareAdminClusterId: User provided OnePlatform identifier that is used as
      part of the resource name. This must be unique among all GKE on-prem
      clusters within a project and location and will return a 409 if the
      cluster already exists. (https://tools.ietf.org/html/rfc1123) format.
  """

  membership = _messages.StringField(1)
  vmwareAdminClusterId = _messages.StringField(2)


class EnrollVmwareClusterRequest(_messages.Message):
  r"""Message for enrolling an existing VMware cluster to the Anthos On-Prem
  API.

  Fields:
    adminClusterMembership: Required. The admin cluster this VMware user
      cluster belongs to. This is the full resource name of the admin
      cluster's fleet membership. In the future, references to other resource
      types might be allowed if admin clusters are modeled as their own
      resources.
    localName: Optional. The object name of the VMware OnPremUserCluster
      custom resource on the associated admin cluster. This field is used to
      support conflicting resource names when enrolling existing clusters to
      the API. When not provided, this field will resolve to the
      vmware_cluster_id. Otherwise, it must match the object name of the
      VMware OnPremUserCluster custom resource. It is not modifiable outside /
      beyond the enrollment operation.
    validateOnly: Validate the request without actually doing any updates.
    vmwareClusterId: User provided OnePlatform identifier that is used as part
      of the resource name. This must be unique among all GKE on-prem clusters
      within a project and location and will return a 409 if the cluster
      already exists. (https://tools.ietf.org/html/rfc1123) format.
  """

  adminClusterMembership = _messages.StringField(1)
  localName = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)
  vmwareClusterId = _messages.StringField(4)


class EnrollVmwareNodePoolRequest(_messages.Message):
  r"""Message for enrolling a VMware node pool.

  Fields:
    vmwareNodePoolId: The target node pool id to be enrolled.
  """

  vmwareNodePoolId = _messages.StringField(1)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Fleet(_messages.Message):
  r"""Fleet related configuration. Fleets are a Google Cloud concept for
  logically organizing clusters, letting you use and manage multi-cluster
  capabilities and apply consistent policies across your systems. See [Anthos
  Fleets](`https://cloud.google.com/anthos/multicluster-management/fleets`)
  for more details on Anthos multi-cluster capabilities using Fleets. ##

  Fields:
    membership: Output only. The name of the managed fleet Membership resource
      associated to this cluster. Membership names are formatted as
      `projects//locations//memberships/`.
  """

  membership = _messages.StringField(1)


class GkeonpremProjectsLocationsBareMetalAdminClustersCreateRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalAdminClustersCreateRequest object.

  Fields:
    allowPreflightFailure: Optional. If set to true, CLM will force CCFE to
      persist the cluster resource in RMS when the creation fails during
      standalone preflight checks. In that case the subsequent create call
      will fail with "cluster already exists" error and hence a update cluster
      is required to fix the cluster.
    bareMetalAdminCluster: A BareMetalAdminCluster resource to be passed as
      the request body.
    bareMetalAdminClusterId: Required. User provided identifier that is used
      as part of the resource name; must conform to RFC-1034 and additionally
      restrict to lower-cased letters. This comes out roughly to:
      /^a-z+[a-z0-9]$/
    parent: Required. The parent of the project and location where the cluster
      is created in. Format: "projects/{project}/locations/{location}"
    validateOnly: Validate the request without actually doing any updates.
  """

  allowPreflightFailure = _messages.BooleanField(1)
  bareMetalAdminCluster = _messages.MessageField('BareMetalAdminCluster', 2)
  bareMetalAdminClusterId = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  validateOnly = _messages.BooleanField(5)


class GkeonpremProjectsLocationsBareMetalAdminClustersEnrollRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalAdminClustersEnrollRequest object.

  Fields:
    enrollBareMetalAdminClusterRequest: A EnrollBareMetalAdminClusterRequest
      resource to be passed as the request body.
    parent: Required. The parent of the project and location where the cluster
      is enrolled in. Format: "projects/{project}/locations/{location}"
  """

  enrollBareMetalAdminClusterRequest = _messages.MessageField('EnrollBareMetalAdminClusterRequest', 1)
  parent = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsBareMetalAdminClustersGetIamPolicyRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalAdminClustersGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsBareMetalAdminClustersGetRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalAdminClustersGetRequest object.

  Enums:
    ViewValueValuesEnum: View for bare metal admin cluster. When `BASIC` is
      specified, only the cluster resource name and membership are returned.
      The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as
      `FULL', which returns the complete cluster configuration details.

  Fields:
    allowMissing: Optional. If true, return BareMetal Admin Cluster including
      the one that only exists in RMS.
    name: Required. Name of the bare metal admin cluster to get. Format: "proj
      ects/{project}/locations/{location}/bareMetalAdminClusters/{bare_metal_a
      dmin_cluster}"
    view: View for bare metal admin cluster. When `BASIC` is specified, only
      the cluster resource name and membership are returned. The default/unset
      value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL', which returns
      the complete cluster configuration details.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""View for bare metal admin cluster. When `BASIC` is specified, only the
    cluster resource name and membership are returned. The default/unset value
    `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL', which returns the
    complete cluster configuration details.

    Values:
      CLUSTER_VIEW_UNSPECIFIED: If the value is not set, the default `FULL`
        view is used.
      BASIC: Includes basic information of a cluster resource including
        cluster resource name and membership.
      FULL: Includes the complete configuration for bare metal admin cluster
        resource. This is the default value for
        GetBareMetalAdminClusterRequest method.
    """
    CLUSTER_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 3)


class GkeonpremProjectsLocationsBareMetalAdminClustersListRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalAdminClustersListRequest object.

  Enums:
    ViewValueValuesEnum: View for bare metal admin clusters. When `BASIC` is
      specified, only the admin cluster resource name and membership are
      returned. The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same
      as `FULL', which returns the complete admin cluster configuration
      details.

  Fields:
    allowMissing: Optional. If true, return list of BareMetal Admin Clusters
      including the ones that only exists in RMS.
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, at most 50 clusters will be returned. The
      maximum value is 1000; values above 1000 will be coerced to 1000.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. The parent of the project and location where the
      clusters are listed in. Format:
      "projects/{project}/locations/{location}"
    view: View for bare metal admin clusters. When `BASIC` is specified, only
      the admin cluster resource name and membership are returned. The
      default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL',
      which returns the complete admin cluster configuration details.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""View for bare metal admin clusters. When `BASIC` is specified, only
    the admin cluster resource name and membership are returned. The
    default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL',
    which returns the complete admin cluster configuration details.

    Values:
      CLUSTER_VIEW_UNSPECIFIED: If the value is not set, the default `FULL`
        view is used.
      BASIC: Includes basic information of a admin cluster resource including
        admin cluster resource name and membership.
      FULL: Includes the complete configuration for bare metal admin cluster
        resource. This is the default value for
        ListBareMetalAdminClustersRequest method.
    """
    CLUSTER_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  allowMissing = _messages.BooleanField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class GkeonpremProjectsLocationsBareMetalAdminClustersOperationsGetRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalAdminClustersOperationsGetRequest
  object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GkeonpremProjectsLocationsBareMetalAdminClustersOperationsListRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalAdminClustersOperationsListRequest
  object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GkeonpremProjectsLocationsBareMetalAdminClustersPatchRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalAdminClustersPatchRequest object.

  Fields:
    bareMetalAdminCluster: A BareMetalAdminCluster resource to be passed as
      the request body.
    name: Immutable. The bare metal admin cluster resource name.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the BareMetalAdminCluster resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all populated fields in the
      BareMetalAdminCluster message will be updated. Empty fields will be
      ignored unless a field mask is used.
    validateOnly: Validate the request without actually doing any updates.
  """

  bareMetalAdminCluster = _messages.MessageField('BareMetalAdminCluster', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class GkeonpremProjectsLocationsBareMetalAdminClustersQueryVersionConfigRequest(_messages.Message):
  r"""A
  GkeonpremProjectsLocationsBareMetalAdminClustersQueryVersionConfigRequest
  object.

  Fields:
    parent: Required. The parent of the project and location to query for
      version config. Format: "projects/{project}/locations/{location}"
    upgradeConfig_clusterName: The admin cluster resource name. This is the
      full resource name of the admin cluster resource. Format: "projects/{pro
      ject}/locations/{location}/bareMetalAdminClusters/{bare_metal_admin_clus
      ter}"
  """

  parent = _messages.StringField(1, required=True)
  upgradeConfig_clusterName = _messages.StringField(2)


class GkeonpremProjectsLocationsBareMetalAdminClustersSetIamPolicyRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalAdminClustersSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkeonpremProjectsLocationsBareMetalAdminClustersTestIamPermissionsRequest(_messages.Message):
  r"""A
  GkeonpremProjectsLocationsBareMetalAdminClustersTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkeonpremProjectsLocationsBareMetalAdminClustersUnenrollRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalAdminClustersUnenrollRequest
  object.

  Fields:
    allowMissing: If set to true, and the bare metal admin cluster is not
      found, the request will succeed but no action will be taken on the
      server and return a completed LRO.
    etag: The current etag of the bare metal admin cluster. If an etag is
      provided and does not match the current etag of the cluster, deletion
      will be blocked and an ABORTED error will be returned.
    ignoreErrors: If set to true, the unenrollment of a bare metal admin
      cluster resource will succeed even if errors occur during unenrollment.
      This parameter can be used when you want to unenroll admin cluster
      resource and the on-prem admin cluster is disconnected / unreachable.
      WARNING: Using this parameter when your admin cluster still exists may
      result in a deleted GCP admin cluster but existing resourcelink in on-
      prem admin cluster and membership.
    name: Required. Name of the bare metal admin cluster to be unenrolled.
      Format: "projects/{project}/locations/{location}/bareMetalAdminClusters/
      {cluster}"
    validateOnly: Validate the request without actually doing any updates.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  ignoreErrors = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  validateOnly = _messages.BooleanField(5)


class GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsCreateRequest(_messages.Message):
  r"""A
  GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsCreateRequest
  object.

  Fields:
    bareMetalNodePool: A BareMetalNodePool resource to be passed as the
      request body.
    bareMetalNodePoolId: The ID to use for the node pool, which will become
      the final component of the node pool's resource name. This value must be
      up to 63 characters, and valid characters are /a-z-/. The value must not
      be permitted to be a UUID (or UUID-like: anything matching
      /^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$/i).
    parent: Required. The parent resource where this node pool will be
      created.
      projects/{project}/locations/{location}/bareMetalClusters/{cluster}
    validateOnly: If set, only validate the request, but do not actually
      create the node pool.
  """

  bareMetalNodePool = _messages.MessageField('BareMetalNodePool', 1)
  bareMetalNodePoolId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsDeleteRequest(_messages.Message):
  r"""A
  GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsDeleteRequest
  object.

  Fields:
    allowMissing: If set to true, and the bare metal node pool is not found,
      the request will succeed but no action will be taken on the server and
      return a completed LRO.
    etag: The current etag of the BareMetalNodePool. If an etag is provided
      and does not match the current etag of the node pool, deletion will be
      blocked and an ABORTED error will be returned.
    ignoreErrors: If set to true, the deletion of a bare metal node pool
      resource will succeed even if errors occur during deletion. This
      parameter can be used when you want to delete GCP's node pool resource
      and you've already deleted the on-prem admin cluster that hosted your
      node pool. WARNING: Using this parameter when your user cluster still
      exists may result in a deleted GCP node pool but an existing on-prem
      node pool.
    name: Required. The name of the node pool to delete. Format: projects/{pro
      ject}/locations/{location}/bareMetalClusters/{cluster}/bareMetalNodePool
      s/{nodepool}
    validateOnly: If set, only validate the request, but do not actually
      delete the node pool.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  ignoreErrors = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  validateOnly = _messages.BooleanField(5)


class GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsEnrollRequest(_messages.Message):
  r"""A
  GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsEnrollRequest
  object.

  Fields:
    enrollBareMetalNodePoolRequest: A EnrollBareMetalNodePoolRequest resource
      to be passed as the request body.
    parent: Required. The parent resource where this node pool will be
      created.
      projects/{project}/locations/{location}/bareMetalClusters/{cluster}
  """

  enrollBareMetalNodePoolRequest = _messages.MessageField('EnrollBareMetalNodePoolRequest', 1)
  parent = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsGetIamPolicyRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsGetIamPol
  icyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsGetRequest(_messages.Message):
  r"""A
  GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsGetRequest
  object.

  Enums:
    ViewValueValuesEnum: View for bare metal node pool. When `BASIC` is
      specified, only the node pool resource name is returned. The
      default/unset value `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL',
      which returns the complete node pool configuration details.

  Fields:
    name: Required. The name of the node pool to retrieve. projects/{project}/
      locations/{location}/bareMetalClusters/{cluster}/bareMetalNodePools/{nod
      epool}
    view: View for bare metal node pool. When `BASIC` is specified, only the
      node pool resource name is returned. The default/unset value
      `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL', which returns the
      complete node pool configuration details.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""View for bare metal node pool. When `BASIC` is specified, only the
    node pool resource name is returned. The default/unset value
    `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL', which returns the
    complete node pool configuration details.

    Values:
      NODE_POOL_VIEW_UNSPECIFIED: If the value is not set, the default `FULL`
        view is used.
      BASIC: Includes basic information of a node pool resource including node
        pool resource name.
      FULL: Includes the complete configuration for bare metal node pool
        resource. This is the default value for GetBareMetalNodePoolRequest
        method.
    """
    NODE_POOL_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsListRequest(_messages.Message):
  r"""A
  GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsListRequest
  object.

  Enums:
    ViewValueValuesEnum: View for bare metal node pools. When `BASIC` is
      specified, only the node pool resource name is returned. The
      default/unset value `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL',
      which returns the complete node pool configuration details.

  Fields:
    pageSize: The maximum number of node pools to return. The service may
      return fewer than this value. If unspecified, at most 50 node pools will
      be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: A page token, received from a previous `ListBareMetalNodePools`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListBareMetalNodePools` must match the
      call that provided the page token.
    parent: Required. The parent, which owns this collection of node pools.
      Format: projects/{project}/locations/{location}/bareMetalClusters/{bareM
      etalCluster}
    view: View for bare metal node pools. When `BASIC` is specified, only the
      node pool resource name is returned. The default/unset value
      `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL', which returns the
      complete node pool configuration details.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""View for bare metal node pools. When `BASIC` is specified, only the
    node pool resource name is returned. The default/unset value
    `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL', which returns the
    complete node pool configuration details.

    Values:
      NODE_POOL_VIEW_UNSPECIFIED: If the value is not set, the default `FULL`
        view is used.
      BASIC: Includes basic information of a node pool resource including node
        pool resource name.
      FULL: Includes the complete configuration for bare metal node pool
        resource. This is the default value for ListBareMetalNodePoolsRequest
        method.
    """
    NODE_POOL_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsOperationsGetRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsOperation
  sGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsOperationsListRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsOperation
  sListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsPatchRequest(_messages.Message):
  r"""A
  GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsPatchRequest
  object.

  Fields:
    allowMissing: If set to true, and the bare metal node pool is not found,
      the request will create a new bare metal node pool with the provided
      configuration. The user must have both create and update permission to
      call Update with allow_missing set to true.
    bareMetalNodePool: A BareMetalNodePool resource to be passed as the
      request body.
    name: Immutable. The bare metal node pool resource name.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the BareMetalNodePool resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all populated fields in the
      BareMetalNodePool message will be updated. Empty fields will be ignored
      unless a field mask is used.
    validateOnly: Validate the request without actually doing any updates.
  """

  allowMissing = _messages.BooleanField(1)
  bareMetalNodePool = _messages.MessageField('BareMetalNodePool', 2)
  name = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsSetIamPolicyRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsSetIamPol
  icyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsTestIamPermissionsRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsTestIamPe
  rmissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsUnenrollRequest(_messages.Message):
  r"""A
  GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsUnenrollRequest
  object.

  Fields:
    allowMissing: If set to true, and the bare metal node pool is not found,
      the request will succeed but no action will be taken on the server and
      return a completed LRO.
    etag: The current etag of the bare metal node pool. If an etag is provided
      and does not match the current etag of node pool, deletion will be
      blocked and an ABORTED error will be returned.
    name: Required. The name of the node pool to unenroll. Format: projects/{p
      roject}/locations/{location}/bareMetalClusters/{cluster}/bareMetalNodePo
      ols/{nodepool}
    validateOnly: If set, only validate the request, but do not actually
      unenroll the node pool.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class GkeonpremProjectsLocationsBareMetalClustersCreateRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersCreateRequest object.

  Fields:
    allowPreflightFailure: Optional. If set to true, CLM will force CCFE to
      persist the cluster resource in RMS when the creation fails during
      standalone preflight checks. In that case the subsequent create call
      will fail with "cluster already exists" error and hence a update cluster
      is required to fix the cluster.
    bareMetalCluster: A BareMetalCluster resource to be passed as the request
      body.
    bareMetalClusterId: Required. User provided identifier that is used as
      part of the resource name; must conform to RFC-1034 and additionally
      restrict to lower-cased letters. This comes out roughly to:
      /^a-z+[a-z0-9]$/
    parent: Required. The parent of the project and location where the cluster
      is created in. Format: "projects/{project}/locations/{location}"
    validateOnly: Validate the request without actually doing any updates.
  """

  allowPreflightFailure = _messages.BooleanField(1)
  bareMetalCluster = _messages.MessageField('BareMetalCluster', 2)
  bareMetalClusterId = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  validateOnly = _messages.BooleanField(5)


class GkeonpremProjectsLocationsBareMetalClustersDeleteRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersDeleteRequest object.

  Fields:
    allowMissing: If set to true, and the bare metal cluster is not found, the
      request will succeed but no action will be taken on the server and
      return a completed LRO.
    etag: The current etag of the bare metal Cluster. If an etag is provided
      and does not match the current etag of the cluster, deletion will be
      blocked and an ABORTED error will be returned.
    force: If set to true, any node pools from the cluster will also be
      deleted.
    ignoreErrors: If set to true, the deletion of a bare metal user cluster
      resource will succeed even if errors occur during deletion. This
      parameter can be used when you want to delete GCP's cluster resource and
      the on-prem admin cluster that hosts your user cluster is disconnected /
      unreachable or deleted. WARNING: Using this parameter when your user
      cluster still exists may result in a deleted GCP user cluster but an
      existing on-prem user cluster.
    name: Required. Name of the bare metal user cluster to be deleted. Format:
      "projects/{project}/locations/{location}/bareMetalClusters/{bare_metal_c
      luster}"
    validateOnly: Validate the request without actually doing any updates.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  force = _messages.BooleanField(3)
  ignoreErrors = _messages.BooleanField(4)
  name = _messages.StringField(5, required=True)
  validateOnly = _messages.BooleanField(6)


class GkeonpremProjectsLocationsBareMetalClustersEnrollRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersEnrollRequest object.

  Fields:
    enrollBareMetalClusterRequest: A EnrollBareMetalClusterRequest resource to
      be passed as the request body.
    parent: Required. The parent of the project and location where the cluster
      is enrolled in. Format: "projects/{project}/locations/{location}"
  """

  enrollBareMetalClusterRequest = _messages.MessageField('EnrollBareMetalClusterRequest', 1)
  parent = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsBareMetalClustersGetIamPolicyRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsBareMetalClustersGetRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersGetRequest object.

  Enums:
    ViewValueValuesEnum: View for bare metal user cluster. When `BASIC` is
      specified, only the cluster resource name and admin cluster membership
      are returned. The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the
      same as `FULL', which returns the complete cluster configuration
      details.

  Fields:
    allowMissing: Optional. If true, return BareMetal Cluster including the
      one that only exists in RMS.
    name: Required. Name of the bare metal user cluster to get. Format: "proje
      cts/{project}/locations/{location}/bareMetalClusters/{bare_metal_cluster
      }"
    view: View for bare metal user cluster. When `BASIC` is specified, only
      the cluster resource name and admin cluster membership are returned. The
      default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL',
      which returns the complete cluster configuration details.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""View for bare metal user cluster. When `BASIC` is specified, only the
    cluster resource name and admin cluster membership are returned. The
    default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL',
    which returns the complete cluster configuration details.

    Values:
      CLUSTER_VIEW_UNSPECIFIED: If the value is not set, the default `FULL`
        view is used.
      BASIC: Includes basic information of a cluster resource including
        cluster resource name and admin cluster membership.
      FULL: Includes the complete configuration for bare metal cluster
        resource. This is the default value for GetBareMetalClusterRequest
        method.
    """
    CLUSTER_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 3)


class GkeonpremProjectsLocationsBareMetalClustersListRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersListRequest object.

  Enums:
    ViewValueValuesEnum: View for bare metal Clusters. When `BASIC` is
      specified, only the cluster resource name and admin cluster membership
      are returned. The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the
      same as `FULL', which returns the complete cluster configuration
      details.

  Fields:
    allowMissing: Optional. If true, return list of BareMetal Clusters
      including the ones that only exists in RMS.
    filter: A resource filtering expression following
      https://google.aip.dev/160. When non-empty, only resource's whose
      attributes field matches the filter are returned.
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, at most 50 clusters will be returned. The
      maximum value is 1000; values above 1000 will be coerced to 1000.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. The parent of the project and location where the
      clusters are listed in. Format:
      "projects/{project}/locations/{location}"
    view: View for bare metal Clusters. When `BASIC` is specified, only the
      cluster resource name and admin cluster membership are returned. The
      default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL',
      which returns the complete cluster configuration details.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""View for bare metal Clusters. When `BASIC` is specified, only the
    cluster resource name and admin cluster membership are returned. The
    default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL',
    which returns the complete cluster configuration details.

    Values:
      CLUSTER_VIEW_UNSPECIFIED: If the value is not set, the default `FULL`
        view is used.
      BASIC: Includes basic information of a cluster resource including
        cluster resource name and admin cluster membership.
      FULL: Includes the complete configuration for bare metal cluster
        resource. This is the default value for ListBareMetalClustersRequest
        method.
    """
    CLUSTER_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  allowMissing = _messages.BooleanField(1)
  filter = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 6)


class GkeonpremProjectsLocationsBareMetalClustersOperationsGetRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersOperationsGetRequest
  object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GkeonpremProjectsLocationsBareMetalClustersOperationsListRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersOperationsListRequest
  object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GkeonpremProjectsLocationsBareMetalClustersPatchRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersPatchRequest object.

  Fields:
    allowMissing: If set to true, and the bare metal cluster is not found, the
      request will create a new bare metal cluster with the provided
      configuration. The user must have both create and update permission to
      call Update with allow_missing set to true.
    bareMetalCluster: A BareMetalCluster resource to be passed as the request
      body.
    name: Immutable. The bare metal user cluster resource name.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the BareMetalCluster resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all populated fields in the
      BareMetalCluster message will be updated. Empty fields will be ignored
      unless a field mask is used.
    validateOnly: Validate the request without actually doing any updates.
  """

  allowMissing = _messages.BooleanField(1)
  bareMetalCluster = _messages.MessageField('BareMetalCluster', 2)
  name = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class GkeonpremProjectsLocationsBareMetalClustersQueryVersionConfigRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersQueryVersionConfigRequest
  object.

  Fields:
    createConfig_adminClusterMembership: The admin cluster membership. This is
      the full resource name of the admin cluster's fleet membership. Format:
      "projects/{project}/locations/{location}/memberships/{membership}"
    createConfig_adminClusterName: The admin cluster resource name. This is
      the full resource name of the admin cluster resource. Format: "projects/
      {project}/locations/{location}/bareMetalAdminClusters/{bare_metal_admin_
      cluster}"
    parent: Required. The parent of the project and location to query for
      version config. Format: "projects/{project}/locations/{location}"
    upgradeConfig_clusterName: The user cluster resource name. This is the
      full resource name of the user cluster resource. Format: "projects/{proj
      ect}/locations/{location}/bareMetalClusters/{bare_metal_cluster}"
  """

  createConfig_adminClusterMembership = _messages.StringField(1)
  createConfig_adminClusterName = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  upgradeConfig_clusterName = _messages.StringField(4)


class GkeonpremProjectsLocationsBareMetalClustersSetIamPolicyRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkeonpremProjectsLocationsBareMetalClustersTestIamPermissionsRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkeonpremProjectsLocationsBareMetalClustersUnenrollRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalClustersUnenrollRequest object.

  Fields:
    allowMissing: If set to true, and the bare metal cluster is not found, the
      request will succeed but no action will be taken on the server and
      return a completed LRO.
    etag: The current etag of the bare metal Cluster. If an etag is provided
      and does not match the current etag of the cluster, deletion will be
      blocked and an ABORTED error will be returned.
    force: This is required if the cluster has any associated node pools. When
      set, any child node pools will also be unenrolled.
    name: Required. Name of the bare metal user cluster to be unenrolled.
      Format:
      "projects/{project}/locations/{location}/bareMetalClusters/{cluster}"
    validateOnly: Validate the request without actually doing any updates.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  force = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  validateOnly = _messages.BooleanField(5)


class GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsCreateRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandalo
  neNodePoolsCreateRequest object.

  Fields:
    bareMetalStandaloneNodePool: A BareMetalStandaloneNodePool resource to be
      passed as the request body.
    bareMetalStandaloneNodePoolId: The ID to use for the node pool, which will
      become the final component of the node pool's resource name. This value
      must be up to 63 characters, and valid characters are /a-z-/. The value
      must not be permitted to be a UUID (or UUID-like: anything matching
      /^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$/i).
    parent: Required. The parent resource where this node pool will be
      created. projects/{project}/locations/{location}/bareMetalStandaloneClus
      ters/{cluster}
    validateOnly: If set, only validate the request, but do not actually
      create the node pool.
  """

  bareMetalStandaloneNodePool = _messages.MessageField('BareMetalStandaloneNodePool', 1)
  bareMetalStandaloneNodePoolId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsDeleteRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandalo
  neNodePoolsDeleteRequest object.

  Fields:
    allowMissing: If set to true, and the bare metal standalone node pool is
      not found, the request will succeed but no action will be taken on the
      server and return a completed LRO.
    etag: The current etag of the BareMetalStandaloneNodePool. If an etag is
      provided and does not match the current etag of the node pool, deletion
      will be blocked and an ABORTED error will be returned.
    ignoreErrors: If set to true, the deletion of a bare metal standalone node
      pool resource will succeed even if errors occur during deletion. This
      parameter can be used when you want to delete GCP's node pool resource
      and you've already deleted the on-prem admin cluster that hosted your
      node pool. WARNING: Using this parameter when your user cluster still
      exists may result in a deleted GCP node pool but an existing on-prem
      node pool.
    name: Required. The name of the node pool to delete. Format: projects/{pro
      ject}/locations/{location}/bareMetalStandaloneClusters/{cluster}/bareMet
      alStandaloneNodePools/{nodepool}
    validateOnly: If set, only validate the request, but do not actually
      delete the node pool.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  ignoreErrors = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  validateOnly = _messages.BooleanField(5)


class GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsEnrollRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandalo
  neNodePoolsEnrollRequest object.

  Fields:
    enrollBareMetalStandaloneNodePoolRequest: A
      EnrollBareMetalStandaloneNodePoolRequest resource to be passed as the
      request body.
    parent: Required. The parent resource where this node pool will be
      created. projects/{project}/locations/{location}/bareMetalStandaloneClus
      ters/{cluster}
  """

  enrollBareMetalStandaloneNodePoolRequest = _messages.MessageField('EnrollBareMetalStandaloneNodePoolRequest', 1)
  parent = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsGetRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandalo
  neNodePoolsGetRequest object.

  Fields:
    name: Required. The name of the bare metal standalone node pool to
      retrieve. projects/{project}/locations/{location}/bareMetalStandaloneClu
      sters/{cluster}/bareMetalStandaloneNodePools/{nodepool}
  """

  name = _messages.StringField(1, required=True)


class GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsListRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandalo
  neNodePoolsListRequest object.

  Fields:
    pageSize: The maximum number of node pools to return. The service may
      return fewer than this value. If unspecified, at most 50 node pools will
      be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: A page token, received from a previous
      `ListBareMetalStandaloneNodePools` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListBareMetaStandaloneNodePools` must match the call that provided the
      page token.
    parent: Required. The parent, which owns this collection of node pools.
      Format: projects/{project}/locations/{location}/bareMetalStandaloneClust
      ers/{bareMetalStandaloneCluster}
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsPatchRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandalo
  neNodePoolsPatchRequest object.

  Fields:
    allowMissing: If set to true, and the bare metal standalone node pool is
      not found, the request will create a new bare metal standalone node pool
      with the provided configuration. The user must have both create and
      update permission to call Update with allow_missing set to true.
    bareMetalStandaloneNodePool: A BareMetalStandaloneNodePool resource to be
      passed as the request body.
    name: Immutable. The bare metal standalone node pool resource name.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the BareMetalStandaloneNodePool resource by the update.
      The fields specified in the update_mask are relative to the resource,
      not the full request. A field will be overwritten if it is in the mask.
    validateOnly: Validate the request without actually doing any updates.
  """

  allowMissing = _messages.BooleanField(1)
  bareMetalStandaloneNodePool = _messages.MessageField('BareMetalStandaloneNodePool', 2)
  name = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsUnenrollRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandalo
  neNodePoolsUnenrollRequest object.

  Fields:
    allowMissing: If set to true, and the bare metal standalone node pool is
      not found, the request will succeed but no action will be taken on the
      server and return a completed LRO.
    etag: The current etag of the bare metal standalone node pool. If an etag
      is provided and does not match the current etag of node pool, deletion
      will be blocked and an ABORTED error will be returned.
    name: Required. The name of the node pool to unenroll. Format: projects/{p
      roject}/locations/{location}/bareMetalStandaloneClusters/{cluster}/bareM
      etalStandaloneNodePools/{nodepool}
    validateOnly: If set, only validate the request, but do not actually
      unenroll the node pool.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class GkeonpremProjectsLocationsBareMetalStandaloneClustersEnrollRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalStandaloneClustersEnrollRequest
  object.

  Fields:
    enrollBareMetalStandaloneClusterRequest: A
      EnrollBareMetalStandaloneClusterRequest resource to be passed as the
      request body.
    parent: Required. The parent of the project and location where the cluster
      is enrolled in. Format: "projects/{project}/locations/{location}"
  """

  enrollBareMetalStandaloneClusterRequest = _messages.MessageField('EnrollBareMetalStandaloneClusterRequest', 1)
  parent = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsBareMetalStandaloneClustersGetRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalStandaloneClustersGetRequest
  object.

  Fields:
    name: Required. Name of the bare metal standalone cluster to get. Format:
      "projects/{project}/locations/{location}/bareMetalStandaloneClusters/{ba
      re_metal_standalone_cluster}"
  """

  name = _messages.StringField(1, required=True)


class GkeonpremProjectsLocationsBareMetalStandaloneClustersListRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalStandaloneClustersListRequest
  object.

  Fields:
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, at most 50 clusters will be returned. The
      maximum value is 1000; values above 1000 will be coerced to 1000.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. The parent of the project and location where the
      clusters are listed in. Format:
      "projects/{project}/locations/{location}"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkeonpremProjectsLocationsBareMetalStandaloneClustersPatchRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalStandaloneClustersPatchRequest
  object.

  Fields:
    allowMissing: If set to true, and the bare metal standalone cluster is not
      found, the request will create a new bare metal standalone cluster with
      the provided configuration. The user must have both create and update
      permission to call Update with allow_missing set to true.
    bareMetalStandaloneCluster: A BareMetalStandaloneCluster resource to be
      passed as the request body.
    name: Immutable. The bare metal standalone cluster resource name.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the BareMetalStandaloneCluster resource by the update.
      The fields specified in the update_mask are relative to the resource,
      not the full request. A field will be overwritten if it is in the mask.
      If the user does not provide a mask then all populated fields in the
      BareMetalStandaloneCluster message will be updated. Empty fields will be
      ignored unless a field mask is used.
    validateOnly: Validate the request without actually doing any updates.
  """

  allowMissing = _messages.BooleanField(1)
  bareMetalStandaloneCluster = _messages.MessageField('BareMetalStandaloneCluster', 2)
  name = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class GkeonpremProjectsLocationsBareMetalStandaloneClustersQueryVersionConfigRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalStandaloneClustersQueryVersionConfi
  gRequest object.

  Fields:
    parent: Required. The parent of the project and location to query for
      version config. Format: "projects/{project}/locations/{location}"
    upgradeConfig_clusterName: The standalone cluster resource name. This is
      the full resource name of the standalone cluster resource. Format: "proj
      ects/{project}/locations/{location}/bareMetalStandaloneClusters/{bare_me
      tal_standalone_cluster}"
  """

  parent = _messages.StringField(1, required=True)
  upgradeConfig_clusterName = _messages.StringField(2)


class GkeonpremProjectsLocationsBareMetalStandaloneClustersUnenrollRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsBareMetalStandaloneClustersUnenrollRequest
  object.

  Fields:
    allowMissing: If set to true, and the bare metal standalone cluster is not
      found, the request will succeed but no action will be taken on the
      server and return a completed LRO.
    etag: The current etag of the bare metal standalone cluster. If an etag is
      provided and does not match the current etag of the cluster, deletion
      will be blocked and an ABORTED error will be returned.
    force: This is required if the cluster has any associated node pools. When
      set, any child node pools will also be unenrolled.
    ignoreErrors: Optional. If set to true, the unenrollment of a bare metal
      standalone cluster resource will succeed even if errors occur during
      unenrollment. This parameter can be used when you want to unenroll
      standalone cluster resource and the on-prem standalone cluster is
      disconnected / unreachable. WARNING: Using this parameter when your
      standalone cluster still exists may result in a deleted GCP standalone
      cluster but existing resourcelink in on-prem standalone cluster and
      membership.
    name: Required. Name of the bare metal standalone cluster to be
      unenrolled. Format: "projects/{project}/locations/{location}/bareMetalSt
      andaloneClusters/{cluster}"
    validateOnly: Validate the request without actually doing any updates.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  force = _messages.BooleanField(3)
  ignoreErrors = _messages.BooleanField(4)
  name = _messages.StringField(5, required=True)
  validateOnly = _messages.BooleanField(6)


class GkeonpremProjectsLocationsGetRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class GkeonpremProjectsLocationsListRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class GkeonpremProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class GkeonpremProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GkeonpremProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GkeonpremProjectsLocationsVmwareAdminClustersCreateRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareAdminClustersCreateRequest object.

  Fields:
    allowPreflightFailure: Optional. If set to true, CLM will force CCFE to
      persist the cluster resource in RMS when the creation fails during
      standalone preflight checks. In that case the subsequent create call
      will fail with "cluster already exists" error and hence a update cluster
      is required to fix the cluster.
    parent: Required. The parent of the project and location where the cluster
      is created in. Format: "projects/{project}/locations/{location}"
    validateOnly: Validate the request without actually doing any updates.
    vmwareAdminCluster: A VmwareAdminCluster resource to be passed as the
      request body.
    vmwareAdminClusterId: Required. User provided identifier that is used as
      part of the resource name; must conform to RFC-1034 and additionally
      restrict to lower-cased letters. This comes out roughly to:
      /^a-z+[a-z0-9]$/
  """

  allowPreflightFailure = _messages.BooleanField(1)
  parent = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)
  vmwareAdminCluster = _messages.MessageField('VmwareAdminCluster', 4)
  vmwareAdminClusterId = _messages.StringField(5)


class GkeonpremProjectsLocationsVmwareAdminClustersEnrollRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareAdminClustersEnrollRequest object.

  Fields:
    enrollVmwareAdminClusterRequest: A EnrollVmwareAdminClusterRequest
      resource to be passed as the request body.
    parent: Required. The parent of the project and location where the cluster
      is enrolled in. Format: "projects/{project}/locations/{location}"
  """

  enrollVmwareAdminClusterRequest = _messages.MessageField('EnrollVmwareAdminClusterRequest', 1)
  parent = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsVmwareAdminClustersGetIamPolicyRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareAdminClustersGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsVmwareAdminClustersGetRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareAdminClustersGetRequest object.

  Enums:
    ViewValueValuesEnum: View for VMware admin cluster. When `BASIC` is
      specified, only the cluster resource name and membership are returned.
      The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as
      `FULL', which returns the complete cluster configuration details.

  Fields:
    allowMissing: Optional. If true, return Vmware Admin Cluster including the
      one that only exists in RMS.
    name: Required. Name of the VMware admin cluster to be returned. Format: "
      projects/{project}/locations/{location}/vmwareAdminClusters/{vmware_admi
      n_cluster}"
    view: View for VMware admin cluster. When `BASIC` is specified, only the
      cluster resource name and membership are returned. The default/unset
      value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL', which returns
      the complete cluster configuration details.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""View for VMware admin cluster. When `BASIC` is specified, only the
    cluster resource name and membership are returned. The default/unset value
    `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL', which returns the
    complete cluster configuration details.

    Values:
      CLUSTER_VIEW_UNSPECIFIED: If the value is not set, the default `FULL`
        view is used.
      BASIC: Includes basic information of a cluster resource including
        cluster resource name and membership.
      FULL: Includes the complete configuration for VMware admin cluster
        resource. This is the default value for GetVmwareAdminClusterRequest
        method.
    """
    CLUSTER_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 3)


class GkeonpremProjectsLocationsVmwareAdminClustersListRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareAdminClustersListRequest object.

  Enums:
    ViewValueValuesEnum: View for VMware admin clusters. When `BASIC` is
      specified, only the admin cluster resource name and membership are
      returned. The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same
      as `FULL', which returns the complete admin cluster configuration
      details.

  Fields:
    allowMissing: Optional. If true, return list of Vmware Admin Clusters
      including the ones that only exists in RMS.
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, at most 50 clusters will be returned. The
      maximum value is 1000; values above 1000 will be coerced to 1000.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. The parent of the project and location where the
      clusters are listed in. Format:
      "projects/{project}/locations/{location}"
    view: View for VMware admin clusters. When `BASIC` is specified, only the
      admin cluster resource name and membership are returned. The
      default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL',
      which returns the complete admin cluster configuration details.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""View for VMware admin clusters. When `BASIC` is specified, only the
    admin cluster resource name and membership are returned. The default/unset
    value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL', which returns the
    complete admin cluster configuration details.

    Values:
      CLUSTER_VIEW_UNSPECIFIED: If the value is not set, the default `FULL`
        view is used.
      BASIC: Includes basic information of a admin cluster resource including
        admin cluster resource name and membership.
      FULL: Includes the complete configuration for bare metal admin cluster
        resource. This is the default value for ListVmwareAdminClustersRequest
        method.
    """
    CLUSTER_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  allowMissing = _messages.BooleanField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class GkeonpremProjectsLocationsVmwareAdminClustersOperationsGetRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareAdminClustersOperationsGetRequest
  object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GkeonpremProjectsLocationsVmwareAdminClustersOperationsListRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareAdminClustersOperationsListRequest
  object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GkeonpremProjectsLocationsVmwareAdminClustersPatchRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareAdminClustersPatchRequest object.

  Fields:
    name: Immutable. The VMware admin cluster resource name.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the VMwareAdminCluster resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all populated fields in the
      VmwareAdminCluster message will be updated. Empty fields will be ignored
      unless a field mask is used.
    validateOnly: Validate the request without actually doing any updates.
    vmwareAdminCluster: A VmwareAdminCluster resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)
  vmwareAdminCluster = _messages.MessageField('VmwareAdminCluster', 4)


class GkeonpremProjectsLocationsVmwareAdminClustersSetIamPolicyRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareAdminClustersSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkeonpremProjectsLocationsVmwareAdminClustersTestIamPermissionsRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareAdminClustersTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkeonpremProjectsLocationsVmwareAdminClustersUnenrollRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareAdminClustersUnenrollRequest object.

  Fields:
    allowMissing: If set to true, and the VMware admin cluster is not found,
      the request will succeed but no action will be taken on the server and
      return a completed LRO.
    etag: The current etag of the VMware admin cluster. If an etag is provided
      and does not match the current etag of the cluster, deletion will be
      blocked and an ABORTED error will be returned.
    ignoreErrors: Optional. If set to true, the unenrollment of a vmware admin
      cluster resource will succeed even if errors occur during unenrollment.
      This parameter can be used when you want to unenroll admin cluster
      resource and the on-prem admin cluster is disconnected / unreachable.
      WARNING: Using this parameter when your admin cluster still exists may
      result in a deleted GCP admin cluster but existing resourcelink in on-
      prem admin cluster and membership.
    name: Required. Name of the VMware admin cluster to be unenrolled. Format:
      "projects/{project}/locations/{location}/vmwareAdminClusters/{cluster}"
    validateOnly: Validate the request without actually doing any updates.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  ignoreErrors = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  validateOnly = _messages.BooleanField(5)


class GkeonpremProjectsLocationsVmwareClustersCreateRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersCreateRequest object.

  Fields:
    allowPreflightFailure: Optional. If set to true, CLM will force CCFE to
      persist the cluster resource in RMS when the creation fails during
      standalone preflight checks. In that case the subsequent create call
      will fail with "cluster already exists" error and hence a update cluster
      is required to fix the cluster.
    parent: Required. The parent of the project and location where this
      cluster is created in. Format: "projects/{project}/locations/{location}"
    validateOnly: Validate the request without actually doing any updates.
    vmwareCluster: A VmwareCluster resource to be passed as the request body.
    vmwareClusterId: User provided identifier that is used as part of the
      resource name; This value must be up to 40 characters and follow
      RFC-1123 (https://tools.ietf.org/html/rfc1123) format.
  """

  allowPreflightFailure = _messages.BooleanField(1)
  parent = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)
  vmwareCluster = _messages.MessageField('VmwareCluster', 4)
  vmwareClusterId = _messages.StringField(5)


class GkeonpremProjectsLocationsVmwareClustersDeleteRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersDeleteRequest object.

  Fields:
    allowMissing: If set to true, and the VMware cluster is not found, the
      request will succeed but no action will be taken on the server and
      return a completed LRO.
    etag: The current etag of the VMware cluster. If an etag is provided and
      does not match the current etag of the cluster, deletion will be blocked
      and an ABORTED error will be returned.
    force: If set to true, any node pools from the cluster will also be
      deleted.
    ignoreErrors: If set to true, the deletion of a VMware user cluster
      resource will succeed even if errors occur during deletion. This
      parameter can be used when you want to delete GCP's cluster resource and
      the on-prem admin cluster that hosts your user cluster is disconnected /
      unreachable or deleted. WARNING: Using this parameter when your user
      cluster still exists may result in a deleted GCP user cluster but an
      existing on-prem user cluster.
    name: Required. Name of the VMware user cluster to be deleted. Format:
      "projects/{project}/locations/{location}/vmwareClusters/{vmware_cluster}
      "
    validateOnly: Validate the request without actually doing any updates.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  force = _messages.BooleanField(3)
  ignoreErrors = _messages.BooleanField(4)
  name = _messages.StringField(5, required=True)
  validateOnly = _messages.BooleanField(6)


class GkeonpremProjectsLocationsVmwareClustersEnrollRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersEnrollRequest object.

  Fields:
    enrollVmwareClusterRequest: A EnrollVmwareClusterRequest resource to be
      passed as the request body.
    parent: Required. The parent of the project and location where the cluster
      is Enrolled in. Format: "projects/{project}/locations/{location}"
  """

  enrollVmwareClusterRequest = _messages.MessageField('EnrollVmwareClusterRequest', 1)
  parent = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsVmwareClustersGetIamPolicyRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsVmwareClustersGetRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersGetRequest object.

  Enums:
    ViewValueValuesEnum: View for VMware user cluster. When `BASIC` is
      specified, only the cluster resource name and admin cluster membership
      are returned. The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the
      same as `FULL', which returns the complete cluster configuration
      details.

  Fields:
    allowMissing: Optional. If true, return Vmware Cluster including the one
      that only exists in RMS.
    name: Required. Name of the VMware user cluster to be returned. Format:
      "projects/{project}/locations/{location}/vmwareClusters/{vmware_cluster}
      "
    view: View for VMware user cluster. When `BASIC` is specified, only the
      cluster resource name and admin cluster membership are returned. The
      default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL',
      which returns the complete cluster configuration details.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""View for VMware user cluster. When `BASIC` is specified, only the
    cluster resource name and admin cluster membership are returned. The
    default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL',
    which returns the complete cluster configuration details.

    Values:
      CLUSTER_VIEW_UNSPECIFIED: If the value is not set, the default `FULL`
        view is used.
      BASIC: Includes basic information of a cluster resource including
        cluster resource name and admin cluster membership.
      FULL: Includes the complete configuration for VMware cluster resource.
        This is the default value for GetVmwareClusterRequest method.
    """
    CLUSTER_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 3)


class GkeonpremProjectsLocationsVmwareClustersListRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersListRequest object.

  Enums:
    ViewValueValuesEnum: View for VMware clusters. When `BASIC` is specified,
      only the cluster resource name and admin cluster membership are
      returned. The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same
      as `FULL', which returns the complete cluster configuration details.

  Fields:
    allowMissing: Optional. If true, return list of Vmware Clusters including
      the ones that only exists in RMS.
    filter: A resource filtering expression following
      https://google.aip.dev/160. When non-empty, only resource's whose
      attributes field matches the filter are returned.
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, at most 50 clusters will be returned. The
      maximum value is 1000; values above 1000 will be coerced to 1000.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. The parent of the project and location where the
      clusters are listed in. Format:
      "projects/{project}/locations/{location}"
    view: View for VMware clusters. When `BASIC` is specified, only the
      cluster resource name and admin cluster membership are returned. The
      default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL',
      which returns the complete cluster configuration details.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""View for VMware clusters. When `BASIC` is specified, only the cluster
    resource name and admin cluster membership are returned. The default/unset
    value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL', which returns the
    complete cluster configuration details.

    Values:
      CLUSTER_VIEW_UNSPECIFIED: If the value is not set, the default `FULL`
        view is used.
      BASIC: Includes basic information of a cluster resource including
        cluster resource name and admin cluster membership.
      FULL: Includes the complete configuration for VMware cluster resource.
        This is the default value for ListVmwareClustersRequest method.
    """
    CLUSTER_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  allowMissing = _messages.BooleanField(1)
  filter = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 6)


class GkeonpremProjectsLocationsVmwareClustersOperationsGetRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GkeonpremProjectsLocationsVmwareClustersOperationsListRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GkeonpremProjectsLocationsVmwareClustersPatchRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersPatchRequest object.

  Fields:
    allowMissing: If set to true, and the VMware cluster is not found, the
      request will create a new VMware cluster with the provided
      configuration. The user must have both create and update permission to
      call Update with allow_missing set to true.
    name: Immutable. The VMware user cluster resource name.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the VMwareCluster resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all populated fields in the VmwareCluster
      message will be updated. Empty fields will be ignored unless a field
      mask is used.
    validateOnly: Validate the request without actually doing any updates.
    vmwareCluster: A VmwareCluster resource to be passed as the request body.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)
  vmwareCluster = _messages.MessageField('VmwareCluster', 5)


class GkeonpremProjectsLocationsVmwareClustersQueryVersionConfigRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersQueryVersionConfigRequest
  object.

  Fields:
    createConfig_adminClusterMembership: The admin cluster membership. This is
      the full resource name of the admin cluster's fleet membership. Format:
      "projects/{project}/locations/{location}/memberships/{membership}"
    createConfig_adminClusterName: The admin cluster resource name. This is
      the full resource name of the admin cluster resource. Format: "projects/
      {project}/locations/{location}/vmwareAdminClusters/{vmware_admin_cluster
      }"
    parent: Required. The parent of the project and location to query for
      version config. Format: "projects/{project}/locations/{location}"
    upgradeConfig_clusterName: The user cluster resource name. This is the
      full resource name of the user cluster resource. Format: "projects/{proj
      ect}/locations/{location}/vmwareClusters/{vmware_cluster}"
  """

  createConfig_adminClusterMembership = _messages.StringField(1)
  createConfig_adminClusterName = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  upgradeConfig_clusterName = _messages.StringField(4)


class GkeonpremProjectsLocationsVmwareClustersSetIamPolicyRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkeonpremProjectsLocationsVmwareClustersTestIamPermissionsRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkeonpremProjectsLocationsVmwareClustersUnenrollRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersUnenrollRequest object.

  Fields:
    allowMissing: If set to true, and the VMware cluster is not found, the
      request will succeed but no action will be taken on the server and
      return a completed LRO.
    etag: The current etag of the VMware Cluster. If an etag is provided and
      does not match the current etag of the cluster, deletion will be blocked
      and an ABORTED error will be returned.
    force: This is required if the cluster has any associated node pools. When
      set, any child node pools will also be unenrolled.
    name: Required. Name of the VMware user cluster to be unenrolled. Format:
      "projects/{project}/locations/{location}/vmwareClusters/{vmware_cluster}
      "
    validateOnly: Validate the request without actually doing any updates.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  force = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  validateOnly = _messages.BooleanField(5)


class GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsCreateRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsCreateRequest
  object.

  Fields:
    parent: Required. The parent resource where this node pool will be
      created.
      projects/{project}/locations/{location}/vmwareClusters/{cluster}
    validateOnly: If set, only validate the request, but do not actually
      create the node pool.
    vmwareNodePool: A VmwareNodePool resource to be passed as the request
      body.
    vmwareNodePoolId: The ID to use for the node pool, which will become the
      final component of the node pool's resource name. This value must be up
      to 40 characters and follow RFC-1123
      (https://tools.ietf.org/html/rfc1123) format. The value must not be
      permitted to be a UUID (or UUID-like: anything matching
      /^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$/i).
  """

  parent = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)
  vmwareNodePool = _messages.MessageField('VmwareNodePool', 3)
  vmwareNodePoolId = _messages.StringField(4)


class GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsDeleteRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsDeleteRequest
  object.

  Fields:
    allowMissing: If set to true, and the VMware node pool is not found, the
      request will succeed but no action will be taken on the server and
      return a completed LRO.
    etag: The current etag of the VmwareNodePool. If an etag is provided and
      does not match the current etag of the node pool, deletion will be
      blocked and an ABORTED error will be returned.
    ignoreErrors: If set to true, the deletion of a VMware node pool resource
      will succeed even if errors occur during deletion. This parameter can be
      used when you want to delete GCP's node pool resource and you've already
      deleted the on-prem admin cluster that hosted your node pool. WARNING:
      Using this parameter when your user cluster still exists may result in a
      deleted GCP node pool but an existing on-prem node pool.
    name: Required. The name of the node pool to delete. Format: projects/{pro
      ject}/locations/{location}/vmwareClusters/{cluster}/vmwareNodePools/{nod
      epool}
    validateOnly: If set, only validate the request, but do not actually
      delete the node pool.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  ignoreErrors = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  validateOnly = _messages.BooleanField(5)


class GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsEnrollRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsEnrollRequest
  object.

  Fields:
    enrollVmwareNodePoolRequest: A EnrollVmwareNodePoolRequest resource to be
      passed as the request body.
    parent: Required. The parent resource where the node pool is enrolled in.
  """

  enrollVmwareNodePoolRequest = _messages.MessageField('EnrollVmwareNodePoolRequest', 1)
  parent = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsGetIamPolicyRequest(_messages.Message):
  r"""A
  GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsGetRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsGetRequest
  object.

  Enums:
    ViewValueValuesEnum: View for VMware node pool. When `BASIC` is specified,
      only the node pool resource name is returned. The default/unset value
      `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL', which returns the
      complete node pool configuration details.

  Fields:
    name: Required. The name of the node pool to retrieve. projects/{project}/
      locations/{location}/vmwareClusters/{cluster}/vmwareNodePools/{nodepool}
    view: View for VMware node pool. When `BASIC` is specified, only the node
      pool resource name is returned. The default/unset value
      `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL', which returns the
      complete node pool configuration details.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""View for VMware node pool. When `BASIC` is specified, only the node
    pool resource name is returned. The default/unset value
    `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL', which returns the
    complete node pool configuration details.

    Values:
      NODE_POOL_VIEW_UNSPECIFIED: If the value is not set, the default `FULL`
        view is used.
      BASIC: Includes basic information of a node pool resource including node
        pool resource name.
      FULL: Includes the complete configuration for VMware node pool resource.
        This is the default value for GetVmwareNodePoolRequest method.
    """
    NODE_POOL_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsListRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsListRequest
  object.

  Enums:
    ViewValueValuesEnum: View for VMware node pools. When `BASIC` is
      specified, only the node pool resource name is returned. The
      default/unset value `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL',
      which returns the complete node pool configuration details.

  Fields:
    pageSize: The maximum number of node pools to return. The service may
      return fewer than this value. If unspecified, at most 50 node pools will
      be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: A page token, received from a previous `ListVmwareNodePools`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListVmwareNodePools` must match the call
      that provided the page token.
    parent: Required. The parent, which owns this collection of node pools.
      Format:
      projects/{project}/locations/{location}/vmwareClusters/{vmwareCluster}
    view: View for VMware node pools. When `BASIC` is specified, only the node
      pool resource name is returned. The default/unset value
      `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL', which returns the
      complete node pool configuration details.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""View for VMware node pools. When `BASIC` is specified, only the node
    pool resource name is returned. The default/unset value
    `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL', which returns the
    complete node pool configuration details.

    Values:
      NODE_POOL_VIEW_UNSPECIFIED: If the value is not set, the default `FULL`
        view is used.
      BASIC: Includes basic information of a node pool resource including node
        pool resource name.
      FULL: Includes the complete configuration for VMware node pool resource.
        This is the default value for ListVmwareNodePoolsRequest method.
    """
    NODE_POOL_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsOperationsGetRequest(_messages.Message):
  r"""A
  GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsOperationsGetRequest
  object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsOperationsListRequest(_messages.Message):
  r"""A
  GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsOperationsListRequest
  object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsPatchRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsPatchRequest
  object.

  Fields:
    allowMissing: If set to true, and the VMware node pool is not found, the
      request will create a new VMware node pool with the provided
      configuration. The user must have both create and update permission to
      call Update with allow_missing set to true.
    name: Immutable. The resource name of this node pool.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the VMwareNodePool resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all populated fields in the VMwareNodePool
      message will be updated. Empty fields will be ignored unless a field
      mask is used.
    validateOnly: Validate the request without actually doing any updates.
    vmwareNodePool: A VmwareNodePool resource to be passed as the request
      body.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)
  vmwareNodePool = _messages.MessageField('VmwareNodePool', 5)


class GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsSetIamPolicyRequest(_messages.Message):
  r"""A
  GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsTestIamPermissionsRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsTestIamPermissi
  onsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsUnenrollRequest(_messages.Message):
  r"""A GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsUnenrollRequest
  object.

  Fields:
    allowMissing: If set to true, and the VMware node pool is not found, the
      request will succeed but no action will be taken on the server and
      return a completed LRO.
    etag: The current etag of the VMware node pool. If an etag is provided and
      does not match the current etag of node pool, deletion will be blocked
      and an ABORTED error will be returned.
    name: Required. The name of the node pool to unenroll. Format: projects/{p
      roject}/locations/{location}/vmwareClusters/{cluster}/vmwareNodePools/{n
      odepool}
    validateOnly: If set, only validate the request, but do not actually
      unenroll the node pool.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class ListBareMetalAdminClustersResponse(_messages.Message):
  r"""Response message for listing bare metal admin clusters.

  Fields:
    bareMetalAdminClusters: The list of bare metal admin cluster.
    nextPageToken: A token identifying a page of results the server should
      return. If the token is not empty this means that more results are
      available and should be retrieved by repeating the request with the
      provided page token.
    unreachable: Locations that could not be reached.
  """

  bareMetalAdminClusters = _messages.MessageField('BareMetalAdminCluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBareMetalClustersResponse(_messages.Message):
  r"""Response message for listing bare metal Clusters.

  Fields:
    bareMetalClusters: The list of bare metal Clusters.
    nextPageToken: A token identifying a page of results the server should
      return. If the token is not empty this means that more results are
      available and should be retrieved by repeating the request with the
      provided page token.
    unreachable: Locations that could not be reached.
  """

  bareMetalClusters = _messages.MessageField('BareMetalCluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBareMetalNodePoolsResponse(_messages.Message):
  r"""Response message for listing bare metal node pools.

  Fields:
    bareMetalNodePools: The node pools from the specified parent resource.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  bareMetalNodePools = _messages.MessageField('BareMetalNodePool', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBareMetalStandaloneClustersResponse(_messages.Message):
  r"""Response message for listing bare metal standalone clusters.

  Fields:
    bareMetalStandaloneClusters: The list of bare metal standalone cluster.
    nextPageToken: A token identifying a page of results the server should
      return. If the token is not empty this means that more results are
      available and should be retrieved by repeating the request with the
      provided page token.
    unreachable: Locations that could not be reached.
  """

  bareMetalStandaloneClusters = _messages.MessageField('BareMetalStandaloneCluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBareMetalStandaloneNodePoolsResponse(_messages.Message):
  r"""Response message for listing bare metal standalone node pools.

  Fields:
    bareMetalStandaloneNodePools: The node pools from the specified parent
      resource.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  bareMetalStandaloneNodePools = _messages.MessageField('BareMetalStandaloneNodePool', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListVmwareAdminClustersResponse(_messages.Message):
  r"""Response message for listing VMware admin clusters.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return. If the token is not empty this means that more results are
      available and should be retrieved by repeating the request with the
      provided page token.
    unreachable: Locations that could not be reached.
    vmwareAdminClusters: The list of VMware admin cluster.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  vmwareAdminClusters = _messages.MessageField('VmwareAdminCluster', 3, repeated=True)


class ListVmwareClustersResponse(_messages.Message):
  r"""Response message for listing VMware Clusters.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return. If the token is not empty this means that more results are
      available and should be retrieved by repeating the request with the
      provided page token.
    unreachable: Locations that could not be reached.
    vmwareClusters: The list of VMware Cluster.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  vmwareClusters = _messages.MessageField('VmwareCluster', 3, repeated=True)


class ListVmwareNodePoolsResponse(_messages.Message):
  r"""Response message for listing VMware node pools.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
    vmwareNodePools: The node pools from the specified parent resource.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  vmwareNodePools = _messages.MessageField('VmwareNodePool', 3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Metric(_messages.Message):
  r"""Progress metric is (string, int|float|string) pair.

  Enums:
    MetricValueValuesEnum: Required. The metric name.

  Fields:
    doubleValue: For metrics with floating point value.
    intValue: For metrics with integer value.
    metric: Required. The metric name.
    stringValue: For metrics with custom values (ratios, visual progress,
      etc.).
  """

  class MetricValueValuesEnum(_messages.Enum):
    r"""Required. The metric name.

    Values:
      METRIC_ID_UNSPECIFIED: Not set.
      NODES_TOTAL: The total number of nodes being actuated.
      NODES_DRAINING: The number of nodes draining.
      NODES_UPGRADING: The number of nodes actively upgrading.
      NODES_PENDING_UPGRADE: The number of nodes to be upgraded.
      NODES_UPGRADED: The number of nodes upgraded.
      NODES_FAILED: The number of nodes to fail actuation.
      NODES_HEALTHY: The number of nodes healthy.
      NODES_RECONCILING: The number of nodes reconciling.
      NODES_IN_MAINTENANCE: The number of nodes in maintenance mode.
      PREFLIGHTS_COMPLETED: The number of completed preflight checks.
      PREFLIGHTS_RUNNING: The number of preflight checks running.
      PREFLIGHTS_FAILED: The number of preflight checks failed.
      PREFLIGHTS_TOTAL: The total number of preflight checks.
    """
    METRIC_ID_UNSPECIFIED = 0
    NODES_TOTAL = 1
    NODES_DRAINING = 2
    NODES_UPGRADING = 3
    NODES_PENDING_UPGRADE = 4
    NODES_UPGRADED = 5
    NODES_FAILED = 6
    NODES_HEALTHY = 7
    NODES_RECONCILING = 8
    NODES_IN_MAINTENANCE = 9
    PREFLIGHTS_COMPLETED = 10
    PREFLIGHTS_RUNNING = 11
    PREFLIGHTS_FAILED = 12
    PREFLIGHTS_TOTAL = 13

  doubleValue = _messages.FloatField(1)
  intValue = _messages.IntegerField(2)
  metric = _messages.EnumField('MetricValueValuesEnum', 3)
  stringValue = _messages.StringField(4)


class NodeTaint(_messages.Message):
  r"""NodeTaint applied to every Kubernetes node in a node pool. Kubernetes
  taints can be used together with tolerations to control how workloads are
  scheduled to your nodes. Node taints are permanent.

  Enums:
    EffectValueValuesEnum: The taint effect.

  Fields:
    effect: The taint effect.
    key: Key associated with the effect.
    value: Value associated with the effect.
  """

  class EffectValueValuesEnum(_messages.Enum):
    r"""The taint effect.

    Values:
      EFFECT_UNSPECIFIED: Not set.
      NO_SCHEDULE: Do not allow new pods to schedule onto the node unless they
        tolerate the taint, but allow all pods submitted to Kubelet without
        going through the scheduler to start, and allow all already-running
        pods to continue running. Enforced by the scheduler.
      PREFER_NO_SCHEDULE: Like TaintEffectNoSchedule, but the scheduler tries
        not to schedule new pods onto the node, rather than prohibiting new
        pods from scheduling onto the node entirely. Enforced by the
        scheduler.
      NO_EXECUTE: Evict any already-running pods that do not tolerate the
        taint. Currently enforced by NodeController.
    """
    EFFECT_UNSPECIFIED = 0
    NO_SCHEDULE = 1
    PREFER_NO_SCHEDULE = 2
    NO_EXECUTE = 3

  effect = _messages.EnumField('EffectValueValuesEnum', 1)
  key = _messages.StringField(2)
  value = _messages.StringField(3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Enums:
    TypeValueValuesEnum: Output only. Type of operation being executed.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    controlPlaneDisconnected: Output only. Denotes if the local managing
      cluster's control plane is currently disconnected. This is expected to
      occur temporarily during self-managed cluster upgrades.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    progress: Output only. Detailed progress information for the operation.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have [Operation.error] value with a
      [google.rpc.Status.code] of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    type: Output only. Type of operation being executed.
    verb: Output only. Name of the verb executed by the operation.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of operation being executed.

    Values:
      OPERATION_TYPE_UNSPECIFIED: Not set.
      CREATE: The resource is being created.
      DELETE: The resource is being deleted.
      UPDATE: The resource is being updated.
      UPGRADE: The resource is being upgraded.
      PLATFORM_UPGRADE: The platform is being upgraded.
    """
    OPERATION_TYPE_UNSPECIFIED = 0
    CREATE = 1
    DELETE = 2
    UPDATE = 3
    UPGRADE = 4
    PLATFORM_UPGRADE = 5

  apiVersion = _messages.StringField(1)
  controlPlaneDisconnected = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  progress = _messages.MessageField('OperationProgress', 5)
  requestedCancellation = _messages.BooleanField(6)
  statusMessage = _messages.StringField(7)
  target = _messages.StringField(8)
  type = _messages.EnumField('TypeValueValuesEnum', 9)
  verb = _messages.StringField(10)


class OperationProgress(_messages.Message):
  r"""Information about operation progress.

  Fields:
    stages: The stages of the operation.
  """

  stages = _messages.MessageField('OperationStage', 1, repeated=True)


class OperationStage(_messages.Message):
  r"""Information about a particular stage of an operation.

  Enums:
    StageValueValuesEnum: The high-level stage of the operation.
    StateValueValuesEnum: Output only. State of the stage.

  Fields:
    endTime: Time the stage ended.
    metrics: Progress metric bundle.
    stage: The high-level stage of the operation.
    startTime: Time the stage started.
    state: Output only. State of the stage.
  """

  class StageValueValuesEnum(_messages.Enum):
    r"""The high-level stage of the operation.

    Values:
      STAGE_UNSPECIFIED: Not set.
      PREFLIGHT_CHECK: Preflight checks are running.
      CONFIGURE: Resource is being configured.
      DEPLOY: Resource is being deployed.
      HEALTH_CHECK: Waiting for the resource to become healthy.
      UPDATE: Resource is being updated.
    """
    STAGE_UNSPECIFIED = 0
    PREFLIGHT_CHECK = 1
    CONFIGURE = 2
    DEPLOY = 3
    HEALTH_CHECK = 4
    UPDATE = 5

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the stage.

    Values:
      STATE_UNSPECIFIED: Not set.
      PENDING: The stage is pending.
      RUNNING: The stage is running
      SUCCEEDED: The stage has completed successfully.
      FAILED: The stage has failed.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4

  endTime = _messages.StringField(1)
  metrics = _messages.MessageField('Metric', 2, repeated=True)
  stage = _messages.EnumField('StageValueValuesEnum', 3)
  startTime = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class QueryBareMetalAdminVersionConfigResponse(_messages.Message):
  r"""Response message for querying bare metal admin cluster version config.

  Fields:
    versions: List of available versions to install or to upgrade to.
  """

  versions = _messages.MessageField('BareMetalVersionInfo', 1, repeated=True)


class QueryBareMetalStandaloneVersionConfigResponse(_messages.Message):
  r"""Response message for querying bare metal standalone cluster version
  config.

  Fields:
    versions: List of available versions to install or to upgrade to.
  """

  versions = _messages.MessageField('BareMetalVersionInfo', 1, repeated=True)


class QueryBareMetalVersionConfigResponse(_messages.Message):
  r"""Response message for querying bare metal admin cluster version config.

  Fields:
    versions: List of available versions to install or to upgrade to.
  """

  versions = _messages.MessageField('BareMetalVersionInfo', 1, repeated=True)


class QueryVmwareVersionConfigResponse(_messages.Message):
  r"""Response message for querying VMware user cluster version config.

  Fields:
    versions: List of available versions to install or to upgrade to.
  """

  versions = _messages.MessageField('VmwareVersionInfo', 1, repeated=True)


class ResourceCondition(_messages.Message):
  r"""ResourceCondition provides a standard mechanism for higher-level status
  reporting from controller.

  Enums:
    StateValueValuesEnum: state of the condition.

  Fields:
    lastTransitionTime: Last time the condition transit from one status to
      another.
    message: Human-readable message indicating details about last transition.
    reason: Machine-readable message indicating details about last transition.
    state: state of the condition.
    type: Type of the condition. (e.g., ClusterRunning, NodePoolRunning or
      ServerSidePreflightReady)
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""state of the condition.

    Values:
      STATE_UNSPECIFIED: Not set.
      STATE_TRUE: Resource is in the condition.
      STATE_FALSE: Resource is not in the condition.
      STATE_UNKNOWN: Kubernetes controller can't decide if the resource is in
        the condition or not.
    """
    STATE_UNSPECIFIED = 0
    STATE_TRUE = 1
    STATE_FALSE = 2
    STATE_UNKNOWN = 3

  lastTransitionTime = _messages.StringField(1)
  message = _messages.StringField(2)
  reason = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  type = _messages.StringField(5)


class ResourceStatus(_messages.Message):
  r"""ResourceStatus describes why a cluster or node pool has a certain
  status. (e.g., ERROR or DEGRADED).

  Fields:
    conditions: ResourceCondition provide a standard mechanism for higher-
      level status reporting from controller.
    errorMessage: Human-friendly representation of the error message from
      controller. The error message can be temporary as the controller
      controller creates a cluster or node pool. If the error message persists
      for a longer period of time, it can be used to surface error message to
      indicate real problems requiring user intervention.
    version: Reflect current version of the resource.
    versions: Shows the mapping of a given version to the number of machines
      under this version.
  """

  conditions = _messages.MessageField('ResourceCondition', 1, repeated=True)
  errorMessage = _messages.StringField(2)
  version = _messages.StringField(3)
  versions = _messages.MessageField('Versions', 4)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('Policy', 1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class UpgradeDependency(_messages.Message):
  r"""UpgradeDependency represents a dependency when upgrading a resource.

  Fields:
    currentVersion: Current version of the dependency e.g. 1.15.0.
    membership: Membership names are formatted as
      `projects//locations//memberships/`.
    resourceName: Resource name of the dependency.
    targetVersion: Target version of the dependency e.g. 1.16.1. This is the
      version the dependency needs to be upgraded to before a resource can be
      upgraded.
  """

  currentVersion = _messages.StringField(1)
  membership = _messages.StringField(2)
  resourceName = _messages.StringField(3)
  targetVersion = _messages.StringField(4)


class ValidationCheck(_messages.Message):
  r"""ValidationCheck represents the result of preflight check.

  Enums:
    OptionValueValuesEnum: Options used for the validation check
    ScenarioValueValuesEnum: Output only. The scenario when the preflight
      checks were run.

  Fields:
    option: Options used for the validation check
    scenario: Output only. The scenario when the preflight checks were run.
    status: Output only. The detailed validation check status.
  """

  class OptionValueValuesEnum(_messages.Enum):
    r"""Options used for the validation check

    Values:
      OPTIONS_UNSPECIFIED: Default value. Standard preflight validation check
        will be used.
      SKIP_VALIDATION_CHECK_BLOCKING: Prevent failed preflight checks from
        failing.
      SKIP_VALIDATION_ALL: Skip all preflight check validations.
    """
    OPTIONS_UNSPECIFIED = 0
    SKIP_VALIDATION_CHECK_BLOCKING = 1
    SKIP_VALIDATION_ALL = 2

  class ScenarioValueValuesEnum(_messages.Enum):
    r"""Output only. The scenario when the preflight checks were run.

    Values:
      SCENARIO_UNSPECIFIED: Default value. This value is unused.
      CREATE: The validation check occurred during a create flow.
      UPDATE: The validation check occurred during an update flow.
    """
    SCENARIO_UNSPECIFIED = 0
    CREATE = 1
    UPDATE = 2

  option = _messages.EnumField('OptionValueValuesEnum', 1)
  scenario = _messages.EnumField('ScenarioValueValuesEnum', 2)
  status = _messages.MessageField('ValidationCheckStatus', 3)


class ValidationCheckResult(_messages.Message):
  r"""ValidationCheckResult defines the details about the validation check.

  Enums:
    StateValueValuesEnum: The validation check state.

  Fields:
    category: The category of the validation.
    description: The description of the validation check.
    details: Detailed failure information, which might be unformatted.
    reason: A human-readable message of the check failure.
    state: The validation check state.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The validation check state.

    Values:
      STATE_UNKNOWN: The default value. The check result is unknown.
      STATE_FAILURE: The check failed.
      STATE_SKIPPED: The check was skipped.
      STATE_FATAL: The check itself failed to complete.
      STATE_WARNING: The check encountered a warning.
    """
    STATE_UNKNOWN = 0
    STATE_FAILURE = 1
    STATE_SKIPPED = 2
    STATE_FATAL = 3
    STATE_WARNING = 4

  category = _messages.StringField(1)
  description = _messages.StringField(2)
  details = _messages.StringField(3)
  reason = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class ValidationCheckStatus(_messages.Message):
  r"""ValidationCheckStatus defines the detailed validation check status.

  Fields:
    result: Individual checks which failed as part of the Preflight check
      execution.
  """

  result = _messages.MessageField('ValidationCheckResult', 1, repeated=True)


class Version(_messages.Message):
  r"""Version describes the number of nodes at a given version under a
  resource.

  Fields:
    count: Number of machines under the above version.
    version: Resource version.
  """

  count = _messages.IntegerField(1)
  version = _messages.StringField(2)


class Versions(_messages.Message):
  r"""Versions describes the mapping of a given version to the number of
  machines under this version.

  Fields:
    versions: Shows the mapping of a given version to the number of machines
      under this version.
  """

  versions = _messages.MessageField('Version', 1, repeated=True)


class VmwareAAGConfig(_messages.Message):
  r"""Specifies anti affinity group config for the VMware user cluster.

  Fields:
    aagConfigDisabled: Spread nodes across at least three physical hosts
      (requires at least three hosts). Enabled by default.
  """

  aagConfigDisabled = _messages.BooleanField(1)


class VmwareAddressPool(_messages.Message):
  r"""Represents an IP pool used by the load balancer.

  Fields:
    addresses: Required. The addresses that are part of this pool. Each
      address must be either in the CIDR form (*******/24) or range form
      (*******-*******).
    avoidBuggyIps: If true, avoid using IPs ending in .0 or .255. This avoids
      buggy consumer devices mistakenly dropping IPv4 traffic for those
      special IP addresses.
    manualAssign: If true, prevent IP addresses from being automatically
      assigned.
    pool: Required. The name of the address pool.
  """

  addresses = _messages.StringField(1, repeated=True)
  avoidBuggyIps = _messages.BooleanField(2)
  manualAssign = _messages.BooleanField(3)
  pool = _messages.StringField(4)


class VmwareAdminAddonNodeConfig(_messages.Message):
  r"""VmwareAdminAddonNodeConfig contains add-on node configurations for
  VMware admin cluster.

  Fields:
    autoResizeConfig: VmwareAutoResizeConfig config specifies auto resize
      config.
  """

  autoResizeConfig = _messages.MessageField('VmwareAutoResizeConfig', 1)


class VmwareAdminAuthorizationConfig(_messages.Message):
  r"""VmwareAdminAuthorizationConfig represents configuration for admin
  cluster authorization.

  Fields:
    viewerUsers: For VMware admin clusters, users will be granted the cluster-
      viewer role on the cluster.
  """

  viewerUsers = _messages.MessageField('ClusterUser', 1, repeated=True)


class VmwareAdminCluster(_messages.Message):
  r"""Resource that represents a VMware admin cluster.

  Enums:
    StateValueValuesEnum: Output only. The current state of VMware admin
      cluster.

  Messages:
    AnnotationsValue: Annotations on the VMware admin cluster. This field has
      the same restrictions as Kubernetes annotations. The total size of all
      keys and values combined is limited to 256k. Key can have 2 segments:
      prefix (optional) and name (required), separated by a slash (/). Prefix
      must be a DNS subdomain. Name must be 63 characters or less, begin and
      end with alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.

  Fields:
    addonNode: The VMware admin cluster addon node configuration.
    annotations: Annotations on the VMware admin cluster. This field has the
      same restrictions as Kubernetes annotations. The total size of all keys
      and values combined is limited to 256k. Key can have 2 segments: prefix
      (optional) and name (required), separated by a slash (/). Prefix must be
      a DNS subdomain. Name must be 63 characters or less, begin and end with
      alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.
    antiAffinityGroups: The VMware admin cluster anti affinity group
      configuration.
    authorization: The VMware admin cluster authorization configuration.
    autoRepairConfig: The VMware admin cluster auto repair configuration.
    bootstrapClusterMembership: The bootstrap cluster this VMware admin
      cluster belongs to.
    controlPlaneNode: The VMware admin cluster control plane node
      configuration.
    createTime: Output only. The time at which VMware admin cluster was
      created.
    description: A human readable description of this VMware admin cluster.
    enableAdvancedCluster: Enable advanced cluster.
    endpoint: Output only. The DNS name of VMware admin cluster's API server.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding. Allows clients to
      perform consistent read-modify-writes through optimistic concurrency
      control.
    fleet: Output only. Fleet configuration for the cluster.
    imageType: The OS image type for the VMware admin cluster.
    loadBalancer: The VMware admin cluster load balancer configuration.
    localName: Output only. The object name of the VMware OnPremAdminCluster
      custom resource. This field is used to support conflicting names when
      enrolling existing clusters to the API. When used as a part of cluster
      enrollment, this field will differ from the ID in the resource name. For
      new clusters, this field will match the user provided cluster name and
      be visible in the last component of the resource name. It is not
      modifiable. All users should use this name to access their cluster using
      gkectl or kubectl and should expect to see the local name when viewing
      admin cluster controller logs.
    name: Immutable. The VMware admin cluster resource name.
    networkConfig: The VMware admin cluster network configuration.
    onPremVersion: The Anthos clusters on the VMware version for the admin
      cluster.
    platformConfig: The VMware platform configuration.
    preparedSecrets: Output only. The VMware admin cluster prepared secrets
      configuration. It should always be enabled by the Central API, instead
      of letting users set it.
    privateRegistryConfig: Configuration for registry.
    reconciling: Output only. If set, there are currently changes in flight to
      the VMware admin cluster.
    state: Output only. The current state of VMware admin cluster.
    status: Output only. ResourceStatus representing detailed cluster state.
    uid: Output only. The unique identifier of the VMware admin cluster.
    updateTime: Output only. The time at which VMware admin cluster was last
      updated.
    validationCheck: Output only. ValidationCheck represents the result of the
      preflight check job.
    vcenter: The VMware admin cluster VCenter configuration.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of VMware admin cluster.

    Values:
      STATE_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the cluster is being
        created.
      RUNNING: The RUNNING state indicates the cluster has been created and is
        fully usable.
      RECONCILING: The RECONCILING state indicates that the cluster is being
        updated. It remains available, but potentially with degraded
        performance.
      STOPPING: The STOPPING state indicates the cluster is being deleted.
      ERROR: The ERROR state indicates the cluster is in a broken
        unrecoverable state.
      DEGRADED: The DEGRADED state indicates the cluster requires user action
        to restore full functionality.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RECONCILING = 3
    STOPPING = 4
    ERROR = 5
    DEGRADED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations on the VMware admin cluster. This field has the same
    restrictions as Kubernetes annotations. The total size of all keys and
    values combined is limited to 256k. Key can have 2 segments: prefix
    (optional) and name (required), separated by a slash (/). Prefix must be a
    DNS subdomain. Name must be 63 characters or less, begin and end with
    alphanumerics, with dashes (-), underscores (_), dots (.), and
    alphanumerics between.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  addonNode = _messages.MessageField('VmwareAdminAddonNodeConfig', 1)
  annotations = _messages.MessageField('AnnotationsValue', 2)
  antiAffinityGroups = _messages.MessageField('VmwareAAGConfig', 3)
  authorization = _messages.MessageField('VmwareAdminAuthorizationConfig', 4)
  autoRepairConfig = _messages.MessageField('VmwareAutoRepairConfig', 5)
  bootstrapClusterMembership = _messages.StringField(6)
  controlPlaneNode = _messages.MessageField('VmwareAdminControlPlaneNodeConfig', 7)
  createTime = _messages.StringField(8)
  description = _messages.StringField(9)
  enableAdvancedCluster = _messages.BooleanField(10)
  endpoint = _messages.StringField(11)
  etag = _messages.StringField(12)
  fleet = _messages.MessageField('Fleet', 13)
  imageType = _messages.StringField(14)
  loadBalancer = _messages.MessageField('VmwareAdminLoadBalancerConfig', 15)
  localName = _messages.StringField(16)
  name = _messages.StringField(17)
  networkConfig = _messages.MessageField('VmwareAdminNetworkConfig', 18)
  onPremVersion = _messages.StringField(19)
  platformConfig = _messages.MessageField('VmwarePlatformConfig', 20)
  preparedSecrets = _messages.MessageField('VmwareAdminPreparedSecretsConfig', 21)
  privateRegistryConfig = _messages.MessageField('VmwareAdminPrivateRegistryConfig', 22)
  reconciling = _messages.BooleanField(23)
  state = _messages.EnumField('StateValueValuesEnum', 24)
  status = _messages.MessageField('ResourceStatus', 25)
  uid = _messages.StringField(26)
  updateTime = _messages.StringField(27)
  validationCheck = _messages.MessageField('ValidationCheck', 28)
  vcenter = _messages.MessageField('VmwareAdminVCenterConfig', 29)


class VmwareAdminControlPlaneNodeConfig(_messages.Message):
  r"""VmwareAdminControlPlaneNodeConfig contains control plane node
  configuration for VMware admin cluster.

  Fields:
    cpus: The number of vCPUs for the control-plane node of the admin cluster.
    memory: The number of mebibytes of memory for the control-plane node of
      the admin cluster.
    replicas: The number of control plane nodes for this VMware admin cluster.
      (default: 1 replica).
  """

  cpus = _messages.IntegerField(1)
  memory = _messages.IntegerField(2)
  replicas = _messages.IntegerField(3)


class VmwareAdminF5BigIpConfig(_messages.Message):
  r"""VmwareAdminF5BigIpConfig represents configuration parameters for an F5
  BIG-IP load balancer.

  Fields:
    address: The load balancer's IP address.
    partition: The preexisting partition to be used by the load balancer. This
      partition is usually created for the admin cluster for example:
      'my-f5-admin-partition'.
    snatPool: The pool name. Only necessary, if using SNAT.
  """

  address = _messages.StringField(1)
  partition = _messages.StringField(2)
  snatPool = _messages.StringField(3)


class VmwareAdminHAControlPlaneConfig(_messages.Message):
  r"""Specifies HA admin control plane config.

  Fields:
    controlPlaneIpBlock: Static IP addresses for the admin control plane
      nodes.
  """

  controlPlaneIpBlock = _messages.MessageField('VmwareIpBlock', 1)


class VmwareAdminLoadBalancerConfig(_messages.Message):
  r"""VmwareAdminLoadBalancerConfig contains load balancer configuration for
  VMware admin cluster.

  Fields:
    f5Config: Configuration for F5 Big IP typed load balancers.
    manualLbConfig: Manually configured load balancers.
    metalLbConfig: MetalLB load balancers.
    seesawConfig: Output only. Configuration for Seesaw typed load balancers.
    vipConfig: The VIPs used by the load balancer.
  """

  f5Config = _messages.MessageField('VmwareAdminF5BigIpConfig', 1)
  manualLbConfig = _messages.MessageField('VmwareAdminManualLbConfig', 2)
  metalLbConfig = _messages.MessageField('VmwareAdminMetalLbConfig', 3)
  seesawConfig = _messages.MessageField('VmwareAdminSeesawConfig', 4)
  vipConfig = _messages.MessageField('VmwareAdminVipConfig', 5)


class VmwareAdminManualLbConfig(_messages.Message):
  r"""A VmwareAdminManualLbConfig object.

  Fields:
    addonsNodePort: NodePort for add-ons server in the admin cluster.
    controlPlaneNodePort: NodePort for control plane service. The Kubernetes
      API server in the admin cluster is implemented as a Service of type
      NodePort (ex. 30968).
    ingressHttpNodePort: NodePort for ingress service's http. The ingress
      service in the admin cluster is implemented as a Service of type
      NodePort (ex. 32527).
    ingressHttpsNodePort: NodePort for ingress service's https. The ingress
      service in the admin cluster is implemented as a Service of type
      NodePort (ex. 30139).
    konnectivityServerNodePort: NodePort for konnectivity server service
      running as a sidecar in each kube-apiserver pod (ex. 30564).
  """

  addonsNodePort = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  controlPlaneNodePort = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  ingressHttpNodePort = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  ingressHttpsNodePort = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  konnectivityServerNodePort = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class VmwareAdminMetalLbConfig(_messages.Message):
  r"""VmwareAdminMetalLbConfig represents configuration parameters for a
  MetalLB load balancer. For admin clusters, currently no configurations is
  needed.

  Fields:
    enabled: Whether MetalLB is enabled.
  """

  enabled = _messages.BooleanField(1)


class VmwareAdminNetworkConfig(_messages.Message):
  r"""VmwareAdminNetworkConfig contains network configuration for VMware admin
  cluster.

  Fields:
    dhcpIpConfig: Configuration settings for a DHCP IP configuration.
    haControlPlaneConfig: Configuration for HA admin cluster control plane.
    hostConfig: Represents common network settings irrespective of the host's
      IP address.
    podAddressCidrBlocks: Required. All pods in the cluster are assigned an
      RFC1918 IPv4 address from these ranges. Only a single range is
      supported. This field cannot be changed after creation.
    serviceAddressCidrBlocks: Required. All services in the cluster are
      assigned an RFC1918 IPv4 address from these ranges. Only a single range
      is supported. This field cannot be changed after creation.
    staticIpConfig: Configuration settings for a static IP configuration.
    vcenterNetwork: vcenter_network specifies vCenter network name.
  """

  dhcpIpConfig = _messages.MessageField('VmwareDhcpIpConfig', 1)
  haControlPlaneConfig = _messages.MessageField('VmwareAdminHAControlPlaneConfig', 2)
  hostConfig = _messages.MessageField('VmwareHostConfig', 3)
  podAddressCidrBlocks = _messages.StringField(4, repeated=True)
  serviceAddressCidrBlocks = _messages.StringField(5, repeated=True)
  staticIpConfig = _messages.MessageField('VmwareStaticIpConfig', 6)
  vcenterNetwork = _messages.StringField(7)


class VmwareAdminPreparedSecretsConfig(_messages.Message):
  r"""VmwareAdminPreparedSecretsConfig represents configuration for admin
  cluster prepared secrets.

  Fields:
    enabled: Whether prepared secrets is enabled.
  """

  enabled = _messages.BooleanField(1)


class VmwareAdminPrivateRegistryConfig(_messages.Message):
  r"""VmwareAdminPrivateRegistryConfig represents configuration for admin
  cluster registry.

  Fields:
    address: The registry address.
    caCert: When the container runtime pulls an image from private registry,
      the registry must prove its identity by presenting a certificate. The
      registry's certificate is signed by a certificate authority (CA). The
      container runtime uses the CA's certificate to validate the registry's
      certificate.
  """

  address = _messages.StringField(1)
  caCert = _messages.StringField(2)


class VmwareAdminSeesawConfig(_messages.Message):
  r"""VmwareSeesawConfig represents configuration parameters for an already
  existing Seesaw load balancer. IMPORTANT: Please note that the Anthos On-
  Prem API will not generate or update Seesaw configurations it can only bind
  a pre-existing configuration to a new user cluster. IMPORTANT: When
  attempting to create a user cluster with a pre-existing Seesaw load balancer
  you will need to follow some preparation steps before calling the
  'CreateVmwareCluster' API method. First you will need to create the user
  cluster's namespace via kubectl. The namespace will need to use the
  following naming convention : -gke-onprem-mgmt or -gke-onprem-mgmt depending
  on whether you used the 'VmwareCluster.local_name' to disambiguate
  collisions; for more context see the documentation of
  'VmwareCluster.local_name'. Once the namespace is created you will need to
  create a secret resource via kubectl. This secret will contain copies of
  your Seesaw credentials. The Secret must be called 'user-cluster-creds' and
  contain Seesaw's SSH and Cert credentials. The credentials must be keyed
  with the following names: 'seesaw-ssh-private-key', 'seesaw-ssh-public-key',
  'seesaw-ssh-ca-key', 'seesaw-ssh-ca-cert'.

  Fields:
    enableHa: Enable two load balancer VMs to achieve a highly-available
      Seesaw load balancer.
    group: In general the following format should be used for the Seesaw group
      name: seesaw-for-[cluster_name].
    ipBlocks: The IP Blocks to be used by the Seesaw load balancer
    masterIp: MasterIP is the IP announced by the master of Seesaw group.
    stackdriverName: Name to be used by Stackdriver.
    vms: Names of the VMs created for this Seesaw group.
  """

  enableHa = _messages.BooleanField(1)
  group = _messages.StringField(2)
  ipBlocks = _messages.MessageField('VmwareIpBlock', 3, repeated=True)
  masterIp = _messages.StringField(4)
  stackdriverName = _messages.StringField(5)
  vms = _messages.StringField(6, repeated=True)


class VmwareAdminVCenterConfig(_messages.Message):
  r"""VmwareAdminVCenterConfig contains VCenter configuration for VMware admin
  cluster.

  Fields:
    address: The vCenter IP address.
    caCertData: Contains the vCenter CA certificate public key for SSL
      verification.
    cluster: The name of the vCenter cluster for the admin cluster.
    dataDisk: The name of the virtual machine disk (VMDK) for the admin
      cluster.
    datacenter: The name of the vCenter datacenter for the admin cluster.
    datastore: The name of the vCenter datastore for the admin cluster.
    folder: The name of the vCenter folder for the admin cluster.
    resourcePool: The name of the vCenter resource pool for the admin cluster.
    storagePolicyName: The name of the vCenter storage policy for the user
      cluster.
  """

  address = _messages.StringField(1)
  caCertData = _messages.StringField(2)
  cluster = _messages.StringField(3)
  dataDisk = _messages.StringField(4)
  datacenter = _messages.StringField(5)
  datastore = _messages.StringField(6)
  folder = _messages.StringField(7)
  resourcePool = _messages.StringField(8)
  storagePolicyName = _messages.StringField(9)


class VmwareAdminVipConfig(_messages.Message):
  r"""VmwareAdminVipConfig for VMware load balancer configurations.

  Fields:
    addonsVip: The VIP to configure the load balancer for add-ons.
    controlPlaneVip: The VIP which you previously set aside for the Kubernetes
      API of the admin cluster.
  """

  addonsVip = _messages.StringField(1)
  controlPlaneVip = _messages.StringField(2)


class VmwareAutoRepairConfig(_messages.Message):
  r"""Specifies config to enable/disable auto repair. The cluster-health-
  controller is deployed only if Enabled is true.

  Fields:
    enabled: Whether auto repair is enabled.
  """

  enabled = _messages.BooleanField(1)


class VmwareAutoResizeConfig(_messages.Message):
  r"""Represents auto resizing configurations for the VMware user cluster.

  Fields:
    enabled: Whether to enable controle plane node auto resizing.
  """

  enabled = _messages.BooleanField(1)


class VmwareBundleConfig(_messages.Message):
  r"""VmwareBundleConfig represents configuration for the bundle.

  Fields:
    status: Output only. Resource status for the bundle.
    version: The version of the bundle.
  """

  status = _messages.MessageField('ResourceStatus', 1)
  version = _messages.StringField(2)


class VmwareCluster(_messages.Message):
  r"""Resource that represents a VMware user cluster. ##

  Enums:
    StateValueValuesEnum: Output only. The current state of VMware user
      cluster.

  Messages:
    AnnotationsValue: Annotations on the VMware user cluster. This field has
      the same restrictions as Kubernetes annotations. The total size of all
      keys and values combined is limited to 256k. Key can have 2 segments:
      prefix (optional) and name (required), separated by a slash (/). Prefix
      must be a DNS subdomain. Name must be 63 characters or less, begin and
      end with alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.

  Fields:
    adminClusterMembership: Required. The admin cluster this VMware user
      cluster belongs to. This is the full resource name of the admin
      cluster's fleet membership. In the future, references to other resource
      types might be allowed if admin clusters are modeled as their own
      resources.
    adminClusterName: Output only. The resource name of the VMware admin
      cluster hosting this user cluster.
    annotations: Annotations on the VMware user cluster. This field has the
      same restrictions as Kubernetes annotations. The total size of all keys
      and values combined is limited to 256k. Key can have 2 segments: prefix
      (optional) and name (required), separated by a slash (/). Prefix must be
      a DNS subdomain. Name must be 63 characters or less, begin and end with
      alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.
    antiAffinityGroups: AAGConfig specifies whether to spread VMware user
      cluster nodes across at least three physical hosts in the datacenter.
    authorization: RBAC policy that will be applied and managed by the Anthos
      On-Prem API.
    autoRepairConfig: Configuration for auto repairing.
    binaryAuthorization: Binary Authorization related configurations.
    controlPlaneNode: VMware user cluster control plane nodes must have either
      1 or 3 replicas.
    createTime: Output only. The time at which VMware user cluster was
      created.
    dataplaneV2: VmwareDataplaneV2Config specifies configuration for Dataplane
      V2.
    deleteTime: Output only. The time at which VMware user cluster was
      deleted.
    description: A human readable description of this VMware user cluster.
    disableBundledIngress: Disable bundled ingress.
    enableAdvancedCluster: Enable advanced cluster.
    enableControlPlaneV2: Enable control plane V2. Default to false.
    endpoint: Output only. The DNS name of VMware user cluster's API server.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding. Allows clients to
      perform consistent read-modify-writes through optimistic concurrency
      control.
    fleet: Output only. Fleet configuration for the cluster.
    loadBalancer: Load balancer configuration.
    localName: Output only. The object name of the VMware OnPremUserCluster
      custom resource on the associated admin cluster. This field is used to
      support conflicting names when enrolling existing clusters to the API.
      When used as a part of cluster enrollment, this field will differ from
      the ID in the resource name. For new clusters, this field will match the
      user provided cluster name and be visible in the last component of the
      resource name. It is not modifiable. All users should use this name to
      access their cluster using gkectl or kubectl and should expect to see
      the local name when viewing admin cluster controller logs.
    name: Immutable. The VMware user cluster resource name.
    networkConfig: The VMware user cluster network configuration.
    onPremVersion: Required. The Anthos clusters on the VMware version for
      your user cluster.
    reconciling: Output only. If set, there are currently changes in flight to
      the VMware user cluster.
    state: Output only. The current state of VMware user cluster.
    status: Output only. ResourceStatus representing detailed cluster state.
    storage: Storage configuration.
    uid: Output only. The unique identifier of the VMware user cluster.
    updateTime: Output only. The time at which VMware user cluster was last
      updated.
    upgradePolicy: Specifies upgrade policy for the cluster.
    validationCheck: Output only. ValidationCheck represents the result of the
      preflight check job.
    vcenter: VmwareVCenterConfig specifies vCenter config for the user
      cluster. If unspecified, it is inherited from the admin cluster.
    vmTrackingEnabled: Enable VM tracking.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of VMware user cluster.

    Values:
      STATE_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the cluster is being
        created.
      RUNNING: The RUNNING state indicates the cluster has been created and is
        fully usable.
      RECONCILING: The RECONCILING state indicates that the cluster is being
        updated. It remains available, but potentially with degraded
        performance.
      STOPPING: The STOPPING state indicates the cluster is being deleted.
      ERROR: The ERROR state indicates the cluster is in a broken
        unrecoverable state.
      DEGRADED: The DEGRADED state indicates the cluster requires user action
        to restore full functionality.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RECONCILING = 3
    STOPPING = 4
    ERROR = 5
    DEGRADED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations on the VMware user cluster. This field has the same
    restrictions as Kubernetes annotations. The total size of all keys and
    values combined is limited to 256k. Key can have 2 segments: prefix
    (optional) and name (required), separated by a slash (/). Prefix must be a
    DNS subdomain. Name must be 63 characters or less, begin and end with
    alphanumerics, with dashes (-), underscores (_), dots (.), and
    alphanumerics between.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  adminClusterMembership = _messages.StringField(1)
  adminClusterName = _messages.StringField(2)
  annotations = _messages.MessageField('AnnotationsValue', 3)
  antiAffinityGroups = _messages.MessageField('VmwareAAGConfig', 4)
  authorization = _messages.MessageField('Authorization', 5)
  autoRepairConfig = _messages.MessageField('VmwareAutoRepairConfig', 6)
  binaryAuthorization = _messages.MessageField('BinaryAuthorization', 7)
  controlPlaneNode = _messages.MessageField('VmwareControlPlaneNodeConfig', 8)
  createTime = _messages.StringField(9)
  dataplaneV2 = _messages.MessageField('VmwareDataplaneV2Config', 10)
  deleteTime = _messages.StringField(11)
  description = _messages.StringField(12)
  disableBundledIngress = _messages.BooleanField(13)
  enableAdvancedCluster = _messages.BooleanField(14)
  enableControlPlaneV2 = _messages.BooleanField(15)
  endpoint = _messages.StringField(16)
  etag = _messages.StringField(17)
  fleet = _messages.MessageField('Fleet', 18)
  loadBalancer = _messages.MessageField('VmwareLoadBalancerConfig', 19)
  localName = _messages.StringField(20)
  name = _messages.StringField(21)
  networkConfig = _messages.MessageField('VmwareNetworkConfig', 22)
  onPremVersion = _messages.StringField(23)
  reconciling = _messages.BooleanField(24)
  state = _messages.EnumField('StateValueValuesEnum', 25)
  status = _messages.MessageField('ResourceStatus', 26)
  storage = _messages.MessageField('VmwareStorageConfig', 27)
  uid = _messages.StringField(28)
  updateTime = _messages.StringField(29)
  upgradePolicy = _messages.MessageField('VmwareClusterUpgradePolicy', 30)
  validationCheck = _messages.MessageField('ValidationCheck', 31)
  vcenter = _messages.MessageField('VmwareVCenterConfig', 32)
  vmTrackingEnabled = _messages.BooleanField(33)


class VmwareClusterUpgradePolicy(_messages.Message):
  r"""VmwareClusterUpgradePolicy defines the cluster upgrade policy.

  Fields:
    controlPlaneOnly: Controls whether the upgrade applies to the control
      plane only.
  """

  controlPlaneOnly = _messages.BooleanField(1)


class VmwareControlPlaneNodeConfig(_messages.Message):
  r"""Specifies control plane node config for the VMware user cluster.

  Fields:
    autoResizeConfig: AutoResizeConfig provides auto resizing configurations.
    cpus: The number of CPUs for each admin cluster node that serve as control
      planes for this VMware user cluster. (default: 4 CPUs)
    memory: The megabytes of memory for each admin cluster node that serves as
      a control plane for this VMware user cluster (default: 8192 MB memory).
    replicas: The number of control plane nodes for this VMware user cluster.
      (default: 1 replica).
    vsphereConfig: Vsphere-specific config.
  """

  autoResizeConfig = _messages.MessageField('VmwareAutoResizeConfig', 1)
  cpus = _messages.IntegerField(2)
  memory = _messages.IntegerField(3)
  replicas = _messages.IntegerField(4)
  vsphereConfig = _messages.MessageField('VmwareControlPlaneVsphereConfig', 5)


class VmwareControlPlaneV2Config(_messages.Message):
  r"""Specifies control plane V2 config.

  Fields:
    controlPlaneIpBlock: Static IP addresses for the control plane nodes.
  """

  controlPlaneIpBlock = _messages.MessageField('VmwareIpBlock', 1)


class VmwareControlPlaneVsphereConfig(_messages.Message):
  r"""Specifies control plane node config.

  Fields:
    datastore: The Vsphere datastore used by the control plane Node.
    storagePolicyName: The Vsphere storage policy used by the control plane
      Node.
  """

  datastore = _messages.StringField(1)
  storagePolicyName = _messages.StringField(2)


class VmwareDataplaneV2Config(_messages.Message):
  r"""Contains configurations for Dataplane V2, which is optimized dataplane
  for Kubernetes networking. For more information, see:
  https://cloud.google.com/kubernetes-engine/docs/concepts/dataplane-v2

  Fields:
    advancedNetworking: Enable advanced networking which requires
      dataplane_v2_enabled to be set true.
    dataplaneV2Enabled: Enables Dataplane V2.
    forwardMode: Configure ForwardMode for Dataplane v2.
    windowsDataplaneV2Enabled: Enable Dataplane V2 for clusters with Windows
      nodes.
  """

  advancedNetworking = _messages.BooleanField(1)
  dataplaneV2Enabled = _messages.BooleanField(2)
  forwardMode = _messages.StringField(3)
  windowsDataplaneV2Enabled = _messages.BooleanField(4)


class VmwareDhcpIpConfig(_messages.Message):
  r"""Represents the network configuration required for the VMware user
  clusters with DHCP IP configurations.

  Fields:
    enabled: enabled is a flag to mark if DHCP IP allocation is used for
      VMware user clusters.
  """

  enabled = _messages.BooleanField(1)


class VmwareF5BigIpConfig(_messages.Message):
  r"""Represents configuration parameters for an F5 BIG-IP load balancer.

  Fields:
    address: The load balancer's IP address.
    partition: The preexisting partition to be used by the load balancer. This
      partition is usually created for the admin cluster for example:
      'my-f5-admin-partition'.
    snatPool: The pool name. Only necessary, if using SNAT.
  """

  address = _messages.StringField(1)
  partition = _messages.StringField(2)
  snatPool = _messages.StringField(3)


class VmwareHostConfig(_messages.Message):
  r"""Represents the common parameters for all the hosts irrespective of their
  IP address.

  Fields:
    dnsSearchDomains: DNS search domains.
    dnsServers: DNS servers.
    ntpServers: NTP servers.
  """

  dnsSearchDomains = _messages.StringField(1, repeated=True)
  dnsServers = _messages.StringField(2, repeated=True)
  ntpServers = _messages.StringField(3, repeated=True)


class VmwareHostIp(_messages.Message):
  r"""Represents VMware user cluster node's network configuration.

  Fields:
    hostname: Hostname of the machine. VM's name will be used if this field is
      empty.
    ip: IP could be an IP address (like *******) or a CIDR (like *******/24).
  """

  hostname = _messages.StringField(1)
  ip = _messages.StringField(2)


class VmwareIpBlock(_messages.Message):
  r"""Represents a collection of IP addresses to assign to nodes.

  Fields:
    gateway: The network gateway used by the VMware user cluster.
    ips: The node's network configurations used by the VMware user cluster.
    netmask: The netmask used by the VMware user cluster.
  """

  gateway = _messages.StringField(1)
  ips = _messages.MessageField('VmwareHostIp', 2, repeated=True)
  netmask = _messages.StringField(3)


class VmwareLoadBalancerConfig(_messages.Message):
  r"""Specifies the locad balancer config for the VMware user cluster.

  Fields:
    f5Config: Configuration for F5 Big IP typed load balancers.
    manualLbConfig: Manually configured load balancers.
    metalLbConfig: Configuration for MetalLB typed load balancers.
    seesawConfig: Output only. Configuration for Seesaw typed load balancers.
    vipConfig: The VIPs used by the load balancer.
  """

  f5Config = _messages.MessageField('VmwareF5BigIpConfig', 1)
  manualLbConfig = _messages.MessageField('VmwareManualLbConfig', 2)
  metalLbConfig = _messages.MessageField('VmwareMetalLbConfig', 3)
  seesawConfig = _messages.MessageField('VmwareSeesawConfig', 4)
  vipConfig = _messages.MessageField('VmwareVipConfig', 5)


class VmwareManualLbConfig(_messages.Message):
  r"""Represents configuration parameters for an already existing manual load
  balancer. Given the nature of manual load balancers it is expected that said
  load balancer will be fully managed by users. IMPORTANT: Please note that
  the Anthos On-Prem API will not generate or update ManualLB configurations
  it can only bind a pre-existing configuration to a new VMware user cluster.

  Fields:
    controlPlaneNodePort: NodePort for control plane service. The Kubernetes
      API server in the admin cluster is implemented as a Service of type
      NodePort (ex. 30968).
    ingressHttpNodePort: NodePort for ingress service's http. The ingress
      service in the admin cluster is implemented as a Service of type
      NodePort (ex. 32527).
    ingressHttpsNodePort: NodePort for ingress service's https. The ingress
      service in the admin cluster is implemented as a Service of type
      NodePort (ex. 30139).
    konnectivityServerNodePort: NodePort for konnectivity server service
      running as a sidecar in each kube-apiserver pod (ex. 30564).
  """

  controlPlaneNodePort = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  ingressHttpNodePort = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  ingressHttpsNodePort = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  konnectivityServerNodePort = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class VmwareMetalLbConfig(_messages.Message):
  r"""Represents configuration parameters for the MetalLB load balancer.

  Fields:
    addressPools: Required. AddressPools is a list of non-overlapping IP pools
      used by load balancer typed services. All addresses must be routable to
      load balancer nodes. IngressVIP must be included in the pools.
  """

  addressPools = _messages.MessageField('VmwareAddressPool', 1, repeated=True)


class VmwareNetworkConfig(_messages.Message):
  r"""Specifies network config for the VMware user cluster.

  Fields:
    controlPlaneV2Config: Configuration for control plane V2 mode.
    dhcpIpConfig: Configuration settings for a DHCP IP configuration.
    hostConfig: Represents common network settings irrespective of the host's
      IP address.
    podAddressCidrBlocks: Required. All pods in the cluster are assigned an
      RFC1918 IPv4 address from these ranges. Only a single range is
      supported. This field cannot be changed after creation.
    serviceAddressCidrBlocks: Required. All services in the cluster are
      assigned an RFC1918 IPv4 address from these ranges. Only a single range
      is supported. This field cannot be changed after creation.
    staticIpConfig: Configuration settings for a static IP configuration.
    vcenterNetwork: vcenter_network specifies vCenter network name. Inherited
      from the admin cluster.
  """

  controlPlaneV2Config = _messages.MessageField('VmwareControlPlaneV2Config', 1)
  dhcpIpConfig = _messages.MessageField('VmwareDhcpIpConfig', 2)
  hostConfig = _messages.MessageField('VmwareHostConfig', 3)
  podAddressCidrBlocks = _messages.StringField(4, repeated=True)
  serviceAddressCidrBlocks = _messages.StringField(5, repeated=True)
  staticIpConfig = _messages.MessageField('VmwareStaticIpConfig', 6)
  vcenterNetwork = _messages.StringField(7)


class VmwareNodeConfig(_messages.Message):
  r"""Parameters that describe the configuration of all nodes within a given
  node pool.

  Messages:
    LabelsValue: The map of Kubernetes labels (key/value pairs) to be applied
      to each node. These will added in addition to any default label(s) that
      Kubernetes may apply to the node. In case of conflict in label keys, the
      applied set may differ depending on the Kubernetes version -- it's best
      to assume the behavior is undefined and conflicts should be avoided. For
      more information, including usage and the valid values, see:
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/labels/

  Fields:
    bootDiskSizeGb: VMware disk size to be used during creation.
    cpus: The number of CPUs for each node in the node pool.
    enableLoadBalancer: Allow node pool traffic to be load balanced. Only
      works for clusters with MetalLB load balancers.
    image: The OS image name in vCenter, only valid when using Windows.
    imageType: Required. The OS image to be used for each node in a node pool.
      Currently `cos`, `cos_cgv2`, `ubuntu`, `ubuntu_cgv2`,
      `ubuntu_containerd` and `windows` are supported.
    labels: The map of Kubernetes labels (key/value pairs) to be applied to
      each node. These will added in addition to any default label(s) that
      Kubernetes may apply to the node. In case of conflict in label keys, the
      applied set may differ depending on the Kubernetes version -- it's best
      to assume the behavior is undefined and conflicts should be avoided. For
      more information, including usage and the valid values, see:
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/labels/
    memoryMb: The megabytes of memory for each node in the node pool.
    replicas: The number of nodes in the node pool.
    taints: The initial taints assigned to nodes of this node pool.
    vsphereConfig: Specifies the vSphere config for node pool.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The map of Kubernetes labels (key/value pairs) to be applied to each
    node. These will added in addition to any default label(s) that Kubernetes
    may apply to the node. In case of conflict in label keys, the applied set
    may differ depending on the Kubernetes version -- it's best to assume the
    behavior is undefined and conflicts should be avoided. For more
    information, including usage and the valid values, see:
    https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bootDiskSizeGb = _messages.IntegerField(1)
  cpus = _messages.IntegerField(2)
  enableLoadBalancer = _messages.BooleanField(3)
  image = _messages.StringField(4)
  imageType = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  memoryMb = _messages.IntegerField(7)
  replicas = _messages.IntegerField(8)
  taints = _messages.MessageField('NodeTaint', 9, repeated=True)
  vsphereConfig = _messages.MessageField('VmwareVsphereConfig', 10)


class VmwareNodePool(_messages.Message):
  r"""Resource VmwareNodePool represents a VMware node pool. ##

  Enums:
    StateValueValuesEnum: Output only. The current state of the node pool.

  Messages:
    AnnotationsValue: Annotations on the node pool. This field has the same
      restrictions as Kubernetes annotations. The total size of all keys and
      values combined is limited to 256k. Key can have 2 segments: prefix
      (optional) and name (required), separated by a slash (/). Prefix must be
      a DNS subdomain. Name must be 63 characters or less, begin and end with
      alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.

  Fields:
    annotations: Annotations on the node pool. This field has the same
      restrictions as Kubernetes annotations. The total size of all keys and
      values combined is limited to 256k. Key can have 2 segments: prefix
      (optional) and name (required), separated by a slash (/). Prefix must be
      a DNS subdomain. Name must be 63 characters or less, begin and end with
      alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.
    config: Required. The node configuration of the node pool.
    createTime: Output only. The time at which this node pool was created.
    deleteTime: Output only. The time at which this node pool was deleted. If
      the resource is not deleted, this must be empty
    displayName: The display name for the node pool.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding. Allows clients to
      perform consistent read-modify-writes through optimistic concurrency
      control.
    name: Immutable. The resource name of this node pool.
    nodePoolAutoscaling: Node pool autoscaling config for the node pool.
    onPremVersion: Anthos version for the node pool. Defaults to the user
      cluster version.
    reconciling: Output only. If set, there are currently changes in flight to
      the node pool.
    state: Output only. The current state of the node pool.
    status: Output only. ResourceStatus representing the detailed VMware node
      pool state.
    uid: Output only. The unique identifier of the node pool.
    updateTime: Output only. The time at which this node pool was last
      updated.
    upgradePolicy: Upgrade policy for the node pool.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the node pool.

    Values:
      STATE_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the node pool is being
        created.
      RUNNING: The RUNNING state indicates the node pool has been created and
        is fully usable.
      RECONCILING: The RECONCILING state indicates that the node pool is being
        updated. It remains available, but potentially with degraded
        performance.
      STOPPING: The STOPPING state indicates the cluster is being deleted
      ERROR: The ERROR state indicates the node pool is in a broken
        unrecoverable state.
      DEGRADED: The DEGRADED state indicates the node pool requires user
        action to restore full functionality.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RECONCILING = 3
    STOPPING = 4
    ERROR = 5
    DEGRADED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations on the node pool. This field has the same restrictions as
    Kubernetes annotations. The total size of all keys and values combined is
    limited to 256k. Key can have 2 segments: prefix (optional) and name
    (required), separated by a slash (/). Prefix must be a DNS subdomain. Name
    must be 63 characters or less, begin and end with alphanumerics, with
    dashes (-), underscores (_), dots (.), and alphanumerics between.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  config = _messages.MessageField('VmwareNodeConfig', 2)
  createTime = _messages.StringField(3)
  deleteTime = _messages.StringField(4)
  displayName = _messages.StringField(5)
  etag = _messages.StringField(6)
  name = _messages.StringField(7)
  nodePoolAutoscaling = _messages.MessageField('VmwareNodePoolAutoscalingConfig', 8)
  onPremVersion = _messages.StringField(9)
  reconciling = _messages.BooleanField(10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  status = _messages.MessageField('ResourceStatus', 12)
  uid = _messages.StringField(13)
  updateTime = _messages.StringField(14)
  upgradePolicy = _messages.MessageField('VmwareNodePoolUpgradePolicy', 15)


class VmwareNodePoolAutoscalingConfig(_messages.Message):
  r"""NodePoolAutoscaling config for the NodePool to allow for the kubernetes
  to scale NodePool.

  Fields:
    maxReplicas: Maximum number of replicas in the NodePool.
    minReplicas: Minimum number of replicas in the NodePool.
  """

  maxReplicas = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minReplicas = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class VmwareNodePoolUpgradePolicy(_messages.Message):
  r"""Parameters that describe the upgrade policy for the node pool.

  Fields:
    independent: Specify the intent to upgrade the node pool with or without
      the control plane upgrade. Defaults to false i.e. upgrade the node pool
      with control plane upgrade. Set this to true to upgrade or downgrade the
      node pool independently from the control plane.
  """

  independent = _messages.BooleanField(1)


class VmwarePlatformConfig(_messages.Message):
  r"""VmwarePlatformConfig represents configuration for the VMware platform.

  Fields:
    bundles: Output only. The list of bundles installed in the admin cluster.
    platformVersion: Output only. The platform version e.g. 1.13.2.
    requiredPlatformVersion: Input only. The required platform version e.g.
      1.13.1. If the current platform version is lower than the target
      version, the platform version will be updated to the target version. If
      the target version is not installed in the platform (bundle versions),
      download the target version bundle.
    status: Output only. Resource status for the platform.
  """

  bundles = _messages.MessageField('VmwareBundleConfig', 1, repeated=True)
  platformVersion = _messages.StringField(2)
  requiredPlatformVersion = _messages.StringField(3)
  status = _messages.MessageField('ResourceStatus', 4)


class VmwareSeesawConfig(_messages.Message):
  r"""VmwareSeesawConfig represents configuration parameters for an already
  existing Seesaw load balancer. IMPORTANT: Please note that the Anthos On-
  Prem API will not generate or update Seesaw configurations it can only bind
  a pre-existing configuration to a new user cluster. IMPORTANT: When
  attempting to create a user cluster with a pre-existing Seesaw load balancer
  you will need to follow some preparation steps before calling the
  'CreateVmwareCluster' API method. First you will need to create the user
  cluster's namespace via kubectl. The namespace will need to use the
  following naming convention : -gke-onprem-mgmt or -gke-onprem-mgmt depending
  on whether you used the 'VmwareCluster.local_name' to disambiguate
  collisions; for more context see the documentation of
  'VmwareCluster.local_name'. Once the namespace is created you will need to
  create a secret resource via kubectl. This secret will contain copies of
  your Seesaw credentials. The Secret must be called 'user-cluster-creds' and
  contain Seesaw's SSH and Cert credentials. The credentials must be keyed
  with the following names: 'seesaw-ssh-private-key', 'seesaw-ssh-public-key',
  'seesaw-ssh-ca-key', 'seesaw-ssh-ca-cert'.

  Fields:
    enableHa: Enable two load balancer VMs to achieve a highly-available
      Seesaw load balancer.
    group: Required. In general the following format should be used for the
      Seesaw group name: seesaw-for-[cluster_name].
    ipBlocks: Required. The IP Blocks to be used by the Seesaw load balancer
    masterIp: Required. MasterIP is the IP announced by the master of Seesaw
      group.
    stackdriverName: Name to be used by Stackdriver.
    vms: Names of the VMs created for this Seesaw group.
  """

  enableHa = _messages.BooleanField(1)
  group = _messages.StringField(2)
  ipBlocks = _messages.MessageField('VmwareIpBlock', 3, repeated=True)
  masterIp = _messages.StringField(4)
  stackdriverName = _messages.StringField(5)
  vms = _messages.StringField(6, repeated=True)


class VmwareStaticIpConfig(_messages.Message):
  r"""Represents the network configuration required for the VMware user
  clusters with Static IP configurations.

  Fields:
    ipBlocks: Represents the configuration values for static IP allocation to
      nodes.
  """

  ipBlocks = _messages.MessageField('VmwareIpBlock', 1, repeated=True)


class VmwareStorageConfig(_messages.Message):
  r"""Specifies vSphere CSI components deployment config in the VMware user
  cluster.

  Fields:
    vsphereCsiDisabled: Whether or not to deploy vSphere CSI components in the
      VMware user cluster. Enabled by default.
  """

  vsphereCsiDisabled = _messages.BooleanField(1)


class VmwareVCenterConfig(_messages.Message):
  r"""Represents configuration for the VMware VCenter for the user cluster.

  Fields:
    address: Output only. The vCenter IP address.
    caCertData: Contains the vCenter CA certificate public key for SSL
      verification.
    cluster: The name of the vCenter cluster for the user cluster.
    datacenter: The name of the vCenter datacenter for the user cluster.
    datastore: The name of the vCenter datastore for the user cluster.
    folder: The name of the vCenter folder for the user cluster.
    resourcePool: The name of the vCenter resource pool for the user cluster.
    storagePolicyName: The name of the vCenter storage policy for the user
      cluster.
  """

  address = _messages.StringField(1)
  caCertData = _messages.StringField(2)
  cluster = _messages.StringField(3)
  datacenter = _messages.StringField(4)
  datastore = _messages.StringField(5)
  folder = _messages.StringField(6)
  resourcePool = _messages.StringField(7)
  storagePolicyName = _messages.StringField(8)


class VmwareVersionInfo(_messages.Message):
  r"""Contains information about a specific Anthos on VMware version.

  Fields:
    dependencies: The list of upgrade dependencies for this version.
    hasDependencies: If set, the cluster dependencies (e.g. the admin cluster,
      other user clusters managed by the same admin cluster) must be upgraded
      before this version can be installed or upgraded to.
    isInstalled: If set, the version is installed in the admin cluster.
      Otherwise, the version bundle must be downloaded and installed before a
      user cluster can be created at or upgraded to this version.
    version: Version number e.g. 1.13.1-gke.1000.
  """

  dependencies = _messages.MessageField('UpgradeDependency', 1, repeated=True)
  hasDependencies = _messages.BooleanField(2)
  isInstalled = _messages.BooleanField(3)
  version = _messages.StringField(4)


class VmwareVipConfig(_messages.Message):
  r"""Specifies the VIP config for the VMware user cluster load balancer.

  Fields:
    controlPlaneVip: The VIP which you previously set aside for the Kubernetes
      API of this cluster.
    ingressVip: The VIP which you previously set aside for ingress traffic
      into this cluster.
  """

  controlPlaneVip = _messages.StringField(1)
  ingressVip = _messages.StringField(2)


class VmwareVsphereConfig(_messages.Message):
  r"""VmwareVsphereConfig represents configuration for the VMware VCenter for
  node pool.

  Fields:
    datastore: The name of the vCenter datastore. Inherited from the user
      cluster.
    hostGroups: Vsphere host groups to apply to all VMs in the node pool
    storagePolicyName: The name of the vCenter storage policy. Inherited from
      the user cluster.
    tags: Tags to apply to VMs.
  """

  datastore = _messages.StringField(1)
  hostGroups = _messages.StringField(2, repeated=True)
  storagePolicyName = _messages.StringField(3)
  tags = _messages.MessageField('VmwareVsphereTag', 4, repeated=True)


class VmwareVsphereTag(_messages.Message):
  r"""VmwareVsphereTag describes a vSphere tag to be placed on VMs in the node
  pool. For more information, see https://docs.vmware.com/en/VMware-
  vSphere/7.0/com.vmware.vsphere.vcenterhost.doc/GUID-E8E854DD-
  AA97-4E0C-8419-CE84F93C4058.html

  Fields:
    category: The Vsphere tag category.
    tag: The Vsphere tag name.
  """

  category = _messages.StringField(1)
  tag = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    GkeonpremProjectsLocationsBareMetalAdminClustersGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    GkeonpremProjectsLocationsBareMetalAdminClustersQueryVersionConfigRequest, 'upgradeConfig_clusterName', 'upgradeConfig.clusterName')
encoding.AddCustomJsonFieldMapping(
    GkeonpremProjectsLocationsBareMetalClustersGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    GkeonpremProjectsLocationsBareMetalClustersQueryVersionConfigRequest, 'createConfig_adminClusterMembership', 'createConfig.adminClusterMembership')
encoding.AddCustomJsonFieldMapping(
    GkeonpremProjectsLocationsBareMetalClustersQueryVersionConfigRequest, 'createConfig_adminClusterName', 'createConfig.adminClusterName')
encoding.AddCustomJsonFieldMapping(
    GkeonpremProjectsLocationsBareMetalClustersQueryVersionConfigRequest, 'upgradeConfig_clusterName', 'upgradeConfig.clusterName')
encoding.AddCustomJsonFieldMapping(
    GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    GkeonpremProjectsLocationsBareMetalStandaloneClustersQueryVersionConfigRequest, 'upgradeConfig_clusterName', 'upgradeConfig.clusterName')
encoding.AddCustomJsonFieldMapping(
    GkeonpremProjectsLocationsVmwareAdminClustersGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    GkeonpremProjectsLocationsVmwareClustersGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    GkeonpremProjectsLocationsVmwareClustersQueryVersionConfigRequest, 'createConfig_adminClusterMembership', 'createConfig.adminClusterMembership')
encoding.AddCustomJsonFieldMapping(
    GkeonpremProjectsLocationsVmwareClustersQueryVersionConfigRequest, 'createConfig_adminClusterName', 'createConfig.adminClusterName')
encoding.AddCustomJsonFieldMapping(
    GkeonpremProjectsLocationsVmwareClustersQueryVersionConfigRequest, 'upgradeConfig_clusterName', 'upgradeConfig.clusterName')
encoding.AddCustomJsonFieldMapping(
    GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
