"""Generated client library for logging version v2."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.logging.v2 import logging_v2_messages as messages


class LoggingV2(base_api.BaseApiClient):
  """Generated client library for service logging version v2."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://logging.googleapis.com/'
  MTLS_BASE_URL = 'https://logging.mtls.googleapis.com/'

  _PACKAGE = 'logging'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/cloud-platform.read-only', 'https://www.googleapis.com/auth/logging.admin', 'https://www.googleapis.com/auth/logging.read', 'https://www.googleapis.com/auth/logging.write']
  _VERSION = 'v2'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'LoggingV2'
  _URL_VERSION = 'v2'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new logging handle."""
    url = url or self.BASE_URL
    super(LoggingV2, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.billingAccounts_exclusions = self.BillingAccountsExclusionsService(self)
    self.billingAccounts_locations_buckets_links = self.BillingAccountsLocationsBucketsLinksService(self)
    self.billingAccounts_locations_buckets_views_logs = self.BillingAccountsLocationsBucketsViewsLogsService(self)
    self.billingAccounts_locations_buckets_views = self.BillingAccountsLocationsBucketsViewsService(self)
    self.billingAccounts_locations_buckets = self.BillingAccountsLocationsBucketsService(self)
    self.billingAccounts_locations_operations = self.BillingAccountsLocationsOperationsService(self)
    self.billingAccounts_locations_recentQueries = self.BillingAccountsLocationsRecentQueriesService(self)
    self.billingAccounts_locations_savedQueries = self.BillingAccountsLocationsSavedQueriesService(self)
    self.billingAccounts_locations = self.BillingAccountsLocationsService(self)
    self.billingAccounts_logs = self.BillingAccountsLogsService(self)
    self.billingAccounts_sinks = self.BillingAccountsSinksService(self)
    self.billingAccounts = self.BillingAccountsService(self)
    self.data = self.DataService(self)
    self.entries = self.EntriesService(self)
    self.exclusions = self.ExclusionsService(self)
    self.folders_exclusions = self.FoldersExclusionsService(self)
    self.folders_locations_buckets_links = self.FoldersLocationsBucketsLinksService(self)
    self.folders_locations_buckets_views_logs = self.FoldersLocationsBucketsViewsLogsService(self)
    self.folders_locations_buckets_views = self.FoldersLocationsBucketsViewsService(self)
    self.folders_locations_buckets = self.FoldersLocationsBucketsService(self)
    self.folders_locations_logScopes = self.FoldersLocationsLogScopesService(self)
    self.folders_locations_operations = self.FoldersLocationsOperationsService(self)
    self.folders_locations_recentQueries = self.FoldersLocationsRecentQueriesService(self)
    self.folders_locations_savedQueries = self.FoldersLocationsSavedQueriesService(self)
    self.folders_locations = self.FoldersLocationsService(self)
    self.folders_logs = self.FoldersLogsService(self)
    self.folders_sinks = self.FoldersSinksService(self)
    self.folders = self.FoldersService(self)
    self.locations_buckets_links = self.LocationsBucketsLinksService(self)
    self.locations_buckets_views = self.LocationsBucketsViewsService(self)
    self.locations_buckets = self.LocationsBucketsService(self)
    self.locations_operations = self.LocationsOperationsService(self)
    self.locations = self.LocationsService(self)
    self.logs = self.LogsService(self)
    self.monitoredResourceDescriptors = self.MonitoredResourceDescriptorsService(self)
    self.organizations_exclusions = self.OrganizationsExclusionsService(self)
    self.organizations_locations_buckets_links = self.OrganizationsLocationsBucketsLinksService(self)
    self.organizations_locations_buckets_views_logs = self.OrganizationsLocationsBucketsViewsLogsService(self)
    self.organizations_locations_buckets_views = self.OrganizationsLocationsBucketsViewsService(self)
    self.organizations_locations_buckets = self.OrganizationsLocationsBucketsService(self)
    self.organizations_locations_logScopes = self.OrganizationsLocationsLogScopesService(self)
    self.organizations_locations_operations = self.OrganizationsLocationsOperationsService(self)
    self.organizations_locations_recentQueries = self.OrganizationsLocationsRecentQueriesService(self)
    self.organizations_locations_savedQueries = self.OrganizationsLocationsSavedQueriesService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations_logs = self.OrganizationsLogsService(self)
    self.organizations_sinks = self.OrganizationsSinksService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_exclusions = self.ProjectsExclusionsService(self)
    self.projects_locations_buckets_links = self.ProjectsLocationsBucketsLinksService(self)
    self.projects_locations_buckets_views_logs = self.ProjectsLocationsBucketsViewsLogsService(self)
    self.projects_locations_buckets_views = self.ProjectsLocationsBucketsViewsService(self)
    self.projects_locations_buckets = self.ProjectsLocationsBucketsService(self)
    self.projects_locations_logScopes = self.ProjectsLocationsLogScopesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_recentQueries = self.ProjectsLocationsRecentQueriesService(self)
    self.projects_locations_savedQueries = self.ProjectsLocationsSavedQueriesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects_logs = self.ProjectsLogsService(self)
    self.projects_metrics = self.ProjectsMetricsService(self)
    self.projects_sinks = self.ProjectsSinksService(self)
    self.projects = self.ProjectsService(self)
    self.query = self.QueryService(self)
    self.sinks = self.SinksService(self)
    self.v2 = self.V2Service(self)

  class BillingAccountsExclusionsService(base_api.BaseApiService):
    """Service class for the billingAccounts_exclusions resource."""

    _NAME = 'billingAccounts_exclusions'

    def __init__(self, client):
      super(LoggingV2.BillingAccountsExclusionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new exclusion in the _Default sink in a specified parent resource. Only log entries belonging to that resource can be excluded. You can have up to 10 exclusions in a resource.

      Args:
        request: (LoggingBillingAccountsExclusionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/exclusions',
        http_method='POST',
        method_id='logging.billingAccounts.exclusions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/exclusions',
        request_field='logExclusion',
        request_type_name='LoggingBillingAccountsExclusionsCreateRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an exclusion in the _Default sink.

      Args:
        request: (LoggingBillingAccountsExclusionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/exclusions/{exclusionsId}',
        http_method='DELETE',
        method_id='logging.billingAccounts.exclusions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingBillingAccountsExclusionsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the description of an exclusion in the _Default sink.

      Args:
        request: (LoggingBillingAccountsExclusionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/exclusions/{exclusionsId}',
        http_method='GET',
        method_id='logging.billingAccounts.exclusions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingBillingAccountsExclusionsGetRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the exclusions on the _Default sink in a parent resource.

      Args:
        request: (LoggingBillingAccountsExclusionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListExclusionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/exclusions',
        http_method='GET',
        method_id='logging.billingAccounts.exclusions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/exclusions',
        request_field='',
        request_type_name='LoggingBillingAccountsExclusionsListRequest',
        response_type_name='ListExclusionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Changes one or more properties of an existing exclusion in the _Default sink.

      Args:
        request: (LoggingBillingAccountsExclusionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/exclusions/{exclusionsId}',
        http_method='PATCH',
        method_id='logging.billingAccounts.exclusions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logExclusion',
        request_type_name='LoggingBillingAccountsExclusionsPatchRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

  class BillingAccountsLocationsBucketsLinksService(base_api.BaseApiService):
    """Service class for the billingAccounts_locations_buckets_links resource."""

    _NAME = 'billingAccounts_locations_buckets_links'

    def __init__(self, client):
      super(LoggingV2.BillingAccountsLocationsBucketsLinksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Asynchronously creates a linked dataset in BigQuery which makes it possible to use BigQuery to read the logs stored in the log bucket. A log bucket may currently only contain one link.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsLinksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}/links',
        http_method='POST',
        method_id='logging.billingAccounts.locations.buckets.links.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['linkId'],
        relative_path='v2/{+parent}/links',
        request_field='link',
        request_type_name='LoggingBillingAccountsLocationsBucketsLinksCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a link. This will also delete the corresponding BigQuery linked dataset.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsLinksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}/links/{linksId}',
        http_method='DELETE',
        method_id='logging.billingAccounts.locations.buckets.links.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsBucketsLinksDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a link.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsLinksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Link) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}/links/{linksId}',
        http_method='GET',
        method_id='logging.billingAccounts.locations.buckets.links.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsBucketsLinksGetRequest',
        response_type_name='Link',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists links.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsLinksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLinksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}/links',
        http_method='GET',
        method_id='logging.billingAccounts.locations.buckets.links.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/links',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsBucketsLinksListRequest',
        response_type_name='ListLinksResponse',
        supports_download=False,
    )

  class BillingAccountsLocationsBucketsViewsLogsService(base_api.BaseApiService):
    """Service class for the billingAccounts_locations_buckets_views_logs resource."""

    _NAME = 'billingAccounts_locations_buckets_views_logs'

    def __init__(self, client):
      super(LoggingV2.BillingAccountsLocationsBucketsViewsLogsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the logs in projects, organizations, folders, or billing accounts. Only logs that have entries are listed.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsViewsLogsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}/logs',
        http_method='GET',
        method_id='logging.billingAccounts.locations.buckets.views.logs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'resourceNames'],
        relative_path='v2/{+parent}/logs',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsBucketsViewsLogsListRequest',
        response_type_name='ListLogsResponse',
        supports_download=False,
    )

  class BillingAccountsLocationsBucketsViewsService(base_api.BaseApiService):
    """Service class for the billingAccounts_locations_buckets_views resource."""

    _NAME = 'billingAccounts_locations_buckets_views'

    def __init__(self, client):
      super(LoggingV2.BillingAccountsLocationsBucketsViewsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a view over log entries in a log bucket. A bucket may contain a maximum of 30 views.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsViewsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}/views',
        http_method='POST',
        method_id='logging.billingAccounts.locations.buckets.views.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['viewId'],
        relative_path='v2/{+parent}/views',
        request_field='logView',
        request_type_name='LoggingBillingAccountsLocationsBucketsViewsCreateRequest',
        response_type_name='LogView',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a view on a log bucket. If an UNAVAILABLE error is returned, this indicates that system is not in a state where it can delete the view. If this occurs, please try again in a few minutes.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsViewsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='DELETE',
        method_id='logging.billingAccounts.locations.buckets.views.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsBucketsViewsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a view on a log bucket.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsViewsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='GET',
        method_id='logging.billingAccounts.locations.buckets.views.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsBucketsViewsGetRequest',
        response_type_name='LogView',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists views on a log bucket.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsViewsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListViewsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}/views',
        http_method='GET',
        method_id='logging.billingAccounts.locations.buckets.views.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/views',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsBucketsViewsListRequest',
        response_type_name='ListViewsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a view on a log bucket. This method replaces the value of the filter field from the existing view with the corresponding value from the new view. If an UNAVAILABLE error is returned, this indicates that system is not in a state where it can update the view. If this occurs, please try again in a few minutes.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsViewsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='PATCH',
        method_id='logging.billingAccounts.locations.buckets.views.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logView',
        request_type_name='LoggingBillingAccountsLocationsBucketsViewsPatchRequest',
        response_type_name='LogView',
        supports_download=False,
    )

  class BillingAccountsLocationsBucketsService(base_api.BaseApiService):
    """Service class for the billingAccounts_locations_buckets resource."""

    _NAME = 'billingAccounts_locations_buckets'

    def __init__(self, client):
      super(LoggingV2.BillingAccountsLocationsBucketsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a log bucket that can be used to store log entries. After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets',
        http_method='POST',
        method_id='logging.billingAccounts.locations.buckets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bucketId'],
        relative_path='v2/{+parent}/buckets',
        request_field='logBucket',
        request_type_name='LoggingBillingAccountsLocationsBucketsCreateRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def CreateAsync(self, request, global_params=None):
      r"""Creates a log bucket asynchronously that can be used to store log entries.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsCreateAsyncRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CreateAsync')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateAsync.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets:createAsync',
        http_method='POST',
        method_id='logging.billingAccounts.locations.buckets.createAsync',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bucketId'],
        relative_path='v2/{+parent}/buckets:createAsync',
        request_field='logBucket',
        request_type_name='LoggingBillingAccountsLocationsBucketsCreateAsyncRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a log bucket.Changes the bucket's lifecycle_state to the DELETE_REQUESTED state. After 7 days, the bucket will be purged and all log entries in the bucket will be permanently deleted.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='DELETE',
        method_id='logging.billingAccounts.locations.buckets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsBucketsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a log bucket.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='GET',
        method_id='logging.billingAccounts.locations.buckets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsBucketsGetRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists log buckets.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBucketsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets',
        http_method='GET',
        method_id='logging.billingAccounts.locations.buckets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/buckets',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsBucketsListRequest',
        response_type_name='ListBucketsResponse',
        supports_download=False,
    )

    def Move(self, request, global_params=None):
      r"""Moves a bucket from one location to another location. This method creates a new bucket at the new location with an ACTIVE state. The bucket at the old location will remain available with an ARCHIVED state such that it is queryable but can no longer be used as a sink destination. All corresponding sinks are updated to point to the new bucket. Currently, the contents of the old bucket are not copied to the new one. In order to be movable, a bucket must satisfy the following restrictions: It's a _Default or _Required bucket. It has a location of "global". It has a non-project parent when it's a _Default bucket.

      Args:
        request: (MoveBucketRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Move')
      return self._RunMethod(
          config, request, global_params=global_params)

    Move.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}:move',
        http_method='POST',
        method_id='logging.billingAccounts.locations.buckets.move',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:move',
        request_field='<request>',
        request_type_name='MoveBucketRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a log bucket.If the bucket has a lifecycle_state of DELETE_REQUESTED, then FAILED_PRECONDITION will be returned.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='PATCH',
        method_id='logging.billingAccounts.locations.buckets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logBucket',
        request_type_name='LoggingBillingAccountsLocationsBucketsPatchRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a log bucket. A bucket that has been deleted can be undeleted within the grace period of 7 days.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}:undelete',
        http_method='POST',
        method_id='logging.billingAccounts.locations.buckets.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:undelete',
        request_field='undeleteBucketRequest',
        request_type_name='LoggingBillingAccountsLocationsBucketsUndeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def UpdateAsync(self, request, global_params=None):
      r"""Updates a log bucket asynchronously.If the bucket has a lifecycle_state of DELETE_REQUESTED, then FAILED_PRECONDITION will be returned.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingBillingAccountsLocationsBucketsUpdateAsyncRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateAsync')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateAsync.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/buckets/{bucketsId}:updateAsync',
        http_method='POST',
        method_id='logging.billingAccounts.locations.buckets.updateAsync',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}:updateAsync',
        request_field='logBucket',
        request_type_name='LoggingBillingAccountsLocationsBucketsUpdateAsyncRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class BillingAccountsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the billingAccounts_locations_operations resource."""

    _NAME = 'billingAccounts_locations_operations'

    def __init__(self, client):
      super(LoggingV2.BillingAccountsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def ApproveRedaction(self, request, global_params=None):
      r"""Once the impact assessment completes, the redaction operation will move into WAIT_FOR_USER_APPROVAL stage wherein it's going to wait for the user to approve the redaction operation. Please note that the operation will be in progress at this point and if the user doesn't approve the redaction operation within the grace period, it will be auto-cancelled.The redaction operation can also be approved before operation moves into the WAIT_FOR_USER_APPROVAL stage. In that case redaction process will commence as soon as the impact assessment is complete. This is functionally similar to approving after the operation moves to WAIT_FOR_USER_APPROVAL stage but without any wait time to begin redaction.Once the user approves, the redaction operation will begin redacting the log entries.

      Args:
        request: (LoggingBillingAccountsLocationsOperationsApproveRedactionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ApproveRedactionOperationResponse) The response message.
      """
      config = self.GetMethodConfig('ApproveRedaction')
      return self._RunMethod(
          config, request, global_params=global_params)

    ApproveRedaction.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/operations/{operationsId}:approveRedaction',
        http_method='GET',
        method_id='logging.billingAccounts.locations.operations.approveRedaction',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:approveRedaction',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsOperationsApproveRedactionRequest',
        response_type_name='ApproveRedactionOperationResponse',
        supports_download=False,
    )

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to Code.CANCELLED.

      Args:
        request: (LoggingBillingAccountsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='logging.billingAccounts.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='LoggingBillingAccountsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (LoggingBillingAccountsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='logging.billingAccounts.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.

      Args:
        request: (LoggingBillingAccountsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='logging.billingAccounts.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/operations',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class BillingAccountsLocationsRecentQueriesService(base_api.BaseApiService):
    """Service class for the billingAccounts_locations_recentQueries resource."""

    _NAME = 'billingAccounts_locations_recentQueries'

    def __init__(self, client):
      super(LoggingV2.BillingAccountsLocationsRecentQueriesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the RecentQueries that were created by the user making the request.

      Args:
        request: (LoggingBillingAccountsLocationsRecentQueriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRecentQueriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/recentQueries',
        http_method='GET',
        method_id='logging.billingAccounts.locations.recentQueries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/recentQueries',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsRecentQueriesListRequest',
        response_type_name='ListRecentQueriesResponse',
        supports_download=False,
    )

  class BillingAccountsLocationsSavedQueriesService(base_api.BaseApiService):
    """Service class for the billingAccounts_locations_savedQueries resource."""

    _NAME = 'billingAccounts_locations_savedQueries'

    def __init__(self, client):
      super(LoggingV2.BillingAccountsLocationsSavedQueriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SavedQuery for the user making the request.

      Args:
        request: (LoggingBillingAccountsLocationsSavedQueriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SavedQuery) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/savedQueries',
        http_method='POST',
        method_id='logging.billingAccounts.locations.savedQueries.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['savedQueryId'],
        relative_path='v2/{+parent}/savedQueries',
        request_field='savedQuery',
        request_type_name='LoggingBillingAccountsLocationsSavedQueriesCreateRequest',
        response_type_name='SavedQuery',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing SavedQuery that was created by the user making the request.

      Args:
        request: (LoggingBillingAccountsLocationsSavedQueriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/savedQueries/{savedQueriesId}',
        http_method='DELETE',
        method_id='logging.billingAccounts.locations.savedQueries.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsSavedQueriesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns all data associated with the requested query.

      Args:
        request: (LoggingBillingAccountsLocationsSavedQueriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SavedQuery) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/savedQueries/{savedQueriesId}',
        http_method='GET',
        method_id='logging.billingAccounts.locations.savedQueries.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsSavedQueriesGetRequest',
        response_type_name='SavedQuery',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the SavedQueries that were created by the user making the request.

      Args:
        request: (LoggingBillingAccountsLocationsSavedQueriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSavedQueriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/savedQueries',
        http_method='GET',
        method_id='logging.billingAccounts.locations.savedQueries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/savedQueries',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsSavedQueriesListRequest',
        response_type_name='ListSavedQueriesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing SavedQuery.

      Args:
        request: (LoggingBillingAccountsLocationsSavedQueriesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SavedQuery) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}/savedQueries/{savedQueriesId}',
        http_method='PATCH',
        method_id='logging.billingAccounts.locations.savedQueries.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='savedQuery',
        request_type_name='LoggingBillingAccountsLocationsSavedQueriesPatchRequest',
        response_type_name='SavedQuery',
        supports_download=False,
    )

  class BillingAccountsLocationsService(base_api.BaseApiService):
    """Service class for the billingAccounts_locations resource."""

    _NAME = 'billingAccounts_locations'

    def __init__(self, client):
      super(LoggingV2.BillingAccountsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (LoggingBillingAccountsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations/{locationsId}',
        http_method='GET',
        method_id='logging.billingAccounts.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (LoggingBillingAccountsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/locations',
        http_method='GET',
        method_id='logging.billingAccounts.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/locations',
        request_field='',
        request_type_name='LoggingBillingAccountsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class BillingAccountsLogsService(base_api.BaseApiService):
    """Service class for the billingAccounts_logs resource."""

    _NAME = 'billingAccounts_logs'

    def __init__(self, client):
      super(LoggingV2.BillingAccountsLogsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes all the log entries in a log for the global _Default Log Bucket. The log reappears if it receives new entries. Log entries written shortly before the delete operation might not be deleted. Entries received after the delete operation with a timestamp before the operation will be deleted.

      Args:
        request: (LoggingBillingAccountsLogsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/logs/{logsId}',
        http_method='DELETE',
        method_id='logging.billingAccounts.logs.delete',
        ordered_params=['logName'],
        path_params=['logName'],
        query_params=[],
        relative_path='v2/{+logName}',
        request_field='',
        request_type_name='LoggingBillingAccountsLogsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the logs in projects, organizations, folders, or billing accounts. Only logs that have entries are listed.

      Args:
        request: (LoggingBillingAccountsLogsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/logs',
        http_method='GET',
        method_id='logging.billingAccounts.logs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'resourceNames'],
        relative_path='v2/{+parent}/logs',
        request_field='',
        request_type_name='LoggingBillingAccountsLogsListRequest',
        response_type_name='ListLogsResponse',
        supports_download=False,
    )

  class BillingAccountsSinksService(base_api.BaseApiService):
    """Service class for the billingAccounts_sinks resource."""

    _NAME = 'billingAccounts_sinks'

    def __init__(self, client):
      super(LoggingV2.BillingAccountsSinksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a sink that exports specified log entries to a destination. The export begins upon ingress, unless the sink's writer_identity is not permitted to write to the destination. A sink can export log entries only from the resource owning the sink.

      Args:
        request: (LoggingBillingAccountsSinksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/sinks',
        http_method='POST',
        method_id='logging.billingAccounts.sinks.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['customWriterIdentity', 'uniqueWriterIdentity'],
        relative_path='v2/{+parent}/sinks',
        request_field='logSink',
        request_type_name='LoggingBillingAccountsSinksCreateRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a sink. If the sink has a unique writer_identity, then that service account is also deleted.

      Args:
        request: (LoggingBillingAccountsSinksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/sinks/{sinksId}',
        http_method='DELETE',
        method_id='logging.billingAccounts.sinks.delete',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=[],
        relative_path='v2/{+sinkName}',
        request_field='',
        request_type_name='LoggingBillingAccountsSinksDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a sink.

      Args:
        request: (LoggingBillingAccountsSinksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/sinks/{sinksId}',
        http_method='GET',
        method_id='logging.billingAccounts.sinks.get',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=[],
        relative_path='v2/{+sinkName}',
        request_field='',
        request_type_name='LoggingBillingAccountsSinksGetRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists sinks.

      Args:
        request: (LoggingBillingAccountsSinksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSinksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/sinks',
        http_method='GET',
        method_id='logging.billingAccounts.sinks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/sinks',
        request_field='',
        request_type_name='LoggingBillingAccountsSinksListRequest',
        response_type_name='ListSinksResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a sink. This method replaces the values of the destination and filter fields of the existing sink with the corresponding values from the new sink.The updated sink might also have a new writer_identity; see the unique_writer_identity field.

      Args:
        request: (LoggingBillingAccountsSinksPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/sinks/{sinksId}',
        http_method='PATCH',
        method_id='logging.billingAccounts.sinks.patch',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=['customWriterIdentity', 'uniqueWriterIdentity', 'updateMask'],
        relative_path='v2/{+sinkName}',
        request_field='logSink',
        request_type_name='LoggingBillingAccountsSinksPatchRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates a sink. This method replaces the values of the destination and filter fields of the existing sink with the corresponding values from the new sink.The updated sink might also have a new writer_identity; see the unique_writer_identity field.

      Args:
        request: (LoggingBillingAccountsSinksUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/sinks/{sinksId}',
        http_method='PUT',
        method_id='logging.billingAccounts.sinks.update',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=['customWriterIdentity', 'uniqueWriterIdentity', 'updateMask'],
        relative_path='v2/{+sinkName}',
        request_field='logSink',
        request_type_name='LoggingBillingAccountsSinksUpdateRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

  class BillingAccountsService(base_api.BaseApiService):
    """Service class for the billingAccounts resource."""

    _NAME = 'billingAccounts'

    def __init__(self, client):
      super(LoggingV2.BillingAccountsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetCmekSettings(self, request, global_params=None):
      r"""Gets the Logging CMEK settings for the given resource.Note: CMEK for the Log Router can be configured for Google Cloud projects, folders, organizations, and billing accounts. Once configured for an organization, it applies to all projects and folders in the Google Cloud organization.See Enabling CMEK for Log Router (https://cloud.google.com/logging/docs/routing/managed-encryption) for more information.

      Args:
        request: (LoggingBillingAccountsGetCmekSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CmekSettings) The response message.
      """
      config = self.GetMethodConfig('GetCmekSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetCmekSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/cmekSettings',
        http_method='GET',
        method_id='logging.billingAccounts.getCmekSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}/cmekSettings',
        request_field='',
        request_type_name='LoggingBillingAccountsGetCmekSettingsRequest',
        response_type_name='CmekSettings',
        supports_download=False,
    )

    def GetSettings(self, request, global_params=None):
      r"""Gets the settings for the given resource.Note: Settings can be retrieved for Google Cloud projects, folders, organizations, and billing accounts.See View default resource settings for Logging (https://cloud.google.com/logging/docs/default-settings#view-org-settings) for more information.

      Args:
        request: (LoggingBillingAccountsGetSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Settings) The response message.
      """
      config = self.GetMethodConfig('GetSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/billingAccounts/{billingAccountsId}/settings',
        http_method='GET',
        method_id='logging.billingAccounts.getSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}/settings',
        request_field='',
        request_type_name='LoggingBillingAccountsGetSettingsRequest',
        response_type_name='Settings',
        supports_download=False,
    )

  class DataService(base_api.BaseApiService):
    """Service class for the data resource."""

    _NAME = 'data'

    def __init__(self, client):
      super(LoggingV2.DataService, self).__init__(client)
      self._upload_configs = {
          }

    def Query(self, request, global_params=None):
      r"""Runs a (possibly multi-step) SQL query asynchronously in the tenant project and returns handles that can be used to fetch the results of each step. Raw table references are not permitted; all tables must be referenced in the form of views.

      Args:
        request: (QueryDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QueryDataResponse) The response message.
      """
      config = self.GetMethodConfig('Query')
      return self._RunMethod(
          config, request, global_params=global_params)

    Query.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='logging.data.query',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v2/data:query',
        request_field='<request>',
        request_type_name='QueryDataRequest',
        response_type_name='QueryDataResponse',
        supports_download=False,
    )

    def QueryLocal(self, request, global_params=None):
      r"""Runs a (possibly multi-step) SQL query asynchronously in the customer project and returns handles that can be used to fetch the results of each step. View references are translated to linked dataset tables, and references to other raw BigQuery tables are permitted.

      Args:
        request: (QueryDataLocalRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QueryDataResponse) The response message.
      """
      config = self.GetMethodConfig('QueryLocal')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryLocal.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='logging.data.queryLocal',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v2/data:queryLocal',
        request_field='<request>',
        request_type_name='QueryDataLocalRequest',
        response_type_name='QueryDataResponse',
        supports_download=False,
    )

    def QuerySync(self, request, global_params=None):
      r"""Attempts to run the query synchronously and return all query results. If the query cannot be executed synchronously, returns query handle that can be used to fetch the results via ReadQueryResults. All tables must be referenced in the form of Observability Views.

      Args:
        request: (QueryDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QueryResults) The response message.
      """
      config = self.GetMethodConfig('QuerySync')
      return self._RunMethod(
          config, request, global_params=global_params)

    QuerySync.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='logging.data.querySync',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v2/data:querySync',
        request_field='<request>',
        request_type_name='QueryDataRequest',
        response_type_name='QueryResults',
        supports_download=False,
    )

    def ReadQueryResults(self, request, global_params=None):
      r"""Retrieves the results from a single step of a previous call to QueryData, QueryDataSync or QueryDataLocal. If retrieving results from QueryDataLocal, then the same credentials must be used that were provided in the previous call.

      Args:
        request: (ReadQueryResultsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QueryResults) The response message.
      """
      config = self.GetMethodConfig('ReadQueryResults')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReadQueryResults.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='logging.data.readQueryResults',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v2/data:readQueryResults',
        request_field='<request>',
        request_type_name='ReadQueryResultsRequest',
        response_type_name='QueryResults',
        supports_download=False,
    )

  class EntriesService(base_api.BaseApiService):
    """Service class for the entries resource."""

    _NAME = 'entries'

    def __init__(self, client):
      super(LoggingV2.EntriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Copy(self, request, global_params=None):
      r"""Copies a set of log entries from a log bucket to a Cloud Storage bucket.

      Args:
        request: (CopyLogEntriesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Copy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Copy.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='logging.entries.copy',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v2/entries:copy',
        request_field='<request>',
        request_type_name='CopyLogEntriesRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists log entries. Use this method to retrieve log entries that originated from a project/folder/organization/billing account. For ways to export log entries, see Exporting Logs (https://cloud.google.com/logging/docs/export).

      Args:
        request: (ListLogEntriesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogEntriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='logging.entries.list',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v2/entries:list',
        request_field='<request>',
        request_type_name='ListLogEntriesRequest',
        response_type_name='ListLogEntriesResponse',
        supports_download=False,
    )

    def Redact(self, request, global_params=None):
      r"""Redacts the contents of log entries. This method replaces the payload of entries in a log bucket that match a filter with a RedactedLogEntryTombstone message.

      Args:
        request: (RedactLogEntriesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Redact')
      return self._RunMethod(
          config, request, global_params=global_params)

    Redact.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='logging.entries.redact',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v2/entries:redact',
        request_field='<request>',
        request_type_name='RedactLogEntriesRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Tail(self, request, global_params=None):
      r"""Streaming read of log entries as they are received. Until the stream is terminated, it will continue reading logs.

      Args:
        request: (TailLogEntriesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TailLogEntriesResponse) The response message.
      """
      config = self.GetMethodConfig('Tail')
      return self._RunMethod(
          config, request, global_params=global_params)

    Tail.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='logging.entries.tail',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v2/entries:tail',
        request_field='<request>',
        request_type_name='TailLogEntriesRequest',
        response_type_name='TailLogEntriesResponse',
        supports_download=False,
    )

    def Write(self, request, global_params=None):
      r"""Writes log entries to Logging. This API method is the only way to send log entries to Logging. This method is used, directly or indirectly, by the Logging agent (fluentd) and all logging libraries configured to use Logging. A single request may contain log entries for a maximum of 1000 different resource names (projects, organizations, billing accounts or folders), where the resource name for a log entry is determined from its logName field.

      Args:
        request: (WriteLogEntriesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WriteLogEntriesResponse) The response message.
      """
      config = self.GetMethodConfig('Write')
      return self._RunMethod(
          config, request, global_params=global_params)

    Write.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='logging.entries.write',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v2/entries:write',
        request_field='<request>',
        request_type_name='WriteLogEntriesRequest',
        response_type_name='WriteLogEntriesResponse',
        supports_download=False,
    )

  class ExclusionsService(base_api.BaseApiService):
    """Service class for the exclusions resource."""

    _NAME = 'exclusions'

    def __init__(self, client):
      super(LoggingV2.ExclusionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new exclusion in the _Default sink in a specified parent resource. Only log entries belonging to that resource can be excluded. You can have up to 10 exclusions in a resource.

      Args:
        request: (LoggingExclusionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/exclusions',
        http_method='POST',
        method_id='logging.exclusions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/exclusions',
        request_field='logExclusion',
        request_type_name='LoggingExclusionsCreateRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an exclusion in the _Default sink.

      Args:
        request: (LoggingExclusionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/exclusions/{exclusionsId}',
        http_method='DELETE',
        method_id='logging.exclusions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingExclusionsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the description of an exclusion in the _Default sink.

      Args:
        request: (LoggingExclusionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/exclusions/{exclusionsId}',
        http_method='GET',
        method_id='logging.exclusions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingExclusionsGetRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the exclusions on the _Default sink in a parent resource.

      Args:
        request: (LoggingExclusionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListExclusionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/exclusions',
        http_method='GET',
        method_id='logging.exclusions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/exclusions',
        request_field='',
        request_type_name='LoggingExclusionsListRequest',
        response_type_name='ListExclusionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Changes one or more properties of an existing exclusion in the _Default sink.

      Args:
        request: (LoggingExclusionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/exclusions/{exclusionsId}',
        http_method='PATCH',
        method_id='logging.exclusions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logExclusion',
        request_type_name='LoggingExclusionsPatchRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

  class FoldersExclusionsService(base_api.BaseApiService):
    """Service class for the folders_exclusions resource."""

    _NAME = 'folders_exclusions'

    def __init__(self, client):
      super(LoggingV2.FoldersExclusionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new exclusion in the _Default sink in a specified parent resource. Only log entries belonging to that resource can be excluded. You can have up to 10 exclusions in a resource.

      Args:
        request: (LoggingFoldersExclusionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/exclusions',
        http_method='POST',
        method_id='logging.folders.exclusions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/exclusions',
        request_field='logExclusion',
        request_type_name='LoggingFoldersExclusionsCreateRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an exclusion in the _Default sink.

      Args:
        request: (LoggingFoldersExclusionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/exclusions/{exclusionsId}',
        http_method='DELETE',
        method_id='logging.folders.exclusions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingFoldersExclusionsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the description of an exclusion in the _Default sink.

      Args:
        request: (LoggingFoldersExclusionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/exclusions/{exclusionsId}',
        http_method='GET',
        method_id='logging.folders.exclusions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingFoldersExclusionsGetRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the exclusions on the _Default sink in a parent resource.

      Args:
        request: (LoggingFoldersExclusionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListExclusionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/exclusions',
        http_method='GET',
        method_id='logging.folders.exclusions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/exclusions',
        request_field='',
        request_type_name='LoggingFoldersExclusionsListRequest',
        response_type_name='ListExclusionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Changes one or more properties of an existing exclusion in the _Default sink.

      Args:
        request: (LoggingFoldersExclusionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/exclusions/{exclusionsId}',
        http_method='PATCH',
        method_id='logging.folders.exclusions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logExclusion',
        request_type_name='LoggingFoldersExclusionsPatchRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

  class FoldersLocationsBucketsLinksService(base_api.BaseApiService):
    """Service class for the folders_locations_buckets_links resource."""

    _NAME = 'folders_locations_buckets_links'

    def __init__(self, client):
      super(LoggingV2.FoldersLocationsBucketsLinksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Asynchronously creates a linked dataset in BigQuery which makes it possible to use BigQuery to read the logs stored in the log bucket. A log bucket may currently only contain one link.

      Args:
        request: (LoggingFoldersLocationsBucketsLinksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}/links',
        http_method='POST',
        method_id='logging.folders.locations.buckets.links.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['linkId'],
        relative_path='v2/{+parent}/links',
        request_field='link',
        request_type_name='LoggingFoldersLocationsBucketsLinksCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a link. This will also delete the corresponding BigQuery linked dataset.

      Args:
        request: (LoggingFoldersLocationsBucketsLinksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}/links/{linksId}',
        http_method='DELETE',
        method_id='logging.folders.locations.buckets.links.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingFoldersLocationsBucketsLinksDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a link.

      Args:
        request: (LoggingFoldersLocationsBucketsLinksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Link) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}/links/{linksId}',
        http_method='GET',
        method_id='logging.folders.locations.buckets.links.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingFoldersLocationsBucketsLinksGetRequest',
        response_type_name='Link',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists links.

      Args:
        request: (LoggingFoldersLocationsBucketsLinksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLinksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}/links',
        http_method='GET',
        method_id='logging.folders.locations.buckets.links.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/links',
        request_field='',
        request_type_name='LoggingFoldersLocationsBucketsLinksListRequest',
        response_type_name='ListLinksResponse',
        supports_download=False,
    )

  class FoldersLocationsBucketsViewsLogsService(base_api.BaseApiService):
    """Service class for the folders_locations_buckets_views_logs resource."""

    _NAME = 'folders_locations_buckets_views_logs'

    def __init__(self, client):
      super(LoggingV2.FoldersLocationsBucketsViewsLogsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the logs in projects, organizations, folders, or billing accounts. Only logs that have entries are listed.

      Args:
        request: (LoggingFoldersLocationsBucketsViewsLogsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}/logs',
        http_method='GET',
        method_id='logging.folders.locations.buckets.views.logs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'resourceNames'],
        relative_path='v2/{+parent}/logs',
        request_field='',
        request_type_name='LoggingFoldersLocationsBucketsViewsLogsListRequest',
        response_type_name='ListLogsResponse',
        supports_download=False,
    )

  class FoldersLocationsBucketsViewsService(base_api.BaseApiService):
    """Service class for the folders_locations_buckets_views resource."""

    _NAME = 'folders_locations_buckets_views'

    def __init__(self, client):
      super(LoggingV2.FoldersLocationsBucketsViewsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a view over log entries in a log bucket. A bucket may contain a maximum of 30 views.

      Args:
        request: (LoggingFoldersLocationsBucketsViewsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}/views',
        http_method='POST',
        method_id='logging.folders.locations.buckets.views.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['viewId'],
        relative_path='v2/{+parent}/views',
        request_field='logView',
        request_type_name='LoggingFoldersLocationsBucketsViewsCreateRequest',
        response_type_name='LogView',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a view on a log bucket. If an UNAVAILABLE error is returned, this indicates that system is not in a state where it can delete the view. If this occurs, please try again in a few minutes.

      Args:
        request: (LoggingFoldersLocationsBucketsViewsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='DELETE',
        method_id='logging.folders.locations.buckets.views.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingFoldersLocationsBucketsViewsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a view on a log bucket.

      Args:
        request: (LoggingFoldersLocationsBucketsViewsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='GET',
        method_id='logging.folders.locations.buckets.views.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingFoldersLocationsBucketsViewsGetRequest',
        response_type_name='LogView',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (LoggingFoldersLocationsBucketsViewsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}:getIamPolicy',
        http_method='POST',
        method_id='logging.folders.locations.buckets.views.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='LoggingFoldersLocationsBucketsViewsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists views on a log bucket.

      Args:
        request: (LoggingFoldersLocationsBucketsViewsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListViewsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}/views',
        http_method='GET',
        method_id='logging.folders.locations.buckets.views.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/views',
        request_field='',
        request_type_name='LoggingFoldersLocationsBucketsViewsListRequest',
        response_type_name='ListViewsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a view on a log bucket. This method replaces the value of the filter field from the existing view with the corresponding value from the new view. If an UNAVAILABLE error is returned, this indicates that system is not in a state where it can update the view. If this occurs, please try again in a few minutes.

      Args:
        request: (LoggingFoldersLocationsBucketsViewsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='PATCH',
        method_id='logging.folders.locations.buckets.views.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logView',
        request_type_name='LoggingFoldersLocationsBucketsViewsPatchRequest',
        response_type_name='LogView',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (LoggingFoldersLocationsBucketsViewsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}:setIamPolicy',
        http_method='POST',
        method_id='logging.folders.locations.buckets.views.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='LoggingFoldersLocationsBucketsViewsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (LoggingFoldersLocationsBucketsViewsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}:testIamPermissions',
        http_method='POST',
        method_id='logging.folders.locations.buckets.views.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='LoggingFoldersLocationsBucketsViewsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class FoldersLocationsBucketsService(base_api.BaseApiService):
    """Service class for the folders_locations_buckets resource."""

    _NAME = 'folders_locations_buckets'

    def __init__(self, client):
      super(LoggingV2.FoldersLocationsBucketsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a log bucket that can be used to store log entries. After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingFoldersLocationsBucketsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets',
        http_method='POST',
        method_id='logging.folders.locations.buckets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bucketId'],
        relative_path='v2/{+parent}/buckets',
        request_field='logBucket',
        request_type_name='LoggingFoldersLocationsBucketsCreateRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def CreateAsync(self, request, global_params=None):
      r"""Creates a log bucket asynchronously that can be used to store log entries.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingFoldersLocationsBucketsCreateAsyncRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CreateAsync')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateAsync.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets:createAsync',
        http_method='POST',
        method_id='logging.folders.locations.buckets.createAsync',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bucketId'],
        relative_path='v2/{+parent}/buckets:createAsync',
        request_field='logBucket',
        request_type_name='LoggingFoldersLocationsBucketsCreateAsyncRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a log bucket.Changes the bucket's lifecycle_state to the DELETE_REQUESTED state. After 7 days, the bucket will be purged and all log entries in the bucket will be permanently deleted.

      Args:
        request: (LoggingFoldersLocationsBucketsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='DELETE',
        method_id='logging.folders.locations.buckets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingFoldersLocationsBucketsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a log bucket.

      Args:
        request: (LoggingFoldersLocationsBucketsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='GET',
        method_id='logging.folders.locations.buckets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingFoldersLocationsBucketsGetRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists log buckets.

      Args:
        request: (LoggingFoldersLocationsBucketsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBucketsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets',
        http_method='GET',
        method_id='logging.folders.locations.buckets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/buckets',
        request_field='',
        request_type_name='LoggingFoldersLocationsBucketsListRequest',
        response_type_name='ListBucketsResponse',
        supports_download=False,
    )

    def Move(self, request, global_params=None):
      r"""Moves a bucket from one location to another location. This method creates a new bucket at the new location with an ACTIVE state. The bucket at the old location will remain available with an ARCHIVED state such that it is queryable but can no longer be used as a sink destination. All corresponding sinks are updated to point to the new bucket. Currently, the contents of the old bucket are not copied to the new one. In order to be movable, a bucket must satisfy the following restrictions: It's a _Default or _Required bucket. It has a location of "global". It has a non-project parent when it's a _Default bucket.

      Args:
        request: (MoveBucketRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Move')
      return self._RunMethod(
          config, request, global_params=global_params)

    Move.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}:move',
        http_method='POST',
        method_id='logging.folders.locations.buckets.move',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:move',
        request_field='<request>',
        request_type_name='MoveBucketRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a log bucket.If the bucket has a lifecycle_state of DELETE_REQUESTED, then FAILED_PRECONDITION will be returned.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingFoldersLocationsBucketsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='PATCH',
        method_id='logging.folders.locations.buckets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logBucket',
        request_type_name='LoggingFoldersLocationsBucketsPatchRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a log bucket. A bucket that has been deleted can be undeleted within the grace period of 7 days.

      Args:
        request: (LoggingFoldersLocationsBucketsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}:undelete',
        http_method='POST',
        method_id='logging.folders.locations.buckets.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:undelete',
        request_field='undeleteBucketRequest',
        request_type_name='LoggingFoldersLocationsBucketsUndeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def UpdateAsync(self, request, global_params=None):
      r"""Updates a log bucket asynchronously.If the bucket has a lifecycle_state of DELETE_REQUESTED, then FAILED_PRECONDITION will be returned.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingFoldersLocationsBucketsUpdateAsyncRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateAsync')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateAsync.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/buckets/{bucketsId}:updateAsync',
        http_method='POST',
        method_id='logging.folders.locations.buckets.updateAsync',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}:updateAsync',
        request_field='logBucket',
        request_type_name='LoggingFoldersLocationsBucketsUpdateAsyncRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class FoldersLocationsLogScopesService(base_api.BaseApiService):
    """Service class for the folders_locations_logScopes resource."""

    _NAME = 'folders_locations_logScopes'

    def __init__(self, client):
      super(LoggingV2.FoldersLocationsLogScopesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a log scope.

      Args:
        request: (LoggingFoldersLocationsLogScopesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogScope) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/logScopes',
        http_method='POST',
        method_id='logging.folders.locations.logScopes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['logScopeId'],
        relative_path='v2/{+parent}/logScopes',
        request_field='logScope',
        request_type_name='LoggingFoldersLocationsLogScopesCreateRequest',
        response_type_name='LogScope',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a log scope.

      Args:
        request: (LoggingFoldersLocationsLogScopesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/logScopes/{logScopesId}',
        http_method='DELETE',
        method_id='logging.folders.locations.logScopes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingFoldersLocationsLogScopesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a log scope.

      Args:
        request: (LoggingFoldersLocationsLogScopesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogScope) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/logScopes/{logScopesId}',
        http_method='GET',
        method_id='logging.folders.locations.logScopes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingFoldersLocationsLogScopesGetRequest',
        response_type_name='LogScope',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists log scopes.

      Args:
        request: (LoggingFoldersLocationsLogScopesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogScopesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/logScopes',
        http_method='GET',
        method_id='logging.folders.locations.logScopes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/logScopes',
        request_field='',
        request_type_name='LoggingFoldersLocationsLogScopesListRequest',
        response_type_name='ListLogScopesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a log scope.

      Args:
        request: (LoggingFoldersLocationsLogScopesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogScope) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/logScopes/{logScopesId}',
        http_method='PATCH',
        method_id='logging.folders.locations.logScopes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logScope',
        request_type_name='LoggingFoldersLocationsLogScopesPatchRequest',
        response_type_name='LogScope',
        supports_download=False,
    )

  class FoldersLocationsOperationsService(base_api.BaseApiService):
    """Service class for the folders_locations_operations resource."""

    _NAME = 'folders_locations_operations'

    def __init__(self, client):
      super(LoggingV2.FoldersLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def ApproveRedaction(self, request, global_params=None):
      r"""Once the impact assessment completes, the redaction operation will move into WAIT_FOR_USER_APPROVAL stage wherein it's going to wait for the user to approve the redaction operation. Please note that the operation will be in progress at this point and if the user doesn't approve the redaction operation within the grace period, it will be auto-cancelled.The redaction operation can also be approved before operation moves into the WAIT_FOR_USER_APPROVAL stage. In that case redaction process will commence as soon as the impact assessment is complete. This is functionally similar to approving after the operation moves to WAIT_FOR_USER_APPROVAL stage but without any wait time to begin redaction.Once the user approves, the redaction operation will begin redacting the log entries.

      Args:
        request: (LoggingFoldersLocationsOperationsApproveRedactionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ApproveRedactionOperationResponse) The response message.
      """
      config = self.GetMethodConfig('ApproveRedaction')
      return self._RunMethod(
          config, request, global_params=global_params)

    ApproveRedaction.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/operations/{operationsId}:approveRedaction',
        http_method='GET',
        method_id='logging.folders.locations.operations.approveRedaction',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:approveRedaction',
        request_field='',
        request_type_name='LoggingFoldersLocationsOperationsApproveRedactionRequest',
        response_type_name='ApproveRedactionOperationResponse',
        supports_download=False,
    )

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to Code.CANCELLED.

      Args:
        request: (LoggingFoldersLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='logging.folders.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='LoggingFoldersLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (LoggingFoldersLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='logging.folders.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingFoldersLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.

      Args:
        request: (LoggingFoldersLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='logging.folders.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/operations',
        request_field='',
        request_type_name='LoggingFoldersLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class FoldersLocationsRecentQueriesService(base_api.BaseApiService):
    """Service class for the folders_locations_recentQueries resource."""

    _NAME = 'folders_locations_recentQueries'

    def __init__(self, client):
      super(LoggingV2.FoldersLocationsRecentQueriesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the RecentQueries that were created by the user making the request.

      Args:
        request: (LoggingFoldersLocationsRecentQueriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRecentQueriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/recentQueries',
        http_method='GET',
        method_id='logging.folders.locations.recentQueries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/recentQueries',
        request_field='',
        request_type_name='LoggingFoldersLocationsRecentQueriesListRequest',
        response_type_name='ListRecentQueriesResponse',
        supports_download=False,
    )

  class FoldersLocationsSavedQueriesService(base_api.BaseApiService):
    """Service class for the folders_locations_savedQueries resource."""

    _NAME = 'folders_locations_savedQueries'

    def __init__(self, client):
      super(LoggingV2.FoldersLocationsSavedQueriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SavedQuery for the user making the request.

      Args:
        request: (LoggingFoldersLocationsSavedQueriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SavedQuery) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/savedQueries',
        http_method='POST',
        method_id='logging.folders.locations.savedQueries.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['savedQueryId'],
        relative_path='v2/{+parent}/savedQueries',
        request_field='savedQuery',
        request_type_name='LoggingFoldersLocationsSavedQueriesCreateRequest',
        response_type_name='SavedQuery',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing SavedQuery that was created by the user making the request.

      Args:
        request: (LoggingFoldersLocationsSavedQueriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/savedQueries/{savedQueriesId}',
        http_method='DELETE',
        method_id='logging.folders.locations.savedQueries.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingFoldersLocationsSavedQueriesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns all data associated with the requested query.

      Args:
        request: (LoggingFoldersLocationsSavedQueriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SavedQuery) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/savedQueries/{savedQueriesId}',
        http_method='GET',
        method_id='logging.folders.locations.savedQueries.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingFoldersLocationsSavedQueriesGetRequest',
        response_type_name='SavedQuery',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the SavedQueries that were created by the user making the request.

      Args:
        request: (LoggingFoldersLocationsSavedQueriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSavedQueriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/savedQueries',
        http_method='GET',
        method_id='logging.folders.locations.savedQueries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/savedQueries',
        request_field='',
        request_type_name='LoggingFoldersLocationsSavedQueriesListRequest',
        response_type_name='ListSavedQueriesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing SavedQuery.

      Args:
        request: (LoggingFoldersLocationsSavedQueriesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SavedQuery) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/savedQueries/{savedQueriesId}',
        http_method='PATCH',
        method_id='logging.folders.locations.savedQueries.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='savedQuery',
        request_type_name='LoggingFoldersLocationsSavedQueriesPatchRequest',
        response_type_name='SavedQuery',
        supports_download=False,
    )

  class FoldersLocationsService(base_api.BaseApiService):
    """Service class for the folders_locations resource."""

    _NAME = 'folders_locations'

    def __init__(self, client):
      super(LoggingV2.FoldersLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (LoggingFoldersLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}',
        http_method='GET',
        method_id='logging.folders.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingFoldersLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (LoggingFoldersLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations',
        http_method='GET',
        method_id='logging.folders.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/locations',
        request_field='',
        request_type_name='LoggingFoldersLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class FoldersLogsService(base_api.BaseApiService):
    """Service class for the folders_logs resource."""

    _NAME = 'folders_logs'

    def __init__(self, client):
      super(LoggingV2.FoldersLogsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes all the log entries in a log for the global _Default Log Bucket. The log reappears if it receives new entries. Log entries written shortly before the delete operation might not be deleted. Entries received after the delete operation with a timestamp before the operation will be deleted.

      Args:
        request: (LoggingFoldersLogsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/logs/{logsId}',
        http_method='DELETE',
        method_id='logging.folders.logs.delete',
        ordered_params=['logName'],
        path_params=['logName'],
        query_params=[],
        relative_path='v2/{+logName}',
        request_field='',
        request_type_name='LoggingFoldersLogsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the logs in projects, organizations, folders, or billing accounts. Only logs that have entries are listed.

      Args:
        request: (LoggingFoldersLogsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/logs',
        http_method='GET',
        method_id='logging.folders.logs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'resourceNames'],
        relative_path='v2/{+parent}/logs',
        request_field='',
        request_type_name='LoggingFoldersLogsListRequest',
        response_type_name='ListLogsResponse',
        supports_download=False,
    )

  class FoldersSinksService(base_api.BaseApiService):
    """Service class for the folders_sinks resource."""

    _NAME = 'folders_sinks'

    def __init__(self, client):
      super(LoggingV2.FoldersSinksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a sink that exports specified log entries to a destination. The export begins upon ingress, unless the sink's writer_identity is not permitted to write to the destination. A sink can export log entries only from the resource owning the sink.

      Args:
        request: (LoggingFoldersSinksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sinks',
        http_method='POST',
        method_id='logging.folders.sinks.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['customWriterIdentity', 'uniqueWriterIdentity'],
        relative_path='v2/{+parent}/sinks',
        request_field='logSink',
        request_type_name='LoggingFoldersSinksCreateRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a sink. If the sink has a unique writer_identity, then that service account is also deleted.

      Args:
        request: (LoggingFoldersSinksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sinks/{sinksId}',
        http_method='DELETE',
        method_id='logging.folders.sinks.delete',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=[],
        relative_path='v2/{+sinkName}',
        request_field='',
        request_type_name='LoggingFoldersSinksDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a sink.

      Args:
        request: (LoggingFoldersSinksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sinks/{sinksId}',
        http_method='GET',
        method_id='logging.folders.sinks.get',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=[],
        relative_path='v2/{+sinkName}',
        request_field='',
        request_type_name='LoggingFoldersSinksGetRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists sinks.

      Args:
        request: (LoggingFoldersSinksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSinksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sinks',
        http_method='GET',
        method_id='logging.folders.sinks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/sinks',
        request_field='',
        request_type_name='LoggingFoldersSinksListRequest',
        response_type_name='ListSinksResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a sink. This method replaces the values of the destination and filter fields of the existing sink with the corresponding values from the new sink.The updated sink might also have a new writer_identity; see the unique_writer_identity field.

      Args:
        request: (LoggingFoldersSinksPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sinks/{sinksId}',
        http_method='PATCH',
        method_id='logging.folders.sinks.patch',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=['customWriterIdentity', 'uniqueWriterIdentity', 'updateMask'],
        relative_path='v2/{+sinkName}',
        request_field='logSink',
        request_type_name='LoggingFoldersSinksPatchRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates a sink. This method replaces the values of the destination and filter fields of the existing sink with the corresponding values from the new sink.The updated sink might also have a new writer_identity; see the unique_writer_identity field.

      Args:
        request: (LoggingFoldersSinksUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sinks/{sinksId}',
        http_method='PUT',
        method_id='logging.folders.sinks.update',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=['customWriterIdentity', 'uniqueWriterIdentity', 'updateMask'],
        relative_path='v2/{+sinkName}',
        request_field='logSink',
        request_type_name='LoggingFoldersSinksUpdateRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(LoggingV2.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

    def GetCmekSettings(self, request, global_params=None):
      r"""Gets the Logging CMEK settings for the given resource.Note: CMEK for the Log Router can be configured for Google Cloud projects, folders, organizations, and billing accounts. Once configured for an organization, it applies to all projects and folders in the Google Cloud organization.See Enabling CMEK for Log Router (https://cloud.google.com/logging/docs/routing/managed-encryption) for more information.

      Args:
        request: (LoggingFoldersGetCmekSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CmekSettings) The response message.
      """
      config = self.GetMethodConfig('GetCmekSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetCmekSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/cmekSettings',
        http_method='GET',
        method_id='logging.folders.getCmekSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}/cmekSettings',
        request_field='',
        request_type_name='LoggingFoldersGetCmekSettingsRequest',
        response_type_name='CmekSettings',
        supports_download=False,
    )

    def GetSettings(self, request, global_params=None):
      r"""Gets the settings for the given resource.Note: Settings can be retrieved for Google Cloud projects, folders, organizations, and billing accounts.See View default resource settings for Logging (https://cloud.google.com/logging/docs/default-settings#view-org-settings) for more information.

      Args:
        request: (LoggingFoldersGetSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Settings) The response message.
      """
      config = self.GetMethodConfig('GetSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/settings',
        http_method='GET',
        method_id='logging.folders.getSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}/settings',
        request_field='',
        request_type_name='LoggingFoldersGetSettingsRequest',
        response_type_name='Settings',
        supports_download=False,
    )

    def UpdateSettings(self, request, global_params=None):
      r"""Updates the settings for the given resource. This method applies to all feature configurations for organization and folders.UpdateSettings fails when any of the following are true: The value of storage_location either isn't supported by Logging or violates the location OrgPolicy. The default_sink_config field is set, but it has an unspecified filter write mode. The value of kms_key_name is invalid. The associated service account doesn't have the required roles/cloudkms.cryptoKeyEncrypterDecrypter role assigned for the key. Access to the key is disabled.See Configure default settings for organizations and folders (https://cloud.google.com/logging/docs/default-settings) for more information.

      Args:
        request: (LoggingFoldersUpdateSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Settings) The response message.
      """
      config = self.GetMethodConfig('UpdateSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/settings',
        http_method='PATCH',
        method_id='logging.folders.updateSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}/settings',
        request_field='settings',
        request_type_name='LoggingFoldersUpdateSettingsRequest',
        response_type_name='Settings',
        supports_download=False,
    )

  class LocationsBucketsLinksService(base_api.BaseApiService):
    """Service class for the locations_buckets_links resource."""

    _NAME = 'locations_buckets_links'

    def __init__(self, client):
      super(LoggingV2.LocationsBucketsLinksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Asynchronously creates a linked dataset in BigQuery which makes it possible to use BigQuery to read the logs stored in the log bucket. A log bucket may currently only contain one link.

      Args:
        request: (LoggingLocationsBucketsLinksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}/links',
        http_method='POST',
        method_id='logging.locations.buckets.links.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['linkId'],
        relative_path='v2/{+parent}/links',
        request_field='link',
        request_type_name='LoggingLocationsBucketsLinksCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a link. This will also delete the corresponding BigQuery linked dataset.

      Args:
        request: (LoggingLocationsBucketsLinksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}/links/{linksId}',
        http_method='DELETE',
        method_id='logging.locations.buckets.links.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingLocationsBucketsLinksDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a link.

      Args:
        request: (LoggingLocationsBucketsLinksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Link) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}/links/{linksId}',
        http_method='GET',
        method_id='logging.locations.buckets.links.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingLocationsBucketsLinksGetRequest',
        response_type_name='Link',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists links.

      Args:
        request: (LoggingLocationsBucketsLinksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLinksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}/links',
        http_method='GET',
        method_id='logging.locations.buckets.links.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/links',
        request_field='',
        request_type_name='LoggingLocationsBucketsLinksListRequest',
        response_type_name='ListLinksResponse',
        supports_download=False,
    )

  class LocationsBucketsViewsService(base_api.BaseApiService):
    """Service class for the locations_buckets_views resource."""

    _NAME = 'locations_buckets_views'

    def __init__(self, client):
      super(LoggingV2.LocationsBucketsViewsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a view over log entries in a log bucket. A bucket may contain a maximum of 30 views.

      Args:
        request: (LoggingLocationsBucketsViewsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}/views',
        http_method='POST',
        method_id='logging.locations.buckets.views.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['viewId'],
        relative_path='v2/{+parent}/views',
        request_field='logView',
        request_type_name='LoggingLocationsBucketsViewsCreateRequest',
        response_type_name='LogView',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a view on a log bucket. If an UNAVAILABLE error is returned, this indicates that system is not in a state where it can delete the view. If this occurs, please try again in a few minutes.

      Args:
        request: (LoggingLocationsBucketsViewsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='DELETE',
        method_id='logging.locations.buckets.views.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingLocationsBucketsViewsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a view on a log bucket.

      Args:
        request: (LoggingLocationsBucketsViewsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='GET',
        method_id='logging.locations.buckets.views.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingLocationsBucketsViewsGetRequest',
        response_type_name='LogView',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (LoggingLocationsBucketsViewsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}:getIamPolicy',
        http_method='POST',
        method_id='logging.locations.buckets.views.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='LoggingLocationsBucketsViewsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists views on a log bucket.

      Args:
        request: (LoggingLocationsBucketsViewsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListViewsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}/views',
        http_method='GET',
        method_id='logging.locations.buckets.views.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/views',
        request_field='',
        request_type_name='LoggingLocationsBucketsViewsListRequest',
        response_type_name='ListViewsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a view on a log bucket. This method replaces the value of the filter field from the existing view with the corresponding value from the new view. If an UNAVAILABLE error is returned, this indicates that system is not in a state where it can update the view. If this occurs, please try again in a few minutes.

      Args:
        request: (LoggingLocationsBucketsViewsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='PATCH',
        method_id='logging.locations.buckets.views.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logView',
        request_type_name='LoggingLocationsBucketsViewsPatchRequest',
        response_type_name='LogView',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (LoggingLocationsBucketsViewsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}:setIamPolicy',
        http_method='POST',
        method_id='logging.locations.buckets.views.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='LoggingLocationsBucketsViewsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (LoggingLocationsBucketsViewsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}:testIamPermissions',
        http_method='POST',
        method_id='logging.locations.buckets.views.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='LoggingLocationsBucketsViewsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class LocationsBucketsService(base_api.BaseApiService):
    """Service class for the locations_buckets resource."""

    _NAME = 'locations_buckets'

    def __init__(self, client):
      super(LoggingV2.LocationsBucketsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a log bucket that can be used to store log entries. After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingLocationsBucketsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets',
        http_method='POST',
        method_id='logging.locations.buckets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bucketId'],
        relative_path='v2/{+parent}/buckets',
        request_field='logBucket',
        request_type_name='LoggingLocationsBucketsCreateRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def CreateAsync(self, request, global_params=None):
      r"""Creates a log bucket asynchronously that can be used to store log entries.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingLocationsBucketsCreateAsyncRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CreateAsync')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateAsync.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets:createAsync',
        http_method='POST',
        method_id='logging.locations.buckets.createAsync',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bucketId'],
        relative_path='v2/{+parent}/buckets:createAsync',
        request_field='logBucket',
        request_type_name='LoggingLocationsBucketsCreateAsyncRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a log bucket.Changes the bucket's lifecycle_state to the DELETE_REQUESTED state. After 7 days, the bucket will be purged and all log entries in the bucket will be permanently deleted.

      Args:
        request: (LoggingLocationsBucketsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='DELETE',
        method_id='logging.locations.buckets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingLocationsBucketsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a log bucket.

      Args:
        request: (LoggingLocationsBucketsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='GET',
        method_id='logging.locations.buckets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingLocationsBucketsGetRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists log buckets.

      Args:
        request: (LoggingLocationsBucketsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBucketsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets',
        http_method='GET',
        method_id='logging.locations.buckets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/buckets',
        request_field='',
        request_type_name='LoggingLocationsBucketsListRequest',
        response_type_name='ListBucketsResponse',
        supports_download=False,
    )

    def Move(self, request, global_params=None):
      r"""Moves a bucket from one location to another location. This method creates a new bucket at the new location with an ACTIVE state. The bucket at the old location will remain available with an ARCHIVED state such that it is queryable but can no longer be used as a sink destination. All corresponding sinks are updated to point to the new bucket. Currently, the contents of the old bucket are not copied to the new one. In order to be movable, a bucket must satisfy the following restrictions: It's a _Default or _Required bucket. It has a location of "global". It has a non-project parent when it's a _Default bucket.

      Args:
        request: (MoveBucketRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Move')
      return self._RunMethod(
          config, request, global_params=global_params)

    Move.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}:move',
        http_method='POST',
        method_id='logging.locations.buckets.move',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:move',
        request_field='<request>',
        request_type_name='MoveBucketRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a log bucket.If the bucket has a lifecycle_state of DELETE_REQUESTED, then FAILED_PRECONDITION will be returned.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingLocationsBucketsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='PATCH',
        method_id='logging.locations.buckets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logBucket',
        request_type_name='LoggingLocationsBucketsPatchRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a log bucket. A bucket that has been deleted can be undeleted within the grace period of 7 days.

      Args:
        request: (LoggingLocationsBucketsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}:undelete',
        http_method='POST',
        method_id='logging.locations.buckets.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:undelete',
        request_field='undeleteBucketRequest',
        request_type_name='LoggingLocationsBucketsUndeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def UpdateAsync(self, request, global_params=None):
      r"""Updates a log bucket asynchronously.If the bucket has a lifecycle_state of DELETE_REQUESTED, then FAILED_PRECONDITION will be returned.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingLocationsBucketsUpdateAsyncRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateAsync')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateAsync.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/buckets/{bucketsId}:updateAsync',
        http_method='POST',
        method_id='logging.locations.buckets.updateAsync',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}:updateAsync',
        request_field='logBucket',
        request_type_name='LoggingLocationsBucketsUpdateAsyncRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class LocationsOperationsService(base_api.BaseApiService):
    """Service class for the locations_operations resource."""

    _NAME = 'locations_operations'

    def __init__(self, client):
      super(LoggingV2.LocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def ApproveRedaction(self, request, global_params=None):
      r"""Once the impact assessment completes, the redaction operation will move into WAIT_FOR_USER_APPROVAL stage wherein it's going to wait for the user to approve the redaction operation. Please note that the operation will be in progress at this point and if the user doesn't approve the redaction operation within the grace period, it will be auto-cancelled.The redaction operation can also be approved before operation moves into the WAIT_FOR_USER_APPROVAL stage. In that case redaction process will commence as soon as the impact assessment is complete. This is functionally similar to approving after the operation moves to WAIT_FOR_USER_APPROVAL stage but without any wait time to begin redaction.Once the user approves, the redaction operation will begin redacting the log entries.

      Args:
        request: (LoggingLocationsOperationsApproveRedactionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ApproveRedactionOperationResponse) The response message.
      """
      config = self.GetMethodConfig('ApproveRedaction')
      return self._RunMethod(
          config, request, global_params=global_params)

    ApproveRedaction.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/operations/{operationsId}:approveRedaction',
        http_method='GET',
        method_id='logging.locations.operations.approveRedaction',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:approveRedaction',
        request_field='',
        request_type_name='LoggingLocationsOperationsApproveRedactionRequest',
        response_type_name='ApproveRedactionOperationResponse',
        supports_download=False,
    )

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to Code.CANCELLED.

      Args:
        request: (LoggingLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='logging.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='LoggingLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (LoggingLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='logging.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.

      Args:
        request: (LoggingLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='logging.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/operations',
        request_field='',
        request_type_name='LoggingLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class LocationsService(base_api.BaseApiService):
    """Service class for the locations resource."""

    _NAME = 'locations'

    def __init__(self, client):
      super(LoggingV2.LocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (LoggingLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations/{locationsId}',
        http_method='GET',
        method_id='logging.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (LoggingLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/locations',
        http_method='GET',
        method_id='logging.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/locations',
        request_field='',
        request_type_name='LoggingLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class LogsService(base_api.BaseApiService):
    """Service class for the logs resource."""

    _NAME = 'logs'

    def __init__(self, client):
      super(LoggingV2.LogsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes all the log entries in a log for the global _Default Log Bucket. The log reappears if it receives new entries. Log entries written shortly before the delete operation might not be deleted. Entries received after the delete operation with a timestamp before the operation will be deleted.

      Args:
        request: (LoggingLogsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/logs/{logsId}',
        http_method='DELETE',
        method_id='logging.logs.delete',
        ordered_params=['logName'],
        path_params=['logName'],
        query_params=[],
        relative_path='v2/{+logName}',
        request_field='',
        request_type_name='LoggingLogsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the logs in projects, organizations, folders, or billing accounts. Only logs that have entries are listed.

      Args:
        request: (LoggingLogsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/logs',
        http_method='GET',
        method_id='logging.logs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'resourceNames'],
        relative_path='v2/{+parent}/logs',
        request_field='',
        request_type_name='LoggingLogsListRequest',
        response_type_name='ListLogsResponse',
        supports_download=False,
    )

  class MonitoredResourceDescriptorsService(base_api.BaseApiService):
    """Service class for the monitoredResourceDescriptors resource."""

    _NAME = 'monitoredResourceDescriptors'

    def __init__(self, client):
      super(LoggingV2.MonitoredResourceDescriptorsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the descriptors for monitored resource types used by Logging.

      Args:
        request: (LoggingMonitoredResourceDescriptorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMonitoredResourceDescriptorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='logging.monitoredResourceDescriptors.list',
        ordered_params=[],
        path_params=[],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/monitoredResourceDescriptors',
        request_field='',
        request_type_name='LoggingMonitoredResourceDescriptorsListRequest',
        response_type_name='ListMonitoredResourceDescriptorsResponse',
        supports_download=False,
    )

  class OrganizationsExclusionsService(base_api.BaseApiService):
    """Service class for the organizations_exclusions resource."""

    _NAME = 'organizations_exclusions'

    def __init__(self, client):
      super(LoggingV2.OrganizationsExclusionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new exclusion in the _Default sink in a specified parent resource. Only log entries belonging to that resource can be excluded. You can have up to 10 exclusions in a resource.

      Args:
        request: (LoggingOrganizationsExclusionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/exclusions',
        http_method='POST',
        method_id='logging.organizations.exclusions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/exclusions',
        request_field='logExclusion',
        request_type_name='LoggingOrganizationsExclusionsCreateRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an exclusion in the _Default sink.

      Args:
        request: (LoggingOrganizationsExclusionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/exclusions/{exclusionsId}',
        http_method='DELETE',
        method_id='logging.organizations.exclusions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingOrganizationsExclusionsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the description of an exclusion in the _Default sink.

      Args:
        request: (LoggingOrganizationsExclusionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/exclusions/{exclusionsId}',
        http_method='GET',
        method_id='logging.organizations.exclusions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingOrganizationsExclusionsGetRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the exclusions on the _Default sink in a parent resource.

      Args:
        request: (LoggingOrganizationsExclusionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListExclusionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/exclusions',
        http_method='GET',
        method_id='logging.organizations.exclusions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/exclusions',
        request_field='',
        request_type_name='LoggingOrganizationsExclusionsListRequest',
        response_type_name='ListExclusionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Changes one or more properties of an existing exclusion in the _Default sink.

      Args:
        request: (LoggingOrganizationsExclusionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/exclusions/{exclusionsId}',
        http_method='PATCH',
        method_id='logging.organizations.exclusions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logExclusion',
        request_type_name='LoggingOrganizationsExclusionsPatchRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

  class OrganizationsLocationsBucketsLinksService(base_api.BaseApiService):
    """Service class for the organizations_locations_buckets_links resource."""

    _NAME = 'organizations_locations_buckets_links'

    def __init__(self, client):
      super(LoggingV2.OrganizationsLocationsBucketsLinksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Asynchronously creates a linked dataset in BigQuery which makes it possible to use BigQuery to read the logs stored in the log bucket. A log bucket may currently only contain one link.

      Args:
        request: (LoggingOrganizationsLocationsBucketsLinksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}/links',
        http_method='POST',
        method_id='logging.organizations.locations.buckets.links.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['linkId'],
        relative_path='v2/{+parent}/links',
        request_field='link',
        request_type_name='LoggingOrganizationsLocationsBucketsLinksCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a link. This will also delete the corresponding BigQuery linked dataset.

      Args:
        request: (LoggingOrganizationsLocationsBucketsLinksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}/links/{linksId}',
        http_method='DELETE',
        method_id='logging.organizations.locations.buckets.links.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsBucketsLinksDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a link.

      Args:
        request: (LoggingOrganizationsLocationsBucketsLinksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Link) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}/links/{linksId}',
        http_method='GET',
        method_id='logging.organizations.locations.buckets.links.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsBucketsLinksGetRequest',
        response_type_name='Link',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists links.

      Args:
        request: (LoggingOrganizationsLocationsBucketsLinksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLinksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}/links',
        http_method='GET',
        method_id='logging.organizations.locations.buckets.links.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/links',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsBucketsLinksListRequest',
        response_type_name='ListLinksResponse',
        supports_download=False,
    )

  class OrganizationsLocationsBucketsViewsLogsService(base_api.BaseApiService):
    """Service class for the organizations_locations_buckets_views_logs resource."""

    _NAME = 'organizations_locations_buckets_views_logs'

    def __init__(self, client):
      super(LoggingV2.OrganizationsLocationsBucketsViewsLogsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the logs in projects, organizations, folders, or billing accounts. Only logs that have entries are listed.

      Args:
        request: (LoggingOrganizationsLocationsBucketsViewsLogsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}/logs',
        http_method='GET',
        method_id='logging.organizations.locations.buckets.views.logs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'resourceNames'],
        relative_path='v2/{+parent}/logs',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsBucketsViewsLogsListRequest',
        response_type_name='ListLogsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsBucketsViewsService(base_api.BaseApiService):
    """Service class for the organizations_locations_buckets_views resource."""

    _NAME = 'organizations_locations_buckets_views'

    def __init__(self, client):
      super(LoggingV2.OrganizationsLocationsBucketsViewsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a view over log entries in a log bucket. A bucket may contain a maximum of 30 views.

      Args:
        request: (LoggingOrganizationsLocationsBucketsViewsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}/views',
        http_method='POST',
        method_id='logging.organizations.locations.buckets.views.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['viewId'],
        relative_path='v2/{+parent}/views',
        request_field='logView',
        request_type_name='LoggingOrganizationsLocationsBucketsViewsCreateRequest',
        response_type_name='LogView',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a view on a log bucket. If an UNAVAILABLE error is returned, this indicates that system is not in a state where it can delete the view. If this occurs, please try again in a few minutes.

      Args:
        request: (LoggingOrganizationsLocationsBucketsViewsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='DELETE',
        method_id='logging.organizations.locations.buckets.views.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsBucketsViewsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a view on a log bucket.

      Args:
        request: (LoggingOrganizationsLocationsBucketsViewsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='GET',
        method_id='logging.organizations.locations.buckets.views.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsBucketsViewsGetRequest',
        response_type_name='LogView',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (LoggingOrganizationsLocationsBucketsViewsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}:getIamPolicy',
        http_method='POST',
        method_id='logging.organizations.locations.buckets.views.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='LoggingOrganizationsLocationsBucketsViewsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists views on a log bucket.

      Args:
        request: (LoggingOrganizationsLocationsBucketsViewsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListViewsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}/views',
        http_method='GET',
        method_id='logging.organizations.locations.buckets.views.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/views',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsBucketsViewsListRequest',
        response_type_name='ListViewsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a view on a log bucket. This method replaces the value of the filter field from the existing view with the corresponding value from the new view. If an UNAVAILABLE error is returned, this indicates that system is not in a state where it can update the view. If this occurs, please try again in a few minutes.

      Args:
        request: (LoggingOrganizationsLocationsBucketsViewsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='PATCH',
        method_id='logging.organizations.locations.buckets.views.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logView',
        request_type_name='LoggingOrganizationsLocationsBucketsViewsPatchRequest',
        response_type_name='LogView',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (LoggingOrganizationsLocationsBucketsViewsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}:setIamPolicy',
        http_method='POST',
        method_id='logging.organizations.locations.buckets.views.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='LoggingOrganizationsLocationsBucketsViewsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (LoggingOrganizationsLocationsBucketsViewsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}:testIamPermissions',
        http_method='POST',
        method_id='logging.organizations.locations.buckets.views.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='LoggingOrganizationsLocationsBucketsViewsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsBucketsService(base_api.BaseApiService):
    """Service class for the organizations_locations_buckets resource."""

    _NAME = 'organizations_locations_buckets'

    def __init__(self, client):
      super(LoggingV2.OrganizationsLocationsBucketsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a log bucket that can be used to store log entries. After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingOrganizationsLocationsBucketsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets',
        http_method='POST',
        method_id='logging.organizations.locations.buckets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bucketId'],
        relative_path='v2/{+parent}/buckets',
        request_field='logBucket',
        request_type_name='LoggingOrganizationsLocationsBucketsCreateRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def CreateAsync(self, request, global_params=None):
      r"""Creates a log bucket asynchronously that can be used to store log entries.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingOrganizationsLocationsBucketsCreateAsyncRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CreateAsync')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateAsync.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets:createAsync',
        http_method='POST',
        method_id='logging.organizations.locations.buckets.createAsync',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bucketId'],
        relative_path='v2/{+parent}/buckets:createAsync',
        request_field='logBucket',
        request_type_name='LoggingOrganizationsLocationsBucketsCreateAsyncRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a log bucket.Changes the bucket's lifecycle_state to the DELETE_REQUESTED state. After 7 days, the bucket will be purged and all log entries in the bucket will be permanently deleted.

      Args:
        request: (LoggingOrganizationsLocationsBucketsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='DELETE',
        method_id='logging.organizations.locations.buckets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsBucketsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a log bucket.

      Args:
        request: (LoggingOrganizationsLocationsBucketsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='GET',
        method_id='logging.organizations.locations.buckets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsBucketsGetRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists log buckets.

      Args:
        request: (LoggingOrganizationsLocationsBucketsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBucketsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets',
        http_method='GET',
        method_id='logging.organizations.locations.buckets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/buckets',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsBucketsListRequest',
        response_type_name='ListBucketsResponse',
        supports_download=False,
    )

    def Move(self, request, global_params=None):
      r"""Moves a bucket from one location to another location. This method creates a new bucket at the new location with an ACTIVE state. The bucket at the old location will remain available with an ARCHIVED state such that it is queryable but can no longer be used as a sink destination. All corresponding sinks are updated to point to the new bucket. Currently, the contents of the old bucket are not copied to the new one. In order to be movable, a bucket must satisfy the following restrictions: It's a _Default or _Required bucket. It has a location of "global". It has a non-project parent when it's a _Default bucket.

      Args:
        request: (MoveBucketRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Move')
      return self._RunMethod(
          config, request, global_params=global_params)

    Move.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}:move',
        http_method='POST',
        method_id='logging.organizations.locations.buckets.move',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:move',
        request_field='<request>',
        request_type_name='MoveBucketRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a log bucket.If the bucket has a lifecycle_state of DELETE_REQUESTED, then FAILED_PRECONDITION will be returned.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingOrganizationsLocationsBucketsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='PATCH',
        method_id='logging.organizations.locations.buckets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logBucket',
        request_type_name='LoggingOrganizationsLocationsBucketsPatchRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a log bucket. A bucket that has been deleted can be undeleted within the grace period of 7 days.

      Args:
        request: (LoggingOrganizationsLocationsBucketsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}:undelete',
        http_method='POST',
        method_id='logging.organizations.locations.buckets.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:undelete',
        request_field='undeleteBucketRequest',
        request_type_name='LoggingOrganizationsLocationsBucketsUndeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def UpdateAsync(self, request, global_params=None):
      r"""Updates a log bucket asynchronously.If the bucket has a lifecycle_state of DELETE_REQUESTED, then FAILED_PRECONDITION will be returned.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingOrganizationsLocationsBucketsUpdateAsyncRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateAsync')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateAsync.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/buckets/{bucketsId}:updateAsync',
        http_method='POST',
        method_id='logging.organizations.locations.buckets.updateAsync',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}:updateAsync',
        request_field='logBucket',
        request_type_name='LoggingOrganizationsLocationsBucketsUpdateAsyncRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsLocationsLogScopesService(base_api.BaseApiService):
    """Service class for the organizations_locations_logScopes resource."""

    _NAME = 'organizations_locations_logScopes'

    def __init__(self, client):
      super(LoggingV2.OrganizationsLocationsLogScopesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a log scope.

      Args:
        request: (LoggingOrganizationsLocationsLogScopesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogScope) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/logScopes',
        http_method='POST',
        method_id='logging.organizations.locations.logScopes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['logScopeId'],
        relative_path='v2/{+parent}/logScopes',
        request_field='logScope',
        request_type_name='LoggingOrganizationsLocationsLogScopesCreateRequest',
        response_type_name='LogScope',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a log scope.

      Args:
        request: (LoggingOrganizationsLocationsLogScopesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/logScopes/{logScopesId}',
        http_method='DELETE',
        method_id='logging.organizations.locations.logScopes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsLogScopesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a log scope.

      Args:
        request: (LoggingOrganizationsLocationsLogScopesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogScope) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/logScopes/{logScopesId}',
        http_method='GET',
        method_id='logging.organizations.locations.logScopes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsLogScopesGetRequest',
        response_type_name='LogScope',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists log scopes.

      Args:
        request: (LoggingOrganizationsLocationsLogScopesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogScopesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/logScopes',
        http_method='GET',
        method_id='logging.organizations.locations.logScopes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/logScopes',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsLogScopesListRequest',
        response_type_name='ListLogScopesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a log scope.

      Args:
        request: (LoggingOrganizationsLocationsLogScopesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogScope) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/logScopes/{logScopesId}',
        http_method='PATCH',
        method_id='logging.organizations.locations.logScopes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logScope',
        request_type_name='LoggingOrganizationsLocationsLogScopesPatchRequest',
        response_type_name='LogScope',
        supports_download=False,
    )

  class OrganizationsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_operations resource."""

    _NAME = 'organizations_locations_operations'

    def __init__(self, client):
      super(LoggingV2.OrganizationsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def ApproveRedaction(self, request, global_params=None):
      r"""Once the impact assessment completes, the redaction operation will move into WAIT_FOR_USER_APPROVAL stage wherein it's going to wait for the user to approve the redaction operation. Please note that the operation will be in progress at this point and if the user doesn't approve the redaction operation within the grace period, it will be auto-cancelled.The redaction operation can also be approved before operation moves into the WAIT_FOR_USER_APPROVAL stage. In that case redaction process will commence as soon as the impact assessment is complete. This is functionally similar to approving after the operation moves to WAIT_FOR_USER_APPROVAL stage but without any wait time to begin redaction.Once the user approves, the redaction operation will begin redacting the log entries.

      Args:
        request: (LoggingOrganizationsLocationsOperationsApproveRedactionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ApproveRedactionOperationResponse) The response message.
      """
      config = self.GetMethodConfig('ApproveRedaction')
      return self._RunMethod(
          config, request, global_params=global_params)

    ApproveRedaction.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}:approveRedaction',
        http_method='GET',
        method_id='logging.organizations.locations.operations.approveRedaction',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:approveRedaction',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsOperationsApproveRedactionRequest',
        response_type_name='ApproveRedactionOperationResponse',
        supports_download=False,
    )

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to Code.CANCELLED.

      Args:
        request: (LoggingOrganizationsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='logging.organizations.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='LoggingOrganizationsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (LoggingOrganizationsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='logging.organizations.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.

      Args:
        request: (LoggingOrganizationsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='logging.organizations.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/operations',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsRecentQueriesService(base_api.BaseApiService):
    """Service class for the organizations_locations_recentQueries resource."""

    _NAME = 'organizations_locations_recentQueries'

    def __init__(self, client):
      super(LoggingV2.OrganizationsLocationsRecentQueriesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the RecentQueries that were created by the user making the request.

      Args:
        request: (LoggingOrganizationsLocationsRecentQueriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRecentQueriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/recentQueries',
        http_method='GET',
        method_id='logging.organizations.locations.recentQueries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/recentQueries',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsRecentQueriesListRequest',
        response_type_name='ListRecentQueriesResponse',
        supports_download=False,
    )

  class OrganizationsLocationsSavedQueriesService(base_api.BaseApiService):
    """Service class for the organizations_locations_savedQueries resource."""

    _NAME = 'organizations_locations_savedQueries'

    def __init__(self, client):
      super(LoggingV2.OrganizationsLocationsSavedQueriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SavedQuery for the user making the request.

      Args:
        request: (LoggingOrganizationsLocationsSavedQueriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SavedQuery) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/savedQueries',
        http_method='POST',
        method_id='logging.organizations.locations.savedQueries.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['savedQueryId'],
        relative_path='v2/{+parent}/savedQueries',
        request_field='savedQuery',
        request_type_name='LoggingOrganizationsLocationsSavedQueriesCreateRequest',
        response_type_name='SavedQuery',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing SavedQuery that was created by the user making the request.

      Args:
        request: (LoggingOrganizationsLocationsSavedQueriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/savedQueries/{savedQueriesId}',
        http_method='DELETE',
        method_id='logging.organizations.locations.savedQueries.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsSavedQueriesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns all data associated with the requested query.

      Args:
        request: (LoggingOrganizationsLocationsSavedQueriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SavedQuery) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/savedQueries/{savedQueriesId}',
        http_method='GET',
        method_id='logging.organizations.locations.savedQueries.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsSavedQueriesGetRequest',
        response_type_name='SavedQuery',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the SavedQueries that were created by the user making the request.

      Args:
        request: (LoggingOrganizationsLocationsSavedQueriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSavedQueriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/savedQueries',
        http_method='GET',
        method_id='logging.organizations.locations.savedQueries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/savedQueries',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsSavedQueriesListRequest',
        response_type_name='ListSavedQueriesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing SavedQuery.

      Args:
        request: (LoggingOrganizationsLocationsSavedQueriesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SavedQuery) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/savedQueries/{savedQueriesId}',
        http_method='PATCH',
        method_id='logging.organizations.locations.savedQueries.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='savedQuery',
        request_type_name='LoggingOrganizationsLocationsSavedQueriesPatchRequest',
        response_type_name='SavedQuery',
        supports_download=False,
    )

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(LoggingV2.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (LoggingOrganizationsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}',
        http_method='GET',
        method_id='logging.organizations.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (LoggingOrganizationsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations',
        http_method='GET',
        method_id='logging.organizations.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/locations',
        request_field='',
        request_type_name='LoggingOrganizationsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class OrganizationsLogsService(base_api.BaseApiService):
    """Service class for the organizations_logs resource."""

    _NAME = 'organizations_logs'

    def __init__(self, client):
      super(LoggingV2.OrganizationsLogsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes all the log entries in a log for the global _Default Log Bucket. The log reappears if it receives new entries. Log entries written shortly before the delete operation might not be deleted. Entries received after the delete operation with a timestamp before the operation will be deleted.

      Args:
        request: (LoggingOrganizationsLogsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/logs/{logsId}',
        http_method='DELETE',
        method_id='logging.organizations.logs.delete',
        ordered_params=['logName'],
        path_params=['logName'],
        query_params=[],
        relative_path='v2/{+logName}',
        request_field='',
        request_type_name='LoggingOrganizationsLogsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the logs in projects, organizations, folders, or billing accounts. Only logs that have entries are listed.

      Args:
        request: (LoggingOrganizationsLogsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/logs',
        http_method='GET',
        method_id='logging.organizations.logs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'resourceNames'],
        relative_path='v2/{+parent}/logs',
        request_field='',
        request_type_name='LoggingOrganizationsLogsListRequest',
        response_type_name='ListLogsResponse',
        supports_download=False,
    )

  class OrganizationsSinksService(base_api.BaseApiService):
    """Service class for the organizations_sinks resource."""

    _NAME = 'organizations_sinks'

    def __init__(self, client):
      super(LoggingV2.OrganizationsSinksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a sink that exports specified log entries to a destination. The export begins upon ingress, unless the sink's writer_identity is not permitted to write to the destination. A sink can export log entries only from the resource owning the sink.

      Args:
        request: (LoggingOrganizationsSinksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sinks',
        http_method='POST',
        method_id='logging.organizations.sinks.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['customWriterIdentity', 'uniqueWriterIdentity'],
        relative_path='v2/{+parent}/sinks',
        request_field='logSink',
        request_type_name='LoggingOrganizationsSinksCreateRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a sink. If the sink has a unique writer_identity, then that service account is also deleted.

      Args:
        request: (LoggingOrganizationsSinksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sinks/{sinksId}',
        http_method='DELETE',
        method_id='logging.organizations.sinks.delete',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=[],
        relative_path='v2/{+sinkName}',
        request_field='',
        request_type_name='LoggingOrganizationsSinksDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a sink.

      Args:
        request: (LoggingOrganizationsSinksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sinks/{sinksId}',
        http_method='GET',
        method_id='logging.organizations.sinks.get',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=[],
        relative_path='v2/{+sinkName}',
        request_field='',
        request_type_name='LoggingOrganizationsSinksGetRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists sinks.

      Args:
        request: (LoggingOrganizationsSinksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSinksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sinks',
        http_method='GET',
        method_id='logging.organizations.sinks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/sinks',
        request_field='',
        request_type_name='LoggingOrganizationsSinksListRequest',
        response_type_name='ListSinksResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a sink. This method replaces the values of the destination and filter fields of the existing sink with the corresponding values from the new sink.The updated sink might also have a new writer_identity; see the unique_writer_identity field.

      Args:
        request: (LoggingOrganizationsSinksPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sinks/{sinksId}',
        http_method='PATCH',
        method_id='logging.organizations.sinks.patch',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=['customWriterIdentity', 'uniqueWriterIdentity', 'updateMask'],
        relative_path='v2/{+sinkName}',
        request_field='logSink',
        request_type_name='LoggingOrganizationsSinksPatchRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates a sink. This method replaces the values of the destination and filter fields of the existing sink with the corresponding values from the new sink.The updated sink might also have a new writer_identity; see the unique_writer_identity field.

      Args:
        request: (LoggingOrganizationsSinksUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sinks/{sinksId}',
        http_method='PUT',
        method_id='logging.organizations.sinks.update',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=['customWriterIdentity', 'uniqueWriterIdentity', 'updateMask'],
        relative_path='v2/{+sinkName}',
        request_field='logSink',
        request_type_name='LoggingOrganizationsSinksUpdateRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(LoggingV2.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetCmekSettings(self, request, global_params=None):
      r"""Gets the Logging CMEK settings for the given resource.Note: CMEK for the Log Router can be configured for Google Cloud projects, folders, organizations, and billing accounts. Once configured for an organization, it applies to all projects and folders in the Google Cloud organization.See Enabling CMEK for Log Router (https://cloud.google.com/logging/docs/routing/managed-encryption) for more information.

      Args:
        request: (LoggingOrganizationsGetCmekSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CmekSettings) The response message.
      """
      config = self.GetMethodConfig('GetCmekSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetCmekSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/cmekSettings',
        http_method='GET',
        method_id='logging.organizations.getCmekSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}/cmekSettings',
        request_field='',
        request_type_name='LoggingOrganizationsGetCmekSettingsRequest',
        response_type_name='CmekSettings',
        supports_download=False,
    )

    def GetSettings(self, request, global_params=None):
      r"""Gets the settings for the given resource.Note: Settings can be retrieved for Google Cloud projects, folders, organizations, and billing accounts.See View default resource settings for Logging (https://cloud.google.com/logging/docs/default-settings#view-org-settings) for more information.

      Args:
        request: (LoggingOrganizationsGetSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Settings) The response message.
      """
      config = self.GetMethodConfig('GetSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/settings',
        http_method='GET',
        method_id='logging.organizations.getSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}/settings',
        request_field='',
        request_type_name='LoggingOrganizationsGetSettingsRequest',
        response_type_name='Settings',
        supports_download=False,
    )

    def UpdateCmekSettings(self, request, global_params=None):
      r"""Updates the Log Router CMEK settings for the given resource.Note: CMEK for the Log Router can currently only be configured for Google Cloud organizations. Once configured, it applies to all projects and folders in the Google Cloud organization.UpdateCmekSettings fails when any of the following are true: The value of kms_key_name is invalid. The associated service account doesn't have the required roles/cloudkms.cryptoKeyEncrypterDecrypter role assigned for the key. Access to the key is disabled.See Enabling CMEK for Log Router (https://cloud.google.com/logging/docs/routing/managed-encryption) for more information.

      Args:
        request: (LoggingOrganizationsUpdateCmekSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CmekSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateCmekSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateCmekSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/cmekSettings',
        http_method='PATCH',
        method_id='logging.organizations.updateCmekSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}/cmekSettings',
        request_field='cmekSettings',
        request_type_name='LoggingOrganizationsUpdateCmekSettingsRequest',
        response_type_name='CmekSettings',
        supports_download=False,
    )

    def UpdateSettings(self, request, global_params=None):
      r"""Updates the settings for the given resource. This method applies to all feature configurations for organization and folders.UpdateSettings fails when any of the following are true: The value of storage_location either isn't supported by Logging or violates the location OrgPolicy. The default_sink_config field is set, but it has an unspecified filter write mode. The value of kms_key_name is invalid. The associated service account doesn't have the required roles/cloudkms.cryptoKeyEncrypterDecrypter role assigned for the key. Access to the key is disabled.See Configure default settings for organizations and folders (https://cloud.google.com/logging/docs/default-settings) for more information.

      Args:
        request: (LoggingOrganizationsUpdateSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Settings) The response message.
      """
      config = self.GetMethodConfig('UpdateSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/settings',
        http_method='PATCH',
        method_id='logging.organizations.updateSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}/settings',
        request_field='settings',
        request_type_name='LoggingOrganizationsUpdateSettingsRequest',
        response_type_name='Settings',
        supports_download=False,
    )

  class ProjectsExclusionsService(base_api.BaseApiService):
    """Service class for the projects_exclusions resource."""

    _NAME = 'projects_exclusions'

    def __init__(self, client):
      super(LoggingV2.ProjectsExclusionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new exclusion in the _Default sink in a specified parent resource. Only log entries belonging to that resource can be excluded. You can have up to 10 exclusions in a resource.

      Args:
        request: (LoggingProjectsExclusionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/exclusions',
        http_method='POST',
        method_id='logging.projects.exclusions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/exclusions',
        request_field='logExclusion',
        request_type_name='LoggingProjectsExclusionsCreateRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an exclusion in the _Default sink.

      Args:
        request: (LoggingProjectsExclusionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/exclusions/{exclusionsId}',
        http_method='DELETE',
        method_id='logging.projects.exclusions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingProjectsExclusionsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the description of an exclusion in the _Default sink.

      Args:
        request: (LoggingProjectsExclusionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/exclusions/{exclusionsId}',
        http_method='GET',
        method_id='logging.projects.exclusions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingProjectsExclusionsGetRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the exclusions on the _Default sink in a parent resource.

      Args:
        request: (LoggingProjectsExclusionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListExclusionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/exclusions',
        http_method='GET',
        method_id='logging.projects.exclusions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/exclusions',
        request_field='',
        request_type_name='LoggingProjectsExclusionsListRequest',
        response_type_name='ListExclusionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Changes one or more properties of an existing exclusion in the _Default sink.

      Args:
        request: (LoggingProjectsExclusionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogExclusion) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/exclusions/{exclusionsId}',
        http_method='PATCH',
        method_id='logging.projects.exclusions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logExclusion',
        request_type_name='LoggingProjectsExclusionsPatchRequest',
        response_type_name='LogExclusion',
        supports_download=False,
    )

  class ProjectsLocationsBucketsLinksService(base_api.BaseApiService):
    """Service class for the projects_locations_buckets_links resource."""

    _NAME = 'projects_locations_buckets_links'

    def __init__(self, client):
      super(LoggingV2.ProjectsLocationsBucketsLinksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Asynchronously creates a linked dataset in BigQuery which makes it possible to use BigQuery to read the logs stored in the log bucket. A log bucket may currently only contain one link.

      Args:
        request: (LoggingProjectsLocationsBucketsLinksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}/links',
        http_method='POST',
        method_id='logging.projects.locations.buckets.links.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['linkId'],
        relative_path='v2/{+parent}/links',
        request_field='link',
        request_type_name='LoggingProjectsLocationsBucketsLinksCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a link. This will also delete the corresponding BigQuery linked dataset.

      Args:
        request: (LoggingProjectsLocationsBucketsLinksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}/links/{linksId}',
        http_method='DELETE',
        method_id='logging.projects.locations.buckets.links.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingProjectsLocationsBucketsLinksDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a link.

      Args:
        request: (LoggingProjectsLocationsBucketsLinksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Link) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}/links/{linksId}',
        http_method='GET',
        method_id='logging.projects.locations.buckets.links.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingProjectsLocationsBucketsLinksGetRequest',
        response_type_name='Link',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists links.

      Args:
        request: (LoggingProjectsLocationsBucketsLinksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLinksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}/links',
        http_method='GET',
        method_id='logging.projects.locations.buckets.links.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/links',
        request_field='',
        request_type_name='LoggingProjectsLocationsBucketsLinksListRequest',
        response_type_name='ListLinksResponse',
        supports_download=False,
    )

  class ProjectsLocationsBucketsViewsLogsService(base_api.BaseApiService):
    """Service class for the projects_locations_buckets_views_logs resource."""

    _NAME = 'projects_locations_buckets_views_logs'

    def __init__(self, client):
      super(LoggingV2.ProjectsLocationsBucketsViewsLogsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the logs in projects, organizations, folders, or billing accounts. Only logs that have entries are listed.

      Args:
        request: (LoggingProjectsLocationsBucketsViewsLogsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}/logs',
        http_method='GET',
        method_id='logging.projects.locations.buckets.views.logs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'resourceNames'],
        relative_path='v2/{+parent}/logs',
        request_field='',
        request_type_name='LoggingProjectsLocationsBucketsViewsLogsListRequest',
        response_type_name='ListLogsResponse',
        supports_download=False,
    )

  class ProjectsLocationsBucketsViewsService(base_api.BaseApiService):
    """Service class for the projects_locations_buckets_views resource."""

    _NAME = 'projects_locations_buckets_views'

    def __init__(self, client):
      super(LoggingV2.ProjectsLocationsBucketsViewsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a view over log entries in a log bucket. A bucket may contain a maximum of 30 views.

      Args:
        request: (LoggingProjectsLocationsBucketsViewsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}/views',
        http_method='POST',
        method_id='logging.projects.locations.buckets.views.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['viewId'],
        relative_path='v2/{+parent}/views',
        request_field='logView',
        request_type_name='LoggingProjectsLocationsBucketsViewsCreateRequest',
        response_type_name='LogView',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a view on a log bucket. If an UNAVAILABLE error is returned, this indicates that system is not in a state where it can delete the view. If this occurs, please try again in a few minutes.

      Args:
        request: (LoggingProjectsLocationsBucketsViewsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='DELETE',
        method_id='logging.projects.locations.buckets.views.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingProjectsLocationsBucketsViewsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a view on a log bucket.

      Args:
        request: (LoggingProjectsLocationsBucketsViewsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='GET',
        method_id='logging.projects.locations.buckets.views.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingProjectsLocationsBucketsViewsGetRequest',
        response_type_name='LogView',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (LoggingProjectsLocationsBucketsViewsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}:getIamPolicy',
        http_method='POST',
        method_id='logging.projects.locations.buckets.views.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='LoggingProjectsLocationsBucketsViewsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists views on a log bucket.

      Args:
        request: (LoggingProjectsLocationsBucketsViewsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListViewsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}/views',
        http_method='GET',
        method_id='logging.projects.locations.buckets.views.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/views',
        request_field='',
        request_type_name='LoggingProjectsLocationsBucketsViewsListRequest',
        response_type_name='ListViewsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a view on a log bucket. This method replaces the value of the filter field from the existing view with the corresponding value from the new view. If an UNAVAILABLE error is returned, this indicates that system is not in a state where it can update the view. If this occurs, please try again in a few minutes.

      Args:
        request: (LoggingProjectsLocationsBucketsViewsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogView) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}',
        http_method='PATCH',
        method_id='logging.projects.locations.buckets.views.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logView',
        request_type_name='LoggingProjectsLocationsBucketsViewsPatchRequest',
        response_type_name='LogView',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (LoggingProjectsLocationsBucketsViewsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}:setIamPolicy',
        http_method='POST',
        method_id='logging.projects.locations.buckets.views.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='LoggingProjectsLocationsBucketsViewsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (LoggingProjectsLocationsBucketsViewsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}/views/{viewsId}:testIamPermissions',
        http_method='POST',
        method_id='logging.projects.locations.buckets.views.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='LoggingProjectsLocationsBucketsViewsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsBucketsService(base_api.BaseApiService):
    """Service class for the projects_locations_buckets resource."""

    _NAME = 'projects_locations_buckets'

    def __init__(self, client):
      super(LoggingV2.ProjectsLocationsBucketsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a log bucket that can be used to store log entries. After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingProjectsLocationsBucketsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets',
        http_method='POST',
        method_id='logging.projects.locations.buckets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bucketId'],
        relative_path='v2/{+parent}/buckets',
        request_field='logBucket',
        request_type_name='LoggingProjectsLocationsBucketsCreateRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def CreateAsync(self, request, global_params=None):
      r"""Creates a log bucket asynchronously that can be used to store log entries.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingProjectsLocationsBucketsCreateAsyncRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CreateAsync')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateAsync.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets:createAsync',
        http_method='POST',
        method_id='logging.projects.locations.buckets.createAsync',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bucketId'],
        relative_path='v2/{+parent}/buckets:createAsync',
        request_field='logBucket',
        request_type_name='LoggingProjectsLocationsBucketsCreateAsyncRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a log bucket.Changes the bucket's lifecycle_state to the DELETE_REQUESTED state. After 7 days, the bucket will be purged and all log entries in the bucket will be permanently deleted.

      Args:
        request: (LoggingProjectsLocationsBucketsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='DELETE',
        method_id='logging.projects.locations.buckets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingProjectsLocationsBucketsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a log bucket.

      Args:
        request: (LoggingProjectsLocationsBucketsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='GET',
        method_id='logging.projects.locations.buckets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingProjectsLocationsBucketsGetRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists log buckets.

      Args:
        request: (LoggingProjectsLocationsBucketsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBucketsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets',
        http_method='GET',
        method_id='logging.projects.locations.buckets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/buckets',
        request_field='',
        request_type_name='LoggingProjectsLocationsBucketsListRequest',
        response_type_name='ListBucketsResponse',
        supports_download=False,
    )

    def Move(self, request, global_params=None):
      r"""Moves a bucket from one location to another location. This method creates a new bucket at the new location with an ACTIVE state. The bucket at the old location will remain available with an ARCHIVED state such that it is queryable but can no longer be used as a sink destination. All corresponding sinks are updated to point to the new bucket. Currently, the contents of the old bucket are not copied to the new one. In order to be movable, a bucket must satisfy the following restrictions: It's a _Default or _Required bucket. It has a location of "global". It has a non-project parent when it's a _Default bucket.

      Args:
        request: (MoveBucketRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Move')
      return self._RunMethod(
          config, request, global_params=global_params)

    Move.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}:move',
        http_method='POST',
        method_id='logging.projects.locations.buckets.move',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:move',
        request_field='<request>',
        request_type_name='MoveBucketRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a log bucket.If the bucket has a lifecycle_state of DELETE_REQUESTED, then FAILED_PRECONDITION will be returned.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingProjectsLocationsBucketsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogBucket) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}',
        http_method='PATCH',
        method_id='logging.projects.locations.buckets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logBucket',
        request_type_name='LoggingProjectsLocationsBucketsPatchRequest',
        response_type_name='LogBucket',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a log bucket. A bucket that has been deleted can be undeleted within the grace period of 7 days.

      Args:
        request: (LoggingProjectsLocationsBucketsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}:undelete',
        http_method='POST',
        method_id='logging.projects.locations.buckets.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:undelete',
        request_field='undeleteBucketRequest',
        request_type_name='LoggingProjectsLocationsBucketsUndeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def UpdateAsync(self, request, global_params=None):
      r"""Updates a log bucket asynchronously.If the bucket has a lifecycle_state of DELETE_REQUESTED, then FAILED_PRECONDITION will be returned.After a bucket has been created, the bucket's location cannot be changed.

      Args:
        request: (LoggingProjectsLocationsBucketsUpdateAsyncRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateAsync')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateAsync.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/buckets/{bucketsId}:updateAsync',
        http_method='POST',
        method_id='logging.projects.locations.buckets.updateAsync',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}:updateAsync',
        request_field='logBucket',
        request_type_name='LoggingProjectsLocationsBucketsUpdateAsyncRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsLogScopesService(base_api.BaseApiService):
    """Service class for the projects_locations_logScopes resource."""

    _NAME = 'projects_locations_logScopes'

    def __init__(self, client):
      super(LoggingV2.ProjectsLocationsLogScopesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a log scope.

      Args:
        request: (LoggingProjectsLocationsLogScopesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogScope) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/logScopes',
        http_method='POST',
        method_id='logging.projects.locations.logScopes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['logScopeId'],
        relative_path='v2/{+parent}/logScopes',
        request_field='logScope',
        request_type_name='LoggingProjectsLocationsLogScopesCreateRequest',
        response_type_name='LogScope',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a log scope.

      Args:
        request: (LoggingProjectsLocationsLogScopesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/logScopes/{logScopesId}',
        http_method='DELETE',
        method_id='logging.projects.locations.logScopes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingProjectsLocationsLogScopesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a log scope.

      Args:
        request: (LoggingProjectsLocationsLogScopesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogScope) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/logScopes/{logScopesId}',
        http_method='GET',
        method_id='logging.projects.locations.logScopes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingProjectsLocationsLogScopesGetRequest',
        response_type_name='LogScope',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists log scopes.

      Args:
        request: (LoggingProjectsLocationsLogScopesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogScopesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/logScopes',
        http_method='GET',
        method_id='logging.projects.locations.logScopes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/logScopes',
        request_field='',
        request_type_name='LoggingProjectsLocationsLogScopesListRequest',
        response_type_name='ListLogScopesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a log scope.

      Args:
        request: (LoggingProjectsLocationsLogScopesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogScope) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/logScopes/{logScopesId}',
        http_method='PATCH',
        method_id='logging.projects.locations.logScopes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='logScope',
        request_type_name='LoggingProjectsLocationsLogScopesPatchRequest',
        response_type_name='LogScope',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(LoggingV2.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def ApproveRedaction(self, request, global_params=None):
      r"""Once the impact assessment completes, the redaction operation will move into WAIT_FOR_USER_APPROVAL stage wherein it's going to wait for the user to approve the redaction operation. Please note that the operation will be in progress at this point and if the user doesn't approve the redaction operation within the grace period, it will be auto-cancelled.The redaction operation can also be approved before operation moves into the WAIT_FOR_USER_APPROVAL stage. In that case redaction process will commence as soon as the impact assessment is complete. This is functionally similar to approving after the operation moves to WAIT_FOR_USER_APPROVAL stage but without any wait time to begin redaction.Once the user approves, the redaction operation will begin redacting the log entries.

      Args:
        request: (LoggingProjectsLocationsOperationsApproveRedactionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ApproveRedactionOperationResponse) The response message.
      """
      config = self.GetMethodConfig('ApproveRedaction')
      return self._RunMethod(
          config, request, global_params=global_params)

    ApproveRedaction.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:approveRedaction',
        http_method='GET',
        method_id='logging.projects.locations.operations.approveRedaction',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:approveRedaction',
        request_field='',
        request_type_name='LoggingProjectsLocationsOperationsApproveRedactionRequest',
        response_type_name='ApproveRedactionOperationResponse',
        supports_download=False,
    )

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to Code.CANCELLED.

      Args:
        request: (LoggingProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='logging.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='LoggingProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (LoggingProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='logging.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.

      Args:
        request: (LoggingProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='logging.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/operations',
        request_field='',
        request_type_name='LoggingProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsRecentQueriesService(base_api.BaseApiService):
    """Service class for the projects_locations_recentQueries resource."""

    _NAME = 'projects_locations_recentQueries'

    def __init__(self, client):
      super(LoggingV2.ProjectsLocationsRecentQueriesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the RecentQueries that were created by the user making the request.

      Args:
        request: (LoggingProjectsLocationsRecentQueriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRecentQueriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/recentQueries',
        http_method='GET',
        method_id='logging.projects.locations.recentQueries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/recentQueries',
        request_field='',
        request_type_name='LoggingProjectsLocationsRecentQueriesListRequest',
        response_type_name='ListRecentQueriesResponse',
        supports_download=False,
    )

  class ProjectsLocationsSavedQueriesService(base_api.BaseApiService):
    """Service class for the projects_locations_savedQueries resource."""

    _NAME = 'projects_locations_savedQueries'

    def __init__(self, client):
      super(LoggingV2.ProjectsLocationsSavedQueriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SavedQuery for the user making the request.

      Args:
        request: (LoggingProjectsLocationsSavedQueriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SavedQuery) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/savedQueries',
        http_method='POST',
        method_id='logging.projects.locations.savedQueries.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['savedQueryId'],
        relative_path='v2/{+parent}/savedQueries',
        request_field='savedQuery',
        request_type_name='LoggingProjectsLocationsSavedQueriesCreateRequest',
        response_type_name='SavedQuery',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing SavedQuery that was created by the user making the request.

      Args:
        request: (LoggingProjectsLocationsSavedQueriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/savedQueries/{savedQueriesId}',
        http_method='DELETE',
        method_id='logging.projects.locations.savedQueries.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingProjectsLocationsSavedQueriesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns all data associated with the requested query.

      Args:
        request: (LoggingProjectsLocationsSavedQueriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SavedQuery) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/savedQueries/{savedQueriesId}',
        http_method='GET',
        method_id='logging.projects.locations.savedQueries.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingProjectsLocationsSavedQueriesGetRequest',
        response_type_name='SavedQuery',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the SavedQueries that were created by the user making the request.

      Args:
        request: (LoggingProjectsLocationsSavedQueriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSavedQueriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/savedQueries',
        http_method='GET',
        method_id='logging.projects.locations.savedQueries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/savedQueries',
        request_field='',
        request_type_name='LoggingProjectsLocationsSavedQueriesListRequest',
        response_type_name='ListSavedQueriesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing SavedQuery.

      Args:
        request: (LoggingProjectsLocationsSavedQueriesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SavedQuery) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/savedQueries/{savedQueriesId}',
        http_method='PATCH',
        method_id='logging.projects.locations.savedQueries.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='savedQuery',
        request_type_name='LoggingProjectsLocationsSavedQueriesPatchRequest',
        response_type_name='SavedQuery',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(LoggingV2.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (LoggingProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='logging.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='LoggingProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (LoggingProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations',
        http_method='GET',
        method_id='logging.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/locations',
        request_field='',
        request_type_name='LoggingProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsLogsService(base_api.BaseApiService):
    """Service class for the projects_logs resource."""

    _NAME = 'projects_logs'

    def __init__(self, client):
      super(LoggingV2.ProjectsLogsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes all the log entries in a log for the global _Default Log Bucket. The log reappears if it receives new entries. Log entries written shortly before the delete operation might not be deleted. Entries received after the delete operation with a timestamp before the operation will be deleted.

      Args:
        request: (LoggingProjectsLogsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/logs/{logsId}',
        http_method='DELETE',
        method_id='logging.projects.logs.delete',
        ordered_params=['logName'],
        path_params=['logName'],
        query_params=[],
        relative_path='v2/{+logName}',
        request_field='',
        request_type_name='LoggingProjectsLogsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the logs in projects, organizations, folders, or billing accounts. Only logs that have entries are listed.

      Args:
        request: (LoggingProjectsLogsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/logs',
        http_method='GET',
        method_id='logging.projects.logs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'resourceNames'],
        relative_path='v2/{+parent}/logs',
        request_field='',
        request_type_name='LoggingProjectsLogsListRequest',
        response_type_name='ListLogsResponse',
        supports_download=False,
    )

  class ProjectsMetricsService(base_api.BaseApiService):
    """Service class for the projects_metrics resource."""

    _NAME = 'projects_metrics'

    def __init__(self, client):
      super(LoggingV2.ProjectsMetricsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a logs-based metric.

      Args:
        request: (LoggingProjectsMetricsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogMetric) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/metrics',
        http_method='POST',
        method_id='logging.projects.metrics.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/metrics',
        request_field='logMetric',
        request_type_name='LoggingProjectsMetricsCreateRequest',
        response_type_name='LogMetric',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a logs-based metric.

      Args:
        request: (LoggingProjectsMetricsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/metrics/{metricsId}',
        http_method='DELETE',
        method_id='logging.projects.metrics.delete',
        ordered_params=['metricName'],
        path_params=['metricName'],
        query_params=[],
        relative_path='v2/{+metricName}',
        request_field='',
        request_type_name='LoggingProjectsMetricsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a logs-based metric.

      Args:
        request: (LoggingProjectsMetricsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogMetric) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/metrics/{metricsId}',
        http_method='GET',
        method_id='logging.projects.metrics.get',
        ordered_params=['metricName'],
        path_params=['metricName'],
        query_params=[],
        relative_path='v2/{+metricName}',
        request_field='',
        request_type_name='LoggingProjectsMetricsGetRequest',
        response_type_name='LogMetric',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists logs-based metrics.

      Args:
        request: (LoggingProjectsMetricsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLogMetricsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/metrics',
        http_method='GET',
        method_id='logging.projects.metrics.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/metrics',
        request_field='',
        request_type_name='LoggingProjectsMetricsListRequest',
        response_type_name='ListLogMetricsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Creates or updates a logs-based metric.

      Args:
        request: (LoggingProjectsMetricsUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogMetric) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/metrics/{metricsId}',
        http_method='PUT',
        method_id='logging.projects.metrics.update',
        ordered_params=['metricName'],
        path_params=['metricName'],
        query_params=[],
        relative_path='v2/{+metricName}',
        request_field='logMetric',
        request_type_name='LoggingProjectsMetricsUpdateRequest',
        response_type_name='LogMetric',
        supports_download=False,
    )

  class ProjectsSinksService(base_api.BaseApiService):
    """Service class for the projects_sinks resource."""

    _NAME = 'projects_sinks'

    def __init__(self, client):
      super(LoggingV2.ProjectsSinksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a sink that exports specified log entries to a destination. The export begins upon ingress, unless the sink's writer_identity is not permitted to write to the destination. A sink can export log entries only from the resource owning the sink.

      Args:
        request: (LoggingProjectsSinksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sinks',
        http_method='POST',
        method_id='logging.projects.sinks.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['customWriterIdentity', 'uniqueWriterIdentity'],
        relative_path='v2/{+parent}/sinks',
        request_field='logSink',
        request_type_name='LoggingProjectsSinksCreateRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a sink. If the sink has a unique writer_identity, then that service account is also deleted.

      Args:
        request: (LoggingProjectsSinksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sinks/{sinksId}',
        http_method='DELETE',
        method_id='logging.projects.sinks.delete',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=[],
        relative_path='v2/{+sinkName}',
        request_field='',
        request_type_name='LoggingProjectsSinksDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a sink.

      Args:
        request: (LoggingProjectsSinksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sinks/{sinksId}',
        http_method='GET',
        method_id='logging.projects.sinks.get',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=[],
        relative_path='v2/{+sinkName}',
        request_field='',
        request_type_name='LoggingProjectsSinksGetRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists sinks.

      Args:
        request: (LoggingProjectsSinksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSinksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sinks',
        http_method='GET',
        method_id='logging.projects.sinks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/sinks',
        request_field='',
        request_type_name='LoggingProjectsSinksListRequest',
        response_type_name='ListSinksResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a sink. This method replaces the values of the destination and filter fields of the existing sink with the corresponding values from the new sink.The updated sink might also have a new writer_identity; see the unique_writer_identity field.

      Args:
        request: (LoggingProjectsSinksPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sinks/{sinksId}',
        http_method='PATCH',
        method_id='logging.projects.sinks.patch',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=['customWriterIdentity', 'uniqueWriterIdentity', 'updateMask'],
        relative_path='v2/{+sinkName}',
        request_field='logSink',
        request_type_name='LoggingProjectsSinksPatchRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates a sink. This method replaces the values of the destination and filter fields of the existing sink with the corresponding values from the new sink.The updated sink might also have a new writer_identity; see the unique_writer_identity field.

      Args:
        request: (LoggingProjectsSinksUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sinks/{sinksId}',
        http_method='PUT',
        method_id='logging.projects.sinks.update',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=['customWriterIdentity', 'uniqueWriterIdentity', 'updateMask'],
        relative_path='v2/{+sinkName}',
        request_field='logSink',
        request_type_name='LoggingProjectsSinksUpdateRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(LoggingV2.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetCmekSettings(self, request, global_params=None):
      r"""Gets the Logging CMEK settings for the given resource.Note: CMEK for the Log Router can be configured for Google Cloud projects, folders, organizations, and billing accounts. Once configured for an organization, it applies to all projects and folders in the Google Cloud organization.See Enabling CMEK for Log Router (https://cloud.google.com/logging/docs/routing/managed-encryption) for more information.

      Args:
        request: (LoggingProjectsGetCmekSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CmekSettings) The response message.
      """
      config = self.GetMethodConfig('GetCmekSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetCmekSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/cmekSettings',
        http_method='GET',
        method_id='logging.projects.getCmekSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}/cmekSettings',
        request_field='',
        request_type_name='LoggingProjectsGetCmekSettingsRequest',
        response_type_name='CmekSettings',
        supports_download=False,
    )

    def GetSettings(self, request, global_params=None):
      r"""Gets the settings for the given resource.Note: Settings can be retrieved for Google Cloud projects, folders, organizations, and billing accounts.See View default resource settings for Logging (https://cloud.google.com/logging/docs/default-settings#view-org-settings) for more information.

      Args:
        request: (LoggingProjectsGetSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Settings) The response message.
      """
      config = self.GetMethodConfig('GetSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/settings',
        http_method='GET',
        method_id='logging.projects.getSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}/settings',
        request_field='',
        request_type_name='LoggingProjectsGetSettingsRequest',
        response_type_name='Settings',
        supports_download=False,
    )

  class QueryService(base_api.BaseApiService):
    """Service class for the query resource."""

    _NAME = 'query'

    def __init__(self, client):
      super(LoggingV2.QueryService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a running query. If called on a handle from a multi-step query, the entire query is cancelled.

      Args:
        request: (CancelQueryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CancelQueryResponse) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='logging.query.cancel',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v2/query:cancel',
        request_field='<request>',
        request_type_name='CancelQueryRequest',
        response_type_name='CancelQueryResponse',
        supports_download=False,
    )

    def Validate(self, request, global_params=None):
      r"""Validates a query before passing it to QueryData and returns query metadata synchronously.

      Args:
        request: (QueryDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ValidateQueryResponse) The response message.
      """
      config = self.GetMethodConfig('Validate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Validate.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='logging.query.validate',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v2/query:validate',
        request_field='<request>',
        request_type_name='QueryDataRequest',
        response_type_name='ValidateQueryResponse',
        supports_download=False,
    )

    def ValidateLocal(self, request, global_params=None):
      r"""Validates a query before passing it to QueryDataLocal and returns query metadata synchronously.

      Args:
        request: (QueryDataLocalRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ValidateQueryResponse) The response message.
      """
      config = self.GetMethodConfig('ValidateLocal')
      return self._RunMethod(
          config, request, global_params=global_params)

    ValidateLocal.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='logging.query.validateLocal',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v2/query:validateLocal',
        request_field='<request>',
        request_type_name='QueryDataLocalRequest',
        response_type_name='ValidateQueryResponse',
        supports_download=False,
    )

  class SinksService(base_api.BaseApiService):
    """Service class for the sinks resource."""

    _NAME = 'sinks'

    def __init__(self, client):
      super(LoggingV2.SinksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a sink that exports specified log entries to a destination. The export begins upon ingress, unless the sink's writer_identity is not permitted to write to the destination. A sink can export log entries only from the resource owning the sink.

      Args:
        request: (LoggingSinksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/sinks',
        http_method='POST',
        method_id='logging.sinks.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['customWriterIdentity', 'uniqueWriterIdentity'],
        relative_path='v2/{+parent}/sinks',
        request_field='logSink',
        request_type_name='LoggingSinksCreateRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a sink. If the sink has a unique writer_identity, then that service account is also deleted.

      Args:
        request: (LoggingSinksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/sinks/{sinksId}',
        http_method='DELETE',
        method_id='logging.sinks.delete',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=[],
        relative_path='v2/{+sinkName}',
        request_field='',
        request_type_name='LoggingSinksDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a sink.

      Args:
        request: (LoggingSinksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/sinks/{sinksId}',
        http_method='GET',
        method_id='logging.sinks.get',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=[],
        relative_path='v2/{+sinkName}',
        request_field='',
        request_type_name='LoggingSinksGetRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists sinks.

      Args:
        request: (LoggingSinksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSinksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/sinks',
        http_method='GET',
        method_id='logging.sinks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/sinks',
        request_field='',
        request_type_name='LoggingSinksListRequest',
        response_type_name='ListSinksResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates a sink. This method replaces the values of the destination and filter fields of the existing sink with the corresponding values from the new sink.The updated sink might also have a new writer_identity; see the unique_writer_identity field.

      Args:
        request: (LoggingSinksUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LogSink) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/sinks/{sinksId}',
        http_method='PUT',
        method_id='logging.sinks.update',
        ordered_params=['sinkName'],
        path_params=['sinkName'],
        query_params=['customWriterIdentity', 'uniqueWriterIdentity', 'updateMask'],
        relative_path='v2/{+sinkName}',
        request_field='logSink',
        request_type_name='LoggingSinksUpdateRequest',
        response_type_name='LogSink',
        supports_download=False,
    )

  class V2Service(base_api.BaseApiService):
    """Service class for the v2 resource."""

    _NAME = 'v2'

    def __init__(self, client):
      super(LoggingV2.V2Service, self).__init__(client)
      self._upload_configs = {
          }

    def GetCmekSettings(self, request, global_params=None):
      r"""Gets the Logging CMEK settings for the given resource.Note: CMEK for the Log Router can be configured for Google Cloud projects, folders, organizations, and billing accounts. Once configured for an organization, it applies to all projects and folders in the Google Cloud organization.See Enabling CMEK for Log Router (https://cloud.google.com/logging/docs/routing/managed-encryption) for more information.

      Args:
        request: (LoggingGetCmekSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CmekSettings) The response message.
      """
      config = self.GetMethodConfig('GetCmekSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetCmekSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/cmekSettings',
        http_method='GET',
        method_id='logging.getCmekSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}/cmekSettings',
        request_field='',
        request_type_name='LoggingGetCmekSettingsRequest',
        response_type_name='CmekSettings',
        supports_download=False,
    )

    def GetSettings(self, request, global_params=None):
      r"""Gets the settings for the given resource.Note: Settings can be retrieved for Google Cloud projects, folders, organizations, and billing accounts.See View default resource settings for Logging (https://cloud.google.com/logging/docs/default-settings#view-org-settings) for more information.

      Args:
        request: (LoggingGetSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Settings) The response message.
      """
      config = self.GetMethodConfig('GetSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/settings',
        http_method='GET',
        method_id='logging.getSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}/settings',
        request_field='',
        request_type_name='LoggingGetSettingsRequest',
        response_type_name='Settings',
        supports_download=False,
    )

    def UpdateCmekSettings(self, request, global_params=None):
      r"""Updates the Log Router CMEK settings for the given resource.Note: CMEK for the Log Router can currently only be configured for Google Cloud organizations. Once configured, it applies to all projects and folders in the Google Cloud organization.UpdateCmekSettings fails when any of the following are true: The value of kms_key_name is invalid. The associated service account doesn't have the required roles/cloudkms.cryptoKeyEncrypterDecrypter role assigned for the key. Access to the key is disabled.See Enabling CMEK for Log Router (https://cloud.google.com/logging/docs/routing/managed-encryption) for more information.

      Args:
        request: (LoggingUpdateCmekSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CmekSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateCmekSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateCmekSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/cmekSettings',
        http_method='PATCH',
        method_id='logging.updateCmekSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}/cmekSettings',
        request_field='cmekSettings',
        request_type_name='LoggingUpdateCmekSettingsRequest',
        response_type_name='CmekSettings',
        supports_download=False,
    )

    def UpdateSettings(self, request, global_params=None):
      r"""Updates the settings for the given resource. This method applies to all feature configurations for organization and folders.UpdateSettings fails when any of the following are true: The value of storage_location either isn't supported by Logging or violates the location OrgPolicy. The default_sink_config field is set, but it has an unspecified filter write mode. The value of kms_key_name is invalid. The associated service account doesn't have the required roles/cloudkms.cryptoKeyEncrypterDecrypter role assigned for the key. Access to the key is disabled.See Configure default settings for organizations and folders (https://cloud.google.com/logging/docs/default-settings) for more information.

      Args:
        request: (LoggingUpdateSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Settings) The response message.
      """
      config = self.GetMethodConfig('UpdateSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/{v2Id}/{v2Id1}/settings',
        http_method='PATCH',
        method_id='logging.updateSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}/settings',
        request_field='settings',
        request_type_name='LoggingUpdateSettingsRequest',
        response_type_name='Settings',
        supports_download=False,
    )
