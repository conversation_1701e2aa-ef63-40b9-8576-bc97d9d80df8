"""Generated client library for eventflow version v1beta2."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.eventflow.v1beta2 import eventflow_v1beta2_messages as messages


class EventflowV1beta2(base_api.BaseApiClient):
  """Generated client library for service eventflow version v1beta2."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://eventflow.googleapis.com/'
  MTLS_BASE_URL = 'https://eventflow.mtls.googleapis.com/'

  _PACKAGE = 'eventflow'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/firebase']
  _VERSION = 'v1beta2'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'EventflowV1beta2'
  _URL_VERSION = 'v1beta2'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new eventflow handle."""
    url = url or self.BASE_URL
    super(EventflowV1beta2, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_flows = self.ProjectsFlowsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsFlowsService(base_api.BaseApiService):
    """Service class for the projects_flows resource."""

    _NAME = 'projects_flows'

    def __init__(self, client):
      super(EventflowV1beta2.ProjectsFlowsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a flow, and returns the new Flow.

      Args:
        request: (EventflowProjectsFlowsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Flow) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/flows',
        http_method='POST',
        method_id='eventflow.projects.flows.create',
        ordered_params=['namespace'],
        path_params=['namespace'],
        query_params=[],
        relative_path='v1beta2/projects/{+namespace}/flows',
        request_field='flow',
        request_type_name='EventflowProjectsFlowsCreateRequest',
        response_type_name='Flow',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a flow. Returns NOT_FOUND if the item does not exist.

      Args:
        request: (EventflowProjectsFlowsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/flows/{flowsId}',
        http_method='DELETE',
        method_id='eventflow.projects.flows.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='EventflowProjectsFlowsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a flow. Returns NOT_FOUND if the flow does not exist.

      Args:
        request: (EventflowProjectsFlowsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Flow) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/flows/{flowsId}',
        http_method='GET',
        method_id='eventflow.projects.flows.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='EventflowProjectsFlowsGetRequest',
        response_type_name='Flow',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists flows.

      Args:
        request: (EventflowProjectsFlowsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFlowsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/flows',
        http_method='GET',
        method_id='eventflow.projects.flows.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta2/{+parent}/flows',
        request_field='',
        request_type_name='EventflowProjectsFlowsListRequest',
        response_type_name='ListFlowsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates a flow, returning the updated flow. Empty fields (proto3 default values) mean don't change those fields. The call returns INVALID_ARGUMENT status if the spec.name, spec.namespace, or spec.trigger.event_type is change. trigger.event_type is changed.

      Args:
        request: (EventflowProjectsFlowsUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Flow) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/flows/{flowsId}',
        http_method='PUT',
        method_id='eventflow.projects.flows.update',
        ordered_params=['namespace', 'name'],
        path_params=['name', 'namespace'],
        query_params=[],
        relative_path='v1beta2/projects/{+namespace}/flows/{+name}',
        request_field='flow',
        request_type_name='EventflowProjectsFlowsUpdateRequest',
        response_type_name='Flow',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(EventflowV1beta2.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
