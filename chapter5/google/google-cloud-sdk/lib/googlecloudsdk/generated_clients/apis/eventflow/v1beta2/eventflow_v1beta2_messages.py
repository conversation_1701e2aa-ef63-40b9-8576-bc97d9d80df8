"""Generated message classes for eventflow version v1beta2.

Creates and manages flows, each of which that specifies a trigger condition
for emitting an event and a resulting action that should happen.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'eventflow'


class Action(_messages.Message):
  r"""A consumer of events running in a Processor, for example a particular
  Google Cloud Function.

  Fields:
    name: Where the event will be delivered to, for example "projects/my-
      project-id/locations/mordor-central1/functions/functionName"
    processor: Where the action runs. For example "google.serverless" or
      "http".
  """

  name = _messages.StringField(1)
  processor = _messages.StringField(2)


class Condition(_messages.Message):
  r"""The different dimensions of the status.

  Enums:
    StatusValueValuesEnum: Required. The actual status.
    TypeValueValuesEnum: Required. What type of condition is this.

  Fields:
    lastHeartbeatTime: Last time we got an update on a given condition.
    lastTransitionTime: Last time the condition transit from one status to
      another.
    message: Human-readable message indicating details about last transition.
    reason: One-word CamelCase reason for the condition's last transition.
    status: Required. The actual status.
    type: Required. What type of condition is this.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Required. The actual status.

    Values:
      UNKNOWN: Should never be set in the API.
      TRUE: The condition is active.
      FALSE: The condition is inactive.
    """
    UNKNOWN = 0
    TRUE = 1
    FALSE = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. What type of condition is this.

    Values:
      UNKNOWN_TYPE: Never set by the API.
      SERVER_ERROR: Whether there is an error caused by the server hosting
        this API. The reason field will be the error code. The message field
        will be the error message.
      CLIENT_ERROR: Whether there is an error caused by how the client calls
        this API. The reason field will be the error code. The message field
        will be the error message.
      DEPLOYED: Whether the flow is deployed to all regions. The reason field
        will be of the following form: 0of4RegionsDeployed 1of4RegionsDeployed
        4of4RegionsDeployed The message field will be a comma-separated list
        of regions deployed. For example "us-west1,europe-west1,us-east1,asia-
        east1"
      SOURCE_ACTIVATED: Whether the source is activated. The reason field will
        be one of: DoesNotNeedActivation AwaitingResponse
        AcknowledgedActivation ActivationFailed The message field will be the
        error code and message in case of error.
      ACTION_ACTIVATED: Whether the action is activated. The reason field will
        be one of: DoesNotNeedActivation AwaitingResponse
        AcknowledgedActivation ActivationFailed The message field will be the
        error code and message in case of error.
    """
    UNKNOWN_TYPE = 0
    SERVER_ERROR = 1
    CLIENT_ERROR = 2
    DEPLOYED = 3
    SOURCE_ACTIVATED = 4
    ACTION_ACTIVATED = 5

  lastHeartbeatTime = _messages.StringField(1)
  lastTransitionTime = _messages.StringField(2)
  message = _messages.StringField(3)
  reason = _messages.StringField(4)
  status = _messages.EnumField('StatusValueValuesEnum', 5)
  type = _messages.EnumField('TypeValueValuesEnum', 6)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EventTrigger(_messages.Message):
  r"""An EventTrigger represents an interest in a subset of events occurring
  in a service.

  Fields:
    eventType: Required. The type of event to observe. For example:
      `google.storage.object.finalize` and
      `google.firebase.analytics.event.log`. Event type consists of three
      parts: 1. namespace: The domain name of the organization in reverse-
      domain notation (e.g. `acme.net` appears as `net.acme`) and any
      organization specific subdivisions. If the organization's top-level
      domain is `com`, the top-level domain is omitted (e.g. `google.com`
      appears as `google`). For example, `google.storage` and
      `google.firebase.analytics`. 2. resource type: The type of resource on
      which event occurs. For example, the Google Cloud Storage API includes
      the types `object` and `bucket`. 3. action: The action that generates
      the event. For example, actions for a Google Cloud Storage Object
      include 'finalize' and 'delete'. These parts are lower case and joined
      by '.'.
    resource: Required. The resource(s) from which to observe events, for
      example, `projects/_/buckets/myBucket/objects/{objectPath=**}`. Can be a
      specific resource or use wildcards to match a set of resources.
      Wildcards can either match a single segment in the resource name, using
      '*', or multiple segments, using '**'. For example,
      `projects/myProject/buckets/*/objects/**` would match all objects in all
      buckets in the 'myProject' project. The contents of wildcards can also
      be captured. This is done by assigning it to a variable name in braces.
      For example,
      `projects/myProject/buckets/{bucket_id=*}/objects/{object_path=**}`.
      Additionally, a single segment capture can omit `=*` and a multiple
      segment capture can specify additional structure. For example, the
      following all match the same buckets, but capture different data:
      `projects/myProject/buckets/*/objects/users/*/data/**` `projects/myProje
      ct/buckets/{bucket_id=*}/objects/users/{user_id}/data/{data_path=**}` `p
      rojects/myProject/buckets/{bucket_id}/objects/{object_path=users/*/data/
      **}` Not all syntactically correct values are accepted by all services.
      For example: 1. The authorization model must support it. Google Cloud
      Functions only allows EventTriggers to be deployed that observe
      resources in the same project as the `CloudFunction`. 2. The resource
      type must match the pattern expected for an `event_type`. For example,
      an `EventTrigger` that has an `event_type` of
      "google.pubsub.topic.publish" should have a resource that matches Google
      Cloud Pub/Sub topics. Additionally, some services may support short
      names when creating an `EventTrigger`. These will always be returned in
      the normalized "long" format. See each *service's* documentation for
      supported formats.
    service: The hostname of the service that should be observed. If no string
      is provided, the default service implementing the API will be used. For
      example, `storage.googleapis.com` is the default for all event types in
      the 'google.storage` namespace.
  """

  eventType = _messages.StringField(1)
  resource = _messages.StringField(2)
  service = _messages.StringField(3)


class EventflowProjectsFlowsCreateRequest(_messages.Message):
  r"""A EventflowProjectsFlowsCreateRequest object.

  Fields:
    flow: A Flow resource to be passed as the request body.
    namespace: Namespace defines the space within each name must be unique. An
      empty namespace is equivalent to the "default" namespace, but "default"
      is the canonical representation. Not all objects are required to be
      scoped to a namespace - the value of this field for those objects will
      be empty. Must be a DNS_LABEL. Cannot be updated. More info:
      http://kubernetes.io/docs/user-guide/namespaces +optional
  """

  flow = _messages.MessageField('Flow', 1)
  namespace = _messages.StringField(2, required=True)


class EventflowProjectsFlowsDeleteRequest(_messages.Message):
  r"""A EventflowProjectsFlowsDeleteRequest object.

  Fields:
    name: The name of the flow, of the form
      "projects/{projectId}/flows/{flowId}". (Note, this is different from the
      flowId that is stored in flow.metadata.name.)
  """

  name = _messages.StringField(1, required=True)


class EventflowProjectsFlowsGetRequest(_messages.Message):
  r"""A EventflowProjectsFlowsGetRequest object.

  Fields:
    name: The name of the flow, of the form
      "projects/{projectId}/flows/{flowId}". (Note, this is different from the
      flowId that is stored in flow.metadata.name.)
  """

  name = _messages.StringField(1, required=True)


class EventflowProjectsFlowsListRequest(_messages.Message):
  r"""A EventflowProjectsFlowsListRequest object.

  Fields:
    pageSize: Requested page size. Server may return more items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: The parent, of form "projects/{projectId}", from which the flows
      should be listed.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class EventflowProjectsFlowsUpdateRequest(_messages.Message):
  r"""A EventflowProjectsFlowsUpdateRequest object.

  Fields:
    flow: A Flow resource to be passed as the request body.
    name: Name must be unique within a namespace. Is required when creating
      resources, although some resources may allow a client to request the
      generation of an appropriate name automatically. Name is primarily
      intended for creation idempotence and configuration definition. Cannot
      be updated. More info: http://kubernetes.io/docs/user-
      guide/identifiers#names +optional
    namespace: Namespace defines the space within each name must be unique. An
      empty namespace is equivalent to the "default" namespace, but "default"
      is the canonical representation. Not all objects are required to be
      scoped to a namespace - the value of this field for those objects will
      be empty. Must be a DNS_LABEL. Cannot be updated. More info:
      http://kubernetes.io/docs/user-guide/namespaces +optional
  """

  flow = _messages.MessageField('Flow', 1)
  name = _messages.StringField(2, required=True)
  namespace = _messages.StringField(3, required=True)


class Flow(_messages.Message):
  r"""A binding of a Source to an Action specifying the trigger condition and
  the event type.

  Fields:
    apiVersion: The version of the schema the object should have. e.g.
      "eventing.dev/v1beta2".
    kind: Always "Flow".
    metadata: Kubernates-style standard metadata. The Google Hosted version of
      this API uses the flow metadata fields as follows: name: is of the form
      "flowId" (does not include project ID or slashes) generate_name: is
      optionally used on creation if name is not set, in which case it is a
      prefix of a system-generated name. namespace: the project ID for the
      project storing owning this flow (not necessarily the project of the
      event source) creation_timestamp: The time at which the flow was first
      created in the underlying database. Output only. labels["event_type"]:
      Set by the system on creation time to be the same as
      spec.trigger.event_type Output only. labels["source"]: Set by the system
      on creation time to be the same as spec.trigger.resource Output only.
      labels["action"]: Set by the system on creation time to be the same as
      spec.action.name Output only. labels["processor"]: Set by the system on
      creation time to be the same as spec.action.processor
    spec: Desired state of the flow.
    status: Output only. A set of status conditions and when they were last
      observed.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('FlowSpec', 4)
  status = _messages.MessageField('FlowStatus', 5)


class FlowSpec(_messages.Message):
  r"""Desired state of a Flow.

  Fields:
    action: Where an action gets delivered to. For example an HTTP endpoint.
    trigger: Contains the event_type, the "resource" path, and the hostname of
      the service hosting the event source. The "resource" includes the event
      source and a path match expression specifying a condition for emitting
      an event.
  """

  action = _messages.MessageField('Action', 1)
  trigger = _messages.MessageField('EventTrigger', 2)


class FlowStatus(_messages.Message):
  r"""Current status of a flow.

  Fields:
    conditions: The latest available observations of an object's current
      state.
    observedGeneration: The generation most recently observed by the component
      responsible for acting upon changes to the desired state of the
      resource.
  """

  conditions = _messages.MessageField('Condition', 1, repeated=True)
  observedGeneration = _messages.IntegerField(2)


class ListFlowsResponse(_messages.Message):
  r"""Response message for EventingFlow.ListFlows.

  Fields:
    flows: The list of items.
    nextPageToken: A token to retrieve next page of results.
  """

  flows = _messages.MessageField('Flow', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ObjectMeta(_messages.Message):
  r"""ObjectMeta is metadata that all persisted resources must have, which
  includes all objects users must create.

  Messages:
    AnnotationsValue: Annotations is an unstructured key value map stored with
      a resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: http://kubernetes.io/docs/user-
      guide/annotations +optional
    LabelsValue: Map of string keys and values that can be used to organize
      and categorize (scope and select) objects. May match selectors of
      replication controllers and routes. More info:
      http://kubernetes.io/docs/user-guide/labels +optional

  Fields:
    annotations: Annotations is an unstructured key value map stored with a
      resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: http://kubernetes.io/docs/user-
      guide/annotations +optional
    creationTimestamp: CreationTimestamp is a timestamp representing the
      server time when this object was created. It is not guaranteed to be set
      in happens-before order across separate operations. Clients may not set
      this value. It is represented in RFC3339 form and is in UTC. Populated
      by the system. Read-only. Null for lists. More info:
      https://git.k8s.io/community/contributors/devel/api-
      conventions.md#metadata +optional
    generateName: GenerateName is an optional prefix, used by the server, to
      generate a unique name ONLY IF the Name field has not been provided. If
      this field is used, the name returned to the client will be different
      than the name passed. This value will also be combined with a unique
      suffix. The provided value has the same validation rules as the Name
      field, and may be truncated by the length of the suffix required to make
      the value unique on the server. If this field is specified and the
      generated name exists, the server will NOT return a 409 - instead, it
      will either return 201 Created or 500 with Reason ServerTimeout
      indicating a unique name could not be found in the time allotted, and
      the client should retry (optionally after the time indicated in the
      Retry-After header). Applied only if Name is not specified. More info:
      https://git.k8s.io/community/contributors/devel/api-
      conventions.md#idempotency +optional string generateName = 2;
    generation: A sequence number representing a specific generation of the
      desired state. Populated by the system. Read-only. +optional
    labels: Map of string keys and values that can be used to organize and
      categorize (scope and select) objects. May match selectors of
      replication controllers and routes. More info:
      http://kubernetes.io/docs/user-guide/labels +optional
    name: Name must be unique within a namespace. Is required when creating
      resources, although some resources may allow a client to request the
      generation of an appropriate name automatically. Name is primarily
      intended for creation idempotence and configuration definition. Cannot
      be updated. More info: http://kubernetes.io/docs/user-
      guide/identifiers#names +optional
    namespace: Namespace defines the space within each name must be unique. An
      empty namespace is equivalent to the "default" namespace, but "default"
      is the canonical representation. Not all objects are required to be
      scoped to a namespace - the value of this field for those objects will
      be empty. Must be a DNS_LABEL. Cannot be updated. More info:
      http://kubernetes.io/docs/user-guide/namespaces +optional
    resourceVersion: An opaque value that represents the internal version of
      this object that can be used by clients to determine when objects have
      changed. May be used for optimistic concurrency, change detection, and
      the watch operation on a resource or set of resources. Clients must
      treat these values as opaque and passed unmodified back to the server.
      They may only be valid for a particular resource or set of resources.
      Populated by the system. Read-only. Value must be treated as opaque by
      clients and . More info: https://github.com/kubernetes/community/blob/ma
      ster/contributors/devel/sig-architecture/api-conventions.md#concurrency-
      control-and-consistency +optional
    selfLink: SelfLink is a URL representing this object. Populated by the
      system. Read-only. +optional string selfLink = 4;
    uid: UID is the unique in time and space value for this object. It is
      typically generated by the server on successful creation of a resource
      and is not allowed to change on PUT operations. Populated by the system.
      Read-only. More info: http://kubernetes.io/docs/user-
      guide/identifiers#uids +optional
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations is an unstructured key value map stored with a resource
    that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: http://kubernetes.io/docs/user-guide/annotations
    +optional

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Map of string keys and values that can be used to organize and
    categorize (scope and select) objects. May match selectors of replication
    controllers and routes. More info: http://kubernetes.io/docs/user-
    guide/labels +optional

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  creationTimestamp = _messages.StringField(2)
  generateName = _messages.StringField(3)
  generation = _messages.FloatField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  namespace = _messages.StringField(7)
  resourceVersion = _messages.StringField(8)
  selfLink = _messages.StringField(9)
  uid = _messages.StringField(10)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
