"""Generated message classes for speech version v2.

Converts audio to text by applying powerful neural network models.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'speech'


class AccessMetadata(_messages.Message):
  r"""The access metadata for a particular region. This can be applied if the
  org policy for the given project disallows a particular region.

  Enums:
    ConstraintTypeValueValuesEnum: Describes the different types of
      constraints that are applied.

  Fields:
    constraintType: Describes the different types of constraints that are
      applied.
  """

  class ConstraintTypeValueValuesEnum(_messages.Enum):
    r"""Describes the different types of constraints that are applied.

    Values:
      CONSTRAINT_TYPE_UNSPECIFIED: Unspecified constraint applied.
      RESOURCE_LOCATIONS_ORG_POLICY_CREATE_CONSTRAINT: The project's org
        policy disallows the given region.
    """
    CONSTRAINT_TYPE_UNSPECIFIED = 0
    RESOURCE_LOCATIONS_ORG_POLICY_CREATE_CONSTRAINT = 1

  constraintType = _messages.EnumField('ConstraintTypeValueValuesEnum', 1)


class AdaptationPhraseSet(_messages.Message):
  r"""A biasing PhraseSet, which can be either a string referencing the name
  of an existing PhraseSets resource, or an inline definition of a PhraseSet.

  Fields:
    inlinePhraseSet: An inline defined PhraseSet.
    phraseSet: The name of an existing PhraseSet resource. The user must have
      read access to the resource and it must not be deleted.
  """

  inlinePhraseSet = _messages.MessageField('PhraseSet', 1)
  phraseSet = _messages.StringField(2)


class AutoDetectDecodingConfig(_messages.Message):
  r"""Automatically detected decoding parameters. Supported for the following
  encodings: * WAV_LINEAR16: 16-bit signed little-endian PCM samples in a WAV
  container. * WAV_MULAW: 8-bit companded mulaw samples in a WAV container. *
  WAV_ALAW: 8-bit companded alaw samples in a WAV container. * RFC4867_5_AMR:
  AMR frames with an rfc4867.5 header. * RFC4867_5_AMRWB: AMR-WB frames with
  an rfc4867.5 header. * FLAC: FLAC frames in the "native FLAC" container
  format. * MP3: MPEG audio frames with optional (ignored) ID3 metadata. *
  OGG_OPUS: Opus audio frames in an Ogg container. * WEBM_OPUS: Opus audio
  frames in a WebM container. * MP4_AAC: AAC audio frames in an MP4 container.
  * M4A_AAC: AAC audio frames in an M4A container. * MOV_AAC: AAC audio frames
  in an MOV container.
  """



class BatchRecognizeFileMetadata(_messages.Message):
  r"""Metadata about a single file in a batch for BatchRecognize.

  Fields:
    config: Features and audio metadata to use for the Automatic Speech
      Recognition. This field in combination with the config_mask field can be
      used to override parts of the default_recognition_config of the
      Recognizer resource as well as the config at the request level.
    configMask: The list of fields in config that override the values in the
      default_recognition_config of the recognizer during this recognition
      request. If no mask is provided, all non-default valued fields in config
      override the values in the recognizer for this recognition request. If a
      mask is provided, only the fields listed in the mask override the config
      in the recognizer for this recognition request. If a wildcard (`*`) is
      provided, config completely overrides and replaces the config in the
      recognizer for this recognition request.
    uri: Cloud Storage URI for the audio file.
  """

  config = _messages.MessageField('RecognitionConfig', 1)
  configMask = _messages.StringField(2)
  uri = _messages.StringField(3)


class BatchRecognizeFileResult(_messages.Message):
  r"""Final results for a single file.

  Fields:
    cloudStorageResult: Recognition results written to Cloud Storage. This is
      populated only when GcsOutputConfig is set in the
      RecognitionOutputConfig.
    error: Error if one was encountered.
    inlineResult: Recognition results. This is populated only when
      InlineOutputConfig is set in the RecognitionOutputConfig.
    metadata: A RecognitionResponseMetadata attribute.
    transcript: Deprecated. Use `inline_result.transcript` instead.
    uri: Deprecated. Use `cloud_storage_result.native_format_uri` instead.
  """

  cloudStorageResult = _messages.MessageField('CloudStorageResult', 1)
  error = _messages.MessageField('Status', 2)
  inlineResult = _messages.MessageField('InlineResult', 3)
  metadata = _messages.MessageField('RecognitionResponseMetadata', 4)
  transcript = _messages.MessageField('BatchRecognizeResults', 5)
  uri = _messages.StringField(6)


class BatchRecognizeMetadata(_messages.Message):
  r"""Operation metadata for BatchRecognize.

  Messages:
    TranscriptionMetadataValue: Map from provided filename to the
      transcription metadata for that file.

  Fields:
    transcriptionMetadata: Map from provided filename to the transcription
      metadata for that file.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TranscriptionMetadataValue(_messages.Message):
    r"""Map from provided filename to the transcription metadata for that
    file.

    Messages:
      AdditionalProperty: An additional property for a
        TranscriptionMetadataValue object.

    Fields:
      additionalProperties: Additional properties of type
        TranscriptionMetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TranscriptionMetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A BatchRecognizeTranscriptionMetadata attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('BatchRecognizeTranscriptionMetadata', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  transcriptionMetadata = _messages.MessageField('TranscriptionMetadataValue', 1)


class BatchRecognizeRequest(_messages.Message):
  r"""Request message for the BatchRecognize method.

  Enums:
    ProcessingStrategyValueValuesEnum: Processing strategy to use for this
      request.

  Fields:
    config: Features and audio metadata to use for the Automatic Speech
      Recognition. This field in combination with the config_mask field can be
      used to override parts of the default_recognition_config of the
      Recognizer resource.
    configMask: The list of fields in config that override the values in the
      default_recognition_config of the recognizer during this recognition
      request. If no mask is provided, all given fields in config override the
      values in the recognizer for this recognition request. If a mask is
      provided, only the fields listed in the mask override the config in the
      recognizer for this recognition request. If a wildcard (`*`) is
      provided, config completely overrides and replaces the config in the
      recognizer for this recognition request.
    files: Audio files with file metadata for ASR. The maximum number of files
      allowed to be specified is 15.
    processingStrategy: Processing strategy to use for this request.
    recognitionOutputConfig: Configuration options for where to output the
      transcripts of each file.
    recognizer: Required. The name of the Recognizer to use during
      recognition. The expected format is
      `projects/{project}/locations/{location}/recognizers/{recognizer}`. The
      {recognizer} segment may be set to `_` to use an empty implicit
      Recognizer.
  """

  class ProcessingStrategyValueValuesEnum(_messages.Enum):
    r"""Processing strategy to use for this request.

    Values:
      PROCESSING_STRATEGY_UNSPECIFIED: Default value for the processing
        strategy. The request is processed as soon as its received.
      DYNAMIC_BATCHING: If selected, processes the request during lower
        utilization periods for a price discount. The request is fulfilled
        within 24 hours.
    """
    PROCESSING_STRATEGY_UNSPECIFIED = 0
    DYNAMIC_BATCHING = 1

  config = _messages.MessageField('RecognitionConfig', 1)
  configMask = _messages.StringField(2)
  files = _messages.MessageField('BatchRecognizeFileMetadata', 3, repeated=True)
  processingStrategy = _messages.EnumField('ProcessingStrategyValueValuesEnum', 4)
  recognitionOutputConfig = _messages.MessageField('RecognitionOutputConfig', 5)
  recognizer = _messages.StringField(6)


class BatchRecognizeResponse(_messages.Message):
  r"""Response message for BatchRecognize that is packaged into a longrunning
  Operation.

  Messages:
    ResultsValue: Map from filename to the final result for that file.

  Fields:
    results: Map from filename to the final result for that file.
    totalBilledDuration: When available, billed audio seconds for the
      corresponding request.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResultsValue(_messages.Message):
    r"""Map from filename to the final result for that file.

    Messages:
      AdditionalProperty: An additional property for a ResultsValue object.

    Fields:
      additionalProperties: Additional properties of type ResultsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResultsValue object.

      Fields:
        key: Name of the additional property.
        value: A BatchRecognizeFileResult attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('BatchRecognizeFileResult', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  results = _messages.MessageField('ResultsValue', 1)
  totalBilledDuration = _messages.StringField(2)


class BatchRecognizeResults(_messages.Message):
  r"""Output type for Cloud Storage of BatchRecognize transcripts. Though this
  proto isn't returned in this API anywhere, the Cloud Storage transcripts
  will be this proto serialized and should be parsed as such.

  Fields:
    metadata: Metadata about the recognition.
    results: Sequential list of transcription results corresponding to
      sequential portions of audio.
  """

  metadata = _messages.MessageField('RecognitionResponseMetadata', 1)
  results = _messages.MessageField('SpeechRecognitionResult', 2, repeated=True)


class BatchRecognizeTranscriptionMetadata(_messages.Message):
  r"""Metadata about transcription for a single file (for example, progress
  percent).

  Fields:
    error: Error if one was encountered.
    progressPercent: How much of the file has been transcribed so far.
    uri: The Cloud Storage URI to which recognition results will be written.
  """

  error = _messages.MessageField('Status', 1)
  progressPercent = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  uri = _messages.StringField(3)


class ClassItem(_messages.Message):
  r"""An item of the class.

  Fields:
    value: The class item's value.
  """

  value = _messages.StringField(1)


class CloudStorageResult(_messages.Message):
  r"""Final results written to Cloud Storage.

  Fields:
    srtFormatUri: The Cloud Storage URI to which recognition results were
      written as SRT formatted captions. This is populated only when `SRT`
      output is requested.
    uri: The Cloud Storage URI to which recognition results were written.
    vttFormatUri: The Cloud Storage URI to which recognition results were
      written as VTT formatted captions. This is populated only when `VTT`
      output is requested.
  """

  srtFormatUri = _messages.StringField(1)
  uri = _messages.StringField(2)
  vttFormatUri = _messages.StringField(3)


class Config(_messages.Message):
  r"""Message representing the config for the Speech-to-Text API. This
  includes an optional [KMS key](https://cloud.google.com/kms/docs/resource-
  hierarchy#keys) with which incoming data will be encrypted.

  Fields:
    kmsKeyName: Optional. An optional [KMS key
      name](https://cloud.google.com/kms/docs/resource-hierarchy#keys) that if
      present, will be used to encrypt Speech-to-Text resources at-rest.
      Updating this key will not encrypt existing resources using this key;
      only new resources will be encrypted using this key. The expected format
      is `projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKe
      ys/{crypto_key}`.
    name: Output only. Identifier. The name of the config resource. There is
      exactly one config resource per project per location. The expected
      format is `projects/{project}/locations/{location}/config`.
    updateTime: Output only. The most recent time this resource was modified.
  """

  kmsKeyName = _messages.StringField(1)
  name = _messages.StringField(2)
  updateTime = _messages.StringField(3)


class CreateCustomClassRequest(_messages.Message):
  r"""Request message for the CreateCustomClass method.

  Fields:
    customClass: Required. The CustomClass to create.
    customClassId: The ID to use for the CustomClass, which will become the
      final component of the CustomClass's resource name. This value should be
      4-63 characters, and valid characters are /a-z-/.
    parent: Required. The project and location where this CustomClass will be
      created. The expected format is
      `projects/{project}/locations/{location}`.
    validateOnly: If set, validate the request and preview the CustomClass,
      but do not actually create it.
  """

  customClass = _messages.MessageField('CustomClass', 1)
  customClassId = _messages.StringField(2)
  parent = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class CreatePhraseSetRequest(_messages.Message):
  r"""Request message for the CreatePhraseSet method.

  Fields:
    parent: Required. The project and location where this PhraseSet will be
      created. The expected format is
      `projects/{project}/locations/{location}`.
    phraseSet: Required. The PhraseSet to create.
    phraseSetId: The ID to use for the PhraseSet, which will become the final
      component of the PhraseSet's resource name. This value should be 4-63
      characters, and valid characters are /a-z-/.
    validateOnly: If set, validate the request and preview the PhraseSet, but
      do not actually create it.
  """

  parent = _messages.StringField(1)
  phraseSet = _messages.MessageField('PhraseSet', 2)
  phraseSetId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class CreateRecognizerRequest(_messages.Message):
  r"""Request message for the CreateRecognizer method.

  Fields:
    parent: Required. The project and location where this Recognizer will be
      created. The expected format is
      `projects/{project}/locations/{location}`.
    recognizer: Required. The Recognizer to create.
    recognizerId: The ID to use for the Recognizer, which will become the
      final component of the Recognizer's resource name. This value should be
      4-63 characters, and valid characters are /a-z-/.
    validateOnly: If set, validate the request and preview the Recognizer, but
      do not actually create it.
  """

  parent = _messages.StringField(1)
  recognizer = _messages.MessageField('Recognizer', 2)
  recognizerId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class CustomClass(_messages.Message):
  r"""CustomClass for biasing in speech recognition. Used to define a set of
  words or phrases that represents a common concept or theme likely to appear
  in your audio, for example a list of passenger ship names.

  Enums:
    StateValueValuesEnum: Output only. The CustomClass lifecycle state.

  Messages:
    AnnotationsValue: Optional. Allows users to store small amounts of
      arbitrary data. Both the key and the value must be 63 characters or less
      each. At most 100 annotations.

  Fields:
    annotations: Optional. Allows users to store small amounts of arbitrary
      data. Both the key and the value must be 63 characters or less each. At
      most 100 annotations.
    createTime: Output only. Creation time.
    deleteTime: Output only. The time at which this resource was requested for
      deletion.
    displayName: Optional. User-settable, human-readable name for the
      CustomClass. Must be 63 characters or less.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields. This may be sent on update, undelete, and delete
      requests to ensure the client has an up-to-date value before proceeding.
    expireTime: Output only. The time at which this resource will be purged.
    items: A collection of class items.
    kmsKeyName: Output only. The [KMS key
      name](https://cloud.google.com/kms/docs/resource-hierarchy#keys) with
      which the CustomClass is encrypted. The expected format is `projects/{pr
      oject}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}`
      .
    kmsKeyVersionName: Output only. The [KMS key version
      name](https://cloud.google.com/kms/docs/resource-hierarchy#key_versions)
      with which the CustomClass is encrypted. The expected format is `project
      s/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_
      key}/cryptoKeyVersions/{crypto_key_version}`.
    name: Output only. Identifier. The resource name of the CustomClass.
      Format:
      `projects/{project}/locations/{location}/customClasses/{custom_class}`.
    reconciling: Output only. Whether or not this CustomClass is in the
      process of being updated.
    state: Output only. The CustomClass lifecycle state.
    uid: Output only. System-assigned unique identifier for the CustomClass.
    updateTime: Output only. The most recent time this resource was modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The CustomClass lifecycle state.

    Values:
      STATE_UNSPECIFIED: Unspecified state. This is only used/useful for
        distinguishing unset values.
      ACTIVE: The normal and active state.
      DELETED: This CustomClass has been deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Allows users to store small amounts of arbitrary data. Both
    the key and the value must be 63 characters or less each. At most 100
    annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  deleteTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  expireTime = _messages.StringField(6)
  items = _messages.MessageField('ClassItem', 7, repeated=True)
  kmsKeyName = _messages.StringField(8)
  kmsKeyVersionName = _messages.StringField(9)
  name = _messages.StringField(10)
  reconciling = _messages.BooleanField(11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  uid = _messages.StringField(13)
  updateTime = _messages.StringField(14)


class DeleteCustomClassRequest(_messages.Message):
  r"""Request message for the DeleteCustomClass method.

  Fields:
    allowMissing: If set to true, and the CustomClass is not found, the
      request will succeed and be a no-op (no Operation is recorded in this
      case).
    etag: This checksum is computed by the server based on the value of other
      fields. This may be sent on update, undelete, and delete requests to
      ensure the client has an up-to-date value before proceeding.
    name: Required. The name of the CustomClass to delete. Format:
      `projects/{project}/locations/{location}/customClasses/{custom_class}`
    validateOnly: If set, validate the request and preview the deleted
      CustomClass, but do not actually delete it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DeletePhraseSetRequest(_messages.Message):
  r"""Request message for the DeletePhraseSet method.

  Fields:
    allowMissing: If set to true, and the PhraseSet is not found, the request
      will succeed and be a no-op (no Operation is recorded in this case).
    etag: This checksum is computed by the server based on the value of other
      fields. This may be sent on update, undelete, and delete requests to
      ensure the client has an up-to-date value before proceeding.
    name: Required. The name of the PhraseSet to delete. Format:
      `projects/{project}/locations/{location}/phraseSets/{phrase_set}`
    validateOnly: If set, validate the request and preview the deleted
      PhraseSet, but do not actually delete it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DeleteRecognizerRequest(_messages.Message):
  r"""Request message for the DeleteRecognizer method.

  Fields:
    allowMissing: If set to true, and the Recognizer is not found, the request
      will succeed and be a no-op (no Operation is recorded in this case).
    etag: This checksum is computed by the server based on the value of other
      fields. This may be sent on update, undelete, and delete requests to
      ensure the client has an up-to-date value before proceeding.
    name: Required. The name of the Recognizer to delete. Format:
      `projects/{project}/locations/{location}/recognizers/{recognizer}`
    validateOnly: If set, validate the request and preview the deleted
      Recognizer, but do not actually delete it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class Entry(_messages.Message):
  r"""A single replacement configuration.

  Fields:
    caseSensitive: Whether the search is case sensitive.
    replace: What to replace with. Max length is 100 characters.
    search: What to replace. Max length is 100 characters.
  """

  caseSensitive = _messages.BooleanField(1)
  replace = _messages.StringField(2)
  search = _messages.StringField(3)


class ExplicitDecodingConfig(_messages.Message):
  r"""Explicitly specified decoding parameters.

  Enums:
    EncodingValueValuesEnum: Required. Encoding of the audio data sent for
      recognition.

  Fields:
    audioChannelCount: Optional. Number of channels present in the audio data
      sent for recognition. Note that this field is marked as OPTIONAL for
      backward compatibility reasons. It is (and has always been) effectively
      REQUIRED. The maximum allowed value is 8.
    encoding: Required. Encoding of the audio data sent for recognition.
    sampleRateHertz: Optional. Sample rate in Hertz of the audio data sent for
      recognition. Valid values are: 8000-48000, and 16000 is optimal. For
      best results, set the sampling rate of the audio source to 16000 Hz. If
      that's not possible, use the native sample rate of the audio source
      (instead of resampling). Note that this field is marked as OPTIONAL for
      backward compatibility reasons. It is (and has always been) effectively
      REQUIRED.
  """

  class EncodingValueValuesEnum(_messages.Enum):
    r"""Required. Encoding of the audio data sent for recognition.

    Values:
      AUDIO_ENCODING_UNSPECIFIED: Default value. This value is unused.
      LINEAR16: Headerless 16-bit signed little-endian PCM samples.
      MULAW: Headerless 8-bit companded mulaw samples.
      ALAW: Headerless 8-bit companded alaw samples.
      AMR: AMR frames with an rfc4867.5 header.
      AMR_WB: AMR-WB frames with an rfc4867.5 header.
      FLAC: FLAC frames in the "native FLAC" container format.
      MP3: MPEG audio frames with optional (ignored) ID3 metadata.
      OGG_OPUS: Opus audio frames in an Ogg container.
      WEBM_OPUS: Opus audio frames in a WebM container.
      MP4_AAC: AAC audio frames in an MP4 container.
      M4A_AAC: AAC audio frames in an M4A container.
      MOV_AAC: AAC audio frames in an MOV container.
    """
    AUDIO_ENCODING_UNSPECIFIED = 0
    LINEAR16 = 1
    MULAW = 2
    ALAW = 3
    AMR = 4
    AMR_WB = 5
    FLAC = 6
    MP3 = 7
    OGG_OPUS = 8
    WEBM_OPUS = 9
    MP4_AAC = 10
    M4A_AAC = 11
    MOV_AAC = 12

  audioChannelCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  encoding = _messages.EnumField('EncodingValueValuesEnum', 2)
  sampleRateHertz = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GcsOutputConfig(_messages.Message):
  r"""Output configurations for Cloud Storage.

  Fields:
    uri: The Cloud Storage URI prefix with which recognition results will be
      written.
  """

  uri = _messages.StringField(1)


class InlineOutputConfig(_messages.Message):
  r"""Output configurations for inline response."""


class InlineResult(_messages.Message):
  r"""Final results returned inline in the recognition response.

  Fields:
    srtCaptions: The transcript for the audio file as SRT formatted captions.
      This is populated only when `SRT` output is requested.
    transcript: The transcript for the audio file.
    vttCaptions: The transcript for the audio file as VTT formatted captions.
      This is populated only when `VTT` output is requested.
  """

  srtCaptions = _messages.StringField(1)
  transcript = _messages.MessageField('BatchRecognizeResults', 2)
  vttCaptions = _messages.StringField(3)


class LanguageMetadata(_messages.Message):
  r"""The metadata about locales available in a given region. Currently this
  is just the models that are available for each locale

  Messages:
    ModelsValue: Map of locale (language code) -> models

  Fields:
    models: Map of locale (language code) -> models
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModelsValue(_messages.Message):
    r"""Map of locale (language code) -> models

    Messages:
      AdditionalProperty: An additional property for a ModelsValue object.

    Fields:
      additionalProperties: Additional properties of type ModelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModelsValue object.

      Fields:
        key: Name of the additional property.
        value: A ModelMetadata attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ModelMetadata', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  models = _messages.MessageField('ModelsValue', 1)


class ListCustomClassesResponse(_messages.Message):
  r"""Response message for the ListCustomClasses method.

  Fields:
    customClasses: The list of requested CustomClasses.
    nextPageToken: A token, which can be sent as page_token to retrieve the
      next page. If this field is omitted, there are no subsequent pages. This
      token expires after 72 hours.
  """

  customClasses = _messages.MessageField('CustomClass', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListPhraseSetsResponse(_messages.Message):
  r"""Response message for the ListPhraseSets method.

  Fields:
    nextPageToken: A token, which can be sent as page_token to retrieve the
      next page. If this field is omitted, there are no subsequent pages. This
      token expires after 72 hours.
    phraseSets: The list of requested PhraseSets.
  """

  nextPageToken = _messages.StringField(1)
  phraseSets = _messages.MessageField('PhraseSet', 2, repeated=True)


class ListRecognizersResponse(_messages.Message):
  r"""Response message for the ListRecognizers method.

  Fields:
    nextPageToken: A token, which can be sent as page_token to retrieve the
      next page. If this field is omitted, there are no subsequent pages. This
      token expires after 72 hours.
    recognizers: The list of requested Recognizers.
  """

  nextPageToken = _messages.StringField(1)
  recognizers = _messages.MessageField('Recognizer', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LocationsMetadata(_messages.Message):
  r"""Main metadata for the Locations API for STT V2. Currently this is just
  the metadata about locales, models, and features

  Fields:
    accessMetadata: Information about access metadata for the region and given
      project.
    languages: Information about available locales, models, and features
      represented in the hierarchical structure of locales -> models ->
      features
  """

  accessMetadata = _messages.MessageField('AccessMetadata', 1)
  languages = _messages.MessageField('LanguageMetadata', 2)


class ModelFeature(_messages.Message):
  r"""Represents a singular feature of a model. If the feature is
  `recognizer`, the release_state of the feature represents the release_state
  of the model

  Fields:
    feature: The name of the feature (Note: the feature can be `recognizer`)
    releaseState: The release state of the feature
  """

  feature = _messages.StringField(1)
  releaseState = _messages.StringField(2)


class ModelFeatures(_messages.Message):
  r"""Represents the collection of features belonging to a model

  Fields:
    modelFeature: Repeated field that contains all features of the model
  """

  modelFeature = _messages.MessageField('ModelFeature', 1, repeated=True)


class ModelMetadata(_messages.Message):
  r"""The metadata about the models in a given region for a specific locale.
  Currently this is just the features of the model

  Messages:
    ModelFeaturesValue: Map of the model name -> features of that model

  Fields:
    modelFeatures: Map of the model name -> features of that model
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModelFeaturesValue(_messages.Message):
    r"""Map of the model name -> features of that model

    Messages:
      AdditionalProperty: An additional property for a ModelFeaturesValue
        object.

    Fields:
      additionalProperties: Additional properties of type ModelFeaturesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModelFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A ModelFeatures attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ModelFeatures', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  modelFeatures = _messages.MessageField('ModelFeaturesValue', 1)


class NativeOutputFileFormatConfig(_messages.Message):
  r"""Output configurations for serialized `BatchRecognizeResults` protos."""


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of a long-running operation.

  Fields:
    batchRecognizeMetadata: Metadata specific to the BatchRecognize method.
    batchRecognizeRequest: The BatchRecognizeRequest that spawned the
      Operation.
    createCustomClassRequest: The CreateCustomClassRequest that spawned the
      Operation.
    createPhraseSetRequest: The CreatePhraseSetRequest that spawned the
      Operation.
    createRecognizerRequest: The CreateRecognizerRequest that spawned the
      Operation.
    createTime: The time the operation was created.
    deleteCustomClassRequest: The DeleteCustomClassRequest that spawned the
      Operation.
    deletePhraseSetRequest: The DeletePhraseSetRequest that spawned the
      Operation.
    deleteRecognizerRequest: The DeleteRecognizerRequest that spawned the
      Operation.
    kmsKeyName: The [KMS key name](https://cloud.google.com/kms/docs/resource-
      hierarchy#keys) with which the content of the Operation is encrypted.
      The expected format is `projects/{project}/locations/{location}/keyRings
      /{key_ring}/cryptoKeys/{crypto_key}`.
    kmsKeyVersionName: The [KMS key version
      name](https://cloud.google.com/kms/docs/resource-hierarchy#key_versions)
      with which content of the Operation is encrypted. The expected format is
      `projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/
      {crypto_key}/cryptoKeyVersions/{crypto_key_version}`.
    method: The method that triggered the operation.
    progressPercent: The percent progress of the Operation. Values can range
      from 0-100. If the value is 100, then the operation is finished.
    resource: The resource path for the target of the operation.
    undeleteCustomClassRequest: The UndeleteCustomClassRequest that spawned
      the Operation.
    undeletePhraseSetRequest: The UndeletePhraseSetRequest that spawned the
      Operation.
    undeleteRecognizerRequest: The UndeleteRecognizerRequest that spawned the
      Operation.
    updateConfigRequest: The UpdateConfigRequest that spawned the Operation.
    updateCustomClassRequest: The UpdateCustomClassRequest that spawned the
      Operation.
    updatePhraseSetRequest: The UpdatePhraseSetRequest that spawned the
      Operation.
    updateRecognizerRequest: The UpdateRecognizerRequest that spawned the
      Operation.
    updateTime: The time the operation was last updated.
  """

  batchRecognizeMetadata = _messages.MessageField('BatchRecognizeMetadata', 1)
  batchRecognizeRequest = _messages.MessageField('BatchRecognizeRequest', 2)
  createCustomClassRequest = _messages.MessageField('CreateCustomClassRequest', 3)
  createPhraseSetRequest = _messages.MessageField('CreatePhraseSetRequest', 4)
  createRecognizerRequest = _messages.MessageField('CreateRecognizerRequest', 5)
  createTime = _messages.StringField(6)
  deleteCustomClassRequest = _messages.MessageField('DeleteCustomClassRequest', 7)
  deletePhraseSetRequest = _messages.MessageField('DeletePhraseSetRequest', 8)
  deleteRecognizerRequest = _messages.MessageField('DeleteRecognizerRequest', 9)
  kmsKeyName = _messages.StringField(10)
  kmsKeyVersionName = _messages.StringField(11)
  method = _messages.StringField(12)
  progressPercent = _messages.IntegerField(13, variant=_messages.Variant.INT32)
  resource = _messages.StringField(14)
  undeleteCustomClassRequest = _messages.MessageField('UndeleteCustomClassRequest', 15)
  undeletePhraseSetRequest = _messages.MessageField('UndeletePhraseSetRequest', 16)
  undeleteRecognizerRequest = _messages.MessageField('UndeleteRecognizerRequest', 17)
  updateConfigRequest = _messages.MessageField('UpdateConfigRequest', 18)
  updateCustomClassRequest = _messages.MessageField('UpdateCustomClassRequest', 19)
  updatePhraseSetRequest = _messages.MessageField('UpdatePhraseSetRequest', 20)
  updateRecognizerRequest = _messages.MessageField('UpdateRecognizerRequest', 21)
  updateTime = _messages.StringField(22)


class OutputFormatConfig(_messages.Message):
  r"""Configuration for the format of the results stored to `output`.

  Fields:
    native: Configuration for the native output format. If this field is set
      or if no other output format field is set, then transcripts will be
      written to the sink in the native format.
    srt: Configuration for the SRT output format. If this field is set, then
      transcripts will be written to the sink in the SRT format.
    vtt: Configuration for the VTT output format. If this field is set, then
      transcripts will be written to the sink in the VTT format.
  """

  native = _messages.MessageField('NativeOutputFileFormatConfig', 1)
  srt = _messages.MessageField('SrtOutputFileFormatConfig', 2)
  vtt = _messages.MessageField('VttOutputFileFormatConfig', 3)


class Phrase(_messages.Message):
  r"""A Phrase contains words and phrase "hints" so that the speech
  recognition is more likely to recognize them. This can be used to improve
  the accuracy for specific words and phrases, for example, if specific
  commands are typically spoken by the user. This can also be used to add
  additional words to the vocabulary of the recognizer. List items can also
  include CustomClass references containing groups of words that represent
  common concepts that occur in natural language.

  Fields:
    boost: Hint Boost. Overrides the boost set at the phrase set level.
      Positive value will increase the probability that a specific phrase will
      be recognized over other similar sounding phrases. The higher the boost,
      the higher the chance of false positive recognition as well. Negative
      boost values would correspond to anti-biasing. Anti-biasing is not
      enabled, so negative boost values will return an error. Boost values
      must be between 0 and 20. Any values outside that range will return an
      error. We recommend using a binary search approach to finding the
      optimal value for your use case as well as adding phrases both with and
      without boost to your requests.
    value: The phrase itself.
  """

  boost = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  value = _messages.StringField(2)


class PhraseSet(_messages.Message):
  r"""PhraseSet for biasing in speech recognition. A PhraseSet is used to
  provide "hints" to the speech recognizer to favor specific words and phrases
  in the results.

  Enums:
    StateValueValuesEnum: Output only. The PhraseSet lifecycle state.

  Messages:
    AnnotationsValue: Allows users to store small amounts of arbitrary data.
      Both the key and the value must be 63 characters or less each. At most
      100 annotations.

  Fields:
    annotations: Allows users to store small amounts of arbitrary data. Both
      the key and the value must be 63 characters or less each. At most 100
      annotations.
    boost: Hint Boost. Positive value will increase the probability that a
      specific phrase will be recognized over other similar sounding phrases.
      The higher the boost, the higher the chance of false positive
      recognition as well. Valid `boost` values are between 0 (exclusive) and
      20. We recommend using a binary search approach to finding the optimal
      value for your use case as well as adding phrases both with and without
      boost to your requests.
    createTime: Output only. Creation time.
    deleteTime: Output only. The time at which this resource was requested for
      deletion.
    displayName: User-settable, human-readable name for the PhraseSet. Must be
      63 characters or less.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields. This may be sent on update, undelete, and delete
      requests to ensure the client has an up-to-date value before proceeding.
    expireTime: Output only. The time at which this resource will be purged.
    kmsKeyName: Output only. The [KMS key
      name](https://cloud.google.com/kms/docs/resource-hierarchy#keys) with
      which the PhraseSet is encrypted. The expected format is `projects/{proj
      ect}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}`.
    kmsKeyVersionName: Output only. The [KMS key version
      name](https://cloud.google.com/kms/docs/resource-hierarchy#key_versions)
      with which the PhraseSet is encrypted. The expected format is `projects/
      {project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_ke
      y}/cryptoKeyVersions/{crypto_key_version}`.
    name: Output only. Identifier. The resource name of the PhraseSet. Format:
      `projects/{project}/locations/{location}/phraseSets/{phrase_set}`.
    phrases: A list of word and phrases.
    reconciling: Output only. Whether or not this PhraseSet is in the process
      of being updated.
    state: Output only. The PhraseSet lifecycle state.
    uid: Output only. System-assigned unique identifier for the PhraseSet.
    updateTime: Output only. The most recent time this resource was modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The PhraseSet lifecycle state.

    Values:
      STATE_UNSPECIFIED: Unspecified state. This is only used/useful for
        distinguishing unset values.
      ACTIVE: The normal and active state.
      DELETED: This PhraseSet has been deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Allows users to store small amounts of arbitrary data. Both the key
    and the value must be 63 characters or less each. At most 100 annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  boost = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  createTime = _messages.StringField(3)
  deleteTime = _messages.StringField(4)
  displayName = _messages.StringField(5)
  etag = _messages.StringField(6)
  expireTime = _messages.StringField(7)
  kmsKeyName = _messages.StringField(8)
  kmsKeyVersionName = _messages.StringField(9)
  name = _messages.StringField(10)
  phrases = _messages.MessageField('Phrase', 11, repeated=True)
  reconciling = _messages.BooleanField(12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  uid = _messages.StringField(14)
  updateTime = _messages.StringField(15)


class RecognitionConfig(_messages.Message):
  r"""Provides information to the Recognizer that specifies how to process the
  recognition request.

  Fields:
    adaptation: Speech adaptation context that weights recognizer predictions
      for specific words and phrases.
    autoDecodingConfig: Automatically detect decoding parameters. Preferred
      for supported formats.
    explicitDecodingConfig: Explicitly specified decoding parameters. Required
      if using headerless PCM audio (linear16, mulaw, alaw).
    features: Speech recognition features to enable.
    languageCodes: Optional. The language of the supplied audio as a
      [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt) language tag.
      Language tags are normalized to BCP-47 before they are used eg "en-us"
      becomes "en-US". Supported languages for each model are listed in the
      [Table of Supported Models](https://cloud.google.com/speech-to-
      text/v2/docs/speech-to-text-supported-languages). If additional
      languages are provided, recognition result will contain recognition in
      the most likely language detected. The recognition result will include
      the language tag of the language detected in the audio.
    model: Optional. Which model to use for recognition requests. Select the
      model best suited to your domain to get best results. Guidance for
      choosing which model to use can be found in the [Transcription Models
      Documentation](https://cloud.google.com/speech-to-
      text/v2/docs/transcription-model) and the models supported in each
      region can be found in the [Table Of Supported
      Models](https://cloud.google.com/speech-to-text/v2/docs/speech-to-text-
      supported-languages).
    transcriptNormalization: Optional. Use transcription normalization to
      automatically replace parts of the transcript with phrases of your
      choosing. For StreamingRecognize, this normalization only applies to
      stable partial transcripts (stability > 0.8) and final transcripts.
    translationConfig: Optional. Optional configuration used to automatically
      run translation on the given audio to the desired language for supported
      models.
  """

  adaptation = _messages.MessageField('SpeechAdaptation', 1)
  autoDecodingConfig = _messages.MessageField('AutoDetectDecodingConfig', 2)
  explicitDecodingConfig = _messages.MessageField('ExplicitDecodingConfig', 3)
  features = _messages.MessageField('RecognitionFeatures', 4)
  languageCodes = _messages.StringField(5, repeated=True)
  model = _messages.StringField(6)
  transcriptNormalization = _messages.MessageField('TranscriptNormalization', 7)
  translationConfig = _messages.MessageField('TranslationConfig', 8)


class RecognitionFeatures(_messages.Message):
  r"""Available recognition features.

  Enums:
    MultiChannelModeValueValuesEnum: Mode for recognizing multi-channel audio.

  Fields:
    diarizationConfig: Configuration to enable speaker diarization and set
      additional parameters to make diarization better suited for your
      application. When this is enabled, we send all the words from the
      beginning of the audio for the top alternative in every consecutive
      STREAMING responses. This is done in order to improve our speaker tags
      as our models learn to identify the speakers in the conversation over
      time. For non-streaming requests, the diarization results will be
      provided only in the top alternative of the FINAL
      SpeechRecognitionResult.
    enableAutomaticPunctuation: If `true`, adds punctuation to recognition
      result hypotheses. This feature is only available in select languages.
      The default `false` value does not add punctuation to result hypotheses.
    enableSpokenEmojis: The spoken emoji behavior for the call. If `true`,
      adds spoken emoji formatting for the request. This will replace spoken
      emojis with the corresponding Unicode symbols in the final transcript.
      If `false`, spoken emojis are not replaced.
    enableSpokenPunctuation: The spoken punctuation behavior for the call. If
      `true`, replaces spoken punctuation with the corresponding symbols in
      the request. For example, "how are you question mark" becomes "how are
      you?". See https://cloud.google.com/speech-to-text/docs/spoken-
      punctuation for support. If `false`, spoken punctuation is not replaced.
    enableWordConfidence: If `true`, the top result includes a list of words
      and the confidence for those words. If `false`, no word-level confidence
      information is returned. The default is `false`.
    enableWordTimeOffsets: If `true`, the top result includes a list of words
      and the start and end time offsets (timestamps) for those words. If
      `false`, no word-level time offset information is returned. The default
      is `false`.
    maxAlternatives: Maximum number of recognition hypotheses to be returned.
      The server may return fewer than `max_alternatives`. Valid values are
      `0`-`30`. A value of `0` or `1` will return a maximum of one. If
      omitted, will return a maximum of one.
    multiChannelMode: Mode for recognizing multi-channel audio.
    profanityFilter: If set to `true`, the server will attempt to filter out
      profanities, replacing all but the initial character in each filtered
      word with asterisks, for instance, "f***". If set to `false` or omitted,
      profanities won't be filtered out.
  """

  class MultiChannelModeValueValuesEnum(_messages.Enum):
    r"""Mode for recognizing multi-channel audio.

    Values:
      MULTI_CHANNEL_MODE_UNSPECIFIED: Default value for the multi-channel
        mode. If the audio contains multiple channels, only the first channel
        will be transcribed; other channels will be ignored.
      SEPARATE_RECOGNITION_PER_CHANNEL: If selected, each channel in the
        provided audio is transcribed independently. This cannot be selected
        if the selected model is `latest_short`.
    """
    MULTI_CHANNEL_MODE_UNSPECIFIED = 0
    SEPARATE_RECOGNITION_PER_CHANNEL = 1

  diarizationConfig = _messages.MessageField('SpeakerDiarizationConfig', 1)
  enableAutomaticPunctuation = _messages.BooleanField(2)
  enableSpokenEmojis = _messages.BooleanField(3)
  enableSpokenPunctuation = _messages.BooleanField(4)
  enableWordConfidence = _messages.BooleanField(5)
  enableWordTimeOffsets = _messages.BooleanField(6)
  maxAlternatives = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  multiChannelMode = _messages.EnumField('MultiChannelModeValueValuesEnum', 8)
  profanityFilter = _messages.BooleanField(9)


class RecognitionOutputConfig(_messages.Message):
  r"""Configuration options for the output(s) of recognition.

  Fields:
    gcsOutputConfig: If this message is populated, recognition results are
      written to the provided Google Cloud Storage URI.
    inlineResponseConfig: If this message is populated, recognition results
      are provided in the BatchRecognizeResponse message of the Operation when
      completed. This is only supported when calling BatchRecognize with just
      one audio file.
    outputFormatConfig: Optional. Configuration for the format of the results
      stored to `output`. If unspecified transcripts will be written in the
      `NATIVE` format only.
  """

  gcsOutputConfig = _messages.MessageField('GcsOutputConfig', 1)
  inlineResponseConfig = _messages.MessageField('InlineOutputConfig', 2)
  outputFormatConfig = _messages.MessageField('OutputFormatConfig', 3)


class RecognitionResponseMetadata(_messages.Message):
  r"""Metadata about the recognition request and response.

  Fields:
    requestId: Global request identifier auto-generated by the API.
    totalBilledDuration: When available, billed audio seconds for the
      corresponding request.
  """

  requestId = _messages.StringField(1)
  totalBilledDuration = _messages.StringField(2)


class RecognizeRequest(_messages.Message):
  r"""Request message for the Recognize method. Either `content` or `uri` must
  be supplied. Supplying both or neither returns INVALID_ARGUMENT. See
  [content limits](https://cloud.google.com/speech-to-text/quotas#content).

  Fields:
    config: Features and audio metadata to use for the Automatic Speech
      Recognition. This field in combination with the config_mask field can be
      used to override parts of the default_recognition_config of the
      Recognizer resource.
    configMask: The list of fields in config that override the values in the
      default_recognition_config of the recognizer during this recognition
      request. If no mask is provided, all non-default valued fields in config
      override the values in the recognizer for this recognition request. If a
      mask is provided, only the fields listed in the mask override the config
      in the recognizer for this recognition request. If a wildcard (`*`) is
      provided, config completely overrides and replaces the config in the
      recognizer for this recognition request.
    content: The audio data bytes encoded as specified in RecognitionConfig.
      As with all bytes fields, proto buffers use a pure binary
      representation, whereas JSON representations use base64.
    uri: URI that points to a file that contains audio data bytes as specified
      in RecognitionConfig. The file must not be compressed (for example,
      gzip). Currently, only Google Cloud Storage URIs are supported, which
      must be specified in the following format:
      `gs://bucket_name/object_name` (other URI formats return
      INVALID_ARGUMENT). For more information, see [Request
      URIs](https://cloud.google.com/storage/docs/reference-uris).
  """

  config = _messages.MessageField('RecognitionConfig', 1)
  configMask = _messages.StringField(2)
  content = _messages.BytesField(3)
  uri = _messages.StringField(4)


class RecognizeResponse(_messages.Message):
  r"""Response message for the Recognize method.

  Fields:
    metadata: Metadata about the recognition.
    results: Sequential list of transcription results corresponding to
      sequential portions of audio.
  """

  metadata = _messages.MessageField('RecognitionResponseMetadata', 1)
  results = _messages.MessageField('SpeechRecognitionResult', 2, repeated=True)


class Recognizer(_messages.Message):
  r"""A Recognizer message. Stores recognition configuration and metadata.

  Enums:
    StateValueValuesEnum: Output only. The Recognizer lifecycle state.

  Messages:
    AnnotationsValue: Allows users to store small amounts of arbitrary data.
      Both the key and the value must be 63 characters or less each. At most
      100 annotations.

  Fields:
    annotations: Allows users to store small amounts of arbitrary data. Both
      the key and the value must be 63 characters or less each. At most 100
      annotations.
    createTime: Output only. Creation time.
    defaultRecognitionConfig: Default configuration to use for requests with
      this Recognizer. This can be overwritten by inline configuration in the
      RecognizeRequest.config field.
    deleteTime: Output only. The time at which this Recognizer was requested
      for deletion.
    displayName: User-settable, human-readable name for the Recognizer. Must
      be 63 characters or less.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields. This may be sent on update, undelete, and delete
      requests to ensure the client has an up-to-date value before proceeding.
    expireTime: Output only. The time at which this Recognizer will be purged.
    kmsKeyName: Output only. The [KMS key
      name](https://cloud.google.com/kms/docs/resource-hierarchy#keys) with
      which the Recognizer is encrypted. The expected format is `projects/{pro
      ject}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}`.
    kmsKeyVersionName: Output only. The [KMS key version
      name](https://cloud.google.com/kms/docs/resource-hierarchy#key_versions)
      with which the Recognizer is encrypted. The expected format is `projects
      /{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_k
      ey}/cryptoKeyVersions/{crypto_key_version}`.
    languageCodes: Optional. This field is now deprecated. Prefer the
      `language_codes` field in the `RecognitionConfig` message. The language
      of the supplied audio as a [BCP-47](https://www.rfc-
      editor.org/rfc/bcp/bcp47.txt) language tag. Supported languages for each
      model are listed in the [Table of Supported
      Models](https://cloud.google.com/speech-to-text/v2/docs/speech-to-text-
      supported-languages). If additional languages are provided, recognition
      result will contain recognition in the most likely language detected.
      The recognition result will include the language tag of the language
      detected in the audio. When you create or update a Recognizer, these
      values are stored in normalized BCP-47 form. For example, "en-us" is
      stored as "en-US".
    model: Optional. This field is now deprecated. Prefer the `model` field in
      the `RecognitionConfig` message. Which model to use for recognition
      requests. Select the model best suited to your domain to get best
      results. Guidance for choosing which model to use can be found in the
      [Transcription Models Documentation](https://cloud.google.com/speech-to-
      text/v2/docs/transcription-model) and the models supported in each
      region can be found in the [Table Of Supported
      Models](https://cloud.google.com/speech-to-text/v2/docs/speech-to-text-
      supported-languages).
    name: Output only. Identifier. The resource name of the Recognizer.
      Format:
      `projects/{project}/locations/{location}/recognizers/{recognizer}`.
    reconciling: Output only. Whether or not this Recognizer is in the process
      of being updated.
    state: Output only. The Recognizer lifecycle state.
    uid: Output only. System-assigned unique identifier for the Recognizer.
    updateTime: Output only. The most recent time this Recognizer was
      modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The Recognizer lifecycle state.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      ACTIVE: The Recognizer is active and ready for use.
      DELETED: This Recognizer has been deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Allows users to store small amounts of arbitrary data. Both the key
    and the value must be 63 characters or less each. At most 100 annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  defaultRecognitionConfig = _messages.MessageField('RecognitionConfig', 3)
  deleteTime = _messages.StringField(4)
  displayName = _messages.StringField(5)
  etag = _messages.StringField(6)
  expireTime = _messages.StringField(7)
  kmsKeyName = _messages.StringField(8)
  kmsKeyVersionName = _messages.StringField(9)
  languageCodes = _messages.StringField(10, repeated=True)
  model = _messages.StringField(11)
  name = _messages.StringField(12)
  reconciling = _messages.BooleanField(13)
  state = _messages.EnumField('StateValueValuesEnum', 14)
  uid = _messages.StringField(15)
  updateTime = _messages.StringField(16)


class SpeakerDiarizationConfig(_messages.Message):
  r"""Configuration to enable speaker diarization.

  Fields:
    maxSpeakerCount: Required. Maximum number of speakers in the conversation.
      Valid values are: 1-6. Must be >= `min_speaker_count`. This range gives
      you more flexibility by allowing the system to automatically determine
      the correct number of speakers.
    minSpeakerCount: Required. Minimum number of speakers in the conversation.
      This range gives you more flexibility by allowing the system to
      automatically determine the correct number of speakers. To fix the
      number of speakers detected in the audio, set `min_speaker_count` =
      `max_speaker_count`.
  """

  maxSpeakerCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minSpeakerCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class SpeechAdaptation(_messages.Message):
  r"""Provides "hints" to the speech recognizer to favor specific words and
  phrases in the results. PhraseSets can be specified as an inline resource,
  or a reference to an existing PhraseSet resource.

  Fields:
    customClasses: A list of inline CustomClasses. Existing CustomClass
      resources can be referenced directly in a PhraseSet.
    phraseSets: A list of inline or referenced PhraseSets.
  """

  customClasses = _messages.MessageField('CustomClass', 1, repeated=True)
  phraseSets = _messages.MessageField('AdaptationPhraseSet', 2, repeated=True)


class SpeechProjectsLocationsConfigGetRequest(_messages.Message):
  r"""A SpeechProjectsLocationsConfigGetRequest object.

  Fields:
    name: Required. The name of the config to retrieve. There is exactly one
      config resource per project per location. The expected format is
      `projects/{project}/locations/{location}/config`.
  """

  name = _messages.StringField(1, required=True)


class SpeechProjectsLocationsConfigUpdateRequest(_messages.Message):
  r"""A SpeechProjectsLocationsConfigUpdateRequest object.

  Fields:
    config: A Config resource to be passed as the request body.
    name: Output only. Identifier. The name of the config resource. There is
      exactly one config resource per project per location. The expected
      format is `projects/{project}/locations/{location}/config`.
    updateMask: The list of fields to be updated.
  """

  config = _messages.MessageField('Config', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SpeechProjectsLocationsCustomClassesCreateRequest(_messages.Message):
  r"""A SpeechProjectsLocationsCustomClassesCreateRequest object.

  Fields:
    customClass: A CustomClass resource to be passed as the request body.
    customClassId: The ID to use for the CustomClass, which will become the
      final component of the CustomClass's resource name. This value should be
      4-63 characters, and valid characters are /a-z-/.
    parent: Required. The project and location where this CustomClass will be
      created. The expected format is
      `projects/{project}/locations/{location}`.
    validateOnly: If set, validate the request and preview the CustomClass,
      but do not actually create it.
  """

  customClass = _messages.MessageField('CustomClass', 1)
  customClassId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class SpeechProjectsLocationsCustomClassesDeleteRequest(_messages.Message):
  r"""A SpeechProjectsLocationsCustomClassesDeleteRequest object.

  Fields:
    allowMissing: If set to true, and the CustomClass is not found, the
      request will succeed and be a no-op (no Operation is recorded in this
      case).
    etag: This checksum is computed by the server based on the value of other
      fields. This may be sent on update, undelete, and delete requests to
      ensure the client has an up-to-date value before proceeding.
    name: Required. The name of the CustomClass to delete. Format:
      `projects/{project}/locations/{location}/customClasses/{custom_class}`
    validateOnly: If set, validate the request and preview the deleted
      CustomClass, but do not actually delete it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class SpeechProjectsLocationsCustomClassesGetRequest(_messages.Message):
  r"""A SpeechProjectsLocationsCustomClassesGetRequest object.

  Fields:
    name: Required. The name of the CustomClass to retrieve. The expected
      format is
      `projects/{project}/locations/{location}/customClasses/{custom_class}`.
  """

  name = _messages.StringField(1, required=True)


class SpeechProjectsLocationsCustomClassesListRequest(_messages.Message):
  r"""A SpeechProjectsLocationsCustomClassesListRequest object.

  Fields:
    pageSize: Number of results per requests. A valid page_size ranges from 0
      to 100 inclusive. If the page_size is zero or unspecified, a page size
      of 5 will be chosen. If the page size exceeds 100, it will be coerced
      down to 100. Note that a call might return fewer results than the
      requested page size.
    pageToken: A page token, received from a previous ListCustomClasses call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to ListCustomClasses must match the call that
      provided the page token.
    parent: Required. The project and location of CustomClass resources to
      list. The expected format is `projects/{project}/locations/{location}`.
    showDeleted: Whether, or not, to show resources that have been deleted.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class SpeechProjectsLocationsCustomClassesPatchRequest(_messages.Message):
  r"""A SpeechProjectsLocationsCustomClassesPatchRequest object.

  Fields:
    customClass: A CustomClass resource to be passed as the request body.
    name: Output only. Identifier. The resource name of the CustomClass.
      Format:
      `projects/{project}/locations/{location}/customClasses/{custom_class}`.
    updateMask: The list of fields to be updated. If empty, all fields are
      considered for update.
    validateOnly: If set, validate the request and preview the updated
      CustomClass, but do not actually update it.
  """

  customClass = _messages.MessageField('CustomClass', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SpeechProjectsLocationsGetRequest(_messages.Message):
  r"""A SpeechProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class SpeechProjectsLocationsListRequest(_messages.Message):
  r"""A SpeechProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class SpeechProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A SpeechProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class SpeechProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A SpeechProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class SpeechProjectsLocationsPhraseSetsCreateRequest(_messages.Message):
  r"""A SpeechProjectsLocationsPhraseSetsCreateRequest object.

  Fields:
    parent: Required. The project and location where this PhraseSet will be
      created. The expected format is
      `projects/{project}/locations/{location}`.
    phraseSet: A PhraseSet resource to be passed as the request body.
    phraseSetId: The ID to use for the PhraseSet, which will become the final
      component of the PhraseSet's resource name. This value should be 4-63
      characters, and valid characters are /a-z-/.
    validateOnly: If set, validate the request and preview the PhraseSet, but
      do not actually create it.
  """

  parent = _messages.StringField(1, required=True)
  phraseSet = _messages.MessageField('PhraseSet', 2)
  phraseSetId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SpeechProjectsLocationsPhraseSetsDeleteRequest(_messages.Message):
  r"""A SpeechProjectsLocationsPhraseSetsDeleteRequest object.

  Fields:
    allowMissing: If set to true, and the PhraseSet is not found, the request
      will succeed and be a no-op (no Operation is recorded in this case).
    etag: This checksum is computed by the server based on the value of other
      fields. This may be sent on update, undelete, and delete requests to
      ensure the client has an up-to-date value before proceeding.
    name: Required. The name of the PhraseSet to delete. Format:
      `projects/{project}/locations/{location}/phraseSets/{phrase_set}`
    validateOnly: If set, validate the request and preview the deleted
      PhraseSet, but do not actually delete it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class SpeechProjectsLocationsPhraseSetsGetRequest(_messages.Message):
  r"""A SpeechProjectsLocationsPhraseSetsGetRequest object.

  Fields:
    name: Required. The name of the PhraseSet to retrieve. The expected format
      is `projects/{project}/locations/{location}/phraseSets/{phrase_set}`.
  """

  name = _messages.StringField(1, required=True)


class SpeechProjectsLocationsPhraseSetsListRequest(_messages.Message):
  r"""A SpeechProjectsLocationsPhraseSetsListRequest object.

  Fields:
    pageSize: The maximum number of PhraseSets to return. The service may
      return fewer than this value. If unspecified, at most 5 PhraseSets will
      be returned. The maximum value is 100; values above 100 will be coerced
      to 100.
    pageToken: A page token, received from a previous ListPhraseSets call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to ListPhraseSets must match the call that provided
      the page token.
    parent: Required. The project and location of PhraseSet resources to list.
      The expected format is `projects/{project}/locations/{location}`.
    showDeleted: Whether, or not, to show resources that have been deleted.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class SpeechProjectsLocationsPhraseSetsPatchRequest(_messages.Message):
  r"""A SpeechProjectsLocationsPhraseSetsPatchRequest object.

  Fields:
    name: Output only. Identifier. The resource name of the PhraseSet. Format:
      `projects/{project}/locations/{location}/phraseSets/{phrase_set}`.
    phraseSet: A PhraseSet resource to be passed as the request body.
    updateMask: The list of fields to update. If empty, all non-default valued
      fields are considered for update. Use `*` to update the entire PhraseSet
      resource.
    validateOnly: If set, validate the request and preview the updated
      PhraseSet, but do not actually update it.
  """

  name = _messages.StringField(1, required=True)
  phraseSet = _messages.MessageField('PhraseSet', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SpeechProjectsLocationsRecognizersCreateRequest(_messages.Message):
  r"""A SpeechProjectsLocationsRecognizersCreateRequest object.

  Fields:
    parent: Required. The project and location where this Recognizer will be
      created. The expected format is
      `projects/{project}/locations/{location}`.
    recognizer: A Recognizer resource to be passed as the request body.
    recognizerId: The ID to use for the Recognizer, which will become the
      final component of the Recognizer's resource name. This value should be
      4-63 characters, and valid characters are /a-z-/.
    validateOnly: If set, validate the request and preview the Recognizer, but
      do not actually create it.
  """

  parent = _messages.StringField(1, required=True)
  recognizer = _messages.MessageField('Recognizer', 2)
  recognizerId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SpeechProjectsLocationsRecognizersDeleteRequest(_messages.Message):
  r"""A SpeechProjectsLocationsRecognizersDeleteRequest object.

  Fields:
    allowMissing: If set to true, and the Recognizer is not found, the request
      will succeed and be a no-op (no Operation is recorded in this case).
    etag: This checksum is computed by the server based on the value of other
      fields. This may be sent on update, undelete, and delete requests to
      ensure the client has an up-to-date value before proceeding.
    name: Required. The name of the Recognizer to delete. Format:
      `projects/{project}/locations/{location}/recognizers/{recognizer}`
    validateOnly: If set, validate the request and preview the deleted
      Recognizer, but do not actually delete it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class SpeechProjectsLocationsRecognizersGetRequest(_messages.Message):
  r"""A SpeechProjectsLocationsRecognizersGetRequest object.

  Fields:
    name: Required. The name of the Recognizer to retrieve. The expected
      format is
      `projects/{project}/locations/{location}/recognizers/{recognizer}`.
  """

  name = _messages.StringField(1, required=True)


class SpeechProjectsLocationsRecognizersListRequest(_messages.Message):
  r"""A SpeechProjectsLocationsRecognizersListRequest object.

  Fields:
    pageSize: The maximum number of Recognizers to return. The service may
      return fewer than this value. If unspecified, at most 5 Recognizers will
      be returned. The maximum value is 100; values above 100 will be coerced
      to 100.
    pageToken: A page token, received from a previous ListRecognizers call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to ListRecognizers must match the call that provided
      the page token.
    parent: Required. The project and location of Recognizers to list. The
      expected format is `projects/{project}/locations/{location}`.
    showDeleted: Whether, or not, to show resources that have been deleted.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class SpeechProjectsLocationsRecognizersPatchRequest(_messages.Message):
  r"""A SpeechProjectsLocationsRecognizersPatchRequest object.

  Fields:
    name: Output only. Identifier. The resource name of the Recognizer.
      Format:
      `projects/{project}/locations/{location}/recognizers/{recognizer}`.
    recognizer: A Recognizer resource to be passed as the request body.
    updateMask: The list of fields to update. If empty, all non-default valued
      fields are considered for update. Use `*` to update the entire
      Recognizer resource.
    validateOnly: If set, validate the request and preview the updated
      Recognizer, but do not actually update it.
  """

  name = _messages.StringField(1, required=True)
  recognizer = _messages.MessageField('Recognizer', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SpeechProjectsLocationsRecognizersRecognizeRequest(_messages.Message):
  r"""A SpeechProjectsLocationsRecognizersRecognizeRequest object.

  Fields:
    recognizeRequest: A RecognizeRequest resource to be passed as the request
      body.
    recognizer: Required. The name of the Recognizer to use during
      recognition. The expected format is
      `projects/{project}/locations/{location}/recognizers/{recognizer}`. The
      {recognizer} segment may be set to `_` to use an empty implicit
      Recognizer.
  """

  recognizeRequest = _messages.MessageField('RecognizeRequest', 1)
  recognizer = _messages.StringField(2, required=True)


class SpeechRecognitionAlternative(_messages.Message):
  r"""Alternative hypotheses (a.k.a. n-best list).

  Fields:
    confidence: The confidence estimate between 0.0 and 1.0. A higher number
      indicates an estimated greater likelihood that the recognized words are
      correct. This field is set only for the top alternative of a non-
      streaming result or, of a streaming result where is_final is set to
      `true`. This field is not guaranteed to be accurate and users should not
      rely on it to be always provided. The default of 0.0 is a sentinel value
      indicating `confidence` was not set.
    transcript: Transcript text representing the words that the user spoke.
    words: A list of word-specific information for each recognized word. When
      the SpeakerDiarizationConfig is set, you will see all the words from the
      beginning of the audio.
  """

  confidence = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  transcript = _messages.StringField(2)
  words = _messages.MessageField('WordInfo', 3, repeated=True)


class SpeechRecognitionResult(_messages.Message):
  r"""A speech recognition result corresponding to a portion of the audio.

  Fields:
    alternatives: May contain one or more recognition hypotheses. These
      alternatives are ordered in terms of accuracy, with the top (first)
      alternative being the most probable, as ranked by the recognizer.
    channelTag: For multi-channel audio, this is the channel number
      corresponding to the recognized result for the audio from that channel.
      For `audio_channel_count` = `N`, its output values can range from `1` to
      `N`.
    languageCode: Output only. The [BCP-47](https://www.rfc-
      editor.org/rfc/bcp/bcp47.txt) language tag of the language in this
      result. This language code was detected to have the most likelihood of
      being spoken in the audio.
    resultEndOffset: Time offset of the end of this result relative to the
      beginning of the audio.
  """

  alternatives = _messages.MessageField('SpeechRecognitionAlternative', 1, repeated=True)
  channelTag = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  languageCode = _messages.StringField(3)
  resultEndOffset = _messages.StringField(4)


class SrtOutputFileFormatConfig(_messages.Message):
  r"""Output configurations [SubRip
  Text](https://www.matroska.org/technical/subtitles.html#srt-subtitles)
  formatted subtitle file.
  """



class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StreamingRecognitionResult(_messages.Message):
  r"""A streaming speech recognition result corresponding to a portion of the
  audio that is currently being processed.

  Fields:
    alternatives: May contain one or more recognition hypotheses. These
      alternatives are ordered in terms of accuracy, with the top (first)
      alternative being the most probable, as ranked by the recognizer.
    channelTag: For multi-channel audio, this is the channel number
      corresponding to the recognized result for the audio from that channel.
      For `audio_channel_count` = `N`, its output values can range from `1` to
      `N`.
    isFinal: If `false`, this StreamingRecognitionResult represents an interim
      result that may change. If `true`, this is the final time the speech
      service will return this particular StreamingRecognitionResult, the
      recognizer will not return any further hypotheses for this portion of
      the transcript and corresponding audio.
    languageCode: Output only. The [BCP-47](https://www.rfc-
      editor.org/rfc/bcp/bcp47.txt) language tag of the language in this
      result. This language code was detected to have the most likelihood of
      being spoken in the audio.
    resultEndOffset: Time offset of the end of this result relative to the
      beginning of the audio.
    stability: An estimate of the likelihood that the recognizer will not
      change its guess about this interim result. Values range from 0.0
      (completely unstable) to 1.0 (completely stable). This field is only
      provided for interim results (is_final=`false`). The default of 0.0 is a
      sentinel value indicating `stability` was not set.
  """

  alternatives = _messages.MessageField('SpeechRecognitionAlternative', 1, repeated=True)
  channelTag = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  isFinal = _messages.BooleanField(3)
  languageCode = _messages.StringField(4)
  resultEndOffset = _messages.StringField(5)
  stability = _messages.FloatField(6, variant=_messages.Variant.FLOAT)


class TranscriptNormalization(_messages.Message):
  r"""Transcription normalization configuration. Use transcription
  normalization to automatically replace parts of the transcript with phrases
  of your choosing. For StreamingRecognize, this normalization only applies to
  stable partial transcripts (stability > 0.8) and final transcripts.

  Fields:
    entries: A list of replacement entries. We will perform replacement with
      one entry at a time. For example, the second entry in ["cat" => "dog",
      "mountain cat" => "mountain dog"] will never be applied because we will
      always process the first entry before it. At most 100 entries.
  """

  entries = _messages.MessageField('Entry', 1, repeated=True)


class TranslationConfig(_messages.Message):
  r"""Translation configuration. Use to translate the given audio into text
  for the desired language.

  Fields:
    targetLanguage: Required. The language code to translate to.
  """

  targetLanguage = _messages.StringField(1)


class UndeleteCustomClassRequest(_messages.Message):
  r"""Request message for the UndeleteCustomClass method.

  Fields:
    etag: This checksum is computed by the server based on the value of other
      fields. This may be sent on update, undelete, and delete requests to
      ensure the client has an up-to-date value before proceeding.
    name: Required. The name of the CustomClass to undelete. Format:
      `projects/{project}/locations/{location}/customClasses/{custom_class}`
    validateOnly: If set, validate the request and preview the undeleted
      CustomClass, but do not actually undelete it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class UndeletePhraseSetRequest(_messages.Message):
  r"""Request message for the UndeletePhraseSet method.

  Fields:
    etag: This checksum is computed by the server based on the value of other
      fields. This may be sent on update, undelete, and delete requests to
      ensure the client has an up-to-date value before proceeding.
    name: Required. The name of the PhraseSet to undelete. Format:
      `projects/{project}/locations/{location}/phraseSets/{phrase_set}`
    validateOnly: If set, validate the request and preview the undeleted
      PhraseSet, but do not actually undelete it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class UndeleteRecognizerRequest(_messages.Message):
  r"""Request message for the UndeleteRecognizer method.

  Fields:
    etag: This checksum is computed by the server based on the value of other
      fields. This may be sent on update, undelete, and delete requests to
      ensure the client has an up-to-date value before proceeding.
    name: Required. The name of the Recognizer to undelete. Format:
      `projects/{project}/locations/{location}/recognizers/{recognizer}`
    validateOnly: If set, validate the request and preview the undeleted
      Recognizer, but do not actually undelete it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class UpdateConfigRequest(_messages.Message):
  r"""Request message for the UpdateConfig method.

  Fields:
    config: Required. The config to update. The config's `name` field is used
      to identify the config to be updated. The expected format is
      `projects/{project}/locations/{location}/config`.
    updateMask: The list of fields to be updated.
  """

  config = _messages.MessageField('Config', 1)
  updateMask = _messages.StringField(2)


class UpdateCustomClassRequest(_messages.Message):
  r"""Request message for the UpdateCustomClass method.

  Fields:
    customClass: Required. The CustomClass to update. The CustomClass's `name`
      field is used to identify the CustomClass to update. Format:
      `projects/{project}/locations/{location}/customClasses/{custom_class}`.
    updateMask: The list of fields to be updated. If empty, all fields are
      considered for update.
    validateOnly: If set, validate the request and preview the updated
      CustomClass, but do not actually update it.
  """

  customClass = _messages.MessageField('CustomClass', 1)
  updateMask = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class UpdatePhraseSetRequest(_messages.Message):
  r"""Request message for the UpdatePhraseSet method.

  Fields:
    phraseSet: Required. The PhraseSet to update. The PhraseSet's `name` field
      is used to identify the PhraseSet to update. Format:
      `projects/{project}/locations/{location}/phraseSets/{phrase_set}`.
    updateMask: The list of fields to update. If empty, all non-default valued
      fields are considered for update. Use `*` to update the entire PhraseSet
      resource.
    validateOnly: If set, validate the request and preview the updated
      PhraseSet, but do not actually update it.
  """

  phraseSet = _messages.MessageField('PhraseSet', 1)
  updateMask = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class UpdateRecognizerRequest(_messages.Message):
  r"""Request message for the UpdateRecognizer method.

  Fields:
    recognizer: Required. The Recognizer to update. The Recognizer's `name`
      field is used to identify the Recognizer to update. Format:
      `projects/{project}/locations/{location}/recognizers/{recognizer}`.
    updateMask: The list of fields to update. If empty, all non-default valued
      fields are considered for update. Use `*` to update the entire
      Recognizer resource.
    validateOnly: If set, validate the request and preview the updated
      Recognizer, but do not actually update it.
  """

  recognizer = _messages.MessageField('Recognizer', 1)
  updateMask = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class VttOutputFileFormatConfig(_messages.Message):
  r"""Output configurations for [WebVTT](https://www.w3.org/TR/webvtt1/)
  formatted subtitle file.
  """



class WordInfo(_messages.Message):
  r"""Word-specific information for recognized words.

  Fields:
    confidence: The confidence estimate between 0.0 and 1.0. A higher number
      indicates an estimated greater likelihood that the recognized words are
      correct. This field is set only for the top alternative of a non-
      streaming result or, of a streaming result where is_final is set to
      `true`. This field is not guaranteed to be accurate and users should not
      rely on it to be always provided. The default of 0.0 is a sentinel value
      indicating `confidence` was not set.
    endOffset: Time offset relative to the beginning of the audio, and
      corresponding to the end of the spoken word. This field is only set if
      enable_word_time_offsets is `true` and only in the top hypothesis. This
      is an experimental feature and the accuracy of the time offset can vary.
    speakerLabel: A distinct label is assigned for every speaker within the
      audio. This field specifies which one of those speakers was detected to
      have spoken this word. `speaker_label` is set if
      SpeakerDiarizationConfig is given and only in the top alternative.
    startOffset: Time offset relative to the beginning of the audio, and
      corresponding to the start of the spoken word. This field is only set if
      enable_word_time_offsets is `true` and only in the top hypothesis. This
      is an experimental feature and the accuracy of the time offset can vary.
    word: The word corresponding to this set of information.
  """

  confidence = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  endOffset = _messages.StringField(2)
  speakerLabel = _messages.StringField(3)
  startOffset = _messages.StringField(4)
  word = _messages.StringField(5)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
