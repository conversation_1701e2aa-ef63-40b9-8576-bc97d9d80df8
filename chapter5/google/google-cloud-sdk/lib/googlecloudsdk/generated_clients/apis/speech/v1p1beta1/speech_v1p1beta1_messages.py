"""Generated message classes for speech version v1p1beta1.

Converts audio to text by applying powerful neural network models.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'speech'


class ABNFGrammar(_messages.Message):
  r"""A ABNFGrammar object.

  Fields:
    abnfStrings: All declarations and rules of an ABNF grammar broken up into
      multiple strings that will end up concatenated.
  """

  abnfStrings = _messages.StringField(1, repeated=True)


class ClassItem(_messages.Message):
  r"""An item of the class.

  Fields:
    value: The class item's value.
  """

  value = _messages.StringField(1)


class CreateCustomClassRequest(_messages.Message):
  r"""Message sent by the client for the `CreateCustomClass` method.

  Fields:
    customClass: Required. The custom class to create.
    customClassId: Required. The ID to use for the custom class, which will
      become the final component of the custom class' resource name. This
      value should restrict to letters, numbers, and hyphens, with the first
      character a letter, the last a letter or a number, and be 4-63
      characters.
  """

  customClass = _messages.MessageField('CustomClass', 1)
  customClassId = _messages.StringField(2)


class CreatePhraseSetRequest(_messages.Message):
  r"""Message sent by the client for the `CreatePhraseSet` method.

  Fields:
    phraseSet: Required. The phrase set to create.
    phraseSetId: Required. The ID to use for the phrase set, which will become
      the final component of the phrase set's resource name. This value should
      restrict to letters, numbers, and hyphens, with the first character a
      letter, the last a letter or a number, and be 4-63 characters.
  """

  phraseSet = _messages.MessageField('PhraseSet', 1)
  phraseSetId = _messages.StringField(2)


class CustomClass(_messages.Message):
  r"""A set of words or phrases that represents a common concept likely to
  appear in your audio, for example a list of passenger ship names.
  CustomClass items can be substituted into placeholders that you set in
  PhraseSet phrases.

  Enums:
    StateValueValuesEnum: Output only. The CustomClass lifecycle state. This
      field is not used.

  Messages:
    AnnotationsValue: Output only. Allows users to store small amounts of
      arbitrary data. Both the key and the value must be 63 characters or less
      each. At most 100 annotations. This field is not used.

  Fields:
    annotations: Output only. Allows users to store small amounts of arbitrary
      data. Both the key and the value must be 63 characters or less each. At
      most 100 annotations. This field is not used.
    customClassId: If this custom class is a resource, the custom_class_id is
      the resource id of the CustomClass. Case sensitive.
    deleteTime: Output only. The time at which this resource was requested for
      deletion. This field is not used.
    displayName: Output only. User-settable, human-readable name for the
      CustomClass. Must be 63 characters or less. This field is not used.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields. This may be sent on update, undelete, and delete
      requests to ensure the client has an up-to-date value before proceeding.
      This field is not used.
    expireTime: Output only. The time at which this resource will be purged.
      This field is not used.
    items: A collection of class items.
    kmsKeyName: Output only. The [KMS key
      name](https://cloud.google.com/kms/docs/resource-hierarchy#keys) with
      which the content of the ClassItem is encrypted. The expected format is
      `projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/
      {crypto_key}`.
    kmsKeyVersionName: Output only. The [KMS key version
      name](https://cloud.google.com/kms/docs/resource-hierarchy#key_versions)
      with which content of the ClassItem is encrypted. The expected format is
      `projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/
      {crypto_key}/cryptoKeyVersions/{crypto_key_version}`.
    name: The resource name of the custom class.
    reconciling: Output only. Whether or not this CustomClass is in the
      process of being updated. This field is not used.
    state: Output only. The CustomClass lifecycle state. This field is not
      used.
    uid: Output only. System-assigned unique identifier for the CustomClass.
      This field is not used.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The CustomClass lifecycle state. This field is not used.

    Values:
      STATE_UNSPECIFIED: Unspecified state. This is only used/useful for
        distinguishing unset values.
      ACTIVE: The normal and active state.
      DELETED: This CustomClass has been deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Output only. Allows users to store small amounts of arbitrary data.
    Both the key and the value must be 63 characters or less each. At most 100
    annotations. This field is not used.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  customClassId = _messages.StringField(2)
  deleteTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  expireTime = _messages.StringField(6)
  items = _messages.MessageField('ClassItem', 7, repeated=True)
  kmsKeyName = _messages.StringField(8)
  kmsKeyVersionName = _messages.StringField(9)
  name = _messages.StringField(10)
  reconciling = _messages.BooleanField(11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  uid = _messages.StringField(13)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Entry(_messages.Message):
  r"""A single replacement configuration.

  Fields:
    caseSensitive: Whether the search is case sensitive.
    replace: What to replace with. Max length is 100 characters.
    search: What to replace. Max length is 100 characters.
  """

  caseSensitive = _messages.BooleanField(1)
  replace = _messages.StringField(2)
  search = _messages.StringField(3)


class ListCustomClassesResponse(_messages.Message):
  r"""Message returned to the client by the `ListCustomClasses` method.

  Fields:
    customClasses: The custom classes.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  customClasses = _messages.MessageField('CustomClass', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListPhraseSetResponse(_messages.Message):
  r"""Message returned to the client by the `ListPhraseSet` method.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    phraseSets: The phrase set.
  """

  nextPageToken = _messages.StringField(1)
  phraseSets = _messages.MessageField('PhraseSet', 2, repeated=True)


class LongRunningRecognizeMetadata(_messages.Message):
  r"""Describes the progress of a long-running `LongRunningRecognize` call. It
  is included in the `metadata` field of the `Operation` returned by the
  `GetOperation` call of the `google::longrunning::Operations` service.

  Fields:
    lastUpdateTime: Time of the most recent processing update.
    outputConfig: Output only. A copy of the TranscriptOutputConfig if it was
      set in the request.
    progressPercent: Approximate percentage of audio processed thus far.
      Guaranteed to be 100 when the audio is fully processed and the results
      are available.
    startTime: Time when the request was received.
    uri: Output only. The URI of the audio file being transcribed. Empty if
      the audio was sent as byte content.
  """

  lastUpdateTime = _messages.StringField(1)
  outputConfig = _messages.MessageField('TranscriptOutputConfig', 2)
  progressPercent = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  startTime = _messages.StringField(4)
  uri = _messages.StringField(5)


class LongRunningRecognizeRequest(_messages.Message):
  r"""The top-level message sent by the client for the `LongRunningRecognize`
  method.

  Fields:
    audio: Required. The audio data to be recognized.
    config: Required. Provides information to the recognizer that specifies
      how to process the request.
    outputConfig: Optional. Specifies an optional destination for the
      recognition results.
  """

  audio = _messages.MessageField('RecognitionAudio', 1)
  config = _messages.MessageField('RecognitionConfig', 2)
  outputConfig = _messages.MessageField('TranscriptOutputConfig', 3)


class LongRunningRecognizeResponse(_messages.Message):
  r"""The only message returned to the client by the `LongRunningRecognize`
  method. It contains the result as zero or more sequential
  `SpeechRecognitionResult` messages. It is included in the `result.response`
  field of the `Operation` returned by the `GetOperation` call of the
  `google::longrunning::Operations` service.

  Fields:
    outputConfig: Original output config if present in the request.
    outputError: If the transcript output fails this field contains the
      relevant error.
    requestId: The ID associated with the request. This is a unique ID
      specific only to the given request.
    results: Sequential list of transcription results corresponding to
      sequential portions of audio.
    speechAdaptationInfo: Provides information on speech adaptation behavior
      in response
    totalBilledTime: When available, billed audio seconds for the
      corresponding request.
  """

  outputConfig = _messages.MessageField('TranscriptOutputConfig', 1)
  outputError = _messages.MessageField('Status', 2)
  requestId = _messages.IntegerField(3)
  results = _messages.MessageField('SpeechRecognitionResult', 4, repeated=True)
  speechAdaptationInfo = _messages.MessageField('SpeechAdaptationInfo', 5)
  totalBilledTime = _messages.StringField(6)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class Phrase(_messages.Message):
  r"""A phrases containing words and phrase "hints" so that the speech
  recognition is more likely to recognize them. This can be used to improve
  the accuracy for specific words and phrases, for example, if specific
  commands are typically spoken by the user. This can also be used to add
  additional words to the vocabulary of the recognizer. See [usage
  limits](https://cloud.google.com/speech-to-text/quotas#content). List items
  can also include pre-built or custom classes containing groups of words that
  represent common concepts that occur in natural language. For example,
  rather than providing a phrase hint for every month of the year (e.g. "i was
  born in january", "i was born in febuary", ...), use the pre-built `$MONTH`
  class improves the likelihood of correctly transcribing audio that includes
  months (e.g. "i was born in $month"). To refer to pre-built classes, use the
  class' symbol prepended with `$` e.g. `$MONTH`. To refer to custom classes
  that were defined inline in the request, set the class's `custom_class_id`
  to a string unique to all class resources and inline classes. Then use the
  class' id wrapped in $`{...}` e.g. "${my-months}". To refer to custom
  classes resources, use the class' id wrapped in `${}` (e.g. `${my-months}`).
  Speech-to-Text supports three locations: `global`, `us` (US North America),
  and `eu` (Europe). If you are calling the `speech.googleapis.com` endpoint,
  use the `global` location. To specify a region, use a [regional
  endpoint](https://cloud.google.com/speech-to-text/docs/endpoints) with
  matching `us` or `eu` location value.

  Fields:
    boost: Hint Boost. Overrides the boost set at the phrase set level.
      Positive value will increase the probability that a specific phrase will
      be recognized over other similar sounding phrases. The higher the boost,
      the higher the chance of false positive recognition as well. Negative
      boost will simply be ignored. Though `boost` can accept a wide range of
      positive values, most use cases are best served with values between 0
      and 20. We recommend using a binary search approach to finding the
      optimal value for your use case as well as adding phrases both with and
      without boost to your requests.
    value: The phrase itself.
  """

  boost = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  value = _messages.StringField(2)


class PhraseSet(_messages.Message):
  r"""Provides "hints" to the speech recognizer to favor specific words and
  phrases in the results.

  Enums:
    StateValueValuesEnum: Output only. The CustomClass lifecycle state. This
      field is not used.

  Messages:
    AnnotationsValue: Output only. Allows users to store small amounts of
      arbitrary data. Both the key and the value must be 63 characters or less
      each. At most 100 annotations. This field is not used.

  Fields:
    annotations: Output only. Allows users to store small amounts of arbitrary
      data. Both the key and the value must be 63 characters or less each. At
      most 100 annotations. This field is not used.
    boost: Hint Boost. Positive value will increase the probability that a
      specific phrase will be recognized over other similar sounding phrases.
      The higher the boost, the higher the chance of false positive
      recognition as well. Negative boost values would correspond to anti-
      biasing. Anti-biasing is not enabled, so negative boost will simply be
      ignored. Though `boost` can accept a wide range of positive values, most
      use cases are best served with values between 0 (exclusive) and 20. We
      recommend using a binary search approach to finding the optimal value
      for your use case as well as adding phrases both with and without boost
      to your requests.
    deleteTime: Output only. The time at which this resource was requested for
      deletion. This field is not used.
    displayName: Output only. User-settable, human-readable name for the
      PhraseSet. Must be 63 characters or less. This field is not used.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields. This may be sent on update, undelete, and delete
      requests to ensure the client has an up-to-date value before proceeding.
      This field is not used.
    expireTime: Output only. The time at which this resource will be purged.
      This field is not used.
    kmsKeyName: Output only. The [KMS key
      name](https://cloud.google.com/kms/docs/resource-hierarchy#keys) with
      which the content of the PhraseSet is encrypted. The expected format is
      `projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/
      {crypto_key}`.
    kmsKeyVersionName: Output only. The [KMS key version
      name](https://cloud.google.com/kms/docs/resource-hierarchy#key_versions)
      with which content of the PhraseSet is encrypted. The expected format is
      `projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/
      {crypto_key}/cryptoKeyVersions/{crypto_key_version}`.
    name: The resource name of the phrase set.
    phrases: A list of word and phrases.
    reconciling: Output only. Whether or not this PhraseSet is in the process
      of being updated. This field is not used.
    state: Output only. The CustomClass lifecycle state. This field is not
      used.
    uid: Output only. System-assigned unique identifier for the PhraseSet.
      This field is not used.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The CustomClass lifecycle state. This field is not used.

    Values:
      STATE_UNSPECIFIED: Unspecified state. This is only used/useful for
        distinguishing unset values.
      ACTIVE: The normal and active state.
      DELETED: This CustomClass has been deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Output only. Allows users to store small amounts of arbitrary data.
    Both the key and the value must be 63 characters or less each. At most 100
    annotations. This field is not used.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  boost = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  deleteTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  expireTime = _messages.StringField(6)
  kmsKeyName = _messages.StringField(7)
  kmsKeyVersionName = _messages.StringField(8)
  name = _messages.StringField(9)
  phrases = _messages.MessageField('Phrase', 10, repeated=True)
  reconciling = _messages.BooleanField(11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  uid = _messages.StringField(13)


class RecognitionAudio(_messages.Message):
  r"""Contains audio data in the encoding specified in the
  `RecognitionConfig`. Either `content` or `uri` must be supplied. Supplying
  both or neither returns google.rpc.Code.INVALID_ARGUMENT. See [content
  limits](https://cloud.google.com/speech-to-text/quotas#content).

  Fields:
    content: The audio data bytes encoded as specified in `RecognitionConfig`.
      Note: as with all bytes fields, proto buffers use a pure binary
      representation, whereas JSON representations use base64.
    uri: URI that points to a file that contains audio data bytes as specified
      in `RecognitionConfig`. The file must not be compressed (for example,
      gzip). Currently, only Google Cloud Storage URIs are supported, which
      must be specified in the following format:
      `gs://bucket_name/object_name` (other URI formats return
      google.rpc.Code.INVALID_ARGUMENT). For more information, see [Request
      URIs](https://cloud.google.com/storage/docs/reference-uris).
  """

  content = _messages.BytesField(1)
  uri = _messages.StringField(2)


class RecognitionConfig(_messages.Message):
  r"""Provides information to the recognizer that specifies how to process the
  request.

  Enums:
    EncodingValueValuesEnum: Encoding of audio data sent in all
      `RecognitionAudio` messages. This field is optional for `FLAC` and `WAV`
      audio files and required for all other audio formats. For details, see
      AudioEncoding.

  Fields:
    adaptation: Speech adaptation configuration improves the accuracy of
      speech recognition. For more information, see the [speech
      adaptation](https://cloud.google.com/speech-to-text/docs/adaptation)
      documentation. When speech adaptation is set it supersedes the
      `speech_contexts` field.
    alternativeLanguageCodes: A list of up to 3 additional
      [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt) language tags,
      listing possible alternative languages of the supplied audio. See
      [Language Support](https://cloud.google.com/speech-to-
      text/docs/languages) for a list of the currently supported language
      codes. If alternative languages are listed, recognition result will
      contain recognition in the most likely language detected including the
      main language_code. The recognition result will include the language tag
      of the language detected in the audio. Note: This feature is only
      supported for Voice Command and Voice Search use cases and performance
      may vary for other use cases (e.g., phone call transcription).
    audioChannelCount: The number of channels in the input audio data. ONLY
      set this for MULTI-CHANNEL recognition. Valid values for LINEAR16,
      OGG_OPUS and FLAC are `1`-`8`. Valid value for MULAW, AMR, AMR_WB and
      SPEEX_WITH_HEADER_BYTE is only `1`. If `0` or omitted, defaults to one
      channel (mono). Note: We only recognize the first channel by default. To
      perform independent recognition on each channel set
      `enable_separate_recognition_per_channel` to 'true'.
    diarizationConfig: Config to enable speaker diarization and set additional
      parameters to make diarization better suited for your application. Note:
      When this is enabled, we send all the words from the beginning of the
      audio for the top alternative in every consecutive STREAMING responses.
      This is done in order to improve our speaker tags as our models learn to
      identify the speakers in the conversation over time. For non-streaming
      requests, the diarization results will be provided only in the top
      alternative of the FINAL SpeechRecognitionResult.
    diarizationSpeakerCount: If set, specifies the estimated number of
      speakers in the conversation. Defaults to '2'. Ignored unless
      enable_speaker_diarization is set to true. Note: Use diarization_config
      instead.
    enableAutomaticPunctuation: If 'true', adds punctuation to recognition
      result hypotheses. This feature is only available in select languages.
      Setting this for requests in other languages has no effect at all. The
      default 'false' value does not add punctuation to result hypotheses.
    enableSeparateRecognitionPerChannel: This needs to be set to `true`
      explicitly and `audio_channel_count` > 1 to get each channel recognized
      separately. The recognition result will contain a `channel_tag` field to
      state which channel that result belongs to. If this is not true, we will
      only recognize the first channel. The request is billed cumulatively for
      all channels recognized: `audio_channel_count` multiplied by the length
      of the audio.
    enableSpeakerDiarization: If 'true', enables speaker detection for each
      recognized word in the top alternative of the recognition result using a
      speaker_label provided in the WordInfo. Note: Use diarization_config
      instead.
    enableSpokenEmojis: The spoken emoji behavior for the call If not set,
      uses default behavior based on model of choice If 'true', adds spoken
      emoji formatting for the request. This will replace spoken emojis with
      the corresponding Unicode symbols in the final transcript. If 'false',
      spoken emojis are not replaced.
    enableSpokenPunctuation: The spoken punctuation behavior for the call If
      not set, uses default behavior based on model of choice e.g.
      command_and_search will enable spoken punctuation by default If 'true',
      replaces spoken punctuation with the corresponding symbols in the
      request. For example, "how are you question mark" becomes "how are
      you?". See https://cloud.google.com/speech-to-text/docs/spoken-
      punctuation for support. If 'false', spoken punctuation is not replaced.
    enableWordConfidence: If `true`, the top result includes a list of words
      and the confidence for those words. If `false`, no word-level confidence
      information is returned. The default is `false`.
    enableWordTimeOffsets: If `true`, the top result includes a list of words
      and the start and end time offsets (timestamps) for those words. If
      `false`, no word-level time offset information is returned. The default
      is `false`.
    encoding: Encoding of audio data sent in all `RecognitionAudio` messages.
      This field is optional for `FLAC` and `WAV` audio files and required for
      all other audio formats. For details, see AudioEncoding.
    languageCode: Required. The language of the supplied audio as a
      [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt) language tag.
      Example: "en-US". See [Language
      Support](https://cloud.google.com/speech-to-text/docs/languages) for a
      list of the currently supported language codes.
    maxAlternatives: Maximum number of recognition hypotheses to be returned.
      Specifically, the maximum number of `SpeechRecognitionAlternative`
      messages within each `SpeechRecognitionResult`. The server may return
      fewer than `max_alternatives`. Valid values are `0`-`30`. A value of `0`
      or `1` will return a maximum of one. If omitted, will return a maximum
      of one.
    metadata: Metadata regarding this request.
    model: Which model to select for the given request. Select the model best
      suited to your domain to get best results. If a model is not explicitly
      specified, then we auto-select a model based on the parameters in the
      RecognitionConfig. *Model* *Description* latest_long Best for long form
      content like media or conversation. latest_short Best for short form
      content like commands or single shot directed speech. command_and_search
      Best for short queries such as voice commands or voice search.
      phone_call Best for audio that originated from a phone call (typically
      recorded at an 8khz sampling rate). video Best for audio that originated
      from video or includes multiple speakers. Ideally the audio is recorded
      at a 16khz or greater sampling rate. This is a premium model that costs
      more than the standard rate. default Best for audio that is not one of
      the specific audio models. For example, long-form audio. Ideally the
      audio is high-fidelity, recorded at a 16khz or greater sampling rate.
      medical_conversation Best for audio that originated from a conversation
      between a medical provider and patient. medical_dictation Best for audio
      that originated from dictation notes by a medical provider.
    profanityFilter: If set to `true`, the server will attempt to filter out
      profanities, replacing all but the initial character in each filtered
      word with asterisks, e.g. "f***". If set to `false` or omitted,
      profanities won't be filtered out.
    sampleRateHertz: Sample rate in Hertz of the audio data sent in all
      `RecognitionAudio` messages. Valid values are: 8000-48000. 16000 is
      optimal. For best results, set the sampling rate of the audio source to
      16000 Hz. If that's not possible, use the native sample rate of the
      audio source (instead of re-sampling). This field is optional for FLAC
      and WAV audio files, but is required for all other audio formats. For
      details, see AudioEncoding.
    speechContexts: Array of SpeechContext. A means to provide context to
      assist the speech recognition. For more information, see [speech
      adaptation](https://cloud.google.com/speech-to-text/docs/adaptation).
    transcriptNormalization: Optional. Use transcription normalization to
      automatically replace parts of the transcript with phrases of your
      choosing. For StreamingRecognize, this normalization only applies to
      stable partial transcripts (stability > 0.8) and final transcripts.
    useEnhanced: Set to true to use an enhanced model for speech recognition.
      If `use_enhanced` is set to true and the `model` field is not set, then
      an appropriate enhanced model is chosen if an enhanced model exists for
      the audio. If `use_enhanced` is true and an enhanced version of the
      specified model does not exist, then the speech is recognized using the
      standard version of the specified model.
  """

  class EncodingValueValuesEnum(_messages.Enum):
    r"""Encoding of audio data sent in all `RecognitionAudio` messages. This
    field is optional for `FLAC` and `WAV` audio files and required for all
    other audio formats. For details, see AudioEncoding.

    Values:
      ENCODING_UNSPECIFIED: Not specified.
      LINEAR16: Uncompressed 16-bit signed little-endian samples (Linear PCM).
      FLAC: `FLAC` (Free Lossless Audio Codec) is the recommended encoding
        because it is lossless--therefore recognition is not compromised--and
        requires only about half the bandwidth of `LINEAR16`. `FLAC` stream
        encoding supports 16-bit and 24-bit samples, however, not all fields
        in `STREAMINFO` are supported.
      MULAW: 8-bit samples that compand 14-bit audio samples using G.711
        PCMU/mu-law.
      AMR: Adaptive Multi-Rate Narrowband codec. `sample_rate_hertz` must be
        8000.
      AMR_WB: Adaptive Multi-Rate Wideband codec. `sample_rate_hertz` must be
        16000.
      OGG_OPUS: Opus encoded audio frames in Ogg container
        ([OggOpus](https://wiki.xiph.org/OggOpus)). `sample_rate_hertz` must
        be one of 8000, 12000, 16000, 24000, or 48000.
      SPEEX_WITH_HEADER_BYTE: Although the use of lossy encodings is not
        recommended, if a very low bitrate encoding is required, `OGG_OPUS` is
        highly preferred over Speex encoding. The [Speex](https://speex.org/)
        encoding supported by Cloud Speech API has a header byte in each
        block, as in MIME type `audio/x-speex-with-header-byte`. It is a
        variant of the RTP Speex encoding defined in [RFC
        5574](https://tools.ietf.org/html/rfc5574). The stream is a sequence
        of blocks, one block per RTP packet. Each block starts with a byte
        containing the length of the block, in bytes, followed by one or more
        frames of Speex data, padded to an integral number of bytes (octets)
        as specified in RFC 5574. In other words, each RTP header is replaced
        with a single byte containing the block length. Only Speex wideband is
        supported. `sample_rate_hertz` must be 16000.
      MP3: MP3 audio. MP3 encoding is a Beta feature and only available in
        v1p1beta1. Support all standard MP3 bitrates (which range from 32-320
        kbps). When using this encoding, `sample_rate_hertz` has to match the
        sample rate of the file being used.
      WEBM_OPUS: Opus encoded audio frames in WebM container
        ([WebM](https://www.webmproject.org/docs/container/)).
        `sample_rate_hertz` must be one of 8000, 12000, 16000, 24000, or
        48000.
      ALAW: 8-bit samples that compand 13-bit audio samples using G.711
        PCMU/a-law.
    """
    ENCODING_UNSPECIFIED = 0
    LINEAR16 = 1
    FLAC = 2
    MULAW = 3
    AMR = 4
    AMR_WB = 5
    OGG_OPUS = 6
    SPEEX_WITH_HEADER_BYTE = 7
    MP3 = 8
    WEBM_OPUS = 9
    ALAW = 10

  adaptation = _messages.MessageField('SpeechAdaptation', 1)
  alternativeLanguageCodes = _messages.StringField(2, repeated=True)
  audioChannelCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  diarizationConfig = _messages.MessageField('SpeakerDiarizationConfig', 4)
  diarizationSpeakerCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  enableAutomaticPunctuation = _messages.BooleanField(6)
  enableSeparateRecognitionPerChannel = _messages.BooleanField(7)
  enableSpeakerDiarization = _messages.BooleanField(8)
  enableSpokenEmojis = _messages.BooleanField(9)
  enableSpokenPunctuation = _messages.BooleanField(10)
  enableWordConfidence = _messages.BooleanField(11)
  enableWordTimeOffsets = _messages.BooleanField(12)
  encoding = _messages.EnumField('EncodingValueValuesEnum', 13)
  languageCode = _messages.StringField(14)
  maxAlternatives = _messages.IntegerField(15, variant=_messages.Variant.INT32)
  metadata = _messages.MessageField('RecognitionMetadata', 16)
  model = _messages.StringField(17)
  profanityFilter = _messages.BooleanField(18)
  sampleRateHertz = _messages.IntegerField(19, variant=_messages.Variant.INT32)
  speechContexts = _messages.MessageField('SpeechContext', 20, repeated=True)
  transcriptNormalization = _messages.MessageField('TranscriptNormalization', 21)
  useEnhanced = _messages.BooleanField(22)


class RecognitionMetadata(_messages.Message):
  r"""Description of audio data to be recognized.

  Enums:
    InteractionTypeValueValuesEnum: The use case most closely describing the
      audio content to be recognized.
    MicrophoneDistanceValueValuesEnum: The audio type that most closely
      describes the audio being recognized.
    OriginalMediaTypeValueValuesEnum: The original media the speech was
      recorded on.
    RecordingDeviceTypeValueValuesEnum: The type of device the speech was
      recorded with.

  Fields:
    audioTopic: Description of the content. Eg. "Recordings of federal supreme
      court hearings from 2012".
    industryNaicsCodeOfAudio: The industry vertical to which this speech
      recognition request most closely applies. This is most indicative of the
      topics contained in the audio. Use the 6-digit NAICS code to identify
      the industry vertical - see https://www.naics.com/search/.
    interactionType: The use case most closely describing the audio content to
      be recognized.
    microphoneDistance: The audio type that most closely describes the audio
      being recognized.
    obfuscatedId: Obfuscated (privacy-protected) ID of the user, to identify
      number of unique users using the service.
    originalMediaType: The original media the speech was recorded on.
    originalMimeType: Mime type of the original audio file. For example
      `audio/m4a`, `audio/x-alaw-basic`, `audio/mp3`, `audio/3gpp`. A list of
      possible audio mime types is maintained at
      http://www.iana.org/assignments/media-types/media-types.xhtml#audio
    recordingDeviceName: The device used to make the recording. Examples
      'Nexus 5X' or 'Polycom SoundStation IP 6000' or 'POTS' or 'VoIP' or
      'Cardioid Microphone'.
    recordingDeviceType: The type of device the speech was recorded with.
  """

  class InteractionTypeValueValuesEnum(_messages.Enum):
    r"""The use case most closely describing the audio content to be
    recognized.

    Values:
      INTERACTION_TYPE_UNSPECIFIED: Use case is either unknown or is something
        other than one of the other values below.
      DISCUSSION: Multiple people in a conversation or discussion. For example
        in a meeting with two or more people actively participating. Typically
        all the primary people speaking would be in the same room (if not, see
        PHONE_CALL)
      PRESENTATION: One or more persons lecturing or presenting to others,
        mostly uninterrupted.
      PHONE_CALL: A phone-call or video-conference in which two or more
        people, who are not in the same room, are actively participating.
      VOICEMAIL: A recorded message intended for another person to listen to.
      PROFESSIONALLY_PRODUCED: Professionally produced audio (eg. TV Show,
        Podcast).
      VOICE_SEARCH: Transcribe spoken questions and queries into text.
      VOICE_COMMAND: Transcribe voice commands, such as for controlling a
        device.
      DICTATION: Transcribe speech to text to create a written document, such
        as a text-message, email or report.
    """
    INTERACTION_TYPE_UNSPECIFIED = 0
    DISCUSSION = 1
    PRESENTATION = 2
    PHONE_CALL = 3
    VOICEMAIL = 4
    PROFESSIONALLY_PRODUCED = 5
    VOICE_SEARCH = 6
    VOICE_COMMAND = 7
    DICTATION = 8

  class MicrophoneDistanceValueValuesEnum(_messages.Enum):
    r"""The audio type that most closely describes the audio being recognized.

    Values:
      MICROPHONE_DISTANCE_UNSPECIFIED: Audio type is not known.
      NEARFIELD: The audio was captured from a closely placed microphone. Eg.
        phone, dictaphone, or handheld microphone. Generally if there speaker
        is within 1 meter of the microphone.
      MIDFIELD: The speaker if within 3 meters of the microphone.
      FARFIELD: The speaker is more than 3 meters away from the microphone.
    """
    MICROPHONE_DISTANCE_UNSPECIFIED = 0
    NEARFIELD = 1
    MIDFIELD = 2
    FARFIELD = 3

  class OriginalMediaTypeValueValuesEnum(_messages.Enum):
    r"""The original media the speech was recorded on.

    Values:
      ORIGINAL_MEDIA_TYPE_UNSPECIFIED: Unknown original media type.
      AUDIO: The speech data is an audio recording.
      VIDEO: The speech data originally recorded on a video.
    """
    ORIGINAL_MEDIA_TYPE_UNSPECIFIED = 0
    AUDIO = 1
    VIDEO = 2

  class RecordingDeviceTypeValueValuesEnum(_messages.Enum):
    r"""The type of device the speech was recorded with.

    Values:
      RECORDING_DEVICE_TYPE_UNSPECIFIED: The recording device is unknown.
      SMARTPHONE: Speech was recorded on a smartphone.
      PC: Speech was recorded using a personal computer or tablet.
      PHONE_LINE: Speech was recorded over a phone line.
      VEHICLE: Speech was recorded in a vehicle.
      OTHER_OUTDOOR_DEVICE: Speech was recorded outdoors.
      OTHER_INDOOR_DEVICE: Speech was recorded indoors.
    """
    RECORDING_DEVICE_TYPE_UNSPECIFIED = 0
    SMARTPHONE = 1
    PC = 2
    PHONE_LINE = 3
    VEHICLE = 4
    OTHER_OUTDOOR_DEVICE = 5
    OTHER_INDOOR_DEVICE = 6

  audioTopic = _messages.StringField(1)
  industryNaicsCodeOfAudio = _messages.IntegerField(2, variant=_messages.Variant.UINT32)
  interactionType = _messages.EnumField('InteractionTypeValueValuesEnum', 3)
  microphoneDistance = _messages.EnumField('MicrophoneDistanceValueValuesEnum', 4)
  obfuscatedId = _messages.IntegerField(5)
  originalMediaType = _messages.EnumField('OriginalMediaTypeValueValuesEnum', 6)
  originalMimeType = _messages.StringField(7)
  recordingDeviceName = _messages.StringField(8)
  recordingDeviceType = _messages.EnumField('RecordingDeviceTypeValueValuesEnum', 9)


class RecognizeRequest(_messages.Message):
  r"""The top-level message sent by the client for the `Recognize` method.

  Fields:
    audio: Required. The audio data to be recognized.
    config: Required. Provides information to the recognizer that specifies
      how to process the request.
  """

  audio = _messages.MessageField('RecognitionAudio', 1)
  config = _messages.MessageField('RecognitionConfig', 2)


class RecognizeResponse(_messages.Message):
  r"""The only message returned to the client by the `Recognize` method. It
  contains the result as zero or more sequential `SpeechRecognitionResult`
  messages.

  Fields:
    requestId: The ID associated with the request. This is a unique ID
      specific only to the given request.
    results: Sequential list of transcription results corresponding to
      sequential portions of audio.
    speechAdaptationInfo: Provides information on adaptation behavior in
      response
    totalBilledTime: When available, billed audio seconds for the
      corresponding request.
    usingLegacyModels: Whether request used legacy asr models (was not
      automatically migrated to use conformer models).
  """

  requestId = _messages.IntegerField(1)
  results = _messages.MessageField('SpeechRecognitionResult', 2, repeated=True)
  speechAdaptationInfo = _messages.MessageField('SpeechAdaptationInfo', 3)
  totalBilledTime = _messages.StringField(4)
  usingLegacyModels = _messages.BooleanField(5)


class SpeakerDiarizationConfig(_messages.Message):
  r"""Config to enable speaker diarization.

  Fields:
    enableSpeakerDiarization: If 'true', enables speaker detection for each
      recognized word in the top alternative of the recognition result using a
      speaker_label provided in the WordInfo.
    maxSpeakerCount: Maximum number of speakers in the conversation. This
      range gives you more flexibility by allowing the system to automatically
      determine the correct number of speakers. If not set, the default value
      is 6.
    minSpeakerCount: Minimum number of speakers in the conversation. This
      range gives you more flexibility by allowing the system to automatically
      determine the correct number of speakers. If not set, the default value
      is 2.
    speakerTag: Output only. Unused.
  """

  enableSpeakerDiarization = _messages.BooleanField(1)
  maxSpeakerCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  minSpeakerCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  speakerTag = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class SpeechAdaptation(_messages.Message):
  r"""Speech adaptation configuration.

  Fields:
    abnfGrammar: Augmented Backus-Naur form (ABNF) is a standardized grammar
      notation comprised by a set of derivation rules. See specifications:
      https://www.w3.org/TR/speech-grammar
    customClasses: A collection of custom classes. To specify the classes
      inline, leave the class' `name` blank and fill in the rest of its
      fields, giving it a unique `custom_class_id`. Refer to the inline
      defined class in phrase hints by its `custom_class_id`.
    phraseSetReferences: A collection of phrase set resource names to use.
    phraseSets: A collection of phrase sets. To specify the hints inline,
      leave the phrase set's `name` blank and fill in the rest of its fields.
      Any phrase set can use any custom class.
  """

  abnfGrammar = _messages.MessageField('ABNFGrammar', 1)
  customClasses = _messages.MessageField('CustomClass', 2, repeated=True)
  phraseSetReferences = _messages.StringField(3, repeated=True)
  phraseSets = _messages.MessageField('PhraseSet', 4, repeated=True)


class SpeechAdaptationInfo(_messages.Message):
  r"""Information on speech adaptation use in results

  Fields:
    adaptationTimeout: Whether there was a timeout when applying speech
      adaptation. If true, adaptation had no effect in the response
      transcript.
    timeoutMessage: If set, returns a message specifying which part of the
      speech adaptation request timed out.
  """

  adaptationTimeout = _messages.BooleanField(1)
  timeoutMessage = _messages.StringField(2)


class SpeechContext(_messages.Message):
  r"""Provides "hints" to the speech recognizer to favor specific words and
  phrases in the results.

  Fields:
    boost: Hint Boost. Positive value will increase the probability that a
      specific phrase will be recognized over other similar sounding phrases.
      The higher the boost, the higher the chance of false positive
      recognition as well. Negative boost values would correspond to anti-
      biasing. Anti-biasing is not enabled, so negative boost will simply be
      ignored. Though `boost` can accept a wide range of positive values, most
      use cases are best served with values between 0 and 20. We recommend
      using a binary search approach to finding the optimal value for your use
      case.
    phrases: A list of strings containing words and phrases "hints" so that
      the speech recognition is more likely to recognize them. This can be
      used to improve the accuracy for specific words and phrases, for
      example, if specific commands are typically spoken by the user. This can
      also be used to add additional words to the vocabulary of the
      recognizer. See [usage limits](https://cloud.google.com/speech-to-
      text/quotas#content). List items can also be set to classes for groups
      of words that represent common concepts that occur in natural language.
      For example, rather than providing phrase hints for every month of the
      year, using the $MONTH class improves the likelihood of correctly
      transcribing audio that includes months.
  """

  boost = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  phrases = _messages.StringField(2, repeated=True)


class SpeechOperationsGetRequest(_messages.Message):
  r"""A SpeechOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class SpeechOperationsListRequest(_messages.Message):
  r"""A SpeechOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class SpeechProjectsLocationsCustomClassesCreateRequest(_messages.Message):
  r"""A SpeechProjectsLocationsCustomClassesCreateRequest object.

  Fields:
    createCustomClassRequest: A CreateCustomClassRequest resource to be passed
      as the request body.
    parent: Required. The parent resource where this custom class will be
      created. Format: `projects/{project}/locations/{location}/customClasses`
      Speech-to-Text supports three locations: `global`, `us` (US North
      America), and `eu` (Europe). If you are calling the
      `speech.googleapis.com` endpoint, use the `global` location. To specify
      a region, use a [regional endpoint](https://cloud.google.com/speech-to-
      text/docs/endpoints) with matching `us` or `eu` location value.
  """

  createCustomClassRequest = _messages.MessageField('CreateCustomClassRequest', 1)
  parent = _messages.StringField(2, required=True)


class SpeechProjectsLocationsCustomClassesDeleteRequest(_messages.Message):
  r"""A SpeechProjectsLocationsCustomClassesDeleteRequest object.

  Fields:
    name: Required. The name of the custom class to delete. Format:
      `projects/{project}/locations/{location}/customClasses/{custom_class}`
      Speech-to-Text supports three locations: `global`, `us` (US North
      America), and `eu` (Europe). If you are calling the
      `speech.googleapis.com` endpoint, use the `global` location. To specify
      a region, use a [regional endpoint](https://cloud.google.com/speech-to-
      text/docs/endpoints) with matching `us` or `eu` location value.
  """

  name = _messages.StringField(1, required=True)


class SpeechProjectsLocationsCustomClassesGetRequest(_messages.Message):
  r"""A SpeechProjectsLocationsCustomClassesGetRequest object.

  Fields:
    name: Required. The name of the custom class to retrieve. Format:
      `projects/{project}/locations/{location}/customClasses/{custom_class}`
  """

  name = _messages.StringField(1, required=True)


class SpeechProjectsLocationsCustomClassesListRequest(_messages.Message):
  r"""A SpeechProjectsLocationsCustomClassesListRequest object.

  Fields:
    pageSize: The maximum number of custom classes to return. The service may
      return fewer than this value. If unspecified, at most 50 custom classes
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: A page token, received from a previous `ListCustomClass` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListCustomClass` must match the call that
      provided the page token.
    parent: Required. The parent, which owns this collection of custom
      classes. Format: `projects/{project}/locations/{location}/customClasses`
      Speech-to-Text supports three locations: `global`, `us` (US North
      America), and `eu` (Europe). If you are calling the
      `speech.googleapis.com` endpoint, use the `global` location. To specify
      a region, use a [regional endpoint](https://cloud.google.com/speech-to-
      text/docs/endpoints) with matching `us` or `eu` location value.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SpeechProjectsLocationsCustomClassesPatchRequest(_messages.Message):
  r"""A SpeechProjectsLocationsCustomClassesPatchRequest object.

  Fields:
    customClass: A CustomClass resource to be passed as the request body.
    name: The resource name of the custom class.
    updateMask: The list of fields to be updated.
  """

  customClass = _messages.MessageField('CustomClass', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SpeechProjectsLocationsPhraseSetsCreateRequest(_messages.Message):
  r"""A SpeechProjectsLocationsPhraseSetsCreateRequest object.

  Fields:
    createPhraseSetRequest: A CreatePhraseSetRequest resource to be passed as
      the request body.
    parent: Required. The parent resource where this phrase set will be
      created. Format: `projects/{project}/locations/{location}` Speech-to-
      Text supports three locations: `global`, `us` (US North America), and
      `eu` (Europe). If you are calling the `speech.googleapis.com` endpoint,
      use the `global` location. To specify a region, use a [regional
      endpoint](https://cloud.google.com/speech-to-text/docs/endpoints) with
      matching `us` or `eu` location value.
  """

  createPhraseSetRequest = _messages.MessageField('CreatePhraseSetRequest', 1)
  parent = _messages.StringField(2, required=True)


class SpeechProjectsLocationsPhraseSetsDeleteRequest(_messages.Message):
  r"""A SpeechProjectsLocationsPhraseSetsDeleteRequest object.

  Fields:
    name: Required. The name of the phrase set to delete. Format:
      `projects/{project}/locations/{location}/phraseSets/{phrase_set}`
  """

  name = _messages.StringField(1, required=True)


class SpeechProjectsLocationsPhraseSetsGetRequest(_messages.Message):
  r"""A SpeechProjectsLocationsPhraseSetsGetRequest object.

  Fields:
    name: Required. The name of the phrase set to retrieve. Format:
      `projects/{project}/locations/{location}/phraseSets/{phrase_set}`
      Speech-to-Text supports three locations: `global`, `us` (US North
      America), and `eu` (Europe). If you are calling the
      `speech.googleapis.com` endpoint, use the `global` location. To specify
      a region, use a [regional endpoint](https://cloud.google.com/speech-to-
      text/docs/endpoints) with matching `us` or `eu` location value.
  """

  name = _messages.StringField(1, required=True)


class SpeechProjectsLocationsPhraseSetsListRequest(_messages.Message):
  r"""A SpeechProjectsLocationsPhraseSetsListRequest object.

  Fields:
    pageSize: The maximum number of phrase sets to return. The service may
      return fewer than this value. If unspecified, at most 50 phrase sets
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: A page token, received from a previous `ListPhraseSet` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListPhraseSet` must match the call that provided
      the page token.
    parent: Required. The parent, which owns this collection of phrase set.
      Format: `projects/{project}/locations/{location}` Speech-to-Text
      supports three locations: `global`, `us` (US North America), and `eu`
      (Europe). If you are calling the `speech.googleapis.com` endpoint, use
      the `global` location. To specify a region, use a [regional
      endpoint](https://cloud.google.com/speech-to-text/docs/endpoints) with
      matching `us` or `eu` location value.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SpeechProjectsLocationsPhraseSetsPatchRequest(_messages.Message):
  r"""A SpeechProjectsLocationsPhraseSetsPatchRequest object.

  Fields:
    name: The resource name of the phrase set.
    phraseSet: A PhraseSet resource to be passed as the request body.
    updateMask: The list of fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  phraseSet = _messages.MessageField('PhraseSet', 2)
  updateMask = _messages.StringField(3)


class SpeechRecognitionAlternative(_messages.Message):
  r"""Alternative hypotheses (a.k.a. n-best list).

  Fields:
    confidence: The confidence estimate between 0.0 and 1.0. A higher number
      indicates an estimated greater likelihood that the recognized words are
      correct. This field is set only for the top alternative of a non-
      streaming result or, of a streaming result where `is_final=true`. This
      field is not guaranteed to be accurate and users should not rely on it
      to be always provided. The default of 0.0 is a sentinel value indicating
      `confidence` was not set.
    transcript: Transcript text representing the words that the user spoke. In
      languages that use spaces to separate words, the transcript might have a
      leading space if it isn't the first result. You can concatenate each
      result to obtain the full transcript without using a separator.
    words: A list of word-specific information for each recognized word. Note:
      When `enable_speaker_diarization` is true, you will see all the words
      from the beginning of the audio.
  """

  confidence = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  transcript = _messages.StringField(2)
  words = _messages.MessageField('WordInfo', 3, repeated=True)


class SpeechRecognitionResult(_messages.Message):
  r"""A speech recognition result corresponding to a portion of the audio.

  Fields:
    alternatives: May contain one or more recognition hypotheses (up to the
      maximum specified in `max_alternatives`). These alternatives are ordered
      in terms of accuracy, with the top (first) alternative being the most
      probable, as ranked by the recognizer.
    channelTag: For multi-channel audio, this is the channel number
      corresponding to the recognized result for the audio from that channel.
      For audio_channel_count = N, its output values can range from '1' to
      'N'.
    languageCode: Output only. The [BCP-47](https://www.rfc-
      editor.org/rfc/bcp/bcp47.txt) language tag of the language in this
      result. This language code was detected to have the most likelihood of
      being spoken in the audio.
    resultEndTime: Time offset of the end of this result relative to the
      beginning of the audio.
  """

  alternatives = _messages.MessageField('SpeechRecognitionAlternative', 1, repeated=True)
  channelTag = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  languageCode = _messages.StringField(3)
  resultEndTime = _messages.StringField(4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TranscriptNormalization(_messages.Message):
  r"""Transcription normalization configuration. Use transcription
  normalization to automatically replace parts of the transcript with phrases
  of your choosing. For StreamingRecognize, this normalization only applies to
  stable partial transcripts (stability > 0.8) and final transcripts.

  Fields:
    entries: A list of replacement entries. We will perform replacement with
      one entry at a time. For example, the second entry in ["cat" => "dog",
      "mountain cat" => "mountain dog"] will never be applied because we will
      always process the first entry before it. At most 100 entries.
  """

  entries = _messages.MessageField('Entry', 1, repeated=True)


class TranscriptOutputConfig(_messages.Message):
  r"""Specifies an optional destination for the recognition results.

  Fields:
    gcsUri: Specifies a Cloud Storage URI for the recognition results. Must be
      specified in the format: `gs://bucket_name/object_name`, and the bucket
      must already exist.
  """

  gcsUri = _messages.StringField(1)


class WordInfo(_messages.Message):
  r"""Word-specific information for recognized words.

  Fields:
    confidence: The confidence estimate between 0.0 and 1.0. A higher number
      indicates an estimated greater likelihood that the recognized words are
      correct. This field is set only for the top alternative of a non-
      streaming result or, of a streaming result where `is_final=true`. This
      field is not guaranteed to be accurate and users should not rely on it
      to be always provided. The default of 0.0 is a sentinel value indicating
      `confidence` was not set.
    endTime: Time offset relative to the beginning of the audio, and
      corresponding to the end of the spoken word. This field is only set if
      `enable_word_time_offsets=true` and only in the top hypothesis. This is
      an experimental feature and the accuracy of the time offset can vary.
    speakerLabel: Output only. A label value assigned for every unique speaker
      within the audio. This field specifies which speaker was detected to
      have spoken this word. For some models, like medical_conversation this
      can be actual speaker role, for example "patient" or "provider", but
      generally this would be a number identifying a speaker. This field is
      only set if enable_speaker_diarization = 'true' and only for the top
      alternative.
    speakerTag: Output only. A distinct integer value is assigned for every
      speaker within the audio. This field specifies which one of those
      speakers was detected to have spoken this word. Value ranges from '1' to
      diarization_speaker_count. speaker_tag is set if
      enable_speaker_diarization = 'true' and only for the top alternative.
      Note: Use speaker_label instead.
    startTime: Time offset relative to the beginning of the audio, and
      corresponding to the start of the spoken word. This field is only set if
      `enable_word_time_offsets=true` and only in the top hypothesis. This is
      an experimental feature and the accuracy of the time offset can vary.
    word: The word corresponding to this set of information.
  """

  confidence = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  endTime = _messages.StringField(2)
  speakerLabel = _messages.StringField(3)
  speakerTag = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  startTime = _messages.StringField(5)
  word = _messages.StringField(6)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
