"""Generated message classes for iam version v3alpha.

Manages identity and access control for Google Cloud resources, including the
creation of service accounts, which you can use to authenticate to Google and
make API calls. Enabling this API also enables the IAM Service Account
Credentials API (iamcredentials.googleapis.com). However, disabling this API
doesn't disable the IAM Service Account Credentials API.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'iam'


class GoogleIamAdminV1AuditData(_messages.Message):
  r"""Audit log information specific to Cloud IAM admin APIs. This message is
  serialized as an `Any` type in the `ServiceData` message of an `AuditLog`
  message.

  Fields:
    permissionDelta: The permission_delta when when creating or updating a
      Role.
  """

  permissionDelta = _messages.MessageField('GoogleIamAdminV1AuditDataPermissionDelta', 1)


class GoogleIamAdminV1AuditDataPermissionDelta(_messages.Message):
  r"""A PermissionDelta message to record the added_permissions and
  removed_permissions inside a role.

  Fields:
    addedPermissions: Added permissions.
    removedPermissions: Removed permissions.
  """

  addedPermissions = _messages.StringField(1, repeated=True)
  removedPermissions = _messages.StringField(2, repeated=True)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=********************1`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=********************1`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=********************1`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('GoogleTypeExpr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class GoogleIamV3alphaAccessPolicy(_messages.Message):
  r"""An IAM access policy resource.

  Messages:
    AnnotationsValue: Optional. User defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations

  Fields:
    annotations: Optional. User defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations
    createTime: Output only. The time when the access policy was created.
    details: Optional. The details for the access policy.
    displayName: Optional. The description of the access policy. Must be less
      than or equal to 63 characters.
    etag: Optional. The etag for the access policy. If this is provided on
      update, it must match the server's etag.
    name: Identifier. The resource name of the access policy. The following
      formats are supported:
      `projects/{project_id}/locations/{location}/accessPolicies/{policy_id}`
      `projects/{project_number}/locations/{location}/accessPolicies/{policy_i
      d}`
      `folders/{folder_id}/locations/{location}/accessPolicies/{policy_id}` `o
      rganizations/{organization_id}/locations/{location}/accessPolicies/{poli
      cy_id}`
    uid: Output only. The globally unique ID of the access policy.
    updateTime: Output only. The time when the access policy was most recently
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User defined annotations. See
    https://google.aip.dev/148#annotations for more details such as format and
    size limitations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  details = _messages.MessageField('GoogleIamV3alphaAccessPolicyDetails', 3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  uid = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class GoogleIamV3alphaAccessPolicyDetails(_messages.Message):
  r"""Access policy details.

  Fields:
    rules: Required. A list of access policy rules.
  """

  rules = _messages.MessageField('GoogleIamV3alphaAccessPolicyRule', 1, repeated=True)


class GoogleIamV3alphaAccessPolicyRule(_messages.Message):
  r"""Access Policy Rule that determines the behavior of the policy.

  Enums:
    EffectValueValuesEnum: Required. The effect of the rule.

  Messages:
    ActivationConditionsValue: Optional. The conditions that determine whether
      this rule applies to a request. Conditions are identified by their key,
      which is the FQDN of the service that they are relevant to, e.g.:
      "activationConditions": { "iam.googleapis.com": { "cel_condition": } }
      Each rule is evaluated independently. If this rule does not apply to a
      request, other rules might still apply. Currently supported keys are: *
      `eventarc.googleapis.com`

  Fields:
    activationConditions: Optional. The conditions that determine whether this
      rule applies to a request. Conditions are identified by their key, which
      is the FQDN of the service that they are relevant to, e.g.:
      "activationConditions": { "iam.googleapis.com": { "cel_condition": } }
      Each rule is evaluated independently. If this rule does not apply to a
      request, other rules might still apply. Currently supported keys are: *
      `eventarc.googleapis.com`
    description: Optional. Customer specified description of the rule. Must be
      less than or equal to 256 characters.
    effect: Required. The effect of the rule.
    excludedPermissions: Optional. Specifies the permissions that this rule
      excludes from the set of affected permissions given by `permissions`. If
      a permission appears in `permissions` _and_ in `excluded_permissions`
      then it will _not_ be subject to the policy effect. The excluded
      permissions can be specified using the same syntax as `permissions`.
    excludedPrincipals: Optional. The identities that are excluded from the
      access policy rule, even if they are listed in the `principals`. For
      example, you could add a Google group to the `principals`, then exclude
      specific users who belong to that group.
    permissions: Required. The permissions that are explicitly affected by
      this rule. Each permission uses the format
      `{service_fqdn}/{resource}.{verb}`, where `{service_fqdn}` is the fully
      qualified domain name for the service. Currently supported permissions
      are: * `eventarc.googleapis.com/messageBuses.publish`.
    principals: Required. The identities for which this rule's effect governs
      using one or more permissions on Google Cloud resources. This field can
      contain the following values: * `principal://goog/subject/{email_id}`: A
      specific Google Account. Includes Gmail, Cloud Identity, and Google
      Workspace user accounts. For example,
      `principal://goog/subject/<EMAIL>`. * `principal://iam.googlea
      pis.com/projects/-/serviceAccounts/{service_account_id}`: A Google Cloud
      service account. For example,
      `principal://iam.googleapis.com/projects/-/serviceAccounts/my-service-
      <EMAIL>`. *
      `principalSet://goog/group/{group_id}`: A Google group. For example,
      `principalSet://goog/group/<EMAIL>`. *
      `principalSet://goog/cloudIdentityCustomerId/{customer_id}`: All of the
      principals associated with the specified Google Workspace or Cloud
      Identity customer ID. For example,
      `principalSet://goog/cloudIdentityCustomerId/C01Abc35`. If an identifier
      that was previously set on a policy is soft deleted, then calls to read
      that policy will return the identifier with a deleted prefix. Users
      cannot set identifiers with this syntax. *
      `deleted:principal://goog/subject/{email_id}?uid={uid}`: A specific
      Google Account that was deleted recently. For example,
      `deleted:principal://goog/subject/<EMAIL>?uid=**********`. If
      the Google Account is recovered, this identifier reverts to the standard
      identifier for a Google Account. *
      `deleted:principalSet://goog/group/{group_id}?uid={uid}`: A Google group
      that was deleted recently. For example,
      `deleted:principalSet://goog/group/<EMAIL>?uid=**********`.
      If the Google group is restored, this identifier reverts to the standard
      identifier for a Google group. * `deleted:principal://iam.googleapis.com
      /projects/-/serviceAccounts/{service_account_id}?uid={uid}`: A Google
      Cloud service account that was deleted recently. For example,
      `deleted:principal://iam.googleapis.com/projects/-/serviceAccounts/my-
      <EMAIL>?uid=**********`. If the service
      account is undeleted, this identifier reverts to the standard identifier
      for a service account.
  """

  class EffectValueValuesEnum(_messages.Enum):
    r"""Required. The effect of the rule.

    Values:
      EFFECT_UNSPECIFIED: The effect is unspecified.
      DENY: The policy will deny access if it evaluates to true.
      ALLOW: The policy will grant access if it evaluates to true.
    """
    EFFECT_UNSPECIFIED = 0
    DENY = 1
    ALLOW = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ActivationConditionsValue(_messages.Message):
    r"""Optional. The conditions that determine whether this rule applies to a
    request. Conditions are identified by their key, which is the FQDN of the
    service that they are relevant to, e.g.: "activationConditions": {
    "iam.googleapis.com": { "cel_condition": } } Each rule is evaluated
    independently. If this rule does not apply to a request, other rules might
    still apply. Currently supported keys are: * `eventarc.googleapis.com`

    Messages:
      AdditionalProperty: An additional property for a
        ActivationConditionsValue object.

    Fields:
      additionalProperties: Additional properties of type
        ActivationConditionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ActivationConditionsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleIamV3alphaAccessPolicyRuleActivationCondition
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleIamV3alphaAccessPolicyRuleActivationCondition', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  activationConditions = _messages.MessageField('ActivationConditionsValue', 1)
  description = _messages.StringField(2)
  effect = _messages.EnumField('EffectValueValuesEnum', 3)
  excludedPermissions = _messages.StringField(4, repeated=True)
  excludedPrincipals = _messages.StringField(5, repeated=True)
  permissions = _messages.StringField(6, repeated=True)
  principals = _messages.StringField(7, repeated=True)


class GoogleIamV3alphaAccessPolicyRuleActivationCondition(_messages.Message):
  r"""Defines an activation condition.

  Fields:
    celCondition: Required. The CEL condition that will be evaluated to
      determine rule applicability. Note that the attributes and functions
      that can be used in the condition will be limited by the namespace it is
      associated with in the `activation_conditions` map. Expr.expression must
      be less than 512 characters in length.
  """

  celCondition = _messages.MessageField('GoogleTypeExpr', 1)


class GoogleIamV3alphaAccessTrace(_messages.Message):
  r"""AccessTrace contains trace(provenance) info of the individual role
  bindings within the policies. NOTE : To be used only as part of
  TranslationReport.

  Fields:
    contributingPolicyIds: Optional. Contributing policy ids.
    mappedPermissions: Optional. Mapped permissions.
    matchedRoles: Optional. Matched roles.
    principal: Optional. Principal.
    resource: Optional. Resource.
  """

  contributingPolicyIds = _messages.StringField(1, repeated=True)
  mappedPermissions = _messages.StringField(2, repeated=True)
  matchedRoles = _messages.StringField(3, repeated=True)
  principal = _messages.StringField(4)
  resource = _messages.StringField(5)


class GoogleIamV3alphaExportTranslatedPoliciesRequest(_messages.Message):
  r"""Message for requesting export of TranslatedPolicies.

  Enums:
    InlineDestinationValueValuesEnum: Inline export of translated policy with
      format mentioned in this enum.

  Fields:
    filter: Optional. Filtering results. Filter expression would be on the
      TranslatedPolicy resource.
    inlineDestination: Inline export of translated policy with format
      mentioned in this enum.
  """

  class InlineDestinationValueValuesEnum(_messages.Enum):
    r"""Inline export of translated policy with format mentioned in this enum.

    Values:
      INLINE_DESTINATION_FORMAT_UNSPECIFIED: Default value. This is unused.
      TERRAFORM: Represents terraform format of policy.
    """
    INLINE_DESTINATION_FORMAT_UNSPECIFIED = 0
    TERRAFORM = 1

  filter = _messages.StringField(1)
  inlineDestination = _messages.EnumField('InlineDestinationValueValuesEnum', 2)


class GoogleIamV3alphaListAccessPoliciesResponse(_messages.Message):
  r"""Response message for ListAccessPolicies method.

  Fields:
    accessPolicies: The access policies from the specified parent.
    nextPageToken: Optional. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
  """

  accessPolicies = _messages.MessageField('GoogleIamV3alphaAccessPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleIamV3alphaListPolicyBindingsResponse(_messages.Message):
  r"""Response message for ListPolicyBindings method.

  Fields:
    nextPageToken: Optional. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    policyBindings: The policy bindings from the specified parent.
  """

  nextPageToken = _messages.StringField(1)
  policyBindings = _messages.MessageField('GoogleIamV3alphaPolicyBinding', 2, repeated=True)


class GoogleIamV3alphaListPolicyPortersResponse(_messages.Message):
  r"""Message for response to listing PolicyPorters

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    policyPorters: The list of PolicyPorter
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  policyPorters = _messages.MessageField('GoogleIamV3alphaPolicyPorter', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleIamV3alphaListPrincipalAccessBoundaryPoliciesResponse(_messages.Message):
  r"""Response message for ListPrincipalAccessBoundaryPolicies method.

  Fields:
    nextPageToken: Optional. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    principalAccessBoundaryPolicies: The principal access boundary policies
      from the specified parent.
  """

  nextPageToken = _messages.StringField(1)
  principalAccessBoundaryPolicies = _messages.MessageField('GoogleIamV3alphaPrincipalAccessBoundaryPolicy', 2, repeated=True)


class GoogleIamV3alphaListSourcePoliciesResponse(_messages.Message):
  r"""Message for response to listing SourcePolicies

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    sourcePolicies: The list of SourcePolicy
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  sourcePolicies = _messages.MessageField('GoogleIamV3alphaSourcePolicy', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleIamV3alphaListTranslatedPoliciesResponse(_messages.Message):
  r"""Message for response to listing TranslatedPolicies

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    translatedPolicies: The list of TranslatedPolicy
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  translatedPolicies = _messages.MessageField('GoogleIamV3alphaTranslatedPolicy', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleIamV3alphaListTranslationsResponse(_messages.Message):
  r"""Message for response to listing Translations

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    translations: The list of Translation
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  translations = _messages.MessageField('GoogleIamV3alphaTranslation', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleIamV3alphaOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleIamV3alphaPolicy(_messages.Message):
  r"""One of the policies supported by IAM V3

  Fields:
    principalAccessBoundaryPolicy: The principal access boundary kind policy
  """

  principalAccessBoundaryPolicy = _messages.MessageField('GoogleIamV3alphaPrincipalAccessBoundaryPolicy', 1)


class GoogleIamV3alphaPolicyBinding(_messages.Message):
  r"""IAM policy binding resource.

  Enums:
    PolicyKindValueValuesEnum: Immutable. The kind of the policy to attach in
      this binding. This field must be one of the following: - Left empty
      (will be automatically set to the policy kind) - The input policy kind

  Messages:
    AnnotationsValue: Optional. User-defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations

  Fields:
    annotations: Optional. User-defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations
    condition: Optional. The condition to apply to the policy binding. When
      set, the `expression` field in the `Expr` must include from 1 to 10
      subexpressions, joined by the "||"(Logical OR), "&&"(Logical AND) or
      "!"(Logical NOT) operators and cannot contain more than 250 characters.
      The condition is currently only supported when bound to policies of kind
      principal access boundary. When the bound policy is a principal access
      boundary policy, the only supported attributes in any subexpression are
      `principal.type` and `principal.subject`. An example expression is:
      "principal.type == 'iam.googleapis.com/ServiceAccount'" or
      "principal.subject == '<EMAIL>'". Allowed operations for
      `principal.subject`: - `principal.subject == ` - `principal.subject != `
      - `principal.subject in []` - `principal.subject.startsWith()` -
      `principal.subject.endsWith()` Allowed operations for `principal.type`:
      - `principal.type == ` - `principal.type != ` - `principal.type in []`
      Supported principal types are Workspace, Workforce Pool, Workload Pool
      and Service Account. Allowed string must be one of: -
      iam.googleapis.com/WorkspaceIdentity -
      iam.googleapis.com/WorkforcePoolIdentity -
      iam.googleapis.com/WorkloadPoolIdentity -
      iam.googleapis.com/ServiceAccount
    createTime: Output only. The time when the policy binding was created.
    displayName: Optional. The description of the policy binding. Must be less
      than or equal to 63 characters.
    etag: Optional. The etag for the policy binding. If this is provided on
      update, it must match the server's etag.
    name: Identifier. The name of the policy binding, in the format
      `{binding_parent/locations/{location}/policyBindings/{policy_binding_id}
      `. The binding parent is the closest Resource Manager resource (project,
      folder, or organization) to the binding target. Format: * `projects/{pro
      ject_id}/locations/{location}/policyBindings/{policy_binding_id}` * `pro
      jects/{project_number}/locations/{location}/policyBindings/{policy_bindi
      ng_id}` * `folders/{folder_id}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `organizations/{organization_id}/locations/{location}/
      policyBindings/{policy_binding_id}`
    policy: Required. Immutable. The resource name of the policy to be bound.
      The binding parent and policy must belong to the same organization.
    policyKind: Immutable. The kind of the policy to attach in this binding.
      This field must be one of the following: - Left empty (will be
      automatically set to the policy kind) - The input policy kind
    policyUid: Output only. The globally unique ID of the policy to be bound.
    target: Required. Immutable. Target is the full resource name of the
      resource to which the policy will be bound. Immutable once set.
    uid: Output only. The globally unique ID of the policy binding. Assigned
      when the policy binding is created.
    updateTime: Output only. The time when the policy binding was most
      recently updated.
  """

  class PolicyKindValueValuesEnum(_messages.Enum):
    r"""Immutable. The kind of the policy to attach in this binding. This
    field must be one of the following: - Left empty (will be automatically
    set to the policy kind) - The input policy kind

    Values:
      POLICY_KIND_UNSPECIFIED: Unspecified policy kind; Not a valid state
      PRINCIPAL_ACCESS_BOUNDARY: Principal access boundary policy kind
      ACCESS: Access policy kind. Keep behind visibility label until Access
        Policy launch.
    """
    POLICY_KIND_UNSPECIFIED = 0
    PRINCIPAL_ACCESS_BOUNDARY = 1
    ACCESS = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User-defined annotations. See
    https://google.aip.dev/148#annotations for more details such as format and
    size limitations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  condition = _messages.MessageField('GoogleTypeExpr', 2)
  createTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  policy = _messages.StringField(7)
  policyKind = _messages.EnumField('PolicyKindValueValuesEnum', 8)
  policyUid = _messages.StringField(9)
  target = _messages.MessageField('GoogleIamV3alphaPolicyBindingTarget', 10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class GoogleIamV3alphaPolicyBindingTarget(_messages.Message):
  r"""Target is the full resource name of the resource to which the policy
  will be bound. Immutable once set.

  Fields:
    principalSet: Immutable. Full Resource Name used for principal access
      boundary policy bindings. The principal set must be directly parented by
      the policy binding's parent or same as the parent if the target is a
      project/folder/organization. Examples: * For binding's parented by an
      organization: * Organization:
      `//cloudresourcemanager.googleapis.com/organizations/ORGANIZATION_ID` *
      Workforce Identity:
      `//iam.googleapis.com/locations/global/workforcePools/WORKFORCE_POOL_ID`
      * Workspace Identity:
      `//iam.googleapis.com/locations/global/workspace/WORKSPACE_ID` * For
      binding's parented by a folder: * Folder:
      `//cloudresourcemanager.googleapis.com/folders/FOLDER_ID` * For
      binding's parented by a project: * Project: *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_NUMBER` *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_ID` * Workload
      Identity Pool: `//iam.googleapis.com/projects/PROJECT_NUMBER/locations/L
      OCATION/workloadIdentityPools/WORKLOAD_POOL_ID`
    resource: Immutable. Full Resource Name used for access policy bindings
      Examples: * Organization:
      `//cloudresourcemanager.googleapis.com/organizations/ORGANIZATION_ID` *
      Folder: `//cloudresourcemanager.googleapis.com/folders/FOLDER_ID` *
      Project: *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_NUMBER` *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_ID`
  """

  principalSet = _messages.StringField(1)
  resource = _messages.StringField(2)


class GoogleIamV3alphaPolicyInaccessible(_messages.Message):
  r"""A marker to indicate that the policy is inaccessible"""


class GoogleIamV3alphaPolicyNotFound(_messages.Message):
  r"""A marker to indicate that the policy was not found"""


class GoogleIamV3alphaPolicyPorter(_messages.Message):
  r"""Policy porter resource defines a workspace where customers can upload
  security policies from different clouds and get them translated to GCP
  security policies.

  Messages:
    LabelsValue: Optional. Resource labels as key value pairs, used for List
      API filtering.

  Fields:
    createTime: Output only. [Output only] Create time stamp
    description: Optional. Description about translation.
    displayName: Required. An arbitrary user-provided name for policy porter.
      The display name should adhere to the following format: * Must be 6 to
      63 characters in length. * Can only contain lowercase letters, numbers,
      and hyphens. * Must start with a letter.
    labels: Optional. Resource labels as key value pairs, used for List API
      filtering.
    name: Identifier. Name of resource. Format:
      projects/{project}/locations/{location}/policyPorters/{policy_porter}.
    policyConfig: Optional. Represents config for PolicyPorter such as
      policies, mappings, etc. required by PolicyPorter.
    updateTime: Output only. [Output only] Update time stamp
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels as key value pairs, used for List API
    filtering.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  policyConfig = _messages.MessageField('GoogleIamV3alphaPolicyPorterConfig', 6)
  updateTime = _messages.StringField(7)


class GoogleIamV3alphaPolicyPorterConfig(_messages.Message):
  r"""Represents config for PolicyPorter such as policies, mappings, etc.
  required by PolicyPorter.

  Enums:
    SourceCloudValueValuesEnum: Optional. Type of source cloud from which
      policies are being translated.

  Fields:
    awsMappings: Optional. Mappings required for translating policies from AWS
      source cloud.
    inlinePolicyJson: Inline representation of policy json that is being
      translated.
    sourceCloud: Optional. Type of source cloud from which policies are being
      translated.
  """

  class SourceCloudValueValuesEnum(_messages.Enum):
    r"""Optional. Type of source cloud from which policies are being
    translated.

    Values:
      SOURCE_CLOUD_UNSPECIFIED: Default value. This value is unused.
      AWS: Represents AWS cloud.
    """
    SOURCE_CLOUD_UNSPECIFIED = 0
    AWS = 1

  awsMappings = _messages.MessageField('GoogleIamV3alphaPolicyPorterConfigAwsMappings', 1)
  inlinePolicyJson = _messages.MessageField('GoogleIamV3alphaPolicyPorterConfigInlinePolicyJson', 2)
  sourceCloud = _messages.EnumField('SourceCloudValueValuesEnum', 3)


class GoogleIamV3alphaPolicyPorterConfigAwsMappings(_messages.Message):
  r"""Resource and principal mappings required for translating policies.

  Messages:
    PrincipalMappingValue: Optional. User provided principal mapping for keys
      in `principal_keys` field. Keys format: [AWS JSON policy elements: Princ
      ipal](https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policie
      s_elements_principal.html). Values format: See [Members field in binding
      s](https://cloud.google.com/iam/docs/reference/rest/v1/Policy#binding).
    ResourceMappingValue: Optional. User provided resource mapping for keys in
      `resource_keys` field. Keys format: [IAM JSON policy elements: Resource]
      (https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_ele
      ments_resource.html). Values format: See [Resource
      names](https://cloud.google.com/apis/design/resource_names).

  Fields:
    principalKeys: Output only. Principal keys identified in the policy json
      for which mappings are required for translation. Mappings need to be
      provided in `principal_mapping` field.
    principalMapping: Optional. User provided principal mapping for keys in
      `principal_keys` field. Keys format: [AWS JSON policy elements: Principa
      l](https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_e
      lements_principal.html). Values format: See [Members field in bindings](
      https://cloud.google.com/iam/docs/reference/rest/v1/Policy#binding).
    resourceKeys: Output only. Resource keys identified in the policy json for
      which mappings are required for translation. Mappings need to be
      provided in `resource_mapping` field.
    resourceMapping: Optional. User provided resource mapping for keys in
      `resource_keys` field. Keys format: [IAM JSON policy elements: Resource]
      (https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_ele
      ments_resource.html). Values format: See [Resource
      names](https://cloud.google.com/apis/design/resource_names).
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PrincipalMappingValue(_messages.Message):
    r"""Optional. User provided principal mapping for keys in `principal_keys`
    field. Keys format: [AWS JSON policy elements: Principal](https://docs.aws
    .amazon.com/IAM/latest/UserGuide/reference_policies_elements_principal.htm
    l). Values format: See [Members field in bindings](https://cloud.google.co
    m/iam/docs/reference/rest/v1/Policy#binding).

    Messages:
      AdditionalProperty: An additional property for a PrincipalMappingValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        PrincipalMappingValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PrincipalMappingValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceMappingValue(_messages.Message):
    r"""Optional. User provided resource mapping for keys in `resource_keys`
    field. Keys format: [IAM JSON policy elements: Resource](https://docs.aws.
    amazon.com/IAM/latest/UserGuide/reference_policies_elements_resource.html)
    . Values format: See [Resource
    names](https://cloud.google.com/apis/design/resource_names).

    Messages:
      AdditionalProperty: An additional property for a ResourceMappingValue
        object.

    Fields:
      additionalProperties: Additional properties of type ResourceMappingValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceMappingValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  principalKeys = _messages.StringField(1, repeated=True)
  principalMapping = _messages.MessageField('PrincipalMappingValue', 2)
  resourceKeys = _messages.StringField(3, repeated=True)
  resourceMapping = _messages.MessageField('ResourceMappingValue', 4)


class GoogleIamV3alphaPolicyPorterConfigInlinePolicyJson(_messages.Message):
  r"""Inline representation of policy json that is being translated.

  Fields:
    content: Required. Content of the file containing policies in json format.
    fileName: Required. Name of file from which policy json got imported. This
      helps user to recognize the source of policies.
  """

  content = _messages.StringField(1)
  fileName = _messages.StringField(2)


class GoogleIamV3alphaPolicyTrace(_messages.Message):
  r"""TranslationTraceTable contains trace(provenance) info of the translated
  policies. NOTE : To be used only as part of TranslationReport.

  Fields:
    contributingPolicyIds: Optional. Contributing policy ids.
    translatedPolicyId: Optional. Translated policy id.
  """

  contributingPolicyIds = _messages.StringField(1, repeated=True)
  translatedPolicyId = _messages.StringField(2)


class GoogleIamV3alphaPrincipalAccessBoundaryPolicy(_messages.Message):
  r"""An IAM principal access boundary policy resource.

  Messages:
    AnnotationsValue: Optional. User defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations

  Fields:
    annotations: Optional. User defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations
    createTime: Output only. The time when the principal access boundary
      policy was created.
    details: Optional. The details for the principal access boundary policy.
    displayName: Optional. The description of the principal access boundary
      policy. Must be less than or equal to 63 characters.
    etag: Optional. The etag for the principal access boundary. If this is
      provided on update, it must match the server's etag.
    name: Identifier. The resource name of the principal access boundary
      policy. The following format is supported: `organizations/{organization_
      id}/locations/{location}/principalAccessBoundaryPolicies/{policy_id}`
    uid: Output only. The globally unique ID of the principal access boundary
      policy.
    updateTime: Output only. The time when the principal access boundary
      policy was most recently updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User defined annotations. See
    https://google.aip.dev/148#annotations for more details such as format and
    size limitations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  details = _messages.MessageField('GoogleIamV3alphaPrincipalAccessBoundaryPolicyDetails', 3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  uid = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class GoogleIamV3alphaPrincipalAccessBoundaryPolicyDetails(_messages.Message):
  r"""Principal access boundary policy details

  Fields:
    enforcementVersion: Optional. The version number (for example, `1` or
      `latest`) that indicates which permissions are able to be blocked by the
      policy. If empty, the PAB policy version will be set to the most recent
      version number at the time of the policy's creation.
    rules: Required. A list of principal access boundary policy rules. The
      number of rules in a policy is limited to 500.
  """

  enforcementVersion = _messages.StringField(1)
  rules = _messages.MessageField('GoogleIamV3alphaPrincipalAccessBoundaryPolicyRule', 2, repeated=True)


class GoogleIamV3alphaPrincipalAccessBoundaryPolicyRule(_messages.Message):
  r"""Principal access boundary policy rule that defines the resource
  boundary.

  Enums:
    EffectValueValuesEnum: Required. The access relationship of principals to
      the resources in this rule.

  Fields:
    description: Optional. The description of the principal access boundary
      policy rule. Must be less than or equal to 256 characters.
    effect: Required. The access relationship of principals to the resources
      in this rule.
    resources: Required. A list of Resource Manager resources. If a resource
      is listed in the rule, then the rule applies for that resource and its
      descendants. The number of resources in a policy is limited to 500
      across all rules in the policy. The following resource types are
      supported: * Organizations, such as
      `//cloudresourcemanager.googleapis.com/organizations/123`. * Folders,
      such as `//cloudresourcemanager.googleapis.com/folders/123`. * Projects,
      such as `//cloudresourcemanager.googleapis.com/projects/123` or
      `//cloudresourcemanager.googleapis.com/projects/my-project-id`.
  """

  class EffectValueValuesEnum(_messages.Enum):
    r"""Required. The access relationship of principals to the resources in
    this rule.

    Values:
      EFFECT_UNSPECIFIED: Effect unspecified.
      ALLOW: Allows access to the resources in this rule.
    """
    EFFECT_UNSPECIFIED = 0
    ALLOW = 1

  description = _messages.StringField(1)
  effect = _messages.EnumField('EffectValueValuesEnum', 2)
  resources = _messages.StringField(3, repeated=True)


class GoogleIamV3alphaSearchAccessPolicyBindingsResponse(_messages.Message):
  r"""Response message for SearchAccessPolicyBindings rpc.

  Fields:
    nextPageToken: Optional. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    policyBindings: The policy bindings that reference the specified policy.
  """

  nextPageToken = _messages.StringField(1)
  policyBindings = _messages.MessageField('GoogleIamV3alphaPolicyBinding', 2, repeated=True)


class GoogleIamV3alphaSearchApplicablePoliciesResponse(_messages.Message):
  r"""Response message for SearchApplicablePolicies

  Fields:
    bindingsAndPolicies: A list of Bindings and the policies associated with
      those bindings. The bindings are ordered by attachment point starting
      from the lowest level of the resource hierarchy. No order is guaranteed
      for bindings for a given enforcement point.
    nextPageToken: The page token to use in a follow up
      SearchApplicablePolicies request
    responseComplete: Does the response contain the full list of all bindings
      and policies applicable or were some excluded due to lack of permissions
  """

  bindingsAndPolicies = _messages.MessageField('GoogleIamV3alphaSearchApplicablePoliciesResponseBindingAndPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  responseComplete = _messages.BooleanField(3)


class GoogleIamV3alphaSearchApplicablePoliciesResponseBindingAndPolicy(_messages.Message):
  r"""A pair of a binding and a policy referenced by that binding (if
  accessible)

  Fields:
    binding: A binding between a target and a policy
    policy: The policy associated with the above binding. Omitted if the
      policy cannot be retrieved due to lack of permissions
    policyInaccessible: Will be set if there was a permission error getting
      the policy (even though the binding was accessible).
    policyNotFound: Will be set if there was not a permission error, but the
      policy was not found. This would indicate the binding is orphaned. Any
      orphan binding will eventually be cleaned up - this state is temporary.
  """

  binding = _messages.MessageField('GoogleIamV3alphaPolicyBinding', 1)
  policy = _messages.MessageField('GoogleIamV3alphaPolicy', 2)
  policyInaccessible = _messages.MessageField('GoogleIamV3alphaPolicyInaccessible', 3)
  policyNotFound = _messages.MessageField('GoogleIamV3alphaPolicyNotFound', 4)


class GoogleIamV3alphaSearchPrincipalAccessBoundaryPolicyBindingsResponse(_messages.Message):
  r"""Response message for SearchPrincipalAccessBoundaryPolicyBindings rpc.

  Fields:
    nextPageToken: Optional. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    policyBindings: The policy bindings that reference the specified policy.
  """

  nextPageToken = _messages.StringField(1)
  policyBindings = _messages.MessageField('GoogleIamV3alphaPolicyBinding', 2, repeated=True)


class GoogleIamV3alphaSearchTargetPolicyBindingsResponse(_messages.Message):
  r"""Response message for SearchTargetPolicyBindings method.

  Fields:
    nextPageToken: Optional. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    policyBindings: The policy bindings bound to the specified target.
  """

  nextPageToken = _messages.StringField(1)
  policyBindings = _messages.MessageField('GoogleIamV3alphaPolicyBinding', 2, repeated=True)


class GoogleIamV3alphaSourcePolicy(_messages.Message):
  r"""SourcePolicy resource represents a security policy provided by user for
  translation.

  Fields:
    awsIamPolicy: AWS IAM policy.
    createTime: Output only. [Output only] Create time stamp
    description: Optional. The description of the source policy. It explains
      the purpose of this policy.
    displayName: Required. The display name of the source policy.
    name: Identifier. Name of resource. Format: projects/{project}/locations/{
      location}/policyPorters/{policy_porter}/translations/{translation}/sourc
      ePolicies/{source_policy}.
    updateTime: Output only. [Output only] Update time stamp
  """

  awsIamPolicy = _messages.MessageField('GoogleIamV3alphaSourcePolicyAwsIamPolicy', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class GoogleIamV3alphaSourcePolicyAwsIamPolicy(_messages.Message):
  r"""Proto representation of AWS IAM policy and attachment. Please check [AWS
  - IAM JSON policy reference](https://docs.aws.amazon.com/IAM/latest/UserGuid
  e/reference_policies.html) to see the AWS IAM JSON policy reference.

  Fields:
    arn: The Amazon Resource Name (ARN). ARNs are unique identifiers for
      Amazon Web Services resources.
    attachmentInfo: Information about the attachment of the policy.
    defaultVersion: The identifier for the version of the policy that is set
      as the default version.
    id: The stable and unique string identifying the policy.
    name: The friendly name (not ARN) identifying the policy.
    path: The path for the policy.
    policyVersions: Policy versions.
  """

  arn = _messages.StringField(1)
  attachmentInfo = _messages.MessageField('GoogleIamV3alphaSourcePolicyAwsIamPolicyAttachmentInfo', 2, repeated=True)
  defaultVersion = _messages.StringField(3)
  id = _messages.StringField(4)
  name = _messages.StringField(5)
  path = _messages.StringField(6)
  policyVersions = _messages.MessageField('GoogleIamV3alphaSourcePolicyAwsIamPolicyPolicyVersion', 7, repeated=True)


class GoogleIamV3alphaSourcePolicyAwsIamPolicyAttachmentInfo(_messages.Message):
  r"""Information about the attachment of the policy.

  Fields:
    principal: Principal where policy is attached.
    resource: Resource where policy is attached.
  """

  principal = _messages.StringField(1)
  resource = _messages.StringField(2)


class GoogleIamV3alphaSourcePolicyAwsIamPolicyDocument(_messages.Message):
  r"""The policy document is the content of the policy containing policy
  rules/statements.

  Fields:
    id: The Id element specifies an optional identifier for the policy.
    statements: Statements present in a policy.
    version: The Version policy element specifies the language syntax rules
      that are to be used to process a policy.
  """

  id = _messages.StringField(1)
  statements = _messages.MessageField('GoogleIamV3alphaSourcePolicyAwsIamPolicyDocumentStatement', 2, repeated=True)
  version = _messages.StringField(3)


class GoogleIamV3alphaSourcePolicyAwsIamPolicyDocumentStatement(_messages.Message):
  r"""Defines single rule/statement in a policy.

  Fields:
    actions: The Action element describes the specific action or actions that
      will be allowed or denied.
    effect: The Effect element is required and specifies whether the statement
      results in an allow or an explicit deny.
    id: Optional id to identify a statement within a policy.
    notActions: NotAction explicitly matches everything except the specified
      list of actions.
    notPrincipal: NotPrincipal element to deny access to all principals except
      the IAM user, federated user, IAM role, AWS account, AWS service, or
      other principal specified in the NotPrincipal element.
    notResources: NotResource is an advanced policy element that explicitly
      matches every resource except those specified.
    principal: Principal element is present in a resource-based JSON policy to
      specify the principal that is allowed or denied access to a resource.
    resources: The Resource element specifies the object or objects that the
      statement covers.
  """

  actions = _messages.StringField(1, repeated=True)
  effect = _messages.StringField(2)
  id = _messages.StringField(3)
  notActions = _messages.StringField(4, repeated=True)
  notPrincipal = _messages.MessageField('GoogleIamV3alphaSourcePolicyAwsIamPolicyDocumentStatementPrincipal', 5)
  notResources = _messages.StringField(6, repeated=True)
  principal = _messages.MessageField('GoogleIamV3alphaSourcePolicyAwsIamPolicyDocumentStatementPrincipal', 7)
  resources = _messages.StringField(8, repeated=True)


class GoogleIamV3alphaSourcePolicyAwsIamPolicyDocumentStatementPrincipal(_messages.Message):
  r"""Principal represents IAM user, federated user, IAM role, AWS account,
  AWS service, or other principal.

  Fields:
    aws: AWS account identifier.
    canonicalUser: Canonical user identifier in AWS account.
    federated: A web identity session principal or SAML session principal.
    service: AWS service principal.
  """

  aws = _messages.StringField(1, repeated=True)
  canonicalUser = _messages.StringField(2, repeated=True)
  federated = _messages.StringField(3, repeated=True)
  service = _messages.StringField(4, repeated=True)


class GoogleIamV3alphaSourcePolicyAwsIamPolicyPolicyVersion(_messages.Message):
  r"""Policy version.

  Fields:
    isDefaultVersion: Boolean to identify the default version.
    policyDocument: Policy document containing the policy.
    version: Version id of the policy version.
  """

  isDefaultVersion = _messages.BooleanField(1)
  policyDocument = _messages.MessageField('GoogleIamV3alphaSourcePolicyAwsIamPolicyDocument', 2)
  version = _messages.StringField(3)


class GoogleIamV3alphaTranslatedPolicy(_messages.Message):
  r"""TranslatedPolicy resource represents an output translated policy from
  PolicyPorter. User can refine the TranslatedPolicy before applying it.

  Fields:
    createTime: Output only. [Output only] Create time stamp
    description: Optional. The description of the translated policy. It
      explains the purpose of this policy.
    displayName: Required. The display name of the translated policy.
    name: Identifier. Name of resource. Format: projects/{project}/locations/{
      location}/policyPorters/{policy_porter}/translations/{translation}/trans
      latedPolicies/{translated_policy}.
    policy: Output only. System generated policy.
    skipExport: Optional. If this flag is set to true, this policy will be
      skipped from exporting the policy.
    updateTime: Output only. [Output only] Update time stamp
    userUpdatedPolicy: Optional. User updated translated policy.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  name = _messages.StringField(4)
  policy = _messages.MessageField('GoogleIamV3alphaTranslatedPolicyPolicyWrapper', 5)
  skipExport = _messages.BooleanField(6)
  updateTime = _messages.StringField(7)
  userUpdatedPolicy = _messages.MessageField('GoogleIamV3alphaTranslatedPolicyPolicyWrapper', 8)


class GoogleIamV3alphaTranslatedPolicyGcpIamV1Policy(_messages.Message):
  r"""Represents GCP IAM V1 policy.

  Fields:
    attachmentResource: Required. Resource name to which policy binding is
      attached. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field
    binding: Required. Binding of role and members representing GCP IAM V1
      binding. See [Policy Binding Reference](https://cloud.google.com/iam/doc
      s/reference/rest/v1/Policy#Binding) binding reference.
  """

  attachmentResource = _messages.StringField(1)
  binding = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)


class GoogleIamV3alphaTranslatedPolicyPolicyWrapper(_messages.Message):
  r"""The translated policy wrapper.

  Fields:
    gcpIamV1Policy: GCP IAM V1 policy.
    sourcePolicies: Output only. Source policies which contributed to this
      translated policy.
    updateTime: Output only. Update time stamp.
  """

  gcpIamV1Policy = _messages.MessageField('GoogleIamV3alphaTranslatedPolicyGcpIamV1Policy', 1)
  sourcePolicies = _messages.StringField(2, repeated=True)
  updateTime = _messages.StringField(3)


class GoogleIamV3alphaTranslation(_messages.Message):
  r"""Translation resource represents one translation instance on the
  PolicyPorter resource.

  Enums:
    StateValueValuesEnum: Output only. State of the translation.

  Fields:
    createTime: Output only. [Output only] Create time stamp
    displayName: Required. The display name of translation.
    name: Identifier. Name of resource. Format: projects/{project}/locations/{
      location}/policyPorters/{policy_porter}/translations/{translation}.
    policyConfig: Output only. Represents config for PolicyPorter such as
      policies, mappings, etc. required by PolicyPorter.
    state: Output only. State of the translation.
    translationReport: Output only. Translation report contains insights about
      a translation.
    updateTime: Output only. [Output only] Update time stamp
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the translation.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      INITIALIZING: Represents initialization of translation.
      TRANSLATING: Represents in progress translation.
      REFINING: Represents refining of translation.
      COMPLETED: Represents completed translation.
      FAILED: Represents failed translation.
    """
    STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    TRANSLATING = 2
    REFINING = 3
    COMPLETED = 4
    FAILED = 5

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  name = _messages.StringField(3)
  policyConfig = _messages.MessageField('GoogleIamV3alphaPolicyPorterConfig', 4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  translationReport = _messages.MessageField('GoogleIamV3alphaTranslationReport', 6)
  updateTime = _messages.StringField(7)


class GoogleIamV3alphaTranslationReport(_messages.Message):
  r"""Translation report contains meta-details about translation (non-critical
  failures, provenance of a translated access etc). NOTE : To be used only as
  part of Translation resource.

  Fields:
    messages: Optional. Messages to be displayed in the translation report.
    translationTraceTable: Optional. Trace table for the translation report.
  """

  messages = _messages.StringField(1, repeated=True)
  translationTraceTable = _messages.MessageField('GoogleIamV3alphaTranslationTraceTable', 2)


class GoogleIamV3alphaTranslationTraceTable(_messages.Message):
  r"""TranslationTraceTable contains trace(provenance) info for the artifacts
  produced by translation. NOTE : To be used only as part of
  TranslationReport.

  Fields:
    accessTraces: Optional. Access traces for the translated policies.
    policyTraces: Optional. Policy traces for the translated policies.
  """

  accessTraces = _messages.MessageField('GoogleIamV3alphaAccessTrace', 1, repeated=True)
  policyTraces = _messages.MessageField('GoogleIamV3alphaPolicyTrace', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeExpr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class IamFoldersLocationsAccessPoliciesCreateRequest(_messages.Message):
  r"""A IamFoldersLocationsAccessPoliciesCreateRequest object.

  Fields:
    accessPolicyId: Required. The ID to use for the access policy, which will
      become the final component of the access policy's resource name. This
      value must start with a lowercase letter followed by up to 62 lowercase
      letters, numbers, hyphens, or dots. Pattern, /a-z{2,62}/. This value
      must be unique among all access policies with the same parent.
    googleIamV3alphaAccessPolicy: A GoogleIamV3alphaAccessPolicy resource to
      be passed as the request body.
    parent: Required. The parent resource where this access policy will be
      created. Format: `projects/{project_id}/locations/{location}`
      `projects/{project_number}/locations/{location}`
      `folders/{folder_id}/locations/{location}`
      `organizations/{organization_id}/locations/{location}`
    validateOnly: Optional. If set, validate the request and preview the
      creation, but do not actually post it.
  """

  accessPolicyId = _messages.StringField(1)
  googleIamV3alphaAccessPolicy = _messages.MessageField('GoogleIamV3alphaAccessPolicy', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class IamFoldersLocationsAccessPoliciesDeleteRequest(_messages.Message):
  r"""A IamFoldersLocationsAccessPoliciesDeleteRequest object.

  Fields:
    etag: Optional. The etag of the access policy. If this is provided, it
      must match the server's etag.
    force: Optional. If set to true, the request will force the deletion of
      the Policy even if the Policy references PolicyBindings.
    name: Required. The name of the access policy to delete. Format: `projects
      /{project_id}/locations/{location}/accessPolicies/{access_policy_id}` `p
      rojects/{project_number}/locations/{location}/accessPolicies/{access_pol
      icy_id}` `folders/{folder_id}/locations/{location}/accessPolicies/{acces
      s_policy_id}` `organizations/{organization_id}/locations/{location}/acce
      ssPolicies/{access_policy_id}`
    validateOnly: Optional. If set, validate the request and preview the
      deletion, but do not actually post it.
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class IamFoldersLocationsAccessPoliciesGetRequest(_messages.Message):
  r"""A IamFoldersLocationsAccessPoliciesGetRequest object.

  Fields:
    name: Required. The name of the access policy to retrieve. Format: `projec
      ts/{project_id}/locations/{location}/accessPolicies/{access_policy_id}`
      `projects/{project_number}/locations/{location}/accessPolicies/{access_p
      olicy_id}` `folders/{folder_id}/locations/{location}/accessPolicies/{acc
      ess_policy_id}` `organizations/{organization_id}/locations/{location}/ac
      cessPolicies/{access_policy_id}`
  """

  name = _messages.StringField(1, required=True)


class IamFoldersLocationsAccessPoliciesListRequest(_messages.Message):
  r"""A IamFoldersLocationsAccessPoliciesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of access policies to return. The
      service may return fewer than this value. If unspecified, at most 50
      access policies will be returned. Valid value ranges from 1 to 1000;
      values above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListAccessPolicies` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListAccessPolicies`
      must match the call that provided the page token.
    parent: Required. The parent resource, which owns the collection of access
      policy resources. Format: `projects/{project_id}/locations/{location}`
      `projects/{project_number}/locations/{location}`
      `folders/{folder_id}/locations/{location}`
      `organizations/{organization_id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class IamFoldersLocationsAccessPoliciesPatchRequest(_messages.Message):
  r"""A IamFoldersLocationsAccessPoliciesPatchRequest object.

  Fields:
    googleIamV3alphaAccessPolicy: A GoogleIamV3alphaAccessPolicy resource to
      be passed as the request body.
    name: Identifier. The resource name of the access policy. The following
      formats are supported:
      `projects/{project_id}/locations/{location}/accessPolicies/{policy_id}`
      `projects/{project_number}/locations/{location}/accessPolicies/{policy_i
      d}`
      `folders/{folder_id}/locations/{location}/accessPolicies/{policy_id}` `o
      rganizations/{organization_id}/locations/{location}/accessPolicies/{poli
      cy_id}`
    validateOnly: Optional. If set, validate the request and preview the
      update, but do not actually post it.
  """

  googleIamV3alphaAccessPolicy = _messages.MessageField('GoogleIamV3alphaAccessPolicy', 1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class IamFoldersLocationsAccessPoliciesSearchPolicyBindingsRequest(_messages.Message):
  r"""A IamFoldersLocationsAccessPoliciesSearchPolicyBindingsRequest object.

  Fields:
    name: Required. The name of the access policy. Format: `organizations/{org
      anization_id}/locations/{location}/accessPolicies/{access_policy_id}` `f
      olders/{folder_id}/locations/{location}/accessPolicies/{access_policy_id
      }` `projects/{project_id}/locations/{location}/accessPolicies/{access_po
      licy_id}` `projects/{project_number}/locations/{location}/accessPolicies
      /{access_policy_id}`
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchAccessPolicyBindingsRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `SearchAccessPolicyBindingsRequest` must match the call that provided
      the page token.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class IamFoldersLocationsOperationsGetRequest(_messages.Message):
  r"""A IamFoldersLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamFoldersLocationsPolicyBindingsCreateRequest(_messages.Message):
  r"""A IamFoldersLocationsPolicyBindingsCreateRequest object.

  Fields:
    googleIamV3alphaPolicyBinding: A GoogleIamV3alphaPolicyBinding resource to
      be passed as the request body.
    parent: Required. The parent resource where this policy binding will be
      created. The binding parent is the closest Resource Manager resource
      (project, folder or organization) to the binding target. Format: *
      `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
    policyBindingId: Required. The ID to use for the policy binding, which
      will become the final component of the policy binding's resource name.
      This value must start with a lowercase letter followed by up to 62
      lowercase letters, numbers, hyphens, or dots. Pattern, /a-z{2,62}/.
    validateOnly: Optional. If set, validate the request and preview the
      creation, but do not actually post it.
  """

  googleIamV3alphaPolicyBinding = _messages.MessageField('GoogleIamV3alphaPolicyBinding', 1)
  parent = _messages.StringField(2, required=True)
  policyBindingId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamFoldersLocationsPolicyBindingsDeleteRequest(_messages.Message):
  r"""A IamFoldersLocationsPolicyBindingsDeleteRequest object.

  Fields:
    etag: Optional. The etag of the policy binding. If this is provided, it
      must match the server's etag.
    name: Required. The name of the policy binding to delete. Format: * `proje
      cts/{project_id}/locations/{location}/policyBindings/{policy_binding_id}
      ` * `projects/{project_number}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `folders/{folder_id}/locations/{location}/policyBindin
      gs/{policy_binding_id}` * `organizations/{organization_id}/locations/{lo
      cation}/policyBindings/{policy_binding_id}`
    validateOnly: Optional. If set, validate the request and preview the
      deletion, but do not actually post it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class IamFoldersLocationsPolicyBindingsGetRequest(_messages.Message):
  r"""A IamFoldersLocationsPolicyBindingsGetRequest object.

  Fields:
    name: Required. The name of the policy binding to retrieve. Format: * `pro
      jects/{project_id}/locations/{location}/policyBindings/{policy_binding_i
      d}` * `projects/{project_number}/locations/{location}/policyBindings/{po
      licy_binding_id}` * `folders/{folder_id}/locations/{location}/policyBind
      ings/{policy_binding_id}` * `organizations/{organization_id}/locations/{
      location}/policyBindings/{policy_binding_id}`
  """

  name = _messages.StringField(1, required=True)


class IamFoldersLocationsPolicyBindingsListRequest(_messages.Message):
  r"""A IamFoldersLocationsPolicyBindingsListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Filter rules are case insensitive. Some eligible fields for filtering
      are: + `target` + `policy` Some examples of filter queries: *
      `target:ex*`: The binding target's name starts with "ex". *
      `target:example`: The binding target's name is `example`. *
      `policy:example`: The binding policy's name is `example`.
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListPolicyBindings` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListPolicyBindings`
      must match the call that provided the page token.
    parent: Required. The parent resource, which owns the collection of policy
      bindings. Format: * `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class IamFoldersLocationsPolicyBindingsPatchRequest(_messages.Message):
  r"""A IamFoldersLocationsPolicyBindingsPatchRequest object.

  Fields:
    googleIamV3alphaPolicyBinding: A GoogleIamV3alphaPolicyBinding resource to
      be passed as the request body.
    name: Identifier. The name of the policy binding, in the format
      `{binding_parent/locations/{location}/policyBindings/{policy_binding_id}
      `. The binding parent is the closest Resource Manager resource (project,
      folder, or organization) to the binding target. Format: * `projects/{pro
      ject_id}/locations/{location}/policyBindings/{policy_binding_id}` * `pro
      jects/{project_number}/locations/{location}/policyBindings/{policy_bindi
      ng_id}` * `folders/{folder_id}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `organizations/{organization_id}/locations/{location}/
      policyBindings/{policy_binding_id}`
    updateMask: Optional. The list of fields to update
    validateOnly: Optional. If set, validate the request and preview the
      update, but do not actually post it.
  """

  googleIamV3alphaPolicyBinding = _messages.MessageField('GoogleIamV3alphaPolicyBinding', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamFoldersLocationsPolicyBindingsSearchTargetPolicyBindingsRequest(_messages.Message):
  r"""A IamFoldersLocationsPolicyBindingsSearchTargetPolicyBindingsRequest
  object.

  Fields:
    filter: Optional. Filtering currently only supports the kind of policies
      to return, and must be in the format "policy_kind={policy_kind}". If
      String is empty, bindings bound to all kinds of policies would be
      returned. The only supported values are:
      "policy_kind=PRINCIPAL_ACCESS_BOUNDARY", "policy_kind=ACCESS"
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchTargetPolicyBindingsRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `SearchTargetPolicyBindingsRequest` must match the call that provided
      the page token.
    parent: Required. The parent resource where this search will be performed.
      This should be the nearest Resource Manager resource (project, folder,
      or organization) to the target. Format: *
      `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
    target: Required. The target resource, which is bound to the policy in the
      binding. Format: *
      `//iam.googleapis.com/locations/global/workforcePools/POOL_ID` * `//iam.
      googleapis.com/projects/PROJECT_NUMBER/locations/global/workloadIdentity
      Pools/POOL_ID` *
      `//iam.googleapis.com/locations/global/workspace/WORKSPACE_ID` *
      `//cloudresourcemanager.googleapis.com/projects/{project_number}` *
      `//cloudresourcemanager.googleapis.com/folders/{folder_id}` *
      `//cloudresourcemanager.googleapis.com/organizations/{organization_id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  target = _messages.StringField(5)


class IamOrganizationsLocationsAccessPoliciesCreateRequest(_messages.Message):
  r"""A IamOrganizationsLocationsAccessPoliciesCreateRequest object.

  Fields:
    accessPolicyId: Required. The ID to use for the access policy, which will
      become the final component of the access policy's resource name. This
      value must start with a lowercase letter followed by up to 62 lowercase
      letters, numbers, hyphens, or dots. Pattern, /a-z{2,62}/. This value
      must be unique among all access policies with the same parent.
    googleIamV3alphaAccessPolicy: A GoogleIamV3alphaAccessPolicy resource to
      be passed as the request body.
    parent: Required. The parent resource where this access policy will be
      created. Format: `projects/{project_id}/locations/{location}`
      `projects/{project_number}/locations/{location}`
      `folders/{folder_id}/locations/{location}`
      `organizations/{organization_id}/locations/{location}`
    validateOnly: Optional. If set, validate the request and preview the
      creation, but do not actually post it.
  """

  accessPolicyId = _messages.StringField(1)
  googleIamV3alphaAccessPolicy = _messages.MessageField('GoogleIamV3alphaAccessPolicy', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class IamOrganizationsLocationsAccessPoliciesDeleteRequest(_messages.Message):
  r"""A IamOrganizationsLocationsAccessPoliciesDeleteRequest object.

  Fields:
    etag: Optional. The etag of the access policy. If this is provided, it
      must match the server's etag.
    force: Optional. If set to true, the request will force the deletion of
      the Policy even if the Policy references PolicyBindings.
    name: Required. The name of the access policy to delete. Format: `projects
      /{project_id}/locations/{location}/accessPolicies/{access_policy_id}` `p
      rojects/{project_number}/locations/{location}/accessPolicies/{access_pol
      icy_id}` `folders/{folder_id}/locations/{location}/accessPolicies/{acces
      s_policy_id}` `organizations/{organization_id}/locations/{location}/acce
      ssPolicies/{access_policy_id}`
    validateOnly: Optional. If set, validate the request and preview the
      deletion, but do not actually post it.
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class IamOrganizationsLocationsAccessPoliciesGetRequest(_messages.Message):
  r"""A IamOrganizationsLocationsAccessPoliciesGetRequest object.

  Fields:
    name: Required. The name of the access policy to retrieve. Format: `projec
      ts/{project_id}/locations/{location}/accessPolicies/{access_policy_id}`
      `projects/{project_number}/locations/{location}/accessPolicies/{access_p
      olicy_id}` `folders/{folder_id}/locations/{location}/accessPolicies/{acc
      ess_policy_id}` `organizations/{organization_id}/locations/{location}/ac
      cessPolicies/{access_policy_id}`
  """

  name = _messages.StringField(1, required=True)


class IamOrganizationsLocationsAccessPoliciesListRequest(_messages.Message):
  r"""A IamOrganizationsLocationsAccessPoliciesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of access policies to return. The
      service may return fewer than this value. If unspecified, at most 50
      access policies will be returned. Valid value ranges from 1 to 1000;
      values above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListAccessPolicies` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListAccessPolicies`
      must match the call that provided the page token.
    parent: Required. The parent resource, which owns the collection of access
      policy resources. Format: `projects/{project_id}/locations/{location}`
      `projects/{project_number}/locations/{location}`
      `folders/{folder_id}/locations/{location}`
      `organizations/{organization_id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class IamOrganizationsLocationsAccessPoliciesPatchRequest(_messages.Message):
  r"""A IamOrganizationsLocationsAccessPoliciesPatchRequest object.

  Fields:
    googleIamV3alphaAccessPolicy: A GoogleIamV3alphaAccessPolicy resource to
      be passed as the request body.
    name: Identifier. The resource name of the access policy. The following
      formats are supported:
      `projects/{project_id}/locations/{location}/accessPolicies/{policy_id}`
      `projects/{project_number}/locations/{location}/accessPolicies/{policy_i
      d}`
      `folders/{folder_id}/locations/{location}/accessPolicies/{policy_id}` `o
      rganizations/{organization_id}/locations/{location}/accessPolicies/{poli
      cy_id}`
    validateOnly: Optional. If set, validate the request and preview the
      update, but do not actually post it.
  """

  googleIamV3alphaAccessPolicy = _messages.MessageField('GoogleIamV3alphaAccessPolicy', 1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class IamOrganizationsLocationsAccessPoliciesSearchPolicyBindingsRequest(_messages.Message):
  r"""A IamOrganizationsLocationsAccessPoliciesSearchPolicyBindingsRequest
  object.

  Fields:
    name: Required. The name of the access policy. Format: `organizations/{org
      anization_id}/locations/{location}/accessPolicies/{access_policy_id}` `f
      olders/{folder_id}/locations/{location}/accessPolicies/{access_policy_id
      }` `projects/{project_id}/locations/{location}/accessPolicies/{access_po
      licy_id}` `projects/{project_number}/locations/{location}/accessPolicies
      /{access_policy_id}`
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchAccessPolicyBindingsRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `SearchAccessPolicyBindingsRequest` must match the call that provided
      the page token.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class IamOrganizationsLocationsOperationsGetRequest(_messages.Message):
  r"""A IamOrganizationsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamOrganizationsLocationsPolicyBindingsCreateRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPolicyBindingsCreateRequest object.

  Fields:
    googleIamV3alphaPolicyBinding: A GoogleIamV3alphaPolicyBinding resource to
      be passed as the request body.
    parent: Required. The parent resource where this policy binding will be
      created. The binding parent is the closest Resource Manager resource
      (project, folder or organization) to the binding target. Format: *
      `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
    policyBindingId: Required. The ID to use for the policy binding, which
      will become the final component of the policy binding's resource name.
      This value must start with a lowercase letter followed by up to 62
      lowercase letters, numbers, hyphens, or dots. Pattern, /a-z{2,62}/.
    validateOnly: Optional. If set, validate the request and preview the
      creation, but do not actually post it.
  """

  googleIamV3alphaPolicyBinding = _messages.MessageField('GoogleIamV3alphaPolicyBinding', 1)
  parent = _messages.StringField(2, required=True)
  policyBindingId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamOrganizationsLocationsPolicyBindingsDeleteRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPolicyBindingsDeleteRequest object.

  Fields:
    etag: Optional. The etag of the policy binding. If this is provided, it
      must match the server's etag.
    name: Required. The name of the policy binding to delete. Format: * `proje
      cts/{project_id}/locations/{location}/policyBindings/{policy_binding_id}
      ` * `projects/{project_number}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `folders/{folder_id}/locations/{location}/policyBindin
      gs/{policy_binding_id}` * `organizations/{organization_id}/locations/{lo
      cation}/policyBindings/{policy_binding_id}`
    validateOnly: Optional. If set, validate the request and preview the
      deletion, but do not actually post it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class IamOrganizationsLocationsPolicyBindingsGetRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPolicyBindingsGetRequest object.

  Fields:
    name: Required. The name of the policy binding to retrieve. Format: * `pro
      jects/{project_id}/locations/{location}/policyBindings/{policy_binding_i
      d}` * `projects/{project_number}/locations/{location}/policyBindings/{po
      licy_binding_id}` * `folders/{folder_id}/locations/{location}/policyBind
      ings/{policy_binding_id}` * `organizations/{organization_id}/locations/{
      location}/policyBindings/{policy_binding_id}`
  """

  name = _messages.StringField(1, required=True)


class IamOrganizationsLocationsPolicyBindingsListRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPolicyBindingsListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Filter rules are case insensitive. Some eligible fields for filtering
      are: + `target` + `policy` Some examples of filter queries: *
      `target:ex*`: The binding target's name starts with "ex". *
      `target:example`: The binding target's name is `example`. *
      `policy:example`: The binding policy's name is `example`.
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListPolicyBindings` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListPolicyBindings`
      must match the call that provided the page token.
    parent: Required. The parent resource, which owns the collection of policy
      bindings. Format: * `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class IamOrganizationsLocationsPolicyBindingsPatchRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPolicyBindingsPatchRequest object.

  Fields:
    googleIamV3alphaPolicyBinding: A GoogleIamV3alphaPolicyBinding resource to
      be passed as the request body.
    name: Identifier. The name of the policy binding, in the format
      `{binding_parent/locations/{location}/policyBindings/{policy_binding_id}
      `. The binding parent is the closest Resource Manager resource (project,
      folder, or organization) to the binding target. Format: * `projects/{pro
      ject_id}/locations/{location}/policyBindings/{policy_binding_id}` * `pro
      jects/{project_number}/locations/{location}/policyBindings/{policy_bindi
      ng_id}` * `folders/{folder_id}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `organizations/{organization_id}/locations/{location}/
      policyBindings/{policy_binding_id}`
    updateMask: Optional. The list of fields to update
    validateOnly: Optional. If set, validate the request and preview the
      update, but do not actually post it.
  """

  googleIamV3alphaPolicyBinding = _messages.MessageField('GoogleIamV3alphaPolicyBinding', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamOrganizationsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest(_messages.Message):
  r"""A
  IamOrganizationsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest
  object.

  Fields:
    filter: Optional. Filtering currently only supports the kind of policies
      to return, and must be in the format "policy_kind={policy_kind}". If
      String is empty, bindings bound to all kinds of policies would be
      returned. The only supported values are:
      "policy_kind=PRINCIPAL_ACCESS_BOUNDARY", "policy_kind=ACCESS"
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchTargetPolicyBindingsRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `SearchTargetPolicyBindingsRequest` must match the call that provided
      the page token.
    parent: Required. The parent resource where this search will be performed.
      This should be the nearest Resource Manager resource (project, folder,
      or organization) to the target. Format: *
      `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
    target: Required. The target resource, which is bound to the policy in the
      binding. Format: *
      `//iam.googleapis.com/locations/global/workforcePools/POOL_ID` * `//iam.
      googleapis.com/projects/PROJECT_NUMBER/locations/global/workloadIdentity
      Pools/POOL_ID` *
      `//iam.googleapis.com/locations/global/workspace/WORKSPACE_ID` *
      `//cloudresourcemanager.googleapis.com/projects/{project_number}` *
      `//cloudresourcemanager.googleapis.com/folders/{folder_id}` *
      `//cloudresourcemanager.googleapis.com/organizations/{organization_id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  target = _messages.StringField(5)


class IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesCreateRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesCreateRequest
  object.

  Fields:
    googleIamV3alphaPrincipalAccessBoundaryPolicy: A
      GoogleIamV3alphaPrincipalAccessBoundaryPolicy resource to be passed as
      the request body.
    parent: Required. The parent resource where this principal access boundary
      policy will be created. Only organizations are supported. Format:
      `organizations/{organization_id}/locations/{location}`
    principalAccessBoundaryPolicyId: Required. The ID to use for the principal
      access boundary policy, which will become the final component of the
      principal access boundary policy's resource name. This value must start
      with a lowercase letter followed by up to 62 lowercase letters, numbers,
      hyphens, or dots. Pattern, /a-z{2,62}/.
    validateOnly: Optional. If set, validate the request and preview the
      creation, but do not actually post it.
  """

  googleIamV3alphaPrincipalAccessBoundaryPolicy = _messages.MessageField('GoogleIamV3alphaPrincipalAccessBoundaryPolicy', 1)
  parent = _messages.StringField(2, required=True)
  principalAccessBoundaryPolicyId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesDeleteRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesDeleteRequest
  object.

  Fields:
    etag: Optional. The etag of the principal access boundary policy. If this
      is provided, it must match the server's etag.
    force: Optional. If set to true, the request will force the deletion of
      the policy even if the policy is referenced in policy bindings.
    name: Required. The name of the principal access boundary policy to
      delete. Format: `organizations/{organization_id}/locations/{location}/pr
      incipalAccessBoundaryPolicies/{principal_access_boundary_policy_id}`
    validateOnly: Optional. If set, validate the request and preview the
      deletion, but do not actually post it.
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesGetRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesGetRequest
  object.

  Fields:
    name: Required. The name of the principal access boundary policy to
      retrieve. Format: `organizations/{organization_id}/locations/{location}/
      principalAccessBoundaryPolicies/{principal_access_boundary_policy_id}`
  """

  name = _messages.StringField(1, required=True)


class IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesListRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of principal access boundary
      policies to return. The service may return fewer than this value. If
      unspecified, at most 50 principal access boundary policies will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListPrincipalAccessBoundaryPolicies` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListPrincipalAccessBoundaryPolicies` must match the call that provided
      the page token.
    parent: Required. The parent resource, which owns the collection of
      principal access boundary policies. Format:
      `organizations/{organization_id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesPatchRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesPatchRequest
  object.

  Fields:
    googleIamV3alphaPrincipalAccessBoundaryPolicy: A
      GoogleIamV3alphaPrincipalAccessBoundaryPolicy resource to be passed as
      the request body.
    name: Identifier. The resource name of the principal access boundary
      policy. The following format is supported: `organizations/{organization_
      id}/locations/{location}/principalAccessBoundaryPolicies/{policy_id}`
    updateMask: Optional. The list of fields to update
    validateOnly: Optional. If set, validate the request and preview the
      update, but do not actually post it.
  """

  googleIamV3alphaPrincipalAccessBoundaryPolicy = _messages.MessageField('GoogleIamV3alphaPrincipalAccessBoundaryPolicy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesSearchPolicyBindingsRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesSearchPolicyBi
  ndingsRequest object.

  Fields:
    name: Required. The name of the principal access boundary policy. Format:
      `organizations/{organization_id}/locations/{location}/principalAccessBou
      ndaryPolicies/{principal_access_boundary_policy_id}`
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchPrincipalAccessBoundaryPolicyBindingsRequest` call. Provide this
      to retrieve the subsequent page. When paginating, all other parameters
      provided to `SearchPrincipalAccessBoundaryPolicyBindingsRequest` must
      match the call that provided the page token.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class IamProjectsLocationsAccessPoliciesCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsAccessPoliciesCreateRequest object.

  Fields:
    accessPolicyId: Required. The ID to use for the access policy, which will
      become the final component of the access policy's resource name. This
      value must start with a lowercase letter followed by up to 62 lowercase
      letters, numbers, hyphens, or dots. Pattern, /a-z{2,62}/. This value
      must be unique among all access policies with the same parent.
    googleIamV3alphaAccessPolicy: A GoogleIamV3alphaAccessPolicy resource to
      be passed as the request body.
    parent: Required. The parent resource where this access policy will be
      created. Format: `projects/{project_id}/locations/{location}`
      `projects/{project_number}/locations/{location}`
      `folders/{folder_id}/locations/{location}`
      `organizations/{organization_id}/locations/{location}`
    validateOnly: Optional. If set, validate the request and preview the
      creation, but do not actually post it.
  """

  accessPolicyId = _messages.StringField(1)
  googleIamV3alphaAccessPolicy = _messages.MessageField('GoogleIamV3alphaAccessPolicy', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class IamProjectsLocationsAccessPoliciesDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsAccessPoliciesDeleteRequest object.

  Fields:
    etag: Optional. The etag of the access policy. If this is provided, it
      must match the server's etag.
    force: Optional. If set to true, the request will force the deletion of
      the Policy even if the Policy references PolicyBindings.
    name: Required. The name of the access policy to delete. Format: `projects
      /{project_id}/locations/{location}/accessPolicies/{access_policy_id}` `p
      rojects/{project_number}/locations/{location}/accessPolicies/{access_pol
      icy_id}` `folders/{folder_id}/locations/{location}/accessPolicies/{acces
      s_policy_id}` `organizations/{organization_id}/locations/{location}/acce
      ssPolicies/{access_policy_id}`
    validateOnly: Optional. If set, validate the request and preview the
      deletion, but do not actually post it.
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class IamProjectsLocationsAccessPoliciesGetRequest(_messages.Message):
  r"""A IamProjectsLocationsAccessPoliciesGetRequest object.

  Fields:
    name: Required. The name of the access policy to retrieve. Format: `projec
      ts/{project_id}/locations/{location}/accessPolicies/{access_policy_id}`
      `projects/{project_number}/locations/{location}/accessPolicies/{access_p
      olicy_id}` `folders/{folder_id}/locations/{location}/accessPolicies/{acc
      ess_policy_id}` `organizations/{organization_id}/locations/{location}/ac
      cessPolicies/{access_policy_id}`
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsAccessPoliciesListRequest(_messages.Message):
  r"""A IamProjectsLocationsAccessPoliciesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of access policies to return. The
      service may return fewer than this value. If unspecified, at most 50
      access policies will be returned. Valid value ranges from 1 to 1000;
      values above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListAccessPolicies` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListAccessPolicies`
      must match the call that provided the page token.
    parent: Required. The parent resource, which owns the collection of access
      policy resources. Format: `projects/{project_id}/locations/{location}`
      `projects/{project_number}/locations/{location}`
      `folders/{folder_id}/locations/{location}`
      `organizations/{organization_id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class IamProjectsLocationsAccessPoliciesPatchRequest(_messages.Message):
  r"""A IamProjectsLocationsAccessPoliciesPatchRequest object.

  Fields:
    googleIamV3alphaAccessPolicy: A GoogleIamV3alphaAccessPolicy resource to
      be passed as the request body.
    name: Identifier. The resource name of the access policy. The following
      formats are supported:
      `projects/{project_id}/locations/{location}/accessPolicies/{policy_id}`
      `projects/{project_number}/locations/{location}/accessPolicies/{policy_i
      d}`
      `folders/{folder_id}/locations/{location}/accessPolicies/{policy_id}` `o
      rganizations/{organization_id}/locations/{location}/accessPolicies/{poli
      cy_id}`
    validateOnly: Optional. If set, validate the request and preview the
      update, but do not actually post it.
  """

  googleIamV3alphaAccessPolicy = _messages.MessageField('GoogleIamV3alphaAccessPolicy', 1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class IamProjectsLocationsAccessPoliciesSearchPolicyBindingsRequest(_messages.Message):
  r"""A IamProjectsLocationsAccessPoliciesSearchPolicyBindingsRequest object.

  Fields:
    name: Required. The name of the access policy. Format: `organizations/{org
      anization_id}/locations/{location}/accessPolicies/{access_policy_id}` `f
      olders/{folder_id}/locations/{location}/accessPolicies/{access_policy_id
      }` `projects/{project_id}/locations/{location}/accessPolicies/{access_po
      licy_id}` `projects/{project_number}/locations/{location}/accessPolicies
      /{access_policy_id}`
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchAccessPolicyBindingsRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `SearchAccessPolicyBindingsRequest` must match the call that provided
      the page token.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class IamProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A IamProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsPolicyBindingsCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyBindingsCreateRequest object.

  Fields:
    googleIamV3alphaPolicyBinding: A GoogleIamV3alphaPolicyBinding resource to
      be passed as the request body.
    parent: Required. The parent resource where this policy binding will be
      created. The binding parent is the closest Resource Manager resource
      (project, folder or organization) to the binding target. Format: *
      `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
    policyBindingId: Required. The ID to use for the policy binding, which
      will become the final component of the policy binding's resource name.
      This value must start with a lowercase letter followed by up to 62
      lowercase letters, numbers, hyphens, or dots. Pattern, /a-z{2,62}/.
    validateOnly: Optional. If set, validate the request and preview the
      creation, but do not actually post it.
  """

  googleIamV3alphaPolicyBinding = _messages.MessageField('GoogleIamV3alphaPolicyBinding', 1)
  parent = _messages.StringField(2, required=True)
  policyBindingId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamProjectsLocationsPolicyBindingsDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyBindingsDeleteRequest object.

  Fields:
    etag: Optional. The etag of the policy binding. If this is provided, it
      must match the server's etag.
    name: Required. The name of the policy binding to delete. Format: * `proje
      cts/{project_id}/locations/{location}/policyBindings/{policy_binding_id}
      ` * `projects/{project_number}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `folders/{folder_id}/locations/{location}/policyBindin
      gs/{policy_binding_id}` * `organizations/{organization_id}/locations/{lo
      cation}/policyBindings/{policy_binding_id}`
    validateOnly: Optional. If set, validate the request and preview the
      deletion, but do not actually post it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class IamProjectsLocationsPolicyBindingsGetRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyBindingsGetRequest object.

  Fields:
    name: Required. The name of the policy binding to retrieve. Format: * `pro
      jects/{project_id}/locations/{location}/policyBindings/{policy_binding_i
      d}` * `projects/{project_number}/locations/{location}/policyBindings/{po
      licy_binding_id}` * `folders/{folder_id}/locations/{location}/policyBind
      ings/{policy_binding_id}` * `organizations/{organization_id}/locations/{
      location}/policyBindings/{policy_binding_id}`
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsPolicyBindingsListRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyBindingsListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Filter rules are case insensitive. Some eligible fields for filtering
      are: + `target` + `policy` Some examples of filter queries: *
      `target:ex*`: The binding target's name starts with "ex". *
      `target:example`: The binding target's name is `example`. *
      `policy:example`: The binding policy's name is `example`.
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListPolicyBindings` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListPolicyBindings`
      must match the call that provided the page token.
    parent: Required. The parent resource, which owns the collection of policy
      bindings. Format: * `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class IamProjectsLocationsPolicyBindingsPatchRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyBindingsPatchRequest object.

  Fields:
    googleIamV3alphaPolicyBinding: A GoogleIamV3alphaPolicyBinding resource to
      be passed as the request body.
    name: Identifier. The name of the policy binding, in the format
      `{binding_parent/locations/{location}/policyBindings/{policy_binding_id}
      `. The binding parent is the closest Resource Manager resource (project,
      folder, or organization) to the binding target. Format: * `projects/{pro
      ject_id}/locations/{location}/policyBindings/{policy_binding_id}` * `pro
      jects/{project_number}/locations/{location}/policyBindings/{policy_bindi
      ng_id}` * `folders/{folder_id}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `organizations/{organization_id}/locations/{location}/
      policyBindings/{policy_binding_id}`
    updateMask: Optional. The list of fields to update
    validateOnly: Optional. If set, validate the request and preview the
      update, but do not actually post it.
  """

  googleIamV3alphaPolicyBinding = _messages.MessageField('GoogleIamV3alphaPolicyBinding', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamProjectsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest
  object.

  Fields:
    filter: Optional. Filtering currently only supports the kind of policies
      to return, and must be in the format "policy_kind={policy_kind}". If
      String is empty, bindings bound to all kinds of policies would be
      returned. The only supported values are:
      "policy_kind=PRINCIPAL_ACCESS_BOUNDARY", "policy_kind=ACCESS"
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchTargetPolicyBindingsRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `SearchTargetPolicyBindingsRequest` must match the call that provided
      the page token.
    parent: Required. The parent resource where this search will be performed.
      This should be the nearest Resource Manager resource (project, folder,
      or organization) to the target. Format: *
      `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
    target: Required. The target resource, which is bound to the policy in the
      binding. Format: *
      `//iam.googleapis.com/locations/global/workforcePools/POOL_ID` * `//iam.
      googleapis.com/projects/PROJECT_NUMBER/locations/global/workloadIdentity
      Pools/POOL_ID` *
      `//iam.googleapis.com/locations/global/workspace/WORKSPACE_ID` *
      `//cloudresourcemanager.googleapis.com/projects/{project_number}` *
      `//cloudresourcemanager.googleapis.com/folders/{folder_id}` *
      `//cloudresourcemanager.googleapis.com/organizations/{organization_id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  target = _messages.StringField(5)


class IamProjectsLocationsPolicyPortersCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyPortersCreateRequest object.

  Fields:
    googleIamV3alphaPolicyPorter: A GoogleIamV3alphaPolicyPorter resource to
      be passed as the request body.
    parent: Required. Value for parent. Format:
      projects/{project}/locations/{location}.
    policyPorterId: Required. Id of the requesting object If auto-generating
      Id server-side, remove this field and policy_porter_id from the
      method_signature of Create RPC
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  googleIamV3alphaPolicyPorter = _messages.MessageField('GoogleIamV3alphaPolicyPorter', 1)
  parent = _messages.StringField(2, required=True)
  policyPorterId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class IamProjectsLocationsPolicyPortersDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyPortersDeleteRequest object.

  Fields:
    force: Optional. If set to true, any Translations from this PolicyPorter
      will also be deleted. (Otherwise, the request will only work if the
      PolicyPorter has no Translations.)
    name: Required. Name of the resource. Format:
      projects/{project}/locations/{location}/policyPorters/{policy_porter}.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class IamProjectsLocationsPolicyPortersGetRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyPortersGetRequest object.

  Fields:
    name: Required. Name of the resource. Format:
      projects/{project}/locations/{location}/policyPorters/{policy_porter}.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsPolicyPortersListRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyPortersListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListPolicyPortersRequest. Format:
      projects/{project}/locations/{location}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class IamProjectsLocationsPolicyPortersPatchRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyPortersPatchRequest object.

  Fields:
    googleIamV3alphaPolicyPorter: A GoogleIamV3alphaPolicyPorter resource to
      be passed as the request body.
    name: Identifier. Name of resource. Format:
      projects/{project}/locations/{location}/policyPorters/{policy_porter}.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the PolicyPorter resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  googleIamV3alphaPolicyPorter = _messages.MessageField('GoogleIamV3alphaPolicyPorter', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class IamProjectsLocationsPolicyPortersTranslationsCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyPortersTranslationsCreateRequest object.

  Fields:
    googleIamV3alphaTranslation: A GoogleIamV3alphaTranslation resource to be
      passed as the request body.
    parent: Required. Value for parent. Format:
      projects/{project}/locations/{location}/policyPorters/{policy_porter}.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    translationId: Required. Id of the requesting object If auto-generating Id
      server-side, remove this field and translation_id from the
      method_signature of Create RPC
  """

  googleIamV3alphaTranslation = _messages.MessageField('GoogleIamV3alphaTranslation', 1)
  parent = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  translationId = _messages.StringField(4)


class IamProjectsLocationsPolicyPortersTranslationsDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyPortersTranslationsDeleteRequest object.

  Fields:
    force: Optional. If set to true, any SourcePolicies and TranslatedPolicies
      from this Translation will also be deleted. (Otherwise, the request will
      only work if the Translation has no SourcePolicies and
      TranslatedPolicies.)
    name: Required. Name of the resource Format: projects/{project}/locations/
      {location}/policyPorters/{policy_porter}/translations/{translation}.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class IamProjectsLocationsPolicyPortersTranslationsGetRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyPortersTranslationsGetRequest object.

  Fields:
    name: Required. Name of the resource. Format: projects/{project}/locations
      /{location}/policyPorters/{policy_porter}/translations/{translation}.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsPolicyPortersTranslationsListRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyPortersTranslationsListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListTranslationsRequest. Format:
      projects/{project}/locations/{location}/policyPorters/{policy_porter}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class IamProjectsLocationsPolicyPortersTranslationsSourcePoliciesGetRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyPortersTranslationsSourcePoliciesGetRequest
  object.

  Fields:
    name: Required. Name of the resource. Format: projects/{project}/locations
      /{location}/policyPorters/{policy_porter}/translations/{translation}/sou
      rcePolicies/{source_policy}.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsPolicyPortersTranslationsSourcePoliciesListRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyPortersTranslationsSourcePoliciesListRequest
  object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListSourcePoliciesRequest. Format: proj
      ects/{project}/locations/{location}/policyPorters/{policy_porter}/transl
      ations/{translation}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesExportRequest(_messages.Message):
  r"""A
  IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesExportRequest
  object.

  Fields:
    googleIamV3alphaExportTranslatedPoliciesRequest: A
      GoogleIamV3alphaExportTranslatedPoliciesRequest resource to be passed as
      the request body.
    parent: Required. Parent value for ExportTranslatedPoliciesRequest.
      Format: projects/{project}/locations/{location}/policyPorters/{policy_po
      rter}/translations/{translation}.
  """

  googleIamV3alphaExportTranslatedPoliciesRequest = _messages.MessageField('GoogleIamV3alphaExportTranslatedPoliciesRequest', 1)
  parent = _messages.StringField(2, required=True)


class IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesGetRequest(_messages.Message):
  r"""A
  IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesGetRequest
  object.

  Fields:
    name: Required. Name of the resource. Format: projects/{project}/locations
      /{location}/policyPorters/{policy_porter}/translations/{translation}/tra
      nslatedPolicies/{translated_policy}.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesListRequest(_messages.Message):
  r"""A
  IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesListRequest
  object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListTranslatedPoliciesRequest. Format:
      projects/{project}/locations/{location}/policyPorters/{policy_porter}/tr
      anslations/{translation}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesPatchRequest(_messages.Message):
  r"""A
  IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesPatchRequest
  object.

  Fields:
    googleIamV3alphaTranslatedPolicy: A GoogleIamV3alphaTranslatedPolicy
      resource to be passed as the request body.
    name: Identifier. Name of resource. Format: projects/{project}/locations/{
      location}/policyPorters/{policy_porter}/translations/{translation}/trans
      latedPolicies/{translated_policy}.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the TranslatedPolicy resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  googleIamV3alphaTranslatedPolicy = _messages.MessageField('GoogleIamV3alphaTranslatedPolicy', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class IamSearchApplicablePoliciesSearchRequest(_messages.Message):
  r"""A IamSearchApplicablePoliciesSearchRequest object.

  Fields:
    filter: Optional. Filtering currently only supports the kind of policies
      to return, and must be in the format "kind:[policyKind1] OR
      kind:[policyKind2]". New policy kinds may be added in the future without
      notice. Example value: "kind:principalAccessBoundaryPolicies"
    pageSize: Optional. The limit of number of items (binding+policy pairs) to
      return. The default and maximum is 100 and values above 100 are
      truncated to 100.
    pageToken: Optional. A page token, received from a previous
      `SearchApplicablePolicies` call.
    targetQuery: Required. The target for which to list the policies and
      bindings for. Binding conditions will not be evaluated and all bindings
      that are bound to the target will be returned. All targets from the
      PolicyBinding are supported, as well as principals that are part of the
      principalSet. For example: *
      `//cloudresourcemanager.googleapis.com/organizations/ORGANIZATION_ID` *
      `//cloudresourcemanager.googleapis.com/folders/FOLDER_ID` *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_NUMBER` *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_ID` * `//iam.goo
      gleapis.com/projects/PROJECT_NUMBER/locations/LOCATION/workloadIdentityP
      ools/WORKLOAD_POOL_ID` *
      `//iam.googleapis.com/locations/global/workforcePools/WORKFORCE_POOL_ID`
      * `//iam.googleapis.com/locations/global/workspace/WORKSPACE_ID` *
      `principal:<EMAIL>` *
      `principal://iam.googleapis.com/locations/global/workforcePools/pool-
      id/subject/alice` * `principal://iam.googleapis.com/projects/123/locatio
      ns/global/workloadIdentityPools/pool-id/subject/alice` *
      `serviceAccount:<EMAIL>` *
      `user:<EMAIL>` * `<EMAIL>`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  targetQuery = _messages.StringField(4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
