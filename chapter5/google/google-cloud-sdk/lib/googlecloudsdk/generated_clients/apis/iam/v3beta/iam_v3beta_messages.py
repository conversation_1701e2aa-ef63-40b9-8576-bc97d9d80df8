"""Generated message classes for iam version v3beta.

Manages identity and access control for Google Cloud resources, including the
creation of service accounts, which you can use to authenticate to Google and
make API calls. Enabling this API also enables the IAM Service Account
Credentials API (iamcredentials.googleapis.com). However, disabling this API
doesn't disable the IAM Service Account Credentials API.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'iam'


class GoogleIamAdminV1AuditData(_messages.Message):
  r"""Audit log information specific to Cloud IAM admin APIs. This message is
  serialized as an `Any` type in the `ServiceData` message of an `AuditLog`
  message.

  Fields:
    permissionDelta: The permission_delta when when creating or updating a
      Role.
  """

  permissionDelta = _messages.MessageField('GoogleIamAdminV1AuditDataPermissionDelta', 1)


class GoogleIamAdminV1AuditDataPermissionDelta(_messages.Message):
  r"""A PermissionDelta message to record the added_permissions and
  removed_permissions inside a role.

  Fields:
    addedPermissions: Added permissions.
    removedPermissions: Removed permissions.
  """

  addedPermissions = _messages.StringField(1, repeated=True)
  removedPermissions = _messages.StringField(2, repeated=True)


class GoogleIamV3betaAccessPolicy(_messages.Message):
  r"""An IAM access policy resource.

  Messages:
    AnnotationsValue: Optional. User defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations

  Fields:
    annotations: Optional. User defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations
    createTime: Output only. The time when the access policy was created.
    details: Optional. The details for the access policy.
    displayName: Optional. The description of the access policy. Must be less
      than or equal to 63 characters.
    etag: Optional. The etag for the access policy. If this is provided on
      update, it must match the server's etag.
    name: Identifier. The resource name of the access policy. The following
      formats are supported:
      `projects/{project_id}/locations/{location}/accessPolicies/{policy_id}`
      `projects/{project_number}/locations/{location}/accessPolicies/{policy_i
      d}`
      `folders/{folder_id}/locations/{location}/accessPolicies/{policy_id}` `o
      rganizations/{organization_id}/locations/{location}/accessPolicies/{poli
      cy_id}`
    uid: Output only. The globally unique ID of the access policy.
    updateTime: Output only. The time when the access policy was most recently
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User defined annotations. See
    https://google.aip.dev/148#annotations for more details such as format and
    size limitations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  details = _messages.MessageField('GoogleIamV3betaAccessPolicyDetails', 3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  uid = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class GoogleIamV3betaAccessPolicyDetails(_messages.Message):
  r"""Access policy details.

  Fields:
    rules: Required. A list of access policy rules.
  """

  rules = _messages.MessageField('GoogleIamV3betaAccessPolicyRule', 1, repeated=True)


class GoogleIamV3betaAccessPolicyRule(_messages.Message):
  r"""Access Policy Rule that determines the behavior of the policy.

  Enums:
    EffectValueValuesEnum: Required. The effect of the rule.

  Messages:
    ConditionsValue: Optional. The conditions that determine whether this rule
      applies to a request. Conditions are identified by their key, which is
      the FQDN of the service that they are relevant to, e.g.: "conditions": {
      "iam.googleapis.com": } Each rule is evaluated independently. If this
      rule does not apply to a request, other rules might still apply.
      Currently supported keys are: * `eventarc.googleapis.com// The
      conditions that determine whether this rule applies to a request.
      Conditions are identified by their key, which is the FQDN of the service
      that they are relevant to, e.g.: "conditions": { "iam.googleapis.com": {
      "cel_condition": } } Each rule is evaluated independently. If this rule
      does not apply to a request, other rules might still apply. Currently
      supported keys are: * `eventarc.googleapis.com` * `iam.googleapis.com`

  Fields:
    conditions: Optional. The conditions that determine whether this rule
      applies to a request. Conditions are identified by their key, which is
      the FQDN of the service that they are relevant to, e.g.: "conditions": {
      "iam.googleapis.com": } Each rule is evaluated independently. If this
      rule does not apply to a request, other rules might still apply.
      Currently supported keys are: * `eventarc.googleapis.com// The
      conditions that determine whether this rule applies to a request.
      Conditions are identified by their key, which is the FQDN of the service
      that they are relevant to, e.g.: "conditions": { "iam.googleapis.com": {
      "cel_condition": } } Each rule is evaluated independently. If this rule
      does not apply to a request, other rules might still apply. Currently
      supported keys are: * `eventarc.googleapis.com` * `iam.googleapis.com`
    description: Optional. Customer specified description of the rule. Must be
      less than or equal to 256 characters.
    effect: Required. The effect of the rule.
    excludedPrincipals: Optional. The identities that are excluded from the
      access policy rule, even if they are listed in the `principals`. For
      example, you could add a Google group to the `principals`, then exclude
      specific users who belong to that group.
    operation: Required. Attributes that are used to determine whether this
      rule applies to a request.
    principals: Required. The identities for which this rule's effect governs
      using one or more permissions on Google Cloud resources. This field can
      contain the following values: * `principal://goog/subject/{email_id}`: A
      specific Google Account. Includes Gmail, Cloud Identity, and Google
      Workspace user accounts. For example,
      `principal://goog/subject/<EMAIL>`. * `principal://iam.googlea
      pis.com/projects/-/serviceAccounts/{service_account_id}`: A Google Cloud
      service account. For example,
      `principal://iam.googleapis.com/projects/-/serviceAccounts/my-service-
      <EMAIL>`. *
      `principalSet://goog/group/{group_id}`: A Google group. For example,
      `principalSet://goog/group/<EMAIL>`. *
      `principalSet://goog/cloudIdentityCustomerId/{customer_id}`: All of the
      principals associated with the specified Google Workspace or Cloud
      Identity customer ID. For example,
      `principalSet://goog/cloudIdentityCustomerId/C01Abc35`. If an identifier
      that was previously set on a policy is soft deleted, then calls to read
      that policy will return the identifier with a deleted prefix. Users
      cannot set identifiers with this syntax. *
      `deleted:principal://goog/subject/{email_id}?uid={uid}`: A specific
      Google Account that was deleted recently. For example,
      `deleted:principal://goog/subject/<EMAIL>?uid=**********`. If
      the Google Account is recovered, this identifier reverts to the standard
      identifier for a Google Account. *
      `deleted:principalSet://goog/group/{group_id}?uid={uid}`: A Google group
      that was deleted recently. For example,
      `deleted:principalSet://goog/group/<EMAIL>?uid=**********`.
      If the Google group is restored, this identifier reverts to the standard
      identifier for a Google group. * `deleted:principal://iam.googleapis.com
      /projects/-/serviceAccounts/{service_account_id}?uid={uid}`: A Google
      Cloud service account that was deleted recently. For example,
      `deleted:principal://iam.googleapis.com/projects/-/serviceAccounts/my-
      <EMAIL>?uid=**********`. If the service
      account is undeleted, this identifier reverts to the standard identifier
      for a service account.
  """

  class EffectValueValuesEnum(_messages.Enum):
    r"""Required. The effect of the rule.

    Values:
      EFFECT_UNSPECIFIED: The effect is unspecified.
      DENY: The policy will deny access if it evaluates to true.
      ALLOW: The policy will grant access if it evaluates to true.
    """
    EFFECT_UNSPECIFIED = 0
    DENY = 1
    ALLOW = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConditionsValue(_messages.Message):
    r"""Optional. The conditions that determine whether this rule applies to a
    request. Conditions are identified by their key, which is the FQDN of the
    service that they are relevant to, e.g.: "conditions": {
    "iam.googleapis.com": } Each rule is evaluated independently. If this rule
    does not apply to a request, other rules might still apply. Currently
    supported keys are: * `eventarc.googleapis.com// The conditions that
    determine whether this rule applies to a request. Conditions are
    identified by their key, which is the FQDN of the service that they are
    relevant to, e.g.: "conditions": { "iam.googleapis.com": {
    "cel_condition": } } Each rule is evaluated independently. If this rule
    does not apply to a request, other rules might still apply. Currently
    supported keys are: * `eventarc.googleapis.com` * `iam.googleapis.com`

    Messages:
      AdditionalProperty: An additional property for a ConditionsValue object.

    Fields:
      additionalProperties: Additional properties of type ConditionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConditionsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleTypeExpr attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleTypeExpr', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  conditions = _messages.MessageField('ConditionsValue', 1)
  description = _messages.StringField(2)
  effect = _messages.EnumField('EffectValueValuesEnum', 3)
  excludedPrincipals = _messages.StringField(4, repeated=True)
  operation = _messages.MessageField('GoogleIamV3betaAccessPolicyRuleOperation', 5)
  principals = _messages.StringField(6, repeated=True)


class GoogleIamV3betaAccessPolicyRuleOperation(_messages.Message):
  r"""Attributes that are used to determine whether this rule applies to a
  request.

  Fields:
    excludedPermissions: Optional. Specifies the permissions that this rule
      excludes from the set of affected permissions given by `permissions`. If
      a permission appears in `permissions` _and_ in `excluded_permissions`
      then it will _not_ be subject to the policy effect. The excluded
      permissions can be specified using the same syntax as `permissions`.
    permissions: Optional. The permissions that are explicitly affected by
      this rule. Each permission uses the format
      `{service_fqdn}/{resource}.{verb}`, where `{service_fqdn}` is the fully
      qualified domain name for the service. Currently supported permissions
      are: * `eventarc.googleapis.com/messageBuses.publish`.
  """

  excludedPermissions = _messages.StringField(1, repeated=True)
  permissions = _messages.StringField(2, repeated=True)


class GoogleIamV3betaListAccessPoliciesResponse(_messages.Message):
  r"""Response message for ListAccessPolicies method.

  Fields:
    accessPolicies: The access policies from the specified parent.
    nextPageToken: Optional. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
  """

  accessPolicies = _messages.MessageField('GoogleIamV3betaAccessPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleIamV3betaListPolicyBindingsResponse(_messages.Message):
  r"""Response message for ListPolicyBindings method.

  Fields:
    nextPageToken: Optional. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    policyBindings: The policy bindings from the specified parent.
  """

  nextPageToken = _messages.StringField(1)
  policyBindings = _messages.MessageField('GoogleIamV3betaPolicyBinding', 2, repeated=True)


class GoogleIamV3betaListPrincipalAccessBoundaryPoliciesResponse(_messages.Message):
  r"""Response message for ListPrincipalAccessBoundaryPolicies method.

  Fields:
    nextPageToken: Optional. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    principalAccessBoundaryPolicies: The principal access boundary policies
      from the specified parent.
  """

  nextPageToken = _messages.StringField(1)
  principalAccessBoundaryPolicies = _messages.MessageField('GoogleIamV3betaPrincipalAccessBoundaryPolicy', 2, repeated=True)


class GoogleIamV3betaOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleIamV3betaPolicyBinding(_messages.Message):
  r"""IAM policy binding resource.

  Enums:
    PolicyKindValueValuesEnum: Immutable. The kind of the policy to attach in
      this binding. This field must be one of the following: - Left empty
      (will be automatically set to the policy kind) - The input policy kind

  Messages:
    AnnotationsValue: Optional. User-defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations

  Fields:
    annotations: Optional. User-defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations
    condition: Optional. The condition to apply to the policy binding. When
      set, the `expression` field in the `Expr` must include from 1 to 10
      subexpressions, joined by the "||"(Logical OR), "&&"(Logical AND) or
      "!"(Logical NOT) operators and cannot contain more than 250 characters.
      The condition is currently only supported when bound to policies of kind
      principal access boundary. When the bound policy is a principal access
      boundary policy, the only supported attributes in any subexpression are
      `principal.type` and `principal.subject`. An example expression is:
      "principal.type == 'iam.googleapis.com/ServiceAccount'" or
      "principal.subject == '<EMAIL>'". Allowed operations for
      `principal.subject`: - `principal.subject == ` - `principal.subject != `
      - `principal.subject in []` - `principal.subject.startsWith()` -
      `principal.subject.endsWith()` Allowed operations for `principal.type`:
      - `principal.type == ` - `principal.type != ` - `principal.type in []`
      Supported principal types are Workspace, Workforce Pool, Workload Pool
      and Service Account. Allowed string must be one of: -
      iam.googleapis.com/WorkspaceIdentity -
      iam.googleapis.com/WorkforcePoolIdentity -
      iam.googleapis.com/WorkloadPoolIdentity -
      iam.googleapis.com/ServiceAccount
    createTime: Output only. The time when the policy binding was created.
    displayName: Optional. The description of the policy binding. Must be less
      than or equal to 63 characters.
    etag: Optional. The etag for the policy binding. If this is provided on
      update, it must match the server's etag.
    name: Identifier. The name of the policy binding, in the format
      `{binding_parent/locations/{location}/policyBindings/{policy_binding_id}
      `. The binding parent is the closest Resource Manager resource (project,
      folder, or organization) to the binding target. Format: * `projects/{pro
      ject_id}/locations/{location}/policyBindings/{policy_binding_id}` * `pro
      jects/{project_number}/locations/{location}/policyBindings/{policy_bindi
      ng_id}` * `folders/{folder_id}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `organizations/{organization_id}/locations/{location}/
      policyBindings/{policy_binding_id}`
    policy: Required. Immutable. The resource name of the policy to be bound.
      The binding parent and policy must belong to the same organization.
    policyKind: Immutable. The kind of the policy to attach in this binding.
      This field must be one of the following: - Left empty (will be
      automatically set to the policy kind) - The input policy kind
    policyUid: Output only. The globally unique ID of the policy to be bound.
    target: Required. Immutable. Target is the full resource name of the
      resource to which the policy will be bound. Immutable once set.
    uid: Output only. The globally unique ID of the policy binding. Assigned
      when the policy binding is created.
    updateTime: Output only. The time when the policy binding was most
      recently updated.
  """

  class PolicyKindValueValuesEnum(_messages.Enum):
    r"""Immutable. The kind of the policy to attach in this binding. This
    field must be one of the following: - Left empty (will be automatically
    set to the policy kind) - The input policy kind

    Values:
      POLICY_KIND_UNSPECIFIED: Unspecified policy kind; Not a valid state
      PRINCIPAL_ACCESS_BOUNDARY: Principal access boundary policy kind
      ACCESS: Access policy kind. Keep behind visibility label until Access
        Policy launch.
    """
    POLICY_KIND_UNSPECIFIED = 0
    PRINCIPAL_ACCESS_BOUNDARY = 1
    ACCESS = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User-defined annotations. See
    https://google.aip.dev/148#annotations for more details such as format and
    size limitations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  condition = _messages.MessageField('GoogleTypeExpr', 2)
  createTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  policy = _messages.StringField(7)
  policyKind = _messages.EnumField('PolicyKindValueValuesEnum', 8)
  policyUid = _messages.StringField(9)
  target = _messages.MessageField('GoogleIamV3betaPolicyBindingTarget', 10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class GoogleIamV3betaPolicyBindingTarget(_messages.Message):
  r"""Target is the full resource name of the resource to which the policy
  will be bound. Immutable once set.

  Fields:
    principalSet: Immutable. Full Resource Name used for principal access
      boundary policy bindings. The principal set must be directly parented by
      the policy binding's parent or same as the parent if the target is a
      project/folder/organization. Examples: * For binding's parented by an
      organization: * Organization:
      `//cloudresourcemanager.googleapis.com/organizations/ORGANIZATION_ID` *
      Workforce Identity:
      `//iam.googleapis.com/locations/global/workforcePools/WORKFORCE_POOL_ID`
      * Workspace Identity:
      `//iam.googleapis.com/locations/global/workspace/WORKSPACE_ID` * For
      binding's parented by a folder: * Folder:
      `//cloudresourcemanager.googleapis.com/folders/FOLDER_ID` * For
      binding's parented by a project: * Project: *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_NUMBER` *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_ID` * Workload
      Identity Pool: `//iam.googleapis.com/projects/PROJECT_NUMBER/locations/L
      OCATION/workloadIdentityPools/WORKLOAD_POOL_ID`
    resource: Immutable. Full Resource Name used for access policy bindings
      Examples: * Organization:
      `//cloudresourcemanager.googleapis.com/organizations/ORGANIZATION_ID` *
      Folder: `//cloudresourcemanager.googleapis.com/folders/FOLDER_ID` *
      Project: *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_NUMBER` *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_ID`
  """

  principalSet = _messages.StringField(1)
  resource = _messages.StringField(2)


class GoogleIamV3betaPrincipalAccessBoundaryPolicy(_messages.Message):
  r"""An IAM principal access boundary policy resource.

  Messages:
    AnnotationsValue: Optional. User defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations

  Fields:
    annotations: Optional. User defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations
    createTime: Output only. The time when the principal access boundary
      policy was created.
    details: Optional. The details for the principal access boundary policy.
    displayName: Optional. The description of the principal access boundary
      policy. Must be less than or equal to 63 characters.
    etag: Optional. The etag for the principal access boundary. If this is
      provided on update, it must match the server's etag.
    name: Identifier. The resource name of the principal access boundary
      policy. The following format is supported: `organizations/{organization_
      id}/locations/{location}/principalAccessBoundaryPolicies/{policy_id}`
    uid: Output only. The globally unique ID of the principal access boundary
      policy.
    updateTime: Output only. The time when the principal access boundary
      policy was most recently updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User defined annotations. See
    https://google.aip.dev/148#annotations for more details such as format and
    size limitations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  details = _messages.MessageField('GoogleIamV3betaPrincipalAccessBoundaryPolicyDetails', 3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  uid = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class GoogleIamV3betaPrincipalAccessBoundaryPolicyDetails(_messages.Message):
  r"""Principal access boundary policy details

  Fields:
    enforcementVersion: Optional. The version number (for example, `1` or
      `latest`) that indicates which permissions are able to be blocked by the
      policy. If empty, the PAB policy version will be set to the most recent
      version number at the time of the policy's creation.
    rules: Required. A list of principal access boundary policy rules. The
      number of rules in a policy is limited to 500.
  """

  enforcementVersion = _messages.StringField(1)
  rules = _messages.MessageField('GoogleIamV3betaPrincipalAccessBoundaryPolicyRule', 2, repeated=True)


class GoogleIamV3betaPrincipalAccessBoundaryPolicyRule(_messages.Message):
  r"""Principal access boundary policy rule that defines the resource
  boundary.

  Enums:
    EffectValueValuesEnum: Required. The access relationship of principals to
      the resources in this rule.

  Fields:
    description: Optional. The description of the principal access boundary
      policy rule. Must be less than or equal to 256 characters.
    effect: Required. The access relationship of principals to the resources
      in this rule.
    resources: Required. A list of Resource Manager resources. If a resource
      is listed in the rule, then the rule applies for that resource and its
      descendants. The number of resources in a policy is limited to 500
      across all rules in the policy. The following resource types are
      supported: * Organizations, such as
      `//cloudresourcemanager.googleapis.com/organizations/123`. * Folders,
      such as `//cloudresourcemanager.googleapis.com/folders/123`. * Projects,
      such as `//cloudresourcemanager.googleapis.com/projects/123` or
      `//cloudresourcemanager.googleapis.com/projects/my-project-id`.
  """

  class EffectValueValuesEnum(_messages.Enum):
    r"""Required. The access relationship of principals to the resources in
    this rule.

    Values:
      EFFECT_UNSPECIFIED: Effect unspecified.
      ALLOW: Allows access to the resources in this rule.
    """
    EFFECT_UNSPECIFIED = 0
    ALLOW = 1

  description = _messages.StringField(1)
  effect = _messages.EnumField('EffectValueValuesEnum', 2)
  resources = _messages.StringField(3, repeated=True)


class GoogleIamV3betaSearchAccessPolicyBindingsResponse(_messages.Message):
  r"""Response message for SearchAccessPolicyBindings rpc.

  Fields:
    nextPageToken: Optional. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    policyBindings: The policy bindings that reference the specified policy.
  """

  nextPageToken = _messages.StringField(1)
  policyBindings = _messages.MessageField('GoogleIamV3betaPolicyBinding', 2, repeated=True)


class GoogleIamV3betaSearchPrincipalAccessBoundaryPolicyBindingsResponse(_messages.Message):
  r"""Response message for SearchPrincipalAccessBoundaryPolicyBindings rpc.

  Fields:
    nextPageToken: Optional. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    policyBindings: The policy bindings that reference the specified policy.
  """

  nextPageToken = _messages.StringField(1)
  policyBindings = _messages.MessageField('GoogleIamV3betaPolicyBinding', 2, repeated=True)


class GoogleIamV3betaSearchTargetPolicyBindingsResponse(_messages.Message):
  r"""Response message for SearchTargetPolicyBindings method.

  Fields:
    nextPageToken: Optional. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    policyBindings: The policy bindings bound to the specified target.
  """

  nextPageToken = _messages.StringField(1)
  policyBindings = _messages.MessageField('GoogleIamV3betaPolicyBinding', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeExpr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class IamFoldersLocationsAccessPoliciesCreateRequest(_messages.Message):
  r"""A IamFoldersLocationsAccessPoliciesCreateRequest object.

  Fields:
    accessPolicyId: Required. The ID to use for the access policy, which will
      become the final component of the access policy's resource name. This
      value must start with a lowercase letter followed by up to 62 lowercase
      letters, numbers, hyphens, or dots. Pattern, /a-z{2,62}/. This value
      must be unique among all access policies with the same parent.
    googleIamV3betaAccessPolicy: A GoogleIamV3betaAccessPolicy resource to be
      passed as the request body.
    parent: Required. The parent resource where this access policy will be
      created. Format: `projects/{project_id}/locations/{location}`
      `projects/{project_number}/locations/{location}`
      `folders/{folder_id}/locations/{location}`
      `organizations/{organization_id}/locations/{location}`
    validateOnly: Optional. If set, validate the request and preview the
      creation, but do not actually post it.
  """

  accessPolicyId = _messages.StringField(1)
  googleIamV3betaAccessPolicy = _messages.MessageField('GoogleIamV3betaAccessPolicy', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class IamFoldersLocationsAccessPoliciesDeleteRequest(_messages.Message):
  r"""A IamFoldersLocationsAccessPoliciesDeleteRequest object.

  Fields:
    etag: Optional. The etag of the access policy. If this is provided, it
      must match the server's etag.
    force: Optional. If set to true, the request will force the deletion of
      the Policy even if the Policy references PolicyBindings.
    name: Required. The name of the access policy to delete. Format: `projects
      /{project_id}/locations/{location}/accessPolicies/{access_policy_id}` `p
      rojects/{project_number}/locations/{location}/accessPolicies/{access_pol
      icy_id}` `folders/{folder_id}/locations/{location}/accessPolicies/{acces
      s_policy_id}` `organizations/{organization_id}/locations/{location}/acce
      ssPolicies/{access_policy_id}`
    validateOnly: Optional. If set, validate the request and preview the
      deletion, but do not actually post it.
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class IamFoldersLocationsAccessPoliciesGetRequest(_messages.Message):
  r"""A IamFoldersLocationsAccessPoliciesGetRequest object.

  Fields:
    name: Required. The name of the access policy to retrieve. Format: `projec
      ts/{project_id}/locations/{location}/accessPolicies/{access_policy_id}`
      `projects/{project_number}/locations/{location}/accessPolicies/{access_p
      olicy_id}` `folders/{folder_id}/locations/{location}/accessPolicies/{acc
      ess_policy_id}` `organizations/{organization_id}/locations/{location}/ac
      cessPolicies/{access_policy_id}`
  """

  name = _messages.StringField(1, required=True)


class IamFoldersLocationsAccessPoliciesListRequest(_messages.Message):
  r"""A IamFoldersLocationsAccessPoliciesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of access policies to return. The
      service may return fewer than this value. If unspecified, at most 50
      access policies will be returned. Valid value ranges from 1 to 1000;
      values above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListAccessPolicies` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListAccessPolicies`
      must match the call that provided the page token.
    parent: Required. The parent resource, which owns the collection of access
      policy resources. Format: `projects/{project_id}/locations/{location}`
      `projects/{project_number}/locations/{location}`
      `folders/{folder_id}/locations/{location}`
      `organizations/{organization_id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class IamFoldersLocationsAccessPoliciesPatchRequest(_messages.Message):
  r"""A IamFoldersLocationsAccessPoliciesPatchRequest object.

  Fields:
    googleIamV3betaAccessPolicy: A GoogleIamV3betaAccessPolicy resource to be
      passed as the request body.
    name: Identifier. The resource name of the access policy. The following
      formats are supported:
      `projects/{project_id}/locations/{location}/accessPolicies/{policy_id}`
      `projects/{project_number}/locations/{location}/accessPolicies/{policy_i
      d}`
      `folders/{folder_id}/locations/{location}/accessPolicies/{policy_id}` `o
      rganizations/{organization_id}/locations/{location}/accessPolicies/{poli
      cy_id}`
    validateOnly: Optional. If set, validate the request and preview the
      update, but do not actually post it.
  """

  googleIamV3betaAccessPolicy = _messages.MessageField('GoogleIamV3betaAccessPolicy', 1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class IamFoldersLocationsAccessPoliciesSearchPolicyBindingsRequest(_messages.Message):
  r"""A IamFoldersLocationsAccessPoliciesSearchPolicyBindingsRequest object.

  Fields:
    name: Required. The name of the access policy. Format: `organizations/{org
      anization_id}/locations/{location}/accessPolicies/{access_policy_id}` `f
      olders/{folder_id}/locations/{location}/accessPolicies/{access_policy_id
      }` `projects/{project_id}/locations/{location}/accessPolicies/{access_po
      licy_id}` `projects/{project_number}/locations/{location}/accessPolicies
      /{access_policy_id}`
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchAccessPolicyBindingsRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `SearchAccessPolicyBindingsRequest` must match the call that provided
      the page token.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class IamFoldersLocationsOperationsGetRequest(_messages.Message):
  r"""A IamFoldersLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamFoldersLocationsPolicyBindingsCreateRequest(_messages.Message):
  r"""A IamFoldersLocationsPolicyBindingsCreateRequest object.

  Fields:
    googleIamV3betaPolicyBinding: A GoogleIamV3betaPolicyBinding resource to
      be passed as the request body.
    parent: Required. The parent resource where this policy binding will be
      created. The binding parent is the closest Resource Manager resource
      (project, folder or organization) to the binding target. Format: *
      `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
    policyBindingId: Required. The ID to use for the policy binding, which
      will become the final component of the policy binding's resource name.
      This value must start with a lowercase letter followed by up to 62
      lowercase letters, numbers, hyphens, or dots. Pattern, /a-z{2,62}/.
    validateOnly: Optional. If set, validate the request and preview the
      creation, but do not actually post it.
  """

  googleIamV3betaPolicyBinding = _messages.MessageField('GoogleIamV3betaPolicyBinding', 1)
  parent = _messages.StringField(2, required=True)
  policyBindingId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamFoldersLocationsPolicyBindingsDeleteRequest(_messages.Message):
  r"""A IamFoldersLocationsPolicyBindingsDeleteRequest object.

  Fields:
    etag: Optional. The etag of the policy binding. If this is provided, it
      must match the server's etag.
    name: Required. The name of the policy binding to delete. Format: * `proje
      cts/{project_id}/locations/{location}/policyBindings/{policy_binding_id}
      ` * `projects/{project_number}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `folders/{folder_id}/locations/{location}/policyBindin
      gs/{policy_binding_id}` * `organizations/{organization_id}/locations/{lo
      cation}/policyBindings/{policy_binding_id}`
    validateOnly: Optional. If set, validate the request and preview the
      deletion, but do not actually post it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class IamFoldersLocationsPolicyBindingsGetRequest(_messages.Message):
  r"""A IamFoldersLocationsPolicyBindingsGetRequest object.

  Fields:
    name: Required. The name of the policy binding to retrieve. Format: * `pro
      jects/{project_id}/locations/{location}/policyBindings/{policy_binding_i
      d}` * `projects/{project_number}/locations/{location}/policyBindings/{po
      licy_binding_id}` * `folders/{folder_id}/locations/{location}/policyBind
      ings/{policy_binding_id}` * `organizations/{organization_id}/locations/{
      location}/policyBindings/{policy_binding_id}`
  """

  name = _messages.StringField(1, required=True)


class IamFoldersLocationsPolicyBindingsListRequest(_messages.Message):
  r"""A IamFoldersLocationsPolicyBindingsListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Filter rules are case insensitive. Some eligible fields for filtering
      are: + `target` + `policy` Some examples of filter queries: *
      `target:ex*`: The binding target's name starts with "ex". *
      `target:example`: The binding target's name is `example`. *
      `policy:example`: The binding policy's name is `example`.
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListPolicyBindings` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListPolicyBindings`
      must match the call that provided the page token.
    parent: Required. The parent resource, which owns the collection of policy
      bindings. Format: * `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class IamFoldersLocationsPolicyBindingsPatchRequest(_messages.Message):
  r"""A IamFoldersLocationsPolicyBindingsPatchRequest object.

  Fields:
    googleIamV3betaPolicyBinding: A GoogleIamV3betaPolicyBinding resource to
      be passed as the request body.
    name: Identifier. The name of the policy binding, in the format
      `{binding_parent/locations/{location}/policyBindings/{policy_binding_id}
      `. The binding parent is the closest Resource Manager resource (project,
      folder, or organization) to the binding target. Format: * `projects/{pro
      ject_id}/locations/{location}/policyBindings/{policy_binding_id}` * `pro
      jects/{project_number}/locations/{location}/policyBindings/{policy_bindi
      ng_id}` * `folders/{folder_id}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `organizations/{organization_id}/locations/{location}/
      policyBindings/{policy_binding_id}`
    updateMask: Optional. The list of fields to update
    validateOnly: Optional. If set, validate the request and preview the
      update, but do not actually post it.
  """

  googleIamV3betaPolicyBinding = _messages.MessageField('GoogleIamV3betaPolicyBinding', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamFoldersLocationsPolicyBindingsSearchTargetPolicyBindingsRequest(_messages.Message):
  r"""A IamFoldersLocationsPolicyBindingsSearchTargetPolicyBindingsRequest
  object.

  Fields:
    filter: Optional. Filtering currently only supports the kind of policies
      to return, and must be in the format "policy_kind={policy_kind}". If
      String is empty, bindings bound to all kinds of policies would be
      returned. The only supported values are:
      "policy_kind=PRINCIPAL_ACCESS_BOUNDARY", "policy_kind=ACCESS"
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchTargetPolicyBindingsRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `SearchTargetPolicyBindingsRequest` must match the call that provided
      the page token.
    parent: Required. The parent resource where this search will be performed.
      This should be the nearest Resource Manager resource (project, folder,
      or organization) to the target. Format: *
      `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
    target: Required. The target resource, which is bound to the policy in the
      binding. Format: *
      `//iam.googleapis.com/locations/global/workforcePools/POOL_ID` * `//iam.
      googleapis.com/projects/PROJECT_NUMBER/locations/global/workloadIdentity
      Pools/POOL_ID` *
      `//iam.googleapis.com/locations/global/workspace/WORKSPACE_ID` *
      `//cloudresourcemanager.googleapis.com/projects/{project_number}` *
      `//cloudresourcemanager.googleapis.com/folders/{folder_id}` *
      `//cloudresourcemanager.googleapis.com/organizations/{organization_id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  target = _messages.StringField(5)


class IamOrganizationsLocationsAccessPoliciesCreateRequest(_messages.Message):
  r"""A IamOrganizationsLocationsAccessPoliciesCreateRequest object.

  Fields:
    accessPolicyId: Required. The ID to use for the access policy, which will
      become the final component of the access policy's resource name. This
      value must start with a lowercase letter followed by up to 62 lowercase
      letters, numbers, hyphens, or dots. Pattern, /a-z{2,62}/. This value
      must be unique among all access policies with the same parent.
    googleIamV3betaAccessPolicy: A GoogleIamV3betaAccessPolicy resource to be
      passed as the request body.
    parent: Required. The parent resource where this access policy will be
      created. Format: `projects/{project_id}/locations/{location}`
      `projects/{project_number}/locations/{location}`
      `folders/{folder_id}/locations/{location}`
      `organizations/{organization_id}/locations/{location}`
    validateOnly: Optional. If set, validate the request and preview the
      creation, but do not actually post it.
  """

  accessPolicyId = _messages.StringField(1)
  googleIamV3betaAccessPolicy = _messages.MessageField('GoogleIamV3betaAccessPolicy', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class IamOrganizationsLocationsAccessPoliciesDeleteRequest(_messages.Message):
  r"""A IamOrganizationsLocationsAccessPoliciesDeleteRequest object.

  Fields:
    etag: Optional. The etag of the access policy. If this is provided, it
      must match the server's etag.
    force: Optional. If set to true, the request will force the deletion of
      the Policy even if the Policy references PolicyBindings.
    name: Required. The name of the access policy to delete. Format: `projects
      /{project_id}/locations/{location}/accessPolicies/{access_policy_id}` `p
      rojects/{project_number}/locations/{location}/accessPolicies/{access_pol
      icy_id}` `folders/{folder_id}/locations/{location}/accessPolicies/{acces
      s_policy_id}` `organizations/{organization_id}/locations/{location}/acce
      ssPolicies/{access_policy_id}`
    validateOnly: Optional. If set, validate the request and preview the
      deletion, but do not actually post it.
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class IamOrganizationsLocationsAccessPoliciesGetRequest(_messages.Message):
  r"""A IamOrganizationsLocationsAccessPoliciesGetRequest object.

  Fields:
    name: Required. The name of the access policy to retrieve. Format: `projec
      ts/{project_id}/locations/{location}/accessPolicies/{access_policy_id}`
      `projects/{project_number}/locations/{location}/accessPolicies/{access_p
      olicy_id}` `folders/{folder_id}/locations/{location}/accessPolicies/{acc
      ess_policy_id}` `organizations/{organization_id}/locations/{location}/ac
      cessPolicies/{access_policy_id}`
  """

  name = _messages.StringField(1, required=True)


class IamOrganizationsLocationsAccessPoliciesListRequest(_messages.Message):
  r"""A IamOrganizationsLocationsAccessPoliciesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of access policies to return. The
      service may return fewer than this value. If unspecified, at most 50
      access policies will be returned. Valid value ranges from 1 to 1000;
      values above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListAccessPolicies` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListAccessPolicies`
      must match the call that provided the page token.
    parent: Required. The parent resource, which owns the collection of access
      policy resources. Format: `projects/{project_id}/locations/{location}`
      `projects/{project_number}/locations/{location}`
      `folders/{folder_id}/locations/{location}`
      `organizations/{organization_id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class IamOrganizationsLocationsAccessPoliciesPatchRequest(_messages.Message):
  r"""A IamOrganizationsLocationsAccessPoliciesPatchRequest object.

  Fields:
    googleIamV3betaAccessPolicy: A GoogleIamV3betaAccessPolicy resource to be
      passed as the request body.
    name: Identifier. The resource name of the access policy. The following
      formats are supported:
      `projects/{project_id}/locations/{location}/accessPolicies/{policy_id}`
      `projects/{project_number}/locations/{location}/accessPolicies/{policy_i
      d}`
      `folders/{folder_id}/locations/{location}/accessPolicies/{policy_id}` `o
      rganizations/{organization_id}/locations/{location}/accessPolicies/{poli
      cy_id}`
    validateOnly: Optional. If set, validate the request and preview the
      update, but do not actually post it.
  """

  googleIamV3betaAccessPolicy = _messages.MessageField('GoogleIamV3betaAccessPolicy', 1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class IamOrganizationsLocationsAccessPoliciesSearchPolicyBindingsRequest(_messages.Message):
  r"""A IamOrganizationsLocationsAccessPoliciesSearchPolicyBindingsRequest
  object.

  Fields:
    name: Required. The name of the access policy. Format: `organizations/{org
      anization_id}/locations/{location}/accessPolicies/{access_policy_id}` `f
      olders/{folder_id}/locations/{location}/accessPolicies/{access_policy_id
      }` `projects/{project_id}/locations/{location}/accessPolicies/{access_po
      licy_id}` `projects/{project_number}/locations/{location}/accessPolicies
      /{access_policy_id}`
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchAccessPolicyBindingsRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `SearchAccessPolicyBindingsRequest` must match the call that provided
      the page token.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class IamOrganizationsLocationsOperationsGetRequest(_messages.Message):
  r"""A IamOrganizationsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamOrganizationsLocationsPolicyBindingsCreateRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPolicyBindingsCreateRequest object.

  Fields:
    googleIamV3betaPolicyBinding: A GoogleIamV3betaPolicyBinding resource to
      be passed as the request body.
    parent: Required. The parent resource where this policy binding will be
      created. The binding parent is the closest Resource Manager resource
      (project, folder or organization) to the binding target. Format: *
      `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
    policyBindingId: Required. The ID to use for the policy binding, which
      will become the final component of the policy binding's resource name.
      This value must start with a lowercase letter followed by up to 62
      lowercase letters, numbers, hyphens, or dots. Pattern, /a-z{2,62}/.
    validateOnly: Optional. If set, validate the request and preview the
      creation, but do not actually post it.
  """

  googleIamV3betaPolicyBinding = _messages.MessageField('GoogleIamV3betaPolicyBinding', 1)
  parent = _messages.StringField(2, required=True)
  policyBindingId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamOrganizationsLocationsPolicyBindingsDeleteRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPolicyBindingsDeleteRequest object.

  Fields:
    etag: Optional. The etag of the policy binding. If this is provided, it
      must match the server's etag.
    name: Required. The name of the policy binding to delete. Format: * `proje
      cts/{project_id}/locations/{location}/policyBindings/{policy_binding_id}
      ` * `projects/{project_number}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `folders/{folder_id}/locations/{location}/policyBindin
      gs/{policy_binding_id}` * `organizations/{organization_id}/locations/{lo
      cation}/policyBindings/{policy_binding_id}`
    validateOnly: Optional. If set, validate the request and preview the
      deletion, but do not actually post it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class IamOrganizationsLocationsPolicyBindingsGetRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPolicyBindingsGetRequest object.

  Fields:
    name: Required. The name of the policy binding to retrieve. Format: * `pro
      jects/{project_id}/locations/{location}/policyBindings/{policy_binding_i
      d}` * `projects/{project_number}/locations/{location}/policyBindings/{po
      licy_binding_id}` * `folders/{folder_id}/locations/{location}/policyBind
      ings/{policy_binding_id}` * `organizations/{organization_id}/locations/{
      location}/policyBindings/{policy_binding_id}`
  """

  name = _messages.StringField(1, required=True)


class IamOrganizationsLocationsPolicyBindingsListRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPolicyBindingsListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Filter rules are case insensitive. Some eligible fields for filtering
      are: + `target` + `policy` Some examples of filter queries: *
      `target:ex*`: The binding target's name starts with "ex". *
      `target:example`: The binding target's name is `example`. *
      `policy:example`: The binding policy's name is `example`.
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListPolicyBindings` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListPolicyBindings`
      must match the call that provided the page token.
    parent: Required. The parent resource, which owns the collection of policy
      bindings. Format: * `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class IamOrganizationsLocationsPolicyBindingsPatchRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPolicyBindingsPatchRequest object.

  Fields:
    googleIamV3betaPolicyBinding: A GoogleIamV3betaPolicyBinding resource to
      be passed as the request body.
    name: Identifier. The name of the policy binding, in the format
      `{binding_parent/locations/{location}/policyBindings/{policy_binding_id}
      `. The binding parent is the closest Resource Manager resource (project,
      folder, or organization) to the binding target. Format: * `projects/{pro
      ject_id}/locations/{location}/policyBindings/{policy_binding_id}` * `pro
      jects/{project_number}/locations/{location}/policyBindings/{policy_bindi
      ng_id}` * `folders/{folder_id}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `organizations/{organization_id}/locations/{location}/
      policyBindings/{policy_binding_id}`
    updateMask: Optional. The list of fields to update
    validateOnly: Optional. If set, validate the request and preview the
      update, but do not actually post it.
  """

  googleIamV3betaPolicyBinding = _messages.MessageField('GoogleIamV3betaPolicyBinding', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamOrganizationsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest(_messages.Message):
  r"""A
  IamOrganizationsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest
  object.

  Fields:
    filter: Optional. Filtering currently only supports the kind of policies
      to return, and must be in the format "policy_kind={policy_kind}". If
      String is empty, bindings bound to all kinds of policies would be
      returned. The only supported values are:
      "policy_kind=PRINCIPAL_ACCESS_BOUNDARY", "policy_kind=ACCESS"
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchTargetPolicyBindingsRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `SearchTargetPolicyBindingsRequest` must match the call that provided
      the page token.
    parent: Required. The parent resource where this search will be performed.
      This should be the nearest Resource Manager resource (project, folder,
      or organization) to the target. Format: *
      `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
    target: Required. The target resource, which is bound to the policy in the
      binding. Format: *
      `//iam.googleapis.com/locations/global/workforcePools/POOL_ID` * `//iam.
      googleapis.com/projects/PROJECT_NUMBER/locations/global/workloadIdentity
      Pools/POOL_ID` *
      `//iam.googleapis.com/locations/global/workspace/WORKSPACE_ID` *
      `//cloudresourcemanager.googleapis.com/projects/{project_number}` *
      `//cloudresourcemanager.googleapis.com/folders/{folder_id}` *
      `//cloudresourcemanager.googleapis.com/organizations/{organization_id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  target = _messages.StringField(5)


class IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesCreateRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesCreateRequest
  object.

  Fields:
    googleIamV3betaPrincipalAccessBoundaryPolicy: A
      GoogleIamV3betaPrincipalAccessBoundaryPolicy resource to be passed as
      the request body.
    parent: Required. The parent resource where this principal access boundary
      policy will be created. Only organizations are supported. Format:
      `organizations/{organization_id}/locations/{location}`
    principalAccessBoundaryPolicyId: Required. The ID to use for the principal
      access boundary policy, which will become the final component of the
      principal access boundary policy's resource name. This value must start
      with a lowercase letter followed by up to 62 lowercase letters, numbers,
      hyphens, or dots. Pattern, /a-z{2,62}/.
    validateOnly: Optional. If set, validate the request and preview the
      creation, but do not actually post it.
  """

  googleIamV3betaPrincipalAccessBoundaryPolicy = _messages.MessageField('GoogleIamV3betaPrincipalAccessBoundaryPolicy', 1)
  parent = _messages.StringField(2, required=True)
  principalAccessBoundaryPolicyId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesDeleteRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesDeleteRequest
  object.

  Fields:
    etag: Optional. The etag of the principal access boundary policy. If this
      is provided, it must match the server's etag.
    force: Optional. If set to true, the request will force the deletion of
      the policy even if the policy is referenced in policy bindings.
    name: Required. The name of the principal access boundary policy to
      delete. Format: `organizations/{organization_id}/locations/{location}/pr
      incipalAccessBoundaryPolicies/{principal_access_boundary_policy_id}`
    validateOnly: Optional. If set, validate the request and preview the
      deletion, but do not actually post it.
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesGetRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesGetRequest
  object.

  Fields:
    name: Required. The name of the principal access boundary policy to
      retrieve. Format: `organizations/{organization_id}/locations/{location}/
      principalAccessBoundaryPolicies/{principal_access_boundary_policy_id}`
  """

  name = _messages.StringField(1, required=True)


class IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesListRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of principal access boundary
      policies to return. The service may return fewer than this value. If
      unspecified, at most 50 principal access boundary policies will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListPrincipalAccessBoundaryPolicies` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListPrincipalAccessBoundaryPolicies` must match the call that provided
      the page token.
    parent: Required. The parent resource, which owns the collection of
      principal access boundary policies. Format:
      `organizations/{organization_id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesPatchRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesPatchRequest
  object.

  Fields:
    googleIamV3betaPrincipalAccessBoundaryPolicy: A
      GoogleIamV3betaPrincipalAccessBoundaryPolicy resource to be passed as
      the request body.
    name: Identifier. The resource name of the principal access boundary
      policy. The following format is supported: `organizations/{organization_
      id}/locations/{location}/principalAccessBoundaryPolicies/{policy_id}`
    updateMask: Optional. The list of fields to update
    validateOnly: Optional. If set, validate the request and preview the
      update, but do not actually post it.
  """

  googleIamV3betaPrincipalAccessBoundaryPolicy = _messages.MessageField('GoogleIamV3betaPrincipalAccessBoundaryPolicy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesSearchPolicyBindingsRequest(_messages.Message):
  r"""A IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesSearchPolicyBi
  ndingsRequest object.

  Fields:
    name: Required. The name of the principal access boundary policy. Format:
      `organizations/{organization_id}/locations/{location}/principalAccessBou
      ndaryPolicies/{principal_access_boundary_policy_id}`
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchPrincipalAccessBoundaryPolicyBindingsRequest` call. Provide this
      to retrieve the subsequent page. When paginating, all other parameters
      provided to `SearchPrincipalAccessBoundaryPolicyBindingsRequest` must
      match the call that provided the page token.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class IamProjectsLocationsAccessPoliciesCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsAccessPoliciesCreateRequest object.

  Fields:
    accessPolicyId: Required. The ID to use for the access policy, which will
      become the final component of the access policy's resource name. This
      value must start with a lowercase letter followed by up to 62 lowercase
      letters, numbers, hyphens, or dots. Pattern, /a-z{2,62}/. This value
      must be unique among all access policies with the same parent.
    googleIamV3betaAccessPolicy: A GoogleIamV3betaAccessPolicy resource to be
      passed as the request body.
    parent: Required. The parent resource where this access policy will be
      created. Format: `projects/{project_id}/locations/{location}`
      `projects/{project_number}/locations/{location}`
      `folders/{folder_id}/locations/{location}`
      `organizations/{organization_id}/locations/{location}`
    validateOnly: Optional. If set, validate the request and preview the
      creation, but do not actually post it.
  """

  accessPolicyId = _messages.StringField(1)
  googleIamV3betaAccessPolicy = _messages.MessageField('GoogleIamV3betaAccessPolicy', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class IamProjectsLocationsAccessPoliciesDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsAccessPoliciesDeleteRequest object.

  Fields:
    etag: Optional. The etag of the access policy. If this is provided, it
      must match the server's etag.
    force: Optional. If set to true, the request will force the deletion of
      the Policy even if the Policy references PolicyBindings.
    name: Required. The name of the access policy to delete. Format: `projects
      /{project_id}/locations/{location}/accessPolicies/{access_policy_id}` `p
      rojects/{project_number}/locations/{location}/accessPolicies/{access_pol
      icy_id}` `folders/{folder_id}/locations/{location}/accessPolicies/{acces
      s_policy_id}` `organizations/{organization_id}/locations/{location}/acce
      ssPolicies/{access_policy_id}`
    validateOnly: Optional. If set, validate the request and preview the
      deletion, but do not actually post it.
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class IamProjectsLocationsAccessPoliciesGetRequest(_messages.Message):
  r"""A IamProjectsLocationsAccessPoliciesGetRequest object.

  Fields:
    name: Required. The name of the access policy to retrieve. Format: `projec
      ts/{project_id}/locations/{location}/accessPolicies/{access_policy_id}`
      `projects/{project_number}/locations/{location}/accessPolicies/{access_p
      olicy_id}` `folders/{folder_id}/locations/{location}/accessPolicies/{acc
      ess_policy_id}` `organizations/{organization_id}/locations/{location}/ac
      cessPolicies/{access_policy_id}`
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsAccessPoliciesListRequest(_messages.Message):
  r"""A IamProjectsLocationsAccessPoliciesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of access policies to return. The
      service may return fewer than this value. If unspecified, at most 50
      access policies will be returned. Valid value ranges from 1 to 1000;
      values above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListAccessPolicies` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListAccessPolicies`
      must match the call that provided the page token.
    parent: Required. The parent resource, which owns the collection of access
      policy resources. Format: `projects/{project_id}/locations/{location}`
      `projects/{project_number}/locations/{location}`
      `folders/{folder_id}/locations/{location}`
      `organizations/{organization_id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class IamProjectsLocationsAccessPoliciesPatchRequest(_messages.Message):
  r"""A IamProjectsLocationsAccessPoliciesPatchRequest object.

  Fields:
    googleIamV3betaAccessPolicy: A GoogleIamV3betaAccessPolicy resource to be
      passed as the request body.
    name: Identifier. The resource name of the access policy. The following
      formats are supported:
      `projects/{project_id}/locations/{location}/accessPolicies/{policy_id}`
      `projects/{project_number}/locations/{location}/accessPolicies/{policy_i
      d}`
      `folders/{folder_id}/locations/{location}/accessPolicies/{policy_id}` `o
      rganizations/{organization_id}/locations/{location}/accessPolicies/{poli
      cy_id}`
    validateOnly: Optional. If set, validate the request and preview the
      update, but do not actually post it.
  """

  googleIamV3betaAccessPolicy = _messages.MessageField('GoogleIamV3betaAccessPolicy', 1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class IamProjectsLocationsAccessPoliciesSearchPolicyBindingsRequest(_messages.Message):
  r"""A IamProjectsLocationsAccessPoliciesSearchPolicyBindingsRequest object.

  Fields:
    name: Required. The name of the access policy. Format: `organizations/{org
      anization_id}/locations/{location}/accessPolicies/{access_policy_id}` `f
      olders/{folder_id}/locations/{location}/accessPolicies/{access_policy_id
      }` `projects/{project_id}/locations/{location}/accessPolicies/{access_po
      licy_id}` `projects/{project_number}/locations/{location}/accessPolicies
      /{access_policy_id}`
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchAccessPolicyBindingsRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `SearchAccessPolicyBindingsRequest` must match the call that provided
      the page token.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class IamProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A IamProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsPolicyBindingsCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyBindingsCreateRequest object.

  Fields:
    googleIamV3betaPolicyBinding: A GoogleIamV3betaPolicyBinding resource to
      be passed as the request body.
    parent: Required. The parent resource where this policy binding will be
      created. The binding parent is the closest Resource Manager resource
      (project, folder or organization) to the binding target. Format: *
      `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
    policyBindingId: Required. The ID to use for the policy binding, which
      will become the final component of the policy binding's resource name.
      This value must start with a lowercase letter followed by up to 62
      lowercase letters, numbers, hyphens, or dots. Pattern, /a-z{2,62}/.
    validateOnly: Optional. If set, validate the request and preview the
      creation, but do not actually post it.
  """

  googleIamV3betaPolicyBinding = _messages.MessageField('GoogleIamV3betaPolicyBinding', 1)
  parent = _messages.StringField(2, required=True)
  policyBindingId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamProjectsLocationsPolicyBindingsDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyBindingsDeleteRequest object.

  Fields:
    etag: Optional. The etag of the policy binding. If this is provided, it
      must match the server's etag.
    name: Required. The name of the policy binding to delete. Format: * `proje
      cts/{project_id}/locations/{location}/policyBindings/{policy_binding_id}
      ` * `projects/{project_number}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `folders/{folder_id}/locations/{location}/policyBindin
      gs/{policy_binding_id}` * `organizations/{organization_id}/locations/{lo
      cation}/policyBindings/{policy_binding_id}`
    validateOnly: Optional. If set, validate the request and preview the
      deletion, but do not actually post it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class IamProjectsLocationsPolicyBindingsGetRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyBindingsGetRequest object.

  Fields:
    name: Required. The name of the policy binding to retrieve. Format: * `pro
      jects/{project_id}/locations/{location}/policyBindings/{policy_binding_i
      d}` * `projects/{project_number}/locations/{location}/policyBindings/{po
      licy_binding_id}` * `folders/{folder_id}/locations/{location}/policyBind
      ings/{policy_binding_id}` * `organizations/{organization_id}/locations/{
      location}/policyBindings/{policy_binding_id}`
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsPolicyBindingsListRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyBindingsListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Filter rules are case insensitive. Some eligible fields for filtering
      are: + `target` + `policy` Some examples of filter queries: *
      `target:ex*`: The binding target's name starts with "ex". *
      `target:example`: The binding target's name is `example`. *
      `policy:example`: The binding policy's name is `example`.
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListPolicyBindings` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListPolicyBindings`
      must match the call that provided the page token.
    parent: Required. The parent resource, which owns the collection of policy
      bindings. Format: * `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class IamProjectsLocationsPolicyBindingsPatchRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyBindingsPatchRequest object.

  Fields:
    googleIamV3betaPolicyBinding: A GoogleIamV3betaPolicyBinding resource to
      be passed as the request body.
    name: Identifier. The name of the policy binding, in the format
      `{binding_parent/locations/{location}/policyBindings/{policy_binding_id}
      `. The binding parent is the closest Resource Manager resource (project,
      folder, or organization) to the binding target. Format: * `projects/{pro
      ject_id}/locations/{location}/policyBindings/{policy_binding_id}` * `pro
      jects/{project_number}/locations/{location}/policyBindings/{policy_bindi
      ng_id}` * `folders/{folder_id}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `organizations/{organization_id}/locations/{location}/
      policyBindings/{policy_binding_id}`
    updateMask: Optional. The list of fields to update
    validateOnly: Optional. If set, validate the request and preview the
      update, but do not actually post it.
  """

  googleIamV3betaPolicyBinding = _messages.MessageField('GoogleIamV3betaPolicyBinding', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class IamProjectsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest(_messages.Message):
  r"""A IamProjectsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest
  object.

  Fields:
    filter: Optional. Filtering currently only supports the kind of policies
      to return, and must be in the format "policy_kind={policy_kind}". If
      String is empty, bindings bound to all kinds of policies would be
      returned. The only supported values are:
      "policy_kind=PRINCIPAL_ACCESS_BOUNDARY", "policy_kind=ACCESS"
    pageSize: Optional. The maximum number of policy bindings to return. The
      service may return fewer than this value. If unspecified, at most 50
      policy bindings will be returned. The maximum value is 1000; values
      above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchTargetPolicyBindingsRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `SearchTargetPolicyBindingsRequest` must match the call that provided
      the page token.
    parent: Required. The parent resource where this search will be performed.
      This should be the nearest Resource Manager resource (project, folder,
      or organization) to the target. Format: *
      `projects/{project_id}/locations/{location}` *
      `projects/{project_number}/locations/{location}` *
      `folders/{folder_id}/locations/{location}` *
      `organizations/{organization_id}/locations/{location}`
    target: Required. The target resource, which is bound to the policy in the
      binding. Format: *
      `//iam.googleapis.com/locations/global/workforcePools/POOL_ID` * `//iam.
      googleapis.com/projects/PROJECT_NUMBER/locations/global/workloadIdentity
      Pools/POOL_ID` *
      `//iam.googleapis.com/locations/global/workspace/WORKSPACE_ID` *
      `//cloudresourcemanager.googleapis.com/projects/{project_number}` *
      `//cloudresourcemanager.googleapis.com/folders/{folder_id}` *
      `//cloudresourcemanager.googleapis.com/organizations/{organization_id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  target = _messages.StringField(5)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
