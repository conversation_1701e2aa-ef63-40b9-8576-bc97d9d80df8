"""Generated client library for iam version v3alpha."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.iam.v3alpha import iam_v3alpha_messages as messages


class IamV3alpha(base_api.BaseApiClient):
  """Generated client library for service iam version v3alpha."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://iam.googleapis.com/'
  MTLS_BASE_URL = 'https://iam.mtls.googleapis.com/'

  _PACKAGE = 'iam'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v3alpha'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'IamV3alpha'
  _URL_VERSION = 'v3alpha'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new iam handle."""
    url = url or self.BASE_URL
    super(IamV3alpha, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.folders_locations_accessPolicies = self.FoldersLocationsAccessPoliciesService(self)
    self.folders_locations_operations = self.FoldersLocationsOperationsService(self)
    self.folders_locations_policyBindings = self.FoldersLocationsPolicyBindingsService(self)
    self.folders_locations = self.FoldersLocationsService(self)
    self.folders = self.FoldersService(self)
    self.organizations_locations_accessPolicies = self.OrganizationsLocationsAccessPoliciesService(self)
    self.organizations_locations_operations = self.OrganizationsLocationsOperationsService(self)
    self.organizations_locations_policyBindings = self.OrganizationsLocationsPolicyBindingsService(self)
    self.organizations_locations_principalAccessBoundaryPolicies = self.OrganizationsLocationsPrincipalAccessBoundaryPoliciesService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_locations_accessPolicies = self.ProjectsLocationsAccessPoliciesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_policyBindings = self.ProjectsLocationsPolicyBindingsService(self)
    self.projects_locations_policyPorters_translations_sourcePolicies = self.ProjectsLocationsPolicyPortersTranslationsSourcePoliciesService(self)
    self.projects_locations_policyPorters_translations_translatedPolicies = self.ProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesService(self)
    self.projects_locations_policyPorters_translations = self.ProjectsLocationsPolicyPortersTranslationsService(self)
    self.projects_locations_policyPorters = self.ProjectsLocationsPolicyPortersService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)
    self.searchApplicablePolicies = self.SearchApplicablePoliciesService(self)

  class FoldersLocationsAccessPoliciesService(base_api.BaseApiService):
    """Service class for the folders_locations_accessPolicies resource."""

    _NAME = 'folders_locations_accessPolicies'

    def __init__(self, client):
      super(IamV3alpha.FoldersLocationsAccessPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an access policy, and returns a long running operation.

      Args:
        request: (IamFoldersLocationsAccessPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/folders/{foldersId}/locations/{locationsId}/accessPolicies',
        http_method='POST',
        method_id='iam.folders.locations.accessPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['accessPolicyId', 'validateOnly'],
        relative_path='v3alpha/{+parent}/accessPolicies',
        request_field='googleIamV3alphaAccessPolicy',
        request_type_name='IamFoldersLocationsAccessPoliciesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an access policy.

      Args:
        request: (IamFoldersLocationsAccessPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/folders/{foldersId}/locations/{locationsId}/accessPolicies/{accessPoliciesId}',
        http_method='DELETE',
        method_id='iam.folders.locations.accessPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'force', 'validateOnly'],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamFoldersLocationsAccessPoliciesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an access policy.

      Args:
        request: (IamFoldersLocationsAccessPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaAccessPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/folders/{foldersId}/locations/{locationsId}/accessPolicies/{accessPoliciesId}',
        http_method='GET',
        method_id='iam.folders.locations.accessPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamFoldersLocationsAccessPoliciesGetRequest',
        response_type_name='GoogleIamV3alphaAccessPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists access policies.

      Args:
        request: (IamFoldersLocationsAccessPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaListAccessPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/folders/{foldersId}/locations/{locationsId}/accessPolicies',
        http_method='GET',
        method_id='iam.folders.locations.accessPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v3alpha/{+parent}/accessPolicies',
        request_field='',
        request_type_name='IamFoldersLocationsAccessPoliciesListRequest',
        response_type_name='GoogleIamV3alphaListAccessPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an access policy.

      Args:
        request: (IamFoldersLocationsAccessPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/folders/{foldersId}/locations/{locationsId}/accessPolicies/{accessPoliciesId}',
        http_method='PATCH',
        method_id='iam.folders.locations.accessPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v3alpha/{+name}',
        request_field='googleIamV3alphaAccessPolicy',
        request_type_name='IamFoldersLocationsAccessPoliciesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SearchPolicyBindings(self, request, global_params=None):
      r"""Returns all policy bindings that bind a specific policy if a user has searchPolicyBindings permission on that policy.

      Args:
        request: (IamFoldersLocationsAccessPoliciesSearchPolicyBindingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaSearchAccessPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchPolicyBindings')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchPolicyBindings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/folders/{foldersId}/locations/{locationsId}/accessPolicies/{accessPoliciesId}:searchPolicyBindings',
        http_method='GET',
        method_id='iam.folders.locations.accessPolicies.searchPolicyBindings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v3alpha/{+name}:searchPolicyBindings',
        request_field='',
        request_type_name='IamFoldersLocationsAccessPoliciesSearchPolicyBindingsRequest',
        response_type_name='GoogleIamV3alphaSearchAccessPolicyBindingsResponse',
        supports_download=False,
    )

  class FoldersLocationsOperationsService(base_api.BaseApiService):
    """Service class for the folders_locations_operations resource."""

    _NAME = 'folders_locations_operations'

    def __init__(self, client):
      super(IamV3alpha.FoldersLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamFoldersLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/folders/{foldersId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.folders.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamFoldersLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class FoldersLocationsPolicyBindingsService(base_api.BaseApiService):
    """Service class for the folders_locations_policyBindings resource."""

    _NAME = 'folders_locations_policyBindings'

    def __init__(self, client):
      super(IamV3alpha.FoldersLocationsPolicyBindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a policy binding and returns a long-running operation. Callers will need the IAM permissions on both the policy and target. Once the binding is created, the policy is applied to the target.

      Args:
        request: (IamFoldersLocationsPolicyBindingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/folders/{foldersId}/locations/{locationsId}/policyBindings',
        http_method='POST',
        method_id='iam.folders.locations.policyBindings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['policyBindingId', 'validateOnly'],
        relative_path='v3alpha/{+parent}/policyBindings',
        request_field='googleIamV3alphaPolicyBinding',
        request_type_name='IamFoldersLocationsPolicyBindingsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a policy binding and returns a long-running operation. Callers will need the IAM permissions on both the policy and target. Once the binding is deleted, the policy no longer applies to the target.

      Args:
        request: (IamFoldersLocationsPolicyBindingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/folders/{foldersId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='DELETE',
        method_id='iam.folders.locations.policyBindings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamFoldersLocationsPolicyBindingsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a policy binding.

      Args:
        request: (IamFoldersLocationsPolicyBindingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaPolicyBinding) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/folders/{foldersId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='GET',
        method_id='iam.folders.locations.policyBindings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamFoldersLocationsPolicyBindingsGetRequest',
        response_type_name='GoogleIamV3alphaPolicyBinding',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists policy bindings.

      Args:
        request: (IamFoldersLocationsPolicyBindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaListPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/folders/{foldersId}/locations/{locationsId}/policyBindings',
        http_method='GET',
        method_id='iam.folders.locations.policyBindings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v3alpha/{+parent}/policyBindings',
        request_field='',
        request_type_name='IamFoldersLocationsPolicyBindingsListRequest',
        response_type_name='GoogleIamV3alphaListPolicyBindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a policy binding and returns a long-running operation. Callers will need the IAM permissions on the policy and target in the binding to update. Target and policy are immutable and cannot be updated.

      Args:
        request: (IamFoldersLocationsPolicyBindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/folders/{foldersId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='PATCH',
        method_id='iam.folders.locations.policyBindings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v3alpha/{+name}',
        request_field='googleIamV3alphaPolicyBinding',
        request_type_name='IamFoldersLocationsPolicyBindingsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SearchTargetPolicyBindings(self, request, global_params=None):
      r"""Search policy bindings by target. Returns all policy binding objects bound directly to target.

      Args:
        request: (IamFoldersLocationsPolicyBindingsSearchTargetPolicyBindingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaSearchTargetPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchTargetPolicyBindings')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchTargetPolicyBindings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/folders/{foldersId}/locations/{locationsId}/policyBindings:searchTargetPolicyBindings',
        http_method='GET',
        method_id='iam.folders.locations.policyBindings.searchTargetPolicyBindings',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'target'],
        relative_path='v3alpha/{+parent}/policyBindings:searchTargetPolicyBindings',
        request_field='',
        request_type_name='IamFoldersLocationsPolicyBindingsSearchTargetPolicyBindingsRequest',
        response_type_name='GoogleIamV3alphaSearchTargetPolicyBindingsResponse',
        supports_download=False,
    )

  class FoldersLocationsService(base_api.BaseApiService):
    """Service class for the folders_locations resource."""

    _NAME = 'folders_locations'

    def __init__(self, client):
      super(IamV3alpha.FoldersLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(IamV3alpha.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsLocationsAccessPoliciesService(base_api.BaseApiService):
    """Service class for the organizations_locations_accessPolicies resource."""

    _NAME = 'organizations_locations_accessPolicies'

    def __init__(self, client):
      super(IamV3alpha.OrganizationsLocationsAccessPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an access policy, and returns a long running operation.

      Args:
        request: (IamOrganizationsLocationsAccessPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/accessPolicies',
        http_method='POST',
        method_id='iam.organizations.locations.accessPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['accessPolicyId', 'validateOnly'],
        relative_path='v3alpha/{+parent}/accessPolicies',
        request_field='googleIamV3alphaAccessPolicy',
        request_type_name='IamOrganizationsLocationsAccessPoliciesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an access policy.

      Args:
        request: (IamOrganizationsLocationsAccessPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/accessPolicies/{accessPoliciesId}',
        http_method='DELETE',
        method_id='iam.organizations.locations.accessPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'force', 'validateOnly'],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamOrganizationsLocationsAccessPoliciesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an access policy.

      Args:
        request: (IamOrganizationsLocationsAccessPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaAccessPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/accessPolicies/{accessPoliciesId}',
        http_method='GET',
        method_id='iam.organizations.locations.accessPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamOrganizationsLocationsAccessPoliciesGetRequest',
        response_type_name='GoogleIamV3alphaAccessPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists access policies.

      Args:
        request: (IamOrganizationsLocationsAccessPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaListAccessPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/accessPolicies',
        http_method='GET',
        method_id='iam.organizations.locations.accessPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v3alpha/{+parent}/accessPolicies',
        request_field='',
        request_type_name='IamOrganizationsLocationsAccessPoliciesListRequest',
        response_type_name='GoogleIamV3alphaListAccessPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an access policy.

      Args:
        request: (IamOrganizationsLocationsAccessPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/accessPolicies/{accessPoliciesId}',
        http_method='PATCH',
        method_id='iam.organizations.locations.accessPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v3alpha/{+name}',
        request_field='googleIamV3alphaAccessPolicy',
        request_type_name='IamOrganizationsLocationsAccessPoliciesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SearchPolicyBindings(self, request, global_params=None):
      r"""Returns all policy bindings that bind a specific policy if a user has searchPolicyBindings permission on that policy.

      Args:
        request: (IamOrganizationsLocationsAccessPoliciesSearchPolicyBindingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaSearchAccessPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchPolicyBindings')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchPolicyBindings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/accessPolicies/{accessPoliciesId}:searchPolicyBindings',
        http_method='GET',
        method_id='iam.organizations.locations.accessPolicies.searchPolicyBindings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v3alpha/{+name}:searchPolicyBindings',
        request_field='',
        request_type_name='IamOrganizationsLocationsAccessPoliciesSearchPolicyBindingsRequest',
        response_type_name='GoogleIamV3alphaSearchAccessPolicyBindingsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_operations resource."""

    _NAME = 'organizations_locations_operations'

    def __init__(self, client):
      super(IamV3alpha.OrganizationsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamOrganizationsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.organizations.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamOrganizationsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class OrganizationsLocationsPolicyBindingsService(base_api.BaseApiService):
    """Service class for the organizations_locations_policyBindings resource."""

    _NAME = 'organizations_locations_policyBindings'

    def __init__(self, client):
      super(IamV3alpha.OrganizationsLocationsPolicyBindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a policy binding and returns a long-running operation. Callers will need the IAM permissions on both the policy and target. Once the binding is created, the policy is applied to the target.

      Args:
        request: (IamOrganizationsLocationsPolicyBindingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/policyBindings',
        http_method='POST',
        method_id='iam.organizations.locations.policyBindings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['policyBindingId', 'validateOnly'],
        relative_path='v3alpha/{+parent}/policyBindings',
        request_field='googleIamV3alphaPolicyBinding',
        request_type_name='IamOrganizationsLocationsPolicyBindingsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a policy binding and returns a long-running operation. Callers will need the IAM permissions on both the policy and target. Once the binding is deleted, the policy no longer applies to the target.

      Args:
        request: (IamOrganizationsLocationsPolicyBindingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='DELETE',
        method_id='iam.organizations.locations.policyBindings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamOrganizationsLocationsPolicyBindingsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a policy binding.

      Args:
        request: (IamOrganizationsLocationsPolicyBindingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaPolicyBinding) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='GET',
        method_id='iam.organizations.locations.policyBindings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamOrganizationsLocationsPolicyBindingsGetRequest',
        response_type_name='GoogleIamV3alphaPolicyBinding',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists policy bindings.

      Args:
        request: (IamOrganizationsLocationsPolicyBindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaListPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/policyBindings',
        http_method='GET',
        method_id='iam.organizations.locations.policyBindings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v3alpha/{+parent}/policyBindings',
        request_field='',
        request_type_name='IamOrganizationsLocationsPolicyBindingsListRequest',
        response_type_name='GoogleIamV3alphaListPolicyBindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a policy binding and returns a long-running operation. Callers will need the IAM permissions on the policy and target in the binding to update. Target and policy are immutable and cannot be updated.

      Args:
        request: (IamOrganizationsLocationsPolicyBindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='PATCH',
        method_id='iam.organizations.locations.policyBindings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v3alpha/{+name}',
        request_field='googleIamV3alphaPolicyBinding',
        request_type_name='IamOrganizationsLocationsPolicyBindingsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SearchTargetPolicyBindings(self, request, global_params=None):
      r"""Search policy bindings by target. Returns all policy binding objects bound directly to target.

      Args:
        request: (IamOrganizationsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaSearchTargetPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchTargetPolicyBindings')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchTargetPolicyBindings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/policyBindings:searchTargetPolicyBindings',
        http_method='GET',
        method_id='iam.organizations.locations.policyBindings.searchTargetPolicyBindings',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'target'],
        relative_path='v3alpha/{+parent}/policyBindings:searchTargetPolicyBindings',
        request_field='',
        request_type_name='IamOrganizationsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest',
        response_type_name='GoogleIamV3alphaSearchTargetPolicyBindingsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsPrincipalAccessBoundaryPoliciesService(base_api.BaseApiService):
    """Service class for the organizations_locations_principalAccessBoundaryPolicies resource."""

    _NAME = 'organizations_locations_principalAccessBoundaryPolicies'

    def __init__(self, client):
      super(IamV3alpha.OrganizationsLocationsPrincipalAccessBoundaryPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a principal access boundary policy, and returns a long running operation.

      Args:
        request: (IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/principalAccessBoundaryPolicies',
        http_method='POST',
        method_id='iam.organizations.locations.principalAccessBoundaryPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['principalAccessBoundaryPolicyId', 'validateOnly'],
        relative_path='v3alpha/{+parent}/principalAccessBoundaryPolicies',
        request_field='googleIamV3alphaPrincipalAccessBoundaryPolicy',
        request_type_name='IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a principal access boundary policy.

      Args:
        request: (IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/principalAccessBoundaryPolicies/{principalAccessBoundaryPoliciesId}',
        http_method='DELETE',
        method_id='iam.organizations.locations.principalAccessBoundaryPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'force', 'validateOnly'],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a principal access boundary policy.

      Args:
        request: (IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaPrincipalAccessBoundaryPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/principalAccessBoundaryPolicies/{principalAccessBoundaryPoliciesId}',
        http_method='GET',
        method_id='iam.organizations.locations.principalAccessBoundaryPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesGetRequest',
        response_type_name='GoogleIamV3alphaPrincipalAccessBoundaryPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists principal access boundary policies.

      Args:
        request: (IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaListPrincipalAccessBoundaryPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/principalAccessBoundaryPolicies',
        http_method='GET',
        method_id='iam.organizations.locations.principalAccessBoundaryPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v3alpha/{+parent}/principalAccessBoundaryPolicies',
        request_field='',
        request_type_name='IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesListRequest',
        response_type_name='GoogleIamV3alphaListPrincipalAccessBoundaryPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a principal access boundary policy.

      Args:
        request: (IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/principalAccessBoundaryPolicies/{principalAccessBoundaryPoliciesId}',
        http_method='PATCH',
        method_id='iam.organizations.locations.principalAccessBoundaryPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v3alpha/{+name}',
        request_field='googleIamV3alphaPrincipalAccessBoundaryPolicy',
        request_type_name='IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SearchPolicyBindings(self, request, global_params=None):
      r"""Returns all policy bindings that bind a specific policy if a user has searchPolicyBindings permission on that policy.

      Args:
        request: (IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesSearchPolicyBindingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaSearchPrincipalAccessBoundaryPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchPolicyBindings')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchPolicyBindings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/organizations/{organizationsId}/locations/{locationsId}/principalAccessBoundaryPolicies/{principalAccessBoundaryPoliciesId}:searchPolicyBindings',
        http_method='GET',
        method_id='iam.organizations.locations.principalAccessBoundaryPolicies.searchPolicyBindings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v3alpha/{+name}:searchPolicyBindings',
        request_field='',
        request_type_name='IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesSearchPolicyBindingsRequest',
        response_type_name='GoogleIamV3alphaSearchPrincipalAccessBoundaryPolicyBindingsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(IamV3alpha.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(IamV3alpha.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsAccessPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_accessPolicies resource."""

    _NAME = 'projects_locations_accessPolicies'

    def __init__(self, client):
      super(IamV3alpha.ProjectsLocationsAccessPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an access policy, and returns a long running operation.

      Args:
        request: (IamProjectsLocationsAccessPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/accessPolicies',
        http_method='POST',
        method_id='iam.projects.locations.accessPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['accessPolicyId', 'validateOnly'],
        relative_path='v3alpha/{+parent}/accessPolicies',
        request_field='googleIamV3alphaAccessPolicy',
        request_type_name='IamProjectsLocationsAccessPoliciesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an access policy.

      Args:
        request: (IamProjectsLocationsAccessPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/accessPolicies/{accessPoliciesId}',
        http_method='DELETE',
        method_id='iam.projects.locations.accessPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'force', 'validateOnly'],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsAccessPoliciesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an access policy.

      Args:
        request: (IamProjectsLocationsAccessPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaAccessPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/accessPolicies/{accessPoliciesId}',
        http_method='GET',
        method_id='iam.projects.locations.accessPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsAccessPoliciesGetRequest',
        response_type_name='GoogleIamV3alphaAccessPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists access policies.

      Args:
        request: (IamProjectsLocationsAccessPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaListAccessPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/accessPolicies',
        http_method='GET',
        method_id='iam.projects.locations.accessPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v3alpha/{+parent}/accessPolicies',
        request_field='',
        request_type_name='IamProjectsLocationsAccessPoliciesListRequest',
        response_type_name='GoogleIamV3alphaListAccessPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an access policy.

      Args:
        request: (IamProjectsLocationsAccessPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/accessPolicies/{accessPoliciesId}',
        http_method='PATCH',
        method_id='iam.projects.locations.accessPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v3alpha/{+name}',
        request_field='googleIamV3alphaAccessPolicy',
        request_type_name='IamProjectsLocationsAccessPoliciesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SearchPolicyBindings(self, request, global_params=None):
      r"""Returns all policy bindings that bind a specific policy if a user has searchPolicyBindings permission on that policy.

      Args:
        request: (IamProjectsLocationsAccessPoliciesSearchPolicyBindingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaSearchAccessPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchPolicyBindings')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchPolicyBindings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/accessPolicies/{accessPoliciesId}:searchPolicyBindings',
        http_method='GET',
        method_id='iam.projects.locations.accessPolicies.searchPolicyBindings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v3alpha/{+name}:searchPolicyBindings',
        request_field='',
        request_type_name='IamProjectsLocationsAccessPoliciesSearchPolicyBindingsRequest',
        response_type_name='GoogleIamV3alphaSearchAccessPolicyBindingsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(IamV3alpha.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsPolicyBindingsService(base_api.BaseApiService):
    """Service class for the projects_locations_policyBindings resource."""

    _NAME = 'projects_locations_policyBindings'

    def __init__(self, client):
      super(IamV3alpha.ProjectsLocationsPolicyBindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a policy binding and returns a long-running operation. Callers will need the IAM permissions on both the policy and target. Once the binding is created, the policy is applied to the target.

      Args:
        request: (IamProjectsLocationsPolicyBindingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyBindings',
        http_method='POST',
        method_id='iam.projects.locations.policyBindings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['policyBindingId', 'validateOnly'],
        relative_path='v3alpha/{+parent}/policyBindings',
        request_field='googleIamV3alphaPolicyBinding',
        request_type_name='IamProjectsLocationsPolicyBindingsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a policy binding and returns a long-running operation. Callers will need the IAM permissions on both the policy and target. Once the binding is deleted, the policy no longer applies to the target.

      Args:
        request: (IamProjectsLocationsPolicyBindingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='DELETE',
        method_id='iam.projects.locations.policyBindings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyBindingsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a policy binding.

      Args:
        request: (IamProjectsLocationsPolicyBindingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaPolicyBinding) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='GET',
        method_id='iam.projects.locations.policyBindings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyBindingsGetRequest',
        response_type_name='GoogleIamV3alphaPolicyBinding',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists policy bindings.

      Args:
        request: (IamProjectsLocationsPolicyBindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaListPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyBindings',
        http_method='GET',
        method_id='iam.projects.locations.policyBindings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v3alpha/{+parent}/policyBindings',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyBindingsListRequest',
        response_type_name='GoogleIamV3alphaListPolicyBindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a policy binding and returns a long-running operation. Callers will need the IAM permissions on the policy and target in the binding to update. Target and policy are immutable and cannot be updated.

      Args:
        request: (IamProjectsLocationsPolicyBindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='PATCH',
        method_id='iam.projects.locations.policyBindings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v3alpha/{+name}',
        request_field='googleIamV3alphaPolicyBinding',
        request_type_name='IamProjectsLocationsPolicyBindingsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SearchTargetPolicyBindings(self, request, global_params=None):
      r"""Search policy bindings by target. Returns all policy binding objects bound directly to target.

      Args:
        request: (IamProjectsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaSearchTargetPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchTargetPolicyBindings')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchTargetPolicyBindings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyBindings:searchTargetPolicyBindings',
        http_method='GET',
        method_id='iam.projects.locations.policyBindings.searchTargetPolicyBindings',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'target'],
        relative_path='v3alpha/{+parent}/policyBindings:searchTargetPolicyBindings',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest',
        response_type_name='GoogleIamV3alphaSearchTargetPolicyBindingsResponse',
        supports_download=False,
    )

  class ProjectsLocationsPolicyPortersTranslationsSourcePoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_policyPorters_translations_sourcePolicies resource."""

    _NAME = 'projects_locations_policyPorters_translations_sourcePolicies'

    def __init__(self, client):
      super(IamV3alpha.ProjectsLocationsPolicyPortersTranslationsSourcePoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single SourcePolicy.

      Args:
        request: (IamProjectsLocationsPolicyPortersTranslationsSourcePoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaSourcePolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters/{policyPortersId}/translations/{translationsId}/sourcePolicies/{sourcePoliciesId}',
        http_method='GET',
        method_id='iam.projects.locations.policyPorters.translations.sourcePolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyPortersTranslationsSourcePoliciesGetRequest',
        response_type_name='GoogleIamV3alphaSourcePolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SourcePolicies in a given project and location.

      Args:
        request: (IamProjectsLocationsPolicyPortersTranslationsSourcePoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaListSourcePoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters/{policyPortersId}/translations/{translationsId}/sourcePolicies',
        http_method='GET',
        method_id='iam.projects.locations.policyPorters.translations.sourcePolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v3alpha/{+parent}/sourcePolicies',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyPortersTranslationsSourcePoliciesListRequest',
        response_type_name='GoogleIamV3alphaListSourcePoliciesResponse',
        supports_download=False,
    )

  class ProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_policyPorters_translations_translatedPolicies resource."""

    _NAME = 'projects_locations_policyPorters_translations_translatedPolicies'

    def __init__(self, client):
      super(IamV3alpha.ProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Export(self, request, global_params=None):
      r"""Exports translated policies to the destination mentioned in request.

      Args:
        request: (IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters/{policyPortersId}/translations/{translationsId}/translatedPolicies:export',
        http_method='POST',
        method_id='iam.projects.locations.policyPorters.translations.translatedPolicies.export',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v3alpha/{+parent}/translatedPolicies:export',
        request_field='googleIamV3alphaExportTranslatedPoliciesRequest',
        request_type_name='IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesExportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single TranslatedPolicy.

      Args:
        request: (IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaTranslatedPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters/{policyPortersId}/translations/{translationsId}/translatedPolicies/{translatedPoliciesId}',
        http_method='GET',
        method_id='iam.projects.locations.policyPorters.translations.translatedPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesGetRequest',
        response_type_name='GoogleIamV3alphaTranslatedPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TranslatedPolicies in a given project and location.

      Args:
        request: (IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaListTranslatedPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters/{policyPortersId}/translations/{translationsId}/translatedPolicies',
        http_method='GET',
        method_id='iam.projects.locations.policyPorters.translations.translatedPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v3alpha/{+parent}/translatedPolicies',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesListRequest',
        response_type_name='GoogleIamV3alphaListTranslatedPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single TranslatedPolicy.

      Args:
        request: (IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters/{policyPortersId}/translations/{translationsId}/translatedPolicies/{translatedPoliciesId}',
        http_method='PATCH',
        method_id='iam.projects.locations.policyPorters.translations.translatedPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v3alpha/{+name}',
        request_field='googleIamV3alphaTranslatedPolicy',
        request_type_name='IamProjectsLocationsPolicyPortersTranslationsTranslatedPoliciesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsPolicyPortersTranslationsService(base_api.BaseApiService):
    """Service class for the projects_locations_policyPorters_translations resource."""

    _NAME = 'projects_locations_policyPorters_translations'

    def __init__(self, client):
      super(IamV3alpha.ProjectsLocationsPolicyPortersTranslationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Translation in a given Policy Porter.

      Args:
        request: (IamProjectsLocationsPolicyPortersTranslationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters/{policyPortersId}/translations',
        http_method='POST',
        method_id='iam.projects.locations.policyPorters.translations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'translationId'],
        relative_path='v3alpha/{+parent}/translations',
        request_field='googleIamV3alphaTranslation',
        request_type_name='IamProjectsLocationsPolicyPortersTranslationsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Translation.

      Args:
        request: (IamProjectsLocationsPolicyPortersTranslationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters/{policyPortersId}/translations/{translationsId}',
        http_method='DELETE',
        method_id='iam.projects.locations.policyPorters.translations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'requestId'],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyPortersTranslationsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Translation.

      Args:
        request: (IamProjectsLocationsPolicyPortersTranslationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaTranslation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters/{policyPortersId}/translations/{translationsId}',
        http_method='GET',
        method_id='iam.projects.locations.policyPorters.translations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyPortersTranslationsGetRequest',
        response_type_name='GoogleIamV3alphaTranslation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Translations in a given Policy Porter.

      Args:
        request: (IamProjectsLocationsPolicyPortersTranslationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaListTranslationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters/{policyPortersId}/translations',
        http_method='GET',
        method_id='iam.projects.locations.policyPorters.translations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v3alpha/{+parent}/translations',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyPortersTranslationsListRequest',
        response_type_name='GoogleIamV3alphaListTranslationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsPolicyPortersService(base_api.BaseApiService):
    """Service class for the projects_locations_policyPorters resource."""

    _NAME = 'projects_locations_policyPorters'

    def __init__(self, client):
      super(IamV3alpha.ProjectsLocationsPolicyPortersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new PolicyPorter in a given project and location.

      Args:
        request: (IamProjectsLocationsPolicyPortersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters',
        http_method='POST',
        method_id='iam.projects.locations.policyPorters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['policyPorterId', 'requestId'],
        relative_path='v3alpha/{+parent}/policyPorters',
        request_field='googleIamV3alphaPolicyPorter',
        request_type_name='IamProjectsLocationsPolicyPortersCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single PolicyPorter.

      Args:
        request: (IamProjectsLocationsPolicyPortersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters/{policyPortersId}',
        http_method='DELETE',
        method_id='iam.projects.locations.policyPorters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'requestId'],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyPortersDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single PolicyPorter.

      Args:
        request: (IamProjectsLocationsPolicyPortersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaPolicyPorter) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters/{policyPortersId}',
        http_method='GET',
        method_id='iam.projects.locations.policyPorters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3alpha/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyPortersGetRequest',
        response_type_name='GoogleIamV3alphaPolicyPorter',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists PolicyPorters in a given project and location.

      Args:
        request: (IamProjectsLocationsPolicyPortersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaListPolicyPortersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters',
        http_method='GET',
        method_id='iam.projects.locations.policyPorters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v3alpha/{+parent}/policyPorters',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyPortersListRequest',
        response_type_name='GoogleIamV3alphaListPolicyPortersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single PolicyPorter.

      Args:
        request: (IamProjectsLocationsPolicyPortersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3alpha/projects/{projectsId}/locations/{locationsId}/policyPorters/{policyPortersId}',
        http_method='PATCH',
        method_id='iam.projects.locations.policyPorters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v3alpha/{+name}',
        request_field='googleIamV3alphaPolicyPorter',
        request_type_name='IamProjectsLocationsPolicyPortersPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(IamV3alpha.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(IamV3alpha.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

  class SearchApplicablePoliciesService(base_api.BaseApiService):
    """Service class for the searchApplicablePolicies resource."""

    _NAME = 'searchApplicablePolicies'

    def __init__(self, client):
      super(IamV3alpha.SearchApplicablePoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Search(self, request, global_params=None):
      r"""Returns policies (along with the bindings that bind them) that apply to the specified target_query. This means the policies that are bound to the target or any of its ancestors. target_query can be a principal, a principalSet or in the future a resource.

      Args:
        request: (IamSearchApplicablePoliciesSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3alphaSearchApplicablePoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='iam.searchApplicablePolicies.search',
        ordered_params=[],
        path_params=[],
        query_params=['filter', 'pageSize', 'pageToken', 'targetQuery'],
        relative_path='v3alpha/searchApplicablePolicies:search',
        request_field='',
        request_type_name='IamSearchApplicablePoliciesSearchRequest',
        response_type_name='GoogleIamV3alphaSearchApplicablePoliciesResponse',
        supports_download=False,
    )
