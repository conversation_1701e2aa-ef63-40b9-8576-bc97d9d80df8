"""Generated client library for iam version v3."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.iam.v3 import iam_v3_messages as messages


class IamV3(base_api.BaseApiClient):
  """Generated client library for service iam version v3."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://iam.googleapis.com/'
  MTLS_BASE_URL = 'https://iam.mtls.googleapis.com/'

  _PACKAGE = 'iam'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v3'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'IamV3'
  _URL_VERSION = 'v3'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new iam handle."""
    url = url or self.BASE_URL
    super(IamV3, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.folders_locations_operations = self.FoldersLocationsOperationsService(self)
    self.folders_locations_policyBindings = self.FoldersLocationsPolicyBindingsService(self)
    self.folders_locations = self.FoldersLocationsService(self)
    self.folders = self.FoldersService(self)
    self.organizations_locations_operations = self.OrganizationsLocationsOperationsService(self)
    self.organizations_locations_policyBindings = self.OrganizationsLocationsPolicyBindingsService(self)
    self.organizations_locations_principalAccessBoundaryPolicies = self.OrganizationsLocationsPrincipalAccessBoundaryPoliciesService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_policyBindings = self.ProjectsLocationsPolicyBindingsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class FoldersLocationsOperationsService(base_api.BaseApiService):
    """Service class for the folders_locations_operations resource."""

    _NAME = 'folders_locations_operations'

    def __init__(self, client):
      super(IamV3.FoldersLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamFoldersLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/folders/{foldersId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.folders.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='IamFoldersLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class FoldersLocationsPolicyBindingsService(base_api.BaseApiService):
    """Service class for the folders_locations_policyBindings resource."""

    _NAME = 'folders_locations_policyBindings'

    def __init__(self, client):
      super(IamV3.FoldersLocationsPolicyBindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a policy binding and returns a long-running operation. Callers will need the IAM permissions on both the policy and target. Once the binding is created, the policy is applied to the target.

      Args:
        request: (IamFoldersLocationsPolicyBindingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/folders/{foldersId}/locations/{locationsId}/policyBindings',
        http_method='POST',
        method_id='iam.folders.locations.policyBindings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['policyBindingId', 'validateOnly'],
        relative_path='v3/{+parent}/policyBindings',
        request_field='googleIamV3PolicyBinding',
        request_type_name='IamFoldersLocationsPolicyBindingsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a policy binding and returns a long-running operation. Callers will need the IAM permissions on both the policy and target. Once the binding is deleted, the policy no longer applies to the target.

      Args:
        request: (IamFoldersLocationsPolicyBindingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/folders/{foldersId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='DELETE',
        method_id='iam.folders.locations.policyBindings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='IamFoldersLocationsPolicyBindingsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a policy binding.

      Args:
        request: (IamFoldersLocationsPolicyBindingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3PolicyBinding) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/folders/{foldersId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='GET',
        method_id='iam.folders.locations.policyBindings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='IamFoldersLocationsPolicyBindingsGetRequest',
        response_type_name='GoogleIamV3PolicyBinding',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists policy bindings.

      Args:
        request: (IamFoldersLocationsPolicyBindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3ListPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/folders/{foldersId}/locations/{locationsId}/policyBindings',
        http_method='GET',
        method_id='iam.folders.locations.policyBindings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v3/{+parent}/policyBindings',
        request_field='',
        request_type_name='IamFoldersLocationsPolicyBindingsListRequest',
        response_type_name='GoogleIamV3ListPolicyBindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a policy binding and returns a long-running operation. Callers will need the IAM permissions on the policy and target in the binding to update. Target and policy are immutable and cannot be updated.

      Args:
        request: (IamFoldersLocationsPolicyBindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/folders/{foldersId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='PATCH',
        method_id='iam.folders.locations.policyBindings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v3/{+name}',
        request_field='googleIamV3PolicyBinding',
        request_type_name='IamFoldersLocationsPolicyBindingsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SearchTargetPolicyBindings(self, request, global_params=None):
      r"""Search policy bindings by target. Returns all policy binding objects bound directly to target.

      Args:
        request: (IamFoldersLocationsPolicyBindingsSearchTargetPolicyBindingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3SearchTargetPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchTargetPolicyBindings')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchTargetPolicyBindings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/folders/{foldersId}/locations/{locationsId}/policyBindings:searchTargetPolicyBindings',
        http_method='GET',
        method_id='iam.folders.locations.policyBindings.searchTargetPolicyBindings',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'target'],
        relative_path='v3/{+parent}/policyBindings:searchTargetPolicyBindings',
        request_field='',
        request_type_name='IamFoldersLocationsPolicyBindingsSearchTargetPolicyBindingsRequest',
        response_type_name='GoogleIamV3SearchTargetPolicyBindingsResponse',
        supports_download=False,
    )

  class FoldersLocationsService(base_api.BaseApiService):
    """Service class for the folders_locations resource."""

    _NAME = 'folders_locations'

    def __init__(self, client):
      super(IamV3.FoldersLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(IamV3.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_operations resource."""

    _NAME = 'organizations_locations_operations'

    def __init__(self, client):
      super(IamV3.OrganizationsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamOrganizationsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.organizations.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='IamOrganizationsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class OrganizationsLocationsPolicyBindingsService(base_api.BaseApiService):
    """Service class for the organizations_locations_policyBindings resource."""

    _NAME = 'organizations_locations_policyBindings'

    def __init__(self, client):
      super(IamV3.OrganizationsLocationsPolicyBindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a policy binding and returns a long-running operation. Callers will need the IAM permissions on both the policy and target. Once the binding is created, the policy is applied to the target.

      Args:
        request: (IamOrganizationsLocationsPolicyBindingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/organizations/{organizationsId}/locations/{locationsId}/policyBindings',
        http_method='POST',
        method_id='iam.organizations.locations.policyBindings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['policyBindingId', 'validateOnly'],
        relative_path='v3/{+parent}/policyBindings',
        request_field='googleIamV3PolicyBinding',
        request_type_name='IamOrganizationsLocationsPolicyBindingsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a policy binding and returns a long-running operation. Callers will need the IAM permissions on both the policy and target. Once the binding is deleted, the policy no longer applies to the target.

      Args:
        request: (IamOrganizationsLocationsPolicyBindingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/organizations/{organizationsId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='DELETE',
        method_id='iam.organizations.locations.policyBindings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='IamOrganizationsLocationsPolicyBindingsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a policy binding.

      Args:
        request: (IamOrganizationsLocationsPolicyBindingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3PolicyBinding) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/organizations/{organizationsId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='GET',
        method_id='iam.organizations.locations.policyBindings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='IamOrganizationsLocationsPolicyBindingsGetRequest',
        response_type_name='GoogleIamV3PolicyBinding',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists policy bindings.

      Args:
        request: (IamOrganizationsLocationsPolicyBindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3ListPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/organizations/{organizationsId}/locations/{locationsId}/policyBindings',
        http_method='GET',
        method_id='iam.organizations.locations.policyBindings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v3/{+parent}/policyBindings',
        request_field='',
        request_type_name='IamOrganizationsLocationsPolicyBindingsListRequest',
        response_type_name='GoogleIamV3ListPolicyBindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a policy binding and returns a long-running operation. Callers will need the IAM permissions on the policy and target in the binding to update. Target and policy are immutable and cannot be updated.

      Args:
        request: (IamOrganizationsLocationsPolicyBindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/organizations/{organizationsId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='PATCH',
        method_id='iam.organizations.locations.policyBindings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v3/{+name}',
        request_field='googleIamV3PolicyBinding',
        request_type_name='IamOrganizationsLocationsPolicyBindingsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SearchTargetPolicyBindings(self, request, global_params=None):
      r"""Search policy bindings by target. Returns all policy binding objects bound directly to target.

      Args:
        request: (IamOrganizationsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3SearchTargetPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchTargetPolicyBindings')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchTargetPolicyBindings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/organizations/{organizationsId}/locations/{locationsId}/policyBindings:searchTargetPolicyBindings',
        http_method='GET',
        method_id='iam.organizations.locations.policyBindings.searchTargetPolicyBindings',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'target'],
        relative_path='v3/{+parent}/policyBindings:searchTargetPolicyBindings',
        request_field='',
        request_type_name='IamOrganizationsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest',
        response_type_name='GoogleIamV3SearchTargetPolicyBindingsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsPrincipalAccessBoundaryPoliciesService(base_api.BaseApiService):
    """Service class for the organizations_locations_principalAccessBoundaryPolicies resource."""

    _NAME = 'organizations_locations_principalAccessBoundaryPolicies'

    def __init__(self, client):
      super(IamV3.OrganizationsLocationsPrincipalAccessBoundaryPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a principal access boundary policy, and returns a long running operation.

      Args:
        request: (IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/organizations/{organizationsId}/locations/{locationsId}/principalAccessBoundaryPolicies',
        http_method='POST',
        method_id='iam.organizations.locations.principalAccessBoundaryPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['principalAccessBoundaryPolicyId', 'validateOnly'],
        relative_path='v3/{+parent}/principalAccessBoundaryPolicies',
        request_field='googleIamV3PrincipalAccessBoundaryPolicy',
        request_type_name='IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a principal access boundary policy.

      Args:
        request: (IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/organizations/{organizationsId}/locations/{locationsId}/principalAccessBoundaryPolicies/{principalAccessBoundaryPoliciesId}',
        http_method='DELETE',
        method_id='iam.organizations.locations.principalAccessBoundaryPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'force', 'validateOnly'],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a principal access boundary policy.

      Args:
        request: (IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3PrincipalAccessBoundaryPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/organizations/{organizationsId}/locations/{locationsId}/principalAccessBoundaryPolicies/{principalAccessBoundaryPoliciesId}',
        http_method='GET',
        method_id='iam.organizations.locations.principalAccessBoundaryPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesGetRequest',
        response_type_name='GoogleIamV3PrincipalAccessBoundaryPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists principal access boundary policies.

      Args:
        request: (IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3ListPrincipalAccessBoundaryPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/organizations/{organizationsId}/locations/{locationsId}/principalAccessBoundaryPolicies',
        http_method='GET',
        method_id='iam.organizations.locations.principalAccessBoundaryPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v3/{+parent}/principalAccessBoundaryPolicies',
        request_field='',
        request_type_name='IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesListRequest',
        response_type_name='GoogleIamV3ListPrincipalAccessBoundaryPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a principal access boundary policy.

      Args:
        request: (IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/organizations/{organizationsId}/locations/{locationsId}/principalAccessBoundaryPolicies/{principalAccessBoundaryPoliciesId}',
        http_method='PATCH',
        method_id='iam.organizations.locations.principalAccessBoundaryPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v3/{+name}',
        request_field='googleIamV3PrincipalAccessBoundaryPolicy',
        request_type_name='IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SearchPolicyBindings(self, request, global_params=None):
      r"""Returns all policy bindings that bind a specific policy if a user has searchPolicyBindings permission on that policy.

      Args:
        request: (IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesSearchPolicyBindingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3SearchPrincipalAccessBoundaryPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchPolicyBindings')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchPolicyBindings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/organizations/{organizationsId}/locations/{locationsId}/principalAccessBoundaryPolicies/{principalAccessBoundaryPoliciesId}:searchPolicyBindings',
        http_method='GET',
        method_id='iam.organizations.locations.principalAccessBoundaryPolicies.searchPolicyBindings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v3/{+name}:searchPolicyBindings',
        request_field='',
        request_type_name='IamOrganizationsLocationsPrincipalAccessBoundaryPoliciesSearchPolicyBindingsRequest',
        response_type_name='GoogleIamV3SearchPrincipalAccessBoundaryPolicyBindingsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(IamV3.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(IamV3.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(IamV3.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsPolicyBindingsService(base_api.BaseApiService):
    """Service class for the projects_locations_policyBindings resource."""

    _NAME = 'projects_locations_policyBindings'

    def __init__(self, client):
      super(IamV3.ProjectsLocationsPolicyBindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a policy binding and returns a long-running operation. Callers will need the IAM permissions on both the policy and target. Once the binding is created, the policy is applied to the target.

      Args:
        request: (IamProjectsLocationsPolicyBindingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/locations/{locationsId}/policyBindings',
        http_method='POST',
        method_id='iam.projects.locations.policyBindings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['policyBindingId', 'validateOnly'],
        relative_path='v3/{+parent}/policyBindings',
        request_field='googleIamV3PolicyBinding',
        request_type_name='IamProjectsLocationsPolicyBindingsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a policy binding and returns a long-running operation. Callers will need the IAM permissions on both the policy and target. Once the binding is deleted, the policy no longer applies to the target.

      Args:
        request: (IamProjectsLocationsPolicyBindingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='DELETE',
        method_id='iam.projects.locations.policyBindings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyBindingsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a policy binding.

      Args:
        request: (IamProjectsLocationsPolicyBindingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3PolicyBinding) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='GET',
        method_id='iam.projects.locations.policyBindings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyBindingsGetRequest',
        response_type_name='GoogleIamV3PolicyBinding',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists policy bindings.

      Args:
        request: (IamProjectsLocationsPolicyBindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3ListPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/locations/{locationsId}/policyBindings',
        http_method='GET',
        method_id='iam.projects.locations.policyBindings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v3/{+parent}/policyBindings',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyBindingsListRequest',
        response_type_name='GoogleIamV3ListPolicyBindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a policy binding and returns a long-running operation. Callers will need the IAM permissions on the policy and target in the binding to update. Target and policy are immutable and cannot be updated.

      Args:
        request: (IamProjectsLocationsPolicyBindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/locations/{locationsId}/policyBindings/{policyBindingsId}',
        http_method='PATCH',
        method_id='iam.projects.locations.policyBindings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v3/{+name}',
        request_field='googleIamV3PolicyBinding',
        request_type_name='IamProjectsLocationsPolicyBindingsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SearchTargetPolicyBindings(self, request, global_params=None):
      r"""Search policy bindings by target. Returns all policy binding objects bound directly to target.

      Args:
        request: (IamProjectsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV3SearchTargetPolicyBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchTargetPolicyBindings')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchTargetPolicyBindings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/locations/{locationsId}/policyBindings:searchTargetPolicyBindings',
        http_method='GET',
        method_id='iam.projects.locations.policyBindings.searchTargetPolicyBindings',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'target'],
        relative_path='v3/{+parent}/policyBindings:searchTargetPolicyBindings',
        request_field='',
        request_type_name='IamProjectsLocationsPolicyBindingsSearchTargetPolicyBindingsRequest',
        response_type_name='GoogleIamV3SearchTargetPolicyBindingsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(IamV3.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(IamV3.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
