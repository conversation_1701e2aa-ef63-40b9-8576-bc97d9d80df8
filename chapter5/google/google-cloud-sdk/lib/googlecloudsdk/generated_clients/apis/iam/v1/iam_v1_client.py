"""Generated client library for iam version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.iam.v1 import iam_v1_messages as messages


class IamV1(base_api.BaseApiClient):
  """Generated client library for service iam version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://iam.googleapis.com/'
  MTLS_BASE_URL = 'https://iam.mtls.googleapis.com/'

  _PACKAGE = 'iam'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'IamV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new iam handle."""
    url = url or self.BASE_URL
    super(IamV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.iamPolicies = self.IamPoliciesService(self)
    self.locations_workforcePools_installedApps = self.LocationsWorkforcePoolsInstalledAppsService(self)
    self.locations_workforcePools_operations = self.LocationsWorkforcePoolsOperationsService(self)
    self.locations_workforcePools_providers_keys_operations = self.LocationsWorkforcePoolsProvidersKeysOperationsService(self)
    self.locations_workforcePools_providers_keys = self.LocationsWorkforcePoolsProvidersKeysService(self)
    self.locations_workforcePools_providers_operations = self.LocationsWorkforcePoolsProvidersOperationsService(self)
    self.locations_workforcePools_providers_scimTenants_tokens = self.LocationsWorkforcePoolsProvidersScimTenantsTokensService(self)
    self.locations_workforcePools_providers_scimTenants = self.LocationsWorkforcePoolsProvidersScimTenantsService(self)
    self.locations_workforcePools_providers = self.LocationsWorkforcePoolsProvidersService(self)
    self.locations_workforcePools_subjects_operations = self.LocationsWorkforcePoolsSubjectsOperationsService(self)
    self.locations_workforcePools_subjects = self.LocationsWorkforcePoolsSubjectsService(self)
    self.locations_workforcePools = self.LocationsWorkforcePoolsService(self)
    self.locations = self.LocationsService(self)
    self.organizations_roles = self.OrganizationsRolesService(self)
    self.organizations = self.OrganizationsService(self)
    self.permissions = self.PermissionsService(self)
    self.projects_locations_oauthClients_credentials = self.ProjectsLocationsOauthClientsCredentialsService(self)
    self.projects_locations_oauthClients = self.ProjectsLocationsOauthClientsService(self)
    self.projects_locations_workloadIdentityPools_namespaces_managedIdentities_operations = self.ProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesOperationsService(self)
    self.projects_locations_workloadIdentityPools_namespaces_managedIdentities_workloadSources_operations = self.ProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesOperationsService(self)
    self.projects_locations_workloadIdentityPools_namespaces_managedIdentities_workloadSources = self.ProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesService(self)
    self.projects_locations_workloadIdentityPools_namespaces_managedIdentities = self.ProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesService(self)
    self.projects_locations_workloadIdentityPools_namespaces_operations = self.ProjectsLocationsWorkloadIdentityPoolsNamespacesOperationsService(self)
    self.projects_locations_workloadIdentityPools_namespaces_workloadSources_operations = self.ProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesOperationsService(self)
    self.projects_locations_workloadIdentityPools_namespaces_workloadSources = self.ProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesService(self)
    self.projects_locations_workloadIdentityPools_namespaces = self.ProjectsLocationsWorkloadIdentityPoolsNamespacesService(self)
    self.projects_locations_workloadIdentityPools_operations = self.ProjectsLocationsWorkloadIdentityPoolsOperationsService(self)
    self.projects_locations_workloadIdentityPools_providers_keys_operations = self.ProjectsLocationsWorkloadIdentityPoolsProvidersKeysOperationsService(self)
    self.projects_locations_workloadIdentityPools_providers_keys = self.ProjectsLocationsWorkloadIdentityPoolsProvidersKeysService(self)
    self.projects_locations_workloadIdentityPools_providers_operations = self.ProjectsLocationsWorkloadIdentityPoolsProvidersOperationsService(self)
    self.projects_locations_workloadIdentityPools_providers = self.ProjectsLocationsWorkloadIdentityPoolsProvidersService(self)
    self.projects_locations_workloadIdentityPools = self.ProjectsLocationsWorkloadIdentityPoolsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects_roles = self.ProjectsRolesService(self)
    self.projects_serviceAccounts_identityBindings = self.ProjectsServiceAccountsIdentityBindingsService(self)
    self.projects_serviceAccounts_keys = self.ProjectsServiceAccountsKeysService(self)
    self.projects_serviceAccounts = self.ProjectsServiceAccountsService(self)
    self.projects = self.ProjectsService(self)
    self.roles = self.RolesService(self)

  class IamPoliciesService(base_api.BaseApiService):
    """Service class for the iamPolicies resource."""

    _NAME = 'iamPolicies'

    def __init__(self, client):
      super(IamV1.IamPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def LintPolicy(self, request, global_params=None):
      r"""Lints, or validates, an IAM policy. Currently checks the google.iam.v1.Binding.condition field, which contains a condition expression for a role binding. Successful calls to this method always return an HTTP `200 OK` status code, even if the linter detects an issue in the IAM policy.

      Args:
        request: (LintPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LintPolicyResponse) The response message.
      """
      config = self.GetMethodConfig('LintPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    LintPolicy.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='iam.iamPolicies.lintPolicy',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1/iamPolicies:lintPolicy',
        request_field='<request>',
        request_type_name='LintPolicyRequest',
        response_type_name='LintPolicyResponse',
        supports_download=False,
    )

    def QueryAuditableServices(self, request, global_params=None):
      r"""Returns a list of services that allow you to opt into audit logs that are not generated by default. To learn more about audit logs, see the [Logging documentation](https://cloud.google.com/logging/docs/audit).

      Args:
        request: (QueryAuditableServicesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QueryAuditableServicesResponse) The response message.
      """
      config = self.GetMethodConfig('QueryAuditableServices')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryAuditableServices.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='iam.iamPolicies.queryAuditableServices',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1/iamPolicies:queryAuditableServices',
        request_field='<request>',
        request_type_name='QueryAuditableServicesRequest',
        response_type_name='QueryAuditableServicesResponse',
        supports_download=False,
    )

  class LocationsWorkforcePoolsInstalledAppsService(base_api.BaseApiService):
    """Service class for the locations_workforcePools_installedApps resource."""

    _NAME = 'locations_workforcePools_installedApps'

    def __init__(self, client):
      super(IamV1.LocationsWorkforcePoolsInstalledAppsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new WorkforcePoolInstalledApp in a WorkforcePool. You cannot reuse the name of a deleted workforce pool installed app until 30 days after deletion.

      Args:
        request: (IamLocationsWorkforcePoolsInstalledAppsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolInstalledApp) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/installedApps',
        http_method='POST',
        method_id='iam.locations.workforcePools.installedApps.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['workforcePoolInstalledAppId'],
        relative_path='v1/{+parent}/installedApps',
        request_field='workforcePoolInstalledApp',
        request_type_name='IamLocationsWorkforcePoolsInstalledAppsCreateRequest',
        response_type_name='WorkforcePoolInstalledApp',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a WorkforcePoolInstalledApp. You can undelete a workforce pool installed app for 30 days. After 30 days, deletion is permanent. You cannot update deleted workforce pool installed apps. However, you can view and list them.

      Args:
        request: (IamLocationsWorkforcePoolsInstalledAppsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolInstalledApp) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/installedApps/{installedAppsId}',
        http_method='DELETE',
        method_id='iam.locations.workforcePools.installedApps.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsInstalledAppsDeleteRequest',
        response_type_name='WorkforcePoolInstalledApp',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an individual WorkforcePoolInstalledApp.

      Args:
        request: (IamLocationsWorkforcePoolsInstalledAppsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolInstalledApp) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/installedApps/{installedAppsId}',
        http_method='GET',
        method_id='iam.locations.workforcePools.installedApps.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsInstalledAppsGetRequest',
        response_type_name='WorkforcePoolInstalledApp',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all non-deleted WorkforcePoolInstalledApps in a WorkforcePool. If `show_deleted` is set to `true`, then deleted installed apps are also listed.

      Args:
        request: (IamLocationsWorkforcePoolsInstalledAppsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkforcePoolInstalledAppsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/installedApps',
        http_method='GET',
        method_id='iam.locations.workforcePools.installedApps.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v1/{+parent}/installedApps',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsInstalledAppsListRequest',
        response_type_name='ListWorkforcePoolInstalledAppsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing WorkforcePoolInstalledApp.

      Args:
        request: (IamLocationsWorkforcePoolsInstalledAppsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolInstalledApp) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/installedApps/{installedAppsId}',
        http_method='PATCH',
        method_id='iam.locations.workforcePools.installedApps.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='workforcePoolInstalledApp',
        request_type_name='IamLocationsWorkforcePoolsInstalledAppsPatchRequest',
        response_type_name='WorkforcePoolInstalledApp',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a WorkforcePoolInstalledApp, as long as it was deleted fewer than 30 days ago.

      Args:
        request: (IamLocationsWorkforcePoolsInstalledAppsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolInstalledApp) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/installedApps/{installedAppsId}:undelete',
        http_method='POST',
        method_id='iam.locations.workforcePools.installedApps.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteWorkforcePoolInstalledAppRequest',
        request_type_name='IamLocationsWorkforcePoolsInstalledAppsUndeleteRequest',
        response_type_name='WorkforcePoolInstalledApp',
        supports_download=False,
    )

  class LocationsWorkforcePoolsOperationsService(base_api.BaseApiService):
    """Service class for the locations_workforcePools_operations resource."""

    _NAME = 'locations_workforcePools_operations'

    def __init__(self, client):
      super(IamV1.LocationsWorkforcePoolsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamLocationsWorkforcePoolsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.locations.workforcePools.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class LocationsWorkforcePoolsProvidersKeysOperationsService(base_api.BaseApiService):
    """Service class for the locations_workforcePools_providers_keys_operations resource."""

    _NAME = 'locations_workforcePools_providers_keys_operations'

    def __init__(self, client):
      super(IamV1.LocationsWorkforcePoolsProvidersKeysOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersKeysOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/keys/{keysId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.locations.workforcePools.providers.keys.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsProvidersKeysOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class LocationsWorkforcePoolsProvidersKeysService(base_api.BaseApiService):
    """Service class for the locations_workforcePools_providers_keys resource."""

    _NAME = 'locations_workforcePools_providers_keys'

    def __init__(self, client):
      super(IamV1.LocationsWorkforcePoolsProvidersKeysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new WorkforcePoolProviderKey in a WorkforcePoolProvider.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersKeysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/keys',
        http_method='POST',
        method_id='iam.locations.workforcePools.providers.keys.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['workforcePoolProviderKeyId'],
        relative_path='v1/{+parent}/keys',
        request_field='workforcePoolProviderKey',
        request_type_name='IamLocationsWorkforcePoolsProvidersKeysCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a WorkforcePoolProviderKey. You can undelete a key for 30 days. After 30 days, deletion is permanent.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersKeysDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/keys/{keysId}',
        http_method='DELETE',
        method_id='iam.locations.workforcePools.providers.keys.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsProvidersKeysDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a WorkforcePoolProviderKey.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersKeysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolProviderKey) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/keys/{keysId}',
        http_method='GET',
        method_id='iam.locations.workforcePools.providers.keys.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsProvidersKeysGetRequest',
        response_type_name='WorkforcePoolProviderKey',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all non-deleted WorkforcePoolProviderKeys in a WorkforcePoolProvider. If `show_deleted` is set to `true`, then deleted keys are also listed.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersKeysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkforcePoolProviderKeysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/keys',
        http_method='GET',
        method_id='iam.locations.workforcePools.providers.keys.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v1/{+parent}/keys',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsProvidersKeysListRequest',
        response_type_name='ListWorkforcePoolProviderKeysResponse',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a WorkforcePoolProviderKey, as long as it was deleted fewer than 30 days ago.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersKeysUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/keys/{keysId}:undelete',
        http_method='POST',
        method_id='iam.locations.workforcePools.providers.keys.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteWorkforcePoolProviderKeyRequest',
        request_type_name='IamLocationsWorkforcePoolsProvidersKeysUndeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class LocationsWorkforcePoolsProvidersOperationsService(base_api.BaseApiService):
    """Service class for the locations_workforcePools_providers_operations resource."""

    _NAME = 'locations_workforcePools_providers_operations'

    def __init__(self, client):
      super(IamV1.LocationsWorkforcePoolsProvidersOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.locations.workforcePools.providers.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsProvidersOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class LocationsWorkforcePoolsProvidersScimTenantsTokensService(base_api.BaseApiService):
    """Service class for the locations_workforcePools_providers_scimTenants_tokens resource."""

    _NAME = 'locations_workforcePools_providers_scimTenants_tokens'

    def __init__(self, client):
      super(IamV1.LocationsWorkforcePoolsProvidersScimTenantsTokensService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new WorkforcePoolProviderScimToken in a WorkforcePoolProviderScimTenant. You cannot reuse the name of a deleted SCIM token until 30 days after deletion.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersScimTenantsTokensCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolProviderScimToken) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/scimTenants/{scimTenantsId}/tokens',
        http_method='POST',
        method_id='iam.locations.workforcePools.providers.scimTenants.tokens.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['workforcePoolProviderScimTokenId'],
        relative_path='v1/{+parent}/tokens',
        request_field='workforcePoolProviderScimToken',
        request_type_name='IamLocationsWorkforcePoolsProvidersScimTenantsTokensCreateRequest',
        response_type_name='WorkforcePoolProviderScimToken',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a WorkforcePoolProviderScimToken. You can undelete a scim token for 30 days. After 30 days, deletion is permanent. You cannot update deleted scim tokens. However, you can view and list them.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersScimTenantsTokensDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolProviderScimToken) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/scimTenants/{scimTenantsId}/tokens/{tokensId}',
        http_method='DELETE',
        method_id='iam.locations.workforcePools.providers.scimTenants.tokens.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsProvidersScimTenantsTokensDeleteRequest',
        response_type_name='WorkforcePoolProviderScimToken',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an individual WorkforcePoolProviderScimToken.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersScimTenantsTokensGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolProviderScimToken) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/scimTenants/{scimTenantsId}/tokens/{tokensId}',
        http_method='GET',
        method_id='iam.locations.workforcePools.providers.scimTenants.tokens.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsProvidersScimTenantsTokensGetRequest',
        response_type_name='WorkforcePoolProviderScimToken',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all non-deleted WorkforcePoolProviderScimTokenss in a WorkforcePoolProviderScimTenant. If `show_deleted` is set to `true`, then deleted SCIM tokens are also listed.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersScimTenantsTokensListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkforcePoolProviderScimTokensResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/scimTenants/{scimTenantsId}/tokens',
        http_method='GET',
        method_id='iam.locations.workforcePools.providers.scimTenants.tokens.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v1/{+parent}/tokens',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsProvidersScimTenantsTokensListRequest',
        response_type_name='ListWorkforcePoolProviderScimTokensResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing WorkforcePoolProviderScimToken.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersScimTenantsTokensPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolProviderScimToken) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/scimTenants/{scimTenantsId}/tokens/{tokensId}',
        http_method='PATCH',
        method_id='iam.locations.workforcePools.providers.scimTenants.tokens.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='workforcePoolProviderScimToken',
        request_type_name='IamLocationsWorkforcePoolsProvidersScimTenantsTokensPatchRequest',
        response_type_name='WorkforcePoolProviderScimToken',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a WorkforcePoolProviderScimToken, as long as it was deleted fewer than 30 days ago.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersScimTenantsTokensUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolProviderScimToken) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/scimTenants/{scimTenantsId}/tokens/{tokensId}:undelete',
        http_method='POST',
        method_id='iam.locations.workforcePools.providers.scimTenants.tokens.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteWorkforcePoolProviderScimTokenRequest',
        request_type_name='IamLocationsWorkforcePoolsProvidersScimTenantsTokensUndeleteRequest',
        response_type_name='WorkforcePoolProviderScimToken',
        supports_download=False,
    )

  class LocationsWorkforcePoolsProvidersScimTenantsService(base_api.BaseApiService):
    """Service class for the locations_workforcePools_providers_scimTenants resource."""

    _NAME = 'locations_workforcePools_providers_scimTenants'

    def __init__(self, client):
      super(IamV1.LocationsWorkforcePoolsProvidersScimTenantsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new WorkforcePoolProviderScimTenant in a WorkforcePoolProvider. You cannot reuse the name of a deleted scim tenant until 30 days after deletion.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersScimTenantsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolProviderScimTenant) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/scimTenants',
        http_method='POST',
        method_id='iam.locations.workforcePools.providers.scimTenants.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['workforcePoolProviderScimTenantId'],
        relative_path='v1/{+parent}/scimTenants',
        request_field='workforcePoolProviderScimTenant',
        request_type_name='IamLocationsWorkforcePoolsProvidersScimTenantsCreateRequest',
        response_type_name='WorkforcePoolProviderScimTenant',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a WorkforcePoolProviderScimTenant. You can undelete a scim tenant for 30 days. After 30 days, deletion is permanent. You cannot update deleted scim tenants. However, you can view and list them.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersScimTenantsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolProviderScimTenant) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/scimTenants/{scimTenantsId}',
        http_method='DELETE',
        method_id='iam.locations.workforcePools.providers.scimTenants.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsProvidersScimTenantsDeleteRequest',
        response_type_name='WorkforcePoolProviderScimTenant',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an individual WorkforcePoolProviderScimTenant.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersScimTenantsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolProviderScimTenant) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/scimTenants/{scimTenantsId}',
        http_method='GET',
        method_id='iam.locations.workforcePools.providers.scimTenants.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsProvidersScimTenantsGetRequest',
        response_type_name='WorkforcePoolProviderScimTenant',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all non-deleted WorkforcePoolProviderScimTenants in a WorkforcePoolProvider. If `show_deleted` is set to `true`, then deleted scim tenants are also listed.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersScimTenantsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkforcePoolProviderScimTenantsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/scimTenants',
        http_method='GET',
        method_id='iam.locations.workforcePools.providers.scimTenants.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v1/{+parent}/scimTenants',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsProvidersScimTenantsListRequest',
        response_type_name='ListWorkforcePoolProviderScimTenantsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing WorkforcePoolProviderScimTenant.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersScimTenantsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolProviderScimTenant) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/scimTenants/{scimTenantsId}',
        http_method='PATCH',
        method_id='iam.locations.workforcePools.providers.scimTenants.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='workforcePoolProviderScimTenant',
        request_type_name='IamLocationsWorkforcePoolsProvidersScimTenantsPatchRequest',
        response_type_name='WorkforcePoolProviderScimTenant',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a WorkforcePoolProviderScimTenant, as long as it was deleted fewer than 30 days ago.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersScimTenantsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolProviderScimTenant) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}/scimTenants/{scimTenantsId}:undelete',
        http_method='POST',
        method_id='iam.locations.workforcePools.providers.scimTenants.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteWorkforcePoolProviderScimTenantRequest',
        request_type_name='IamLocationsWorkforcePoolsProvidersScimTenantsUndeleteRequest',
        response_type_name='WorkforcePoolProviderScimTenant',
        supports_download=False,
    )

  class LocationsWorkforcePoolsProvidersService(base_api.BaseApiService):
    """Service class for the locations_workforcePools_providers resource."""

    _NAME = 'locations_workforcePools_providers'

    def __init__(self, client):
      super(IamV1.LocationsWorkforcePoolsProvidersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new WorkforcePoolProvider in a WorkforcePool. You cannot reuse the name of a deleted provider until 30 days after deletion.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers',
        http_method='POST',
        method_id='iam.locations.workforcePools.providers.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['workforcePoolProviderId'],
        relative_path='v1/{+parent}/providers',
        request_field='workforcePoolProvider',
        request_type_name='IamLocationsWorkforcePoolsProvidersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a WorkforcePoolProvider. Deleting a provider does not revoke credentials that have already been issued; they continue to grant access. You can undelete a provider for 30 days. After 30 days, deletion is permanent. You cannot update deleted providers. However, you can view and list them.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}',
        http_method='DELETE',
        method_id='iam.locations.workforcePools.providers.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsProvidersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an individual WorkforcePoolProvider.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePoolProvider) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}',
        http_method='GET',
        method_id='iam.locations.workforcePools.providers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsProvidersGetRequest',
        response_type_name='WorkforcePoolProvider',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all non-deleted WorkforcePoolProviders in a WorkforcePool. If `show_deleted` is set to `true`, then deleted providers are also listed.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkforcePoolProvidersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers',
        http_method='GET',
        method_id='iam.locations.workforcePools.providers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v1/{+parent}/providers',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsProvidersListRequest',
        response_type_name='ListWorkforcePoolProvidersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing WorkforcePoolProvider.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}',
        http_method='PATCH',
        method_id='iam.locations.workforcePools.providers.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='workforcePoolProvider',
        request_type_name='IamLocationsWorkforcePoolsProvidersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a WorkforcePoolProvider, as long as it was deleted fewer than 30 days ago.

      Args:
        request: (IamLocationsWorkforcePoolsProvidersUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/providers/{providersId}:undelete',
        http_method='POST',
        method_id='iam.locations.workforcePools.providers.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteWorkforcePoolProviderRequest',
        request_type_name='IamLocationsWorkforcePoolsProvidersUndeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class LocationsWorkforcePoolsSubjectsOperationsService(base_api.BaseApiService):
    """Service class for the locations_workforcePools_subjects_operations resource."""

    _NAME = 'locations_workforcePools_subjects_operations'

    def __init__(self, client):
      super(IamV1.LocationsWorkforcePoolsSubjectsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamLocationsWorkforcePoolsSubjectsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/subjects/{subjectsId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.locations.workforcePools.subjects.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsSubjectsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class LocationsWorkforcePoolsSubjectsService(base_api.BaseApiService):
    """Service class for the locations_workforcePools_subjects resource."""

    _NAME = 'locations_workforcePools_subjects'

    def __init__(self, client):
      super(IamV1.LocationsWorkforcePoolsSubjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a WorkforcePoolSubject. Subject must not already be in a deleted state. A WorkforcePoolSubject is automatically created the first time an external credential is exchanged for a Google Cloud credential using a mapped `google.subject` attribute. There is no endpoint to manually create a WorkforcePoolSubject. For 30 days after a WorkforcePoolSubject is deleted, using the same `google.subject` attribute in token exchanges with Google Cloud STS fails. Call UndeleteWorkforcePoolSubject to undelete a WorkforcePoolSubject that has been deleted, within within 30 days of deleting it. After 30 days, the WorkforcePoolSubject is permanently deleted. At this point, a token exchange with Google Cloud STS that uses the same mapped `google.subject` attribute automatically creates a new WorkforcePoolSubject that is unrelated to the previously deleted WorkforcePoolSubject but has the same `google.subject` value.

      Args:
        request: (IamLocationsWorkforcePoolsSubjectsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/subjects/{subjectsId}',
        http_method='DELETE',
        method_id='iam.locations.workforcePools.subjects.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsSubjectsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a WorkforcePoolSubject, as long as it was deleted fewer than 30 days ago.

      Args:
        request: (IamLocationsWorkforcePoolsSubjectsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/subjects/{subjectsId}:undelete',
        http_method='POST',
        method_id='iam.locations.workforcePools.subjects.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteWorkforcePoolSubjectRequest',
        request_type_name='IamLocationsWorkforcePoolsSubjectsUndeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class LocationsWorkforcePoolsService(base_api.BaseApiService):
    """Service class for the locations_workforcePools resource."""

    _NAME = 'locations_workforcePools'

    def __init__(self, client):
      super(IamV1.LocationsWorkforcePoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new WorkforcePool. You cannot reuse the name of a deleted pool until 30 days after deletion.

      Args:
        request: (IamLocationsWorkforcePoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools',
        http_method='POST',
        method_id='iam.locations.workforcePools.create',
        ordered_params=['location'],
        path_params=['location'],
        query_params=['workforcePoolId'],
        relative_path='v1/{+location}/workforcePools',
        request_field='workforcePool',
        request_type_name='IamLocationsWorkforcePoolsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a WorkforcePool. You cannot use a deleted WorkforcePool to exchange external credentials for Google Cloud credentials. However, deletion does not revoke credentials that have already been issued. Credentials issued for a deleted pool do not grant access to resources. If the pool is undeleted, and the credentials are not expired, they grant access again. You can undelete a pool for 30 days. After 30 days, deletion is permanent. You cannot update deleted pools. However, you can view and list them.

      Args:
        request: (IamLocationsWorkforcePoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}',
        http_method='DELETE',
        method_id='iam.locations.workforcePools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an individual WorkforcePool.

      Args:
        request: (IamLocationsWorkforcePoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkforcePool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}',
        http_method='GET',
        method_id='iam.locations.workforcePools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsGetRequest',
        response_type_name='WorkforcePool',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets IAM policies on a WorkforcePool.

      Args:
        request: (IamLocationsWorkforcePoolsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}:getIamPolicy',
        http_method='POST',
        method_id='iam.locations.workforcePools.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='IamLocationsWorkforcePoolsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all non-deleted WorkforcePools under the specified parent. If `show_deleted` is set to `true`, then deleted pools are also listed.

      Args:
        request: (IamLocationsWorkforcePoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkforcePoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools',
        http_method='GET',
        method_id='iam.locations.workforcePools.list',
        ordered_params=['location'],
        path_params=['location'],
        query_params=['pageSize', 'pageToken', 'parent', 'showDeleted'],
        relative_path='v1/{+location}/workforcePools',
        request_field='',
        request_type_name='IamLocationsWorkforcePoolsListRequest',
        response_type_name='ListWorkforcePoolsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing WorkforcePool.

      Args:
        request: (IamLocationsWorkforcePoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}',
        http_method='PATCH',
        method_id='iam.locations.workforcePools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='workforcePool',
        request_type_name='IamLocationsWorkforcePoolsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets IAM policies on a WorkforcePool.

      Args:
        request: (IamLocationsWorkforcePoolsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}:setIamPolicy',
        http_method='POST',
        method_id='iam.locations.workforcePools.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='IamLocationsWorkforcePoolsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns the caller's permissions on the WorkforcePool. If the pool doesn't exist, this call returns an empty set of permissions. It doesn't return a `NOT_FOUND` error.

      Args:
        request: (IamLocationsWorkforcePoolsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}:testIamPermissions',
        http_method='POST',
        method_id='iam.locations.workforcePools.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='IamLocationsWorkforcePoolsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a WorkforcePool, as long as it was deleted fewer than 30 days ago.

      Args:
        request: (IamLocationsWorkforcePoolsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/locations/{locationsId}/workforcePools/{workforcePoolsId}:undelete',
        http_method='POST',
        method_id='iam.locations.workforcePools.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteWorkforcePoolRequest',
        request_type_name='IamLocationsWorkforcePoolsUndeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class LocationsService(base_api.BaseApiService):
    """Service class for the locations resource."""

    _NAME = 'locations'

    def __init__(self, client):
      super(IamV1.LocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsRolesService(base_api.BaseApiService):
    """Service class for the organizations_roles resource."""

    _NAME = 'organizations_roles'

    def __init__(self, client):
      super(IamV1.OrganizationsRolesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new custom Role.

      Args:
        request: (IamOrganizationsRolesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Role) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/roles',
        http_method='POST',
        method_id='iam.organizations.roles.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/roles',
        request_field='createRoleRequest',
        request_type_name='IamOrganizationsRolesCreateRequest',
        response_type_name='Role',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a custom Role. When you delete a custom role, the following changes occur immediately: * You cannot bind a principal to the custom role in an IAM Policy. * Existing bindings to the custom role are not changed, but they have no effect. * By default, the response from ListRoles does not include the custom role. A deleted custom role still counts toward the [custom role limit](https://cloud.google.com/iam/help/limits) until it is permanently deleted. You have 7 days to undelete the custom role. After 7 days, the following changes occur: * The custom role is permanently deleted and cannot be recovered. * If an IAM policy contains a binding to the custom role, the binding is permanently removed. * The custom role no longer counts toward your custom role limit.

      Args:
        request: (IamOrganizationsRolesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Role) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/roles/{rolesId}',
        http_method='DELETE',
        method_id='iam.organizations.roles.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamOrganizationsRolesDeleteRequest',
        response_type_name='Role',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the definition of a Role.

      Args:
        request: (IamOrganizationsRolesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Role) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/roles/{rolesId}',
        http_method='GET',
        method_id='iam.organizations.roles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamOrganizationsRolesGetRequest',
        response_type_name='Role',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists every predefined Role that IAM supports, or every custom role that is defined for an organization or project.

      Args:
        request: (IamOrganizationsRolesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRolesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/roles',
        http_method='GET',
        method_id='iam.organizations.roles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted', 'view'],
        relative_path='v1/{+parent}/roles',
        request_field='',
        request_type_name='IamOrganizationsRolesListRequest',
        response_type_name='ListRolesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the definition of a custom Role.

      Args:
        request: (IamOrganizationsRolesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Role) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/roles/{rolesId}',
        http_method='PATCH',
        method_id='iam.organizations.roles.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='role',
        request_type_name='IamOrganizationsRolesPatchRequest',
        response_type_name='Role',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a custom Role.

      Args:
        request: (IamOrganizationsRolesUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Role) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/roles/{rolesId}:undelete',
        http_method='POST',
        method_id='iam.organizations.roles.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteRoleRequest',
        request_type_name='IamOrganizationsRolesUndeleteRequest',
        response_type_name='Role',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(IamV1.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class PermissionsService(base_api.BaseApiService):
    """Service class for the permissions resource."""

    _NAME = 'permissions'

    def __init__(self, client):
      super(IamV1.PermissionsService, self).__init__(client)
      self._upload_configs = {
          }

    def QueryTestablePermissions(self, request, global_params=None):
      r"""Lists every permission that you can test on a resource. A permission is testable if you can check whether a principal has that permission on the resource.

      Args:
        request: (QueryTestablePermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QueryTestablePermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('QueryTestablePermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryTestablePermissions.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='iam.permissions.queryTestablePermissions',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1/permissions:queryTestablePermissions',
        request_field='<request>',
        request_type_name='QueryTestablePermissionsRequest',
        response_type_name='QueryTestablePermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOauthClientsCredentialsService(base_api.BaseApiService):
    """Service class for the projects_locations_oauthClients_credentials resource."""

    _NAME = 'projects_locations_oauthClients_credentials'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsOauthClientsCredentialsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new OauthClientCredential.

      Args:
        request: (IamProjectsLocationsOauthClientsCredentialsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OauthClientCredential) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}/credentials',
        http_method='POST',
        method_id='iam.projects.locations.oauthClients.credentials.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['oauthClientCredentialId'],
        relative_path='v1/{+parent}/credentials',
        request_field='oauthClientCredential',
        request_type_name='IamProjectsLocationsOauthClientsCredentialsCreateRequest',
        response_type_name='OauthClientCredential',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an OauthClientCredential. Before deleting an OauthClientCredential, it should first be disabled.

      Args:
        request: (IamProjectsLocationsOauthClientsCredentialsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}/credentials/{credentialsId}',
        http_method='DELETE',
        method_id='iam.projects.locations.oauthClients.credentials.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsOauthClientsCredentialsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an individual OauthClientCredential.

      Args:
        request: (IamProjectsLocationsOauthClientsCredentialsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OauthClientCredential) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}/credentials/{credentialsId}',
        http_method='GET',
        method_id='iam.projects.locations.oauthClients.credentials.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsOauthClientsCredentialsGetRequest',
        response_type_name='OauthClientCredential',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all OauthClientCredentials in an OauthClient.

      Args:
        request: (IamProjectsLocationsOauthClientsCredentialsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOauthClientCredentialsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}/credentials',
        http_method='GET',
        method_id='iam.projects.locations.oauthClients.credentials.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/credentials',
        request_field='',
        request_type_name='IamProjectsLocationsOauthClientsCredentialsListRequest',
        response_type_name='ListOauthClientCredentialsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing OauthClientCredential.

      Args:
        request: (IamProjectsLocationsOauthClientsCredentialsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OauthClientCredential) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}/credentials/{credentialsId}',
        http_method='PATCH',
        method_id='iam.projects.locations.oauthClients.credentials.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='oauthClientCredential',
        request_type_name='IamProjectsLocationsOauthClientsCredentialsPatchRequest',
        response_type_name='OauthClientCredential',
        supports_download=False,
    )

  class ProjectsLocationsOauthClientsService(base_api.BaseApiService):
    """Service class for the projects_locations_oauthClients resource."""

    _NAME = 'projects_locations_oauthClients'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsOauthClientsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new OauthClient. You cannot reuse the name of a deleted OauthClient until 30 days after deletion.

      Args:
        request: (IamProjectsLocationsOauthClientsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OauthClient) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/oauthClients',
        http_method='POST',
        method_id='iam.projects.locations.oauthClients.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['oauthClientId'],
        relative_path='v1/{+parent}/oauthClients',
        request_field='oauthClient',
        request_type_name='IamProjectsLocationsOauthClientsCreateRequest',
        response_type_name='OauthClient',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an OauthClient. You cannot use a deleted OauthClient. However, deletion does not revoke access tokens that have already been issued. They continue to grant access. Deletion does revoke refresh tokens that have already been issued. They cannot be used to renew an access token. If the OauthClient is undeleted, and the refresh tokens are not expired, they are valid for token exchange again. You can undelete an OauthClient for 30 days. After 30 days, deletion is permanent. You cannot update deleted OauthClients. However, you can view and list them.

      Args:
        request: (IamProjectsLocationsOauthClientsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OauthClient) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}',
        http_method='DELETE',
        method_id='iam.projects.locations.oauthClients.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsOauthClientsDeleteRequest',
        response_type_name='OauthClient',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an individual OauthClient.

      Args:
        request: (IamProjectsLocationsOauthClientsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OauthClient) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}',
        http_method='GET',
        method_id='iam.projects.locations.oauthClients.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsOauthClientsGetRequest',
        response_type_name='OauthClient',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all non-deleted OauthClients in a project. If `show_deleted` is set to `true`, then deleted OauthClients are also listed.

      Args:
        request: (IamProjectsLocationsOauthClientsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOauthClientsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/oauthClients',
        http_method='GET',
        method_id='iam.projects.locations.oauthClients.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v1/{+parent}/oauthClients',
        request_field='',
        request_type_name='IamProjectsLocationsOauthClientsListRequest',
        response_type_name='ListOauthClientsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing OauthClient.

      Args:
        request: (IamProjectsLocationsOauthClientsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OauthClient) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}',
        http_method='PATCH',
        method_id='iam.projects.locations.oauthClients.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='oauthClient',
        request_type_name='IamProjectsLocationsOauthClientsPatchRequest',
        response_type_name='OauthClient',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes an OauthClient, as long as it was deleted fewer than 30 days ago.

      Args:
        request: (IamProjectsLocationsOauthClientsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OauthClient) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/oauthClients/{oauthClientsId}:undelete',
        http_method='POST',
        method_id='iam.projects.locations.oauthClients.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteOauthClientRequest',
        request_type_name='IamProjectsLocationsOauthClientsUndeleteRequest',
        response_type_name='OauthClient',
        supports_download=False,
    )

  class ProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_workloadIdentityPools_namespaces_managedIdentities_operations resource."""

    _NAME = 'projects_locations_workloadIdentityPools_namespaces_managedIdentities_operations'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_workloadIdentityPools_namespaces_managedIdentities_workloadSources_operations resource."""

    _NAME = 'projects_locations_workloadIdentityPools_namespaces_managedIdentities_workloadSources_operations'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}/workloadSources/{workloadSourcesId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.workloadSources.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesService(base_api.BaseApiService):
    """Service class for the projects_locations_workloadIdentityPools_namespaces_managedIdentities_workloadSources resource."""

    _NAME = 'projects_locations_workloadIdentityPools_namespaces_managedIdentities_workloadSources'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new WorkloadSource.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}/workloadSources',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.workloadSources.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['workloadSourceId'],
        relative_path='v1/{+parent}/workloadSources',
        request_field='workloadSource',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a WorkloadSource.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}/workloadSources/{workloadSourcesId}',
        http_method='DELETE',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.workloadSources.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an individual WorkloadSource.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkloadSource) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}/workloadSources/{workloadSourcesId}',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.workloadSources.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesGetRequest',
        response_type_name='WorkloadSource',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all WorkloadSources for a WorkloadIdentityPoolNamespace or WorkloadIdentityPoolManagedIdentity.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkloadSourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}/workloadSources',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.workloadSources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/workloadSources',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesListRequest',
        response_type_name='ListWorkloadSourcesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing WorkloadSource.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}/workloadSources/{workloadSourcesId}',
        http_method='PATCH',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.workloadSources.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='workloadSource',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesService(base_api.BaseApiService):
    """Service class for the projects_locations_workloadIdentityPools_namespaces_managedIdentities resource."""

    _NAME = 'projects_locations_workloadIdentityPools_namespaces_managedIdentities'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesService, self).__init__(client)
      self._upload_configs = {
          }

    def AddAttestationRule(self, request, global_params=None):
      r"""Add an AttestationRule on a WorkloadIdentityPoolManagedIdentity. The total attestation rules after addition must not exceed 50.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesAddAttestationRuleRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AddAttestationRule')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddAttestationRule.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}:addAttestationRule',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.addAttestationRule',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:addAttestationRule',
        request_field='addAttestationRuleRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesAddAttestationRuleRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new WorkloadIdentityPoolManagedIdentity in a WorkloadIdentityPoolNamespace.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['workloadIdentityPoolManagedIdentityId'],
        relative_path='v1/{+parent}/managedIdentities',
        request_field='workloadIdentityPoolManagedIdentity',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a WorkloadIdentityPoolManagedIdentity. You can undelete a managed identity for 30 days. After 30 days, deletion is permanent.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}',
        http_method='DELETE',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an individual WorkloadIdentityPoolManagedIdentity.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkloadIdentityPoolManagedIdentity) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesGetRequest',
        response_type_name='WorkloadIdentityPoolManagedIdentity',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the IAM policy of a WorkloadIdentityPool.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}:getIamPolicy',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all non-deleted WorkloadIdentityPoolManagedIdentitys in a namespace. If `show_deleted` is set to `true`, then deleted managed identities are also listed.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkloadIdentityPoolManagedIdentitiesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v1/{+parent}/managedIdentities',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesListRequest',
        response_type_name='ListWorkloadIdentityPoolManagedIdentitiesResponse',
        supports_download=False,
    )

    def ListAttestationRules(self, request, global_params=None):
      r"""List all AttestationRule on a WorkloadIdentityPoolManagedIdentity.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesListAttestationRulesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttestationRulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListAttestationRules')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListAttestationRules.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}:listAttestationRules',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.listAttestationRules',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+resource}:listAttestationRules',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesListAttestationRulesRequest',
        response_type_name='ListAttestationRulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing WorkloadIdentityPoolManagedIdentity in a WorkloadIdentityPoolNamespace.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}',
        http_method='PATCH',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='workloadIdentityPoolManagedIdentity',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RemoveAttestationRule(self, request, global_params=None):
      r"""Remove an AttestationRule on a WorkloadIdentityPoolManagedIdentity.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesRemoveAttestationRuleRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RemoveAttestationRule')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveAttestationRule.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}:removeAttestationRule',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.removeAttestationRule',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:removeAttestationRule',
        request_field='removeAttestationRuleRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesRemoveAttestationRuleRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetAttestationRules(self, request, global_params=None):
      r"""Set all AttestationRule on a WorkloadIdentityPoolManagedIdentity. A maximum of 50 AttestationRules can be set.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesSetAttestationRulesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('SetAttestationRules')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetAttestationRules.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}:setAttestationRules',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.setAttestationRules',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setAttestationRules',
        request_field='setAttestationRulesRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesSetAttestationRulesRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the IAM policies on a WorkloadIdentityPool.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}:setIamPolicy',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns the caller's permissions on a WorkloadIdentityPool.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}:testIamPermissions',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a WorkloadIdentityPoolManagedIdentity, as long as it was deleted fewer than 30 days ago.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/managedIdentities/{managedIdentitiesId}:undelete',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.managedIdentities.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteWorkloadIdentityPoolManagedIdentityRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesUndeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkloadIdentityPoolsNamespacesOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_workloadIdentityPools_namespaces_operations resource."""

    _NAME = 'projects_locations_workloadIdentityPools_namespaces_operations'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsWorkloadIdentityPoolsNamespacesOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_workloadIdentityPools_namespaces_workloadSources_operations resource."""

    _NAME = 'projects_locations_workloadIdentityPools_namespaces_workloadSources_operations'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/workloadSources/{workloadSourcesId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.workloadSources.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesService(base_api.BaseApiService):
    """Service class for the projects_locations_workloadIdentityPools_namespaces_workloadSources resource."""

    _NAME = 'projects_locations_workloadIdentityPools_namespaces_workloadSources'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new WorkloadSource.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/workloadSources',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.workloadSources.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['workloadSourceId'],
        relative_path='v1/{+parent}/workloadSources',
        request_field='workloadSource',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a WorkloadSource.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/workloadSources/{workloadSourcesId}',
        http_method='DELETE',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.workloadSources.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an individual WorkloadSource.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkloadSource) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/workloadSources/{workloadSourcesId}',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.workloadSources.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesGetRequest',
        response_type_name='WorkloadSource',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all WorkloadSources for a WorkloadIdentityPoolNamespace or WorkloadIdentityPoolManagedIdentity.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkloadSourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/workloadSources',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.workloadSources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/workloadSources',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesListRequest',
        response_type_name='ListWorkloadSourcesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing WorkloadSource.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}/workloadSources/{workloadSourcesId}',
        http_method='PATCH',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.workloadSources.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='workloadSource',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkloadIdentityPoolsNamespacesService(base_api.BaseApiService):
    """Service class for the projects_locations_workloadIdentityPools_namespaces resource."""

    _NAME = 'projects_locations_workloadIdentityPools_namespaces'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsWorkloadIdentityPoolsNamespacesService, self).__init__(client)
      self._upload_configs = {
          }

    def AddAttestationRule(self, request, global_params=None):
      r"""Add an AttestationRule on a WorkloadIdentityPoolManagedIdentity. The total attestation rules after addition must not exceed 50.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesAddAttestationRuleRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AddAttestationRule')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddAttestationRule.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}:addAttestationRule',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.addAttestationRule',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:addAttestationRule',
        request_field='addAttestationRuleRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesAddAttestationRuleRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new WorkloadIdentityPoolNamespace in a WorkloadIdentityPool.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['workloadIdentityPoolNamespaceId'],
        relative_path='v1/{+parent}/namespaces',
        request_field='workloadIdentityPoolNamespace',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a WorkloadIdentityPoolNamespace. You can undelete a namespace for 30 days. After 30 days, deletion is permanent.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}',
        http_method='DELETE',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an individual WorkloadIdentityPoolNamespace.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkloadIdentityPoolNamespace) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesGetRequest',
        response_type_name='WorkloadIdentityPoolNamespace',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the IAM policy of a WorkloadIdentityPool.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}:getIamPolicy',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all non-deleted WorkloadIdentityPoolNamespaces in a workload identity pool. If `show_deleted` is set to `true`, then deleted namespaces are also listed.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkloadIdentityPoolNamespacesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v1/{+parent}/namespaces',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesListRequest',
        response_type_name='ListWorkloadIdentityPoolNamespacesResponse',
        supports_download=False,
    )

    def ListAttestationRules(self, request, global_params=None):
      r"""List all AttestationRule on a WorkloadIdentityPoolManagedIdentity.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesListAttestationRulesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttestationRulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListAttestationRules')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListAttestationRules.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}:listAttestationRules',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.listAttestationRules',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+resource}:listAttestationRules',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesListAttestationRulesRequest',
        response_type_name='ListAttestationRulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing WorkloadIdentityPoolNamespace in a WorkloadIdentityPool.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}',
        http_method='PATCH',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='workloadIdentityPoolNamespace',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RemoveAttestationRule(self, request, global_params=None):
      r"""Remove an AttestationRule on a WorkloadIdentityPoolManagedIdentity.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesRemoveAttestationRuleRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RemoveAttestationRule')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveAttestationRule.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}:removeAttestationRule',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.removeAttestationRule',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:removeAttestationRule',
        request_field='removeAttestationRuleRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesRemoveAttestationRuleRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetAttestationRules(self, request, global_params=None):
      r"""Set all AttestationRule on a WorkloadIdentityPoolManagedIdentity. A maximum of 50 AttestationRules can be set.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesSetAttestationRulesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('SetAttestationRules')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetAttestationRules.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}:setAttestationRules',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.setAttestationRules',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setAttestationRules',
        request_field='setAttestationRulesRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesSetAttestationRulesRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the IAM policies on a WorkloadIdentityPool.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}:setIamPolicy',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns the caller's permissions on a WorkloadIdentityPool.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}:testIamPermissions',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a WorkloadIdentityPoolNamespace, as long as it was deleted fewer than 30 days ago.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsNamespacesUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/namespaces/{namespacesId}:undelete',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.namespaces.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteWorkloadIdentityPoolNamespaceRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsNamespacesUndeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkloadIdentityPoolsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_workloadIdentityPools_operations resource."""

    _NAME = 'projects_locations_workloadIdentityPools_operations'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsWorkloadIdentityPoolsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkloadIdentityPoolsProvidersKeysOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_workloadIdentityPools_providers_keys_operations resource."""

    _NAME = 'projects_locations_workloadIdentityPools_providers_keys_operations'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsWorkloadIdentityPoolsProvidersKeysOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}/keys/{keysId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.providers.keys.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkloadIdentityPoolsProvidersKeysService(base_api.BaseApiService):
    """Service class for the projects_locations_workloadIdentityPools_providers_keys resource."""

    _NAME = 'projects_locations_workloadIdentityPools_providers_keys'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsWorkloadIdentityPoolsProvidersKeysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new WorkloadIdentityPoolProviderKey in a WorkloadIdentityPoolProvider.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}/keys',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.providers.keys.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['workloadIdentityPoolProviderKeyId'],
        relative_path='v1/{+parent}/keys',
        request_field='workloadIdentityPoolProviderKey',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an WorkloadIdentityPoolProviderKey. You can undelete a key for 30 days. After 30 days, deletion is permanent.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}/keys/{keysId}',
        http_method='DELETE',
        method_id='iam.projects.locations.workloadIdentityPools.providers.keys.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an individual WorkloadIdentityPoolProviderKey.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkloadIdentityPoolProviderKey) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}/keys/{keysId}',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.providers.keys.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysGetRequest',
        response_type_name='WorkloadIdentityPoolProviderKey',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all non-deleted WorkloadIdentityPoolProviderKeys in a project. If show_deleted is set to `true`, then deleted pools are also listed.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkloadIdentityPoolProviderKeysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}/keys',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.providers.keys.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v1/{+parent}/keys',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysListRequest',
        response_type_name='ListWorkloadIdentityPoolProviderKeysResponse',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes an WorkloadIdentityPoolProviderKey, as long as it was deleted fewer than 30 days ago.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}/keys/{keysId}:undelete',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.providers.keys.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteWorkloadIdentityPoolProviderKeyRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysUndeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkloadIdentityPoolsProvidersOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_workloadIdentityPools_providers_operations resource."""

    _NAME = 'projects_locations_workloadIdentityPools_providers_operations'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsWorkloadIdentityPoolsProvidersOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsProvidersOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}/operations/{operationsId}',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.providers.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsProvidersOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkloadIdentityPoolsProvidersService(base_api.BaseApiService):
    """Service class for the projects_locations_workloadIdentityPools_providers resource."""

    _NAME = 'projects_locations_workloadIdentityPools_providers'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsWorkloadIdentityPoolsProvidersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new WorkloadIdentityPoolProvider in a WorkloadIdentityPool. You cannot reuse the name of a deleted provider until 30 days after deletion.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsProvidersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.providers.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['workloadIdentityPoolProviderId'],
        relative_path='v1/{+parent}/providers',
        request_field='workloadIdentityPoolProvider',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsProvidersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a WorkloadIdentityPoolProvider. Deleting a provider does not revoke credentials that have already been issued; they continue to grant access. You can undelete a provider for 30 days. After 30 days, deletion is permanent. You cannot update deleted providers. However, you can view and list them.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsProvidersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}',
        http_method='DELETE',
        method_id='iam.projects.locations.workloadIdentityPools.providers.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsProvidersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an individual WorkloadIdentityPoolProvider.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsProvidersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkloadIdentityPoolProvider) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.providers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsProvidersGetRequest',
        response_type_name='WorkloadIdentityPoolProvider',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all non-deleted WorkloadIdentityPoolProviders in a WorkloadIdentityPool. If `show_deleted` is set to `true`, then deleted providers are also listed.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsProvidersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkloadIdentityPoolProvidersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.providers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v1/{+parent}/providers',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsProvidersListRequest',
        response_type_name='ListWorkloadIdentityPoolProvidersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing WorkloadIdentityPoolProvider.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsProvidersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}',
        http_method='PATCH',
        method_id='iam.projects.locations.workloadIdentityPools.providers.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='workloadIdentityPoolProvider',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsProvidersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a WorkloadIdentityPoolProvider, as long as it was deleted fewer than 30 days ago.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsProvidersUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/providers/{providersId}:undelete',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.providers.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteWorkloadIdentityPoolProviderRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsProvidersUndeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkloadIdentityPoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_workloadIdentityPools resource."""

    _NAME = 'projects_locations_workloadIdentityPools'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsWorkloadIdentityPoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddAttestationRule(self, request, global_params=None):
      r"""Add an AttestationRule on a WorkloadIdentityPoolManagedIdentity. The total attestation rules after addition must not exceed 50.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsAddAttestationRuleRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AddAttestationRule')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddAttestationRule.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}:addAttestationRule',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.addAttestationRule',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:addAttestationRule',
        request_field='addAttestationRuleRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsAddAttestationRuleRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new WorkloadIdentityPool. You cannot reuse the name of a deleted pool until 30 days after deletion.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['workloadIdentityPoolId'],
        relative_path='v1/{+parent}/workloadIdentityPools',
        request_field='workloadIdentityPool',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a WorkloadIdentityPool. You cannot use a deleted pool to exchange external credentials for Google Cloud credentials. However, deletion does not revoke credentials that have already been issued. Credentials issued for a deleted pool do not grant access to resources. If the pool is undeleted, and the credentials are not expired, they grant access again. You can undelete a pool for 30 days. After 30 days, deletion is permanent. You cannot update deleted pools. However, you can view and list them.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}',
        http_method='DELETE',
        method_id='iam.projects.locations.workloadIdentityPools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an individual WorkloadIdentityPool.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkloadIdentityPool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsGetRequest',
        response_type_name='WorkloadIdentityPool',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the IAM policy of a WorkloadIdentityPool.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}:getIamPolicy',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all non-deleted WorkloadIdentityPools in a project. If `show_deleted` is set to `true`, then deleted pools are also listed.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkloadIdentityPoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v1/{+parent}/workloadIdentityPools',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsListRequest',
        response_type_name='ListWorkloadIdentityPoolsResponse',
        supports_download=False,
    )

    def ListAttestationRules(self, request, global_params=None):
      r"""List all AttestationRule on a WorkloadIdentityPoolManagedIdentity.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsListAttestationRulesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttestationRulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListAttestationRules')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListAttestationRules.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}:listAttestationRules',
        http_method='GET',
        method_id='iam.projects.locations.workloadIdentityPools.listAttestationRules',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+resource}:listAttestationRules',
        request_field='',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsListAttestationRulesRequest',
        response_type_name='ListAttestationRulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing WorkloadIdentityPool.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}',
        http_method='PATCH',
        method_id='iam.projects.locations.workloadIdentityPools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='workloadIdentityPool',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RemoveAttestationRule(self, request, global_params=None):
      r"""Remove an AttestationRule on a WorkloadIdentityPoolManagedIdentity.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsRemoveAttestationRuleRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RemoveAttestationRule')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveAttestationRule.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}:removeAttestationRule',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.removeAttestationRule',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:removeAttestationRule',
        request_field='removeAttestationRuleRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsRemoveAttestationRuleRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetAttestationRules(self, request, global_params=None):
      r"""Set all AttestationRule on a WorkloadIdentityPoolManagedIdentity. A maximum of 50 AttestationRules can be set.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsSetAttestationRulesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('SetAttestationRules')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetAttestationRules.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}:setAttestationRules',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.setAttestationRules',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setAttestationRules',
        request_field='setAttestationRulesRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsSetAttestationRulesRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the IAM policies on a WorkloadIdentityPool.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}:setIamPolicy',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns the caller's permissions on a WorkloadIdentityPool.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}:testIamPermissions',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a WorkloadIdentityPool, as long as it was deleted fewer than 30 days ago.

      Args:
        request: (IamProjectsLocationsWorkloadIdentityPoolsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}:undelete',
        http_method='POST',
        method_id='iam.projects.locations.workloadIdentityPools.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteWorkloadIdentityPoolRequest',
        request_type_name='IamProjectsLocationsWorkloadIdentityPoolsUndeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(IamV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsRolesService(base_api.BaseApiService):
    """Service class for the projects_roles resource."""

    _NAME = 'projects_roles'

    def __init__(self, client):
      super(IamV1.ProjectsRolesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new custom Role.

      Args:
        request: (IamProjectsRolesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Role) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/roles',
        http_method='POST',
        method_id='iam.projects.roles.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/roles',
        request_field='createRoleRequest',
        request_type_name='IamProjectsRolesCreateRequest',
        response_type_name='Role',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a custom Role. When you delete a custom role, the following changes occur immediately: * You cannot bind a principal to the custom role in an IAM Policy. * Existing bindings to the custom role are not changed, but they have no effect. * By default, the response from ListRoles does not include the custom role. A deleted custom role still counts toward the [custom role limit](https://cloud.google.com/iam/help/limits) until it is permanently deleted. You have 7 days to undelete the custom role. After 7 days, the following changes occur: * The custom role is permanently deleted and cannot be recovered. * If an IAM policy contains a binding to the custom role, the binding is permanently removed. * The custom role no longer counts toward your custom role limit.

      Args:
        request: (IamProjectsRolesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Role) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/roles/{rolesId}',
        http_method='DELETE',
        method_id='iam.projects.roles.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsRolesDeleteRequest',
        response_type_name='Role',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the definition of a Role.

      Args:
        request: (IamProjectsRolesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Role) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/roles/{rolesId}',
        http_method='GET',
        method_id='iam.projects.roles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsRolesGetRequest',
        response_type_name='Role',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists every predefined Role that IAM supports, or every custom role that is defined for an organization or project.

      Args:
        request: (IamProjectsRolesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRolesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/roles',
        http_method='GET',
        method_id='iam.projects.roles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted', 'view'],
        relative_path='v1/{+parent}/roles',
        request_field='',
        request_type_name='IamProjectsRolesListRequest',
        response_type_name='ListRolesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the definition of a custom Role.

      Args:
        request: (IamProjectsRolesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Role) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/roles/{rolesId}',
        http_method='PATCH',
        method_id='iam.projects.roles.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='role',
        request_type_name='IamProjectsRolesPatchRequest',
        response_type_name='Role',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undeletes a custom Role.

      Args:
        request: (IamProjectsRolesUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Role) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/roles/{rolesId}:undelete',
        http_method='POST',
        method_id='iam.projects.roles.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteRoleRequest',
        request_type_name='IamProjectsRolesUndeleteRequest',
        response_type_name='Role',
        supports_download=False,
    )

  class ProjectsServiceAccountsIdentityBindingsService(base_api.BaseApiService):
    """Service class for the projects_serviceAccounts_identityBindings resource."""

    _NAME = 'projects_serviceAccounts_identityBindings'

    def __init__(self, client):
      super(IamV1.ProjectsServiceAccountsIdentityBindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create method for the projects_serviceAccounts_identityBindings service.

      Args:
        request: (IamProjectsServiceAccountsIdentityBindingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceAccountIdentityBinding) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/identityBindings',
        http_method='POST',
        method_id='iam.projects.serviceAccounts.identityBindings.create',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/identityBindings',
        request_field='createServiceAccountIdentityBindingRequest',
        request_type_name='IamProjectsServiceAccountsIdentityBindingsCreateRequest',
        response_type_name='ServiceAccountIdentityBinding',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete method for the projects_serviceAccounts_identityBindings service.

      Args:
        request: (IamProjectsServiceAccountsIdentityBindingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/identityBindings/{identityBindingsId}',
        http_method='DELETE',
        method_id='iam.projects.serviceAccounts.identityBindings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsServiceAccountsIdentityBindingsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get method for the projects_serviceAccounts_identityBindings service.

      Args:
        request: (IamProjectsServiceAccountsIdentityBindingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceAccountIdentityBinding) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/identityBindings/{identityBindingsId}',
        http_method='GET',
        method_id='iam.projects.serviceAccounts.identityBindings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsServiceAccountsIdentityBindingsGetRequest',
        response_type_name='ServiceAccountIdentityBinding',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List method for the projects_serviceAccounts_identityBindings service.

      Args:
        request: (IamProjectsServiceAccountsIdentityBindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceAccountIdentityBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/identityBindings',
        http_method='GET',
        method_id='iam.projects.serviceAccounts.identityBindings.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/identityBindings',
        request_field='',
        request_type_name='IamProjectsServiceAccountsIdentityBindingsListRequest',
        response_type_name='ListServiceAccountIdentityBindingsResponse',
        supports_download=False,
    )

  class ProjectsServiceAccountsKeysService(base_api.BaseApiService):
    """Service class for the projects_serviceAccounts_keys resource."""

    _NAME = 'projects_serviceAccounts_keys'

    def __init__(self, client):
      super(IamV1.ProjectsServiceAccountsKeysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a ServiceAccountKey.

      Args:
        request: (IamProjectsServiceAccountsKeysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceAccountKey) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/keys',
        http_method='POST',
        method_id='iam.projects.serviceAccounts.keys.create',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/keys',
        request_field='createServiceAccountKeyRequest',
        request_type_name='IamProjectsServiceAccountsKeysCreateRequest',
        response_type_name='ServiceAccountKey',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a ServiceAccountKey. Deleting a service account key does not revoke short-lived credentials that have been issued based on the service account key.

      Args:
        request: (IamProjectsServiceAccountsKeysDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/keys/{keysId}',
        http_method='DELETE',
        method_id='iam.projects.serviceAccounts.keys.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsServiceAccountsKeysDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Disable(self, request, global_params=None):
      r"""Disable a ServiceAccountKey. A disabled service account key can be re-enabled with EnableServiceAccountKey.

      Args:
        request: (IamProjectsServiceAccountsKeysDisableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Disable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Disable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/keys/{keysId}:disable',
        http_method='POST',
        method_id='iam.projects.serviceAccounts.keys.disable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:disable',
        request_field='disableServiceAccountKeyRequest',
        request_type_name='IamProjectsServiceAccountsKeysDisableRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Enable(self, request, global_params=None):
      r"""Enable a ServiceAccountKey.

      Args:
        request: (IamProjectsServiceAccountsKeysEnableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Enable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/keys/{keysId}:enable',
        http_method='POST',
        method_id='iam.projects.serviceAccounts.keys.enable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:enable',
        request_field='enableServiceAccountKeyRequest',
        request_type_name='IamProjectsServiceAccountsKeysEnableRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a ServiceAccountKey.

      Args:
        request: (IamProjectsServiceAccountsKeysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceAccountKey) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/keys/{keysId}',
        http_method='GET',
        method_id='iam.projects.serviceAccounts.keys.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['publicKeyType'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsServiceAccountsKeysGetRequest',
        response_type_name='ServiceAccountKey',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists every ServiceAccountKey for a service account.

      Args:
        request: (IamProjectsServiceAccountsKeysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceAccountKeysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/keys',
        http_method='GET',
        method_id='iam.projects.serviceAccounts.keys.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['keyTypes'],
        relative_path='v1/{+name}/keys',
        request_field='',
        request_type_name='IamProjectsServiceAccountsKeysListRequest',
        response_type_name='ListServiceAccountKeysResponse',
        supports_download=False,
    )

    def Upload(self, request, global_params=None):
      r"""Uploads the public key portion of a key pair that you manage, and associates the public key with a ServiceAccount. After you upload the public key, you can use the private key from the key pair as a service account key.

      Args:
        request: (IamProjectsServiceAccountsKeysUploadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceAccountKey) The response message.
      """
      config = self.GetMethodConfig('Upload')
      return self._RunMethod(
          config, request, global_params=global_params)

    Upload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/keys:upload',
        http_method='POST',
        method_id='iam.projects.serviceAccounts.keys.upload',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/keys:upload',
        request_field='uploadServiceAccountKeyRequest',
        request_type_name='IamProjectsServiceAccountsKeysUploadRequest',
        response_type_name='ServiceAccountKey',
        supports_download=False,
    )

  class ProjectsServiceAccountsService(base_api.BaseApiService):
    """Service class for the projects_serviceAccounts resource."""

    _NAME = 'projects_serviceAccounts'

    def __init__(self, client):
      super(IamV1.ProjectsServiceAccountsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a ServiceAccount.

      Args:
        request: (IamProjectsServiceAccountsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceAccount) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts',
        http_method='POST',
        method_id='iam.projects.serviceAccounts.create',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/serviceAccounts',
        request_field='createServiceAccountRequest',
        request_type_name='IamProjectsServiceAccountsCreateRequest',
        response_type_name='ServiceAccount',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a ServiceAccount. **Warning:** After you delete a service account, you might not be able to undelete it. If you know that you need to re-enable the service account in the future, use DisableServiceAccount instead. If you delete a service account, IAM permanently removes the service account 30 days later. Google Cloud cannot recover the service account after it is permanently removed, even if you file a support request. To help avoid unplanned outages, we recommend that you disable the service account before you delete it. Use DisableServiceAccount to disable the service account, then wait at least 24 hours and watch for unintended consequences. If there are no unintended consequences, you can delete the service account.

      Args:
        request: (IamProjectsServiceAccountsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}',
        http_method='DELETE',
        method_id='iam.projects.serviceAccounts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsServiceAccountsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Disable(self, request, global_params=None):
      r"""Disables a ServiceAccount immediately. If an application uses the service account to authenticate, that application can no longer call Google APIs or access Google Cloud resources. Existing access tokens for the service account are rejected, and requests for new access tokens will fail. To re-enable the service account, use EnableServiceAccount. After you re-enable the service account, its existing access tokens will be accepted, and you can request new access tokens. To help avoid unplanned outages, we recommend that you disable the service account before you delete it. Use this method to disable the service account, then wait at least 24 hours and watch for unintended consequences. If there are no unintended consequences, you can delete the service account with DeleteServiceAccount.

      Args:
        request: (IamProjectsServiceAccountsDisableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Disable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Disable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:disable',
        http_method='POST',
        method_id='iam.projects.serviceAccounts.disable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:disable',
        request_field='disableServiceAccountRequest',
        request_type_name='IamProjectsServiceAccountsDisableRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Enable(self, request, global_params=None):
      r"""Enables a ServiceAccount that was disabled by DisableServiceAccount. If the service account is already enabled, then this method has no effect. If the service account was disabled by other means-for example, if Google disabled the service account because it was compromised-you cannot use this method to enable the service account.

      Args:
        request: (IamProjectsServiceAccountsEnableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Enable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:enable',
        http_method='POST',
        method_id='iam.projects.serviceAccounts.enable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:enable',
        request_field='enableServiceAccountRequest',
        request_type_name='IamProjectsServiceAccountsEnableRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a ServiceAccount.

      Args:
        request: (IamProjectsServiceAccountsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceAccount) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}',
        http_method='GET',
        method_id='iam.projects.serviceAccounts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamProjectsServiceAccountsGetRequest',
        response_type_name='ServiceAccount',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the IAM policy that is attached to a ServiceAccount. This IAM policy specifies which principals have access to the service account. This method does not tell you whether the service account has been granted any roles on other resources. To check whether a service account has role grants on a resource, use the `getIamPolicy` method for that resource. For example, to view the role grants for a project, call the Resource Manager API's [projects.getIamPolicy](https://cloud.google.com/resource-manager/reference/rest/v1/projects/getIamPolicy) method.

      Args:
        request: (IamProjectsServiceAccountsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:getIamPolicy',
        http_method='POST',
        method_id='iam.projects.serviceAccounts.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='IamProjectsServiceAccountsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists every ServiceAccount that belongs to a specific project.

      Args:
        request: (IamProjectsServiceAccountsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceAccountsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts',
        http_method='GET',
        method_id='iam.projects.serviceAccounts.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+name}/serviceAccounts',
        request_field='',
        request_type_name='IamProjectsServiceAccountsListRequest',
        response_type_name='ListServiceAccountsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Patches a ServiceAccount.

      Args:
        request: (IamProjectsServiceAccountsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceAccount) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}',
        http_method='PATCH',
        method_id='iam.projects.serviceAccounts.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='patchServiceAccountRequest',
        request_type_name='IamProjectsServiceAccountsPatchRequest',
        response_type_name='ServiceAccount',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the IAM policy that is attached to a ServiceAccount. Use this method to grant or revoke access to the service account. For example, you could grant a principal the ability to impersonate the service account. This method does not enable the service account to access other resources. To grant roles to a service account on a resource, follow these steps: 1. Call the resource's `getIamPolicy` method to get its current IAM policy. 2. Edit the policy so that it binds the service account to an IAM role for the resource. 3. Call the resource's `setIamPolicy` method to update its IAM policy. For detailed instructions, see [Manage access to project, folders, and organizations](https://cloud.google.com/iam/help/service-accounts/granting-access-to-service-accounts) or [Manage access to other resources](https://cloud.google.com/iam/help/access/manage-other-resources).

      Args:
        request: (IamProjectsServiceAccountsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:setIamPolicy',
        http_method='POST',
        method_id='iam.projects.serviceAccounts.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='IamProjectsServiceAccountsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def SignBlob(self, request, global_params=None):
      r""" Signs a blob using the system-managed private key for a ServiceAccount.

      Args:
        request: (IamProjectsServiceAccountsSignBlobRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SignBlobResponse) The response message.
      """
      config = self.GetMethodConfig('SignBlob')
      return self._RunMethod(
          config, request, global_params=global_params)

    SignBlob.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:signBlob',
        http_method='POST',
        method_id='iam.projects.serviceAccounts.signBlob',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:signBlob',
        request_field='signBlobRequest',
        request_type_name='IamProjectsServiceAccountsSignBlobRequest',
        response_type_name='SignBlobResponse',
        supports_download=False,
    )

    def SignJwt(self, request, global_params=None):
      r""" Signs a JSON Web Token (JWT) using the system-managed private key for a ServiceAccount.

      Args:
        request: (IamProjectsServiceAccountsSignJwtRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SignJwtResponse) The response message.
      """
      config = self.GetMethodConfig('SignJwt')
      return self._RunMethod(
          config, request, global_params=global_params)

    SignJwt.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:signJwt',
        http_method='POST',
        method_id='iam.projects.serviceAccounts.signJwt',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:signJwt',
        request_field='signJwtRequest',
        request_type_name='IamProjectsServiceAccountsSignJwtRequest',
        response_type_name='SignJwtResponse',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Tests whether the caller has the specified permissions on a ServiceAccount.

      Args:
        request: (IamProjectsServiceAccountsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:testIamPermissions',
        http_method='POST',
        method_id='iam.projects.serviceAccounts.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='IamProjectsServiceAccountsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Restores a deleted ServiceAccount. **Important:** It is not always possible to restore a deleted service account. Use this method only as a last resort. After you delete a service account, IAM permanently removes the service account 30 days later. There is no way to restore a deleted service account that has been permanently removed.

      Args:
        request: (IamProjectsServiceAccountsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UndeleteServiceAccountResponse) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:undelete',
        http_method='POST',
        method_id='iam.projects.serviceAccounts.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteServiceAccountRequest',
        request_type_name='IamProjectsServiceAccountsUndeleteRequest',
        response_type_name='UndeleteServiceAccountResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""**Note:** We are in the process of deprecating this method. Use PatchServiceAccount instead. Updates a ServiceAccount. You can update only the `display_name` field.

      Args:
        request: (ServiceAccount) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceAccount) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}',
        http_method='PUT',
        method_id='iam.projects.serviceAccounts.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='ServiceAccount',
        response_type_name='ServiceAccount',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(IamV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

  class RolesService(base_api.BaseApiService):
    """Service class for the roles resource."""

    _NAME = 'roles'

    def __init__(self, client):
      super(IamV1.RolesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the definition of a Role.

      Args:
        request: (IamRolesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Role) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/roles/{rolesId}',
        http_method='GET',
        method_id='iam.roles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='IamRolesGetRequest',
        response_type_name='Role',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists every predefined Role that IAM supports, or every custom role that is defined for an organization or project.

      Args:
        request: (IamRolesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRolesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='iam.roles.list',
        ordered_params=[],
        path_params=[],
        query_params=['pageSize', 'pageToken', 'parent', 'showDeleted', 'view'],
        relative_path='v1/roles',
        request_field='',
        request_type_name='IamRolesListRequest',
        response_type_name='ListRolesResponse',
        supports_download=False,
    )

    def QueryGrantableRoles(self, request, global_params=None):
      r"""Lists roles that can be granted on a Google Cloud resource. A role is grantable if the IAM policy for the resource can contain bindings to the role.

      Args:
        request: (QueryGrantableRolesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QueryGrantableRolesResponse) The response message.
      """
      config = self.GetMethodConfig('QueryGrantableRoles')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryGrantableRoles.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='iam.roles.queryGrantableRoles',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1/roles:queryGrantableRoles',
        request_field='<request>',
        request_type_name='QueryGrantableRolesRequest',
        response_type_name='QueryGrantableRolesResponse',
        supports_download=False,
    )
