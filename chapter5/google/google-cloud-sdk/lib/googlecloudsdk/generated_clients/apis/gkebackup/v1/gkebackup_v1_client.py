"""Generated client library for gkebackup version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.gkebackup.v1 import gkebackup_v1_messages as messages


class GkebackupV1(base_api.BaseApiClient):
  """Generated client library for service gkebackup version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://gkebackup.googleapis.com/'
  MTLS_BASE_URL = 'https://gkebackup.mtls.googleapis.com/'

  _PACKAGE = 'gkebackup'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'GkebackupV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new gkebackup handle."""
    url = url or self.BASE_URL
    super(GkebackupV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_backupChannels_backupPlanAssociations = self.ProjectsLocationsBackupChannelsBackupPlanAssociationsService(self)
    self.projects_locations_backupChannels_backupPlanBindings = self.ProjectsLocationsBackupChannelsBackupPlanBindingsService(self)
    self.projects_locations_backupChannels = self.ProjectsLocationsBackupChannelsService(self)
    self.projects_locations_backupPlans_backups_volumeBackups = self.ProjectsLocationsBackupPlansBackupsVolumeBackupsService(self)
    self.projects_locations_backupPlans_backups = self.ProjectsLocationsBackupPlansBackupsService(self)
    self.projects_locations_backupPlans = self.ProjectsLocationsBackupPlansService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_restoreChannels_restorePlanAssociations = self.ProjectsLocationsRestoreChannelsRestorePlanAssociationsService(self)
    self.projects_locations_restoreChannels_restorePlanBindings = self.ProjectsLocationsRestoreChannelsRestorePlanBindingsService(self)
    self.projects_locations_restoreChannels = self.ProjectsLocationsRestoreChannelsService(self)
    self.projects_locations_restorePlans_restores_volumeRestores = self.ProjectsLocationsRestorePlansRestoresVolumeRestoresService(self)
    self.projects_locations_restorePlans_restores = self.ProjectsLocationsRestorePlansRestoresService(self)
    self.projects_locations_restorePlans = self.ProjectsLocationsRestorePlansService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsBackupChannelsBackupPlanAssociationsService(base_api.BaseApiService):
    """Service class for the projects_locations_backupChannels_backupPlanAssociations resource."""

    _NAME = 'projects_locations_backupChannels_backupPlanAssociations'

    def __init__(self, client):
      super(GkebackupV1.ProjectsLocationsBackupChannelsBackupPlanAssociationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieve the details of a single BackupPlanAssociation.

      Args:
        request: (GkebackupProjectsLocationsBackupChannelsBackupPlanAssociationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BackupPlanAssociation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupChannels/{backupChannelsId}/backupPlanAssociations/{backupPlanAssociationsId}',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupChannels.backupPlanAssociations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupChannelsBackupPlanAssociationsGetRequest',
        response_type_name='BackupPlanAssociation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BackupPlanAssociations in a given location.

      Args:
        request: (GkebackupProjectsLocationsBackupChannelsBackupPlanAssociationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBackupPlanAssociationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupChannels/{backupChannelsId}/backupPlanAssociations',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupChannels.backupPlanAssociations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/backupPlanAssociations',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupChannelsBackupPlanAssociationsListRequest',
        response_type_name='ListBackupPlanAssociationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsBackupChannelsBackupPlanBindingsService(base_api.BaseApiService):
    """Service class for the projects_locations_backupChannels_backupPlanBindings resource."""

    _NAME = 'projects_locations_backupChannels_backupPlanBindings'

    def __init__(self, client):
      super(GkebackupV1.ProjectsLocationsBackupChannelsBackupPlanBindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieve the details of a single BackupPlanBinding.

      Args:
        request: (GkebackupProjectsLocationsBackupChannelsBackupPlanBindingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BackupPlanBinding) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupChannels/{backupChannelsId}/backupPlanBindings/{backupPlanBindingsId}',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupChannels.backupPlanBindings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupChannelsBackupPlanBindingsGetRequest',
        response_type_name='BackupPlanBinding',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BackupPlanBindings in a given location.

      Args:
        request: (GkebackupProjectsLocationsBackupChannelsBackupPlanBindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBackupPlanBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupChannels/{backupChannelsId}/backupPlanBindings',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupChannels.backupPlanBindings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/backupPlanBindings',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupChannelsBackupPlanBindingsListRequest',
        response_type_name='ListBackupPlanBindingsResponse',
        supports_download=False,
    )

  class ProjectsLocationsBackupChannelsService(base_api.BaseApiService):
    """Service class for the projects_locations_backupChannels resource."""

    _NAME = 'projects_locations_backupChannels'

    def __init__(self, client):
      super(GkebackupV1.ProjectsLocationsBackupChannelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new BackupChannel in a given location.

      Args:
        request: (GkebackupProjectsLocationsBackupChannelsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupChannels',
        http_method='POST',
        method_id='gkebackup.projects.locations.backupChannels.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['backupChannelId'],
        relative_path='v1/{+parent}/backupChannels',
        request_field='backupChannel',
        request_type_name='GkebackupProjectsLocationsBackupChannelsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing BackupChannel.

      Args:
        request: (GkebackupProjectsLocationsBackupChannelsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupChannels/{backupChannelsId}',
        http_method='DELETE',
        method_id='gkebackup.projects.locations.backupChannels.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupChannelsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve the details of a single BackupChannel.

      Args:
        request: (GkebackupProjectsLocationsBackupChannelsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BackupChannel) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupChannels/{backupChannelsId}',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupChannels.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupChannelsGetRequest',
        response_type_name='BackupChannel',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BackupChannels in a given location.

      Args:
        request: (GkebackupProjectsLocationsBackupChannelsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBackupChannelsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupChannels',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupChannels.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/backupChannels',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupChannelsListRequest',
        response_type_name='ListBackupChannelsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a BackupChannel.

      Args:
        request: (GkebackupProjectsLocationsBackupChannelsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupChannels/{backupChannelsId}',
        http_method='PATCH',
        method_id='gkebackup.projects.locations.backupChannels.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='backupChannel',
        request_type_name='GkebackupProjectsLocationsBackupChannelsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsBackupPlansBackupsVolumeBackupsService(base_api.BaseApiService):
    """Service class for the projects_locations_backupPlans_backups_volumeBackups resource."""

    _NAME = 'projects_locations_backupPlans_backups_volumeBackups'

    def __init__(self, client):
      super(GkebackupV1.ProjectsLocationsBackupPlansBackupsVolumeBackupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieve the details of a single VolumeBackup.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VolumeBackup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/backups/{backupsId}/volumeBackups/{volumeBackupsId}',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupPlans.backups.volumeBackups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsGetRequest',
        response_type_name='VolumeBackup',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/backups/{backupsId}/volumeBackups/{volumeBackupsId}:getIamPolicy',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupPlans.backups.volumeBackups.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the VolumeBackups for a given Backup.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListVolumeBackupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/backups/{backupsId}/volumeBackups',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupPlans.backups.volumeBackups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/volumeBackups',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsListRequest',
        response_type_name='ListVolumeBackupsResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/backups/{backupsId}/volumeBackups/{volumeBackupsId}:setIamPolicy',
        http_method='POST',
        method_id='gkebackup.projects.locations.backupPlans.backups.volumeBackups.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/backups/{backupsId}/volumeBackups/{volumeBackupsId}:testIamPermissions',
        http_method='POST',
        method_id='gkebackup.projects.locations.backupPlans.backups.volumeBackups.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsBackupPlansBackupsService(base_api.BaseApiService):
    """Service class for the projects_locations_backupPlans_backups resource."""

    _NAME = 'projects_locations_backupPlans_backups'

    def __init__(self, client):
      super(GkebackupV1.ProjectsLocationsBackupPlansBackupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Backup for the given BackupPlan.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansBackupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/backups',
        http_method='POST',
        method_id='gkebackup.projects.locations.backupPlans.backups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['backupId'],
        relative_path='v1/{+parent}/backups',
        request_field='backup',
        request_type_name='GkebackupProjectsLocationsBackupPlansBackupsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing Backup.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansBackupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/backups/{backupsId}',
        http_method='DELETE',
        method_id='gkebackup.projects.locations.backupPlans.backups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupPlansBackupsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve the details of a single Backup.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansBackupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Backup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/backups/{backupsId}',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupPlans.backups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupPlansBackupsGetRequest',
        response_type_name='Backup',
        supports_download=False,
    )

    def GetBackupIndexDownloadUrl(self, request, global_params=None):
      r"""Retrieve the link to the backupIndex.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansBackupsGetBackupIndexDownloadUrlRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GetBackupIndexDownloadUrlResponse) The response message.
      """
      config = self.GetMethodConfig('GetBackupIndexDownloadUrl')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetBackupIndexDownloadUrl.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/backups/{backupsId}:getBackupIndexDownloadUrl',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupPlans.backups.getBackupIndexDownloadUrl',
        ordered_params=['backup'],
        path_params=['backup'],
        query_params=[],
        relative_path='v1/{+backup}:getBackupIndexDownloadUrl',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupPlansBackupsGetBackupIndexDownloadUrlRequest',
        response_type_name='GetBackupIndexDownloadUrlResponse',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansBackupsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/backups/{backupsId}:getIamPolicy',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupPlans.backups.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupPlansBackupsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Backups for a given BackupPlan.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansBackupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBackupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/backups',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupPlans.backups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v1/{+parent}/backups',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupPlansBackupsListRequest',
        response_type_name='ListBackupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a Backup.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansBackupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/backups/{backupsId}',
        http_method='PATCH',
        method_id='gkebackup.projects.locations.backupPlans.backups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='backup',
        request_type_name='GkebackupProjectsLocationsBackupPlansBackupsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansBackupsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/backups/{backupsId}:setIamPolicy',
        http_method='POST',
        method_id='gkebackup.projects.locations.backupPlans.backups.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='GkebackupProjectsLocationsBackupPlansBackupsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansBackupsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/backups/{backupsId}:testIamPermissions',
        http_method='POST',
        method_id='gkebackup.projects.locations.backupPlans.backups.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='GkebackupProjectsLocationsBackupPlansBackupsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsBackupPlansService(base_api.BaseApiService):
    """Service class for the projects_locations_backupPlans resource."""

    _NAME = 'projects_locations_backupPlans'

    def __init__(self, client):
      super(GkebackupV1.ProjectsLocationsBackupPlansService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new BackupPlan in a given location.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans',
        http_method='POST',
        method_id='gkebackup.projects.locations.backupPlans.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['backupPlanId'],
        relative_path='v1/{+parent}/backupPlans',
        request_field='backupPlan',
        request_type_name='GkebackupProjectsLocationsBackupPlansCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing BackupPlan.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}',
        http_method='DELETE',
        method_id='gkebackup.projects.locations.backupPlans.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupPlansDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve the details of a single BackupPlan.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BackupPlan) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupPlans.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupPlansGetRequest',
        response_type_name='BackupPlan',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}:getIamPolicy',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupPlans.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupPlansGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BackupPlans in a given location.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBackupPlansResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans',
        http_method='GET',
        method_id='gkebackup.projects.locations.backupPlans.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/backupPlans',
        request_field='',
        request_type_name='GkebackupProjectsLocationsBackupPlansListRequest',
        response_type_name='ListBackupPlansResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a BackupPlan.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}',
        http_method='PATCH',
        method_id='gkebackup.projects.locations.backupPlans.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='backupPlan',
        request_type_name='GkebackupProjectsLocationsBackupPlansPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}:setIamPolicy',
        http_method='POST',
        method_id='gkebackup.projects.locations.backupPlans.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='GkebackupProjectsLocationsBackupPlansSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (GkebackupProjectsLocationsBackupPlansTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}:testIamPermissions',
        http_method='POST',
        method_id='gkebackup.projects.locations.backupPlans.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='GkebackupProjectsLocationsBackupPlansTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(GkebackupV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (GkebackupProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='gkebackup.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleLongrunningCancelOperationRequest',
        request_type_name='GkebackupProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (GkebackupProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='gkebackup.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (GkebackupProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='gkebackup.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (GkebackupProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='gkebackup.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='GkebackupProjectsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsRestoreChannelsRestorePlanAssociationsService(base_api.BaseApiService):
    """Service class for the projects_locations_restoreChannels_restorePlanAssociations resource."""

    _NAME = 'projects_locations_restoreChannels_restorePlanAssociations'

    def __init__(self, client):
      super(GkebackupV1.ProjectsLocationsRestoreChannelsRestorePlanAssociationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieve the details of a single RestorePlanAssociation.

      Args:
        request: (GkebackupProjectsLocationsRestoreChannelsRestorePlanAssociationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RestorePlanAssociation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restoreChannels/{restoreChannelsId}/restorePlanAssociations/{restorePlanAssociationsId}',
        http_method='GET',
        method_id='gkebackup.projects.locations.restoreChannels.restorePlanAssociations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestoreChannelsRestorePlanAssociationsGetRequest',
        response_type_name='RestorePlanAssociation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists RestorePlanAssociations in a given location.

      Args:
        request: (GkebackupProjectsLocationsRestoreChannelsRestorePlanAssociationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRestorePlanAssociationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restoreChannels/{restoreChannelsId}/restorePlanAssociations',
        http_method='GET',
        method_id='gkebackup.projects.locations.restoreChannels.restorePlanAssociations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/restorePlanAssociations',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestoreChannelsRestorePlanAssociationsListRequest',
        response_type_name='ListRestorePlanAssociationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsRestoreChannelsRestorePlanBindingsService(base_api.BaseApiService):
    """Service class for the projects_locations_restoreChannels_restorePlanBindings resource."""

    _NAME = 'projects_locations_restoreChannels_restorePlanBindings'

    def __init__(self, client):
      super(GkebackupV1.ProjectsLocationsRestoreChannelsRestorePlanBindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieve the details of a single RestorePlanBinding.

      Args:
        request: (GkebackupProjectsLocationsRestoreChannelsRestorePlanBindingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RestorePlanBinding) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restoreChannels/{restoreChannelsId}/restorePlanBindings/{restorePlanBindingsId}',
        http_method='GET',
        method_id='gkebackup.projects.locations.restoreChannels.restorePlanBindings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestoreChannelsRestorePlanBindingsGetRequest',
        response_type_name='RestorePlanBinding',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists RestorePlanBindings in a given location.

      Args:
        request: (GkebackupProjectsLocationsRestoreChannelsRestorePlanBindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRestorePlanBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restoreChannels/{restoreChannelsId}/restorePlanBindings',
        http_method='GET',
        method_id='gkebackup.projects.locations.restoreChannels.restorePlanBindings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/restorePlanBindings',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestoreChannelsRestorePlanBindingsListRequest',
        response_type_name='ListRestorePlanBindingsResponse',
        supports_download=False,
    )

  class ProjectsLocationsRestoreChannelsService(base_api.BaseApiService):
    """Service class for the projects_locations_restoreChannels resource."""

    _NAME = 'projects_locations_restoreChannels'

    def __init__(self, client):
      super(GkebackupV1.ProjectsLocationsRestoreChannelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new RestoreChannel in a given location.

      Args:
        request: (GkebackupProjectsLocationsRestoreChannelsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restoreChannels',
        http_method='POST',
        method_id='gkebackup.projects.locations.restoreChannels.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['restoreChannelId'],
        relative_path='v1/{+parent}/restoreChannels',
        request_field='restoreChannel',
        request_type_name='GkebackupProjectsLocationsRestoreChannelsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing RestoreChannel.

      Args:
        request: (GkebackupProjectsLocationsRestoreChannelsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restoreChannels/{restoreChannelsId}',
        http_method='DELETE',
        method_id='gkebackup.projects.locations.restoreChannels.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestoreChannelsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve the details of a single RestoreChannel.

      Args:
        request: (GkebackupProjectsLocationsRestoreChannelsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RestoreChannel) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restoreChannels/{restoreChannelsId}',
        http_method='GET',
        method_id='gkebackup.projects.locations.restoreChannels.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestoreChannelsGetRequest',
        response_type_name='RestoreChannel',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists RestoreChannels in a given location.

      Args:
        request: (GkebackupProjectsLocationsRestoreChannelsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRestoreChannelsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restoreChannels',
        http_method='GET',
        method_id='gkebackup.projects.locations.restoreChannels.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/restoreChannels',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestoreChannelsListRequest',
        response_type_name='ListRestoreChannelsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a RestoreChannel.

      Args:
        request: (GkebackupProjectsLocationsRestoreChannelsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restoreChannels/{restoreChannelsId}',
        http_method='PATCH',
        method_id='gkebackup.projects.locations.restoreChannels.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='restoreChannel',
        request_type_name='GkebackupProjectsLocationsRestoreChannelsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsRestorePlansRestoresVolumeRestoresService(base_api.BaseApiService):
    """Service class for the projects_locations_restorePlans_restores_volumeRestores resource."""

    _NAME = 'projects_locations_restorePlans_restores_volumeRestores'

    def __init__(self, client):
      super(GkebackupV1.ProjectsLocationsRestorePlansRestoresVolumeRestoresService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieve the details of a single VolumeRestore.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VolumeRestore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}/restores/{restoresId}/volumeRestores/{volumeRestoresId}',
        http_method='GET',
        method_id='gkebackup.projects.locations.restorePlans.restores.volumeRestores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresGetRequest',
        response_type_name='VolumeRestore',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}/restores/{restoresId}/volumeRestores/{volumeRestoresId}:getIamPolicy',
        http_method='GET',
        method_id='gkebackup.projects.locations.restorePlans.restores.volumeRestores.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the VolumeRestores for a given Restore.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListVolumeRestoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}/restores/{restoresId}/volumeRestores',
        http_method='GET',
        method_id='gkebackup.projects.locations.restorePlans.restores.volumeRestores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/volumeRestores',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresListRequest',
        response_type_name='ListVolumeRestoresResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}/restores/{restoresId}/volumeRestores/{volumeRestoresId}:setIamPolicy',
        http_method='POST',
        method_id='gkebackup.projects.locations.restorePlans.restores.volumeRestores.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}/restores/{restoresId}/volumeRestores/{volumeRestoresId}:testIamPermissions',
        http_method='POST',
        method_id='gkebackup.projects.locations.restorePlans.restores.volumeRestores.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsRestorePlansRestoresService(base_api.BaseApiService):
    """Service class for the projects_locations_restorePlans_restores resource."""

    _NAME = 'projects_locations_restorePlans_restores'

    def __init__(self, client):
      super(GkebackupV1.ProjectsLocationsRestorePlansRestoresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Restore for the given RestorePlan.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansRestoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}/restores',
        http_method='POST',
        method_id='gkebackup.projects.locations.restorePlans.restores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['restoreId'],
        relative_path='v1/{+parent}/restores',
        request_field='restore',
        request_type_name='GkebackupProjectsLocationsRestorePlansRestoresCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing Restore.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansRestoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}/restores/{restoresId}',
        http_method='DELETE',
        method_id='gkebackup.projects.locations.restorePlans.restores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestorePlansRestoresDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the details of a single Restore.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansRestoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Restore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}/restores/{restoresId}',
        http_method='GET',
        method_id='gkebackup.projects.locations.restorePlans.restores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestorePlansRestoresGetRequest',
        response_type_name='Restore',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansRestoresGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}/restores/{restoresId}:getIamPolicy',
        http_method='GET',
        method_id='gkebackup.projects.locations.restorePlans.restores.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestorePlansRestoresGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Restores for a given RestorePlan.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansRestoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRestoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}/restores',
        http_method='GET',
        method_id='gkebackup.projects.locations.restorePlans.restores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/restores',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestorePlansRestoresListRequest',
        response_type_name='ListRestoresResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a Restore.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansRestoresPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}/restores/{restoresId}',
        http_method='PATCH',
        method_id='gkebackup.projects.locations.restorePlans.restores.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='restore',
        request_type_name='GkebackupProjectsLocationsRestorePlansRestoresPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansRestoresSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}/restores/{restoresId}:setIamPolicy',
        http_method='POST',
        method_id='gkebackup.projects.locations.restorePlans.restores.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='GkebackupProjectsLocationsRestorePlansRestoresSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansRestoresTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}/restores/{restoresId}:testIamPermissions',
        http_method='POST',
        method_id='gkebackup.projects.locations.restorePlans.restores.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='GkebackupProjectsLocationsRestorePlansRestoresTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsRestorePlansService(base_api.BaseApiService):
    """Service class for the projects_locations_restorePlans resource."""

    _NAME = 'projects_locations_restorePlans'

    def __init__(self, client):
      super(GkebackupV1.ProjectsLocationsRestorePlansService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new RestorePlan in a given location.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans',
        http_method='POST',
        method_id='gkebackup.projects.locations.restorePlans.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['restorePlanId'],
        relative_path='v1/{+parent}/restorePlans',
        request_field='restorePlan',
        request_type_name='GkebackupProjectsLocationsRestorePlansCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing RestorePlan.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}',
        http_method='DELETE',
        method_id='gkebackup.projects.locations.restorePlans.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestorePlansDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve the details of a single RestorePlan.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RestorePlan) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}',
        http_method='GET',
        method_id='gkebackup.projects.locations.restorePlans.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestorePlansGetRequest',
        response_type_name='RestorePlan',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}:getIamPolicy',
        http_method='GET',
        method_id='gkebackup.projects.locations.restorePlans.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestorePlansGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists RestorePlans in a given location.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRestorePlansResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans',
        http_method='GET',
        method_id='gkebackup.projects.locations.restorePlans.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/restorePlans',
        request_field='',
        request_type_name='GkebackupProjectsLocationsRestorePlansListRequest',
        response_type_name='ListRestorePlansResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a RestorePlan.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}',
        http_method='PATCH',
        method_id='gkebackup.projects.locations.restorePlans.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='restorePlan',
        request_type_name='GkebackupProjectsLocationsRestorePlansPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}:setIamPolicy',
        http_method='POST',
        method_id='gkebackup.projects.locations.restorePlans.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='GkebackupProjectsLocationsRestorePlansSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (GkebackupProjectsLocationsRestorePlansTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/restorePlans/{restorePlansId}:testIamPermissions',
        http_method='POST',
        method_id='gkebackup.projects.locations.restorePlans.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='GkebackupProjectsLocationsRestorePlansTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(GkebackupV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (GkebackupProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='gkebackup.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkebackupProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (GkebackupProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='gkebackup.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='GkebackupProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(GkebackupV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
