"""Generated message classes for networkservices version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'networkservices'


class AWSV4Signature(_messages.Message):
  r"""The configuration needed to generate an AWS V4 Signature

  Fields:
    accessKeyId: Required. The access key ID that your origin uses to identify
      the key.
    originRegion: Required. The name of the AWS region that your origin is in.
    secretAccessKeyVersion: Required. The Secret Manager secret version of the
      secret access key used by your origin. This is the resource name of the
      secret version in the format `projects/*/secrets/*/versions/*` where the
      `*` values are replaced by the project, the secret, and the version that
      you require.
  """

  accessKeyId = _messages.StringField(1)
  originRegion = _messages.StringField(2)
  secretAccessKeyVersion = _messages.StringField(3)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class AuthzExtension(_messages.Message):
  r"""`AuthzExtension` is a resource that allows traffic forwarding to a
  callout backend service to make an authorization decision.

  Enums:
    LoadBalancingSchemeValueValuesEnum: Required. All backend services and
      forwarding rules referenced by this extension must share the same load
      balancing scheme. Supported values: `INTERNAL_MANAGED`,
      `EXTERNAL_MANAGED`. For more information, refer to [Backend services
      overview](https://cloud.google.com/load-balancing/docs/backend-service).
    WireFormatValueValuesEnum: Optional. The format of communication supported
      by the callout extension. If not specified, the default value
      `EXT_PROC_GRPC` is used.

  Messages:
    LabelsValue: Optional. Set of labels associated with the `AuthzExtension`
      resource. The format must comply with [the requirements for
      labels](/compute/docs/labeling-resources#requirements) for Google Cloud
      resources.
    MetadataValue: Optional. The metadata provided here is included as part of
      the `metadata_context` (of type `google.protobuf.Struct`) in the
      `ProcessingRequest` message sent to the extension server. The metadata
      is available under the namespace `com.google.authz_extension.`. The
      following variables are supported in the metadata Struct:
      `{forwarding_rule_id}` - substituted with the forwarding rule's fully
      qualified resource name.

  Fields:
    authority: Required. The `:authority` header in the gRPC request sent from
      Envoy to the extension service.
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A human-readable description of the resource.
    failOpen: Optional. Determines how the proxy behaves if the call to the
      extension fails or times out. When set to `TRUE`, request or response
      processing continues without error. Any subsequent extensions in the
      extension chain are also executed. When set to `FALSE` or the default
      setting of `FALSE` is used, one of the following happens: * If response
      headers have not been delivered to the downstream client, a generic 500
      error is returned to the client. The error response can be tailored by
      configuring a custom error response in the load balancer. * If response
      headers have been delivered, then the HTTP stream to the downstream
      client is reset.
    forwardHeaders: Optional. List of the HTTP headers to forward to the
      extension (from the client). If omitted, all headers are sent. Each
      element is a string indicating the header name.
    labels: Optional. Set of labels associated with the `AuthzExtension`
      resource. The format must comply with [the requirements for
      labels](/compute/docs/labeling-resources#requirements) for Google Cloud
      resources.
    loadBalancingScheme: Required. All backend services and forwarding rules
      referenced by this extension must share the same load balancing scheme.
      Supported values: `INTERNAL_MANAGED`, `EXTERNAL_MANAGED`. For more
      information, refer to [Backend services
      overview](https://cloud.google.com/load-balancing/docs/backend-service).
    metadata: Optional. The metadata provided here is included as part of the
      `metadata_context` (of type `google.protobuf.Struct`) in the
      `ProcessingRequest` message sent to the extension server. The metadata
      is available under the namespace `com.google.authz_extension.`. The
      following variables are supported in the metadata Struct:
      `{forwarding_rule_id}` - substituted with the forwarding rule's fully
      qualified resource name.
    name: Required. Identifier. Name of the `AuthzExtension` resource in the
      following format: `projects/{project}/locations/{location}/authzExtensio
      ns/{authz_extension}`.
    service: Required. The reference to the service that runs the extension.
      To configure a callout extension, `service` must be a fully-qualified
      reference to a [backend service](https://cloud.google.com/compute/docs/r
      eference/rest/v1/backendServices) in the format: `https://www.googleapis
      .com/compute/v1/projects/{project}/regions/{region}/backendServices/{bac
      kendService}` or `https://www.googleapis.com/compute/v1/projects/{projec
      t}/global/backendServices/{backendService}`.
    timeout: Required. Specifies the timeout for each individual message on
      the stream. The timeout must be between 10-10000 milliseconds.
    updateTime: Output only. The timestamp when the resource was updated.
    wireFormat: Optional. The format of communication supported by the callout
      extension. If not specified, the default value `EXT_PROC_GRPC` is used.
  """

  class LoadBalancingSchemeValueValuesEnum(_messages.Enum):
    r"""Required. All backend services and forwarding rules referenced by this
    extension must share the same load balancing scheme. Supported values:
    `INTERNAL_MANAGED`, `EXTERNAL_MANAGED`. For more information, refer to
    [Backend services overview](https://cloud.google.com/load-
    balancing/docs/backend-service).

    Values:
      LOAD_BALANCING_SCHEME_UNSPECIFIED: Default value. Do not use.
      INTERNAL_MANAGED: Signifies that this is used for Internal HTTP(S) Load
        Balancing.
      EXTERNAL_MANAGED: Signifies that this is used for External Managed
        HTTP(S) Load Balancing.
    """
    LOAD_BALANCING_SCHEME_UNSPECIFIED = 0
    INTERNAL_MANAGED = 1
    EXTERNAL_MANAGED = 2

  class WireFormatValueValuesEnum(_messages.Enum):
    r"""Optional. The format of communication supported by the callout
    extension. If not specified, the default value `EXT_PROC_GRPC` is used.

    Values:
      WIRE_FORMAT_UNSPECIFIED: Not specified.
      EXT_PROC_GRPC: The extension service uses ext_proc gRPC API over a gRPC
        stream. This is the default value if the wire format is not specified.
        The backend service for the extension must use HTTP2 or H2C as the
        protocol. All `supported_events` for a client request are sent as part
        of the same gRPC stream.
    """
    WIRE_FORMAT_UNSPECIFIED = 0
    EXT_PROC_GRPC = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of labels associated with the `AuthzExtension` resource.
    The format must comply with [the requirements for
    labels](/compute/docs/labeling-resources#requirements) for Google Cloud
    resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. The metadata provided here is included as part of the
    `metadata_context` (of type `google.protobuf.Struct`) in the
    `ProcessingRequest` message sent to the extension server. The metadata is
    available under the namespace `com.google.authz_extension.`. The following
    variables are supported in the metadata Struct: `{forwarding_rule_id}` -
    substituted with the forwarding rule's fully qualified resource name.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  authority = _messages.StringField(1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  failOpen = _messages.BooleanField(4)
  forwardHeaders = _messages.StringField(5, repeated=True)
  labels = _messages.MessageField('LabelsValue', 6)
  loadBalancingScheme = _messages.EnumField('LoadBalancingSchemeValueValuesEnum', 7)
  metadata = _messages.MessageField('MetadataValue', 8)
  name = _messages.StringField(9)
  service = _messages.StringField(10)
  timeout = _messages.StringField(11)
  updateTime = _messages.StringField(12)
  wireFormat = _messages.EnumField('WireFormatValueValuesEnum', 13)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CDNPolicy(_messages.Message):
  r"""The CDN policy to apply to the configured route.

  Enums:
    CacheModeValueValuesEnum: Optional. Set the CacheMode used by this route.
      BYPASS_CACHE and USE_ORIGIN_HEADERS proxy the origin's headers. Other
      cache modes pass Cache-Control to the client. Use client_ttl to override
      what is sent to the client.
    SignedRequestModeValueValuesEnum: Optional. Specifies whether to enforce
      signed requests. The default value is DISABLED, which means all content
      is public, and does not authorize access. You must also set a
      signed_request_keyset to enable signed requests. When set to
      REQUIRE_SIGNATURES or REQUIRE_TOKENS, all matching requests get their
      signature validated. Requests that aren't signed with the corresponding
      private key, or that are otherwise invalid (such as expired or do not
      match the signature, IP address, or header) are rejected with an HTTP
      403 error. If logging is turned on, then invalid requests are also
      logged.

  Messages:
    NegativeCachingPolicyValue: Optional. A cache TTL for the specified HTTP
      status code. negative_caching must be enabled to configure
      `negative_caching_policy`. The following limitations apply: - Omitting
      the policy and leaving `negative_caching` enabled uses the default TTLs
      for each status code, defined in `negative_caching`. - TTLs must be >=
      `0` (where `0` is "always revalidate") and <= `86400s` (1 day) You can
      set only the following status codes: - HTTP redirection (`300`, `301`,
      `302`, `307`, or `308`) - Client error (`400`, `403`, `404`, `405`,
      `410`, `421`, or `451`) - Server error (`500`, `501`, `502`, `503`, or
      `504`) When you specify an explicit `negative_caching_policy`, ensure
      that you also specify a cache TTL for all response codes that you wish
      to cache. The CDNPolicy doesn't apply any default negative caching when
      a policy exists.

  Fields:
    addSignatures: Optional. Enables signature generation or propagation on
      this route. This field can only be specified when signed_request_mode is
      set to REQUIRE_TOKENS.
    cacheKeyPolicy: Optional. The request parameters that contribute to the
      cache key.
    cacheMode: Optional. Set the CacheMode used by this route. BYPASS_CACHE
      and USE_ORIGIN_HEADERS proxy the origin's headers. Other cache modes
      pass Cache-Control to the client. Use client_ttl to override what is
      sent to the client.
    clientTtl: Optional. Specifies a separate client (such as browser client)
      TTL, separate from the TTL used by the edge caches. Leaving this empty
      uses the same cache TTL for both the CDN and the client-facing response.
      - The TTL must be > `0` and <= `86400s` (1 day) - The `client_ttl`
      cannot be larger than the default_ttl (if set) - Fractions of a second
      are not allowed. Omit this field to use the `default_ttl`, or the max-
      age set by the origin, as the client-facing TTL. When the CacheMode is
      set to USE_ORIGIN_HEADERS or BYPASS_CACHE, you must omit this field.
    defaultTtl: Optional. Specifies the default TTL for cached content served
      by this origin for responses that do not have an existing valid TTL
      (max-age or s-maxage). Defaults to `3600s` (1 hour). - The TTL must be
      >= `0` and <= `31,536,000` seconds (1 year) - Setting a TTL of `0` means
      "always revalidate" (equivalent to must-revalidate) - The value of
      `default_ttl` cannot be set to a value greater than that of max_ttl. -
      Fractions of a second are not allowed. - When the CacheMode is set to
      FORCE_CACHE_ALL, the `default_ttl` overwrites the TTL set in all
      responses. Infrequently accessed objects might be evicted from the cache
      before the defined TTL. Objects that expire are revalidated with the
      origin. When the CacheMode is set to USE_ORIGIN_HEADERS or BYPASS_CACHE,
      you must omit this field.
    maxTtl: Optional. The maximum allowed TTL for cached content served by
      this origin. Defaults to `86400s` (1 day). Cache directives that attempt
      to set a max-age or s-maxage higher than this, or an Expires header more
      than `max_ttl` seconds in the future are capped at the value of
      `max_ttl`, as if it were the value of an s-maxage Cache-Control
      directive. - The TTL must be >= `0` and <= `31,536,000` seconds (1 year)
      - Setting a TTL of `0` means "always revalidate" - The value of
      `max_ttl` must be equal to or greater than default_ttl. - Fractions of a
      second are not allowed. When CacheMode is set to USE_ORIGIN_HEADERS,
      FORCE_CACHE_ALL, or BYPASS_CACHE, you must omit this field.
    negativeCaching: Optional. Negative caching allows setting per-status code
      TTLs, in order to apply fine-grained caching for common errors or
      redirects. This can reduce the load on your origin and improve end-user
      experience by reducing response latency. By default, the CDNPolicy
      applies the following default TTLs to these status codes: - **10m**:
      HTTP 300 (Multiple Choice), 301, 308 (Permanent Redirects) - **120s**:
      HTTP 404 (Not Found), 410 (Gone), 451 (Unavailable For Legal Reasons) -
      **60s**: HTTP 405 (Method Not Found), 501 (Not Implemented) These
      defaults can be overridden in negative_caching_policy
    negativeCachingPolicy: Optional. A cache TTL for the specified HTTP status
      code. negative_caching must be enabled to configure
      `negative_caching_policy`. The following limitations apply: - Omitting
      the policy and leaving `negative_caching` enabled uses the default TTLs
      for each status code, defined in `negative_caching`. - TTLs must be >=
      `0` (where `0` is "always revalidate") and <= `86400s` (1 day) You can
      set only the following status codes: - HTTP redirection (`300`, `301`,
      `302`, `307`, or `308`) - Client error (`400`, `403`, `404`, `405`,
      `410`, `421`, or `451`) - Server error (`500`, `501`, `502`, `503`, or
      `504`) When you specify an explicit `negative_caching_policy`, ensure
      that you also specify a cache TTL for all response codes that you wish
      to cache. The CDNPolicy doesn't apply any default negative caching when
      a policy exists.
    signedRequestKeyset: Optional. The EdgeCacheKeyset containing the set of
      public keys used to validate signed requests at the edge. The following
      are both valid paths to an `EdgeCacheKeyset` resource: *
      `projects/project/locations/global/edgeCacheKeysets/yourKeyset` *
      `yourKeyset` SignedRequestMode must be set to a value other than
      DISABLED when a keyset is provided.
    signedRequestMaximumExpirationTtl: Optional. Limits how far into the
      future the expiration time of a signed request can be. When set, a
      signed request is rejected if its expiration time is later than `now` +
      `signed_request_maximum_expiration_ttl`, where `now` is the time at
      which the signed request is first handled by the CDN. - The TTL must be
      > 0. - Fractions of a second are not allowed. By default,
      `signed_request_maximum_expiration_ttl` is not set and the expiration
      time of a signed request might be arbitrarily far into future.
    signedRequestMode: Optional. Specifies whether to enforce signed requests.
      The default value is DISABLED, which means all content is public, and
      does not authorize access. You must also set a signed_request_keyset to
      enable signed requests. When set to REQUIRE_SIGNATURES or
      REQUIRE_TOKENS, all matching requests get their signature validated.
      Requests that aren't signed with the corresponding private key, or that
      are otherwise invalid (such as expired or do not match the signature, IP
      address, or header) are rejected with an HTTP 403 error. If logging is
      turned on, then invalid requests are also logged.
    signedTokenOptions: Optional. Any additional options for signed tokens.
      `signed_token_options` can only be specified when `signed_request_mode`
      is `REQUIRE_TOKENS`.
  """

  class CacheModeValueValuesEnum(_messages.Enum):
    r"""Optional. Set the CacheMode used by this route. BYPASS_CACHE and
    USE_ORIGIN_HEADERS proxy the origin's headers. Other cache modes pass
    Cache-Control to the client. Use client_ttl to override what is sent to
    the client.

    Values:
      CACHE_MODE_UNSPECIFIED: Unspecified value. Defaults to
        `CACHE_ALL_STATIC`.
      CACHE_ALL_STATIC: Automatically cache static content, including common
        image formats, media (video and audio), and web assets (JavaScript and
        CSS). Requests and responses that are marked as uncacheable, as well
        as dynamic content (including HTML), aren't cached.
      USE_ORIGIN_HEADERS: Only cache responses with valid HTTP caching
        directives. Responses without these headers aren't cached at Google's
        edge, and require a full trip to the origin on every request,
        potentially impacting performance and increasing load on the origin
        server.
      FORCE_CACHE_ALL: Cache all content, ignoring any `private`, `no-store`
        or `no-cache` directives in Cache-Control response headers.
        **Warning:** this might result in caching private, per-user (user
        identifiable) content. Only enable this on routes where the
        EdgeCacheOrigin doesn't serve private or dynamic content, such as
        storage buckets.
      BYPASS_CACHE: Bypass all caching for requests that match routes with
        this CDNPolicy applied. Enabling this causes the edge cache to ignore
        all HTTP caching directives. All responses are fulfilled from the
        origin.
    """
    CACHE_MODE_UNSPECIFIED = 0
    CACHE_ALL_STATIC = 1
    USE_ORIGIN_HEADERS = 2
    FORCE_CACHE_ALL = 3
    BYPASS_CACHE = 4

  class SignedRequestModeValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies whether to enforce signed requests. The default
    value is DISABLED, which means all content is public, and does not
    authorize access. You must also set a signed_request_keyset to enable
    signed requests. When set to REQUIRE_SIGNATURES or REQUIRE_TOKENS, all
    matching requests get their signature validated. Requests that aren't
    signed with the corresponding private key, or that are otherwise invalid
    (such as expired or do not match the signature, IP address, or header) are
    rejected with an HTTP 403 error. If logging is turned on, then invalid
    requests are also logged.

    Values:
      SIGNED_REQUEST_MODE_UNSPECIFIED: Unspecified value. Defaults to
        `DISABLED`.
      DISABLED: Do not enforce signed requests.
      REQUIRE_SIGNATURES: Enforce signed requests using query parameter, path
        component, or cookie signatures. All requests must have a valid
        signature. Requests that are missing the signature (URL or cookie-
        based) are rejected as if the signature was invalid.
      REQUIRE_TOKENS: Enforce signed requests using signed tokens. All
        requests must have a valid signed token. Requests that are missing a
        signed token (URL or cookie-based) are rejected as if the signed token
        was invalid.
    """
    SIGNED_REQUEST_MODE_UNSPECIFIED = 0
    DISABLED = 1
    REQUIRE_SIGNATURES = 2
    REQUIRE_TOKENS = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NegativeCachingPolicyValue(_messages.Message):
    r"""Optional. A cache TTL for the specified HTTP status code.
    negative_caching must be enabled to configure `negative_caching_policy`.
    The following limitations apply: - Omitting the policy and leaving
    `negative_caching` enabled uses the default TTLs for each status code,
    defined in `negative_caching`. - TTLs must be >= `0` (where `0` is "always
    revalidate") and <= `86400s` (1 day) You can set only the following status
    codes: - HTTP redirection (`300`, `301`, `302`, `307`, or `308`) - Client
    error (`400`, `403`, `404`, `405`, `410`, `421`, or `451`) - Server error
    (`500`, `501`, `502`, `503`, or `504`) When you specify an explicit
    `negative_caching_policy`, ensure that you also specify a cache TTL for
    all response codes that you wish to cache. The CDNPolicy doesn't apply any
    default negative caching when a policy exists.

    Messages:
      AdditionalProperty: An additional property for a
        NegativeCachingPolicyValue object.

    Fields:
      additionalProperties: Additional properties of type
        NegativeCachingPolicyValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NegativeCachingPolicyValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  addSignatures = _messages.MessageField('CDNPolicyAddSignaturesOptions', 1)
  cacheKeyPolicy = _messages.MessageField('CDNPolicyCacheKeyPolicy', 2)
  cacheMode = _messages.EnumField('CacheModeValueValuesEnum', 3)
  clientTtl = _messages.StringField(4)
  defaultTtl = _messages.StringField(5)
  maxTtl = _messages.StringField(6)
  negativeCaching = _messages.BooleanField(7)
  negativeCachingPolicy = _messages.MessageField('NegativeCachingPolicyValue', 8)
  signedRequestKeyset = _messages.StringField(9)
  signedRequestMaximumExpirationTtl = _messages.StringField(10)
  signedRequestMode = _messages.EnumField('SignedRequestModeValueValuesEnum', 11)
  signedTokenOptions = _messages.MessageField('CDNPolicySignedTokenOptions', 12)


class CDNPolicyAddSignaturesOptions(_messages.Message):
  r"""The configuration options for adding signatures to responses.

  Enums:
    ActionsValueListEntryValuesEnum:

  Fields:
    actions: Required. The actions to take to add signatures to responses. You
      must specify exactly one action.
    copiedParameters: Optional. The parameters to copy from the verified token
      to the generated token. Only the following parameters can be copied: *
      `PathGlobs` * `paths` * `acl` * `URLPrefix` * `IPRanges` * `SessionID` *
      `id` * `Data` * `data` * `payload` * `Headers` You can specify up to 6
      parameters to copy. A given parameter is be copied only if the parameter
      exists in the verified token. Parameter names are matched exactly as
      specified. The order of the parameters does not matter. Duplicates are
      not allowed. This field can only be specified when the `GENERATE_COOKIE`
      or `GENERATE_TOKEN_HLS_COOKIELESS` actions are specified.
    keyset: Optional. The keyset to use for signature generation. The
      following are both valid paths to an EdgeCacheKeyset resource: *
      `projects/project/locations/global/edgeCacheKeysets/yourKeyset` *
      `yourKeyset` This must be specified when the `GENERATE_COOKIE` or
      `GENERATE_TOKEN_HLS_COOKIELESS` actions are specified. This field can
      not be specified otherwise.
    tokenQueryParameter: Optional. The query parameter in which to put the
      generated token. If not specified, defaults to `edge-cache-token`. If
      specified, the name must be 1-64 characters long and match the regular
      expression `[a-zA-Z]([a-zA-Z0-9_-])*` which means the first character
      must be a letter, and all following characters must be a dash,
      underscore, letter or digit. This field can only be set when the
      `GENERATE_TOKEN_HLS_COOKIELESS` or `PROPAGATE_TOKEN_HLS_COOKIELESS`
      actions are specified.
    tokenTtl: Optional. The duration the token is valid for starting from the
      moment the token is first generated. Defaults to `86400s` (1 day). The
      TTL must be >= 0 and <= 604,800 seconds (1 week). This field can only be
      specified when the `GENERATE_COOKIE` or `GENERATE_TOKEN_HLS_COOKIELESS`
      actions are specified.
  """

  class ActionsValueListEntryValuesEnum(_messages.Enum):
    r"""ActionsValueListEntryValuesEnum enum type.

    Values:
      SIGNATURE_ACTION_UNSPECIFIED: It is an error to specify `UNSPECIFIED`.
      GENERATE_COOKIE: Generate a new signed request cookie and return the
        cookie in a Set-Cookie header of the response. This action cannot be
        combined with the `PROPAGATE_TOKEN_HLS_COOKIELESS` action.
      GENERATE_TOKEN_HLS_COOKIELESS: Generate a new signed request
        authentication token and return the new token by manipulating URLs in
        an HTTP Live Stream (HLS) playlist. This action cannot be combined
        with the `PROPAGATE_TOKEN_HLS_COOKIELESS` action.
      PROPAGATE_TOKEN_HLS_COOKIELESS: Copy the authentication token used in
        the request to the URLs in an HTTP Live Stream (HLS) playlist. This
        action cannot be combined with either the `GENERATE_COOKIE` action or
        the `GENERATE_TOKEN_HLS_COOKIELESS` action.
    """
    SIGNATURE_ACTION_UNSPECIFIED = 0
    GENERATE_COOKIE = 1
    GENERATE_TOKEN_HLS_COOKIELESS = 2
    PROPAGATE_TOKEN_HLS_COOKIELESS = 3

  actions = _messages.EnumField('ActionsValueListEntryValuesEnum', 1, repeated=True)
  copiedParameters = _messages.StringField(2, repeated=True)
  keyset = _messages.StringField(3)
  tokenQueryParameter = _messages.StringField(4)
  tokenTtl = _messages.StringField(5)


class CDNPolicyCacheKeyPolicy(_messages.Message):
  r"""The request parameters that contribute to the cache key.

  Fields:
    excludeHost: Optional. If `true`, exclude a request's host from the cache
      key. Requests with different hosts share content in the cache. If
      `false` (the default), a request's host is included in the cache key.
      Requests with different hosts are stored independently. **Important:**
      Enable this only if the hosts share the same origin and content.
      Removing the host from the cache key might inadvertently result in
      different objects being cached than intended, depending on which route
      the first user matched.
    excludeQueryString: Optional. If `true`, exclude query string parameters
      from the cache key. If `false` (the default), include the query string
      parameters in the cache key according to included_query_parameters and
      excluded_query_parameters. If neither is set, the entire query string is
      included.
    excludedQueryParameters: Optional. The names of query string parameters to
      exclude from cache keys. All other parameters are included. Specify
      either included_query_parameters or excluded_query_parameters, not both.
      `&` and `=` are percent encoded and not treated as delimiters. You can
      exclude up to 20 query parameters. Each query parameter name must be
      between 1 and 32 characters long (inclusive).
    includeProtocol: Optional. If `true`, HTTP and HTTPS requests are cached
      separately.
    includedCookieNames: Optional. The names of cookies to include in cache
      keys. The cookie name and cookie value of each cookie named is used as
      part of the cache key. The following limitations apply: - Must be valid
      RFC 6265 "cookie-name" tokens - Are case sensitive - Cannot start with
      "Edge-Cache-" (case insensitive) Specifying several cookies or cookies
      that have a large range of values, such as per-user, dramatically
      impacts the cache hit rate, and might result in a higher eviction rate
      and reduced performance. You can specify up to three cookie names.
    includedHeaderNames: Optional. The names of HTTP request headers to
      include in cache keys. The value of the header field is used as part of
      the cache key. The following limitations apply: - Header names must be
      valid HTTP RFC 7230 header field values. - Header field names are case
      insensitive - You can specify up to five header names. - To include the
      HTTP method, use `:method` Refer to the documentation for the allowed
      list of header names. Specifying several headers or headers that have a
      large range of values, such as per-user, dramatically impacts the cache
      hit rate, and might result in a higher eviction rate and reduced
      performance.
    includedQueryParameters: Optional. The names of query string parameters to
      include in cache keys. All other parameters are excluded. Specify either
      included_query_parameters or excluded_query_parameters, not both. `&`
      and `=` are percent encoded and not treated as delimiters. You can
      include up to 20 query parameters. Each query parameter name must be
      between 1 and 32 characters long (inclusive).
  """

  excludeHost = _messages.BooleanField(1)
  excludeQueryString = _messages.BooleanField(2)
  excludedQueryParameters = _messages.StringField(3, repeated=True)
  includeProtocol = _messages.BooleanField(4)
  includedCookieNames = _messages.StringField(5, repeated=True)
  includedHeaderNames = _messages.StringField(6, repeated=True)
  includedQueryParameters = _messages.StringField(7, repeated=True)


class CDNPolicySignedTokenOptions(_messages.Message):
  r"""The configuration options for signed tokens.

  Enums:
    AllowedSignatureAlgorithmsValueListEntryValuesEnum:

  Fields:
    allowedSignatureAlgorithms: Optional. The allowed signature algorithms to
      use. Defaults to using only ED25519. You can specify up to 3 signature
      algorithms to use.
    tokenQueryParameter: Optional. The query parameter in which to find the
      token. The name must be 1-64 characters long and match the regular
      expression `[a-zA-Z]([a-zA-Z0-9_-])*` which means the first character
      must be a letter, and all following characters must be a dash,
      underscore, letter or digit. Defaults to `edge-cache-token`.
  """

  class AllowedSignatureAlgorithmsValueListEntryValuesEnum(_messages.Enum):
    r"""AllowedSignatureAlgorithmsValueListEntryValuesEnum enum type.

    Values:
      SIGNATURE_ALGORITHM_UNSPECIFIED: It is an error to specify
        ALGORITHM_UNSPECIFIED.
      ED25519: Use an Ed25519 signature scheme. The signature must be
        specified in the signature field of the token.
      HMAC_SHA_256: Use an HMAC based on a SHA-256 hash. The HMAC must be
        specified in the hmac field of the token.
      HMAC_SHA1: Use an HMAC based on a SHA1 hash. The HMAC must be specified
        in the hmac field of the token.
    """
    SIGNATURE_ALGORITHM_UNSPECIFIED = 0
    ED25519 = 1
    HMAC_SHA_256 = 2
    HMAC_SHA1 = 3

  allowedSignatureAlgorithms = _messages.EnumField('AllowedSignatureAlgorithmsValueListEntryValuesEnum', 1, repeated=True)
  tokenQueryParameter = _messages.StringField(2)


class CORSPolicy(_messages.Message):
  r"""Defines Cross Origin Resource Sharing (CORS) configuration, including
  which CORS response headers are set.

  Fields:
    allowCredentials: Optional. In response to a preflight request, setting
      this to `true` indicates that the actual request can include user
      credentials. This translates to the `Access-Control-Allow-Credentials`
      response header.
    allowHeaders: Optional. The content for the `Access-Control-Allow-Headers`
      response header. You can specify up to 25 headers to include in the
      `Access-Control-Allow-Headers` header.
    allowMethods: Optional. The content for the `Access-Control-Allow-Methods`
      response header. You can specify up to five allowed methods.
    allowOrigins: Optional. A list of origins that are allowed to do CORS
      requests. This translates to the `Access-Control-Allow-Origin` response
      header. You can specify up to 100 allowed origins.
    disabled: Optional. If `true`, specifies that the CORS policy is disabled.
      The default value is `false`, which indicates that the CORS policy is in
      effect.
    exposeHeaders: Optional. The content for the Access-Control-Expose-Headers
      response header. You can specify up to 25 headers to expose in the
      `Access-Control-Expose-Headers` header.
    maxAge: Required. Specifies how long results of a preflight request can be
      cached by a client in seconds. Note that many browser clients enforce a
      maximum TTL of 600s (10 minutes). The following limitations apply: -
      Setting the value to `-1` forces a pre-flight check for all requests
      (not recommended) - A maximum TTL of `86400s` can be set; however, some
      clients might force pre-flight checks at a more regular interval. This
      translates to the `Access-Control-Max-Age` header.
  """

  allowCredentials = _messages.BooleanField(1)
  allowHeaders = _messages.StringField(2, repeated=True)
  allowMethods = _messages.StringField(3, repeated=True)
  allowOrigins = _messages.StringField(4, repeated=True)
  disabled = _messages.BooleanField(5)
  exposeHeaders = _messages.StringField(6, repeated=True)
  maxAge = _messages.StringField(7)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Connection(_messages.Message):
  r"""VPC connectivity information.

  Enums:
    ConnectionTypeValueValuesEnum: Required. The VPC connection type.

  Fields:
    connectionType: Required. The VPC connection type.
    nccHub: Optional. The resource name of the
      [NCC](https://cloud.google.com/network-connectivity-center) hub. Use the
      following format: `projects/{project}/locations/global/hubs/{hub}`.
  """

  class ConnectionTypeValueValuesEnum(_messages.Enum):
    r"""Required. The VPC connection type.

    Values:
      CONNECTION_TYPE_UNSPECIFIED: Unspecified connection type.
      NCC: Connected by [NCC](https://cloud.google.com/network-connectivity-
        center). Producers and consumers can be in separated VPCs connected by
        NCC, or the same VPC.
      SAME_VPC: Producer and consumers must be in the same VPC network.
    """
    CONNECTION_TYPE_UNSPECIFIED = 0
    NCC = 1
    SAME_VPC = 2

  connectionType = _messages.EnumField('ConnectionTypeValueValuesEnum', 1)
  nccHub = _messages.StringField(2)


class EdgeCacheKeyset(_messages.Message):
  r"""Represents a collection of public keys used for validating signed
  requests.

  Messages:
    LabelsValue: Optional. A set of label tags associated with the
      EdgeCacheKeyset resource.

  Fields:
    createTime: Output only. The creation timestamp in RFC3339 text format.
    description: Optional. A human-readable description of the resource.
    labels: Optional. A set of label tags associated with the EdgeCacheKeyset
      resource.
    name: Required. The name of the resource as provided by the client when
      the resource is created. The name must be 1-64 characters long, and
      match the regular expression `[a-zA-Z]([a-zA-Z0-9_-])*` which means the
      first character must be a letter, and all following characters must be a
      dash, an underscore, a letter, or a digit.
    publicKeys: Optional. An ordered list of Ed25519 public keys to use for
      validating signed requests. Ed25519 public keys are not secret and only
      allow Google to validate that a request was signed by your corresponding
      private key. Ensure that the private key is kept secret and that only
      authorized users can add public keys to a keyset. You can rotate keys by
      appending (pushing) a new key to the list of public keys, and removing
      any superseded keys. You must specify `public_keys` or
      validation_shared_keys (or both). The keys in `public_keys` are checked
      first. You can specify at most one Google-managed public key. If you
      specify `public_keys`, you must specify at least one key and can specify
      up to three keys.
    updateTime: Output only. The update timestamp in RFC3339 text format.
    validationSharedKeys: Optional. An ordered list of shared keys to use for
      validating signed requests. Shared keys are secret. Ensure that only
      authorized users can add `validation_shared_keys` to a keyset. You can
      rotate keys by appending (pushing) a new key to the list of
      `validation_shared_keys` and removing any superseded keys. You must
      specify public_keys or `validation_shared_keys` (or both). The keys in
      `public_keys` are checked first. If you specify
      `validation_shared_keys`, you must specify at least one key and can
      specify up to three keys.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. A set of label tags associated with the EdgeCacheKeyset
    resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  publicKeys = _messages.MessageField('PublicKey', 5, repeated=True)
  updateTime = _messages.StringField(6)
  validationSharedKeys = _messages.MessageField('Secret', 7, repeated=True)


class EdgeCacheOrigin(_messages.Message):
  r"""Represents an HTTP-reachable backend for an `EdgeCacheService` resource.

  Enums:
    ProtocolValueValuesEnum: Optional. The protocol to use to connect to the
      configured origin. Defaults to HTTP2, which is strongly recommended for
      both security and performance. When using HTTP2 or HTTPS as the
      protocol, a valid, publicly-signed, unexpired TLS (SSL) certificate must
      be presented by the origin server.
    RetryConditionsValueListEntryValuesEnum:

  Messages:
    LabelsValue: Optional. A set of label tags associated with the
      EdgeCacheOrigin resource.

  Fields:
    awsV4Authentication: Optional. Enable AWS Signature Version 4 origin
      authentication.
    createTime: Output only. The creation timestamp in RFC3339 text format.
    description: Optional. A human-readable description of the resource.
    failoverOrigin: Optional. The EdgeCacheOrigin resource to try when the
      current origin cannot be reached. After max_attempts is reached, the
      configured failover_origin is used to fulfil the request. The following
      are both valid paths to an `EdgeCacheOrigin` resource: * `projects/my-
      project/locations/global/edgeCacheOrigins/my-origin` * `my-origin` The
      value of max_attempts_timeout dictates the timeout across all origins.
    flexShielding: Optional. The FlexShieldingOptions to be used for all
      routes to this origin. If not set, defaults to a global caching layer in
      front of the origin.
    labels: Optional. A set of label tags associated with the EdgeCacheOrigin
      resource.
    maxAttempts: Optional. The maximum number of attempts to cache fill from
      this origin. Another attempt is made when a cache fill fails with one of
      the retry_conditions or following a redirect response matching one of
      the origin_redirect.redirect_conditions. Once the maximum attempts to
      this origin have failed, the failover origin][], if specified, is used.
      The failover origin can have its own `max_attempts`, `retry_conditions`,
      `redirect_conditions`, and `failover_origin` values to control its cache
      fill failures. The total number of allowed attempts to cache fill across
      this and failover origins is limited to four. The total time allowed for
      cache fill attempts across this and failover origins can be controlled
      with `max_attempts_timeout`. The last valid, non-retried response from
      all origins is returned to the client. If no origin returns a valid
      response, an `HTTP 502` error is returned to the client. Defaults to 1.
      Must be a value greater than 0 and less than 5.
    name: Required. The name of the resource as provided by the client when
      the resource is created. The name must be 1-64 characters long, and
      match the regular expression `[a-zA-Z]([a-zA-Z0-9_-])*`, which means
      that the first character must be a letter, and all following characters
      must be a dash, an underscore, a letter, or a digit.
    originAddress: Required. A fully qualified domain name (FQDN), an IPv4 or
      IPv6 address reachable over the public internet, or the address of a
      Google Cloud Storage bucket. This address is used as the origin for
      cache requests. The following are example origins: - **FQDN**: `media-
      backend.example.com` - **IPv4**: `**********` - **IPv6**:
      `2607:f8b0:4012:809::200e` - **Google Cloud Storage**: `gs://bucketname`
      or `bucketname.storage.googleapis.com` The following limitations apply
      to fully-qualified domain names: * They must be resolvable through
      public DNS. * They must not contain a protocol (such as `https://`). *
      They must not contain any slashes. When providing an IP address, it must
      be publicly routable. IPv6 addresses must not be enclosed in square
      brackets.
    originOverrideAction: Optional. The override actions, including URL
      rewrites and header additions, for requests that use this origin.
    originRedirect: Optional. Follow redirects from this origin.
    port: Optional. The port to connect to the origin on. Defaults to port
      **443** for HTTP2 and HTTPS protocols and port **80** for HTTP.
    protocol: Optional. The protocol to use to connect to the configured
      origin. Defaults to HTTP2, which is strongly recommended for both
      security and performance. When using HTTP2 or HTTPS as the protocol, a
      valid, publicly-signed, unexpired TLS (SSL) certificate must be
      presented by the origin server.
    retryConditions: Optional. Specifies one or more retry conditions for the
      configured origin. If the failure mode during a connection attempt to
      the origin matches the configured `retryConditions` values, the origin
      request retries up to max_attempts times. The failover origin, if
      configured, is then used to satisfy the request. The default
      `retry_conditions` value is `CONNECT_FAILURE`. `retry_conditions` values
      apply to this origin, and not to subsequent failover origins, which can
      specify their own `retry_conditions` and `max_attempts` values. For a
      list of valid values, see RetryConditions.
    timeout: Optional. The connection and HTTP timeout configuration for this
      origin.
    updateTime: Output only. The update timestamp in RFC3339 text format.
  """

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""Optional. The protocol to use to connect to the configured origin.
    Defaults to HTTP2, which is strongly recommended for both security and
    performance. When using HTTP2 or HTTPS as the protocol, a valid, publicly-
    signed, unexpired TLS (SSL) certificate must be presented by the origin
    server.

    Values:
      PROTOCOL_UNSPECIFIED: Unspecified value. Defaults to HTTP2.
      HTTP2: The HTTP/2 protocol. HTTP/2 refers to "h2", which requires TLS
        (HTTPS). Requires a valid (public and unexpired) TLS certificate
        present on the origin.
      HTTPS: HTTP/1.1 with TLS (SSL). Requires a valid (public and unexpired)
        TLS certificate present on the origin.
      HTTP: HTTP without TLS (SSL). This is not recommended, because
        communication outside of Google's network is unencrypted to the public
        endpoint (origin).
    """
    PROTOCOL_UNSPECIFIED = 0
    HTTP2 = 1
    HTTPS = 2
    HTTP = 3

  class RetryConditionsValueListEntryValuesEnum(_messages.Enum):
    r"""RetryConditionsValueListEntryValuesEnum enum type.

    Values:
      RETRY_CONDITIONS_UNSPECIFIED: Unspecified
      CONNECT_FAILURE: Retry on failures connecting to origins include
        routing, DNS and TLS handshake errors, and TCP/UDP timeouts.
      HTTP_5XX: Retry if the origin responds with any `HTTP 5xx` response
        code.
      GATEWAY_ERROR: Similar to `5xx`, but only applies to HTTP response codes
        `502`, `503`, or `504`.
      RETRIABLE_4XX: Retry for retriable `4xx` response codes, which include
        `HTTP 409 (Conflict)` and `HTTP 429 (Too Many Requests)`.
      NOT_FOUND: Retry if the origin returns an `HTTP 404 (Not Found)` error.
        This can be useful when generating video content when the segment is
        not yet available.
      FORBIDDEN: Retry if the origin returns an `HTTP 403 (Forbidden)` error.
        This can be useful for origins that return `403` (instead of `404`)
        for missing content for security reasons.
    """
    RETRY_CONDITIONS_UNSPECIFIED = 0
    CONNECT_FAILURE = 1
    HTTP_5XX = 2
    GATEWAY_ERROR = 3
    RETRIABLE_4XX = 4
    NOT_FOUND = 5
    FORBIDDEN = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. A set of label tags associated with the EdgeCacheOrigin
    resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  awsV4Authentication = _messages.MessageField('AWSV4Signature', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  failoverOrigin = _messages.StringField(4)
  flexShielding = _messages.MessageField('FlexShieldingOptions', 5)
  labels = _messages.MessageField('LabelsValue', 6)
  maxAttempts = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  name = _messages.StringField(8)
  originAddress = _messages.StringField(9)
  originOverrideAction = _messages.MessageField('OriginOverrideAction', 10)
  originRedirect = _messages.MessageField('OriginRedirect', 11)
  port = _messages.IntegerField(12, variant=_messages.Variant.INT32)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 13)
  retryConditions = _messages.EnumField('RetryConditionsValueListEntryValuesEnum', 14, repeated=True)
  timeout = _messages.MessageField('Timeout', 15)
  updateTime = _messages.StringField(16)


class EdgeCacheService(_messages.Message):
  r"""Defines the IP addresses, protocols, security policies, cache policies,
  and routing configuration.

  Messages:
    LabelsValue: Optional. A set of label tags associated with the
      `EdgeCacheService` resource.

  Fields:
    createTime: Output only. The creation timestamp in RFC3339 text format.
    description: Optional. A human-readable description of the resource.
    disableHttp2: Optional. Disables HTTP/2. HTTP/2 (h2) is enabled by default
      and recommended for performance. HTTP/2 improves connection re-use and
      reduces connection setup overhead by sending multiple streams over the
      same connection. Some legacy HTTP clients might have issues with HTTP/2
      connections due to broken HTTP/2 implementations. Setting this to `true`
      prevents HTTP/2 from being advertised and negotiated.
    disableQuic: Optional. HTTP/3 (IETF QUIC) and Google QUIC are enabled by
      default.
    edgeSecurityPolicy: Optional. The resource URL that points at the Cloud
      Armor edge security policy that is applied on each request against the
      EdgeCacheService resource.
    edgeSslCertificates: Optional. Certificate resources that are used to
      authenticate connections between users and the EdgeCacheService
      resource. Note that only global certificates with a scope of
      `EDGE_CACHE` can be attached to an EdgeCacheService resource. The
      following are both valid paths to an `edge_ssl_certificates` resource: *
      `projects/project/locations/global/certificates/media-example-com-cert`
      * `media-example-com-cert` You can specify up to five SSL certificates.
    ipv4Addresses: Output only. The IPv4 addresses associated with this
      service. Addresses are static for the lifetime of the service. IP
      addresses provisioned via Bring-Your-Own-IP (BYOIP) are not supported.
    ipv6Addresses: Output only. The IPv6 addresses associated with this
      service. Addresses are static for the lifetime of the service. IP
      addresses provisioned via Bring-Your-Own-IP (BYOIP) are not supported.
    labels: Optional. A set of label tags associated with the
      `EdgeCacheService` resource.
    logConfig: Optional. The logging options for the traffic served by this
      service. If logging is enabled, logs are exported to Cloud Logging.
    name: Required. The name of the resource as provided by the client when
      the resource is created. The name must be 1-64 characters long, and
      match the regular expression `[a-zA-Z]([a-zA-Z0-9_-])*` which means the
      first character must be a letter, and all following characters must be a
      dash, an underscore, a letter, or a digit.
    requireTls: Optional. Require TLS (HTTPS) for all clients connecting to
      this service. Clients who connect over HTTP (port 80) see an `HTTP 301`
      response to the same URL over HTTPS (port 443). You must have at least
      one edge_ssl_certificates specified to enable this.
    routing: Required. Defines how requests are routed, modified, and cached,
      and which origin the content is filled from.
    updateTime: Output only. The update timestamp in RFC3339 text format.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. A set of label tags associated with the `EdgeCacheService`
    resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  disableHttp2 = _messages.BooleanField(3)
  disableQuic = _messages.BooleanField(4)
  edgeSecurityPolicy = _messages.StringField(5)
  edgeSslCertificates = _messages.StringField(6, repeated=True)
  ipv4Addresses = _messages.StringField(7, repeated=True)
  ipv6Addresses = _messages.StringField(8, repeated=True)
  labels = _messages.MessageField('LabelsValue', 9)
  logConfig = _messages.MessageField('LogConfig', 10)
  name = _messages.StringField(11)
  requireTls = _messages.BooleanField(12)
  routing = _messages.MessageField('Routing', 13)
  updateTime = _messages.StringField(14)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EndpointMatcher(_messages.Message):
  r"""A definition of a matcher that selects endpoints to which the policies
  should be applied.

  Fields:
    metadataLabelMatcher: The matcher is based on node metadata presented by
      xDS clients.
  """

  metadataLabelMatcher = _messages.MessageField('EndpointMatcherMetadataLabelMatcher', 1)


class EndpointMatcherMetadataLabelMatcher(_messages.Message):
  r"""The matcher that is based on node metadata presented by xDS clients.

  Enums:
    MetadataLabelMatchCriteriaValueValuesEnum: Specifies how matching should
      be done. Supported values are: MATCH_ANY: At least one of the Labels
      specified in the matcher should match the metadata presented by xDS
      client. MATCH_ALL: The metadata presented by the xDS client should
      contain all of the labels specified here. The selection is determined
      based on the best match. For example, suppose there are three
      EndpointPolicy resources P1, P2 and P3 and if P1 has a the matcher as
      MATCH_ANY , P2 has MATCH_ALL , and P3 has MATCH_ALL . If a client with
      label connects, the config from P1 will be selected. If a client with
      label connects, the config from P2 will be selected. If a client with
      label connects, the config from P3 will be selected. If there is more
      than one best match, (for example, if a config P4 with selector exists
      and if a client with label connects), pick up the one with older
      creation time.

  Fields:
    metadataLabelMatchCriteria: Specifies how matching should be done.
      Supported values are: MATCH_ANY: At least one of the Labels specified in
      the matcher should match the metadata presented by xDS client.
      MATCH_ALL: The metadata presented by the xDS client should contain all
      of the labels specified here. The selection is determined based on the
      best match. For example, suppose there are three EndpointPolicy
      resources P1, P2 and P3 and if P1 has a the matcher as MATCH_ANY , P2
      has MATCH_ALL , and P3 has MATCH_ALL . If a client with label connects,
      the config from P1 will be selected. If a client with label connects,
      the config from P2 will be selected. If a client with label connects,
      the config from P3 will be selected. If there is more than one best
      match, (for example, if a config P4 with selector exists and if a client
      with label connects), pick up the one with older creation time.
    metadataLabels: The list of label value pairs that must match labels in
      the provided metadata based on filterMatchCriteria This list can have at
      most 64 entries. The list can be empty if the match criteria is
      MATCH_ANY, to specify a wildcard match (i.e this matches any client).
  """

  class MetadataLabelMatchCriteriaValueValuesEnum(_messages.Enum):
    r"""Specifies how matching should be done. Supported values are:
    MATCH_ANY: At least one of the Labels specified in the matcher should
    match the metadata presented by xDS client. MATCH_ALL: The metadata
    presented by the xDS client should contain all of the labels specified
    here. The selection is determined based on the best match. For example,
    suppose there are three EndpointPolicy resources P1, P2 and P3 and if P1
    has a the matcher as MATCH_ANY , P2 has MATCH_ALL , and P3 has MATCH_ALL .
    If a client with label connects, the config from P1 will be selected. If a
    client with label connects, the config from P2 will be selected. If a
    client with label connects, the config from P3 will be selected. If there
    is more than one best match, (for example, if a config P4 with selector
    exists and if a client with label connects), pick up the one with older
    creation time.

    Values:
      METADATA_LABEL_MATCH_CRITERIA_UNSPECIFIED: Default value. Should not be
        used.
      MATCH_ANY: At least one of the Labels specified in the matcher should
        match the metadata presented by xDS client.
      MATCH_ALL: The metadata presented by the xDS client should contain all
        of the labels specified here.
    """
    METADATA_LABEL_MATCH_CRITERIA_UNSPECIFIED = 0
    MATCH_ANY = 1
    MATCH_ALL = 2

  metadataLabelMatchCriteria = _messages.EnumField('MetadataLabelMatchCriteriaValueValuesEnum', 1)
  metadataLabels = _messages.MessageField('EndpointMatcherMetadataLabelMatcherMetadataLabels', 2, repeated=True)


class EndpointMatcherMetadataLabelMatcherMetadataLabels(_messages.Message):
  r"""Defines a name-pair value for a single label.

  Fields:
    labelName: Required. Label name presented as key in xDS Node Metadata.
    labelValue: Required. Label value presented as value corresponding to the
      above key, in xDS Node Metadata.
  """

  labelName = _messages.StringField(1)
  labelValue = _messages.StringField(2)


class EndpointPolicy(_messages.Message):
  r"""EndpointPolicy is a resource that helps apply desired configuration on
  the endpoints that match specific criteria. For example, this resource can
  be used to apply "authentication config" an all endpoints that serve on port
  8080.

  Enums:
    TypeValueValuesEnum: Required. The type of endpoint policy. This is
      primarily used to validate the configuration.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the
      EndpointPolicy resource.

  Fields:
    authorizationPolicy: Optional. This field specifies the URL of
      AuthorizationPolicy resource that applies authorization policies to the
      inbound traffic at the matched endpoints. Refer to Authorization. If
      this field is not specified, authorization is disabled(no authz checks)
      for this endpoint.
    clientTlsPolicy: Optional. A URL referring to a ClientTlsPolicy resource.
      ClientTlsPolicy can be set to specify the authentication for traffic
      from the proxy to the actual endpoints. More specifically, it is applied
      to the outgoing traffic from the proxy to the endpoint. This is
      typically used for sidecar model where the proxy identifies itself as
      endpoint to the control plane, with the connection between sidecar and
      endpoint requiring authentication. If this field is not set,
      authentication is disabled(open). Applicable only when
      EndpointPolicyType is SIDECAR_PROXY.
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    endpointMatcher: Required. A matcher that selects endpoints to which the
      policies should be applied.
    labels: Optional. Set of label tags associated with the EndpointPolicy
      resource.
    name: Identifier. Name of the EndpointPolicy resource. It matches pattern
      `projects/{project}/locations/global/endpointPolicies/{endpoint_policy}`
      .
    serverTlsPolicy: Optional. A URL referring to ServerTlsPolicy resource.
      ServerTlsPolicy is used to determine the authentication policy to be
      applied to terminate the inbound traffic at the identified backends. If
      this field is not set, authentication is disabled(open) for this
      endpoint.
    trafficPortSelector: Optional. Port selector for the (matched) endpoints.
      If no port selector is provided, the matched config is applied to all
      ports.
    type: Required. The type of endpoint policy. This is primarily used to
      validate the configuration.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of endpoint policy. This is primarily used to
    validate the configuration.

    Values:
      ENDPOINT_POLICY_TYPE_UNSPECIFIED: Default value. Must not be used.
      SIDECAR_PROXY: Represents a proxy deployed as a sidecar.
      GRPC_SERVER: Represents a proxyless gRPC backend.
    """
    ENDPOINT_POLICY_TYPE_UNSPECIFIED = 0
    SIDECAR_PROXY = 1
    GRPC_SERVER = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the EndpointPolicy
    resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  authorizationPolicy = _messages.StringField(1)
  clientTlsPolicy = _messages.StringField(2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  endpointMatcher = _messages.MessageField('EndpointMatcher', 5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  serverTlsPolicy = _messages.StringField(8)
  trafficPortSelector = _messages.MessageField('TrafficPortSelector', 9)
  type = _messages.EnumField('TypeValueValuesEnum', 10)
  updateTime = _messages.StringField(11)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ExtensionChain(_messages.Message):
  r"""A single extension chain wrapper that contains the match conditions and
  extensions to execute.

  Fields:
    extensions: Required. A set of extensions to execute for the matching
      request. At least one extension is required. Up to 3 extensions can be
      defined for each extension chain for `LbTrafficExtension` resource.
      `LbRouteExtension` and `LbEdgeExtension` chains are limited to 1
      extension per extension chain.
    matchCondition: Required. Conditions under which this chain is invoked for
      a request.
    name: Required. The name for this extension chain. The name is logged as
      part of the HTTP request logs. The name must conform with RFC-1034, is
      restricted to lower-cased letters, numbers and hyphens, and can have a
      maximum length of 63 characters. Additionally, the first character must
      be a letter and the last a letter or a number.
  """

  extensions = _messages.MessageField('ExtensionChainExtension', 1, repeated=True)
  matchCondition = _messages.MessageField('ExtensionChainMatchCondition', 2)
  name = _messages.StringField(3)


class ExtensionChainExtension(_messages.Message):
  r"""A single extension in the chain to execute for the matching request.

  Enums:
    SupportedEventsValueListEntryValuesEnum:

  Messages:
    MetadataValue: Optional. The metadata provided here is included as part of
      the `metadata_context` (of type `google.protobuf.Struct`) in the
      `ProcessingRequest` message sent to the extension server. The metadata
      is available under the namespace `com.google....`. For example:
      `com.google.lb_traffic_extension.lbtrafficextension1.chain1.ext1`. The
      following variables are supported in the metadata:
      `{forwarding_rule_id}` - substituted with the forwarding rule's fully
      qualified resource name. This field must not be set for plugin
      extensions. Setting it results in a validation error. You can set
      metadata at either the resource level or the extension level. The
      extension level metadata is recommended because you can pass a different
      set of metadata through each extension to the backend. This field is
      subject to following limitations: * The total size of the metadata must
      be less than 1KiB. * The total number of keys in the metadata must be
      less than 16. * The length of each key must be less than 64 characters.
      * The length of each value must be less than 1024 characters. * All
      values must be strings.

  Fields:
    authority: Optional. The `:authority` header in the gRPC request sent from
      Envoy to the extension service. Required for Callout extensions. This
      field is not supported for plugin extensions. Setting it results in a
      validation error.
    failOpen: Optional. Determines how the proxy behaves if the call to the
      extension fails or times out. When set to `TRUE`, request or response
      processing continues without error. Any subsequent extensions in the
      extension chain are also executed. When set to `FALSE` or the default
      setting of `FALSE` is used, one of the following happens: * If response
      headers have not been delivered to the downstream client, a generic 500
      error is returned to the client. The error response can be tailored by
      configuring a custom error response in the load balancer. * If response
      headers have been delivered, then the HTTP stream to the downstream
      client is reset.
    forwardHeaders: Optional. List of the HTTP headers to forward to the
      extension (from the client or backend). If omitted, all headers are
      sent. Each element is a string indicating the header name.
    metadata: Optional. The metadata provided here is included as part of the
      `metadata_context` (of type `google.protobuf.Struct`) in the
      `ProcessingRequest` message sent to the extension server. The metadata
      is available under the namespace `com.google....`. For example:
      `com.google.lb_traffic_extension.lbtrafficextension1.chain1.ext1`. The
      following variables are supported in the metadata:
      `{forwarding_rule_id}` - substituted with the forwarding rule's fully
      qualified resource name. This field must not be set for plugin
      extensions. Setting it results in a validation error. You can set
      metadata at either the resource level or the extension level. The
      extension level metadata is recommended because you can pass a different
      set of metadata through each extension to the backend. This field is
      subject to following limitations: * The total size of the metadata must
      be less than 1KiB. * The total number of keys in the metadata must be
      less than 16. * The length of each key must be less than 64 characters.
      * The length of each value must be less than 1024 characters. * All
      values must be strings.
    name: Required. The name for this extension. The name is logged as part of
      the HTTP request logs. The name must conform with RFC-1034, is
      restricted to lower-cased letters, numbers and hyphens, and can have a
      maximum length of 63 characters. Additionally, the first character must
      be a letter and the last a letter or a number.
    service: Required. The reference to the service that runs the extension.
      To configure a callout extension, `service` must be a fully-qualified
      reference to a [backend service](https://cloud.google.com/compute/docs/r
      eference/rest/v1/backendServices) in the format: `https://www.googleapis
      .com/compute/v1/projects/{project}/regions/{region}/backendServices/{bac
      kendService}` or `https://www.googleapis.com/compute/v1/projects/{projec
      t}/global/backendServices/{backendService}`. To configure a plugin
      extension, `service` must be a reference to a [`WasmPlugin`
      resource](https://cloud.google.com/service-
      extensions/docs/reference/rest/v1beta1/projects.locations.wasmPlugins)
      in the format:
      `projects/{project}/locations/{location}/wasmPlugins/{plugin}` or `//net
      workservices.googleapis.com/projects/{project}/locations/{location}/wasm
      Plugins/{wasmPlugin}`. Plugin extensions are currently supported for the
      `LbTrafficExtension`, the `LbRouteExtension`, and the `LbEdgeExtension`
      resources.
    supportedEvents: Optional. A set of events during request or response
      processing for which this extension is called. For the
      `LbTrafficExtension` resource, this field is required. For the
      `LbRouteExtension` resource, this field is optional. If unspecified,
      `REQUEST_HEADERS` event is assumed as supported. For the
      `LbEdgeExtension` resource, this field is required and must only contain
      `REQUEST_HEADERS` event.
    timeout: Optional. Specifies the timeout for each individual message on
      the stream. The timeout must be between `10`-`10000` milliseconds.
      Required for callout extensions. This field is not supported for plugin
      extensions. Setting it results in a validation error.
  """

  class SupportedEventsValueListEntryValuesEnum(_messages.Enum):
    r"""SupportedEventsValueListEntryValuesEnum enum type.

    Values:
      EVENT_TYPE_UNSPECIFIED: Unspecified value. Do not use.
      REQUEST_HEADERS: If included in `supported_events`, the extension is
        called when the HTTP request headers arrive.
      REQUEST_BODY: If included in `supported_events`, the extension is called
        when the HTTP request body arrives.
      RESPONSE_HEADERS: If included in `supported_events`, the extension is
        called when the HTTP response headers arrive.
      RESPONSE_BODY: If included in `supported_events`, the extension is
        called when the HTTP response body arrives.
      REQUEST_TRAILERS: If included in `supported_events`, the extension is
        called when the HTTP request trailers arrives.
      RESPONSE_TRAILERS: If included in `supported_events`, the extension is
        called when the HTTP response trailers arrives.
    """
    EVENT_TYPE_UNSPECIFIED = 0
    REQUEST_HEADERS = 1
    REQUEST_BODY = 2
    RESPONSE_HEADERS = 3
    RESPONSE_BODY = 4
    REQUEST_TRAILERS = 5
    RESPONSE_TRAILERS = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. The metadata provided here is included as part of the
    `metadata_context` (of type `google.protobuf.Struct`) in the
    `ProcessingRequest` message sent to the extension server. The metadata is
    available under the namespace `com.google....`. For example:
    `com.google.lb_traffic_extension.lbtrafficextension1.chain1.ext1`. The
    following variables are supported in the metadata: `{forwarding_rule_id}`
    - substituted with the forwarding rule's fully qualified resource name.
    This field must not be set for plugin extensions. Setting it results in a
    validation error. You can set metadata at either the resource level or the
    extension level. The extension level metadata is recommended because you
    can pass a different set of metadata through each extension to the
    backend. This field is subject to following limitations: * The total size
    of the metadata must be less than 1KiB. * The total number of keys in the
    metadata must be less than 16. * The length of each key must be less than
    64 characters. * The length of each value must be less than 1024
    characters. * All values must be strings.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  authority = _messages.StringField(1)
  failOpen = _messages.BooleanField(2)
  forwardHeaders = _messages.StringField(3, repeated=True)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)
  service = _messages.StringField(6)
  supportedEvents = _messages.EnumField('SupportedEventsValueListEntryValuesEnum', 7, repeated=True)
  timeout = _messages.StringField(8)


class ExtensionChainMatchCondition(_messages.Message):
  r"""Conditions under which this chain is invoked for a request.

  Fields:
    celExpression: Required. A Common Expression Language (CEL) expression
      that is used to match requests for which the extension chain is
      executed. For more information, see [CEL matcher language
      reference](https://cloud.google.com/service-extensions/docs/cel-matcher-
      language-reference).
  """

  celExpression = _messages.StringField(1)


class FlexShieldingOptions(_messages.Message):
  r"""Defines the caching layer to use in front of the origin.

  Enums:
    FlexShieldingRegionsValueListEntryValuesEnum:

  Fields:
    flexShieldingRegions: Optional. Whenever possible, content will be fetched
      from origin and cached in or near the specified region. Best effort. You
      must specify exactly one FlexShieldingRegion.
  """

  class FlexShieldingRegionsValueListEntryValuesEnum(_messages.Enum):
    r"""FlexShieldingRegionsValueListEntryValuesEnum enum type.

    Values:
      FLEX_SHIELDING_REGION_UNSPECIFIED: It is an error to specify
        FLEX_SHIELDING_REGION_UNSPECIFIED.
      AFRICA_SOUTH1: Origin fetch from near africa-south1.
      ME_CENTRAL1: Origin fetch from near me-central1.
    """
    FLEX_SHIELDING_REGION_UNSPECIFIED = 0
    AFRICA_SOUTH1 = 1
    ME_CENTRAL1 = 2

  flexShieldingRegions = _messages.EnumField('FlexShieldingRegionsValueListEntryValuesEnum', 1, repeated=True)


class Gateway(_messages.Message):
  r"""Gateway represents the configuration for a proxy, typically a load
  balancer. It captures the ip:port over which the services are exposed by the
  proxy, along with any policy configurations. Routes have reference to to
  Gateways to dictate how requests should be routed by this Gateway.

  Enums:
    EnvoyHeadersValueValuesEnum: Optional. Determines if envoy will insert
      internal debug headers into upstream requests. Other Envoy headers may
      still be injected. By default, envoy will not insert any debug headers.
    IpVersionValueValuesEnum: Optional. The IP Version that will be used by
      this gateway. Valid options are IPV4 or IPV6. Default is IPV4.
    RoutingModeValueValuesEnum: Optional. The routing mode of the Gateway.
      This field is configurable only for gateways of type SECURE_WEB_GATEWAY.
      This field is required for gateways of type SECURE_WEB_GATEWAY.
    TypeValueValuesEnum: Immutable. The type of the customer managed gateway.
      This field is required. If unspecified, an error is returned.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the Gateway
      resource.

  Fields:
    addresses: Optional. Zero or one IPv4 or IPv6 address on which the Gateway
      will receive the traffic. When no address is provided, an IP from the
      subnetwork is allocated This field only applies to gateways of type
      'SECURE_WEB_GATEWAY'. Gateways of type 'OPEN_MESH' listen on 0.0.0.0 for
      IPv4 and :: for IPv6.
    authorizationPolicy: Optional. A fully-qualified AuthorizationPolicy URL
      reference. Specifies how traffic is authorized. If empty, authorization
      checks are disabled.
    certificateUrls: Optional. A fully-qualified Certificates URL reference.
      The proxy presents a Certificate (selected based on SNI) when
      establishing a TLS connection. This feature only applies to gateways of
      type 'SECURE_WEB_GATEWAY'.
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    envoyHeaders: Optional. Determines if envoy will insert internal debug
      headers into upstream requests. Other Envoy headers may still be
      injected. By default, envoy will not insert any debug headers.
    gatewaySecurityPolicy: Optional. A fully-qualified GatewaySecurityPolicy
      URL reference. Defines how a server should apply security policy to
      inbound (VM to Proxy) initiated connections. For example:
      `projects/*/locations/*/gatewaySecurityPolicies/swg-policy`. This policy
      is specific to gateways of type 'SECURE_WEB_GATEWAY'.
    ipVersion: Optional. The IP Version that will be used by this gateway.
      Valid options are IPV4 or IPV6. Default is IPV4.
    labels: Optional. Set of label tags associated with the Gateway resource.
    name: Identifier. Name of the Gateway resource. It matches pattern
      `projects/*/locations/*/gateways/`.
    network: Optional. The relative resource name identifying the VPC network
      that is using this configuration. For example:
      `projects/*/global/networks/network-1`. Currently, this field is
      specific to gateways of type 'SECURE_WEB_GATEWAY'.
    ports: Required. One or more port numbers (1-65535), on which the Gateway
      will receive traffic. The proxy binds to the specified ports. Gateways
      of type 'SECURE_WEB_GATEWAY' are limited to 1 port. Gateways of type
      'OPEN_MESH' listen on 0.0.0.0 for IPv4 and :: for IPv6 and support
      multiple ports.
    routingMode: Optional. The routing mode of the Gateway. This field is
      configurable only for gateways of type SECURE_WEB_GATEWAY. This field is
      required for gateways of type SECURE_WEB_GATEWAY.
    scope: Optional. Scope determines how configuration across multiple
      Gateway instances are merged. The configuration for multiple Gateway
      instances with the same scope will be merged as presented as a single
      configuration to the proxy/load balancer. Max length 64 characters.
      Scope should start with a letter and can only have letters, numbers,
      hyphens.
    securityPolicy: Optional. A fully-qualified GatewaySecurityPolicy URL
      reference. Defines how a server should apply security policy to inbound
      (VM to Proxy) initiated connections. This policy is specific to gateways
      of type 'SECURE_WEB_GATEWAY'. DEPRECATED!!!! Use the
      gateway_security_policy field instead.
    selfLink: Output only. Server-defined URL of this resource
    serverTlsPolicy: Optional. A fully-qualified ServerTLSPolicy URL
      reference. Specifies how TLS traffic is terminated. If empty, TLS
      termination is disabled.
    subnetwork: Optional. The relative resource name identifying the
      subnetwork in which this SWG is allocated. For example:
      `projects/*/regions/us-central1/subnetworks/network-1` Currently, this
      field is specific to gateways of type 'SECURE_WEB_GATEWAY".
    type: Immutable. The type of the customer managed gateway. This field is
      required. If unspecified, an error is returned.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  class EnvoyHeadersValueValuesEnum(_messages.Enum):
    r"""Optional. Determines if envoy will insert internal debug headers into
    upstream requests. Other Envoy headers may still be injected. By default,
    envoy will not insert any debug headers.

    Values:
      ENVOY_HEADERS_UNSPECIFIED: Defaults to NONE.
      NONE: Suppress envoy debug headers.
      DEBUG_HEADERS: Envoy will insert default internal debug headers into
        upstream requests: x-envoy-attempt-count x-envoy-is-timeout-retry
        x-envoy-expected-rq-timeout-ms x-envoy-original-path x-envoy-upstream-
        stream-duration-ms
    """
    ENVOY_HEADERS_UNSPECIFIED = 0
    NONE = 1
    DEBUG_HEADERS = 2

  class IpVersionValueValuesEnum(_messages.Enum):
    r"""Optional. The IP Version that will be used by this gateway. Valid
    options are IPV4 or IPV6. Default is IPV4.

    Values:
      IP_VERSION_UNSPECIFIED: The type when IP version is not specified.
        Defaults to IPV4.
      IPV4: The type for IP version 4.
      IPV6: The type for IP version 6.
    """
    IP_VERSION_UNSPECIFIED = 0
    IPV4 = 1
    IPV6 = 2

  class RoutingModeValueValuesEnum(_messages.Enum):
    r"""Optional. The routing mode of the Gateway. This field is configurable
    only for gateways of type SECURE_WEB_GATEWAY. This field is required for
    gateways of type SECURE_WEB_GATEWAY.

    Values:
      EXPLICIT_ROUTING_MODE: The routing mode is explicit; clients are
        configured to send traffic through the gateway. This is the default
        routing mode.
      NEXT_HOP_ROUTING_MODE: The routing mode is next-hop. Clients are unaware
        of the gateway, and a route (advanced route or other route type) can
        be configured to direct traffic from client to gateway. The gateway
        then acts as a next-hop to the destination.
    """
    EXPLICIT_ROUTING_MODE = 0
    NEXT_HOP_ROUTING_MODE = 1

  class TypeValueValuesEnum(_messages.Enum):
    r"""Immutable. The type of the customer managed gateway. This field is
    required. If unspecified, an error is returned.

    Values:
      TYPE_UNSPECIFIED: The type of the customer managed gateway is
        unspecified.
      OPEN_MESH: The type of the customer managed gateway is TrafficDirector
        Open Mesh.
      SECURE_WEB_GATEWAY: The type of the customer managed gateway is
        SecureWebGateway (SWG).
    """
    TYPE_UNSPECIFIED = 0
    OPEN_MESH = 1
    SECURE_WEB_GATEWAY = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the Gateway resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  addresses = _messages.StringField(1, repeated=True)
  authorizationPolicy = _messages.StringField(2)
  certificateUrls = _messages.StringField(3, repeated=True)
  createTime = _messages.StringField(4)
  description = _messages.StringField(5)
  envoyHeaders = _messages.EnumField('EnvoyHeadersValueValuesEnum', 6)
  gatewaySecurityPolicy = _messages.StringField(7)
  ipVersion = _messages.EnumField('IpVersionValueValuesEnum', 8)
  labels = _messages.MessageField('LabelsValue', 9)
  name = _messages.StringField(10)
  network = _messages.StringField(11)
  ports = _messages.IntegerField(12, repeated=True, variant=_messages.Variant.INT32)
  routingMode = _messages.EnumField('RoutingModeValueValuesEnum', 13)
  scope = _messages.StringField(14)
  securityPolicy = _messages.StringField(15)
  selfLink = _messages.StringField(16)
  serverTlsPolicy = _messages.StringField(17)
  subnetwork = _messages.StringField(18)
  type = _messages.EnumField('TypeValueValuesEnum', 19)
  updateTime = _messages.StringField(20)


class GatewayRouteView(_messages.Message):
  r"""GatewayRouteView defines view-only resource for Routes to a Gateway

  Fields:
    name: Output only. Identifier. Full path name of the GatewayRouteView
      resource. Format: projects/{project_number}/locations/{location}/gateway
      s/{gateway}/routeViews/{route_view}
    routeId: Output only. The resource id for the route.
    routeLocation: Output only. Location where the route exists.
    routeProjectNumber: Output only. Project number where the route exists.
    routeType: Output only. Type of the route: HttpRoute,GrpcRoute,TcpRoute,
      or TlsRoute
  """

  name = _messages.StringField(1)
  routeId = _messages.StringField(2)
  routeLocation = _messages.StringField(3)
  routeProjectNumber = _messages.IntegerField(4)
  routeType = _messages.StringField(5)


class GrpcRoute(_messages.Message):
  r"""GrpcRoute is the resource defining how gRPC traffic routed by a Mesh or
  Gateway resource is routed.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the GrpcRoute
      resource.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    gateways: Optional. Gateways defines a list of gateways this GrpcRoute is
      attached to, as one of the routing rules to route the requests served by
      the gateway. Each gateway reference should match the pattern:
      `projects/*/locations/global/gateways/`
    hostnames: Required. Service hostnames with an optional port for which
      this route describes traffic. Format: [:] Hostname is the fully
      qualified domain name of a network host. This matches the RFC 1123
      definition of a hostname with 2 notable exceptions: - IPs are not
      allowed. - A hostname may be prefixed with a wildcard label (`*.`). The
      wildcard label must appear by itself as the first label. Hostname can be
      "precise" which is a domain name without the terminating dot of a
      network host (e.g. `foo.example.com`) or "wildcard", which is a domain
      name prefixed with a single wildcard label (e.g. `*.example.com`). Note
      that as per RFC1035 and RFC1123, a label must consist of lower case
      alphanumeric characters or '-', and must start and end with an
      alphanumeric character. No other punctuation is allowed. The routes
      associated with a Mesh or Gateway must have unique hostnames. If you
      attempt to attach multiple routes with conflicting hostnames, the
      configuration will be rejected. For example, while it is acceptable for
      routes for the hostnames `*.foo.bar.com` and `*.bar.com` to be
      associated with the same route, it is not possible to associate two
      routes both with `*.bar.com` or both with `bar.com`. If a port is
      specified, then gRPC clients must use the channel URI with the port to
      match this rule (i.e. "xds:///service:123"), otherwise they must supply
      the URI without a port (i.e. "xds:///service").
    labels: Optional. Set of label tags associated with the GrpcRoute
      resource.
    meshes: Optional. Meshes defines a list of meshes this GrpcRoute is
      attached to, as one of the routing rules to route the requests served by
      the mesh. Each mesh reference should match the pattern:
      `projects/*/locations/global/meshes/`
    name: Identifier. Name of the GrpcRoute resource. It matches pattern
      `projects/*/locations/global/grpcRoutes/`
    rules: Required. A list of detailed rules defining how to route traffic.
      Within a single GrpcRoute, the GrpcRoute.RouteAction associated with the
      first matching GrpcRoute.RouteRule will be executed. At least one rule
      must be supplied.
    selfLink: Output only. Server-defined URL of this resource
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the GrpcRoute resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  gateways = _messages.StringField(3, repeated=True)
  hostnames = _messages.StringField(4, repeated=True)
  labels = _messages.MessageField('LabelsValue', 5)
  meshes = _messages.StringField(6, repeated=True)
  name = _messages.StringField(7)
  rules = _messages.MessageField('GrpcRouteRouteRule', 8, repeated=True)
  selfLink = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class GrpcRouteDestination(_messages.Message):
  r"""The destination to which traffic will be routed.

  Fields:
    serviceName: Required. The URL of a destination service to which to route
      traffic. Must refer to either a BackendService or
      ServiceDirectoryService.
    weight: Optional. Specifies the proportion of requests forwarded to the
      backend referenced by the serviceName field. This is computed as: -
      weight/Sum(weights in this destination list). For non-zero values, there
      may be some epsilon from the exact proportion defined here depending on
      the precision an implementation supports. If only one serviceName is
      specified and it has a weight greater than 0, 100% of the traffic is
      forwarded to that backend. If weights are specified for any one service
      name, they need to be specified for all of them. If weights are
      unspecified for all services, then, traffic is distributed in equal
      proportions to all of them.
  """

  serviceName = _messages.StringField(1)
  weight = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GrpcRouteFaultInjectionPolicy(_messages.Message):
  r"""The specification for fault injection introduced into traffic to test
  the resiliency of clients to destination service failure. As part of fault
  injection, when clients send requests to a destination, delays can be
  introduced on a percentage of requests before sending those requests to the
  destination service. Similarly requests from clients can be aborted by for a
  percentage of requests.

  Fields:
    abort: The specification for aborting to client requests.
    delay: The specification for injecting delay to client requests.
  """

  abort = _messages.MessageField('GrpcRouteFaultInjectionPolicyAbort', 1)
  delay = _messages.MessageField('GrpcRouteFaultInjectionPolicyDelay', 2)


class GrpcRouteFaultInjectionPolicyAbort(_messages.Message):
  r"""Specification of how client requests are aborted as part of fault
  injection before being sent to a destination.

  Fields:
    httpStatus: The HTTP status code used to abort the request. The value must
      be between 200 and 599 inclusive.
    percentage: The percentage of traffic which will be aborted. The value
      must be between [0, 100]
  """

  httpStatus = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  percentage = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GrpcRouteFaultInjectionPolicyDelay(_messages.Message):
  r"""Specification of how client requests are delayed as part of fault
  injection before being sent to a destination.

  Fields:
    fixedDelay: Specify a fixed delay before forwarding the request.
    percentage: The percentage of traffic on which delay will be injected. The
      value must be between [0, 100]
  """

  fixedDelay = _messages.StringField(1)
  percentage = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GrpcRouteHeaderMatch(_messages.Message):
  r"""A match against a collection of headers.

  Enums:
    TypeValueValuesEnum: Optional. Specifies how to match against the value of
      the header. If not specified, a default value of EXACT is used.

  Fields:
    key: Required. The key of the header.
    type: Optional. Specifies how to match against the value of the header. If
      not specified, a default value of EXACT is used.
    value: Required. The value of the header.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies how to match against the value of the header. If
    not specified, a default value of EXACT is used.

    Values:
      TYPE_UNSPECIFIED: Unspecified.
      EXACT: Will only match the exact value provided.
      REGULAR_EXPRESSION: Will match paths conforming to the prefix specified
        by value. RE2 syntax is supported.
    """
    TYPE_UNSPECIFIED = 0
    EXACT = 1
    REGULAR_EXPRESSION = 2

  key = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)
  value = _messages.StringField(3)


class GrpcRouteMethodMatch(_messages.Message):
  r"""Specifies a match against a method.

  Enums:
    TypeValueValuesEnum: Optional. Specifies how to match against the name. If
      not specified, a default value of "EXACT" is used.

  Fields:
    caseSensitive: Optional. Specifies that matches are case sensitive. The
      default value is true. case_sensitive must not be used with a type of
      REGULAR_EXPRESSION.
    grpcMethod: Required. Name of the method to match against. If unspecified,
      will match all methods.
    grpcService: Required. Name of the service to match against. If
      unspecified, will match all services.
    type: Optional. Specifies how to match against the name. If not specified,
      a default value of "EXACT" is used.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies how to match against the name. If not specified, a
    default value of "EXACT" is used.

    Values:
      TYPE_UNSPECIFIED: Unspecified.
      EXACT: Will only match the exact name provided.
      REGULAR_EXPRESSION: Will interpret grpc_method and grpc_service as
        regexes. RE2 syntax is supported.
    """
    TYPE_UNSPECIFIED = 0
    EXACT = 1
    REGULAR_EXPRESSION = 2

  caseSensitive = _messages.BooleanField(1)
  grpcMethod = _messages.StringField(2)
  grpcService = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class GrpcRouteRetryPolicy(_messages.Message):
  r"""The specifications for retries. Specifies one or more conditions for
  which this retry rule applies. Valid values are:

  Fields:
    numRetries: Specifies the allowed number of retries. This number must be >
      0. If not specified, default to 1.
    retryConditions: - connect-failure: Router will retry on failures
      connecting to Backend Services, for example due to connection timeouts.
      - refused-stream: Router will retry if the backend service resets the
      stream with a REFUSED_STREAM error code. This reset type indicates that
      it is safe to retry. - cancelled: Router will retry if the gRPC status
      code in the response header is set to cancelled - deadline-exceeded:
      Router will retry if the gRPC status code in the response header is set
      to deadline-exceeded - resource-exhausted: Router will retry if the gRPC
      status code in the response header is set to resource-exhausted -
      unavailable: Router will retry if the gRPC status code in the response
      header is set to unavailable
  """

  numRetries = _messages.IntegerField(1, variant=_messages.Variant.UINT32)
  retryConditions = _messages.StringField(2, repeated=True)


class GrpcRouteRouteAction(_messages.Message):
  r"""Specifies how to route matched traffic.

  Fields:
    destinations: Optional. The destination services to which traffic should
      be forwarded. If multiple destinations are specified, traffic will be
      split between Backend Service(s) according to the weight field of these
      destinations.
    faultInjectionPolicy: Optional. The specification for fault injection
      introduced into traffic to test the resiliency of clients to destination
      service failure. As part of fault injection, when clients send requests
      to a destination, delays can be introduced on a percentage of requests
      before sending those requests to the destination service. Similarly
      requests from clients can be aborted by for a percentage of requests.
      timeout and retry_policy will be ignored by clients that are configured
      with a fault_injection_policy
    idleTimeout: Optional. Specifies the idle timeout for the selected route.
      The idle timeout is defined as the period in which there are no bytes
      sent or received on either the upstream or downstream connection. If not
      set, the default idle timeout is 1 hour. If set to 0s, the timeout will
      be disabled.
    retryPolicy: Optional. Specifies the retry policy associated with this
      route.
    statefulSessionAffinity: Optional. Specifies cookie-based stateful session
      affinity.
    timeout: Optional. Specifies the timeout for selected route. Timeout is
      computed from the time the request has been fully processed (i.e. end of
      stream) up until the response has been completely processed. Timeout
      includes all retries.
  """

  destinations = _messages.MessageField('GrpcRouteDestination', 1, repeated=True)
  faultInjectionPolicy = _messages.MessageField('GrpcRouteFaultInjectionPolicy', 2)
  idleTimeout = _messages.StringField(3)
  retryPolicy = _messages.MessageField('GrpcRouteRetryPolicy', 4)
  statefulSessionAffinity = _messages.MessageField('GrpcRouteStatefulSessionAffinityPolicy', 5)
  timeout = _messages.StringField(6)


class GrpcRouteRouteMatch(_messages.Message):
  r"""Criteria for matching traffic. A RouteMatch will be considered to match
  when all supplied fields match.

  Fields:
    headers: Optional. Specifies a collection of headers to match.
    method: Optional. A gRPC method to match against. If this field is empty
      or omitted, will match all methods.
  """

  headers = _messages.MessageField('GrpcRouteHeaderMatch', 1, repeated=True)
  method = _messages.MessageField('GrpcRouteMethodMatch', 2)


class GrpcRouteRouteRule(_messages.Message):
  r"""Describes how to route traffic.

  Fields:
    action: Required. A detailed rule defining how to route traffic. This
      field is required.
    matches: Optional. Matches define conditions used for matching the rule
      against incoming gRPC requests. Each match is independent, i.e. this
      rule will be matched if ANY one of the matches is satisfied. If no
      matches field is specified, this rule will unconditionally match
      traffic.
  """

  action = _messages.MessageField('GrpcRouteRouteAction', 1)
  matches = _messages.MessageField('GrpcRouteRouteMatch', 2, repeated=True)


class GrpcRouteStatefulSessionAffinityPolicy(_messages.Message):
  r"""The specification for cookie-based stateful session affinity where the
  date plane supplies a "session cookie" with the name "GSSA" which encodes a
  specific destination host and each request containing that cookie will be
  directed to that host as long as the destination host remains up and
  healthy. The gRPC proxyless mesh library or sidecar proxy will manage the
  session cookie but the client application code is responsible for copying
  the cookie from each RPC in the session to the next.

  Fields:
    cookieTtl: Required. The cookie TTL value for the Set-Cookie header
      generated by the data plane. The lifetime of the cookie may be set to a
      value from 0 to 86400 seconds (24 hours) inclusive. Set this to 0s to
      use a session cookie and disable cookie expiration.
  """

  cookieTtl = _messages.StringField(1)


class HeaderAction(_messages.Message):
  r"""Defines the addition and removal of HTTP headers for requests and
  responses.

  Fields:
    requestHeadersToAdd: Optional. A list of headers to add to the request
      prior to forwarding the request to the origin. You can add a maximum of
      25 request headers.
    requestHeadersToRemove: Optional. A list of header names to remove from
      the request before forwarding the request to the origin. You can specify
      up to 25 request headers to remove.
    responseHeadersToAdd: Optional. A list of headers to add to the response
      before sending it back to the client. You can add a maximum of 25
      response headers. Response headers are only sent to the client, and do
      not have an effect on the cache serving the response.
    responseHeadersToRemove: Optional. A list of headers to remove from the
      response before sending it back to the client. Response headers are only
      sent to the client, and do not have an effect on the cache serving the
      response. You can specify up to 25 response headers to remove.
  """

  requestHeadersToAdd = _messages.MessageField('HeaderActionAddHeader', 1, repeated=True)
  requestHeadersToRemove = _messages.MessageField('HeaderActionRemoveHeader', 2, repeated=True)
  responseHeadersToAdd = _messages.MessageField('HeaderActionAddHeader', 3, repeated=True)
  responseHeadersToRemove = _messages.MessageField('HeaderActionRemoveHeader', 4, repeated=True)


class HeaderActionAddHeader(_messages.Message):
  r"""The header to add.

  Fields:
    headerName: Required. The name of the header to add.
    headerValue: Required. The value of the header to add.
    replace: Optional. Specifies whether to replace all existing headers with
      the same name.
  """

  headerName = _messages.StringField(1)
  headerValue = _messages.StringField(2)
  replace = _messages.BooleanField(3)


class HeaderActionRemoveHeader(_messages.Message):
  r"""The header to remove.

  Fields:
    headerName: Required. The name of the header to remove.
  """

  headerName = _messages.StringField(1)


class HeaderMatch(_messages.Message):
  r"""The match conditions for HTTP request headers.

  Fields:
    exactMatch: Optional. The value of the header must exactly match contents
      of `exact_match`. Only one of `exact_match`, prefix_match, suffix_match,
      or present_match must be set.
    headerName: Required. The header name to match on. The `:method` pseudo-
      header may be used to match on the request HTTP method.
    invertMatch: Optional. If set to `false`, HeaderMatch is considered a
      match when the match criteria above are met. If set to `true`,
      `HeaderMatch` is considered a match when the match criteria above are
      not met. The default is `false`.
    prefixMatch: Optional. The value of the header must start with the
      contents of `prefix_match`. Only one of exact_match, `prefix_match`,
      suffix_match, or present_match must be set.
    presentMatch: Optional. A header with the contents of header_name must
      exist. The match takes place whether or not the request's header has a
      value. Only one of exact_match, prefix_match, suffix_match, or
      `present_match` must be set.
    suffixMatch: Optional. The value of the header must end with the contents
      of `suffix_match`. Only one of exact_match, prefix_match,
      `suffix_match`, or present_match must be set.
  """

  exactMatch = _messages.StringField(1)
  headerName = _messages.StringField(2)
  invertMatch = _messages.BooleanField(3)
  prefixMatch = _messages.StringField(4)
  presentMatch = _messages.BooleanField(5)
  suffixMatch = _messages.StringField(6)


class HostRule(_messages.Message):
  r"""The hostname configured for the EdgeCacheService. A `HostRule` value
  associates a hostname (or hostnames) with a set of routing rules, which
  define configuration based on the path and header.

  Fields:
    description: Optional. A human-readable description of the `HostRule`
      value.
    hosts: Required. A list of host patterns to match. Host patterns must be
      valid hostnames. Ports are not allowed. Wildcard hosts are supported in
      the suffix or prefix form. `*` matches any string of `([a-z0-9-.]*)`. It
      does not match an empty string. When multiple hosts are specified, hosts
      are matched in the following priority: 1. Exact domain names:
      `www.foo.com`. 2. Suffix domain wildcards: `*.foo.com` or
      `*-bar.foo.com`. 3. Prefix domain wildcards: `foo.*` or `foo-*`. 4.
      Special wildcard `*` matching any domain. The wildcard doesn't match the
      empty string. For example, `*-bar.foo.com` matches `baz-bar.foo.com` but
      not `-bar.foo.com`. The longest wildcards match first. Only a single
      host in the entire service can match on ``*``. A domain must be unique
      across all configured hosts within a service. Hosts are matched against
      the HTTP `Host` header, or for HTTP/2 and HTTP/3, the `:authority`
      header, in the incoming request. You can specify up to 10 hosts.
    pathMatcher: Required. The name of the PathMatcher associated with this
      `HostRule`.
  """

  description = _messages.StringField(1)
  hosts = _messages.StringField(2, repeated=True)
  pathMatcher = _messages.StringField(3)


class HttpRoute(_messages.Message):
  r"""HttpRoute is the resource defining how HTTP traffic should be routed by
  a Mesh or Gateway resource.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the HttpRoute
      resource.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    gateways: Optional. Gateways defines a list of gateways this HttpRoute is
      attached to, as one of the routing rules to route the requests served by
      the gateway. Each gateway reference should match the pattern:
      `projects/*/locations/global/gateways/`
    hostnames: Required. Hostnames define a set of hosts that should match
      against the HTTP host header to select a HttpRoute to process the
      request. Hostname is the fully qualified domain name of a network host,
      as defined by RFC 1123 with the exception that: - IPs are not allowed. -
      A hostname may be prefixed with a wildcard label (`*.`). The wildcard
      label must appear by itself as the first label. Hostname can be
      "precise" which is a domain name without the terminating dot of a
      network host (e.g. `foo.example.com`) or "wildcard", which is a domain
      name prefixed with a single wildcard label (e.g. `*.example.com`). Note
      that as per RFC1035 and RFC1123, a label must consist of lower case
      alphanumeric characters or '-', and must start and end with an
      alphanumeric character. No other punctuation is allowed. The routes
      associated with a Mesh or Gateways must have unique hostnames. If you
      attempt to attach multiple routes with conflicting hostnames, the
      configuration will be rejected. For example, while it is acceptable for
      routes for the hostnames `*.foo.bar.com` and `*.bar.com` to be
      associated with the same Mesh (or Gateways under the same scope), it is
      not possible to associate two routes both with `*.bar.com` or both with
      `bar.com`.
    labels: Optional. Set of label tags associated with the HttpRoute
      resource.
    meshes: Optional. Meshes defines a list of meshes this HttpRoute is
      attached to, as one of the routing rules to route the requests served by
      the mesh. Each mesh reference should match the pattern:
      `projects/*/locations/global/meshes/` The attached Mesh should be of a
      type SIDECAR
    name: Identifier. Name of the HttpRoute resource. It matches pattern
      `projects/*/locations/global/httpRoutes/http_route_name>`.
    rules: Required. Rules that define how traffic is routed and handled.
      Rules will be matched sequentially based on the RouteMatch specified for
      the rule.
    selfLink: Output only. Server-defined URL of this resource
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the HttpRoute resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  gateways = _messages.StringField(3, repeated=True)
  hostnames = _messages.StringField(4, repeated=True)
  labels = _messages.MessageField('LabelsValue', 5)
  meshes = _messages.StringField(6, repeated=True)
  name = _messages.StringField(7)
  rules = _messages.MessageField('HttpRouteRouteRule', 8, repeated=True)
  selfLink = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class HttpRouteCorsPolicy(_messages.Message):
  r"""The Specification for allowing client side cross-origin requests.

  Fields:
    allowCredentials: In response to a preflight request, setting this to true
      indicates that the actual request can include user credentials. This
      translates to the Access-Control-Allow-Credentials header. Default value
      is false.
    allowHeaders: Specifies the content for Access-Control-Allow-Headers
      header.
    allowMethods: Specifies the content for Access-Control-Allow-Methods
      header.
    allowOriginRegexes: Specifies the regular expression patterns that match
      allowed origins. For regular expression grammar, please see
      https://github.com/google/re2/wiki/Syntax.
    allowOrigins: Specifies the list of origins that will be allowed to do
      CORS requests. An origin is allowed if it matches either an item in
      allow_origins or an item in allow_origin_regexes.
    disabled: If true, the CORS policy is disabled. The default value is
      false, which indicates that the CORS policy is in effect.
    exposeHeaders: Specifies the content for Access-Control-Expose-Headers
      header.
    maxAge: Specifies how long result of a preflight request can be cached in
      seconds. This translates to the Access-Control-Max-Age header.
  """

  allowCredentials = _messages.BooleanField(1)
  allowHeaders = _messages.StringField(2, repeated=True)
  allowMethods = _messages.StringField(3, repeated=True)
  allowOriginRegexes = _messages.StringField(4, repeated=True)
  allowOrigins = _messages.StringField(5, repeated=True)
  disabled = _messages.BooleanField(6)
  exposeHeaders = _messages.StringField(7, repeated=True)
  maxAge = _messages.StringField(8)


class HttpRouteDestination(_messages.Message):
  r"""Specifications of a destination to which the request should be routed
  to.

  Fields:
    requestHeaderModifier: Optional. The specification for modifying the
      headers of a matching request prior to delivery of the request to the
      destination. If HeaderModifiers are set on both the Destination and the
      RouteAction, they will be merged. Conflicts between the two will not be
      resolved on the configuration.
    responseHeaderModifier: Optional. The specification for modifying the
      headers of a response prior to sending the response back to the client.
      If HeaderModifiers are set on both the Destination and the RouteAction,
      they will be merged. Conflicts between the two will not be resolved on
      the configuration.
    serviceName: The URL of a BackendService to route traffic to.
    weight: Specifies the proportion of requests forwarded to the backend
      referenced by the serviceName field. This is computed as: -
      weight/Sum(weights in this destination list). For non-zero values, there
      may be some epsilon from the exact proportion defined here depending on
      the precision an implementation supports. If only one serviceName is
      specified and it has a weight greater than 0, 100% of the traffic is
      forwarded to that backend. If weights are specified for any one service
      name, they need to be specified for all of them. If weights are
      unspecified for all services, then, traffic is distributed in equal
      proportions to all of them.
  """

  requestHeaderModifier = _messages.MessageField('HttpRouteHeaderModifier', 1)
  responseHeaderModifier = _messages.MessageField('HttpRouteHeaderModifier', 2)
  serviceName = _messages.StringField(3)
  weight = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class HttpRouteFaultInjectionPolicy(_messages.Message):
  r"""The specification for fault injection introduced into traffic to test
  the resiliency of clients to destination service failure. As part of fault
  injection, when clients send requests to a destination, delays can be
  introduced by client proxy on a percentage of requests before sending those
  requests to the destination service. Similarly requests can be aborted by
  client proxy for a percentage of requests.

  Fields:
    abort: The specification for aborting to client requests.
    delay: The specification for injecting delay to client requests.
  """

  abort = _messages.MessageField('HttpRouteFaultInjectionPolicyAbort', 1)
  delay = _messages.MessageField('HttpRouteFaultInjectionPolicyDelay', 2)


class HttpRouteFaultInjectionPolicyAbort(_messages.Message):
  r"""Specification of how client requests are aborted as part of fault
  injection before being sent to a destination.

  Fields:
    httpStatus: The HTTP status code used to abort the request. The value must
      be between 200 and 599 inclusive.
    percentage: The percentage of traffic which will be aborted. The value
      must be between [0, 100]
  """

  httpStatus = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  percentage = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class HttpRouteFaultInjectionPolicyDelay(_messages.Message):
  r"""Specification of how client requests are delayed as part of fault
  injection before being sent to a destination.

  Fields:
    fixedDelay: Specify a fixed delay before forwarding the request.
    percentage: The percentage of traffic on which delay will be injected. The
      value must be between [0, 100]
  """

  fixedDelay = _messages.StringField(1)
  percentage = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class HttpRouteHeaderMatch(_messages.Message):
  r"""Specifies how to select a route rule based on HTTP request headers.

  Fields:
    exactMatch: The value of the header should match exactly the content of
      exact_match.
    header: The name of the HTTP header to match against.
    invertMatch: If specified, the match result will be inverted before
      checking. Default value is set to false.
    prefixMatch: The value of the header must start with the contents of
      prefix_match.
    presentMatch: A header with header_name must exist. The match takes place
      whether or not the header has a value.
    rangeMatch: If specified, the rule will match if the request header value
      is within the range.
    regexMatch: The value of the header must match the regular expression
      specified in regex_match. For regular expression grammar, please see:
      https://github.com/google/re2/wiki/Syntax
    suffixMatch: The value of the header must end with the contents of
      suffix_match.
  """

  exactMatch = _messages.StringField(1)
  header = _messages.StringField(2)
  invertMatch = _messages.BooleanField(3)
  prefixMatch = _messages.StringField(4)
  presentMatch = _messages.BooleanField(5)
  rangeMatch = _messages.MessageField('HttpRouteHeaderMatchIntegerRange', 6)
  regexMatch = _messages.StringField(7)
  suffixMatch = _messages.StringField(8)


class HttpRouteHeaderMatchIntegerRange(_messages.Message):
  r"""Represents an integer value range.

  Fields:
    end: End of the range (exclusive)
    start: Start of the range (inclusive)
  """

  end = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  start = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class HttpRouteHeaderModifier(_messages.Message):
  r"""The specification for modifying HTTP header in HTTP request and HTTP
  response.

  Messages:
    AddValue: Add the headers with given map where key is the name of the
      header, value is the value of the header.
    SetValue: Completely overwrite/replace the headers with given map where
      key is the name of the header, value is the value of the header.

  Fields:
    add: Add the headers with given map where key is the name of the header,
      value is the value of the header.
    remove: Remove headers (matching by header names) specified in the list.
    set: Completely overwrite/replace the headers with given map where key is
      the name of the header, value is the value of the header.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AddValue(_messages.Message):
    r"""Add the headers with given map where key is the name of the header,
    value is the value of the header.

    Messages:
      AdditionalProperty: An additional property for a AddValue object.

    Fields:
      additionalProperties: Additional properties of type AddValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AddValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SetValue(_messages.Message):
    r"""Completely overwrite/replace the headers with given map where key is
    the name of the header, value is the value of the header.

    Messages:
      AdditionalProperty: An additional property for a SetValue object.

    Fields:
      additionalProperties: Additional properties of type SetValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SetValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  add = _messages.MessageField('AddValue', 1)
  remove = _messages.StringField(2, repeated=True)
  set = _messages.MessageField('SetValue', 3)


class HttpRouteHttpDirectResponse(_messages.Message):
  r"""Static HTTP response object to be returned.

  Fields:
    bytesBody: Optional. Response body as bytes. Maximum body size is 4096B.
    status: Required. Status to return as part of HTTP Response. Must be a
      positive integer.
    stringBody: Optional. Response body as a string. Maximum body length is
      1024 characters.
  """

  bytesBody = _messages.BytesField(1)
  status = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  stringBody = _messages.StringField(3)


class HttpRouteQueryParameterMatch(_messages.Message):
  r"""Specifications to match a query parameter in the request.

  Fields:
    exactMatch: The value of the query parameter must exactly match the
      contents of exact_match. Only one of exact_match, regex_match, or
      present_match must be set.
    presentMatch: Specifies that the QueryParameterMatcher matches if request
      contains query parameter, irrespective of whether the parameter has a
      value or not. Only one of exact_match, regex_match, or present_match
      must be set.
    queryParameter: The name of the query parameter to match.
    regexMatch: The value of the query parameter must match the regular
      expression specified by regex_match. For regular expression grammar,
      please see https://github.com/google/re2/wiki/Syntax Only one of
      exact_match, regex_match, or present_match must be set.
  """

  exactMatch = _messages.StringField(1)
  presentMatch = _messages.BooleanField(2)
  queryParameter = _messages.StringField(3)
  regexMatch = _messages.StringField(4)


class HttpRouteRedirect(_messages.Message):
  r"""The specification for redirecting traffic.

  Enums:
    ResponseCodeValueValuesEnum: The HTTP Status code to use for the redirect.

  Fields:
    hostRedirect: The host that will be used in the redirect response instead
      of the one that was supplied in the request.
    httpsRedirect: If set to true, the URL scheme in the redirected request is
      set to https. If set to false, the URL scheme of the redirected request
      will remain the same as that of the request. The default is set to
      false.
    pathRedirect: The path that will be used in the redirect response instead
      of the one that was supplied in the request. path_redirect can not be
      supplied together with prefix_redirect. Supply one alone or neither. If
      neither is supplied, the path of the original request will be used for
      the redirect.
    portRedirect: The port that will be used in the redirected request instead
      of the one that was supplied in the request.
    prefixRewrite: Indicates that during redirection, the matched prefix (or
      path) should be swapped with this value. This option allows URLs be
      dynamically created based on the request.
    responseCode: The HTTP Status code to use for the redirect.
    stripQuery: if set to true, any accompanying query portion of the original
      URL is removed prior to redirecting the request. If set to false, the
      query portion of the original URL is retained. The default is set to
      false.
  """

  class ResponseCodeValueValuesEnum(_messages.Enum):
    r"""The HTTP Status code to use for the redirect.

    Values:
      RESPONSE_CODE_UNSPECIFIED: Default value
      MOVED_PERMANENTLY_DEFAULT: Corresponds to 301.
      FOUND: Corresponds to 302.
      SEE_OTHER: Corresponds to 303.
      TEMPORARY_REDIRECT: Corresponds to 307. In this case, the request method
        will be retained.
      PERMANENT_REDIRECT: Corresponds to 308. In this case, the request method
        will be retained.
    """
    RESPONSE_CODE_UNSPECIFIED = 0
    MOVED_PERMANENTLY_DEFAULT = 1
    FOUND = 2
    SEE_OTHER = 3
    TEMPORARY_REDIRECT = 4
    PERMANENT_REDIRECT = 5

  hostRedirect = _messages.StringField(1)
  httpsRedirect = _messages.BooleanField(2)
  pathRedirect = _messages.StringField(3)
  portRedirect = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  prefixRewrite = _messages.StringField(5)
  responseCode = _messages.EnumField('ResponseCodeValueValuesEnum', 6)
  stripQuery = _messages.BooleanField(7)


class HttpRouteRequestMirrorPolicy(_messages.Message):
  r"""Specifies the policy on how requests are shadowed to a separate mirrored
  destination service. The proxy does not wait for responses from the shadow
  service. Prior to sending traffic to the shadow service, the host/authority
  header is suffixed with -shadow. Mirroring is currently not supported for
  Cloud Run destinations.

  Fields:
    destination: The destination the requests will be mirrored to. The weight
      of the destination will be ignored.
    mirrorPercent: Optional. The percentage of requests to get mirrored to the
      desired destination.
  """

  destination = _messages.MessageField('HttpRouteDestination', 1)
  mirrorPercent = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class HttpRouteRetryPolicy(_messages.Message):
  r"""The specifications for retries.

  Fields:
    numRetries: Specifies the allowed number of retries. This number must be >
      0. If not specified, default to 1.
    perTryTimeout: Specifies a non-zero timeout per retry attempt.
    retryConditions: Specifies one or more conditions when this retry policy
      applies. Valid values are: 5xx: Proxy will attempt a retry if the
      destination service responds with any 5xx response code, of if the
      destination service does not respond at all, example: disconnect, reset,
      read timeout, connection failure and refused streams. gateway-error:
      Similar to 5xx, but only applies to response codes 502, 503, 504. reset:
      Proxy will attempt a retry if the destination service does not respond
      at all (disconnect/reset/read timeout) connect-failure: Proxy will retry
      on failures connecting to destination for example due to connection
      timeouts. retriable-4xx: Proxy will retry fro retriable 4xx response
      codes. Currently the only retriable error supported is 409. refused-
      stream: Proxy will retry if the destination resets the stream with a
      REFUSED_STREAM error code. This reset type indicates that it is safe to
      retry.
  """

  numRetries = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  perTryTimeout = _messages.StringField(2)
  retryConditions = _messages.StringField(3, repeated=True)


class HttpRouteRouteAction(_messages.Message):
  r"""The specifications for routing traffic and applying associated policies.

  Fields:
    corsPolicy: The specification for allowing client side cross-origin
      requests.
    destinations: The destination to which traffic should be forwarded.
    directResponse: Optional. Static HTTP Response object to be returned
      regardless of the request.
    faultInjectionPolicy: The specification for fault injection introduced
      into traffic to test the resiliency of clients to backend service
      failure. As part of fault injection, when clients send requests to a
      backend service, delays can be introduced on a percentage of requests
      before sending those requests to the backend service. Similarly requests
      from clients can be aborted for a percentage of requests. timeout and
      retry_policy will be ignored by clients that are configured with a
      fault_injection_policy
    idleTimeout: Optional. Specifies the idle timeout for the selected route.
      The idle timeout is defined as the period in which there are no bytes
      sent or received on either the upstream or downstream connection. If not
      set, the default idle timeout is 1 hour. If set to 0s, the timeout will
      be disabled.
    redirect: If set, the request is directed as configured by this field.
    requestHeaderModifier: The specification for modifying the headers of a
      matching request prior to delivery of the request to the destination. If
      HeaderModifiers are set on both the Destination and the RouteAction,
      they will be merged. Conflicts between the two will not be resolved on
      the configuration.
    requestMirrorPolicy: Specifies the policy on how requests intended for the
      routes destination are shadowed to a separate mirrored destination.
      Proxy will not wait for the shadow destination to respond before
      returning the response. Prior to sending traffic to the shadow service,
      the host/authority header is suffixed with -shadow.
    responseHeaderModifier: The specification for modifying the headers of a
      response prior to sending the response back to the client. If
      HeaderModifiers are set on both the Destination and the RouteAction,
      they will be merged. Conflicts between the two will not be resolved on
      the configuration.
    retryPolicy: Specifies the retry policy associated with this route.
    statefulSessionAffinity: Optional. Specifies cookie-based stateful session
      affinity.
    timeout: Specifies the timeout for selected route. Timeout is computed
      from the time the request has been fully processed (i.e. end of stream)
      up until the response has been completely processed. Timeout includes
      all retries.
    urlRewrite: The specification for rewrite URL before forwarding requests
      to the destination.
  """

  corsPolicy = _messages.MessageField('HttpRouteCorsPolicy', 1)
  destinations = _messages.MessageField('HttpRouteDestination', 2, repeated=True)
  directResponse = _messages.MessageField('HttpRouteHttpDirectResponse', 3)
  faultInjectionPolicy = _messages.MessageField('HttpRouteFaultInjectionPolicy', 4)
  idleTimeout = _messages.StringField(5)
  redirect = _messages.MessageField('HttpRouteRedirect', 6)
  requestHeaderModifier = _messages.MessageField('HttpRouteHeaderModifier', 7)
  requestMirrorPolicy = _messages.MessageField('HttpRouteRequestMirrorPolicy', 8)
  responseHeaderModifier = _messages.MessageField('HttpRouteHeaderModifier', 9)
  retryPolicy = _messages.MessageField('HttpRouteRetryPolicy', 10)
  statefulSessionAffinity = _messages.MessageField('HttpRouteStatefulSessionAffinityPolicy', 11)
  timeout = _messages.StringField(12)
  urlRewrite = _messages.MessageField('HttpRouteURLRewrite', 13)


class HttpRouteRouteMatch(_messages.Message):
  r"""RouteMatch defines specifications used to match requests. If multiple
  match types are set, this RouteMatch will match if ALL type of matches are
  matched.

  Fields:
    fullPathMatch: The HTTP request path value should exactly match this
      value. Only one of full_path_match, prefix_match, or regex_match should
      be used.
    headers: Specifies a list of HTTP request headers to match against. ALL of
      the supplied headers must be matched.
    ignoreCase: Specifies if prefix_match and full_path_match matches are case
      sensitive. The default value is false.
    prefixMatch: The HTTP request path value must begin with specified
      prefix_match. prefix_match must begin with a /. Only one of
      full_path_match, prefix_match, or regex_match should be used.
    queryParameters: Specifies a list of query parameters to match against.
      ALL of the query parameters must be matched.
    regexMatch: The HTTP request path value must satisfy the regular
      expression specified by regex_match after removing any query parameters
      and anchor supplied with the original URL. For regular expression
      grammar, please see https://github.com/google/re2/wiki/Syntax Only one
      of full_path_match, prefix_match, or regex_match should be used.
  """

  fullPathMatch = _messages.StringField(1)
  headers = _messages.MessageField('HttpRouteHeaderMatch', 2, repeated=True)
  ignoreCase = _messages.BooleanField(3)
  prefixMatch = _messages.StringField(4)
  queryParameters = _messages.MessageField('HttpRouteQueryParameterMatch', 5, repeated=True)
  regexMatch = _messages.StringField(6)


class HttpRouteRouteRule(_messages.Message):
  r"""Specifies how to match traffic and how to route traffic when traffic is
  matched.

  Fields:
    action: The detailed rule defining how to route matched traffic.
    matches: A list of matches define conditions used for matching the rule
      against incoming HTTP requests. Each match is independent, i.e. this
      rule will be matched if ANY one of the matches is satisfied. If no
      matches field is specified, this rule will unconditionally match
      traffic. If a default rule is desired to be configured, add a rule with
      no matches specified to the end of the rules list.
  """

  action = _messages.MessageField('HttpRouteRouteAction', 1)
  matches = _messages.MessageField('HttpRouteRouteMatch', 2, repeated=True)


class HttpRouteStatefulSessionAffinityPolicy(_messages.Message):
  r"""The specification for cookie-based stateful session affinity where the
  date plane supplies a "session cookie" with the name "GSSA" which encodes a
  specific destination host and each request containing that cookie will be
  directed to that host as long as the destination host remains up and
  healthy. The gRPC proxyless mesh library or sidecar proxy will manage the
  session cookie but the client application code is responsible for copying
  the cookie from each RPC in the session to the next.

  Fields:
    cookieTtl: Required. The cookie TTL value for the Set-Cookie header
      generated by the data plane. The lifetime of the cookie may be set to a
      value from 0 to 86400 seconds (24 hours) inclusive. Set this to 0s to
      use a session cookie and disable cookie expiration.
  """

  cookieTtl = _messages.StringField(1)


class HttpRouteURLRewrite(_messages.Message):
  r"""The specification for modifying the URL of the request, prior to
  forwarding the request to the destination.

  Fields:
    hostRewrite: Prior to forwarding the request to the selected destination,
      the requests host header is replaced by this value.
    pathPrefixRewrite: Prior to forwarding the request to the selected
      destination, the matching portion of the requests path is replaced by
      this value.
  """

  hostRewrite = _messages.StringField(1)
  pathPrefixRewrite = _messages.StringField(2)


class InvalidateCacheRequest(_messages.Message):
  r"""The request used by the `InvalidateCache` method.

  Fields:
    cacheTags: A list of cache tags used to identify cached objects. Cache
      tags are specified when the response is first cached, by setting the
      `Cache-Tag` response header at the origin. By default, all objects have
      a cache tag representing the HTTP status code of the response, the MIME
      content-type, and the origin. Multiple cache tags in the same
      revalidation request are treated as Boolean `OR` - for example, `tag1 OR
      tag2 OR tag3`. If a host and path are also specified, these are treated
      as Boolean `AND` with any tags. Up to 10 tags can be specified in a
      single invalidation request.
    host: The hostname to invalidate against. You can specify an exact or
      wildcard host based on the host component. For example,
      `video.example.com` or `*.example.com`.
    path: The path to invalidate against. You can specify an exact or wildcard
      path based on the a path component. For example,
      `/videos/hls/139123.mp4` or `/manifests/*`.
  """

  cacheTags = _messages.StringField(1, repeated=True)
  host = _messages.StringField(2)
  path = _messages.StringField(3)


class InvalidateCacheResponse(_messages.Message):
  r"""The response used by the `InvalidateCache` method."""


class LbRouteExtension(_messages.Message):
  r"""`LbRouteExtension` is a resource that lets you control where traffic is
  routed to for a given request.

  Enums:
    LoadBalancingSchemeValueValuesEnum: Required. All backend services and
      forwarding rules referenced by this extension must share the same load
      balancing scheme. Supported values: `INTERNAL_MANAGED`,
      `EXTERNAL_MANAGED`. For more information, refer to [Backend services
      overview](https://cloud.google.com/load-balancing/docs/backend-service).

  Messages:
    LabelsValue: Optional. Set of labels associated with the
      `LbRouteExtension` resource. The format must comply with [the
      requirements for labels](https://cloud.google.com/compute/docs/labeling-
      resources#requirements) for Google Cloud resources.
    MetadataValue: Optional. The metadata provided here is included as part of
      the `metadata_context` (of type `google.protobuf.Struct`) in the
      `ProcessingRequest` message sent to the extension server. The metadata
      applies to all extensions in all extensions chains in this resource. The
      metadata is available under the key `com.google.lb_route_extension.`.
      The following variables are supported in the metadata:
      `{forwarding_rule_id}` - substituted with the forwarding rule's fully
      qualified resource name. This field must not be set if at least one of
      the extension chains contains plugin extensions. Setting it results in a
      validation error. You can set metadata at either the resource level or
      the extension level. The extension level metadata is recommended because
      you can pass a different set of metadata through each extension to the
      backend.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A human-readable description of the resource.
    extensionChains: Required. A set of ordered extension chains that contain
      the match conditions and extensions to execute. Match conditions for
      each extension chain are evaluated in sequence for a given request. The
      first extension chain that has a condition that matches the request is
      executed. Any subsequent extension chains do not execute. Limited to 5
      extension chains per resource.
    forwardingRules: Required. A list of references to the forwarding rules to
      which this service extension is attached. At least one forwarding rule
      is required. Only one `LbRouteExtension` resource can be associated with
      a forwarding rule.
    labels: Optional. Set of labels associated with the `LbRouteExtension`
      resource. The format must comply with [the requirements for
      labels](https://cloud.google.com/compute/docs/labeling-
      resources#requirements) for Google Cloud resources.
    loadBalancingScheme: Required. All backend services and forwarding rules
      referenced by this extension must share the same load balancing scheme.
      Supported values: `INTERNAL_MANAGED`, `EXTERNAL_MANAGED`. For more
      information, refer to [Backend services
      overview](https://cloud.google.com/load-balancing/docs/backend-service).
    metadata: Optional. The metadata provided here is included as part of the
      `metadata_context` (of type `google.protobuf.Struct`) in the
      `ProcessingRequest` message sent to the extension server. The metadata
      applies to all extensions in all extensions chains in this resource. The
      metadata is available under the key `com.google.lb_route_extension.`.
      The following variables are supported in the metadata:
      `{forwarding_rule_id}` - substituted with the forwarding rule's fully
      qualified resource name. This field must not be set if at least one of
      the extension chains contains plugin extensions. Setting it results in a
      validation error. You can set metadata at either the resource level or
      the extension level. The extension level metadata is recommended because
      you can pass a different set of metadata through each extension to the
      backend.
    name: Required. Identifier. Name of the `LbRouteExtension` resource in the
      following format: `projects/{project}/locations/{location}/lbRouteExtens
      ions/{lb_route_extension}`.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  class LoadBalancingSchemeValueValuesEnum(_messages.Enum):
    r"""Required. All backend services and forwarding rules referenced by this
    extension must share the same load balancing scheme. Supported values:
    `INTERNAL_MANAGED`, `EXTERNAL_MANAGED`. For more information, refer to
    [Backend services overview](https://cloud.google.com/load-
    balancing/docs/backend-service).

    Values:
      LOAD_BALANCING_SCHEME_UNSPECIFIED: Default value. Do not use.
      INTERNAL_MANAGED: Signifies that this is used for Internal HTTP(S) Load
        Balancing.
      EXTERNAL_MANAGED: Signifies that this is used for External Managed
        HTTP(S) Load Balancing.
    """
    LOAD_BALANCING_SCHEME_UNSPECIFIED = 0
    INTERNAL_MANAGED = 1
    EXTERNAL_MANAGED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of labels associated with the `LbRouteExtension`
    resource. The format must comply with [the requirements for
    labels](https://cloud.google.com/compute/docs/labeling-
    resources#requirements) for Google Cloud resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. The metadata provided here is included as part of the
    `metadata_context` (of type `google.protobuf.Struct`) in the
    `ProcessingRequest` message sent to the extension server. The metadata
    applies to all extensions in all extensions chains in this resource. The
    metadata is available under the key `com.google.lb_route_extension.`. The
    following variables are supported in the metadata: `{forwarding_rule_id}`
    - substituted with the forwarding rule's fully qualified resource name.
    This field must not be set if at least one of the extension chains
    contains plugin extensions. Setting it results in a validation error. You
    can set metadata at either the resource level or the extension level. The
    extension level metadata is recommended because you can pass a different
    set of metadata through each extension to the backend.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  extensionChains = _messages.MessageField('ExtensionChain', 3, repeated=True)
  forwardingRules = _messages.StringField(4, repeated=True)
  labels = _messages.MessageField('LabelsValue', 5)
  loadBalancingScheme = _messages.EnumField('LoadBalancingSchemeValueValuesEnum', 6)
  metadata = _messages.MessageField('MetadataValue', 7)
  name = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class LbTrafficExtension(_messages.Message):
  r"""`LbTrafficExtension` is a resource that lets the extension service
  modify the headers and payloads of both requests and responses without
  impacting the choice of backend services or any other security policies
  associated with the backend service.

  Enums:
    LoadBalancingSchemeValueValuesEnum: Required. All backend services and
      forwarding rules referenced by this extension must share the same load
      balancing scheme. Supported values: `INTERNAL_MANAGED` and
      `EXTERNAL_MANAGED`. For more information, refer to [Backend services
      overview](https://cloud.google.com/load-balancing/docs/backend-service).

  Messages:
    LabelsValue: Optional. Set of labels associated with the
      `LbTrafficExtension` resource. The format must comply with [the
      requirements for labels](https://cloud.google.com/compute/docs/labeling-
      resources#requirements) for Google Cloud resources.
    MetadataValue: Optional. The metadata provided here is included as part of
      the `metadata_context` (of type `google.protobuf.Struct`) in the
      `ProcessingRequest` message sent to the extension server. The metadata
      applies to all extensions in all extensions chains in this resource. The
      metadata is available under the key `com.google.lb_traffic_extension.`.
      The following variables are supported in the metadata:
      `{forwarding_rule_id}` - substituted with the forwarding rule's fully
      qualified resource name. This field must not be set if at least one of
      the extension chains contains plugin extensions. Setting it results in a
      validation error. You can set metadata at either the resource level or
      the extension level. The extension level metadata is recommended because
      you can pass a different set of metadata through each extension to the
      backend.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A human-readable description of the resource.
    extensionChains: Required. A set of ordered extension chains that contain
      the match conditions and extensions to execute. Match conditions for
      each extension chain are evaluated in sequence for a given request. The
      first extension chain that has a condition that matches the request is
      executed. Any subsequent extension chains do not execute. Limited to 5
      extension chains per resource.
    forwardingRules: Optional. A list of references to the forwarding rules to
      which this service extension is attached. At least one forwarding rule
      is required. Only one `LbTrafficExtension` resource can be associated
      with a forwarding rule.
    labels: Optional. Set of labels associated with the `LbTrafficExtension`
      resource. The format must comply with [the requirements for
      labels](https://cloud.google.com/compute/docs/labeling-
      resources#requirements) for Google Cloud resources.
    loadBalancingScheme: Required. All backend services and forwarding rules
      referenced by this extension must share the same load balancing scheme.
      Supported values: `INTERNAL_MANAGED` and `EXTERNAL_MANAGED`. For more
      information, refer to [Backend services
      overview](https://cloud.google.com/load-balancing/docs/backend-service).
    metadata: Optional. The metadata provided here is included as part of the
      `metadata_context` (of type `google.protobuf.Struct`) in the
      `ProcessingRequest` message sent to the extension server. The metadata
      applies to all extensions in all extensions chains in this resource. The
      metadata is available under the key `com.google.lb_traffic_extension.`.
      The following variables are supported in the metadata:
      `{forwarding_rule_id}` - substituted with the forwarding rule's fully
      qualified resource name. This field must not be set if at least one of
      the extension chains contains plugin extensions. Setting it results in a
      validation error. You can set metadata at either the resource level or
      the extension level. The extension level metadata is recommended because
      you can pass a different set of metadata through each extension to the
      backend.
    name: Required. Identifier. Name of the `LbTrafficExtension` resource in
      the following format: `projects/{project}/locations/{location}/lbTraffic
      Extensions/{lb_traffic_extension}`.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  class LoadBalancingSchemeValueValuesEnum(_messages.Enum):
    r"""Required. All backend services and forwarding rules referenced by this
    extension must share the same load balancing scheme. Supported values:
    `INTERNAL_MANAGED` and `EXTERNAL_MANAGED`. For more information, refer to
    [Backend services overview](https://cloud.google.com/load-
    balancing/docs/backend-service).

    Values:
      LOAD_BALANCING_SCHEME_UNSPECIFIED: Default value. Do not use.
      INTERNAL_MANAGED: Signifies that this is used for Internal HTTP(S) Load
        Balancing.
      EXTERNAL_MANAGED: Signifies that this is used for External Managed
        HTTP(S) Load Balancing.
    """
    LOAD_BALANCING_SCHEME_UNSPECIFIED = 0
    INTERNAL_MANAGED = 1
    EXTERNAL_MANAGED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of labels associated with the `LbTrafficExtension`
    resource. The format must comply with [the requirements for
    labels](https://cloud.google.com/compute/docs/labeling-
    resources#requirements) for Google Cloud resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. The metadata provided here is included as part of the
    `metadata_context` (of type `google.protobuf.Struct`) in the
    `ProcessingRequest` message sent to the extension server. The metadata
    applies to all extensions in all extensions chains in this resource. The
    metadata is available under the key `com.google.lb_traffic_extension.`.
    The following variables are supported in the metadata:
    `{forwarding_rule_id}` - substituted with the forwarding rule's fully
    qualified resource name. This field must not be set if at least one of the
    extension chains contains plugin extensions. Setting it results in a
    validation error. You can set metadata at either the resource level or the
    extension level. The extension level metadata is recommended because you
    can pass a different set of metadata through each extension to the
    backend.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  extensionChains = _messages.MessageField('ExtensionChain', 3, repeated=True)
  forwardingRules = _messages.StringField(4, repeated=True)
  labels = _messages.MessageField('LabelsValue', 5)
  loadBalancingScheme = _messages.EnumField('LoadBalancingSchemeValueValuesEnum', 6)
  metadata = _messages.MessageField('MetadataValue', 7)
  name = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class ListAuthzExtensionsResponse(_messages.Message):
  r"""Message for response to listing `AuthzExtension` resources.

  Fields:
    authzExtensions: The list of `AuthzExtension` resources.
    nextPageToken: A token identifying a page of results that the server
      returns.
    unreachable: Locations that could not be reached.
  """

  authzExtensions = _messages.MessageField('AuthzExtension', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListEdgeCacheKeysetsResponse(_messages.Message):
  r"""The response returned by the `ListEdgeCacheKeysets` method.

  Fields:
    edgeCacheKeysets: A list of `EdgeCacheKeyset` resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      page_token.
    unreachable: Unreachable resources. Populated when the request attempts to
      list all resources across all supported locations, while some locations
      are temporarily unavailable.
  """

  edgeCacheKeysets = _messages.MessageField('EdgeCacheKeyset', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListEdgeCacheOriginsResponse(_messages.Message):
  r"""The response returned by the `ListEdgeCacheOrigins` method.

  Fields:
    edgeCacheOrigins: A list of `EdgeCacheOrigin` resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      page_token.
    unreachable: Unreachable resources. Populated when the request attempts to
      list all resources across all supported locations, while some locations
      are temporarily unavailable.
  """

  edgeCacheOrigins = _messages.MessageField('EdgeCacheOrigin', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListEdgeCacheServicesResponse(_messages.Message):
  r"""Response returned by the `list` method.

  Fields:
    edgeCacheServices: The list of EdgeCacheService resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      page_token.
    unreachable: Unreachable resources. Populated when the request attempts to
      list all resources across all supported locations, while some locations
      are temporarily unavailable.
  """

  edgeCacheServices = _messages.MessageField('EdgeCacheService', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListEndpointPoliciesResponse(_messages.Message):
  r"""Response returned by the ListEndpointPolicies method.

  Fields:
    endpointPolicies: List of EndpointPolicy resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Unreachable resources. Populated when the request opts into
      return_partial_success and reading across collections e.g. when
      attempting to list all resources across all supported locations.
  """

  endpointPolicies = _messages.MessageField('EndpointPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGatewayRouteViewsResponse(_messages.Message):
  r"""Response returned by the ListGatewayRouteViews method.

  Fields:
    gatewayRouteViews: List of GatewayRouteView resources.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Unreachable resources. Populated when the request attempts to
      list all resources across all supported locations, while some locations
      are temporarily unavailable.
  """

  gatewayRouteViews = _messages.MessageField('GatewayRouteView', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGatewaysResponse(_messages.Message):
  r"""Response returned by the ListGateways method.

  Fields:
    gateways: List of Gateway resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Locations that could not be reached.
  """

  gateways = _messages.MessageField('Gateway', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGrpcRoutesResponse(_messages.Message):
  r"""Response returned by the ListGrpcRoutes method.

  Fields:
    grpcRoutes: List of GrpcRoute resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Unreachable resources. Populated when the request opts into
      return_partial_success and reading across collections e.g. when
      attempting to list all resources across all supported locations.
  """

  grpcRoutes = _messages.MessageField('GrpcRoute', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListHttpRoutesResponse(_messages.Message):
  r"""Response returned by the ListHttpRoutes method.

  Fields:
    httpRoutes: List of HttpRoute resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Unreachable resources. Populated when the request opts into
      return_partial_success and reading across collections e.g. when
      attempting to list all resources across all supported locations.
  """

  httpRoutes = _messages.MessageField('HttpRoute', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLbRouteExtensionsResponse(_messages.Message):
  r"""Message for response to listing `LbRouteExtension` resources.

  Fields:
    lbRouteExtensions: The list of `LbRouteExtension` resources.
    nextPageToken: A token identifying a page of results that the server
      returns.
    unreachable: Locations that could not be reached.
  """

  lbRouteExtensions = _messages.MessageField('LbRouteExtension', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLbTrafficExtensionsResponse(_messages.Message):
  r"""Message for response to listing `LbTrafficExtension` resources.

  Fields:
    lbTrafficExtensions: The list of `LbTrafficExtension` resources.
    nextPageToken: A token identifying a page of results that the server
      returns.
    unreachable: Locations that could not be reached.
  """

  lbTrafficExtensions = _messages.MessageField('LbTrafficExtension', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMeshRouteViewsResponse(_messages.Message):
  r"""Response returned by the ListMeshRouteViews method.

  Fields:
    meshRouteViews: List of MeshRouteView resources.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Unreachable resources. Populated when the request attempts to
      list all resources across all supported locations, while some locations
      are temporarily unavailable.
  """

  meshRouteViews = _messages.MessageField('MeshRouteView', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMeshesResponse(_messages.Message):
  r"""Response returned by the ListMeshes method.

  Fields:
    meshes: List of Mesh resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Unreachable resources. Populated when the request opts into
      `return_partial_success` and reading across collections e.g. when
      attempting to list all resources across all supported locations.
  """

  meshes = _messages.MessageField('Mesh', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMulticastConsumerAssociationsResponse(_messages.Message):
  r"""Response message for ListMulticastConsumerAssociations.

  Fields:
    multicastConsumerAssociations: The list of multicast consumer
      associations.
    nextPageToken: A page token from an earlier query, as returned in
      `next_page_token`.
    unreachable: Locations that could not be reached.
  """

  multicastConsumerAssociations = _messages.MessageField('MulticastConsumerAssociation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMulticastDomainActivationsResponse(_messages.Message):
  r"""Response message for ListMulticastDomainActivations.

  Fields:
    multicastDomainActivations: The list of multicast domain activations.
    nextPageToken: A page token from an earlier query, as returned in
      `next_page_token`.
    unreachable: Locations that could not be reached.
  """

  multicastDomainActivations = _messages.MessageField('MulticastDomainActivation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMulticastDomainGroupsResponse(_messages.Message):
  r"""Response message for ListMulticastDomainGroups.

  Fields:
    multicastDomainGroups: The list of multicast domain groups.
    nextPageToken: A page token from an earlier query, as returned in
      `next_page_token`.
    unreachable: Locations that could not be reached.
  """

  multicastDomainGroups = _messages.MessageField('MulticastDomainGroup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMulticastDomainsResponse(_messages.Message):
  r"""Response message for ListMulticastDomains.

  Fields:
    multicastDomains: The list of multicast domains.
    nextPageToken: A page token from an earlier query, as returned in
      `next_page_token`.
    unreachable: Locations that could not be reached.
  """

  multicastDomains = _messages.MessageField('MulticastDomain', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMulticastGroupConsumerActivationsResponse(_messages.Message):
  r"""Response message for ListMulticastGroupConsumerActivations.

  Fields:
    multicastGroupConsumerActivations: The list of multicast group consumer
      activations.
    nextPageToken: A page token from an earlier query, as returned in
      `next_page_token`.
    unreachable: Locations that could not be reached.
  """

  multicastGroupConsumerActivations = _messages.MessageField('MulticastGroupConsumerActivation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMulticastGroupProducerActivationsResponse(_messages.Message):
  r"""Response message for ListMulticastGroupProducerActivations.

  Fields:
    multicastGroupProducerActivations: The list of multicast group producer
      activations.
    nextPageToken: A page token from an earlier query, as returned in
      `next_page_token`.
    unreachable: Locations that could not be reached.
  """

  multicastGroupProducerActivations = _messages.MessageField('MulticastGroupProducerActivation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMulticastGroupRangeActivationsResponse(_messages.Message):
  r"""Response message for ListMulticastGroupRangeActivations.

  Fields:
    multicastGroupRangeActivations: The list of multicast group range
      activations.
    nextPageToken: A page token from an earlier query, as returned in
      `next_page_token`.
    unreachable: Locations that could not be reached.
  """

  multicastGroupRangeActivations = _messages.MessageField('MulticastGroupRangeActivation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMulticastGroupRangesResponse(_messages.Message):
  r"""Response message for ListMulticastGroupRanges.

  Fields:
    multicastGroupRanges: The list of multicast group ranges.
    nextPageToken: A page token from an earlier query, as returned in
      `next_page_token`.
    unreachable: Locations that could not be reached.
  """

  multicastGroupRanges = _messages.MessageField('MulticastGroupRange', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMulticastProducerAssociationsResponse(_messages.Message):
  r"""Response message for ListMulticastProducerAssociations.

  Fields:
    multicastProducerAssociations: The list of multicast producer
      associations.
    nextPageToken: A page token from an earlier query, as returned in
      `next_page_token`.
    unreachable: Locations that could not be reached.
  """

  multicastProducerAssociations = _messages.MessageField('MulticastProducerAssociation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListServiceBindingsResponse(_messages.Message):
  r"""Response returned by the ListServiceBindings method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    serviceBindings: List of ServiceBinding resources.
    unreachable: Unreachable resources. Populated when the request attempts to
      list all resources across all supported locations, while some locations
      are temporarily unavailable.
  """

  nextPageToken = _messages.StringField(1)
  serviceBindings = _messages.MessageField('ServiceBinding', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListServiceLbPoliciesResponse(_messages.Message):
  r"""Response returned by the ListServiceLbPolicies method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    serviceLbPolicies: List of ServiceLbPolicy resources.
    unreachable: Unreachable resources. Populated when the request attempts to
      list all resources across all supported locations, while some locations
      are temporarily unavailable.
  """

  nextPageToken = _messages.StringField(1)
  serviceLbPolicies = _messages.MessageField('ServiceLbPolicy', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListTcpRoutesResponse(_messages.Message):
  r"""Response returned by the ListTcpRoutes method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    tcpRoutes: List of TcpRoute resources.
    unreachable: Unreachable resources. Populated when the request opts into
      return_partial_success and reading across collections e.g. when
      attempting to list all resources across all supported locations.
  """

  nextPageToken = _messages.StringField(1)
  tcpRoutes = _messages.MessageField('TcpRoute', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListTlsRoutesResponse(_messages.Message):
  r"""Response returned by the ListTlsRoutes method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    tlsRoutes: List of TlsRoute resources.
    unreachable: Unreachable resources. Populated when the request opts into
      return_partial_success and reading across collections e.g. when
      attempting to list all resources across all supported locations.
  """

  nextPageToken = _messages.StringField(1)
  tlsRoutes = _messages.MessageField('TlsRoute', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListWasmPluginVersionsResponse(_messages.Message):
  r"""Response returned by the `ListWasmPluginVersions` method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Unreachable resources. Populated when the request attempts to
      list all resources across all supported locations, while some locations
      are temporarily unavailable.
    wasmPluginVersions: List of `WasmPluginVersion` resources.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  wasmPluginVersions = _messages.MessageField('WasmPluginVersion', 3, repeated=True)


class ListWasmPluginsResponse(_messages.Message):
  r"""Response returned by the `ListWasmPlugins` method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Unreachable resources. Populated when the request attempts to
      list all resources across all supported locations, while some locations
      are temporarily unavailable.
    wasmPlugins: List of `WasmPlugin` resources.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  wasmPlugins = _messages.MessageField('WasmPlugin', 3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LogConfig(_messages.Message):
  r"""The logging options for the traffic served by this service. If logging
  is enabled, logs are exported to Cloud Logging.

  Fields:
    enable: Optional. Specifies whether to enable logging for traffic served
      by this service. Defaults to false.
    sampleRate: Optional. The sampling rate of requests, where `1.0` means all
      logged requests are reported and `0.0` means no logged requests are
      reported. The default value is `0.0`, and the value of the field must be
      in `[0, 1]`. This field can be specified only if logging is enabled for
      this service.
  """

  enable = _messages.BooleanField(1)
  sampleRate = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class LoggingConfig(_messages.Message):
  r"""The configuration for Platform Telemetry logging for Eventarc Advanced
  resources.

  Enums:
    LogSeverityValueValuesEnum: Optional. The minimum severity of logs that
      will be sent to Stackdriver/Platform Telemetry. Logs at severitiy
      \\u2265 this value will be sent, unless it is NONE.

  Fields:
    logSeverity: Optional. The minimum severity of logs that will be sent to
      Stackdriver/Platform Telemetry. Logs at severitiy \u2265 this value will
      be sent, unless it is NONE.
  """

  class LogSeverityValueValuesEnum(_messages.Enum):
    r"""Optional. The minimum severity of logs that will be sent to
    Stackdriver/Platform Telemetry. Logs at severitiy \\u2265 this value will
    be sent, unless it is NONE.

    Values:
      LOG_SEVERITY_UNSPECIFIED: Log severity is not specified. This value is
        treated the same as NONE, but is used to distinguish between no update
        and update to NONE in update_masks.
      NONE: Default value at resource creation, presence of this value must be
        treated as no logging/disable logging.
      DEBUG: Debug or trace level logging.
      INFO: Routine information, such as ongoing status or performance.
      NOTICE: Normal but significant events, such as start up, shut down, or a
        configuration change.
      WARNING: Warning events might cause problems.
      ERROR: Error events are likely to cause problems.
      CRITICAL: Critical events cause more severe problems or outages.
      ALERT: A person must take action immediately.
      EMERGENCY: One or more systems are unusable.
    """
    LOG_SEVERITY_UNSPECIFIED = 0
    NONE = 1
    DEBUG = 2
    INFO = 3
    NOTICE = 4
    WARNING = 5
    ERROR = 6
    CRITICAL = 7
    ALERT = 8
    EMERGENCY = 9

  logSeverity = _messages.EnumField('LogSeverityValueValuesEnum', 1)


class MatchRule(_messages.Message):
  r"""A collection of match conditions (such as query, header, or URI) for a
  request.

  Fields:
    fullPathMatch: Optional. To satisfy the `MatchRule` condition, the path of
      the request must exactly match the value specified in `full_path_match`
      after removing any query parameters and anchors that might be part of
      the original URL. `full_path_match` must begin with a `/`. The value
      must be between 1 and 1024 characters, (inclusive). One of prefix_match,
      `full_path_match`, or path_template_match must be specified.
    headerMatches: Optional. A list of HeaderMatch criteria, all of which must
      match corresponding headers in the request. You can specify up to three
      headers to match on.
    ignoreCase: Optional. Specifies that prefix_match and full_path_match
      matches are not case sensitive. The default value is `false`, which
      means that matches are case sensitive.
    pathTemplateMatch: Optional. To satisfy the `MatchRule` condition, the
      path of the request must match the wildcard pattern specified in
      `path_template_match` after removing any query parameters and anchors
      that might be part of the original URL. `path_template_match` must be
      between 1 and 255 characters (inclusive). The pattern specified by
      `path_template_match` can have at most 10 wildcard operators and 10
      variable captures. One of prefix_match, full_path_match, or
      `path_template_match` must be specified.
    prefixMatch: Optional. To satisfy the `MatchRule` condition, the request's
      path must begin with the specified `prefix_match`. `prefix_match` must
      begin with a `/`. The value must be between 1 and 1024 characters
      (inclusive). One of `prefix_match`, full_path_match, or
      path_template_match must be specified.
    queryParameterMatches: Optional. A list of QueryParameterMatcher criteria,
      all of which must match corresponding query parameters in the request.
      You can specify up to five query parameters to match on.
  """

  fullPathMatch = _messages.StringField(1)
  headerMatches = _messages.MessageField('HeaderMatch', 2, repeated=True)
  ignoreCase = _messages.BooleanField(3)
  pathTemplateMatch = _messages.StringField(4)
  prefixMatch = _messages.StringField(5)
  queryParameterMatches = _messages.MessageField('QueryParameterMatcher', 6, repeated=True)


class Mesh(_messages.Message):
  r"""Mesh represents a logical configuration grouping for workload to
  workload communication within a service mesh. Routes that point to mesh
  dictate how requests are routed within this logical mesh boundary.

  Enums:
    EnvoyHeadersValueValuesEnum: Optional. Determines if envoy will insert
      internal debug headers into upstream requests. Other Envoy headers may
      still be injected. By default, envoy will not insert any debug headers.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the Mesh
      resource.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    envoyHeaders: Optional. Determines if envoy will insert internal debug
      headers into upstream requests. Other Envoy headers may still be
      injected. By default, envoy will not insert any debug headers.
    interceptionPort: Optional. If set to a valid TCP port (1-65535),
      instructs the SIDECAR proxy to listen on the specified port of localhost
      (127.0.0.1) address. The SIDECAR proxy will expect all traffic to be
      redirected to this port regardless of its actual ip:port destination. If
      unset, a port '15001' is used as the interception port. This is
      applicable only for sidecar proxy deployments.
    labels: Optional. Set of label tags associated with the Mesh resource.
    name: Identifier. Name of the Mesh resource. It matches pattern
      `projects/*/locations/global/meshes/`.
    selfLink: Output only. Server-defined URL of this resource
    updateTime: Output only. The timestamp when the resource was updated.
  """

  class EnvoyHeadersValueValuesEnum(_messages.Enum):
    r"""Optional. Determines if envoy will insert internal debug headers into
    upstream requests. Other Envoy headers may still be injected. By default,
    envoy will not insert any debug headers.

    Values:
      ENVOY_HEADERS_UNSPECIFIED: Defaults to NONE.
      NONE: Suppress envoy debug headers.
      DEBUG_HEADERS: Envoy will insert default internal debug headers into
        upstream requests: x-envoy-attempt-count x-envoy-is-timeout-retry
        x-envoy-expected-rq-timeout-ms x-envoy-original-path x-envoy-upstream-
        stream-duration-ms
    """
    ENVOY_HEADERS_UNSPECIFIED = 0
    NONE = 1
    DEBUG_HEADERS = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the Mesh resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  envoyHeaders = _messages.EnumField('EnvoyHeadersValueValuesEnum', 3)
  interceptionPort = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  selfLink = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class MeshRouteView(_messages.Message):
  r"""MeshRouteView defines view-only resource for Routes to a Mesh

  Fields:
    name: Output only. Identifier. Full path name of the MeshRouteView
      resource. Format: projects/{project_number}/locations/{location}/meshes/
      {mesh}/routeViews/{route_view}
    routeId: Output only. The resource id for the route.
    routeLocation: Output only. Location where the route exists.
    routeProjectNumber: Output only. Project number where the route exists.
    routeType: Output only. Type of the route: HttpRoute,GrpcRoute,TcpRoute,
      or TlsRoute
  """

  name = _messages.StringField(1)
  routeId = _messages.StringField(2)
  routeLocation = _messages.StringField(3)
  routeProjectNumber = _messages.IntegerField(4)
  routeType = _messages.StringField(5)


class MulticastConsumerAssociation(_messages.Message):
  r"""Multicast consumer association resource.

  Enums:
    ResourceStateValueValuesEnum: Output only. The resource state of the
      multicast consumer association.

  Messages:
    LabelsValue: Optional. Labels as key-value pairs

  Fields:
    createTime: Output only. [Output only] The timestamp when the multicast
      consumer association was created.
    description: Optional. An optional text description of the multicast
      consumer association.
    labels: Optional. Labels as key-value pairs
    multicastDomainActivation: A string attribute.
    name: Identifier. The resource name of the multicast consumer association.
      Use the following format:
      `projects/*/locations/*/multicastConsumerAssociations/*`.
    network: Required. The resource name of the multicast consumer VPC
      network. Use following format:
      `projects/{project}/locations/global/networks/{network}`.
    placementPolicy: Output only. [Output only] A Compute Engine (placement
      policy)[https://cloud.google.com/compute/docs/instances/placement-
      policies-overview] that can be used to place virtual machine (VM)
      instances as multicast consumers close to the multicast infrastructure
      created for this domain, on a best effort basis.
    resourceState: Output only. The resource state of the multicast consumer
      association.
    uniqueId: Output only. [Output only] The Google-generated UUID for the
      resource. This value is unique across all multicast consumer association
      resources. If a consumer association is deleted and another with the
      same name is created, the new consumer association is assigned a
      different unique_id.
    updateTime: Output only. [Output only] The timestamp when the Multicast
      Consumer Association was most recently updated.
  """

  class ResourceStateValueValuesEnum(_messages.Enum):
    r"""Output only. The resource state of the multicast consumer association.

    Values:
      CONSUMER_RESOURCE_STATE_UNSPECIFIED: The consumer resource state is not
        specified.
      ACTIVE: The consumer resource state is active.
      OBSOLETE: The associated admin resource has been deleted. The consumer
        resource state becomes obsolete.
    """
    CONSUMER_RESOURCE_STATE_UNSPECIFIED = 0
    ACTIVE = 1
    OBSOLETE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key-value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  multicastDomainActivation = _messages.StringField(4)
  name = _messages.StringField(5)
  network = _messages.StringField(6)
  placementPolicy = _messages.StringField(7)
  resourceState = _messages.EnumField('ResourceStateValueValuesEnum', 8)
  uniqueId = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class MulticastDomain(_messages.Message):
  r"""Multicast domain resource.

  Messages:
    LabelsValue: Optional. Labels as key-value pairs.

  Fields:
    adminNetwork: Required. The resource name of the multicast admin VPC
      network. Use the following format:
      `projects/{project}/locations/global/networks/{network}`.
    connection: Required. The VPC connection type for this multicast domain.
    createTime: Output only. [Output only] The timestamp when the multicast
      domain was created.
    description: Optional. An optional text description of the multicast
      domain.
    labels: Optional. Labels as key-value pairs.
    multicastDomainGroup: Optional. The multicast domain group this domain
      should be associated with. Use the following format: `projects/{project}
      /locations/global/multicastDomainGroups/{multicast_domain_group}`.
    name: Identifier. The resource name of the multicast domain. Use the
      following format: `projects/*/locations/global/multicastDomains/*`
    ullMulticastDomain: Optional. Information for an Ultra-Low-Latency
      multicast domain.
    uniqueId: Output only. [Output only] The Google-generated UUID for the
      resource. This value is unique across all multicast domain resources. If
      a domain is deleted and another with the same name is created, the new
      domain is assigned a different unique_id.
    updateTime: Output only. [Output only] The timestamp when the multicast
      domain was most recently updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key-value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  adminNetwork = _messages.StringField(1)
  connection = _messages.MessageField('Connection', 2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  multicastDomainGroup = _messages.StringField(6)
  name = _messages.StringField(7)
  ullMulticastDomain = _messages.MessageField('UllMulticastDomain', 8)
  uniqueId = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class MulticastDomainActivation(_messages.Message):
  r"""Multicast domain activation resource.

  Messages:
    LabelsValue: Optional. Labels as key-value pairs

  Fields:
    adminNetwork: Output only. [Output only] The URL of the admin network.
    createTime: Output only. [Output only] The timestamp when the multicast
      domain activation was created.
    description: Optional. An optional text description of the multicast
      domain activation.
    labels: Optional. Labels as key-value pairs
    multicastConsumerAssociations: Output only. The resource names of
      associated multicast consumer associations. Use the following format:
      `projects/*/locations/*/multicastConsumerAssociations/*`.
    multicastDomain: Optional. The resource name of the multicast domain to
      activate. Use the following format:
      `projects/*/locations/global/multicastDomains/*`.
    name: Identifier. The resource name of the multicast domain activation.
      Use the following format:
      `projects/*/locations/*/multicastDomainActivations/*`.
    trafficSpec: Optional. The traffic specification for the multicast domain
      activation.
    uniqueId: Output only. [Output only] The Google-generated UUID for the
      resource. This value is unique across all multicast domain activation
      resources. If a domain activation is deleted and another with the same
      name is created, the new domain activation is assigned a different
      unique_id.
    updateTime: Output only. [Output only] The timestamp when the multicast
      domain activation was most recently updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key-value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  adminNetwork = _messages.StringField(1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  multicastConsumerAssociations = _messages.StringField(5, repeated=True)
  multicastDomain = _messages.StringField(6)
  name = _messages.StringField(7)
  trafficSpec = _messages.MessageField('TrafficSpec', 8)
  uniqueId = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class MulticastDomainGroup(_messages.Message):
  r"""Multicast domain group resource.

  Messages:
    LabelsValue: Optional. Labels as key-value pairs.

  Fields:
    createTime: Output only. [Output only] The timestamp when the multicast
      domain group was created.
    description: Optional. An optional text description of the multicast
      domain group.
    labels: Optional. Labels as key-value pairs.
    multicastDomains: Output only. [Output only] Multicast domains associated
      with the group. There can be at most 2 multicast domains in a group.
    name: Identifier. The resource name of the multicast domain group. Use the
      following format: `projects/*/locations/global/multicastDomainGroups/*`
    uniqueId: Output only. [Output only] The Google-generated UUID for the
      resource. This value is unique across all multicast domain group
      resources. If a domain is deleted and another with the same name is
      created, the new domain is assigned a different unique_id.
    updateTime: Output only. [Output only] The timestamp when the multicast
      domain group was most recently updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key-value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  multicastDomains = _messages.StringField(4, repeated=True)
  name = _messages.StringField(5)
  uniqueId = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class MulticastGroupConsumerActivation(_messages.Message):
  r"""Multicast group consumer activation resource.

  Enums:
    ResourceStateValueValuesEnum: Output only. The resource state of the
      multicast group consumer activation.

  Messages:
    LabelsValue: Optional. Labels as key-value pairs

  Fields:
    createTime: Output only. [Output only] The timestamp when the multicast
      group consumer activation was created.
    description: Optional. An optional text description of the multicast group
      consumer activation.
    labels: Optional. Labels as key-value pairs
    logConfig: Optional. Specifies the logging options for the activities
      performed related to the multicast group consumer activation. Defaults
      to false. If logging is enabled, logs are exported to Cloud Logging.
    multicastConsumerAssociation: Required. The resource name of the multicast
      consumer association that is in the same zone as this multicast group
      consumer activation. Use the following format:
      `projects/*/locations/*/multicastConsumerAssociations/*`.
    multicastGroup: Required. The resource name of the multicast group created
      by the admin in the same zone as this multicast group consumer
      activation. Use the following format: //
      `projects/*/locations/*/multicastGroups/*`.
    multicastGroupRangeActivation: Required. The resource name of the
      multicast group range activation created by the admin in the same zone
      as this multicast group consumer activation. Use the following format:
      // `projects/*/locations/*/multicastGroupRangeActivations/*`.
    name: Identifier. The resource name of the multicast group consumer
      activation. Use the following format:
      `projects/*/locations/*/multicastGroupConsumerActivations/*`.
    resourceState: Output only. The resource state of the multicast group
      consumer activation.
    uniqueId: Output only. [Output only] The Google-generated UUID for the
      resource. This value is unique across all multicast group consumer
      activation resources. If a group consumer activation is deleted and
      another with the same name is created, the new group consumer activation
      is assigned a different unique_id.
    updateTime: Output only. [Output only] The timestamp when the multicast
      group consumer activation was most recently updated.
  """

  class ResourceStateValueValuesEnum(_messages.Enum):
    r"""Output only. The resource state of the multicast group consumer
    activation.

    Values:
      CONSUMER_RESOURCE_STATE_UNSPECIFIED: The consumer resource state is not
        specified.
      ACTIVE: The consumer resource state is active.
      OBSOLETE: The associated admin resource has been deleted. The consumer
        resource state becomes obsolete.
    """
    CONSUMER_RESOURCE_STATE_UNSPECIFIED = 0
    ACTIVE = 1
    OBSOLETE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key-value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  logConfig = _messages.MessageField('MulticastLogConfig', 4)
  multicastConsumerAssociation = _messages.StringField(5)
  multicastGroup = _messages.StringField(6)
  multicastGroupRangeActivation = _messages.StringField(7)
  name = _messages.StringField(8)
  resourceState = _messages.EnumField('ResourceStateValueValuesEnum', 9)
  uniqueId = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class MulticastGroupProducerActivation(_messages.Message):
  r"""Multicast group producer activation resource.

  Messages:
    LabelsValue: Optional. Labels as key-value pairs

  Fields:
    createTime: Output only. [Output only] The timestamp when the multicast
      group producer activation was created.
    description: Optional. An optional text description of the multicast group
      producer activation.
    labels: Optional. Labels as key-value pairs
    multicastGroup: Required. The resource name of the multicast group created
      by the admin in the same zone as this multicast group producer
      activation. Use the following format: //
      `projects/*/locations/*/multicastGroups/*`.
    multicastGroupRangeActivation: Required. The resource name of the
      multicast group range activationcreated by the admin in the same zone as
      this multicast group producer activation. Use the following format: //
      `projects/*/locations/*/multicastGroupRangeActivations/*`.
    multicastProducerAssociation: Required. The resource name of the multicast
      producer association that is in the same zone as this multicast group
      producer activation. Use the following format:
      `projects/*/locations/*/multicastProducerAssociations/*`.
    name: Identifier. The resource name of the multicast group producer
      activation. Use the following format:
      `projects/*/locations/*/multicastGroupProducerActivations/*`.
    uniqueId: Output only. [Output only] The Google-generated UUID for the
      resource. This value is unique across all multicast group producer
      activation resources. If a group producer activation is deleted and
      another with the same name is created, the new group producer activation
      is assigned a different unique_id.
    updateTime: Output only. [Output only] The timestamp when the multicast
      group producer activation was most recently updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key-value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  multicastGroup = _messages.StringField(4)
  multicastGroupRangeActivation = _messages.StringField(5)
  multicastProducerAssociation = _messages.StringField(6)
  name = _messages.StringField(7)
  uniqueId = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class MulticastGroupRange(_messages.Message):
  r"""Multicast group range resource.

  Enums:
    DistributionScopeValueValuesEnum: Optional. Multicast group range's
      distribution scope. Intra-zone or intra-region cross-zone is supported,
      with default value being intra-region. Cross region distribution is not
      supported.

  Messages:
    LabelsValue: Optional. Labels as key-value pairs.

  Fields:
    consumerAcceptList: Optional. A list of consumer projects that are allowed
      to subscribe to the multicast IP addresses within the range defined by
      this MulticastGroupRange. The project can be specified using its project
      ID or project number. If left empty, then all consumer projects are
      allowed (unless require_explicit_accept is set to true) once they have
      VPC networks associated to the multicast domain. The current max length
      of the accept list is 100.
    createTime: Output only. [Output only] The timestamp when the multicast
      group range was created.
    description: Optional. An optional text description of the multicast group
      range.
    distributionScope: Optional. Multicast group range's distribution scope.
      Intra-zone or intra-region cross-zone is supported, with default value
      being intra-region. Cross region distribution is not supported.
    ipCidrRange: Output only. [Output only] The multicast group IP address
      range.
    labels: Optional. Labels as key-value pairs.
    logConfig: Optional. Specifies the logging options for the activities
      performed related to all the multicast group range activations
      associated with the multicast group range. Defaults to false. If logging
      is enabled, logs are exported to Cloud Logging.
    multicastDomain: Required. The resource name of the multicast domain in
      which to create this multicast group range. Use the following format:
      `projects/*/locations/global/multicastDomains/*`.
    name: Identifier. The resource name of the multicast group range. Use the
      following format: `projects/*/locations/global/multicastGroupRanges/*`.
    requireExplicitAccept: Optional. Whether an empty consumer_accept_list
      will deny all consumer projects.
    reservedInternalRange: Required. The resource name of the internal range
      reserved for this multicast group range. The internal range must be a
      Class D address (********* to ***************) and have a prefix length
      >= 23. Use the following format:
      `projects/*/locations/global/internalRanges/*`.
    uniqueId: Output only. [Output only] The Google-generated UUID for the
      resource. This value is unique across all multicast group range
      resources. If a group range is deleted and another with the same name is
      created, the new group range is assigned a different unique_id.
    updateTime: Output only. [Output only] The timestamp when the multicast
      group range was most recently updated.
  """

  class DistributionScopeValueValuesEnum(_messages.Enum):
    r"""Optional. Multicast group range's distribution scope. Intra-zone or
    intra-region cross-zone is supported, with default value being intra-
    region. Cross region distribution is not supported.

    Values:
      DISTRIBUTION_SCOPE_UNSPECIFIED: Unspecified value means no restriction,
        hence the broadest scope supported by the system applies.
      INTRA_ZONE: Multicast traffic is distributed from source to only
        subscribers in the same zone.
      INTRA_REGION: Multicast traffic is distributed from source to
        subscribers in any zone in the same region.
    """
    DISTRIBUTION_SCOPE_UNSPECIFIED = 0
    INTRA_ZONE = 1
    INTRA_REGION = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key-value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consumerAcceptList = _messages.StringField(1, repeated=True)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  distributionScope = _messages.EnumField('DistributionScopeValueValuesEnum', 4)
  ipCidrRange = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  logConfig = _messages.MessageField('MulticastLogConfig', 7)
  multicastDomain = _messages.StringField(8)
  name = _messages.StringField(9)
  requireExplicitAccept = _messages.BooleanField(10)
  reservedInternalRange = _messages.StringField(11)
  uniqueId = _messages.StringField(12)
  updateTime = _messages.StringField(13)


class MulticastGroupRangeActivation(_messages.Message):
  r"""Multicast group range activation resource.

  Messages:
    LabelsValue: Optional. Labels as key-value pairs.

  Fields:
    createTime: Output only. [Output only] The timestamp when the multicast
      group range activation was created.
    description: Optional. An optional text description of the multicast group
      range activation.
    ipCidrRange: Output only. [Output only] The multicast group IP address
      range.
    labels: Optional. Labels as key-value pairs.
    logConfig: Optional. Specifies the logging options for the activities
      performed regarding the multicast group range activation. Defaults to
      false. If not specified, inherit the logging options from the global
      multicast group range resource. If logging is enabled, logs are exported
      to Cloud Logging. If the log config is explicitly set for the multicast
      group range activation, regardless if it is enabled or not, the value
      overrides what is configured by the multicast group range resource.
    multicastDomainActivation: Optional. The resource name of a multicast
      domain activation that is in the same zone as this multicast group. Use
      the following format:
      `projects/*/locations/*/multicastDomainActivations/*`
    multicastGroupConsumerActivations: Output only. The resource names of
      associated multicast group consumer activations. Use the following
      format: `projects/*/locations/*/multicastGroupConsumerActivations/*`.
    multicastGroupRange: Optional. The resource name of the global multicast
      group range for the group. Use the following format:
      `projects/*/locations/global/multicastGroupRanges/*`
    name: Identifier. The resource name of the multicast group range
      activation. Use the following format:
      `projects/*/locations/*/multicastGroupRangeActivations/*`.
    uniqueId: Output only. [Output only] The Google-generated UUID for the
      resource. This value is unique across all multicast group resources. If
      a group is deleted and another with the same name is created, the new
      group is assigned a different unique_id.
    updateTime: Output only. [Output only] The timestamp when the multicast
      group range activation was most recently updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key-value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  ipCidrRange = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  logConfig = _messages.MessageField('MulticastLogConfig', 5)
  multicastDomainActivation = _messages.StringField(6)
  multicastGroupConsumerActivations = _messages.StringField(7, repeated=True)
  multicastGroupRange = _messages.StringField(8)
  name = _messages.StringField(9)
  uniqueId = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class MulticastLogConfig(_messages.Message):
  r"""The logging configuration.

  Fields:
    enabled: Optional. Whether to enable logging or not.
  """

  enabled = _messages.BooleanField(1)


class MulticastProducerAssociation(_messages.Message):
  r"""Multicast producer association resource.

  Messages:
    LabelsValue: Optional. Labels as key-value pairs

  Fields:
    createTime: Output only. [Output only] The timestamp when the multicast
      producer association was created.
    description: Optional. An optional text description of the multicast
      producer association.
    labels: Optional. Labels as key-value pairs
    multicastDomainActivation: Optional. The resource name of the multicast
      domain activation that is in the same zone as this multicast producer
      association. Use the following format: //
      `projects/*/locations/*/multicastProducerAssociations/*`.
    name: Identifier. The resource name of the multicast producer association.
      Use the following format:
      `projects/*/locations/*/multicastProducerAssociations/*`.
    network: Required. The resource name of the multicast producer VPC
      network. Use following format:
      `projects/{project}/locations/global/networks/{network}`.
    uniqueId: Output only. [Output only] The Google-generated UUID for the
      resource. This value is unique across all multicast producer association
      resources. If a producer association is deleted and another with the
      same name is created, the new producer association is assigned a
      different unique_id.
    updateTime: Output only. [Output only] The timestamp when the Multicast
      Producer Association was most recently updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key-value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  multicastDomainActivation = _messages.StringField(4)
  name = _messages.StringField(5)
  network = _messages.StringField(6)
  uniqueId = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class NetworkservicesProjectsLocationsAuthzExtensionsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsAuthzExtensionsCreateRequest object.

  Fields:
    authzExtension: A AuthzExtension resource to be passed as the request
      body.
    authzExtensionId: Required. User-provided ID of the `AuthzExtension`
      resource to be created.
    parent: Required. The parent resource of the `AuthzExtension` resource.
      Must be in the format `projects/{project}/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      can ignore the request if it has already been completed. The server
      guarantees that for 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server ignores the second request This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  authzExtension = _messages.MessageField('AuthzExtension', 1)
  authzExtensionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsAuthzExtensionsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsAuthzExtensionsDeleteRequest object.

  Fields:
    name: Required. The name of the `AuthzExtension` resource to delete. Must
      be in the format `projects/{project}/locations/{location}/authzExtension
      s/{authz_extension}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      can ignore the request if it has already been completed. The server
      guarantees that for 60 minutes after the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server ignores the second request This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsAuthzExtensionsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsAuthzExtensionsGetRequest object.

  Fields:
    name: Required. A name of the `AuthzExtension` resource to get. Must be in
      the format `projects/{project}/locations/{location}/authzExtensions/{aut
      hz_extension}`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsAuthzExtensionsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsAuthzExtensionsListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint about how to order the results.
    pageSize: Optional. Requested page size. The server might return fewer
      items than requested. If unspecified, the server picks an appropriate
      default.
    pageToken: Optional. A token identifying a page of results that the server
      returns.
    parent: Required. The project and location from which the `AuthzExtension`
      resources are listed. These values are specified in the following
      format: `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsAuthzExtensionsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsAuthzExtensionsPatchRequest object.

  Fields:
    authzExtension: A AuthzExtension resource to be passed as the request
      body.
    name: Required. Identifier. Name of the `AuthzExtension` resource in the
      following format: `projects/{project}/locations/{location}/authzExtensio
      ns/{authz_extension}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      can ignore the request if it has already been completed. The server
      guarantees that for 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server ignores the second request This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Used to specify the fields to be overwritten in the
      `AuthzExtension` resource by the update. The fields specified in the
      `update_mask` are relative to the resource, not the full request. A
      field is overwritten if it is in the mask. If the user does not specify
      a mask, then all fields are overwritten.
  """

  authzExtension = _messages.MessageField('AuthzExtension', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsEdgeCacheKeysetsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheKeysetsCreateRequest object.

  Fields:
    edgeCacheKeyset: A EdgeCacheKeyset resource to be passed as the request
      body.
    edgeCacheKeysetId: Required. The short name of the EdgeCacheKeyset
      resource to create, such as `MyEdgeCacheKeyset`.
    parent: Required. The parent resource of the EdgeCacheKeyset resource.
      Must be in the format `projects/*/locations/global`.
  """

  edgeCacheKeyset = _messages.MessageField('EdgeCacheKeyset', 1)
  edgeCacheKeysetId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsEdgeCacheKeysetsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheKeysetsDeleteRequest object.

  Fields:
    name: Required. The name of the EdgeCacheKeyset resource to delete. Must
      be in the format `projects/*/locations/global/edgeCacheKeysets/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsEdgeCacheKeysetsGetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheKeysetsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkservicesProjectsLocationsEdgeCacheKeysetsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheKeysetsGetRequest object.

  Fields:
    name: Required. The name of the EdgeCacheKeyset resource to get. Must be
      in the format `projects/*/locations/global/edgeCacheKeysets/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsEdgeCacheKeysetsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheKeysetsListRequest object.

  Fields:
    pageSize: The maximum number of EdgeCacheKeyset resources to return per
      call.
    pageToken: The value returned by the last ListEdgeCacheKeysetsResponse
      resource. Indicates that this is a continuation of a previous
      `ListEdgeCacheKeysets` call, and that the system can return the next
      page of data.
    parent: Required. The project and location to list EdgeCacheKeyset
      resources from, specified in the format `projects/*/locations/global`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsEdgeCacheKeysetsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheKeysetsPatchRequest object.

  Fields:
    edgeCacheKeyset: A EdgeCacheKeyset resource to be passed as the request
      body.
    name: Required. The name of the resource as provided by the client when
      the resource is created. The name must be 1-64 characters long, and
      match the regular expression `[a-zA-Z]([a-zA-Z0-9_-])*` which means the
      first character must be a letter, and all following characters must be a
      dash, an underscore, a letter, or a digit.
    updateMask: Optional. `FieldMask` is used to specify the fields to
      overwrite in the EdgeCacheKeyset resource by the update. The fields
      specified in the `update_mask` are relative to the resource, not the
      full request. A field is overwritten if it is in the mask. If the user
      does not provide a mask then all fields are overwritten.
  """

  edgeCacheKeyset = _messages.MessageField('EdgeCacheKeyset', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsEdgeCacheKeysetsSetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheKeysetsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NetworkservicesProjectsLocationsEdgeCacheKeysetsTestIamPermissionsRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsEdgeCacheKeysetsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NetworkservicesProjectsLocationsEdgeCacheOriginsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheOriginsCreateRequest object.

  Fields:
    edgeCacheOrigin: A EdgeCacheOrigin resource to be passed as the request
      body.
    edgeCacheOriginId: Required. The short name of the EdgeCacheOrigin
      resource to create, such as `MyEdgeCacheOrigin`.
    parent: Required. The parent resource of the EdgeCacheOrigin resource.
      Must be in the format `projects/*/locations/global`.
  """

  edgeCacheOrigin = _messages.MessageField('EdgeCacheOrigin', 1)
  edgeCacheOriginId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsEdgeCacheOriginsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheOriginsDeleteRequest object.

  Fields:
    name: Required. The name of the EdgeCacheOrigin to delete. Must be in the
      format `projects/*/locations/global/edgeCacheOrigins/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsEdgeCacheOriginsGetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheOriginsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkservicesProjectsLocationsEdgeCacheOriginsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheOriginsGetRequest object.

  Fields:
    name: Required. The name of the EdgeCacheOrigin resource to get. Must be
      in the format `projects/*/locations/global/edgeCacheOrigins/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsEdgeCacheOriginsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheOriginsListRequest object.

  Fields:
    pageSize: The maximum number of EdgeCacheOrigin resources to return per
      call.
    pageToken: The value returned by the last `ListEdgeCacheOriginsResponse`
      resource. Indicates that this is a continuation of a previous
      `ListEdgeCacheOrigins` call, and that the system can return the next
      page of data.
    parent: Required. The project and location to list EdgeCacheOrigin
      resources from, specified in the format `projects/*/locations/global`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsEdgeCacheOriginsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheOriginsPatchRequest object.

  Fields:
    edgeCacheOrigin: A EdgeCacheOrigin resource to be passed as the request
      body.
    name: Required. The name of the resource as provided by the client when
      the resource is created. The name must be 1-64 characters long, and
      match the regular expression `[a-zA-Z]([a-zA-Z0-9_-])*`, which means
      that the first character must be a letter, and all following characters
      must be a dash, an underscore, a letter, or a digit.
    updateMask: Optional. `FieldMask` is used to specify the fields to
      overwrite in the EdgeCacheOrigin resource by the update. The fields
      specified in `update_mask` are relative to the resource, not the full
      request. A field is overwritten if it is in the mask. If the user does
      not provide a mask value, then all fields are overwritten.
  """

  edgeCacheOrigin = _messages.MessageField('EdgeCacheOrigin', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsEdgeCacheOriginsSetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheOriginsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NetworkservicesProjectsLocationsEdgeCacheOriginsTestIamPermissionsRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsEdgeCacheOriginsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NetworkservicesProjectsLocationsEdgeCacheServicesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheServicesCreateRequest object.

  Fields:
    edgeCacheService: A EdgeCacheService resource to be passed as the request
      body.
    edgeCacheServiceId: Required. The short name of the EdgeCacheService
      resource create, such as `MyEdgeService`.
    parent: Required. The parent resource of the EdgeCacheService resource.
      Must be in the format `projects/*/locations/global`.
  """

  edgeCacheService = _messages.MessageField('EdgeCacheService', 1)
  edgeCacheServiceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsEdgeCacheServicesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheServicesDeleteRequest object.

  Fields:
    name: Required. The name of the EdgeCacheService resource to delete. Must
      be in the format `projects/*/locations/global/edgeCacheServices/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsEdgeCacheServicesGetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheServicesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkservicesProjectsLocationsEdgeCacheServicesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheServicesGetRequest object.

  Fields:
    name: Required. The name of the EdgeCacheService resource to get. Must be
      in the format `projects/*/locations/global/edgeCacheServices/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsEdgeCacheServicesInvalidateCacheRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsEdgeCacheServicesInvalidateCacheRequest
  object.

  Fields:
    edgeCacheService: Required. The name of the EdgeCacheService resource to
      apply the invalidation request to. Must be in the format
      `projects/*/locations/global/edgeCacheServices/*`.
    invalidateCacheRequest: A InvalidateCacheRequest resource to be passed as
      the request body.
  """

  edgeCacheService = _messages.StringField(1, required=True)
  invalidateCacheRequest = _messages.MessageField('InvalidateCacheRequest', 2)


class NetworkservicesProjectsLocationsEdgeCacheServicesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheServicesListRequest object.

  Fields:
    pageSize: The maximum number of EdgeCacheService resources to return per
      call.
    pageToken: The value returned by the last `list` response. Indicates that
      this is a continuation of a previous `list` call, and that the system
      can return the next page of data.
    parent: Required. The project and location to list EdgeCacheService
      resources from, specified in the format `projects/*/locations/global`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsEdgeCacheServicesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheServicesPatchRequest object.

  Fields:
    edgeCacheService: A EdgeCacheService resource to be passed as the request
      body.
    name: Required. The name of the resource as provided by the client when
      the resource is created. The name must be 1-64 characters long, and
      match the regular expression `[a-zA-Z]([a-zA-Z0-9_-])*` which means the
      first character must be a letter, and all following characters must be a
      dash, an underscore, a letter, or a digit.
    updateMask: Optional. `FieldMask` is used to specify the fields to
      overwrite in the EdgeCacheService resource by the update. The fields
      specified in `update_mask` are relative to the resource, not the full
      request. A field is overwritten if it is in the mask. If a mask is not
      provided, then all fields are overwritten.
  """

  edgeCacheService = _messages.MessageField('EdgeCacheService', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsEdgeCacheServicesSetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEdgeCacheServicesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NetworkservicesProjectsLocationsEdgeCacheServicesTestIamPermissionsRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsEdgeCacheServicesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NetworkservicesProjectsLocationsEndpointPoliciesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEndpointPoliciesCreateRequest object.

  Fields:
    endpointPolicy: A EndpointPolicy resource to be passed as the request
      body.
    endpointPolicyId: Required. Short name of the EndpointPolicy resource to
      be created. E.g. "CustomECS".
    parent: Required. The parent resource of the EndpointPolicy. Must be in
      the format `projects/*/locations/global`.
  """

  endpointPolicy = _messages.MessageField('EndpointPolicy', 1)
  endpointPolicyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsEndpointPoliciesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEndpointPoliciesDeleteRequest object.

  Fields:
    name: Required. A name of the EndpointPolicy to delete. Must be in the
      format `projects/*/locations/global/endpointPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsEndpointPoliciesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEndpointPoliciesGetRequest object.

  Fields:
    name: Required. A name of the EndpointPolicy to get. Must be in the format
      `projects/*/locations/global/endpointPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsEndpointPoliciesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEndpointPoliciesListRequest object.

  Fields:
    pageSize: Maximum number of EndpointPolicies to return per call.
    pageToken: The value returned by the last `ListEndpointPoliciesResponse`
      Indicates that this is a continuation of a prior `ListEndpointPolicies`
      call, and that the system should return the next page of data.
    parent: Required. The project and location from which the EndpointPolicies
      should be listed, specified in the format `projects/*/locations/global`.
    returnPartialSuccess: Optional. If true, allow partial responses for
      multi-regional Aggregated List requests. Otherwise if one of the
      locations is down or unreachable, the Aggregated List request will fail.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  returnPartialSuccess = _messages.BooleanField(4)


class NetworkservicesProjectsLocationsEndpointPoliciesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEndpointPoliciesPatchRequest object.

  Fields:
    endpointPolicy: A EndpointPolicy resource to be passed as the request
      body.
    name: Identifier. Name of the EndpointPolicy resource. It matches pattern
      `projects/{project}/locations/global/endpointPolicies/{endpoint_policy}`
      .
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the EndpointPolicy resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  endpointPolicy = _messages.MessageField('EndpointPolicy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsGatewaysCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysCreateRequest object.

  Fields:
    gateway: A Gateway resource to be passed as the request body.
    gatewayId: Required. Short name of the Gateway resource to be created.
    parent: Required. The parent resource of the Gateway. Must be in the
      format `projects/*/locations/*`.
  """

  gateway = _messages.MessageField('Gateway', 1)
  gatewayId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsGatewaysDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysDeleteRequest object.

  Fields:
    name: Required. A name of the Gateway to delete. Must be in the format
      `projects/*/locations/*/gateways/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsGatewaysGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysGetRequest object.

  Fields:
    name: Required. A name of the Gateway to get. Must be in the format
      `projects/*/locations/*/gateways/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsGatewaysListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysListRequest object.

  Fields:
    pageSize: Maximum number of Gateways to return per call.
    pageToken: The value returned by the last `ListGatewaysResponse` Indicates
      that this is a continuation of a prior `ListGateways` call, and that the
      system should return the next page of data.
    parent: Required. The project and location from which the Gateways should
      be listed, specified in the format `projects/*/locations/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsGatewaysPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysPatchRequest object.

  Fields:
    gateway: A Gateway resource to be passed as the request body.
    name: Identifier. Name of the Gateway resource. It matches pattern
      `projects/*/locations/*/gateways/`.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Gateway resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  gateway = _messages.MessageField('Gateway', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsGatewaysRouteViewsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysRouteViewsGetRequest object.

  Fields:
    name: Required. Name of the GatewayRouteView resource. Formats: projects/{
      project_number}/locations/{location}/gateways/{gateway}/routeViews/{rout
      e_view}
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsGatewaysRouteViewsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysRouteViewsListRequest object.

  Fields:
    pageSize: Maximum number of GatewayRouteViews to return per call.
    pageToken: The value returned by the last `ListGatewayRouteViewsResponse`
      Indicates that this is a continuation of a prior `ListGatewayRouteViews`
      call, and that the system should return the next page of data.
    parent: Required. The Gateway to which a Route is associated. Formats:
      projects/{project_number}/locations/{location}/gateways/{gateway}
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsGrpcRoutesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGrpcRoutesCreateRequest object.

  Fields:
    grpcRoute: A GrpcRoute resource to be passed as the request body.
    grpcRouteId: Required. Short name of the GrpcRoute resource to be created.
    parent: Required. The parent resource of the GrpcRoute. Must be in the
      format `projects/*/locations/global`.
  """

  grpcRoute = _messages.MessageField('GrpcRoute', 1)
  grpcRouteId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsGrpcRoutesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGrpcRoutesDeleteRequest object.

  Fields:
    name: Required. A name of the GrpcRoute to delete. Must be in the format
      `projects/*/locations/global/grpcRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsGrpcRoutesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGrpcRoutesGetRequest object.

  Fields:
    name: Required. A name of the GrpcRoute to get. Must be in the format
      `projects/*/locations/global/grpcRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsGrpcRoutesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGrpcRoutesListRequest object.

  Fields:
    pageSize: Maximum number of GrpcRoutes to return per call.
    pageToken: The value returned by the last `ListGrpcRoutesResponse`
      Indicates that this is a continuation of a prior `ListGrpcRoutes` call,
      and that the system should return the next page of data.
    parent: Required. The project and location from which the GrpcRoutes
      should be listed, specified in the format `projects/*/locations/global`.
    returnPartialSuccess: Optional. If true, allow partial responses for
      multi-regional Aggregated List requests. Otherwise if one of the
      locations is down or unreachable, the Aggregated List request will fail.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  returnPartialSuccess = _messages.BooleanField(4)


class NetworkservicesProjectsLocationsGrpcRoutesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGrpcRoutesPatchRequest object.

  Fields:
    grpcRoute: A GrpcRoute resource to be passed as the request body.
    name: Identifier. Name of the GrpcRoute resource. It matches pattern
      `projects/*/locations/global/grpcRoutes/`
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the GrpcRoute resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  grpcRoute = _messages.MessageField('GrpcRoute', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsHttpRoutesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsHttpRoutesCreateRequest object.

  Fields:
    httpRoute: A HttpRoute resource to be passed as the request body.
    httpRouteId: Required. Short name of the HttpRoute resource to be created.
    parent: Required. The parent resource of the HttpRoute. Must be in the
      format `projects/*/locations/global`.
  """

  httpRoute = _messages.MessageField('HttpRoute', 1)
  httpRouteId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsHttpRoutesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsHttpRoutesDeleteRequest object.

  Fields:
    name: Required. A name of the HttpRoute to delete. Must be in the format
      `projects/*/locations/global/httpRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsHttpRoutesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsHttpRoutesGetRequest object.

  Fields:
    name: Required. A name of the HttpRoute to get. Must be in the format
      `projects/*/locations/global/httpRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsHttpRoutesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsHttpRoutesListRequest object.

  Fields:
    pageSize: Maximum number of HttpRoutes to return per call.
    pageToken: The value returned by the last `ListHttpRoutesResponse`
      Indicates that this is a continuation of a prior `ListHttpRoutes` call,
      and that the system should return the next page of data.
    parent: Required. The project and location from which the HttpRoutes
      should be listed, specified in the format `projects/*/locations/global`.
    returnPartialSuccess: Optional. If true, allow partial responses for
      multi-regional Aggregated List requests. Otherwise if one of the
      locations is down or unreachable, the Aggregated List request will fail.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  returnPartialSuccess = _messages.BooleanField(4)


class NetworkservicesProjectsLocationsHttpRoutesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsHttpRoutesPatchRequest object.

  Fields:
    httpRoute: A HttpRoute resource to be passed as the request body.
    name: Identifier. Name of the HttpRoute resource. It matches pattern
      `projects/*/locations/global/httpRoutes/http_route_name>`.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the HttpRoute resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  httpRoute = _messages.MessageField('HttpRoute', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsLbRouteExtensionsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsLbRouteExtensionsCreateRequest object.

  Fields:
    lbRouteExtension: A LbRouteExtension resource to be passed as the request
      body.
    lbRouteExtensionId: Required. User-provided ID of the `LbRouteExtension`
      resource to be created.
    parent: Required. The parent resource of the `LbRouteExtension` resource.
      Must be in the format `projects/{project}/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      can ignore the request if it has already been completed. The server
      guarantees that for 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server ignores the second request This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  lbRouteExtension = _messages.MessageField('LbRouteExtension', 1)
  lbRouteExtensionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsLbRouteExtensionsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsLbRouteExtensionsDeleteRequest object.

  Fields:
    name: Required. The name of the `LbRouteExtension` resource to delete.
      Must be in the format `projects/{project}/locations/{location}/lbRouteEx
      tensions/{lb_route_extension}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      can ignore the request if it has already been completed. The server
      guarantees that for 60 minutes after the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server ignores the second request This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsLbRouteExtensionsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsLbRouteExtensionsGetRequest object.

  Fields:
    name: Required. A name of the `LbRouteExtension` resource to get. Must be
      in the format `projects/{project}/locations/{location}/lbRouteExtensions
      /{lb_route_extension}`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsLbRouteExtensionsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsLbRouteExtensionsListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint about how to order the results.
    pageSize: Optional. Requested page size. The server might return fewer
      items than requested. If unspecified, the server picks an appropriate
      default.
    pageToken: Optional. A token identifying a page of results that the server
      returns.
    parent: Required. The project and location from which the
      `LbRouteExtension` resources are listed. These values are specified in
      the following format: `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsLbRouteExtensionsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsLbRouteExtensionsPatchRequest object.

  Fields:
    lbRouteExtension: A LbRouteExtension resource to be passed as the request
      body.
    name: Required. Identifier. Name of the `LbRouteExtension` resource in the
      following format: `projects/{project}/locations/{location}/lbRouteExtens
      ions/{lb_route_extension}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      can ignore the request if it has already been completed. The server
      guarantees that for 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server ignores the second request This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Used to specify the fields to be overwritten in the
      `LbRouteExtension` resource by the update. The fields specified in the
      `update_mask` are relative to the resource, not the full request. A
      field is overwritten if it is in the mask. If the user does not specify
      a mask, then all fields are overwritten.
  """

  lbRouteExtension = _messages.MessageField('LbRouteExtension', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsLbTrafficExtensionsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsLbTrafficExtensionsCreateRequest
  object.

  Fields:
    lbTrafficExtension: A LbTrafficExtension resource to be passed as the
      request body.
    lbTrafficExtensionId: Required. User-provided ID of the
      `LbTrafficExtension` resource to be created.
    parent: Required. The parent resource of the `LbTrafficExtension`
      resource. Must be in the format
      `projects/{project}/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      can ignore the request if it has already been completed. The server
      guarantees that for 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server ignores the second request This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  lbTrafficExtension = _messages.MessageField('LbTrafficExtension', 1)
  lbTrafficExtensionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsLbTrafficExtensionsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsLbTrafficExtensionsDeleteRequest
  object.

  Fields:
    name: Required. The name of the `LbTrafficExtension` resource to delete.
      Must be in the format `projects/{project}/locations/{location}/lbTraffic
      Extensions/{lb_traffic_extension}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      can ignore the request if it has already been completed. The server
      guarantees that for 60 minutes after the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server ignores the second request This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsLbTrafficExtensionsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsLbTrafficExtensionsGetRequest object.

  Fields:
    name: Required. A name of the `LbTrafficExtension` resource to get. Must
      be in the format `projects/{project}/locations/{location}/lbTrafficExten
      sions/{lb_traffic_extension}`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsLbTrafficExtensionsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsLbTrafficExtensionsListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint about how to order the results.
    pageSize: Optional. Requested page size. The server might return fewer
      items than requested. If unspecified, the server picks an appropriate
      default.
    pageToken: Optional. A token identifying a page of results that the server
      returns.
    parent: Required. The project and location from which the
      `LbTrafficExtension` resources are listed. These values are specified in
      the following format: `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsLbTrafficExtensionsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsLbTrafficExtensionsPatchRequest
  object.

  Fields:
    lbTrafficExtension: A LbTrafficExtension resource to be passed as the
      request body.
    name: Required. Identifier. Name of the `LbTrafficExtension` resource in
      the following format: `projects/{project}/locations/{location}/lbTraffic
      Extensions/{lb_traffic_extension}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      can ignore the request if it has already been completed. The server
      guarantees that for 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server ignores the second request This prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Used to specify the fields to be overwritten in the
      `LbTrafficExtension` resource by the update. The fields specified in the
      `update_mask` are relative to the resource, not the full request. A
      field is overwritten if it is in the mask. If the user does not specify
      a mask, then all fields are overwritten.
  """

  lbTrafficExtension = _messages.MessageField('LbTrafficExtension', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class NetworkservicesProjectsLocationsMeshesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesCreateRequest object.

  Fields:
    mesh: A Mesh resource to be passed as the request body.
    meshId: Required. Short name of the Mesh resource to be created.
    parent: Required. The parent resource of the Mesh. Must be in the format
      `projects/*/locations/global`.
  """

  mesh = _messages.MessageField('Mesh', 1)
  meshId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsMeshesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesDeleteRequest object.

  Fields:
    name: Required. A name of the Mesh to delete. Must be in the format
      `projects/*/locations/global/meshes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMeshesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesGetRequest object.

  Fields:
    name: Required. A name of the Mesh to get. Must be in the format
      `projects/*/locations/global/meshes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMeshesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesListRequest object.

  Fields:
    pageSize: Maximum number of Meshes to return per call.
    pageToken: The value returned by the last `ListMeshesResponse` Indicates
      that this is a continuation of a prior `ListMeshes` call, and that the
      system should return the next page of data.
    parent: Required. The project and location from which the Meshes should be
      listed, specified in the format `projects/*/locations/global`.
    returnPartialSuccess: Optional. If true, allow partial responses for
      multi-regional Aggregated List requests. Otherwise if one of the
      locations is down or unreachable, the Aggregated List request will fail.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  returnPartialSuccess = _messages.BooleanField(4)


class NetworkservicesProjectsLocationsMeshesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesPatchRequest object.

  Fields:
    mesh: A Mesh resource to be passed as the request body.
    name: Identifier. Name of the Mesh resource. It matches pattern
      `projects/*/locations/global/meshes/`.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Mesh resource by the update. The fields specified in
      the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  mesh = _messages.MessageField('Mesh', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsMeshesRouteViewsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesRouteViewsGetRequest object.

  Fields:
    name: Required. Name of the MeshRouteView resource. Format: projects/{proj
      ect_number}/locations/{location}/meshes/{mesh}/routeViews/{route_view}
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMeshesRouteViewsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesRouteViewsListRequest object.

  Fields:
    pageSize: Maximum number of MeshRouteViews to return per call.
    pageToken: The value returned by the last `ListMeshRouteViewsResponse`
      Indicates that this is a continuation of a prior `ListMeshRouteViews`
      call, and that the system should return the next page of data.
    parent: Required. The Mesh to which a Route is associated. Format:
      projects/{project_number}/locations/{location}/meshes/{mesh}
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsMulticastConsumerAssociationsCreateRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastConsumerAssociationsCreateRequest
  object.

  Fields:
    multicastConsumerAssociation: A MulticastConsumerAssociation resource to
      be passed as the request body.
    multicastConsumerAssociationId: Required. A unique name for the multicast
      consumer association. The name is restricted to letters, numbers, and
      hyphen, with the first character a letter, and the last a letter or a
      number. The name must not exceed 48 characters.
    parent: Required. The parent resource of the multicast consumer
      association. Use the following format: `projects/*/locations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  multicastConsumerAssociation = _messages.MessageField('MulticastConsumerAssociation', 1)
  multicastConsumerAssociationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastConsumerAssociationsDeleteRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastConsumerAssociationsDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the multicast consumer association to
      delete. Use the following format:
      `projects/*/locations/*/multicastConsumerAssociations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsMulticastConsumerAssociationsGetRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastConsumerAssociationsGetRequest
  object.

  Fields:
    name: Required. The resource name of the multicast consumer association to
      get. Use the following format:
      `projects/*/locations/*/multicastConsumerAssociations/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMulticastConsumerAssociationsListRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastConsumerAssociationsListRequest
  object.

  Fields:
    filter: Optional. A filter expression that filters the resources listed in
      the response. The expression must be of the form ` ` where operators:
      `<`, `>`, `<=`, `>=`, `!=`, `=`, `:` are supported (colon `:` represents
      a HAS operator which is roughly synonymous with equality). can refer to
      a proto or JSON field, or a synthetic field. Field names can be
      camelCase or snake_case. Examples: * Filter by name: name =
      "RESOURCE_NAME" * Filter by labels: * Resources that have a key named
      `foo` labels.foo:* * Resources that have a key named `foo` whose value
      is `bar` labels.foo = bar
    orderBy: Optional. A field used to sort the results by a certain order.
    pageSize: Optional. The maximum number of multicast consumer associations
      to return per call.
    pageToken: Optional. A page token from an earlier query, as returned in
      `next_page_token`.
    parent: Required. The parent resource for which to list multicast consumer
      associations. Use the following format: `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsMulticastConsumerAssociationsPatchRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastConsumerAssociationsPatchRequest
  object.

  Fields:
    multicastConsumerAssociation: A MulticastConsumerAssociation resource to
      be passed as the request body.
    name: Identifier. The resource name of the multicast consumer association.
      Use the following format:
      `projects/*/locations/*/multicastConsumerAssociations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. The field mask specifies the fields to overwrite in
      the MulticastConsumerAssociation resource by the update. The fields
      specified in the `update_mask` are relative to the resource, not the
      full request. If a field is in the mask, then it is overwritten. If the
      you do not provide a mask, then all fields are overwritten
  """

  multicastConsumerAssociation = _messages.MessageField('MulticastConsumerAssociation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastDomainActivationsCreateRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastDomainActivationsCreateRequest
  object.

  Fields:
    multicastDomainActivation: A MulticastDomainActivation resource to be
      passed as the request body.
    multicastDomainActivationId: Required. A unique name for the multicast
      domain activation. The name is restricted to letters, numbers, and
      hyphen, with the first character a letter, and the last a letter or a
      number. The name must not exceed 48 characters.
    parent: Required. The parent resource of the multicast domain activation.
      Use the following format: `projects/*/locations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  multicastDomainActivation = _messages.MessageField('MulticastDomainActivation', 1)
  multicastDomainActivationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastDomainActivationsDeleteRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastDomainActivationsDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the multicast domain activation to
      delete. Use the following format:
      `projects/*/locations/*/multicastDomainActivations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsMulticastDomainActivationsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainActivationsGetRequest
  object.

  Fields:
    name: Required. The resource name of the multicast domain activation to
      get. Use the following format:
      `projects/*/locations/*/multicastDomainActivations/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMulticastDomainActivationsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainActivationsListRequest
  object.

  Fields:
    filter: Optional. A filter expression that filters the resources listed in
      the response. The expression must be of the form ` ` where operators:
      `<`, `>`, `<=`, `>=`, `!=`, `=`, `:` are supported (colon `:` represents
      a HAS operator which is roughly synonymous with equality). can refer to
      a proto or JSON field, or a synthetic field. Field names can be
      camelCase or snake_case. Examples: * Filter by name: name =
      "RESOURCE_NAME" * Filter by labels: * Resources that have a key named
      `foo` labels.foo:* * Resources that have a key named `foo` whose value
      is `bar` labels.foo = bar
    orderBy: Optional. A field used to sort the results by a certain order.
    pageSize: Optional. The maximum number of multicast domain activations to
      return per call.
    pageToken: Optional. A page token from an earlier query, as returned in
      `next_page_token`.
    parent: Required. The parent resource for which to list multicast domain
      activations. Use the following format: `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsMulticastDomainActivationsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainActivationsPatchRequest
  object.

  Fields:
    multicastDomainActivation: A MulticastDomainActivation resource to be
      passed as the request body.
    name: Identifier. The resource name of the multicast domain activation.
      Use the following format:
      `projects/*/locations/*/multicastDomainActivations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. The field mask specifies the fields to overwrite in
      the MulticastDomainActivation resource by the update. The fields
      specified in the `update_mask` are relative to the resource, not the
      full request. If a field is in the mask, then it is overwritten. If the
      you do not provide a mask, then all fields are overwritten
  """

  multicastDomainActivation = _messages.MessageField('MulticastDomainActivation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastDomainGroupsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainGroupsCreateRequest
  object.

  Fields:
    multicastDomainGroup: A MulticastDomainGroup resource to be passed as the
      request body.
    multicastDomainGroupId: Required. A unique name for the multicast domain
      group. The name is restricted to letters, numbers, and hyphen, with the
      first character a letter, and the last a letter or a number. The name
      must not exceed 48 characters.
    parent: Required. The parent resource of the multicast domain group. Use
      the following format: `projects/*/locations/global`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  multicastDomainGroup = _messages.MessageField('MulticastDomainGroup', 1)
  multicastDomainGroupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastDomainGroupsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainGroupsDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the multicast domain to delete. Use
      the following format: `projects/*/locations/global/multicastDomains/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsMulticastDomainGroupsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainGroupsGetRequest
  object.

  Fields:
    name: Required. The resource name of the multicast domain group to get.
      Use the following format:
      `projects/*/locations/global/multicastDomainGroups/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMulticastDomainGroupsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainGroupsListRequest
  object.

  Fields:
    filter: Optional. A filter expression that filters the resources listed in
      the response. The expression must be of the form ` ` where operators:
      `<`, `>`, `<=`, `>=`, `!=`, `=`, `:` are supported (colon `:` represents
      a HAS operator which is roughly synonymous with equality). can refer to
      a proto or JSON field, or a synthetic field. Field names can be
      camelCase or snake_case. Examples: * Filter by name: name =
      "RESOURCE_NAME" * Filter by labels: * Resources that have a key named
      `foo` labels.foo:* * Resources that have a key named `foo` whose value
      is `bar` labels.foo = bar
    orderBy: Optional. A field used to sort the results by a certain order.
    pageSize: Optional. The maximum number of multicast domain groups to
      return per call.
    pageToken: Optional. A page token from an earlier query, as returned in
      `next_page_token`.
    parent: Required. The parent resource for which to list multicast domain
      groups. Use the following format: `projects/*/locations/global`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsMulticastDomainGroupsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainGroupsPatchRequest
  object.

  Fields:
    multicastDomainGroup: A MulticastDomainGroup resource to be passed as the
      request body.
    name: Identifier. The resource name of the multicast domain group. Use the
      following format: `projects/*/locations/global/multicastDomainGroups/*`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. The field mask specifies the fields to overwrite in
      the multicast domain resource by the update. The fields specified in the
      `update_mask` are relative to the resource, not the full request. If a
      field is in the mask, then it is overwritten. If the you do not provide
      a mask, then all fields are overwritten.
  """

  multicastDomainGroup = _messages.MessageField('MulticastDomainGroup', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastDomainsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainsCreateRequest object.

  Fields:
    multicastDomain: A MulticastDomain resource to be passed as the request
      body.
    multicastDomainId: Required. A unique name for the multicast domain. The
      name is restricted to letters, numbers, and hyphen, with the first
      character a letter, and the last a letter or a number. The name must not
      exceed 48 characters.
    parent: Required. The parent resource of the multicast domain. Use the
      following format: `projects/*/locations/global`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  multicastDomain = _messages.MessageField('MulticastDomain', 1)
  multicastDomainId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastDomainsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainsDeleteRequest object.

  Fields:
    name: Required. The resource name of the multicast domain to delete. Use
      the following format: `projects/*/locations/global/multicastDomains/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsMulticastDomainsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainsGetRequest object.

  Fields:
    name: Required. The resource name of the multicast domain to get. Use the
      following format: `projects/*/locations/global/multicastDomains/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMulticastDomainsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainsListRequest object.

  Fields:
    filter: Optional. A filter expression that filters the resources listed in
      the response. The expression must be of the form ` ` where operators:
      `<`, `>`, `<=`, `>=`, `!=`, `=`, `:` are supported (colon `:` represents
      a HAS operator which is roughly synonymous with equality). can refer to
      a proto or JSON field, or a synthetic field. Field names can be
      camelCase or snake_case. Examples: * Filter by name: name =
      "RESOURCE_NAME" * Filter by labels: * Resources that have a key named
      `foo` labels.foo:* * Resources that have a key named `foo` whose value
      is `bar` labels.foo = bar
    orderBy: Optional. A field used to sort the results by a certain order.
    pageSize: Optional. The maximum number of multicast domains to return per
      call.
    pageToken: Optional. A page token from an earlier query, as returned in
      `next_page_token`.
    parent: Required. The parent resource for which to list multicast domains.
      Use the following format: `projects/*/locations/global`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsMulticastDomainsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainsPatchRequest object.

  Fields:
    multicastDomain: A MulticastDomain resource to be passed as the request
      body.
    name: Identifier. The resource name of the multicast domain. Use the
      following format: `projects/*/locations/global/multicastDomains/*`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. The field mask specifies the fields to overwrite in
      the multicast domain resource by the update. The fields specified in the
      `update_mask` are relative to the resource, not the full request. If a
      field is in the mask, then it is overwritten. If the you do not provide
      a mask, then all fields are overwritten.
  """

  multicastDomain = _messages.MessageField('MulticastDomain', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsCreat
  eRequest object.

  Fields:
    multicastGroupConsumerActivation: A MulticastGroupConsumerActivation
      resource to be passed as the request body.
    multicastGroupConsumerActivationId: Required. A unique name for the
      multicast group consumer activation. The name is restricted to letters,
      numbers, and hyphen, with the first character a letter, and the last a
      letter or a number. The name must not exceed 48 characters.
    parent: Required. The parent resource of the multicast group consumer
      activation. Use the following format: `projects/*/locations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  multicastGroupConsumerActivation = _messages.MessageField('MulticastGroupConsumerActivation', 1)
  multicastGroupConsumerActivationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsDelet
  eRequest object.

  Fields:
    name: Required. The resource name of the multicast group consumer
      activation to delete. Use the following format:
      `projects/*/locations/*/multicastGroupConsumerActivations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsGetRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsGetRequest
  object.

  Fields:
    name: Required. The resource name of the multicast group consumer
      activation to get. Use the following format:
      `projects/*/locations/*/multicastGroupConsumerActivations/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsListRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsListRequest
  object.

  Fields:
    filter: Optional. A filter expression that filters the resources listed in
      the response. The expression must be of the form ` ` where operators:
      `<`, `>`, `<=`, `>=`, `!=`, `=`, `:` are supported (colon `:` represents
      a HAS operator which is roughly synonymous with equality). can refer to
      a proto or JSON field, or a synthetic field. Field names can be
      camelCase or snake_case. Examples: * Filter by name: name =
      "RESOURCE_NAME" * Filter by labels: * Resources that have a key named
      `foo` labels.foo:* * Resources that have a key named `foo` whose value
      is `bar` labels.foo = bar
    orderBy: Optional. A field used to sort the results by a certain order.
    pageSize: Optional. The maximum number of multicast group consumer
      activations to return per call.
    pageToken: Optional. A page token from an earlier query, as returned in
      `next_page_token`.
    parent: Required. The parent resource for which to list multicast group
      consumer activations. Use the following format:
      `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsPatch
  Request object.

  Fields:
    multicastGroupConsumerActivation: A MulticastGroupConsumerActivation
      resource to be passed as the request body.
    name: Identifier. The resource name of the multicast group consumer
      activation. Use the following format:
      `projects/*/locations/*/multicastGroupConsumerActivations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. The field mask specifies the fields to overwrite in
      the MulticastGroupConsumerActivationresource by the update. The fields
      specified in the `update_mask` are relative to the resource, not the
      full request. If a field is in the mask, then it is overwritten. If the
      you do not provide a mask, then all fields are overwritten
  """

  multicastGroupConsumerActivation = _messages.MessageField('MulticastGroupConsumerActivation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastGroupProducerActivationsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupProducerActivationsCreat
  eRequest object.

  Fields:
    multicastGroupProducerActivation: A MulticastGroupProducerActivation
      resource to be passed as the request body.
    multicastGroupProducerActivationId: Required. A unique name for the
      multicast group producer activation. The name is restricted to letters,
      numbers, and hyphen, with the first character a letter, and the last a
      letter or a number. The name must not exceed 48 characters.
    parent: Required. The parent resource of the multicast group producer
      activation. Use the following format: `projects/*/locations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  multicastGroupProducerActivation = _messages.MessageField('MulticastGroupProducerActivation', 1)
  multicastGroupProducerActivationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastGroupProducerActivationsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupProducerActivationsDelet
  eRequest object.

  Fields:
    name: Required. The resource name of the multicast group producer
      activation to delete. Use the following format:
      `projects/*/locations/*/multicastGroupProducerActivations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsMulticastGroupProducerActivationsGetRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastGroupProducerActivationsGetRequest
  object.

  Fields:
    name: Required. The resource name of the multicast group producer
      activation to get. Use the following format:
      `projects/*/locations/*/multicastGroupProducerActivations/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMulticastGroupProducerActivationsListRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastGroupProducerActivationsListRequest
  object.

  Fields:
    filter: Optional. A filter expression that filters the resources listed in
      the response. The expression must be of the form ` ` where operators:
      `<`, `>`, `<=`, `>=`, `!=`, `=`, `:` are supported (colon `:` represents
      a HAS operator which is roughly synonymous with equality). can refer to
      a proto or JSON field, or a synthetic field. Field names can be
      camelCase or snake_case. Examples: * Filter by name: name =
      "RESOURCE_NAME" * Filter by labels: * Resources that have a key named
      `foo` labels.foo:* * Resources that have a key named `foo` whose value
      is `bar` labels.foo = bar
    orderBy: Optional. A field used to sort the results by a certain order.
    pageSize: Optional. The maximum number of multicast group producer
      activations to return per call.
    pageToken: Optional. A page token from an earlier query, as returned in
      `next_page_token`.
    parent: Required. The parent resource for which to list multicast group
      producer activations. Use the following format:
      `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsMulticastGroupProducerActivationsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupProducerActivationsPatch
  Request object.

  Fields:
    multicastGroupProducerActivation: A MulticastGroupProducerActivation
      resource to be passed as the request body.
    name: Identifier. The resource name of the multicast group producer
      activation. Use the following format:
      `projects/*/locations/*/multicastGroupProducerActivations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. The field mask specifies the fields to overwrite in
      the MulticastGroupProducerActivationresource by the update. The fields
      specified in the `update_mask` are relative to the resource, not the
      full request. If a field is in the mask, then it is overwritten. If the
      you do not provide a mask, then all fields are overwritten
  """

  multicastGroupProducerActivation = _messages.MessageField('MulticastGroupProducerActivation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastGroupRangeActivationsCreateRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastGroupRangeActivationsCreateRequest
  object.

  Fields:
    multicastGroupRangeActivation: A MulticastGroupRangeActivation resource to
      be passed as the request body.
    multicastGroupRangeActivationId: Required. A unique name for the multicast
      group range activation. The name is restricted to letters, numbers, and
      hyphen, with the first character a letter, and the last a letter or a
      number. The name must not exceed 48 characters.
    parent: Required. The parent resource of the multicast group range
      activation. Use the following format: `projects/*/locations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  multicastGroupRangeActivation = _messages.MessageField('MulticastGroupRangeActivation', 1)
  multicastGroupRangeActivationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastGroupRangeActivationsDeleteRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastGroupRangeActivationsDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the multicast group range activation
      to delete. Use the following format:
      `projects/*/locations/*/multicastGroupRangeActivations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsMulticastGroupRangeActivationsGetRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastGroupRangeActivationsGetRequest
  object.

  Fields:
    name: Required. The resource name of the multicast group range activation
      to get. Use the following format:
      `projects/*/locations/*/multicastGroupRangeActivations/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMulticastGroupRangeActivationsListRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastGroupRangeActivationsListRequest
  object.

  Fields:
    filter: Optional. A filter expression that filters the resources listed in
      the response. The expression must be of the form ` ` where operators:
      `<`, `>`, `<=`, `>=`, `!=`, `=`, `:` are supported (colon `:` represents
      a HAS operator which is roughly synonymous with equality). can refer to
      a proto or JSON field, or a synthetic field. Field names can be
      camelCase or snake_case. Examples: * Filter by name: name =
      "RESOURCE_NAME" * Filter by labels: * Resources that have a key named
      `foo` labels.foo:* * Resources that have a key named `foo` whose value
      is `bar` labels.foo = bar
    orderBy: Optional. A field used to sort the results by a certain order.
    pageSize: Optional. The maximum number of multicast group range
      activations to return per call.
    pageToken: Optional. A page token from an earlier query, as returned in
      `next_page_token`.
    parent: Required. The parent resource for which to list multicast group
      range activations. Use the following format: `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsMulticastGroupRangeActivationsPatchRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastGroupRangeActivationsPatchRequest
  object.

  Fields:
    multicastGroupRangeActivation: A MulticastGroupRangeActivation resource to
      be passed as the request body.
    name: Identifier. The resource name of the multicast group range
      activation. Use the following format:
      `projects/*/locations/*/multicastGroupRangeActivations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. The field mask specifies the fields to overwrite in
      the multicast group range activation resource by the update. The fields
      specified in the `update_mask` are relative to the resource, not the
      full request. If a field is in the mask, then it is overwritten. If the
      you do not provide a mask, then all fields are overwritten.
  """

  multicastGroupRangeActivation = _messages.MessageField('MulticastGroupRangeActivation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastGroupRangesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupRangesCreateRequest
  object.

  Fields:
    multicastGroupRange: A MulticastGroupRange resource to be passed as the
      request body.
    multicastGroupRangeId: Required. A unique name for the multicast group
      range. The name is restricted to letters, numbers, and hyphen, with the
      first character a letter, and the last a letter or a number. The name
      must not exceed 48 characters.
    parent: Required. The parent resource of the multicast group range. Use
      the following format: `projects/*/locations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  multicastGroupRange = _messages.MessageField('MulticastGroupRange', 1)
  multicastGroupRangeId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastGroupRangesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupRangesDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the multicast group range to delete.
      Use the following format:
      `projects/*/locations/global/multicastGroupRanges/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsMulticastGroupRangesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupRangesGetRequest object.

  Fields:
    name: Required. The resource name of the multicast group range to get. Use
      the following format: `projects/*/locations/*/multicastGroupRanges/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMulticastGroupRangesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupRangesListRequest
  object.

  Fields:
    filter: Optional. A filter expression that filters the resources listed in
      the response. The expression must be of the form ` ` where operators:
      `<`, `>`, `<=`, `>=`, `!=`, `=`, `:` are supported (colon `:` represents
      a HAS operator which is roughly synonymous with equality). can refer to
      a proto or JSON field, or a synthetic field. Field names can be
      camelCase or snake_case. Examples: * Filter by name: name =
      "RESOURCE_NAME" * Filter by labels: * Resources that have a key named
      `foo` labels.foo:* * Resources that have a key named `foo` whose value
      is `bar` labels.foo = bar
    orderBy: Optional. A field used to sort the results by a certain order.
    pageSize: Optional. The maximum number of multicast group ranges to return
      per call.
    pageToken: Optional. A page token from an earlier query, as returned in
      `next_page_token`.
    parent: Required. The parent resource for which to list multicast group
      ranges. Use the following format: `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsMulticastGroupRangesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupRangesPatchRequest
  object.

  Fields:
    multicastGroupRange: A MulticastGroupRange resource to be passed as the
      request body.
    name: Identifier. The resource name of the multicast group range. Use the
      following format: `projects/*/locations/global/multicastGroupRanges/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. The field mask specifies the fields to overwrite in
      the multicast group range resource by the update. The fields specified
      in the `update_mask` are relative to the resource, not the full request.
      If a field is in the mask, then it is overwritten. If the you do not
      provide a mask, then all fields are overwritten.
  """

  multicastGroupRange = _messages.MessageField('MulticastGroupRange', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastProducerAssociationsCreateRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastProducerAssociationsCreateRequest
  object.

  Fields:
    multicastProducerAssociation: A MulticastProducerAssociation resource to
      be passed as the request body.
    multicastProducerAssociationId: Required. A unique name for the multicast
      producer association. The name is restricted to letters, numbers, and
      hyphen, with the first character a letter, and the last a letter or a
      number. The name must not exceed 48 characters.
    parent: Required. The parent resource of the multicast producer
      association. Use the following format: `projects/*/locations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  multicastProducerAssociation = _messages.MessageField('MulticastProducerAssociation', 1)
  multicastProducerAssociationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastProducerAssociationsDeleteRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastProducerAssociationsDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the multicast producer association to
      delete. Use the following format:
      `projects/*/locations/*/multicastProducerAssociations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsMulticastProducerAssociationsGetRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastProducerAssociationsGetRequest
  object.

  Fields:
    name: Required. The resource name of the multicast producer association to
      get. Use the following format:
      `projects/*/locations/*/multicastProducerAssociations/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMulticastProducerAssociationsListRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastProducerAssociationsListRequest
  object.

  Fields:
    filter: Optional. A filter expression that filters the resources listed in
      the response. The expression must be of the form ` ` where operators:
      `<`, `>`, `<=`, `>=`, `!=`, `=`, `:` are supported (colon `:` represents
      a HAS operator which is roughly synonymous with equality). can refer to
      a proto or JSON field, or a synthetic field. Field names can be
      camelCase or snake_case. Examples: * Filter by name: name =
      "RESOURCE_NAME" * Filter by labels: * Resources that have a key named
      `foo` labels.foo:* * Resources that have a key named `foo` whose value
      is `bar` labels.foo = bar
    orderBy: Optional. A field used to sort the results by a certain order.
    pageSize: Optional. The maximum number of multicast producer associations
      to return per call.
    pageToken: Optional. A page token from an earlier query, as returned in
      `next_page_token`.
    parent: Required. The parent resource for which to list multicast producer
      associations. Use the following format: `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsMulticastProducerAssociationsPatchRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastProducerAssociationsPatchRequest
  object.

  Fields:
    multicastProducerAssociation: A MulticastProducerAssociation resource to
      be passed as the request body.
    name: Identifier. The resource name of the multicast producer association.
      Use the following format:
      `projects/*/locations/*/multicastProducerAssociations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. The field mask specifies the fields to overwrite in
      the MulticastProducerAssociation resource by the update. The fields
      specified in the `update_mask` are relative to the resource, not the
      full request. If a field is in the mask, then it is overwritten. If the
      you do not provide a mask, then all fields are overwritten
  """

  multicastProducerAssociation = _messages.MessageField('MulticastProducerAssociation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworkservicesProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class NetworkservicesProjectsLocationsServiceBindingsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceBindingsCreateRequest object.

  Fields:
    parent: Required. The parent resource of the ServiceBinding. Must be in
      the format `projects/*/locations/*`.
    serviceBinding: A ServiceBinding resource to be passed as the request
      body.
    serviceBindingId: Required. Short name of the ServiceBinding resource to
      be created.
  """

  parent = _messages.StringField(1, required=True)
  serviceBinding = _messages.MessageField('ServiceBinding', 2)
  serviceBindingId = _messages.StringField(3)


class NetworkservicesProjectsLocationsServiceBindingsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceBindingsDeleteRequest object.

  Fields:
    name: Required. A name of the ServiceBinding to delete. Must be in the
      format `projects/*/locations/*/serviceBindings/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsServiceBindingsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceBindingsGetRequest object.

  Fields:
    name: Required. A name of the ServiceBinding to get. Must be in the format
      `projects/*/locations/*/serviceBindings/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsServiceBindingsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceBindingsListRequest object.

  Fields:
    pageSize: Maximum number of ServiceBindings to return per call.
    pageToken: The value returned by the last `ListServiceBindingsResponse`
      Indicates that this is a continuation of a prior `ListRouters` call, and
      that the system should return the next page of data.
    parent: Required. The project and location from which the ServiceBindings
      should be listed, specified in the format `projects/*/locations/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsServiceBindingsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceBindingsPatchRequest object.

  Fields:
    name: Identifier. Name of the ServiceBinding resource. It matches pattern
      `projects/*/locations/*/serviceBindings/`.
    serviceBinding: A ServiceBinding resource to be passed as the request
      body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the ServiceBinding resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  serviceBinding = _messages.MessageField('ServiceBinding', 2)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsServiceLbPoliciesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceLbPoliciesCreateRequest object.

  Fields:
    parent: Required. The parent resource of the ServiceLbPolicy. Must be in
      the format `projects/{project}/locations/{location}`.
    serviceLbPolicy: A ServiceLbPolicy resource to be passed as the request
      body.
    serviceLbPolicyId: Required. Short name of the ServiceLbPolicy resource to
      be created. E.g. for resource name `projects/{project}/locations/{locati
      on}/serviceLbPolicies/{service_lb_policy_name}`. the id is value of
      {service_lb_policy_name}
  """

  parent = _messages.StringField(1, required=True)
  serviceLbPolicy = _messages.MessageField('ServiceLbPolicy', 2)
  serviceLbPolicyId = _messages.StringField(3)


class NetworkservicesProjectsLocationsServiceLbPoliciesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceLbPoliciesDeleteRequest object.

  Fields:
    name: Required. A name of the ServiceLbPolicy to delete. Must be in the
      format `projects/{project}/locations/{location}/serviceLbPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsServiceLbPoliciesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceLbPoliciesGetRequest object.

  Fields:
    name: Required. A name of the ServiceLbPolicy to get. Must be in the
      format `projects/{project}/locations/{location}/serviceLbPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsServiceLbPoliciesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceLbPoliciesListRequest object.

  Fields:
    pageSize: Maximum number of ServiceLbPolicies to return per call.
    pageToken: The value returned by the last `ListServiceLbPoliciesResponse`
      Indicates that this is a continuation of a prior `ListRouters` call, and
      that the system should return the next page of data.
    parent: Required. The project and location from which the
      ServiceLbPolicies should be listed, specified in the format
      `projects/{project}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsServiceLbPoliciesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceLbPoliciesPatchRequest object.

  Fields:
    name: Identifier. Name of the ServiceLbPolicy resource. It matches pattern
      `projects/{project}/locations/{location}/serviceLbPolicies/{service_lb_p
      olicy_name}`.
    serviceLbPolicy: A ServiceLbPolicy resource to be passed as the request
      body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the ServiceLbPolicy resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  serviceLbPolicy = _messages.MessageField('ServiceLbPolicy', 2)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsTcpRoutesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTcpRoutesCreateRequest object.

  Fields:
    parent: Required. The parent resource of the TcpRoute. Must be in the
      format `projects/*/locations/global`.
    tcpRoute: A TcpRoute resource to be passed as the request body.
    tcpRouteId: Required. Short name of the TcpRoute resource to be created.
  """

  parent = _messages.StringField(1, required=True)
  tcpRoute = _messages.MessageField('TcpRoute', 2)
  tcpRouteId = _messages.StringField(3)


class NetworkservicesProjectsLocationsTcpRoutesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTcpRoutesDeleteRequest object.

  Fields:
    name: Required. A name of the TcpRoute to delete. Must be in the format
      `projects/*/locations/global/tcpRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsTcpRoutesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTcpRoutesGetRequest object.

  Fields:
    name: Required. A name of the TcpRoute to get. Must be in the format
      `projects/*/locations/global/tcpRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsTcpRoutesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTcpRoutesListRequest object.

  Fields:
    pageSize: Maximum number of TcpRoutes to return per call.
    pageToken: The value returned by the last `ListTcpRoutesResponse`
      Indicates that this is a continuation of a prior `ListTcpRoutes` call,
      and that the system should return the next page of data.
    parent: Required. The project and location from which the TcpRoutes should
      be listed, specified in the format `projects/*/locations/global`.
    returnPartialSuccess: Optional. If true, allow partial responses for
      multi-regional Aggregated List requests. Otherwise if one of the
      locations is down or unreachable, the Aggregated List request will fail.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  returnPartialSuccess = _messages.BooleanField(4)


class NetworkservicesProjectsLocationsTcpRoutesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTcpRoutesPatchRequest object.

  Fields:
    name: Identifier. Name of the TcpRoute resource. It matches pattern
      `projects/*/locations/global/tcpRoutes/tcp_route_name>`.
    tcpRoute: A TcpRoute resource to be passed as the request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the TcpRoute resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  tcpRoute = _messages.MessageField('TcpRoute', 2)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsTlsRoutesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTlsRoutesCreateRequest object.

  Fields:
    parent: Required. The parent resource of the TlsRoute. Must be in the
      format `projects/*/locations/global`.
    tlsRoute: A TlsRoute resource to be passed as the request body.
    tlsRouteId: Required. Short name of the TlsRoute resource to be created.
  """

  parent = _messages.StringField(1, required=True)
  tlsRoute = _messages.MessageField('TlsRoute', 2)
  tlsRouteId = _messages.StringField(3)


class NetworkservicesProjectsLocationsTlsRoutesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTlsRoutesDeleteRequest object.

  Fields:
    name: Required. A name of the TlsRoute to delete. Must be in the format
      `projects/*/locations/global/tlsRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsTlsRoutesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTlsRoutesGetRequest object.

  Fields:
    name: Required. A name of the TlsRoute to get. Must be in the format
      `projects/*/locations/global/tlsRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsTlsRoutesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTlsRoutesListRequest object.

  Fields:
    pageSize: Maximum number of TlsRoutes to return per call.
    pageToken: The value returned by the last `ListTlsRoutesResponse`
      Indicates that this is a continuation of a prior `ListTlsRoutes` call,
      and that the system should return the next page of data.
    parent: Required. The project and location from which the TlsRoutes should
      be listed, specified in the format `projects/*/locations/global`.
    returnPartialSuccess: Optional. If true, allow partial responses for
      multi-regional Aggregated List requests. Otherwise if one of the
      locations is down or unreachable, the Aggregated List request will fail.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  returnPartialSuccess = _messages.BooleanField(4)


class NetworkservicesProjectsLocationsTlsRoutesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTlsRoutesPatchRequest object.

  Fields:
    name: Identifier. Name of the TlsRoute resource. It matches pattern
      `projects/*/locations/global/tlsRoutes/tls_route_name>`.
    tlsRoute: A TlsRoute resource to be passed as the request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the TlsRoute resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  tlsRoute = _messages.MessageField('TlsRoute', 2)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsWasmPluginsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsWasmPluginsCreateRequest object.

  Fields:
    parent: Required. The parent resource of the `WasmPlugin` resource. Must
      be in the format `projects/{project}/locations/global`.
    wasmPlugin: A WasmPlugin resource to be passed as the request body.
    wasmPluginId: Required. User-provided ID of the `WasmPlugin` resource to
      be created.
  """

  parent = _messages.StringField(1, required=True)
  wasmPlugin = _messages.MessageField('WasmPlugin', 2)
  wasmPluginId = _messages.StringField(3)


class NetworkservicesProjectsLocationsWasmPluginsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsWasmPluginsDeleteRequest object.

  Fields:
    name: Required. A name of the `WasmPlugin` resource to delete. Must be in
      the format
      `projects/{project}/locations/global/wasmPlugins/{wasm_plugin}`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsWasmPluginsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsWasmPluginsGetRequest object.

  Enums:
    ViewValueValuesEnum: Determines how much data must be returned in the
      response. See [AIP-157](https://google.aip.dev/157).

  Fields:
    name: Required. A name of the `WasmPlugin` resource to get. Must be in the
      format `projects/{project}/locations/global/wasmPlugins/{wasm_plugin}`.
    view: Determines how much data must be returned in the response. See
      [AIP-157](https://google.aip.dev/157).
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Determines how much data must be returned in the response. See
    [AIP-157](https://google.aip.dev/157).

    Values:
      WASM_PLUGIN_VIEW_UNSPECIFIED: Unspecified value. Do not use.
      WASM_PLUGIN_VIEW_BASIC: If specified in the `GET` request for a
        `WasmPlugin` resource, the server's response includes just the
        `WasmPlugin` resource.
      WASM_PLUGIN_VIEW_FULL: If specified in the `GET` request for a
        `WasmPlugin` resource, the server's response includes the `WasmPlugin`
        resource with all its versions.
    """
    WASM_PLUGIN_VIEW_UNSPECIFIED = 0
    WASM_PLUGIN_VIEW_BASIC = 1
    WASM_PLUGIN_VIEW_FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class NetworkservicesProjectsLocationsWasmPluginsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsWasmPluginsListRequest object.

  Fields:
    pageSize: Maximum number of `WasmPlugin` resources to return per call. If
      not specified, at most 50 `WasmPlugin` resources are returned. The
      maximum value is 1000; values above 1000 are coerced to 1000.
    pageToken: The value returned by the last `ListWasmPluginsResponse` call.
      Indicates that this is a continuation of a prior `ListWasmPlugins` call,
      and that the next page of data is to be returned.
    parent: Required. The project and location from which the `WasmPlugin`
      resources are listed, specified in the following format:
      `projects/{project}/locations/global`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsWasmPluginsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsWasmPluginsPatchRequest object.

  Fields:
    name: Identifier. Name of the `WasmPlugin` resource in the following
      format:
      `projects/{project}/locations/{location}/wasmPlugins/{wasm_plugin}`.
    updateMask: Optional. Used to specify the fields to be overwritten in the
      `WasmPlugin` resource by the update. The fields specified in the
      `update_mask` field are relative to the resource, not the full request.
      An omitted `update_mask` field is treated as an implied `update_mask`
      field equivalent to all fields that are populated (that have a non-empty
      value). The `update_mask` field supports a special value `*`, which
      means that each field in the given `WasmPlugin` resource (including the
      empty ones) replaces the current value.
    wasmPlugin: A WasmPlugin resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  wasmPlugin = _messages.MessageField('WasmPlugin', 3)


class NetworkservicesProjectsLocationsWasmPluginsVersionsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsWasmPluginsVersionsCreateRequest
  object.

  Fields:
    parent: Required. The parent resource of the `WasmPluginVersion` resource.
      Must be in the format
      `projects/{project}/locations/global/wasmPlugins/{wasm_plugin}`.
    wasmPluginVersion: A WasmPluginVersion resource to be passed as the
      request body.
    wasmPluginVersionId: Required. User-provided ID of the `WasmPluginVersion`
      resource to be created.
  """

  parent = _messages.StringField(1, required=True)
  wasmPluginVersion = _messages.MessageField('WasmPluginVersion', 2)
  wasmPluginVersionId = _messages.StringField(3)


class NetworkservicesProjectsLocationsWasmPluginsVersionsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsWasmPluginsVersionsDeleteRequest
  object.

  Fields:
    name: Required. A name of the `WasmPluginVersion` resource to delete. Must
      be in the format `projects/{project}/locations/global/wasmPlugins/{wasm_
      plugin}/versions/{wasm_plugin_version}`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsWasmPluginsVersionsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsWasmPluginsVersionsGetRequest object.

  Fields:
    name: Required. A name of the `WasmPluginVersion` resource to get. Must be
      in the format `projects/{project}/locations/global/wasmPlugins/{wasm_plu
      gin}/versions/{wasm_plugin_version}`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsWasmPluginsVersionsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsWasmPluginsVersionsListRequest object.

  Fields:
    pageSize: Maximum number of `WasmPluginVersion` resources to return per
      call. If not specified, at most 50 `WasmPluginVersion` resources are
      returned. The maximum value is 1000; values above 1000 are coerced to
      1000.
    pageToken: The value returned by the last `ListWasmPluginVersionsResponse`
      call. Indicates that this is a continuation of a prior
      `ListWasmPluginVersions` call, and that the next page of data is to be
      returned.
    parent: Required. The `WasmPlugin` resource whose `WasmPluginVersion`s are
      listed, specified in the following format:
      `projects/{project}/locations/global/wasmPlugins/{wasm_plugin}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class OriginHeaderAction(_messages.Message):
  r"""Defines the addition and removal of HTTP headers for requests/responses.

  Fields:
    requestHeadersToAdd: Optional. A header to add. You can add a maximum of
      25 request headers.
  """

  requestHeadersToAdd = _messages.MessageField('OriginHeaderActionAddHeader', 1, repeated=True)


class OriginHeaderActionAddHeader(_messages.Message):
  r"""Describes a header to add.

  Fields:
    headerName: Required. The name of the header to add.
    headerValue: Required. The value of the header to add.
    replace: Optional. Specifies whether to replace all existing headers with
      the same name. By default, added header values are appended to the
      response or request headers with the same field names. The added values
      are separated by commas. To overwrite existing values, set `replace` to
      `true`.
  """

  headerName = _messages.StringField(1)
  headerValue = _messages.StringField(2)
  replace = _messages.BooleanField(3)


class OriginOverrideAction(_messages.Message):
  r"""Defines how requests and responses can be manipulated on cache fill to
  this origin.

  Fields:
    headerAction: Optional. The header actions, including adding and removing
      headers, for requests handled by this origin.
    urlRewrite: Optional. The URL rewrite configuration for requests that are
      handled by this origin.
  """

  headerAction = _messages.MessageField('OriginHeaderAction', 1)
  urlRewrite = _messages.MessageField('OriginUrlRewrite', 2)


class OriginRedirect(_messages.Message):
  r"""The options for following redirects from the origin.

  Enums:
    RedirectConditionsValueListEntryValuesEnum:

  Fields:
    redirectConditions: Optional. The set of HTTP redirect response codes that
      the CDN follows.
  """

  class RedirectConditionsValueListEntryValuesEnum(_messages.Enum):
    r"""RedirectConditionsValueListEntryValuesEnum enum type.

    Values:
      REDIRECT_CONDITIONS_UNSPECIFIED: It is an error to specify
        `REDIRECT_CONDITIONS_UNSPECIFIED`.
      MOVED_PERMANENTLY: Follow redirect on an `HTTP 301` error.
      FOUND: Follow redirect on an `HTTP 302` error.
      SEE_OTHER: Follow redirect on an `HTTP 303` error.
      TEMPORARY_REDIRECT: Follow redirect on an `HTTP 307` error.
      PERMANENT_REDIRECT: Follow redirect on an `HTTP 308` error.
    """
    REDIRECT_CONDITIONS_UNSPECIFIED = 0
    MOVED_PERMANENTLY = 1
    FOUND = 2
    SEE_OTHER = 3
    TEMPORARY_REDIRECT = 4
    PERMANENT_REDIRECT = 5

  redirectConditions = _messages.EnumField('RedirectConditionsValueListEntryValuesEnum', 1, repeated=True)


class OriginUrlRewrite(_messages.Message):
  r"""The URL rewrite configuration for a given request handled by this
  origin.

  Fields:
    hostRewrite: Optional. Before forwarding the request to the selected
      origin, the request's `Host` header is replaced with the contents of
      `hostRewrite`. The host value must be between 1 and 255 characters.
  """

  hostRewrite = _messages.StringField(1)


class PathMatcher(_messages.Message):
  r"""The name of the `PathMatcher` to use to match the path portion of the
  URL if the HostRule matches the URL's host portion.

  Fields:
    description: Optional. A human-readable description of the resource.
    name: Required. The name to which this `PathMatcher` is referred by the
      HostRule.
    routeRules: Required. A list of RouteRule rules to match against.
      `RouteRule` rules support advanced routing behavior, and can match on
      paths, headers and query parameters, as well as status codes and HTTP
      methods. You must specify at least one rule, and can specify a maximum
      of 200 rules. `RouteRule` rules must not have duplicate priority values.
  """

  description = _messages.StringField(1)
  name = _messages.StringField(2)
  routeRules = _messages.MessageField('RouteRule', 3, repeated=True)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PublicKey(_messages.Message):
  r"""An Ed25519 public key used for validating signed requests.

  Fields:
    id: Required. The ID of the public key. The ID must be 1-64 characters
      long, and match the regular expression `[a-zA-Z]([a-zA-Z0-9_-])*` which
      means the first character must be a letter, and all following characters
      must be a dash, an underscore, a letter, or a digit.
    managed: Optional. Set to `true` to have the CDN automatically manage this
      public key. Managed keys are used by the CDN for dual-token
      authentication. Media CDN internally generates, uses, and rotates the
      underlying public and private key pair. It is not possible to use a
      managed key outside of dual-token authentication. Either `value` or
      `managed` must be specified.
    value: Optional. The base64-encoded value of the Ed25519 public key. The
      base64 encoding can be padded (44 bytes) or unpadded (43 bytes).
      Representations or encodings of the public key other than this are
      rejected with an error. Either `value` or `managed` must be specified.
  """

  id = _messages.StringField(1)
  managed = _messages.BooleanField(2)
  value = _messages.StringField(3)


class QueryParameterMatcher(_messages.Message):
  r"""The match conditions for URI query parameters.

  Fields:
    exactMatch: Optional. The QueryParameterMatcher matches if the value of
      the parameter exactly matches the contents of `exact_match`. The match
      value must be between 1 and 64 characters long (inclusive). Only one of
      present_match or `exact_match` must be set.
    name: Required. The name of the query parameter to match. The query
      parameter must exist in the request; if it doesn't, the request match
      fails. The name must be specified and be between 1 and 32 characters
      long (inclusive).
    presentMatch: Optional. Specifies that the QueryParameterMatcher matches
      if the request contains the query parameter. The match can succeed as
      long as the request contains the query parameter, regardless of whether
      the parameter has a value or not. Only one of `present_match` or
      exact_match must be set.
  """

  exactMatch = _messages.StringField(1)
  name = _messages.StringField(2)
  presentMatch = _messages.BooleanField(3)


class RetryFilterPerRouteConfig(_messages.Message):
  r"""A RetryFilterPerRouteConfig object.

  Fields:
    cryptoKeyName: The name of the crypto key to use for encrypting event
      data.
  """

  cryptoKeyName = _messages.StringField(1)


class RouteAction(_messages.Message):
  r"""The actions (such as rewrites, redirects, CORS header injection, and
  header modification) to take for a given route match.

  Enums:
    CompressionModeValueValuesEnum: Optional. The compression mode to use for
      responses. If not specified, Media CDN doesn't compress uncompressed
      responses received from the origin.

  Fields:
    cdnPolicy: Optional. The policy to use for defining caching and signed
      request behavior for requests that match this route.
    compressionMode: Optional. The compression mode to use for responses. If
      not specified, Media CDN doesn't compress uncompressed responses
      received from the origin.
    corsPolicy: Optional. The Cross-Origin Resource Sharing (CORS) policy for
      requests that match this route.
    urlRewrite: Optional. The URL rewrite configuration for requests that
      match this route.
    wasmAction: Optional. A WasmAction resource in the format:
      `projects/{project}/locations/{location}/wasmActions/{wasm_action}`
  """

  class CompressionModeValueValuesEnum(_messages.Enum):
    r"""Optional. The compression mode to use for responses. If not specified,
    Media CDN doesn't compress uncompressed responses received from the
    origin.

    Values:
      COMPRESSION_MODE_UNSPECIFIED: Unspecified value. Defaults to DISABLED.
      DISABLED: Disables compression.
      AUTOMATIC: Automatically uses the best compression mode. Compression
        requires an appropriate `Accept-Encoding` header to be sent by the
        client and is enabled based on content type, content size, and other
        factors.
    """
    COMPRESSION_MODE_UNSPECIFIED = 0
    DISABLED = 1
    AUTOMATIC = 2

  cdnPolicy = _messages.MessageField('CDNPolicy', 1)
  compressionMode = _messages.EnumField('CompressionModeValueValuesEnum', 2)
  corsPolicy = _messages.MessageField('CORSPolicy', 3)
  urlRewrite = _messages.MessageField('UrlRewrite', 4)
  wasmAction = _messages.StringField(5)


class RouteRule(_messages.Message):
  r"""The priority of a given route, including its match conditions and the
  actions to take on a request that matches.

  Fields:
    description: Optional. A human-readable description of the `RouteRule`.
    headerAction: Optional. The header actions, including adding and removing
      headers, for requests that match this route.
    matchRules: Required. The list of criteria for matching attributes of a
      request to this `RouteRule`. This list has `OR` semantics: the request
      matches this `RouteRule` when any of the MatchRules are satisfied.
      However, predicates within a given `MatchRule` have `AND` semantics. All
      predicates within a `MatchRule` must match for the request to match the
      rule. You can specify up to five match rules.
    origin: Optional. An alternate EdgeCacheOrigin resource that this route
      responds with when a matching response is not in the cache. The
      following are both valid paths to an `EdgeCacheOrigin` resource: *
      `projects/my-project/locations/global/edgeCacheOrigins/my-origin` * `my-
      origin` Only one of `origin` or url_redirect can be set.
    priority: Required. The priority of this route rule, where `1` is the
      highest priority. You cannot configure two or more `RouteRules` with the
      same priority. Priority for each rule must be set to a number between 1
      and 999 inclusive. Priority numbers can have gaps, which enable you to
      add or remove rules in the future without affecting the rest of the
      rules. For example, `1, 2, 3, 4, 5, 9, 12, 16` is a valid series of
      priority numbers to which you could add rules numbered from 6 to 8, 10
      to 11, and 13 to 15 in the future without any impact on existing rules.
    routeAction: Optional. In response to a matching path, the RouteAction
      performs advanced routing actions like URL rewrites, header
      transformations, and so forth prior to forwarding the request to the
      selected origin.
    routeMethods: Optional. Allow overriding the set of methods that are
      allowed for this route. When not set, Media CDN allows only `GET`,
      `HEAD`, and `OPTIONS`.
    urlRedirect: Optional. The URL redirect configuration for requests that
      match this route. Only one of origin or `url_redirect` can be set.
  """

  description = _messages.StringField(1)
  headerAction = _messages.MessageField('HeaderAction', 2)
  matchRules = _messages.MessageField('MatchRule', 3, repeated=True)
  origin = _messages.StringField(4)
  priority = _messages.IntegerField(5)
  routeAction = _messages.MessageField('RouteAction', 6)
  routeMethods = _messages.MessageField('RouteRuleRouteMethods', 7)
  urlRedirect = _messages.MessageField('UrlRedirect', 8)


class RouteRuleRouteMethods(_messages.Message):
  r"""Allow overriding the set of methods that are allowed for a route.

  Fields:
    allowedMethods: Required. The non-empty set of HTTP methods that are
      allowed for this route.
  """

  allowedMethods = _messages.StringField(1, repeated=True)


class Routing(_messages.Message):
  r"""Defines how requests are routed, modified, cached, and which origin the
  content is filled from.

  Fields:
    hostRules: Required. A list of HostRule rules to match against.
      `RouteRule` rules support advanced routing behavior, and can match on
      paths, headers and query parameters, as well as status codes and HTTP
      methods. You can specify up to 50 host rules.
    pathMatchers: Required. A list of PathMatcher values referenced by name by
      HostRule values. `PathMatcher` is used to match the path portion of the
      URL when a`HostRule` value matches the URL's host portion. You can
      specify up to 50 path matchers.
  """

  hostRules = _messages.MessageField('HostRule', 1, repeated=True)
  pathMatchers = _messages.MessageField('PathMatcher', 2, repeated=True)


class Secret(_messages.Message):
  r"""A secret key stored in Secret Manager.

  Fields:
    secretVersion: Required. The name of the secret in Secret Manager. The
      resource name of the secret version must be in the format
      `projects/*/secrets/*/versions/*` where the `*` values are replaced by
      the name of the secret. The secrets must be at least 16 bytes. The
      recommended secret size depends on the signature algorithm that you're
      using. * If you're using `HMAC-SHA1`, we recommend 20-byte secrets. * If
      you're using `HMAC-SHA256`, we recommend 32-byte secrets. See RFC 2104,
      Section 3 for more details on these recommendations.
  """

  secretVersion = _messages.StringField(1)


class ServiceBinding(_messages.Message):
  r"""ServiceBinding can be used to: - Bind a Service Directory Service to be
  used in a BackendService resource. This feature will be deprecated soon. -
  Bind a Private Service Connect producer service to be used in consumer Cloud
  Service Mesh or Application Load Balancers. - Bind a Cloud Run service to be
  used in consumer Cloud Service Mesh or Application Load Balancers.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the
      ServiceBinding resource.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    labels: Optional. Set of label tags associated with the ServiceBinding
      resource.
    name: Identifier. Name of the ServiceBinding resource. It matches pattern
      `projects/*/locations/*/serviceBindings/`.
    service: Optional. The full Service Directory Service name of the format
      `projects/*/locations/*/namespaces/*/services/*`. This field is for
      Service Directory integration which will be deprecated soon.
    serviceId: Output only. The unique identifier of the Service Directory
      Service against which the ServiceBinding resource is validated. This is
      populated when the Service Binding resource is used in another resource
      (like Backend Service). This is of the UUID4 format. This field is for
      Service Directory integration which will be deprecated soon.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the ServiceBinding
    resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  service = _messages.StringField(5)
  serviceId = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class ServiceLbPolicy(_messages.Message):
  r"""ServiceLbPolicy holds global load balancing and traffic distribution
  configuration that can be applied to a BackendService.

  Enums:
    LoadBalancingAlgorithmValueValuesEnum: Optional. The type of load
      balancing algorithm to be used. The default behavior is
      WATERFALL_BY_REGION.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the
      ServiceLbPolicy resource.

  Fields:
    autoCapacityDrain: Optional. Configuration to automatically move traffic
      away for unhealthy IG/NEG for the associated Backend Service.
    createTime: Output only. The timestamp when this resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    failoverConfig: Optional. Configuration related to health based failover.
    isolationConfig: Optional. Configuration to provide isolation support for
      the associated Backend Service.
    labels: Optional. Set of label tags associated with the ServiceLbPolicy
      resource.
    loadBalancingAlgorithm: Optional. The type of load balancing algorithm to
      be used. The default behavior is WATERFALL_BY_REGION.
    name: Identifier. Name of the ServiceLbPolicy resource. It matches pattern
      `projects/{project}/locations/{location}/serviceLbPolicies/{service_lb_p
      olicy_name}`.
    updateTime: Output only. The timestamp when this resource was last
      updated.
  """

  class LoadBalancingAlgorithmValueValuesEnum(_messages.Enum):
    r"""Optional. The type of load balancing algorithm to be used. The default
    behavior is WATERFALL_BY_REGION.

    Values:
      LOAD_BALANCING_ALGORITHM_UNSPECIFIED: The type of the loadbalancing
        algorithm is unspecified.
      SPRAY_TO_WORLD: Balance traffic across all backends across the world
        proportionally based on capacity.
      SPRAY_TO_REGION: Direct traffic to the nearest region with endpoints and
        capacity before spilling over to other regions and spread the traffic
        from each client to all the MIGs/NEGs in a region.
      WATERFALL_BY_REGION: Direct traffic to the nearest region with endpoints
        and capacity before spilling over to other regions. All MIGs/NEGs
        within a region are evenly loaded but each client might not spread the
        traffic to all the MIGs/NEGs in the region.
      WATERFALL_BY_ZONE: Attempt to keep traffic in a single zone closest to
        the client, before spilling over to other zones.
    """
    LOAD_BALANCING_ALGORITHM_UNSPECIFIED = 0
    SPRAY_TO_WORLD = 1
    SPRAY_TO_REGION = 2
    WATERFALL_BY_REGION = 3
    WATERFALL_BY_ZONE = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the ServiceLbPolicy
    resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  autoCapacityDrain = _messages.MessageField('ServiceLbPolicyAutoCapacityDrain', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  failoverConfig = _messages.MessageField('ServiceLbPolicyFailoverConfig', 4)
  isolationConfig = _messages.MessageField('ServiceLbPolicyIsolationConfig', 5)
  labels = _messages.MessageField('LabelsValue', 6)
  loadBalancingAlgorithm = _messages.EnumField('LoadBalancingAlgorithmValueValuesEnum', 7)
  name = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class ServiceLbPolicyAutoCapacityDrain(_messages.Message):
  r"""Option to specify if an unhealthy IG/NEG should be considered for global
  load balancing and traffic routing.

  Fields:
    enable: Optional. If set to 'True', an unhealthy IG/NEG will be set as
      drained. - An IG/NEG is considered unhealthy if less than 25% of the
      instances/endpoints in the IG/NEG are healthy. - This option will never
      result in draining more than 50% of the configured IGs/NEGs for the
      Backend Service.
  """

  enable = _messages.BooleanField(1)


class ServiceLbPolicyFailoverConfig(_messages.Message):
  r"""Option to specify health based failover behavior. This is not related to
  Network load balancer FailoverPolicy.

  Fields:
    failoverHealthThreshold: Optional. The percentage threshold that a load
      balancer will begin to send traffic to failover backends. If the
      percentage of endpoints in a MIG/NEG is smaller than this value, traffic
      would be sent to failover backends if possible. This field should be set
      to a value between 1 and 99. The default value is 50 for Global external
      HTTP(S) load balancer (classic) and Proxyless service mesh, and 70 for
      others.
  """

  failoverHealthThreshold = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class ServiceLbPolicyIsolationConfig(_messages.Message):
  r"""Configuration to provide isolation support for the associated Backend
  Service.

  Enums:
    IsolationGranularityValueValuesEnum: Optional. The isolation granularity
      of the load balancer.
    IsolationModeValueValuesEnum: Optional. The isolation mode of the load
      balancer.

  Fields:
    isolationGranularity: Optional. The isolation granularity of the load
      balancer.
    isolationMode: Optional. The isolation mode of the load balancer.
  """

  class IsolationGranularityValueValuesEnum(_messages.Enum):
    r"""Optional. The isolation granularity of the load balancer.

    Values:
      ISOLATION_GRANULARITY_UNSPECIFIED: No isolation is configured for the
        backend service. Traffic can overflow based on the load balancing
        algorithm.
      REGION: Traffic for this service will be isolated at the cloud region
        level.
    """
    ISOLATION_GRANULARITY_UNSPECIFIED = 0
    REGION = 1

  class IsolationModeValueValuesEnum(_messages.Enum):
    r"""Optional. The isolation mode of the load balancer.

    Values:
      ISOLATION_MODE_UNSPECIFIED: No isolation mode is configured for the
        backend service.
      NEAREST: Traffic will be sent to the nearest region.
      STRICT: Traffic will fail if no serving backends are available in the
        same region as the load balancer.
    """
    ISOLATION_MODE_UNSPECIFIED = 0
    NEAREST = 1
    STRICT = 2

  isolationGranularity = _messages.EnumField('IsolationGranularityValueValuesEnum', 1)
  isolationMode = _messages.EnumField('IsolationModeValueValuesEnum', 2)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TcpRoute(_messages.Message):
  r"""TcpRoute is the resource defining how TCP traffic should be routed by a
  Mesh/Gateway resource.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the TcpRoute
      resource.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    gateways: Optional. Gateways defines a list of gateways this TcpRoute is
      attached to, as one of the routing rules to route the requests served by
      the gateway. Each gateway reference should match the pattern:
      `projects/*/locations/global/gateways/`
    labels: Optional. Set of label tags associated with the TcpRoute resource.
    meshes: Optional. Meshes defines a list of meshes this TcpRoute is
      attached to, as one of the routing rules to route the requests served by
      the mesh. Each mesh reference should match the pattern:
      `projects/*/locations/global/meshes/` The attached Mesh should be of a
      type SIDECAR
    name: Identifier. Name of the TcpRoute resource. It matches pattern
      `projects/*/locations/global/tcpRoutes/tcp_route_name>`.
    rules: Required. Rules that define how traffic is routed and handled. At
      least one RouteRule must be supplied. If there are multiple rules then
      the action taken will be the first rule to match.
    selfLink: Output only. Server-defined URL of this resource
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the TcpRoute resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  gateways = _messages.StringField(3, repeated=True)
  labels = _messages.MessageField('LabelsValue', 4)
  meshes = _messages.StringField(5, repeated=True)
  name = _messages.StringField(6)
  rules = _messages.MessageField('TcpRouteRouteRule', 7, repeated=True)
  selfLink = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class TcpRouteRouteAction(_messages.Message):
  r"""The specifications for routing traffic and applying associated policies.

  Fields:
    destinations: Optional. The destination services to which traffic should
      be forwarded. At least one destination service is required. Only one of
      route destination or original destination can be set.
    idleTimeout: Optional. Specifies the idle timeout for the selected route.
      The idle timeout is defined as the period in which there are no bytes
      sent or received on either the upstream or downstream connection. If not
      set, the default idle timeout is 30 seconds. If set to 0s, the timeout
      will be disabled.
    originalDestination: Optional. If true, Router will use the destination IP
      and port of the original connection as the destination of the request.
      Default is false. Only one of route destinations or original destination
      can be set.
  """

  destinations = _messages.MessageField('TcpRouteRouteDestination', 1, repeated=True)
  idleTimeout = _messages.StringField(2)
  originalDestination = _messages.BooleanField(3)


class TcpRouteRouteDestination(_messages.Message):
  r"""Describe the destination for traffic to be routed to.

  Fields:
    serviceName: Required. The URL of a BackendService to route traffic to.
    weight: Optional. Specifies the proportion of requests forwarded to the
      backend referenced by the serviceName field. This is computed as: -
      weight/Sum(weights in this destination list). For non-zero values, there
      may be some epsilon from the exact proportion defined here depending on
      the precision an implementation supports. If only one serviceName is
      specified and it has a weight greater than 0, 100% of the traffic is
      forwarded to that backend. If weights are specified for any one service
      name, they need to be specified for all of them. If weights are
      unspecified for all services, then, traffic is distributed in equal
      proportions to all of them.
  """

  serviceName = _messages.StringField(1)
  weight = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class TcpRouteRouteMatch(_messages.Message):
  r"""RouteMatch defines the predicate used to match requests to a given
  action. Multiple match types are "OR"ed for evaluation. If no routeMatch
  field is specified, this rule will unconditionally match traffic.

  Fields:
    address: Required. Must be specified in the CIDR range format. A CIDR
      range consists of an IP Address and a prefix length to construct the
      subnet mask. By default, the prefix length is 32 (i.e. matches a single
      IP address). Only IPV4 addresses are supported. Examples: "********" -
      matches against this exact IP address. "10.0.0.0/8" - matches against
      any IP address within the 10.0.0.0 subnet and ************* mask.
      "0.0.0.0/0" - matches against any IP address'.
    port: Required. Specifies the destination port to match against.
  """

  address = _messages.StringField(1)
  port = _messages.StringField(2)


class TcpRouteRouteRule(_messages.Message):
  r"""Specifies how to match traffic and how to route traffic when traffic is
  matched.

  Fields:
    action: Required. The detailed rule defining how to route matched traffic.
    matches: Optional. RouteMatch defines the predicate used to match requests
      to a given action. Multiple match types are "OR"ed for evaluation. If no
      routeMatch field is specified, this rule will unconditionally match
      traffic.
  """

  action = _messages.MessageField('TcpRouteRouteAction', 1)
  matches = _messages.MessageField('TcpRouteRouteMatch', 2, repeated=True)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class Timeout(_messages.Message):
  r"""The timeout configuration for this origin.

  Fields:
    connectTimeout: Optional. The maximum duration to wait for a single origin
      connection to be established, including DNS lookup, TLS handshake, and
      TCP/QUIC connection establishment. Defaults to 5 seconds. The timeout
      must be a value between 1s and 15s. The `connect_timeout` capped by the
      deadline set by the request's max_attempts_timeout. The last connection
      attempt can have a smaller `connect_timeout` so that the total
      `max_attempts_timeout` value is not exceeded.
    maxAttemptsTimeout: Optional. The maximum time across all connection
      attempts to all origins, including failover origins, before returning an
      error to the client. An `HTTP 504` error is returned if the timeout is
      reached before a response is returned. Defaults to 15 seconds. The
      timeout must be a value between 1s and 30s. If a failover origin is
      specified, the `max_attempts_timeout` value of the first configured
      origin sets the deadline for all connection attempts across all failover
      origins.
    readTimeout: Optional. The maximum duration to wait between reads of a
      single HTTP connection or stream. Defaults to 15 seconds. The timeout
      must be a value between 1s and 30s. The `read_timeout` value is capped
      by the response_timeout value. All reads of the HTTP connection or
      stream must be completed by the deadline set by response_timeout. If the
      response headers have already been written to the connection, the
      response is truncated and logged.
    responseTimeout: Optional. The maximum duration to wait for the last byte
      of a response to arrive when reading from the HTTP connection/stream.
      Defaults to 30 seconds. The timeout must be a value between 1s and 120s.
      The `response_timeout` starts after the connection has been established.
      Origins that take longer to write additional bytes to the response than
      the configured `response_timeout` value result in an error returned to
      the client. If the response headers have already been written to the
      connection, the response is truncated and logged.
  """

  connectTimeout = _messages.StringField(1)
  maxAttemptsTimeout = _messages.StringField(2)
  readTimeout = _messages.StringField(3)
  responseTimeout = _messages.StringField(4)


class TlsRoute(_messages.Message):
  r"""TlsRoute defines how traffic should be routed based on SNI and other
  matching L3 attributes.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the TlsRoute
      resource.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    gateways: Optional. Gateways defines a list of gateways this TlsRoute is
      attached to, as one of the routing rules to route the requests served by
      the gateway. Each gateway reference should match the pattern:
      `projects/*/locations/global/gateways/`
    labels: Optional. Set of label tags associated with the TlsRoute resource.
    meshes: Optional. Meshes defines a list of meshes this TlsRoute is
      attached to, as one of the routing rules to route the requests served by
      the mesh. Each mesh reference should match the pattern:
      `projects/*/locations/global/meshes/` The attached Mesh should be of a
      type SIDECAR
    name: Identifier. Name of the TlsRoute resource. It matches pattern
      `projects/*/locations/global/tlsRoutes/tls_route_name>`.
    rules: Required. Rules that define how traffic is routed and handled. At
      least one RouteRule must be supplied. If there are multiple rules then
      the action taken will be the first rule to match.
    selfLink: Output only. Server-defined URL of this resource
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the TlsRoute resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  gateways = _messages.StringField(3, repeated=True)
  labels = _messages.MessageField('LabelsValue', 4)
  meshes = _messages.StringField(5, repeated=True)
  name = _messages.StringField(6)
  rules = _messages.MessageField('TlsRouteRouteRule', 7, repeated=True)
  selfLink = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class TlsRouteRouteAction(_messages.Message):
  r"""The specifications for routing traffic and applying associated policies.

  Fields:
    destinations: Required. The destination services to which traffic should
      be forwarded. At least one destination service is required.
    idleTimeout: Optional. Specifies the idle timeout for the selected route.
      The idle timeout is defined as the period in which there are no bytes
      sent or received on either the upstream or downstream connection. If not
      set, the default idle timeout is 1 hour. If set to 0s, the timeout will
      be disabled.
  """

  destinations = _messages.MessageField('TlsRouteRouteDestination', 1, repeated=True)
  idleTimeout = _messages.StringField(2)


class TlsRouteRouteDestination(_messages.Message):
  r"""Describe the destination for traffic to be routed to.

  Fields:
    serviceName: Required. The URL of a BackendService to route traffic to.
    weight: Optional. Specifies the proportion of requests forwarded to the
      backend referenced by the service_name field. This is computed as: -
      weight/Sum(weights in destinations) Weights in all destinations does not
      need to sum up to 100.
  """

  serviceName = _messages.StringField(1)
  weight = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class TlsRouteRouteMatch(_messages.Message):
  r"""RouteMatch defines the predicate used to match requests to a given
  action. Multiple match types are "AND"ed for evaluation.

  Fields:
    alpn: Optional. ALPN (Application-Layer Protocol Negotiation) to match
      against. Examples: "http/1.1", "h2". At least one of sni_host and alpn
      is required. Up to 5 alpns across all matches can be set.
    sniHost: Optional. SNI (server name indicator) to match against. SNI will
      be matched against all wildcard domains, i.e. `www.example.com` will be
      first matched against `www.example.com`, then `*.example.com`, then
      `*.com.` Partial wildcards are not supported, and values like
      *w.example.com are invalid. At least one of sni_host and alpn is
      required. Up to 100 sni hosts across all matches can be set.
  """

  alpn = _messages.StringField(1, repeated=True)
  sniHost = _messages.StringField(2, repeated=True)


class TlsRouteRouteRule(_messages.Message):
  r"""Specifies how to match traffic and how to route traffic when traffic is
  matched.

  Fields:
    action: Required. The detailed rule defining how to route matched traffic.
    matches: Required. RouteMatch defines the predicate used to match requests
      to a given action. Multiple match types are "OR"ed for evaluation.
      Atleast one RouteMatch must be supplied.
  """

  action = _messages.MessageField('TlsRouteRouteAction', 1)
  matches = _messages.MessageField('TlsRouteRouteMatch', 2, repeated=True)


class TrafficPortSelector(_messages.Message):
  r"""Specification of a port-based selector.

  Fields:
    ports: Optional. A list of ports. Can be port numbers or port range
      (example, [80-90] specifies all ports from 80 to 90, including 80 and
      90) or named ports or * to specify all ports. If the list is empty, all
      ports are selected.
  """

  ports = _messages.StringField(1, repeated=True)


class TrafficSpec(_messages.Message):
  r"""Specifies the traffic volume and multicast group scale parameters that
  are used to set up multicast infrastructure for a multicast domain in a
  zone.

  Fields:
    aggrEgressPps: Optional. Aggregated egress Packet-Per-Second for all
      multicast groups in the domain in this zone.
    aggrIngressPps: Optional. Aggregated ingress Packet-Per-Second for all
      multicast groups in the domain in this zone. Default to
      (aggregated_egress_pps / max_per_group_subscribers) * 2.
    avgPacketSize: Optional. Average packet size (Default to 512 bytes).
    maxPerGroupIngressPps: Optional. Maximum ingress Packet-Per-Second for a
      single multicast group in this zone. Default to aggregated_ingress_pps /
      2.
    maxPerGroupSubscribers: Optional. Maximum number of subscribers for a
      single multicast group in this zone. Default to max(50,
      aggregated_egress_pps / aggregated_ingress_pps).
  """

  aggrEgressPps = _messages.IntegerField(1)
  aggrIngressPps = _messages.IntegerField(2)
  avgPacketSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  maxPerGroupIngressPps = _messages.IntegerField(4)
  maxPerGroupSubscribers = _messages.IntegerField(5)


class UllMulticastDomain(_messages.Message):
  r"""Information for an Ultra-Low-Latency multicast domain. This is only
  available in specific locations and allowlisted projects.

  Fields:
    preconfiguredUllDomain: Optional. The preconfigured Ultra-Low-Latency
      domain name.
  """

  preconfiguredUllDomain = _messages.StringField(1)


class UrlRedirect(_messages.Message):
  r"""The HTTP redirect configuration for a given request.

  Enums:
    RedirectResponseCodeValueValuesEnum: Optional. The HTTP status code to use
      for this redirect action. For a list of supported values, see
      RedirectResponseCode.

  Fields:
    hostRedirect: Optional. The host that is used in the redirect response
      instead of the one that was supplied in the request. The value must be
      between 1 and 255 characters.
    httpsRedirect: Optional. Determines whether the URL scheme in the
      redirected request is adjusted to `HTTPS` or remains that of the
      request. If it is set to `true` and at least one edge_ssl_certificates
      is set on the service, the URL scheme in the redirected request is set
      to `HTTPS`. If it is set to `false`, the URL scheme of the redirected
      request remains the same as that of the request.
    pathRedirect: Optional. The path that is used in the redirect response
      instead of the one that was supplied in the request. `path_redirect`
      cannot be supplied together with prefix_redirect. Supply one alone or
      neither. If neither is supplied, the path of the original request is
      used for the redirect. The path value must be between 1 and 1024
      characters.
    prefixRedirect: Optional. The prefix that replaces the prefix_match
      specified in the RouteRule rule, retaining the remaining portion of the
      URL before redirecting the request. `prefix_redirect` cannot be supplied
      together with path_redirect. Supply one alone or neither. If neither is
      supplied, the path of the original request is used for the redirect. The
      prefix value must be between 1 and 1024 characters.
    redirectResponseCode: Optional. The HTTP status code to use for this
      redirect action. For a list of supported values, see
      RedirectResponseCode.
    stripQuery: Optional. Determines whether accompanying query portions of
      the original URL are removed prior to redirecting the request. If it is
      set to `true`, the accompanying query portion of the original URL is
      removed before redirecting the request. If it is set to `false`, the
      query portion of the original URL is retained. The default is `false`.
  """

  class RedirectResponseCodeValueValuesEnum(_messages.Enum):
    r"""Optional. The HTTP status code to use for this redirect action. For a
    list of supported values, see RedirectResponseCode.

    Values:
      MOVED_PERMANENTLY_DEFAULT: `HTTP 301 (Moved Permanently)`
      FOUND: HTTP 302 Found
      SEE_OTHER: HTTP 303 See Other
      TEMPORARY_REDIRECT: `HTTP 307 (Temporary Redirect)`. In this case, the
        request method is retained.
      PERMANENT_REDIRECT: `HTTP 308 (Permanent Redirect)`. In this case, the
        request method is retained.
    """
    MOVED_PERMANENTLY_DEFAULT = 0
    FOUND = 1
    SEE_OTHER = 2
    TEMPORARY_REDIRECT = 3
    PERMANENT_REDIRECT = 4

  hostRedirect = _messages.StringField(1)
  httpsRedirect = _messages.BooleanField(2)
  pathRedirect = _messages.StringField(3)
  prefixRedirect = _messages.StringField(4)
  redirectResponseCode = _messages.EnumField('RedirectResponseCodeValueValuesEnum', 5)
  stripQuery = _messages.BooleanField(6)


class UrlRewrite(_messages.Message):
  r"""Defines the URL rewrite configuration for a given request.

  Fields:
    hostRewrite: Optional. Before forwarding the request to the selected
      origin, the request's host header is replaced with contents of
      `host_rewrite`. The host value must be between 1 and 255 characters.
    pathPrefixRewrite: Optional. Before forwarding the request to the selected
      origin, the matching portion of the request's path is replaced by
      `path_prefix_rewrite`. If specified, the path value must start with a
      `/` and be between 1 and 1024 characters long (inclusive).
      `path_prefix_rewrite` can only be used when all of a route's match_rules
      specify prefix_match or full_path_match. Only one of
      `path_prefix_rewrite` and path_template_rewrite can be specified.
    pathTemplateRewrite: Optional. Before forwarding the request to the
      selected origin, if the request matched a path_template_match, the
      matching portion of the request's path is replaced re-written using the
      pattern specified by `path_template_rewrite`. `path_template_rewrite`
      must be between 1 and 255 characters (inclusive), must start with a `/`,
      and must only use variables captured by the route's
      `path_template_match`. `path_template_rewrite` can only be used when all
      of a route's match_rules specify `path_template_match`. Only one of
      path_prefix_rewrite and `path_template_rewrite` can be specified.
  """

  hostRewrite = _messages.StringField(1)
  pathPrefixRewrite = _messages.StringField(2)
  pathTemplateRewrite = _messages.StringField(3)


class WasmPlugin(_messages.Message):
  r"""`WasmPlugin` is a resource representing a service executing a customer-
  provided Wasm module.

  Messages:
    LabelsValue: Optional. Set of labels associated with the `WasmPlugin`
      resource. The format must comply with [the following
      requirements](/compute/docs/labeling-resources#requirements).
    VersionsValue: Optional. All versions of this `WasmPlugin` resource in the
      key-value format. The key is the resource ID, and the value is the
      `VersionDetails` object. Lets you create or update a `WasmPlugin`
      resource and its versions in a single request. When the
      `main_version_id` field is not empty, it must point to one of the
      `VersionDetails` objects in the map. If provided in a `PATCH` request,
      the new versions replace the previous set. Any version omitted from the
      `versions` field is removed. Because the `WasmPluginVersion` resource is
      immutable, if a `WasmPluginVersion` resource with the same name already
      exists and differs, the request fails. Note: In a `GET` request, this
      field is populated only if the field `GetWasmPluginRequest.view` is set
      to `WASM_PLUGIN_VIEW_FULL`.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A human-readable description of the resource.
    labels: Optional. Set of labels associated with the `WasmPlugin` resource.
      The format must comply with [the following
      requirements](/compute/docs/labeling-resources#requirements).
    logConfig: Optional. Specifies the logging options for the activity
      performed by this plugin. If logging is enabled, plugin logs are
      exported to Cloud Logging. Note that the settings relate to the logs
      generated by using logging statements in your Wasm code.
    mainVersionId: Optional. The ID of the `WasmPluginVersion` resource that
      is the currently serving one. The version referred to must be a child of
      this `WasmPlugin` resource.
    name: Identifier. Name of the `WasmPlugin` resource in the following
      format:
      `projects/{project}/locations/{location}/wasmPlugins/{wasm_plugin}`.
    updateTime: Output only. The timestamp when the resource was updated.
    usedBy: Output only. List of all
      [extensions](https://cloud.google.com/service-extensions/docs/overview)
      that use this `WasmPlugin` resource.
    versions: Optional. All versions of this `WasmPlugin` resource in the key-
      value format. The key is the resource ID, and the value is the
      `VersionDetails` object. Lets you create or update a `WasmPlugin`
      resource and its versions in a single request. When the
      `main_version_id` field is not empty, it must point to one of the
      `VersionDetails` objects in the map. If provided in a `PATCH` request,
      the new versions replace the previous set. Any version omitted from the
      `versions` field is removed. Because the `WasmPluginVersion` resource is
      immutable, if a `WasmPluginVersion` resource with the same name already
      exists and differs, the request fails. Note: In a `GET` request, this
      field is populated only if the field `GetWasmPluginRequest.view` is set
      to `WASM_PLUGIN_VIEW_FULL`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of labels associated with the `WasmPlugin` resource. The
    format must comply with [the following
    requirements](/compute/docs/labeling-resources#requirements).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class VersionsValue(_messages.Message):
    r"""Optional. All versions of this `WasmPlugin` resource in the key-value
    format. The key is the resource ID, and the value is the `VersionDetails`
    object. Lets you create or update a `WasmPlugin` resource and its versions
    in a single request. When the `main_version_id` field is not empty, it
    must point to one of the `VersionDetails` objects in the map. If provided
    in a `PATCH` request, the new versions replace the previous set. Any
    version omitted from the `versions` field is removed. Because the
    `WasmPluginVersion` resource is immutable, if a `WasmPluginVersion`
    resource with the same name already exists and differs, the request fails.
    Note: In a `GET` request, this field is populated only if the field
    `GetWasmPluginRequest.view` is set to `WASM_PLUGIN_VIEW_FULL`.

    Messages:
      AdditionalProperty: An additional property for a VersionsValue object.

    Fields:
      additionalProperties: Additional properties of type VersionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a VersionsValue object.

      Fields:
        key: Name of the additional property.
        value: A WasmPluginVersionDetails attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('WasmPluginVersionDetails', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  logConfig = _messages.MessageField('WasmPluginLogConfig', 4)
  mainVersionId = _messages.StringField(5)
  name = _messages.StringField(6)
  updateTime = _messages.StringField(7)
  usedBy = _messages.MessageField('WasmPluginUsedBy', 8, repeated=True)
  versions = _messages.MessageField('VersionsValue', 9)


class WasmPluginLogConfig(_messages.Message):
  r"""Specifies the logging options for the activity performed by this plugin.
  If logging is enabled, plugin logs are exported to Cloud Logging.

  Enums:
    MinLogLevelValueValuesEnum: Non-empty default. Specificies the lowest
      level of the plugin logs that are exported to Cloud Logging. This
      setting relates to the logs generated by using logging statements in
      your Wasm code. This field is can be set only if logging is enabled for
      the plugin. If the field is not provided when logging is enabled, it is
      set to `INFO` by default.

  Fields:
    enable: Optional. Specifies whether to enable logging for activity by this
      plugin. Defaults to `false`.
    minLogLevel: Non-empty default. Specificies the lowest level of the plugin
      logs that are exported to Cloud Logging. This setting relates to the
      logs generated by using logging statements in your Wasm code. This field
      is can be set only if logging is enabled for the plugin. If the field is
      not provided when logging is enabled, it is set to `INFO` by default.
    sampleRate: Non-empty default. Configures the sampling rate of activity
      logs, where `1.0` means all logged activity is reported and `0.0` means
      no activity is reported. A floating point value between `0.0` and `1.0`
      indicates that a percentage of log messages is stored. The default value
      when logging is enabled is `1.0`. The value of the field must be between
      `0` and `1` (inclusive). This field can be specified only if logging is
      enabled for this plugin.
  """

  class MinLogLevelValueValuesEnum(_messages.Enum):
    r"""Non-empty default. Specificies the lowest level of the plugin logs
    that are exported to Cloud Logging. This setting relates to the logs
    generated by using logging statements in your Wasm code. This field is can
    be set only if logging is enabled for the plugin. If the field is not
    provided when logging is enabled, it is set to `INFO` by default.

    Values:
      LOG_LEVEL_UNSPECIFIED: Unspecified value. Defaults to `LogLevel.INFO`.
      TRACE: Report logs with TRACE level and above.
      DEBUG: Report logs with DEBUG level and above.
      INFO: Report logs with INFO level and above.
      WARN: Report logs with WARN level and above.
      ERROR: Report logs with ERROR level and above.
      CRITICAL: Report logs with CRITICAL level only.
    """
    LOG_LEVEL_UNSPECIFIED = 0
    TRACE = 1
    DEBUG = 2
    INFO = 3
    WARN = 4
    ERROR = 5
    CRITICAL = 6

  enable = _messages.BooleanField(1)
  minLogLevel = _messages.EnumField('MinLogLevelValueValuesEnum', 2)
  sampleRate = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class WasmPluginUsedBy(_messages.Message):
  r"""Defines a resource that uses the `WasmPlugin` resource.

  Fields:
    name: Output only. Full name of the resource
      https://google.aip.dev/122#full-resource-names, for example `//networkse
      rvices.googleapis.com/projects/{project}/locations/{location}/lbRouteExt
      ensions/{extension}`
  """

  name = _messages.StringField(1)


class WasmPluginVersion(_messages.Message):
  r"""A single immutable version of a `WasmPlugin` resource. Defines the Wasm
  module used and optionally its runtime config.

  Messages:
    LabelsValue: Optional. Set of labels associated with the
      `WasmPluginVersion` resource.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A human-readable description of the resource.
    imageDigest: Output only. The resolved digest for the image specified in
      the `image` field. The digest is resolved during the creation of
      `WasmPluginVersion` resource. This field holds the digest value,
      regardless of whether a tag or digest was originally specified in the
      `image` field.
    imageUri: Optional. URI of the container image containing the plugin,
      stored in the Artifact Registry. When a new `WasmPluginVersion` resource
      is created, the digest of the container image is saved in the
      `image_digest` field. When downloading an image, the digest value is
      used instead of an image tag.
    labels: Optional. Set of labels associated with the `WasmPluginVersion`
      resource.
    name: Identifier. Name of the `WasmPluginVersion` resource in the
      following format:
      `projects/{project}/locations/{location}/wasmPlugins/{wasm_plugin}/
      versions/{wasm_plugin_version}`.
    pluginConfigData: Configuration for the plugin. The configuration is
      provided to the plugin at runtime through the `ON_CONFIGURE` callback.
      When a new `WasmPluginVersion` resource is created, the digest of the
      contents is saved in the `plugin_config_digest` field.
    pluginConfigDigest: Output only. This field holds the digest (usually
      checksum) value for the plugin configuration. The value is calculated
      based on the contents of `plugin_config_data` or the container image
      defined by the `plugin_config_uri` field.
    pluginConfigUri: URI of the plugin configuration stored in the Artifact
      Registry. The configuration is provided to the plugin at runtime through
      the `ON_CONFIGURE` callback. The container image must contain only a
      single file with the name `plugin.config`. When a new
      `WasmPluginVersion` resource is created, the digest of the container
      image is saved in the `plugin_config_digest` field.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of labels associated with the `WasmPluginVersion`
    resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  imageDigest = _messages.StringField(3)
  imageUri = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  pluginConfigData = _messages.BytesField(7)
  pluginConfigDigest = _messages.StringField(8)
  pluginConfigUri = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class WasmPluginVersionDetails(_messages.Message):
  r"""Details of a `WasmPluginVersion` resource to be inlined in the
  `WasmPlugin` resource.

  Messages:
    LabelsValue: Optional. Set of labels associated with the
      `WasmPluginVersion` resource.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A human-readable description of the resource.
    imageDigest: Output only. The resolved digest for the image specified in
      `image`. The digest is resolved during the creation of a
      `WasmPluginVersion` resource. This field holds the digest value
      regardless of whether a tag or digest was originally specified in the
      `image` field.
    imageUri: Optional. URI of the container image containing the Wasm module,
      stored in the Artifact Registry. The container image must contain only a
      single file with the name `plugin.wasm`. When a new `WasmPluginVersion`
      resource is created, the URI gets resolved to an image digest and saved
      in the `image_digest` field.
    labels: Optional. Set of labels associated with the `WasmPluginVersion`
      resource.
    pluginConfigData: Configuration for the plugin. The configuration is
      provided to the plugin at runtime through the `ON_CONFIGURE` callback.
      When a new `WasmPluginVersion` version is created, the digest of the
      contents is saved in the `plugin_config_digest` field.
    pluginConfigDigest: Output only. This field holds the digest (usually
      checksum) value for the plugin configuration. The value is calculated
      based on the contents of the `plugin_config_data` field or the container
      image defined by the `plugin_config_uri` field.
    pluginConfigUri: URI of the plugin configuration stored in the Artifact
      Registry. The configuration is provided to the plugin at runtime through
      the `ON_CONFIGURE` callback. The container image must contain only a
      single file with the name `plugin.config`. When a new
      `WasmPluginVersion` resource is created, the digest of the container
      image is saved in the `plugin_config_digest` field.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of labels associated with the `WasmPluginVersion`
    resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  imageDigest = _messages.StringField(3)
  imageUri = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  pluginConfigData = _messages.BytesField(6)
  pluginConfigDigest = _messages.StringField(7)
  pluginConfigUri = _messages.StringField(8)
  updateTime = _messages.StringField(9)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    NetworkservicesProjectsLocationsEdgeCacheKeysetsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    NetworkservicesProjectsLocationsEdgeCacheOriginsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    NetworkservicesProjectsLocationsEdgeCacheServicesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
