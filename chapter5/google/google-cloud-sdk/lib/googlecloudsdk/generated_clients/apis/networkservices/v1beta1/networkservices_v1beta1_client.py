"""Generated client library for networkservices version v1beta1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.networkservices.v1beta1 import networkservices_v1beta1_messages as messages


class NetworkservicesV1beta1(base_api.BaseApiClient):
  """Generated client library for service networkservices version v1beta1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://networkservices.googleapis.com/'
  MTLS_BASE_URL = 'https://networkservices.mtls.googleapis.com/'

  _PACKAGE = 'networkservices'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1beta1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'NetworkservicesV1beta1'
  _URL_VERSION = 'v1beta1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new networkservices handle."""
    url = url or self.BASE_URL
    super(NetworkservicesV1beta1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_authzExtensions = self.ProjectsLocationsAuthzExtensionsService(self)
    self.projects_locations_endpointPolicies = self.ProjectsLocationsEndpointPoliciesService(self)
    self.projects_locations_gateways_routeViews = self.ProjectsLocationsGatewaysRouteViewsService(self)
    self.projects_locations_gateways = self.ProjectsLocationsGatewaysService(self)
    self.projects_locations_grpcRoutes = self.ProjectsLocationsGrpcRoutesService(self)
    self.projects_locations_httpRoutes = self.ProjectsLocationsHttpRoutesService(self)
    self.projects_locations_lbEdgeExtensions = self.ProjectsLocationsLbEdgeExtensionsService(self)
    self.projects_locations_lbRouteExtensions = self.ProjectsLocationsLbRouteExtensionsService(self)
    self.projects_locations_lbTrafficExtensions = self.ProjectsLocationsLbTrafficExtensionsService(self)
    self.projects_locations_meshes_routeViews = self.ProjectsLocationsMeshesRouteViewsService(self)
    self.projects_locations_meshes = self.ProjectsLocationsMeshesService(self)
    self.projects_locations_multicastConsumerAssociations = self.ProjectsLocationsMulticastConsumerAssociationsService(self)
    self.projects_locations_multicastDomainActivations = self.ProjectsLocationsMulticastDomainActivationsService(self)
    self.projects_locations_multicastDomainGroups = self.ProjectsLocationsMulticastDomainGroupsService(self)
    self.projects_locations_multicastDomains = self.ProjectsLocationsMulticastDomainsService(self)
    self.projects_locations_multicastGroupConsumerActivations = self.ProjectsLocationsMulticastGroupConsumerActivationsService(self)
    self.projects_locations_multicastGroupProducerActivations = self.ProjectsLocationsMulticastGroupProducerActivationsService(self)
    self.projects_locations_multicastGroupRangeActivations = self.ProjectsLocationsMulticastGroupRangeActivationsService(self)
    self.projects_locations_multicastGroupRanges = self.ProjectsLocationsMulticastGroupRangesService(self)
    self.projects_locations_multicastProducerAssociations = self.ProjectsLocationsMulticastProducerAssociationsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_serviceBindings = self.ProjectsLocationsServiceBindingsService(self)
    self.projects_locations_serviceLbPolicies = self.ProjectsLocationsServiceLbPoliciesService(self)
    self.projects_locations_tcpRoutes = self.ProjectsLocationsTcpRoutesService(self)
    self.projects_locations_tlsRoutes = self.ProjectsLocationsTlsRoutesService(self)
    self.projects_locations_wasmPlugins_versions = self.ProjectsLocationsWasmPluginsVersionsService(self)
    self.projects_locations_wasmPlugins = self.ProjectsLocationsWasmPluginsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsAuthzExtensionsService(base_api.BaseApiService):
    """Service class for the projects_locations_authzExtensions resource."""

    _NAME = 'projects_locations_authzExtensions'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsAuthzExtensionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `AuthzExtension` resource in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsAuthzExtensionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/authzExtensions',
        http_method='POST',
        method_id='networkservices.projects.locations.authzExtensions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['authzExtensionId', 'requestId'],
        relative_path='v1beta1/{+parent}/authzExtensions',
        request_field='authzExtension',
        request_type_name='NetworkservicesProjectsLocationsAuthzExtensionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified `AuthzExtension` resource.

      Args:
        request: (NetworkservicesProjectsLocationsAuthzExtensionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/authzExtensions/{authzExtensionsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.authzExtensions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsAuthzExtensionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of the specified `AuthzExtension` resource.

      Args:
        request: (NetworkservicesProjectsLocationsAuthzExtensionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AuthzExtension) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/authzExtensions/{authzExtensionsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.authzExtensions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsAuthzExtensionsGetRequest',
        response_type_name='AuthzExtension',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `AuthzExtension` resources in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsAuthzExtensionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAuthzExtensionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/authzExtensions',
        http_method='GET',
        method_id='networkservices.projects.locations.authzExtensions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/authzExtensions',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsAuthzExtensionsListRequest',
        response_type_name='ListAuthzExtensionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of the specified `AuthzExtension` resource.

      Args:
        request: (NetworkservicesProjectsLocationsAuthzExtensionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/authzExtensions/{authzExtensionsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.authzExtensions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='authzExtension',
        request_type_name='NetworkservicesProjectsLocationsAuthzExtensionsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsEndpointPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_endpointPolicies resource."""

    _NAME = 'projects_locations_endpointPolicies'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsEndpointPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new EndpointPolicy in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/endpointPolicies',
        http_method='POST',
        method_id='networkservices.projects.locations.endpointPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['endpointPolicyId'],
        relative_path='v1beta1/{+parent}/endpointPolicies',
        request_field='endpointPolicy',
        request_type_name='NetworkservicesProjectsLocationsEndpointPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single EndpointPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/endpointPolicies/{endpointPoliciesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.endpointPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEndpointPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single EndpointPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EndpointPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/endpointPolicies/{endpointPoliciesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.endpointPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEndpointPoliciesGetRequest',
        response_type_name='EndpointPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists EndpointPolicies in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEndpointPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/endpointPolicies',
        http_method='GET',
        method_id='networkservices.projects.locations.endpointPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v1beta1/{+parent}/endpointPolicies',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEndpointPoliciesListRequest',
        response_type_name='ListEndpointPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single EndpointPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/endpointPolicies/{endpointPoliciesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.endpointPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='endpointPolicy',
        request_type_name='NetworkservicesProjectsLocationsEndpointPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsGatewaysRouteViewsService(base_api.BaseApiService):
    """Service class for the projects_locations_gateways_routeViews resource."""

    _NAME = 'projects_locations_gateways_routeViews'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsGatewaysRouteViewsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get a single RouteView of a Gateway.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysRouteViewsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GatewayRouteView) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}/routeViews/{routeViewsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.gateways.routeViews.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGatewaysRouteViewsGetRequest',
        response_type_name='GatewayRouteView',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists RouteViews.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysRouteViewsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGatewayRouteViewsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}/routeViews',
        http_method='GET',
        method_id='networkservices.projects.locations.gateways.routeViews.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/routeViews',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGatewaysRouteViewsListRequest',
        response_type_name='ListGatewayRouteViewsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGatewaysService(base_api.BaseApiService):
    """Service class for the projects_locations_gateways resource."""

    _NAME = 'projects_locations_gateways'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsGatewaysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Gateway in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/gateways',
        http_method='POST',
        method_id='networkservices.projects.locations.gateways.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['gatewayId'],
        relative_path='v1beta1/{+parent}/gateways',
        request_field='gateway',
        request_type_name='NetworkservicesProjectsLocationsGatewaysCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Gateway.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.gateways.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGatewaysDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Gateway.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Gateway) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}',
        http_method='GET',
        method_id='networkservices.projects.locations.gateways.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGatewaysGetRequest',
        response_type_name='Gateway',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Gateways in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGatewaysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/gateways',
        http_method='GET',
        method_id='networkservices.projects.locations.gateways.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/gateways',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGatewaysListRequest',
        response_type_name='ListGatewaysResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Gateway.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.gateways.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='gateway',
        request_type_name='NetworkservicesProjectsLocationsGatewaysPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsGrpcRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_grpcRoutes resource."""

    _NAME = 'projects_locations_grpcRoutes'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsGrpcRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new GrpcRoute in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsGrpcRoutesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/grpcRoutes',
        http_method='POST',
        method_id='networkservices.projects.locations.grpcRoutes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['grpcRouteId'],
        relative_path='v1beta1/{+parent}/grpcRoutes',
        request_field='grpcRoute',
        request_type_name='NetworkservicesProjectsLocationsGrpcRoutesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single GrpcRoute.

      Args:
        request: (NetworkservicesProjectsLocationsGrpcRoutesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/grpcRoutes/{grpcRoutesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.grpcRoutes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGrpcRoutesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single GrpcRoute.

      Args:
        request: (NetworkservicesProjectsLocationsGrpcRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GrpcRoute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/grpcRoutes/{grpcRoutesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.grpcRoutes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGrpcRoutesGetRequest',
        response_type_name='GrpcRoute',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists GrpcRoutes in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsGrpcRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGrpcRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/grpcRoutes',
        http_method='GET',
        method_id='networkservices.projects.locations.grpcRoutes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v1beta1/{+parent}/grpcRoutes',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGrpcRoutesListRequest',
        response_type_name='ListGrpcRoutesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single GrpcRoute.

      Args:
        request: (NetworkservicesProjectsLocationsGrpcRoutesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/grpcRoutes/{grpcRoutesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.grpcRoutes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='grpcRoute',
        request_type_name='NetworkservicesProjectsLocationsGrpcRoutesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsHttpRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_httpRoutes resource."""

    _NAME = 'projects_locations_httpRoutes'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsHttpRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new HttpRoute in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsHttpRoutesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/httpRoutes',
        http_method='POST',
        method_id='networkservices.projects.locations.httpRoutes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['httpRouteId'],
        relative_path='v1beta1/{+parent}/httpRoutes',
        request_field='httpRoute',
        request_type_name='NetworkservicesProjectsLocationsHttpRoutesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single HttpRoute.

      Args:
        request: (NetworkservicesProjectsLocationsHttpRoutesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/httpRoutes/{httpRoutesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.httpRoutes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsHttpRoutesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single HttpRoute.

      Args:
        request: (NetworkservicesProjectsLocationsHttpRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpRoute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/httpRoutes/{httpRoutesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.httpRoutes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsHttpRoutesGetRequest',
        response_type_name='HttpRoute',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists HttpRoute in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsHttpRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListHttpRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/httpRoutes',
        http_method='GET',
        method_id='networkservices.projects.locations.httpRoutes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v1beta1/{+parent}/httpRoutes',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsHttpRoutesListRequest',
        response_type_name='ListHttpRoutesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single HttpRoute.

      Args:
        request: (NetworkservicesProjectsLocationsHttpRoutesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/httpRoutes/{httpRoutesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.httpRoutes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='httpRoute',
        request_type_name='NetworkservicesProjectsLocationsHttpRoutesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsLbEdgeExtensionsService(base_api.BaseApiService):
    """Service class for the projects_locations_lbEdgeExtensions resource."""

    _NAME = 'projects_locations_lbEdgeExtensions'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsLbEdgeExtensionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `LbEdgeExtension` resource in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsLbEdgeExtensionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbEdgeExtensions',
        http_method='POST',
        method_id='networkservices.projects.locations.lbEdgeExtensions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['lbEdgeExtensionId', 'requestId'],
        relative_path='v1beta1/{+parent}/lbEdgeExtensions',
        request_field='lbEdgeExtension',
        request_type_name='NetworkservicesProjectsLocationsLbEdgeExtensionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified `LbEdgeExtension` resource.

      Args:
        request: (NetworkservicesProjectsLocationsLbEdgeExtensionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbEdgeExtensions/{lbEdgeExtensionsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.lbEdgeExtensions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbEdgeExtensionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of the specified `LbEdgeExtension` resource.

      Args:
        request: (NetworkservicesProjectsLocationsLbEdgeExtensionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LbEdgeExtension) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbEdgeExtensions/{lbEdgeExtensionsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.lbEdgeExtensions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbEdgeExtensionsGetRequest',
        response_type_name='LbEdgeExtension',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `LbEdgeExtension` resources in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsLbEdgeExtensionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLbEdgeExtensionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbEdgeExtensions',
        http_method='GET',
        method_id='networkservices.projects.locations.lbEdgeExtensions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/lbEdgeExtensions',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbEdgeExtensionsListRequest',
        response_type_name='ListLbEdgeExtensionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of the specified `LbEdgeExtension` resource.

      Args:
        request: (NetworkservicesProjectsLocationsLbEdgeExtensionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbEdgeExtensions/{lbEdgeExtensionsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.lbEdgeExtensions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='lbEdgeExtension',
        request_type_name='NetworkservicesProjectsLocationsLbEdgeExtensionsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsLbRouteExtensionsService(base_api.BaseApiService):
    """Service class for the projects_locations_lbRouteExtensions resource."""

    _NAME = 'projects_locations_lbRouteExtensions'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsLbRouteExtensionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `LbRouteExtension` resource in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsLbRouteExtensionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions',
        http_method='POST',
        method_id='networkservices.projects.locations.lbRouteExtensions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['lbRouteExtensionId', 'requestId'],
        relative_path='v1beta1/{+parent}/lbRouteExtensions',
        request_field='lbRouteExtension',
        request_type_name='NetworkservicesProjectsLocationsLbRouteExtensionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified `LbRouteExtension` resource.

      Args:
        request: (NetworkservicesProjectsLocationsLbRouteExtensionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions/{lbRouteExtensionsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.lbRouteExtensions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbRouteExtensionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of the specified `LbRouteExtension` resource.

      Args:
        request: (NetworkservicesProjectsLocationsLbRouteExtensionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LbRouteExtension) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions/{lbRouteExtensionsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.lbRouteExtensions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbRouteExtensionsGetRequest',
        response_type_name='LbRouteExtension',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `LbRouteExtension` resources in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsLbRouteExtensionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLbRouteExtensionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions',
        http_method='GET',
        method_id='networkservices.projects.locations.lbRouteExtensions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/lbRouteExtensions',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbRouteExtensionsListRequest',
        response_type_name='ListLbRouteExtensionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of the specified `LbRouteExtension` resource.

      Args:
        request: (NetworkservicesProjectsLocationsLbRouteExtensionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions/{lbRouteExtensionsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.lbRouteExtensions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='lbRouteExtension',
        request_type_name='NetworkservicesProjectsLocationsLbRouteExtensionsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsLbTrafficExtensionsService(base_api.BaseApiService):
    """Service class for the projects_locations_lbTrafficExtensions resource."""

    _NAME = 'projects_locations_lbTrafficExtensions'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsLbTrafficExtensionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `LbTrafficExtension` resource in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsLbTrafficExtensionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions',
        http_method='POST',
        method_id='networkservices.projects.locations.lbTrafficExtensions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['lbTrafficExtensionId', 'requestId'],
        relative_path='v1beta1/{+parent}/lbTrafficExtensions',
        request_field='lbTrafficExtension',
        request_type_name='NetworkservicesProjectsLocationsLbTrafficExtensionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified `LbTrafficExtension` resource.

      Args:
        request: (NetworkservicesProjectsLocationsLbTrafficExtensionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions/{lbTrafficExtensionsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.lbTrafficExtensions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbTrafficExtensionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of the specified `LbTrafficExtension` resource.

      Args:
        request: (NetworkservicesProjectsLocationsLbTrafficExtensionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LbTrafficExtension) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions/{lbTrafficExtensionsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.lbTrafficExtensions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbTrafficExtensionsGetRequest',
        response_type_name='LbTrafficExtension',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `LbTrafficExtension` resources in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsLbTrafficExtensionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLbTrafficExtensionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions',
        http_method='GET',
        method_id='networkservices.projects.locations.lbTrafficExtensions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/lbTrafficExtensions',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbTrafficExtensionsListRequest',
        response_type_name='ListLbTrafficExtensionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of the specified `LbTrafficExtension` resource.

      Args:
        request: (NetworkservicesProjectsLocationsLbTrafficExtensionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions/{lbTrafficExtensionsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.lbTrafficExtensions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='lbTrafficExtension',
        request_type_name='NetworkservicesProjectsLocationsLbTrafficExtensionsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMeshesRouteViewsService(base_api.BaseApiService):
    """Service class for the projects_locations_meshes_routeViews resource."""

    _NAME = 'projects_locations_meshes_routeViews'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsMeshesRouteViewsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get a single RouteView of a Mesh.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesRouteViewsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MeshRouteView) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}/routeViews/{routeViewsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.meshes.routeViews.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMeshesRouteViewsGetRequest',
        response_type_name='MeshRouteView',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists RouteViews.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesRouteViewsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMeshRouteViewsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}/routeViews',
        http_method='GET',
        method_id='networkservices.projects.locations.meshes.routeViews.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/routeViews',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMeshesRouteViewsListRequest',
        response_type_name='ListMeshRouteViewsResponse',
        supports_download=False,
    )

  class ProjectsLocationsMeshesService(base_api.BaseApiService):
    """Service class for the projects_locations_meshes resource."""

    _NAME = 'projects_locations_meshes'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsMeshesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Mesh in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/meshes',
        http_method='POST',
        method_id='networkservices.projects.locations.meshes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['meshId'],
        relative_path='v1beta1/{+parent}/meshes',
        request_field='mesh',
        request_type_name='NetworkservicesProjectsLocationsMeshesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Mesh.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.meshes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMeshesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Mesh.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Mesh) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.meshes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMeshesGetRequest',
        response_type_name='Mesh',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Meshes in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMeshesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/meshes',
        http_method='GET',
        method_id='networkservices.projects.locations.meshes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v1beta1/{+parent}/meshes',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMeshesListRequest',
        response_type_name='ListMeshesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Mesh.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.meshes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='mesh',
        request_type_name='NetworkservicesProjectsLocationsMeshesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMulticastConsumerAssociationsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicastConsumerAssociations resource."""

    _NAME = 'projects_locations_multicastConsumerAssociations'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsMulticastConsumerAssociationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new multicast consumer association in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastConsumerAssociationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastConsumerAssociations',
        http_method='POST',
        method_id='networkservices.projects.locations.multicastConsumerAssociations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicastConsumerAssociationId', 'requestId'],
        relative_path='v1beta1/{+parent}/multicastConsumerAssociations',
        request_field='multicastConsumerAssociation',
        request_type_name='NetworkservicesProjectsLocationsMulticastConsumerAssociationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single multicast consumer association.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastConsumerAssociationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastConsumerAssociations/{multicastConsumerAssociationsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.multicastConsumerAssociations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastConsumerAssociationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single multicast consumer association.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastConsumerAssociationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MulticastConsumerAssociation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastConsumerAssociations/{multicastConsumerAssociationsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastConsumerAssociations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastConsumerAssociationsGetRequest',
        response_type_name='MulticastConsumerAssociation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists multicast consumer associations in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastConsumerAssociationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMulticastConsumerAssociationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastConsumerAssociations',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastConsumerAssociations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/multicastConsumerAssociations',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastConsumerAssociationsListRequest',
        response_type_name='ListMulticastConsumerAssociationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single multicast consumer association.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastConsumerAssociationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastConsumerAssociations/{multicastConsumerAssociationsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.multicastConsumerAssociations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='multicastConsumerAssociation',
        request_type_name='NetworkservicesProjectsLocationsMulticastConsumerAssociationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMulticastDomainActivationsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicastDomainActivations resource."""

    _NAME = 'projects_locations_multicastDomainActivations'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsMulticastDomainActivationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new multicast domain activation in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainActivationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomainActivations',
        http_method='POST',
        method_id='networkservices.projects.locations.multicastDomainActivations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicastDomainActivationId', 'requestId'],
        relative_path='v1beta1/{+parent}/multicastDomainActivations',
        request_field='multicastDomainActivation',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainActivationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single multicast domain activation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainActivationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomainActivations/{multicastDomainActivationsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.multicastDomainActivations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainActivationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single multicast domain activation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainActivationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MulticastDomainActivation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomainActivations/{multicastDomainActivationsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastDomainActivations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainActivationsGetRequest',
        response_type_name='MulticastDomainActivation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists multicast domain activations in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainActivationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMulticastDomainActivationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomainActivations',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastDomainActivations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/multicastDomainActivations',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainActivationsListRequest',
        response_type_name='ListMulticastDomainActivationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single multicast domain activation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainActivationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomainActivations/{multicastDomainActivationsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.multicastDomainActivations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='multicastDomainActivation',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainActivationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMulticastDomainGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicastDomainGroups resource."""

    _NAME = 'projects_locations_multicastDomainGroups'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsMulticastDomainGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new multicast domain group in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomainGroups',
        http_method='POST',
        method_id='networkservices.projects.locations.multicastDomainGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicastDomainGroupId', 'requestId'],
        relative_path='v1beta1/{+parent}/multicastDomainGroups',
        request_field='multicastDomainGroup',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single multicast domain.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomainGroups/{multicastDomainGroupsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.multicastDomainGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single multicast domain group.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MulticastDomainGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomainGroups/{multicastDomainGroupsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastDomainGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainGroupsGetRequest',
        response_type_name='MulticastDomainGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists multicast domain groups in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMulticastDomainGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomainGroups',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastDomainGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/multicastDomainGroups',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainGroupsListRequest',
        response_type_name='ListMulticastDomainGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single multicast domain group.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomainGroups/{multicastDomainGroupsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.multicastDomainGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='multicastDomainGroup',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMulticastDomainsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicastDomains resource."""

    _NAME = 'projects_locations_multicastDomains'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsMulticastDomainsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new multicast domain in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomains',
        http_method='POST',
        method_id='networkservices.projects.locations.multicastDomains.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicastDomainId', 'requestId'],
        relative_path='v1beta1/{+parent}/multicastDomains',
        request_field='multicastDomain',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single multicast domain.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomains/{multicastDomainsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.multicastDomains.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single multicast domain.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MulticastDomain) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomains/{multicastDomainsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastDomains.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainsGetRequest',
        response_type_name='MulticastDomain',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists multicast domains in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMulticastDomainsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomains',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastDomains.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/multicastDomains',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainsListRequest',
        response_type_name='ListMulticastDomainsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single multicast domain.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastDomains/{multicastDomainsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.multicastDomains.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='multicastDomain',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMulticastGroupConsumerActivationsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicastGroupConsumerActivations resource."""

    _NAME = 'projects_locations_multicastGroupConsumerActivations'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsMulticastGroupConsumerActivationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new multicast group consumer activation in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupConsumerActivations',
        http_method='POST',
        method_id='networkservices.projects.locations.multicastGroupConsumerActivations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicastGroupConsumerActivationId', 'requestId'],
        relative_path='v1beta1/{+parent}/multicastGroupConsumerActivations',
        request_field='multicastGroupConsumerActivation',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single multicast group consumer activation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupConsumerActivations/{multicastGroupConsumerActivationsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.multicastGroupConsumerActivations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single multicast group consumer activation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MulticastGroupConsumerActivation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupConsumerActivations/{multicastGroupConsumerActivationsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastGroupConsumerActivations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsGetRequest',
        response_type_name='MulticastGroupConsumerActivation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists multicast group consumer activations in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMulticastGroupConsumerActivationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupConsumerActivations',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastGroupConsumerActivations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/multicastGroupConsumerActivations',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsListRequest',
        response_type_name='ListMulticastGroupConsumerActivationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single multicast group consumer activation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupConsumerActivations/{multicastGroupConsumerActivationsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.multicastGroupConsumerActivations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='multicastGroupConsumerActivation',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupConsumerActivationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMulticastGroupProducerActivationsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicastGroupProducerActivations resource."""

    _NAME = 'projects_locations_multicastGroupProducerActivations'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsMulticastGroupProducerActivationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new multicast group consumer activation in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupProducerActivationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupProducerActivations',
        http_method='POST',
        method_id='networkservices.projects.locations.multicastGroupProducerActivations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicastGroupProducerActivationId', 'requestId'],
        relative_path='v1beta1/{+parent}/multicastGroupProducerActivations',
        request_field='multicastGroupProducerActivation',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupProducerActivationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single multicast group consumer activation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupProducerActivationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupProducerActivations/{multicastGroupProducerActivationsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.multicastGroupProducerActivations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupProducerActivationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single multicast group consumer activation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupProducerActivationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MulticastGroupProducerActivation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupProducerActivations/{multicastGroupProducerActivationsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastGroupProducerActivations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupProducerActivationsGetRequest',
        response_type_name='MulticastGroupProducerActivation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists multicast group consumer activations in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupProducerActivationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMulticastGroupProducerActivationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupProducerActivations',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastGroupProducerActivations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/multicastGroupProducerActivations',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupProducerActivationsListRequest',
        response_type_name='ListMulticastGroupProducerActivationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single multicast group consumer activation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupProducerActivationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupProducerActivations/{multicastGroupProducerActivationsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.multicastGroupProducerActivations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='multicastGroupProducerActivation',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupProducerActivationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMulticastGroupRangeActivationsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicastGroupRangeActivations resource."""

    _NAME = 'projects_locations_multicastGroupRangeActivations'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsMulticastGroupRangeActivationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new multicast group range activation in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupRangeActivationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupRangeActivations',
        http_method='POST',
        method_id='networkservices.projects.locations.multicastGroupRangeActivations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicastGroupRangeActivationId', 'requestId'],
        relative_path='v1beta1/{+parent}/multicastGroupRangeActivations',
        request_field='multicastGroupRangeActivation',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupRangeActivationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single multicast group range activation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupRangeActivationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupRangeActivations/{multicastGroupRangeActivationsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.multicastGroupRangeActivations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupRangeActivationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single multicast group range activation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupRangeActivationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MulticastGroupRangeActivation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupRangeActivations/{multicastGroupRangeActivationsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastGroupRangeActivations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupRangeActivationsGetRequest',
        response_type_name='MulticastGroupRangeActivation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists multicast group range activationsin a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupRangeActivationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMulticastGroupRangeActivationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupRangeActivations',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastGroupRangeActivations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/multicastGroupRangeActivations',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupRangeActivationsListRequest',
        response_type_name='ListMulticastGroupRangeActivationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single multicast group range activation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupRangeActivationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupRangeActivations/{multicastGroupRangeActivationsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.multicastGroupRangeActivations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='multicastGroupRangeActivation',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupRangeActivationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMulticastGroupRangesService(base_api.BaseApiService):
    """Service class for the projects_locations_multicastGroupRanges resource."""

    _NAME = 'projects_locations_multicastGroupRanges'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsMulticastGroupRangesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new multicast group range in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupRangesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupRanges',
        http_method='POST',
        method_id='networkservices.projects.locations.multicastGroupRanges.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicastGroupRangeId', 'requestId'],
        relative_path='v1beta1/{+parent}/multicastGroupRanges',
        request_field='multicastGroupRange',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupRangesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single multicast group range.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupRangesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupRanges/{multicastGroupRangesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.multicastGroupRanges.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupRangesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single multicast group range.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupRangesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MulticastGroupRange) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupRanges/{multicastGroupRangesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastGroupRanges.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupRangesGetRequest',
        response_type_name='MulticastGroupRange',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists multicast group ranges in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupRangesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMulticastGroupRangesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupRanges',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastGroupRanges.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/multicastGroupRanges',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupRangesListRequest',
        response_type_name='ListMulticastGroupRangesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single multicast group range.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupRangesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastGroupRanges/{multicastGroupRangesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.multicastGroupRanges.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='multicastGroupRange',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupRangesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMulticastProducerAssociationsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicastProducerAssociations resource."""

    _NAME = 'projects_locations_multicastProducerAssociations'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsMulticastProducerAssociationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new multicast producer association in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastProducerAssociationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastProducerAssociations',
        http_method='POST',
        method_id='networkservices.projects.locations.multicastProducerAssociations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicastProducerAssociationId', 'requestId'],
        relative_path='v1beta1/{+parent}/multicastProducerAssociations',
        request_field='multicastProducerAssociation',
        request_type_name='NetworkservicesProjectsLocationsMulticastProducerAssociationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single multicast consumer association.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastProducerAssociationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastProducerAssociations/{multicastProducerAssociationsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.multicastProducerAssociations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastProducerAssociationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single multicast producer association.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastProducerAssociationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MulticastProducerAssociation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastProducerAssociations/{multicastProducerAssociationsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastProducerAssociations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastProducerAssociationsGetRequest',
        response_type_name='MulticastProducerAssociation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists multicast producer associations in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastProducerAssociationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMulticastProducerAssociationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastProducerAssociations',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastProducerAssociations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/multicastProducerAssociations',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastProducerAssociationsListRequest',
        response_type_name='ListMulticastProducerAssociationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single multicast consumer association.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastProducerAssociationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/multicastProducerAssociations/{multicastProducerAssociationsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.multicastProducerAssociations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='multicastProducerAssociation',
        request_type_name='NetworkservicesProjectsLocationsMulticastProducerAssociationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (NetworkservicesProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='networkservices.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='NetworkservicesProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (NetworkservicesProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (NetworkservicesProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (NetworkservicesProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='networkservices.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+name}/operations',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsServiceBindingsService(base_api.BaseApiService):
    """Service class for the projects_locations_serviceBindings resource."""

    _NAME = 'projects_locations_serviceBindings'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsServiceBindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ServiceBinding in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsServiceBindingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/serviceBindings',
        http_method='POST',
        method_id='networkservices.projects.locations.serviceBindings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['serviceBindingId'],
        relative_path='v1beta1/{+parent}/serviceBindings',
        request_field='serviceBinding',
        request_type_name='NetworkservicesProjectsLocationsServiceBindingsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ServiceBinding.

      Args:
        request: (NetworkservicesProjectsLocationsServiceBindingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/serviceBindings/{serviceBindingsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.serviceBindings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsServiceBindingsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ServiceBinding.

      Args:
        request: (NetworkservicesProjectsLocationsServiceBindingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceBinding) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/serviceBindings/{serviceBindingsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.serviceBindings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsServiceBindingsGetRequest',
        response_type_name='ServiceBinding',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ServiceBinding in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsServiceBindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/serviceBindings',
        http_method='GET',
        method_id='networkservices.projects.locations.serviceBindings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/serviceBindings',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsServiceBindingsListRequest',
        response_type_name='ListServiceBindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single ServiceBinding.

      Args:
        request: (NetworkservicesProjectsLocationsServiceBindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/serviceBindings/{serviceBindingsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.serviceBindings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='serviceBinding',
        request_type_name='NetworkservicesProjectsLocationsServiceBindingsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsServiceLbPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_serviceLbPolicies resource."""

    _NAME = 'projects_locations_serviceLbPolicies'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsServiceLbPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ServiceLbPolicy in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsServiceLbPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies',
        http_method='POST',
        method_id='networkservices.projects.locations.serviceLbPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['serviceLbPolicyId'],
        relative_path='v1beta1/{+parent}/serviceLbPolicies',
        request_field='serviceLbPolicy',
        request_type_name='NetworkservicesProjectsLocationsServiceLbPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ServiceLbPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsServiceLbPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies/{serviceLbPoliciesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.serviceLbPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsServiceLbPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ServiceLbPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsServiceLbPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceLbPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies/{serviceLbPoliciesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.serviceLbPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsServiceLbPoliciesGetRequest',
        response_type_name='ServiceLbPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ServiceLbPolicies in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsServiceLbPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceLbPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies',
        http_method='GET',
        method_id='networkservices.projects.locations.serviceLbPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/serviceLbPolicies',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsServiceLbPoliciesListRequest',
        response_type_name='ListServiceLbPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single ServiceLbPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsServiceLbPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies/{serviceLbPoliciesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.serviceLbPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='serviceLbPolicy',
        request_type_name='NetworkservicesProjectsLocationsServiceLbPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsTcpRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_tcpRoutes resource."""

    _NAME = 'projects_locations_tcpRoutes'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsTcpRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new TcpRoute in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsTcpRoutesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tcpRoutes',
        http_method='POST',
        method_id='networkservices.projects.locations.tcpRoutes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tcpRouteId'],
        relative_path='v1beta1/{+parent}/tcpRoutes',
        request_field='tcpRoute',
        request_type_name='NetworkservicesProjectsLocationsTcpRoutesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single TcpRoute.

      Args:
        request: (NetworkservicesProjectsLocationsTcpRoutesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tcpRoutes/{tcpRoutesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.tcpRoutes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsTcpRoutesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single TcpRoute.

      Args:
        request: (NetworkservicesProjectsLocationsTcpRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TcpRoute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tcpRoutes/{tcpRoutesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.tcpRoutes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsTcpRoutesGetRequest',
        response_type_name='TcpRoute',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TcpRoute in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsTcpRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTcpRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tcpRoutes',
        http_method='GET',
        method_id='networkservices.projects.locations.tcpRoutes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v1beta1/{+parent}/tcpRoutes',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsTcpRoutesListRequest',
        response_type_name='ListTcpRoutesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single TcpRoute.

      Args:
        request: (NetworkservicesProjectsLocationsTcpRoutesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tcpRoutes/{tcpRoutesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.tcpRoutes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='tcpRoute',
        request_type_name='NetworkservicesProjectsLocationsTcpRoutesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsTlsRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_tlsRoutes resource."""

    _NAME = 'projects_locations_tlsRoutes'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsTlsRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new TlsRoute in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsTlsRoutesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tlsRoutes',
        http_method='POST',
        method_id='networkservices.projects.locations.tlsRoutes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tlsRouteId'],
        relative_path='v1beta1/{+parent}/tlsRoutes',
        request_field='tlsRoute',
        request_type_name='NetworkservicesProjectsLocationsTlsRoutesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single TlsRoute.

      Args:
        request: (NetworkservicesProjectsLocationsTlsRoutesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tlsRoutes/{tlsRoutesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.tlsRoutes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsTlsRoutesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single TlsRoute.

      Args:
        request: (NetworkservicesProjectsLocationsTlsRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TlsRoute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tlsRoutes/{tlsRoutesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.tlsRoutes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsTlsRoutesGetRequest',
        response_type_name='TlsRoute',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TlsRoute in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsTlsRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTlsRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tlsRoutes',
        http_method='GET',
        method_id='networkservices.projects.locations.tlsRoutes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v1beta1/{+parent}/tlsRoutes',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsTlsRoutesListRequest',
        response_type_name='ListTlsRoutesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single TlsRoute.

      Args:
        request: (NetworkservicesProjectsLocationsTlsRoutesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tlsRoutes/{tlsRoutesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.tlsRoutes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='tlsRoute',
        request_type_name='NetworkservicesProjectsLocationsTlsRoutesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWasmPluginsVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_wasmPlugins_versions resource."""

    _NAME = 'projects_locations_wasmPlugins_versions'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsWasmPluginsVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `WasmPluginVersion` resource in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsVersionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}/versions',
        http_method='POST',
        method_id='networkservices.projects.locations.wasmPlugins.versions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['wasmPluginVersionId'],
        relative_path='v1beta1/{+parent}/versions',
        request_field='wasmPluginVersion',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsVersionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified `WasmPluginVersion` resource.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsVersionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}/versions/{versionsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.wasmPlugins.versions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsVersionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of the specified `WasmPluginVersion` resource.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WasmPluginVersion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}/versions/{versionsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.wasmPlugins.versions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsVersionsGetRequest',
        response_type_name='WasmPluginVersion',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `WasmPluginVersion` resources in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWasmPluginVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}/versions',
        http_method='GET',
        method_id='networkservices.projects.locations.wasmPlugins.versions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/versions',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsVersionsListRequest',
        response_type_name='ListWasmPluginVersionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsWasmPluginsService(base_api.BaseApiService):
    """Service class for the projects_locations_wasmPlugins resource."""

    _NAME = 'projects_locations_wasmPlugins'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsWasmPluginsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `WasmPlugin` resource in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins',
        http_method='POST',
        method_id='networkservices.projects.locations.wasmPlugins.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['wasmPluginId'],
        relative_path='v1beta1/{+parent}/wasmPlugins',
        request_field='wasmPlugin',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified `WasmPlugin` resource.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.wasmPlugins.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of the specified `WasmPlugin` resource.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WasmPlugin) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.wasmPlugins.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsGetRequest',
        response_type_name='WasmPlugin',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `WasmPlugin` resources in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWasmPluginsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins',
        http_method='GET',
        method_id='networkservices.projects.locations.wasmPlugins.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/wasmPlugins',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsListRequest',
        response_type_name='ListWasmPluginsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of the specified `WasmPlugin` resource.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.wasmPlugins.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='wasmPlugin',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (NetworkservicesProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (NetworkservicesProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='networkservices.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+name}/locations',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(NetworkservicesV1beta1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
