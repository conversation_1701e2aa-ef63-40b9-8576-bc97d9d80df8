"""Generated message classes for networksecurity version v1alpha1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'networksecurity'


class AddAddressGroupItemsRequest(_messages.Message):
  r"""Request used by the AddAddressGroupItems method.

  Fields:
    items: Required. List of items to add.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  items = _messages.StringField(1, repeated=True)
  requestId = _messages.StringField(2)


class AddDNSPeeringZoneRequest(_messages.Message):
  r"""Request used by the AddDnsPeeringZone method.

  Fields:
    dnsSuffix: Required. DNS suffix to add to DNS peering zone.
    requestId: Optional. An optional request ID to identify requests.
    targetNetwork: Optional. An optional target VPC network override. If not
      set, it will default to the SSE Environment partner network.
  """

  dnsSuffix = _messages.StringField(1)
  requestId = _messages.StringField(2)
  targetNetwork = _messages.StringField(3)


class AddressGroup(_messages.Message):
  r"""AddressGroup is a resource that specifies how a collection of IP/DNS
  used in Firewall Policy.

  Enums:
    PurposeValueListEntryValuesEnum:
    TypeValueValuesEnum: Required. The type of the Address Group. Possible
      values are "IPv4" or "IPV6".

  Messages:
    LabelsValue: Optional. Set of label tags associated with the AddressGroup
      resource.

  Fields:
    capacity: Required. Capacity of the Address Group
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. Free-text description of the resource.
    items: Optional. List of items.
    labels: Optional. Set of label tags associated with the AddressGroup
      resource.
    name: Required. Name of the AddressGroup resource. It matches pattern
      `projects/*/locations/{location}/addressGroups/`.
    purpose: Optional. List of supported purposes of the Address Group.
    selfLink: Output only. Server-defined fully-qualified URL for this
      resource.
    type: Required. The type of the Address Group. Possible values are "IPv4"
      or "IPV6".
    updateTime: Output only. The timestamp when the resource was updated.
  """

  class PurposeValueListEntryValuesEnum(_messages.Enum):
    r"""PurposeValueListEntryValuesEnum enum type.

    Values:
      PURPOSE_UNSPECIFIED: Default value. Should never happen.
      DEFAULT: Address Group is distributed to VMC, and is usable in Firewall
        Policies and other systems that rely on VMC.
      CLOUD_ARMOR: Address Group is usable in Cloud Armor.
    """
    PURPOSE_UNSPECIFIED = 0
    DEFAULT = 1
    CLOUD_ARMOR = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of the Address Group. Possible values are "IPv4" or
    "IPV6".

    Values:
      TYPE_UNSPECIFIED: Default value.
      IPV4: IP v4 ranges.
      IPV6: IP v6 ranges.
    """
    TYPE_UNSPECIFIED = 0
    IPV4 = 1
    IPV6 = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the AddressGroup resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  capacity = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  items = _messages.StringField(4, repeated=True)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  purpose = _messages.EnumField('PurposeValueListEntryValuesEnum', 7, repeated=True)
  selfLink = _messages.StringField(8)
  type = _messages.EnumField('TypeValueValuesEnum', 9)
  updateTime = _messages.StringField(10)


class AntivirusOverride(_messages.Message):
  r"""Defines what action to take for antivirus threats per protocol.

  Enums:
    ActionValueValuesEnum: Required. Threat action override. For some threat
      types, only a subset of actions applies.
    ProtocolValueValuesEnum: Required. Protocol to match.

  Fields:
    action: Required. Threat action override. For some threat types, only a
      subset of actions applies.
    protocol: Required. Protocol to match.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. Threat action override. For some threat types, only a subset
    of actions applies.

    Values:
      THREAT_ACTION_UNSPECIFIED: Threat action not specified.
      DEFAULT_ACTION: The default action (as specified by the vendor) is
        taken.
      ALLOW: The packet matching this rule will be allowed to transmit.
      ALERT: The packet matching this rule will be allowed to transmit, but a
        threat_log entry will be sent to the consumer project.
      DENY: The packet matching this rule will be dropped, and a threat_log
        entry will be sent to the consumer project.
    """
    THREAT_ACTION_UNSPECIFIED = 0
    DEFAULT_ACTION = 1
    ALLOW = 2
    ALERT = 3
    DENY = 4

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""Required. Protocol to match.

    Values:
      PROTOCOL_UNSPECIFIED: Protocol not specified.
      SMTP: SMTP protocol
      SMB: SMB protocol
      POP3: POP3 protocol
      IMAP: IMAP protocol
      HTTP2: HTTP2 protocol
      HTTP: HTTP protocol
      FTP: FTP protocol
    """
    PROTOCOL_UNSPECIFIED = 0
    SMTP = 1
    SMB = 2
    POP3 = 3
    IMAP = 4
    HTTP2 = 5
    HTTP = 6
    FTP = 7

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 2)


class AntivirusThreatOverride(_messages.Message):
  r"""Defines what action to take for VIRUS threats per protocol.

  Enums:
    ActionValueValuesEnum: Required. Threat action override. For some threat
      types, only a subset of actions applies.
    ProtocolValueValuesEnum: Required. Protocol to match.

  Fields:
    action: Required. Threat action override. For some threat types, only a
      subset of actions applies.
    protocol: Required. Protocol to match.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. Threat action override. For some threat types, only a subset
    of actions applies.

    Values:
      THREAT_ACTION_UNSPECIFIED: Threat action not specified.
      DEFAULT_ACTION: The default action (as specified by the vendor) is
        taken.
      ALLOW: The packet matching this rule will be allowed to transmit.
      ALERT: The packet matching this rule will be allowed to transmit, but a
        threat_log entry will be sent to the consumer project.
      DENY: The packet matching this rule will be dropped, and a threat_log
        entry will be sent to the consumer project.
    """
    THREAT_ACTION_UNSPECIFIED = 0
    DEFAULT_ACTION = 1
    ALLOW = 2
    ALERT = 3
    DENY = 4

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""Required. Protocol to match.

    Values:
      PROTOCOL_UNSPECIFIED: Protocol not specified.
      SMTP: SMTP protocol
      SMB: SMB protocol
      POP3: POP3 protocol
      IMAP: IMAP protocol
      HTTP2: HTTP2 protocol
      HTTP: HTTP protocol
      FTP: FTP protocol
    """
    PROTOCOL_UNSPECIFIED = 0
    SMTP = 1
    SMB = 2
    POP3 = 3
    IMAP = 4
    HTTP2 = 5
    HTTP = 6
    FTP = 7

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 2)


class AuthorizationPolicy(_messages.Message):
  r"""AuthorizationPolicy is a resource that specifies how a server should
  authorize incoming connections. This resource in itself does not change the
  configuration unless it's attached to a target https proxy or endpoint
  config selector resource.

  Enums:
    ActionValueValuesEnum: Required. The action to take when a rule match is
      found. Possible values are "ALLOW" or "DENY".

  Messages:
    LabelsValue: Optional. Set of label tags associated with the
      AuthorizationPolicy resource.

  Fields:
    action: Required. The action to take when a rule match is found. Possible
      values are "ALLOW" or "DENY".
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. Free-text description of the resource.
    labels: Optional. Set of label tags associated with the
      AuthorizationPolicy resource.
    name: Required. Name of the AuthorizationPolicy resource. It matches
      pattern
      `projects/{project}/locations/{location}/authorizationPolicies/`.
    rules: Optional. List of rules to match. Note that at least one of the
      rules must match in order for the action specified in the 'action' field
      to be taken. A rule is a match if there is a matching source and
      destination. If left blank, the action specified in the `action` field
      will be applied on every request.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. The action to take when a rule match is found. Possible
    values are "ALLOW" or "DENY".

    Values:
      ACTION_UNSPECIFIED: Default value.
      ALLOW: Grant access.
      DENY: Deny access. Deny rules should be avoided unless they are used to
        provide a default "deny all" fallback.
    """
    ACTION_UNSPECIFIED = 0
    ALLOW = 1
    DENY = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the AuthorizationPolicy
    resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  rules = _messages.MessageField('Rule', 6, repeated=True)
  updateTime = _messages.StringField(7)


class AuthzPolicy(_messages.Message):
  r"""`AuthzPolicy` is a resource that allows to forward traffic to a callout
  backend designed to scan the traffic for security purposes.

  Enums:
    ActionValueValuesEnum: Required. Can be one of `ALLOW`, `DENY`, `CUSTOM`.
      When the action is `CUSTOM`, `customProvider` must be specified. When
      the action is `ALLOW`, only requests matching the policy will be
      allowed. When the action is `DENY`, only requests matching the policy
      will be denied. When a request arrives, the policies are evaluated in
      the following order: 1. If there is a `CUSTOM` policy that matches the
      request, the `CUSTOM` policy is evaluated using the custom authorization
      providers and the request is denied if the provider rejects the request.
      2. If there are any `DENY` policies that match the request, the request
      is denied. 3. If there are no `ALLOW` policies for the resource or if
      any of the `ALLOW` policies match the request, the request is allowed.
      4. Else the request is denied by default if none of the configured
      AuthzPolicies with `ALLOW` action match the request.

  Messages:
    LabelsValue: Optional. Set of labels associated with the `AuthzPolicy`
      resource. The format must comply with [the following
      requirements](/compute/docs/labeling-resources#requirements).

  Fields:
    action: Required. Can be one of `ALLOW`, `DENY`, `CUSTOM`. When the action
      is `CUSTOM`, `customProvider` must be specified. When the action is
      `ALLOW`, only requests matching the policy will be allowed. When the
      action is `DENY`, only requests matching the policy will be denied. When
      a request arrives, the policies are evaluated in the following order: 1.
      If there is a `CUSTOM` policy that matches the request, the `CUSTOM`
      policy is evaluated using the custom authorization providers and the
      request is denied if the provider rejects the request. 2. If there are
      any `DENY` policies that match the request, the request is denied. 3. If
      there are no `ALLOW` policies for the resource or if any of the `ALLOW`
      policies match the request, the request is allowed. 4. Else the request
      is denied by default if none of the configured AuthzPolicies with
      `ALLOW` action match the request.
    createTime: Output only. The timestamp when the resource was created.
    customProvider: Optional. Required if the action is `CUSTOM`. Allows
      delegating authorization decisions to Cloud IAP or to Service
      Extensions. One of `cloudIap` or `authzExtension` must be specified.
    description: Optional. A human-readable description of the resource.
    httpRules: Optional. A list of authorization HTTP rules to match against
      the incoming request. A policy match occurs when at least one HTTP rule
      matches the request or when no HTTP rules are specified in the policy.
      At least one HTTP Rule is required for Allow or Deny Action. Limited to
      5 rules.
    labels: Optional. Set of labels associated with the `AuthzPolicy`
      resource. The format must comply with [the following
      requirements](/compute/docs/labeling-resources#requirements).
    name: Required. Identifier. Name of the `AuthzPolicy` resource in the
      following format:
      `projects/{project}/locations/{location}/authzPolicies/{authz_policy}`.
    target: Required. Specifies the set of resources to which this policy
      should be applied to.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. Can be one of `ALLOW`, `DENY`, `CUSTOM`. When the action is
    `CUSTOM`, `customProvider` must be specified. When the action is `ALLOW`,
    only requests matching the policy will be allowed. When the action is
    `DENY`, only requests matching the policy will be denied. When a request
    arrives, the policies are evaluated in the following order: 1. If there is
    a `CUSTOM` policy that matches the request, the `CUSTOM` policy is
    evaluated using the custom authorization providers and the request is
    denied if the provider rejects the request. 2. If there are any `DENY`
    policies that match the request, the request is denied. 3. If there are no
    `ALLOW` policies for the resource or if any of the `ALLOW` policies match
    the request, the request is allowed. 4. Else the request is denied by
    default if none of the configured AuthzPolicies with `ALLOW` action match
    the request.

    Values:
      AUTHZ_ACTION_UNSPECIFIED: Unspecified action.
      ALLOW: Allow request to pass through to the backend.
      DENY: Deny the request and return a HTTP 404 to the client.
      CUSTOM: Delegate the authorization decision to an external authorization
        engine.
    """
    AUTHZ_ACTION_UNSPECIFIED = 0
    ALLOW = 1
    DENY = 2
    CUSTOM = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of labels associated with the `AuthzPolicy` resource.
    The format must comply with [the following
    requirements](/compute/docs/labeling-resources#requirements).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  customProvider = _messages.MessageField('AuthzPolicyCustomProvider', 3)
  description = _messages.StringField(4)
  httpRules = _messages.MessageField('AuthzPolicyAuthzRule', 5, repeated=True)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  target = _messages.MessageField('AuthzPolicyTarget', 8)
  updateTime = _messages.StringField(9)


class AuthzPolicyAuthzRule(_messages.Message):
  r"""Conditions to match against the incoming request.

  Fields:
    from_: Optional. Describes properties of a source of a request.
    to: Optional. Describes properties of a target of a request.
    when: Optional. CEL expression that describes the conditions to be
      satisfied for the action. The result of the CEL expression is ANDed with
      the from and to. Refer to the CEL language reference for a list of
      available attributes.
  """

  from_ = _messages.MessageField('AuthzPolicyAuthzRuleFrom', 1)
  to = _messages.MessageField('AuthzPolicyAuthzRuleTo', 2)
  when = _messages.StringField(3)


class AuthzPolicyAuthzRuleFrom(_messages.Message):
  r"""Describes properties of one or more sources of a request.

  Fields:
    notSources: Optional. Describes the negated properties of request sources.
      Matches requests from sources that do not match the criteria specified
      in this field. At least one of sources or notSources must be specified.
    sources: Optional. Describes the properties of a request's sources. At
      least one of sources or notSources must be specified. Limited to 1
      source. A match occurs when ANY source (in sources or notSources)
      matches the request. Within a single source, the match follows AND
      semantics across fields and OR semantics within a single field, i.e. a
      match occurs when ANY principal matches AND ANY ipBlocks match.
  """

  notSources = _messages.MessageField('AuthzPolicyAuthzRuleFromRequestSource', 1, repeated=True)
  sources = _messages.MessageField('AuthzPolicyAuthzRuleFromRequestSource', 2, repeated=True)


class AuthzPolicyAuthzRuleFromRequestSource(_messages.Message):
  r"""Describes the properties of a single source.

  Fields:
    ipBlocks: Optional. A list of IP addresses or IP address ranges to match
      against the source IP address of the request. Limited to 5 ip_blocks.
    principals: Optional. A list of identities derived from the client's
      certificate. This field will not match on a request unless frontend
      mutual TLS is enabled for the forwarding rule or Gateway and the client
      certificate has been successfully validated by mTLS. Each identity is a
      string whose value is matched against a list of URI SANs, DNS Name SANs,
      or the common name in the client's certificate. A match happens when any
      principal matches with the rule. Limited to 5 principals.
    resources: Optional. A list of resources to match against the resource of
      the source VM of a request. Limited to 5 resources.
  """

  ipBlocks = _messages.MessageField('AuthzPolicyAuthzRuleIpBlock', 1, repeated=True)
  principals = _messages.MessageField('AuthzPolicyAuthzRulePrincipal', 2, repeated=True)
  resources = _messages.MessageField('AuthzPolicyAuthzRuleRequestResource', 3, repeated=True)


class AuthzPolicyAuthzRuleHeaderMatch(_messages.Message):
  r"""Determines how a HTTP header should be matched.

  Fields:
    name: Optional. Specifies the name of the header in the request.
    value: Optional. Specifies how the header match will be performed.
  """

  name = _messages.StringField(1)
  value = _messages.MessageField('AuthzPolicyAuthzRuleStringMatch', 2)


class AuthzPolicyAuthzRuleIpBlock(_messages.Message):
  r"""Represents a range of IP Addresses.

  Fields:
    length: Required. The length of the address range.
    prefix: Required. The address prefix.
  """

  length = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  prefix = _messages.StringField(2)


class AuthzPolicyAuthzRulePrincipal(_messages.Message):
  r"""Describes the properties of a principal to be matched against.

  Enums:
    PrincipalSelectorValueValuesEnum: Optional. An enum to decide what
      principal value the principal rule will match against. If not specified,
      the PrincipalSelector is CLIENT_CERT_URI_SAN.

  Fields:
    principal: Required. A non-empty string whose value is matched against the
      principal value based on the principal_selector. Only exact match can be
      applied for CLIENT_CERT_URI_SAN, CLIENT_CERT_DNS_NAME_SAN,
      CLIENT_CERT_COMMON_NAME selectors.
    principalSelector: Optional. An enum to decide what principal value the
      principal rule will match against. If not specified, the
      PrincipalSelector is CLIENT_CERT_URI_SAN.
  """

  class PrincipalSelectorValueValuesEnum(_messages.Enum):
    r"""Optional. An enum to decide what principal value the principal rule
    will match against. If not specified, the PrincipalSelector is
    CLIENT_CERT_URI_SAN.

    Values:
      PRINCIPAL_SELECTOR_UNSPECIFIED: Unspecified principal selector. It will
        be treated as CLIENT_CERT_URI_SAN by default.
      CLIENT_CERT_URI_SAN: The principal rule is matched against a list of URI
        SANs in the validated client's certificate. A match happens when there
        is any exact URI SAN value match. This is the default principal
        selector.
      CLIENT_CERT_DNS_NAME_SAN: The principal rule is matched against a list
        of DNS Name SANs in the validated client's certificate. A match
        happens when there is any exact DNS Name SAN value match.
      CLIENT_CERT_COMMON_NAME: The principal rule is matched against the
        common name in the client's certificate. Authorization against
        multiple common names in the client certificate is not supported.
        Requests with multiple common names in the client certificate will be
        rejected if CLIENT_CERT_COMMON_NAME is set as the principal selector.
        A match happens when there is an exact common name value match. This
        is only applicable for Application Load Balancers except for classic
        Global External Application load balancer. CLIENT_CERT_COMMON_NAME is
        not supported for INTERNAL_SELF_MANAGED load balancing scheme.
    """
    PRINCIPAL_SELECTOR_UNSPECIFIED = 0
    CLIENT_CERT_URI_SAN = 1
    CLIENT_CERT_DNS_NAME_SAN = 2
    CLIENT_CERT_COMMON_NAME = 3

  principal = _messages.MessageField('AuthzPolicyAuthzRuleStringMatch', 1)
  principalSelector = _messages.EnumField('PrincipalSelectorValueValuesEnum', 2)


class AuthzPolicyAuthzRuleRequestResource(_messages.Message):
  r"""Describes the properties of a client VM resource accessing the internal
  application load balancers.

  Fields:
    iamServiceAccount: Optional. An IAM service account to match against the
      source service account of the VM sending the request.
    tagValueIdSet: Optional. A list of resource tag value permanent IDs to
      match against the resource manager tags value associated with the source
      VM of a request.
  """

  iamServiceAccount = _messages.MessageField('AuthzPolicyAuthzRuleStringMatch', 1)
  tagValueIdSet = _messages.MessageField('AuthzPolicyAuthzRuleRequestResourceTagValueIdSet', 2)


class AuthzPolicyAuthzRuleRequestResourceTagValueIdSet(_messages.Message):
  r"""Describes a set of resource tag value permanent IDs to match against the
  resource manager tags value associated with the source VM of a request.

  Fields:
    ids: Required. A list of resource tag value permanent IDs to match against
      the resource manager tags value associated with the source VM of a
      request. The match follows AND semantics which means all the ids must
      match. Limited to 5 matches.
  """

  ids = _messages.IntegerField(1, repeated=True)


class AuthzPolicyAuthzRuleStringMatch(_messages.Message):
  r"""Determines how a string value should be matched.

  Fields:
    contains: The input string must have the substring specified here. Note:
      empty contains match is not allowed, please use regex instead. Examples:
      * ``abc`` matches the value ``xyz.abc.def``
    exact: The input string must match exactly the string specified here.
      Examples: * ``abc`` only matches the value ``abc``.
    ignoreCase: If true, indicates the exact/prefix/suffix/contains matching
      should be case insensitive. For example, the matcher ``data`` will match
      both input string ``Data`` and ``data`` if set to true.
    prefix: The input string must have the prefix specified here. Note: empty
      prefix is not allowed, please use regex instead. Examples: * ``abc``
      matches the value ``abc.xyz``
    suffix: The input string must have the suffix specified here. Note: empty
      prefix is not allowed, please use regex instead. Examples: * ``abc``
      matches the value ``xyz.abc``
  """

  contains = _messages.StringField(1)
  exact = _messages.StringField(2)
  ignoreCase = _messages.BooleanField(3)
  prefix = _messages.StringField(4)
  suffix = _messages.StringField(5)


class AuthzPolicyAuthzRuleTo(_messages.Message):
  r"""Describes properties of one or more targets of a request.

  Fields:
    notOperations: Optional. Describes the negated properties of the targets
      of a request. Matches requests for operations that do not match the
      criteria specified in this field. At least one of operations or
      notOperations must be specified.
    operations: Optional. Describes properties of one or more targets of a
      request. At least one of operations or notOperations must be specified.
      Limited to 1 operation. A match occurs when ANY operation (in operations
      or notOperations) matches. Within an operation, the match follows AND
      semantics across fields and OR semantics within a field, i.e. a match
      occurs when ANY path matches AND ANY header matches and ANY method
      matches.
  """

  notOperations = _messages.MessageField('AuthzPolicyAuthzRuleToRequestOperation', 1, repeated=True)
  operations = _messages.MessageField('AuthzPolicyAuthzRuleToRequestOperation', 2, repeated=True)


class AuthzPolicyAuthzRuleToRequestOperation(_messages.Message):
  r"""Describes properties of one or more targets of a request.

  Fields:
    headerSet: Optional. A list of headers to match against in http header.
    hosts: Optional. A list of HTTP Hosts to match against. The match can be
      one of exact, prefix, suffix, or contains (substring match). Matches are
      always case sensitive unless the ignoreCase is set. Limited to 5
      matches.
    methods: Optional. A list of HTTP methods to match against. Each entry
      must be a valid HTTP method name (GET, PUT, POST, HEAD, PATCH, DELETE,
      OPTIONS). It only allows exact match and is always case sensitive.
    paths: Optional. A list of paths to match against. The match can be one of
      exact, prefix, suffix, or contains (substring match). Matches are always
      case sensitive unless the ignoreCase is set. Limited to 5 matches. Note
      that this path match includes the query parameters. For gRPC services,
      this should be a fully-qualified name of the form
      /package.service/method.
  """

  headerSet = _messages.MessageField('AuthzPolicyAuthzRuleToRequestOperationHeaderSet', 1)
  hosts = _messages.MessageField('AuthzPolicyAuthzRuleStringMatch', 2, repeated=True)
  methods = _messages.StringField(3, repeated=True)
  paths = _messages.MessageField('AuthzPolicyAuthzRuleStringMatch', 4, repeated=True)


class AuthzPolicyAuthzRuleToRequestOperationHeaderSet(_messages.Message):
  r"""Describes a set of HTTP headers to match against.

  Fields:
    headers: Required. A list of headers to match against in http header. The
      match can be one of exact, prefix, suffix, or contains (substring
      match). The match follows AND semantics which means all the headers must
      match. Matches are always case sensitive unless the ignoreCase is set.
      Limited to 5 matches.
  """

  headers = _messages.MessageField('AuthzPolicyAuthzRuleHeaderMatch', 1, repeated=True)


class AuthzPolicyCustomProvider(_messages.Message):
  r"""Allows delegating authorization decisions to Cloud IAP or to Service
  Extensions.

  Fields:
    authzExtension: Optional. Delegate authorization decision to user authored
      Service Extension. Only one of cloudIap or authzExtension can be
      specified.
    cloudIap: Optional. Delegates authorization decisions to Cloud IAP.
      Applicable only for managed load balancers. Enabling Cloud IAP at the
      AuthzPolicy level is not compatible with Cloud IAP settings in the
      BackendService. Enabling IAP in both places will result in request
      failure. Ensure that IAP is enabled in either the AuthzPolicy or the
      BackendService but not in both places.
  """

  authzExtension = _messages.MessageField('AuthzPolicyCustomProviderAuthzExtension', 1)
  cloudIap = _messages.MessageField('AuthzPolicyCustomProviderCloudIap', 2)


class AuthzPolicyCustomProviderAuthzExtension(_messages.Message):
  r"""Optional. Delegate authorization decision to user authored extension.
  Only one of cloudIap or authzExtension can be specified.

  Fields:
    resources: Required. A list of references to authorization extensions that
      will be invoked for requests matching this policy. Limited to 1 custom
      provider.
  """

  resources = _messages.StringField(1, repeated=True)


class AuthzPolicyCustomProviderCloudIap(_messages.Message):
  r"""Optional. Delegates authorization decisions to Cloud IAP. Applicable
  only for managed load balancers. Enabling Cloud IAP at the AuthzPolicy level
  is not compatible with Cloud IAP settings in the BackendService. Enabling
  IAP in both places will result in request failure. Ensure that IAP is
  enabled in either the AuthzPolicy or the BackendService but not in both
  places.
  """



class AuthzPolicyTarget(_messages.Message):
  r"""Specifies the set of targets to which this policy should be applied to.

  Enums:
    LoadBalancingSchemeValueValuesEnum: Required. All gateways and forwarding
      rules referenced by this policy and extensions must share the same load
      balancing scheme. Supported values: `INTERNAL_MANAGED` and
      `EXTERNAL_MANAGED`. For more information, refer to [Backend services
      overview](https://cloud.google.com/load-balancing/docs/backend-service).

  Fields:
    loadBalancingScheme: Required. All gateways and forwarding rules
      referenced by this policy and extensions must share the same load
      balancing scheme. Supported values: `INTERNAL_MANAGED` and
      `EXTERNAL_MANAGED`. For more information, refer to [Backend services
      overview](https://cloud.google.com/load-balancing/docs/backend-service).
    resources: Required. A list of references to the Forwarding Rules on which
      this policy will be applied.
  """

  class LoadBalancingSchemeValueValuesEnum(_messages.Enum):
    r"""Required. All gateways and forwarding rules referenced by this policy
    and extensions must share the same load balancing scheme. Supported
    values: `INTERNAL_MANAGED` and `EXTERNAL_MANAGED`. For more information,
    refer to [Backend services overview](https://cloud.google.com/load-
    balancing/docs/backend-service).

    Values:
      LOAD_BALANCING_SCHEME_UNSPECIFIED: Default value. Do not use.
      INTERNAL_MANAGED: Signifies that this is used for Regional internal or
        Cross-region internal Application Load Balancing.
      EXTERNAL_MANAGED: Signifies that this is used for Global external or
        Regional external Application Load Balancing.
      INTERNAL_SELF_MANAGED: Signifies that this is used for Cloud Service
        Mesh. Meant for use by CSM GKE controller only.
    """
    LOAD_BALANCING_SCHEME_UNSPECIFIED = 0
    INTERNAL_MANAGED = 1
    EXTERNAL_MANAGED = 2
    INTERNAL_SELF_MANAGED = 3

  loadBalancingScheme = _messages.EnumField('LoadBalancingSchemeValueValuesEnum', 1)
  resources = _messages.StringField(2, repeated=True)


class BackendAuthenticationConfig(_messages.Message):
  r"""BackendAuthenticationConfig message groups the TrustConfig together with
  other settings that control how the load balancer authenticates, and
  expresses its identity to, the backend: * `trustConfig` is the attached
  TrustConfig. * `wellKnownRoots` indicates whether the load balance should
  trust backend server certificates that are issued by public certificate
  authorities, in addition to certificates trusted by the TrustConfig. *
  `clientCertificate` is a client certificate that the load balancer uses to
  express its identity to the backend, if the connection to the backend uses
  mTLS. You can attach the BackendAuthenticationConfig to the load balancer's
  BackendService directly determining how that BackendService negotiates TLS.

  Enums:
    WellKnownRootsValueValuesEnum: Well known roots to use for server
      certificate validation.

  Messages:
    LabelsValue: Set of label tags associated with the resource.

  Fields:
    clientCertificate: Optional. A reference to a
      certificatemanager.googleapis.com.Certificate resource. This is a
      relative resource path following the form
      "projects/{project}/locations/{location}/certificates/{certificate}".
      Used by a BackendService to negotiate mTLS when the backend connection
      uses TLS and the backend requests a client certificate. Must have a
      CLIENT_AUTH scope.
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. Free-text description of the resource.
    etag: Output only. Etag of the resource.
    labels: Set of label tags associated with the resource.
    name: Required. Name of the BackendAuthenticationConfig resource. It
      matches the pattern `projects/*/locations/{location}/backendAuthenticati
      onConfigs/{backend_authentication_config}`
    trustConfig: Optional. A reference to a TrustConfig resource from the
      certificatemanager.googleapis.com namespace. This is a relative resource
      path following the form
      "projects/{project}/locations/{location}/trustConfigs/{trust_config}". A
      BackendService uses the chain of trust represented by this TrustConfig,
      if specified, to validate the server certificates presented by the
      backend. Required unless wellKnownRoots is set to PUBLIC_ROOTS.
    updateTime: Output only. The timestamp when the resource was updated.
    wellKnownRoots: Well known roots to use for server certificate validation.
  """

  class WellKnownRootsValueValuesEnum(_messages.Enum):
    r"""Well known roots to use for server certificate validation.

    Values:
      WELL_KNOWN_ROOTS_UNSPECIFIED: Equivalent to NONE.
      NONE: The BackendService will only validate server certificates against
        roots specified in TrustConfig.
      PUBLIC_ROOTS: The BackendService uses a set of well-known public roots,
        in addition to any roots specified in the trustConfig field, when
        validating the server certificates presented by the backend.
        Validation with these roots is only considered when the
        TlsSettings.sni field in the BackendService is set. The well-known
        roots are a set of root CAs managed by Google. CAs in this set can be
        added or removed without notice.
    """
    WELL_KNOWN_ROOTS_UNSPECIFIED = 0
    NONE = 1
    PUBLIC_ROOTS = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Set of label tags associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  clientCertificate = _messages.StringField(1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  trustConfig = _messages.StringField(7)
  updateTime = _messages.StringField(8)
  wellKnownRoots = _messages.EnumField('WellKnownRootsValueValuesEnum', 9)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CertificateProviderInstance(_messages.Message):
  r"""Specification of a TLS certificate provider instance. Workloads may have
  one or more CertificateProvider instances (plugins) and one of them is
  enabled and configured by specifying this message. Workloads use the values
  from this message to locate and load the CertificateProvider instance
  configuration.

  Fields:
    pluginInstance: Required. Plugin instance name, used to locate and load
      CertificateProvider instance configuration. Set to
      "google_cloud_private_spiffe" to use Certificate Authority Service
      certificate provider instance.
  """

  pluginInstance = _messages.StringField(1)


class ClientTlsPolicy(_messages.Message):
  r"""ClientTlsPolicy is a resource that specifies how a client should
  authenticate connections to backends of a service. This resource itself does
  not affect configuration unless it is attached to a backend service
  resource.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the resource.

  Fields:
    clientCertificate: Optional. Defines a mechanism to provision client
      identity (public and private keys) for peer to peer authentication. The
      presence of this dictates mTLS.
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. Free-text description of the resource.
    labels: Optional. Set of label tags associated with the resource.
    name: Required. Name of the ClientTlsPolicy resource. It matches the
      pattern `projects/{project}/locations/{location}/clientTlsPolicies/{clie
      nt_tls_policy}`
    serverValidationCa: Optional. Defines the mechanism to obtain the
      Certificate Authority certificate to validate the server certificate. If
      empty, client does not validate the server certificate.
    sni: Optional. Server Name Indication string to present to the server
      during TLS handshake. E.g: "secure.example.com".
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  clientCertificate = _messages.MessageField('GoogleCloudNetworksecurityV1alpha1CertificateProvider', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  serverValidationCa = _messages.MessageField('ValidationCA', 6, repeated=True)
  sni = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class CloneAddressGroupItemsRequest(_messages.Message):
  r"""Request used by the CloneAddressGroupItems method.

  Fields:
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    sourceAddressGroup: Required. Source address group to clone items from.
  """

  requestId = _messages.StringField(1)
  sourceAddressGroup = _messages.StringField(2)


class CustomInterceptProfile(_messages.Message):
  r"""CustomInterceptProfile defines in-band integration behavior (intercept).
  It is used by firewall rules with an APPLY_SECURITY_PROFILE_GROUP action.

  Fields:
    interceptEndpointGroup: Required. The target InterceptEndpointGroup. When
      a firewall rule with this security profile attached matches a packet,
      the packet will be intercepted to the location-local target in this
      group.
  """

  interceptEndpointGroup = _messages.StringField(1)


class CustomMirroringProfile(_messages.Message):
  r"""CustomMirroringProfile defines out-of-band integration behavior
  (mirroring). It is used by mirroring rules with a MIRROR action.

  Enums:
    MirroringEndpointGroupTypeValueValuesEnum: Output only.

  Fields:
    mirroringDeploymentGroups: Optional. The target downstream
      MirroringDeploymentGroups. This field is used for Packet Broker
      mirroring endpoint groups to specify the deployment groups that the
      packet should be mirrored to by the broker.
    mirroringEndpointGroup: Required. The target MirroringEndpointGroup. When
      a mirroring rule with this security profile attached matches a packet, a
      replica will be mirrored to the location-local target in this group.
    mirroringEndpointGroupType: Output only.
  """

  class MirroringEndpointGroupTypeValueValuesEnum(_messages.Enum):
    r"""Output only.

    Values:
      MIRRORING_ENDPOINT_GROUP_TYPE_UNSPECIFIED: Default value. This value is
        unused.
      DIRECT: The endpoint group is a direct endpoint group.
      BROKER: The endpoint group is a broker endpoint group.
    """
    MIRRORING_ENDPOINT_GROUP_TYPE_UNSPECIFIED = 0
    DIRECT = 1
    BROKER = 2

  mirroringDeploymentGroups = _messages.StringField(1, repeated=True)
  mirroringEndpointGroup = _messages.StringField(2)
  mirroringEndpointGroupType = _messages.EnumField('MirroringEndpointGroupTypeValueValuesEnum', 3)


class Destination(_messages.Message):
  r"""Specification of traffic destination attributes.

  Fields:
    hosts: Required. List of host names to match. Matched against the
      ":authority" header in http requests. At least one host should match.
      Each host can be an exact match, or a prefix match (example
      "mydomain.*") or a suffix match (example "*.myorg.com") or a presence
      (any) match "*".
    httpHeaderMatch: Optional. Match against key:value pair in http header.
      Provides a flexible match based on HTTP headers, for potentially
      advanced use cases. At least one header should match. Avoid using header
      matches to make authorization decisions unless there is a strong
      guarantee that requests arrive through a trusted client or proxy.
    methods: Optional. A list of HTTP methods to match. At least one method
      should match. Should not be set for gRPC services.
    ports: Required. List of destination ports to match. At least one port
      should match.
  """

  hosts = _messages.StringField(1, repeated=True)
  httpHeaderMatch = _messages.MessageField('HttpHeaderMatch', 2)
  methods = _messages.StringField(3, repeated=True)
  ports = _messages.IntegerField(4, repeated=True, variant=_messages.Variant.UINT32)


class DnsThreatDetector(_messages.Message):
  r"""Message describing DnsThreatDetector object

  Enums:
    ProviderValueValuesEnum: Required. The provider used for DNS threat
      analysis.

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    createTime: Output only. [Output only] Create time stamp
    excludedNetworks: Optional. A list of Network resource names which are
      exempt from the configuration in this DnsThreatDetector. Example:
      `projects/PROJECT_ID/global/networks/NETWORK_NAME`.
    labels: Optional. Labels as key value pairs
    name: Immutable. Identifier. Name of the DnsThreatDetector resource.
    provider: Required. The provider used for DNS threat analysis.
    updateTime: Output only. [Output only] Update time stamp
  """

  class ProviderValueValuesEnum(_messages.Enum):
    r"""Required. The provider used for DNS threat analysis.

    Values:
      PROVIDER_UNSPECIFIED: An unspecified provider.
      INFOBLOX: The Infoblox DNS threat detecter.
    """
    PROVIDER_UNSPECIFIED = 0
    INFOBLOX = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  excludedNetworks = _messages.StringField(2, repeated=True)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  provider = _messages.EnumField('ProviderValueValuesEnum', 5)
  updateTime = _messages.StringField(6)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FirewallAttachment(_messages.Message):
  r"""Message describing Attachment object

  Enums:
    StateValueValuesEnum: Output only. Current state of the attachment.

  Messages:
    LabelsValue: Labels as key value pairs

  Fields:
    createTime: Output only. Create time stamp
    labels: Labels as key value pairs
    name: Immutable. Identifier. name of resource
    producerForwardingRuleName: Required. Name of the regional load balancer
      which the intercepted traffic should be forwarded to: 'projects/{project
      _id}/regions/{region}/forwardingRules/{forwardingRule}'
    producerNatSubnetworkName: Required. Name of the subnet that is used to
      NAT the IP addresses of the intercepted traffic in the attachment's VPC:
      'projects/{project_id}/{location}/subnetworks/{subnetwork_name}'
    reconciling: Output only. Whether reconciling is in progress, recommended
      per https://google.aip.dev/128.
    state: Output only. Current state of the attachment.
    updateTime: Output only. Update time stamp
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the attachment.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: Being created.
      ACTIVE: Attachment is now active.
      DELETING: Being deleted.
      INACTIVE: Down or in an error state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    INACTIVE = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  producerForwardingRuleName = _messages.StringField(4)
  producerNatSubnetworkName = _messages.StringField(5)
  reconciling = _messages.BooleanField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  updateTime = _messages.StringField(8)


class FirewallEndpoint(_messages.Message):
  r"""Message describing Endpoint object

  Enums:
    StateValueValuesEnum: Output only. Current state of the endpoint.
    TypeValueValuesEnum: Optional. Endpoint type.

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    associatedNetworks: Output only. List of networks that are associated with
      this endpoint in the local zone. This is a projection of the
      FirewallEndpointAssociations pointing at this endpoint. A network will
      only appear in this list after traffic routing is fully configured.
      Format: projects/{project}/global/networks/{name}.
    associations: Output only. List of FirewallEndpointAssociations that are
      associated to this endpoint. An association will only appear in this
      list after traffic routing is fully configured.
    billingProjectId: Required. Project to bill on endpoint uptime usage.
    createTime: Output only. Create time stamp
    description: Optional. Description of the firewall endpoint. Max length
      2048 characters.
    firstPartyEndpointSettings: Optional. Firewall endpoint settings for first
      party firewall endpoints.
    labels: Optional. Labels as key value pairs
    name: Immutable. Identifier. name of resource
    reconciling: Output only. Whether reconciling is in progress, recommended
      per https://google.aip.dev/128.
    satisfiesPzi: Output only. [Output Only] Reserved for future use.
    satisfiesPzs: Output only. [Output Only] Reserved for future use.
    state: Output only. Current state of the endpoint.
    thirdPartyEndpointSettings: Optional. Firewall endpoint settings for third
      party firewall endpoints.
    type: Optional. Endpoint type.
    updateTime: Output only. Update time stamp
    wildfireSettings: Optional. Settings for Wildfire analysis.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the endpoint.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: Being created.
      ACTIVE: Processing configuration updates.
      DELETING: Being deleted.
      INACTIVE: Down or in an error state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    INACTIVE = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. Endpoint type.

    Values:
      TYPE_UNSPECIFIED: Not set.
      FIRST_PARTY: First party firewall endpoint.
      THIRD_PARTY: Third party firewall endpoint.
    """
    TYPE_UNSPECIFIED = 0
    FIRST_PARTY = 1
    THIRD_PARTY = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  associatedNetworks = _messages.StringField(1, repeated=True)
  associations = _messages.MessageField('FirewallEndpointAssociationReference', 2, repeated=True)
  billingProjectId = _messages.StringField(3)
  createTime = _messages.StringField(4)
  description = _messages.StringField(5)
  firstPartyEndpointSettings = _messages.MessageField('FirstPartyEndpointSettings', 6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  reconciling = _messages.BooleanField(9)
  satisfiesPzi = _messages.BooleanField(10)
  satisfiesPzs = _messages.BooleanField(11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  thirdPartyEndpointSettings = _messages.MessageField('ThirdPartyEndpointSettings', 13)
  type = _messages.EnumField('TypeValueValuesEnum', 14)
  updateTime = _messages.StringField(15)
  wildfireSettings = _messages.MessageField('FirewallEndpointWildfireSettings', 16)


class FirewallEndpointAssociation(_messages.Message):
  r"""Message describing Association object

  Enums:
    StateValueValuesEnum: Output only. Current state of the association.

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    createTime: Output only. Create time stamp
    disabled: Optional. Whether the association is disabled. True indicates
      that traffic won't be intercepted
    firewallEndpoint: Required. The URL of the FirewallEndpoint that is being
      associated.
    labels: Optional. Labels as key value pairs
    name: Immutable. Identifier. name of resource
    network: Required. The URL of the network that is being associated.
    reconciling: Output only. Whether reconciling is in progress, recommended
      per https://google.aip.dev/128.
    state: Output only. Current state of the association.
    tlsInspectionPolicy: Optional. The URL of the TlsInspectionPolicy that is
      being associated.
    updateTime: Output only. Update time stamp
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the association.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: Being created.
      ACTIVE: Active and ready for traffic.
      DELETING: Being deleted.
      INACTIVE: Down or in an error state.
      ORPHAN: The project that housed the association has been deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    INACTIVE = 4
    ORPHAN = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  disabled = _messages.BooleanField(2)
  firewallEndpoint = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  network = _messages.StringField(6)
  reconciling = _messages.BooleanField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  tlsInspectionPolicy = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class FirewallEndpointAssociationReference(_messages.Message):
  r"""This is a subset of the FirewallEndpointAssociation message, containing
  fields to be used by the consumer.

  Fields:
    name: Output only. The resource name of the FirewallEndpointAssociation.
      Format: projects/{project}/locations/{location}/firewallEndpointAssociat
      ions/{id}
    network: Output only. The VPC network associated. Format:
      projects/{project}/global/networks/{name}.
  """

  name = _messages.StringField(1)
  network = _messages.StringField(2)


class FirewallEndpointWildfireSettings(_messages.Message):
  r"""Settings for Wildfire analysis.

  Enums:
    WildfireRealtimeLookupTimeoutActionValueValuesEnum: Optional. Action to
      take on Wildfire real time signature lookup timeout. Default value is
      ALLOW.
    WildfireRegionValueValuesEnum: Optional. The region where Wildfire
      analysis will be performed. PAN supports regions:
      https://docs.paloaltonetworks.com/advanced-
      wildfire/administration/advanced-wildfire-overview/advanced-wildfire-
      deployments/advanced-wildfire-global-cloud

  Fields:
    enabled: Optional. Indicates whether Wildfire analysis is enabled. Default
      value is false.
    wildfireRealtimeLookupDuration: Optional. Duration in milliseconds on a
      file being held while the Wildfire real time signature cloud performs a
      signature lookup. Value between 1 to 5000 is valid. Default value is
      1000.
    wildfireRealtimeLookupTimeoutAction: Optional. Action to take on Wildfire
      real time signature lookup timeout. Default value is ALLOW.
    wildfireRegion: Optional. The region where Wildfire analysis will be
      performed. PAN supports regions:
      https://docs.paloaltonetworks.com/advanced-
      wildfire/administration/advanced-wildfire-overview/advanced-wildfire-
      deployments/advanced-wildfire-global-cloud
  """

  class WildfireRealtimeLookupTimeoutActionValueValuesEnum(_messages.Enum):
    r"""Optional. Action to take on Wildfire real time signature lookup
    timeout. Default value is ALLOW.

    Values:
      WILDFIRE_REALTIME_SIGNATURE_LOOKUP_TIMEOUT_ACTION_UNSPECIFIED: Wildfire
        real time signature lookup timeout action not specified.
      ALLOW: The files that timed out in the signature lookup will be allowed
        to transmit.
      DENY: The files that timed out in the signature lookup will be denied to
        transmit.
    """
    WILDFIRE_REALTIME_SIGNATURE_LOOKUP_TIMEOUT_ACTION_UNSPECIFIED = 0
    ALLOW = 1
    DENY = 2

  class WildfireRegionValueValuesEnum(_messages.Enum):
    r"""Optional. The region where Wildfire analysis will be performed. PAN
    supports regions: https://docs.paloaltonetworks.com/advanced-
    wildfire/administration/advanced-wildfire-overview/advanced-wildfire-
    deployments/advanced-wildfire-global-cloud

    Values:
      WILDFIRE_REGION_UNSPECIFIED: Wildfire region not specified.
      CANADA: Wildfire region Canada. Canada cloud portal:
        ca.wildfire.paloaltonetworks.com
      UNITED_STATES: Wildfire region United States.
      JAPAN: Wildfire region Japan. Japan cloud portal:
        jp.wildfire.paloaltonetworks.com
      SINGAPORE: Wildfire region Singapore. Singapore cloud portal:
        sg.wildfire.paloaltonetworks.com
      UNITED_KINGDOM: Wildfire region United Kingdom. United Kingdom cloud
        portal: uk.wildfire.paloaltonetworks.com
      AUSTRALIA: Wildfire region Australia. Australia cloud portal:
        au.wildfire.paloaltonetworks.com
      GERMANY: Wildfire region Germany. Germany cloud portal:
        de.wildfire.paloaltonetworks.com
      INDIA: Wildfire region India. India cloud portal:
        in.wildfire.paloaltonetworks.com
      SWITZERLAND: Wildfire region Switzerland. Switzerland cloud portal:
        ch.wildfire.paloaltonetworks.com
      POLAND: Wildfire region Poland. Poland cloud portal:
        pl.wildfire.paloaltonetworks.com
      INDONESIA: Wildfire region Indonesia. Indonesia cloud portal:
        id.wildfire.paloaltonetworks.com
      TAIWAN: Wildfire region Taiwan. Taiwan cloud portal:
        tw.wildfire.paloaltonetworks.com
      FRANCE: Wildfire region France. France cloud portal:
        fr.wildfire.paloaltonetworks.com
      QATAR: Wildfire region Qatar. Qatar cloud portal:
        qatar.wildfire.paloaltonetworks.com
      SOUTH_KOREA: Wildfire region South Korea. South Korea cloud portal:
        kr.wildfire.paloaltonetworks.com
      ISRAEL: Wildfire region Israel. Israel cloud portal:
        il.wildfire.paloaltonetworks.com
      SAUDI_ARABIA: Wildfire region Saudi Arabia. Saudi Arabia cloud portal:
        sa.wildfire.paloaltonetworks.com
      SPAIN: Wildfire region Spain. Spain cloud portal:
        es.wildfire.paloaltonetworks.com
    """
    WILDFIRE_REGION_UNSPECIFIED = 0
    CANADA = 1
    UNITED_STATES = 2
    JAPAN = 3
    SINGAPORE = 4
    UNITED_KINGDOM = 5
    AUSTRALIA = 6
    GERMANY = 7
    INDIA = 8
    SWITZERLAND = 9
    POLAND = 10
    INDONESIA = 11
    TAIWAN = 12
    FRANCE = 13
    QATAR = 14
    SOUTH_KOREA = 15
    ISRAEL = 16
    SAUDI_ARABIA = 17
    SPAIN = 18

  enabled = _messages.BooleanField(1)
  wildfireRealtimeLookupDuration = _messages.StringField(2)
  wildfireRealtimeLookupTimeoutAction = _messages.EnumField('WildfireRealtimeLookupTimeoutActionValueValuesEnum', 3)
  wildfireRegion = _messages.EnumField('WildfireRegionValueValuesEnum', 4)


class FirstPartyEndpointSettings(_messages.Message):
  r"""A FirstPartyEndpointSettings object."""


class ForwardingRule(_messages.Message):
  r"""ForwardingRule contains the forwarding rule related parameters used to
  create or update a PSC attachment via G2CCyrus.

  Fields:
    incarnationId: Required. The Arcus incarnation ID.
    ipAddress: Required. The IP address of the forwarding rule.
    name: Required. The canonical forwarding rule name:
      projects/{project_number}/regions/{region}/forwardingRules/{id}
    network: Required. The network of the forwarding rule, in the format of
      projects/{project_number}/global/networks/{id}.
  """

  incarnationId = _messages.IntegerField(1)
  ipAddress = _messages.StringField(2)
  name = _messages.StringField(3)
  network = _messages.StringField(4)


class GatewayAttachment(_messages.Message):
  r"""GatewayAttachment is a resource that represents the Gateway attachment
  in a given location.

  Enums:
    OriginValueValuesEnum: Required. The origin of the attachment.
    StateValueValuesEnum: Output only. Current state of the GatewayAttachment.

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    createTime: Output only. [Output only] Create time stamp
    forwardingRule: Required. Immutable. The forwarding rule of the
      attachment.
    labels: Optional. Labels as key value pairs
    name: Identifier. The name of the resource.
    origin: Required. The origin of the attachment.
    reconciling: Output only. Whether reconciling is in progress, recommended
      per https://google.aip.dev/128.
    state: Output only. Current state of the GatewayAttachment.
    updateTime: Output only. [Output only] Update time stamp
    userspaceTunneling: Immutable. Userspace tunneling attachment.
  """

  class OriginValueValuesEnum(_messages.Enum):
    r"""Required. The origin of the attachment.

    Values:
      ORIGIN_UNSPECIFIED: Not set.
      ORIGIN_PACKET_BROKER: Packet broker.
      ORIGIN_BUTTER_PACKET_CAPTURE: Butter packet capture.
    """
    ORIGIN_UNSPECIFIED = 0
    ORIGIN_PACKET_BROKER = 1
    ORIGIN_BUTTER_PACKET_CAPTURE = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the GatewayAttachment.

    Values:
      STATE_UNSPECIFIED: Not set.
      ACTIVE: Ready.
      DELETE_FAILED: Failed to delete.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETE_FAILED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  forwardingRule = _messages.MessageField('ForwardingRule', 2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  origin = _messages.EnumField('OriginValueValuesEnum', 5)
  reconciling = _messages.BooleanField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  updateTime = _messages.StringField(8)
  userspaceTunneling = _messages.MessageField('GatewayAttachmentUserspaceTunneling', 9)


class GatewayAttachmentUserspaceTunneling(_messages.Message):
  r"""Userspace tunneling configuration."""


class GatewayEndpoint(_messages.Message):
  r"""GatewayEndpoint is a resource that represents the Gateway endpoint in a
  given location.

  Enums:
    StateValueValuesEnum: Output only. Current state of the GatewayEndpoint.

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    createTime: Output only. [Output only] Create time stamp
    labels: Optional. Labels as key value pairs
    name: Identifier. The name of the resource.
    network: Required. The name of the VPC network of the endpoint. It
      dictates the project used for the Andromeda EP.
    reconciling: Output only. Whether reconciling is in progress, recommended
      per https://google.aip.dev/128.
    state: Output only. Current state of the GatewayEndpoint.
    updateTime: Output only. [Output only] Update time stamp
    userspaceTunneling: Immutable. Userspace tunneling configuration.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the GatewayEndpoint.

    Values:
      STATE_UNSPECIFIED: Not set.
      ACTIVE: Ready.
      DELETE_FAILED: Failed to delete.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETE_FAILED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  network = _messages.StringField(4)
  reconciling = _messages.BooleanField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  updateTime = _messages.StringField(7)
  userspaceTunneling = _messages.MessageField('GatewayEndpointUserspaceTunneling', 8)


class GatewayEndpointUserspaceTunneling(_messages.Message):
  r"""Userspace tunneling configuration.

  Fields:
    ipAddress: Required. The IP address associated with the EP. The caller
      must statically allocate it in a network subnet that's in the same
      region as this EP.
  """

  ipAddress = _messages.StringField(1)


class GatewaySecurityPolicy(_messages.Message):
  r"""The GatewaySecurityPolicy resource contains a collection of
  GatewaySecurityPolicyRules and associated metadata.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. Free-text description of the resource.
    name: Required. Name of the resource. Name is of the form projects/{projec
      t}/locations/{location}/gatewaySecurityPolicies/{gateway_security_policy
      } gateway_security_policy should match the
      pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    tlsInspectionPolicy: Optional. Name of a TLS Inspection Policy resource
      that defines how TLS inspection will be performed for any rule(s) which
      enables it.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  name = _messages.StringField(3)
  tlsInspectionPolicy = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class GatewaySecurityPolicyRule(_messages.Message):
  r"""The GatewaySecurityPolicyRule resource is in a nested collection within
  a GatewaySecurityPolicy and represents a traffic matching condition and
  associated action to perform.

  Enums:
    BasicProfileValueValuesEnum: Required. Profile which tells what the
      primitive action should be.

  Fields:
    applicationMatcher: Optional. CEL expression for matching on
      L7/application level criteria.
    basicProfile: Required. Profile which tells what the primitive action
      should be.
    createTime: Output only. Time when the rule was created.
    description: Optional. Free-text description of the resource.
    enabled: Required. Whether the rule is enforced.
    name: Required. Immutable. Name of the resource. ame is the full resource
      name so projects/{project}/locations/{location}/gatewaySecurityPolicies/
      {gateway_security_policy}/rules/{rule} rule should match the pattern:
      (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    priority: Required. Priority of the rule. Lower number corresponds to
      higher precedence.
    sessionMatcher: Required. CEL expression for matching on session criteria.
    tlsInspectionEnabled: Optional. Flag to enable TLS inspection of traffic
      matching on , can only be true if the parent GatewaySecurityPolicy
      references a TLSInspectionConfig.
    updateTime: Output only. Time when the rule was updated.
  """

  class BasicProfileValueValuesEnum(_messages.Enum):
    r"""Required. Profile which tells what the primitive action should be.

    Values:
      BASIC_PROFILE_UNSPECIFIED: If there is not a mentioned action for the
        target.
      ALLOW: Allow the matched traffic.
      DENY: Deny the matched traffic.
    """
    BASIC_PROFILE_UNSPECIFIED = 0
    ALLOW = 1
    DENY = 2

  applicationMatcher = _messages.StringField(1)
  basicProfile = _messages.EnumField('BasicProfileValueValuesEnum', 2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  enabled = _messages.BooleanField(5)
  name = _messages.StringField(6)
  priority = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  sessionMatcher = _messages.StringField(8)
  tlsInspectionEnabled = _messages.BooleanField(9)
  updateTime = _messages.StringField(10)


class GoogleCloudNetworksecurityV1alpha1CertificateProvider(_messages.Message):
  r"""Specification of certificate provider. Defines the mechanism to obtain
  the certificate and private key for peer to peer authentication.

  Fields:
    certificateProviderInstance: The certificate provider instance
      specification that will be passed to the data plane, which will be used
      to load necessary credential information.
    grpcEndpoint: gRPC specific configuration to access the gRPC server to
      obtain the cert and private key.
    localFilepath: Obtain certificates and private key from a locally mounted
      filesystem path.
  """

  certificateProviderInstance = _messages.MessageField('CertificateProviderInstance', 1)
  grpcEndpoint = _messages.MessageField('GoogleCloudNetworksecurityV1alpha1GrpcEndpoint', 2)
  localFilepath = _messages.MessageField('TlsCertificateFiles', 3)


class GoogleCloudNetworksecurityV1alpha1GrpcEndpoint(_messages.Message):
  r"""Specification of the GRPC Endpoint.

  Fields:
    targetUri: Required. The target URI of the gRPC endpoint. Only UDS path is
      supported, and should start with "unix:".
  """

  targetUri = _messages.StringField(1)


class GoogleIamV1AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('GoogleIamV1AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class GoogleIamV1AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class GoogleIamV1Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('GoogleIamV1AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleIamV1SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('GoogleIamV1Policy', 1)
  updateMask = _messages.StringField(2)


class GoogleIamV1TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleIamV1TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class HttpHeaderMatch(_messages.Message):
  r"""Specification of HTTP header match attributes.

  Fields:
    headerName: Required. The name of the HTTP header to match. For matching
      against the HTTP request's authority, use a headerMatch with the header
      name ":authority". For matching a request's method, use the headerName
      ":method".
    regexMatch: Required. The value of the header must match the regular
      expression specified in regexMatch. For regular expression grammar,
      please see: en.cppreference.com/w/cpp/regex/ecmascript For matching
      against a port specified in the HTTP request, use a headerMatch with
      headerName set to Host and a regular expression that satisfies the
      RFC2616 Host header's port specifier.
  """

  headerName = _messages.StringField(1)
  regexMatch = _messages.StringField(2)


class InterceptDeployment(_messages.Message):
  r"""A deployment represents a zonal intercept backend ready to accept
  GENEVE-encapsulated traffic, e.g. a zonal instance group fronted by an
  internal passthrough load balancer. Deployments are always part of a global
  deployment group which represents a global intercept service.

  Enums:
    StateValueValuesEnum: Output only. The current state of the deployment.
      See https://google.aip.dev/216.

  Messages:
    LabelsValue: Optional. Labels are key/value pairs that help to organize
      and filter resources.

  Fields:
    createTime: Output only. The timestamp when the resource was created. See
      https://google.aip.dev/148#timestamps.
    description: Optional. User-provided description of the deployment. Used
      as additional context for the deployment.
    forwardingRule: Required. Immutable. The regional forwarding rule that
      fronts the interceptors, for example: `projects/123456789/regions/us-
      central1/forwardingRules/my-rule`. See https://google.aip.dev/124.
    interceptDeploymentGroup: Required. Immutable. The deployment group that
      this deployment is a part of, for example:
      `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`.
      See https://google.aip.dev/124.
    labels: Optional. Labels are key/value pairs that help to organize and
      filter resources.
    name: Immutable. Identifier. The resource name of this deployment, for
      example: `projects/123456789/locations/us-
      central1-a/interceptDeployments/my-dep`. See https://google.aip.dev/122
      for more details.
    reconciling: Output only. The current state of the resource does not match
      the user's intended state, and the system is working to reconcile them.
      This part of the normal operation (e.g. linking a new association to the
      parent group). See https://google.aip.dev/128.
    state: Output only. The current state of the deployment. See
      https://google.aip.dev/216.
    updateTime: Output only. The timestamp when the resource was most recently
      updated. See https://google.aip.dev/148#timestamps.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the deployment. See
    https://google.aip.dev/216.

    Values:
      STATE_UNSPECIFIED: State not set (this is not a valid state).
      ACTIVE: The deployment is ready and in sync with the parent group.
      CREATING: The deployment is being created.
      DELETING: The deployment is being deleted.
      OUT_OF_SYNC: The deployment is out of sync with the parent group. In
        most cases, this is a result of a transient issue within the system
        (e.g. a delayed data-path config) and the system is expected to
        recover automatically. See the parent deployment group's state for
        more details.
      DELETE_FAILED: An attempt to delete the deployment has failed. This is a
        terminal state and the deployment is not expected to recover. The only
        permitted operation is to retry deleting the deployment.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    OUT_OF_SYNC = 4
    DELETE_FAILED = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are key/value pairs that help to organize and filter
    resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  forwardingRule = _messages.StringField(3)
  interceptDeploymentGroup = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  reconciling = _messages.BooleanField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  updateTime = _messages.StringField(9)


class InterceptDeploymentGroup(_messages.Message):
  r"""A deployment group aggregates many zonal intercept backends
  (deployments) into a single global intercept service. Consumers can connect
  this service using an endpoint group.

  Enums:
    StateValueValuesEnum: Output only. The current state of the deployment
      group. See https://google.aip.dev/216.

  Messages:
    LabelsValue: Optional. Labels are key/value pairs that help to organize
      and filter resources.

  Fields:
    connectedEndpointGroups: Output only. The list of endpoint groups that are
      connected to this resource.
    createTime: Output only. The timestamp when the resource was created. See
      https://google.aip.dev/148#timestamps.
    description: Optional. User-provided description of the deployment group.
      Used as additional context for the deployment group.
    labels: Optional. Labels are key/value pairs that help to organize and
      filter resources.
    locations: Output only. The list of locations where the deployment group
      is present.
    name: Immutable. Identifier. The resource name of this deployment group,
      for example:
      `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`.
      See https://google.aip.dev/122 for more details.
    nestedDeployments: Output only. The list of Intercept Deployments that
      belong to this group.
    network: Required. Immutable. The network that will be used for all child
      deployments, for example:
      `projects/{project}/global/networks/{network}`. See
      https://google.aip.dev/124.
    reconciling: Output only. The current state of the resource does not match
      the user's intended state, and the system is working to reconcile them.
      This is part of the normal operation (e.g. adding a new deployment to
      the group) See https://google.aip.dev/128.
    state: Output only. The current state of the deployment group. See
      https://google.aip.dev/216.
    updateTime: Output only. The timestamp when the resource was most recently
      updated. See https://google.aip.dev/148#timestamps.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the deployment group. See
    https://google.aip.dev/216.

    Values:
      STATE_UNSPECIFIED: State not set (this is not a valid state).
      ACTIVE: The deployment group is ready.
      CREATING: The deployment group is being created.
      DELETING: The deployment group is being deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are key/value pairs that help to organize and filter
    resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  connectedEndpointGroups = _messages.MessageField('InterceptDeploymentGroupConnectedEndpointGroup', 1, repeated=True)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  locations = _messages.MessageField('InterceptLocation', 5, repeated=True)
  name = _messages.StringField(6)
  nestedDeployments = _messages.MessageField('InterceptDeploymentGroupDeployment', 7, repeated=True)
  network = _messages.StringField(8)
  reconciling = _messages.BooleanField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  updateTime = _messages.StringField(11)


class InterceptDeploymentGroupConnectedEndpointGroup(_messages.Message):
  r"""An endpoint group connected to this deployment group.

  Fields:
    name: Output only. The connected endpoint group's resource name, for
      example:
      `projects/123456789/locations/global/interceptEndpointGroups/my-eg`. See
      https://google.aip.dev/124.
  """

  name = _messages.StringField(1)


class InterceptDeploymentGroupDeployment(_messages.Message):
  r"""A deployment belonging to this deployment group.

  Enums:
    StateValueValuesEnum: Output only. Most recent known state of the
      deployment.

  Fields:
    name: Output only. The name of the Intercept Deployment, in the format: `p
      rojects/{project}/locations/{location}/interceptDeployments/{intercept_d
      eployment}`.
    state: Output only. Most recent known state of the deployment.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Most recent known state of the deployment.

    Values:
      STATE_UNSPECIFIED: State not set (this is not a valid state).
      ACTIVE: The deployment is ready and in sync with the parent group.
      CREATING: The deployment is being created.
      DELETING: The deployment is being deleted.
      OUT_OF_SYNC: The deployment is out of sync with the parent group. In
        most cases, this is a result of a transient issue within the system
        (e.g. a delayed data-path config) and the system is expected to
        recover automatically. See the parent deployment group's state for
        more details.
      DELETE_FAILED: An attempt to delete the deployment has failed. This is a
        terminal state and the deployment is not expected to recover. The only
        permitted operation is to retry deleting the deployment.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    OUT_OF_SYNC = 4
    DELETE_FAILED = 5

  name = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class InterceptEndpointGroup(_messages.Message):
  r"""An endpoint group is a consumer frontend for a deployment group
  (backend). In order to configure intercept for a network, consumers must
  create: - An association between their network and the endpoint group. - A
  security profile that points to the endpoint group. - A firewall rule that
  references the security profile (group).

  Enums:
    StateValueValuesEnum: Output only. The current state of the endpoint
      group. See https://google.aip.dev/216.

  Messages:
    LabelsValue: Optional. Labels are key/value pairs that help to organize
      and filter resources.

  Fields:
    associations: Output only. List of associations to this endpoint group.
    connectedDeploymentGroup: Output only. Details about the connected
      deployment group to this endpoint group.
    createTime: Output only. The timestamp when the resource was created. See
      https://google.aip.dev/148#timestamps.
    description: Optional. User-provided description of the endpoint group.
      Used as additional context for the endpoint group.
    interceptDeploymentGroup: Required. Immutable. The deployment group that
      this endpoint group is connected to, for example:
      `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`.
      See https://google.aip.dev/124.
    labels: Optional. Labels are key/value pairs that help to organize and
      filter resources.
    name: Immutable. Identifier. The resource name of this endpoint group, for
      example:
      `projects/123456789/locations/global/interceptEndpointGroups/my-eg`. See
      https://google.aip.dev/122 for more details.
    reconciling: Output only. The current state of the resource does not match
      the user's intended state, and the system is working to reconcile them.
      This is part of the normal operation (e.g. adding a new association to
      the group). See https://google.aip.dev/128.
    state: Output only. The current state of the endpoint group. See
      https://google.aip.dev/216.
    updateTime: Output only. The timestamp when the resource was most recently
      updated. See https://google.aip.dev/148#timestamps.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the endpoint group. See
    https://google.aip.dev/216.

    Values:
      STATE_UNSPECIFIED: State not set (this is not a valid state).
      ACTIVE: The endpoint group is ready and in sync with the target
        deployment group.
      CLOSED: The deployment group backing this endpoint group has been force-
        deleted. This endpoint group cannot be used and interception is
        effectively disabled.
      CREATING: The endpoint group is being created.
      DELETING: The endpoint group is being deleted.
      OUT_OF_SYNC: The endpoint group is out of sync with the backing
        deployment group. In most cases, this is a result of a transient issue
        within the system (e.g. an inaccessible location) and the system is
        expected to recover automatically. See the associations field for
        details per network and location.
      DELETE_FAILED: An attempt to delete the endpoint group has failed. This
        is a terminal state and the endpoint group is not expected to recover.
        The only permitted operation is to retry deleting the endpoint group.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CLOSED = 2
    CREATING = 3
    DELETING = 4
    OUT_OF_SYNC = 5
    DELETE_FAILED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are key/value pairs that help to organize and filter
    resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  associations = _messages.MessageField('InterceptEndpointGroupAssociationDetails', 1, repeated=True)
  connectedDeploymentGroup = _messages.MessageField('InterceptEndpointGroupConnectedDeploymentGroup', 2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  interceptDeploymentGroup = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  reconciling = _messages.BooleanField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  updateTime = _messages.StringField(10)


class InterceptEndpointGroupAssociation(_messages.Message):
  r"""An endpoint group association represents a link between a network and an
  endpoint group in the organization. Creating an association creates the
  networking infrastructure linking the network to the endpoint group, but
  does not enable intercept by itself. To enable intercept, the user must also
  create a network firewall policy containing intercept rules and associate it
  with the network.

  Enums:
    StateValueValuesEnum: Output only. Current state of the endpoint group
      association.

  Messages:
    LabelsValue: Optional. Labels are key/value pairs that help to organize
      and filter resources.

  Fields:
    createTime: Output only. The timestamp when the resource was created. See
      https://google.aip.dev/148#timestamps.
    interceptEndpointGroup: Required. Immutable. The endpoint group that this
      association is connected to, for example:
      `projects/123456789/locations/global/interceptEndpointGroups/my-eg`. See
      https://google.aip.dev/124.
    labels: Optional. Labels are key/value pairs that help to organize and
      filter resources.
    locations: Output only. The list of locations where the association is
      configured. This information is retrieved from the linked endpoint
      group.
    locationsDetails: Output only. The list of locations where the association
      is present. This information is retrieved from the linked endpoint
      group, and not configured as part of the association itself.
    name: Immutable. Identifier. The resource name of this endpoint group
      association, for example: `projects/123456789/locations/global/intercept
      EndpointGroupAssociations/my-eg-association`. See
      https://google.aip.dev/122 for more details.
    nccGateway: Optional. Immutable. A Network Connectivity Center gateway
      spoke that is associated. for example: `projects/123456789/locations/us-
      central1/spokes/my-spoke`. Exactly one of `network` and `ncc_gateway`
      must be set. See https://google.aip.dev/124.
    network: Required. Immutable. The VPC network that is associated. for
      example: `projects/123456789/global/networks/my-network`. See
      https://google.aip.dev/124.
    reconciling: Output only. The current state of the resource does not match
      the user's intended state, and the system is working to reconcile them.
      This part of the normal operation (e.g. adding a new location to the
      target deployment group). See https://google.aip.dev/128.
    state: Output only. Current state of the endpoint group association.
    updateTime: Output only. The timestamp when the resource was most recently
      updated. See https://google.aip.dev/148#timestamps.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the endpoint group association.

    Values:
      STATE_UNSPECIFIED: Not set.
      ACTIVE: The association is ready and in sync with the linked endpoint
        group.
      CREATING: The association is being created.
      DELETING: The association is being deleted.
      CLOSED: The association is disabled due to a breaking change in another
        resource.
      OUT_OF_SYNC: The association is out of sync with the linked endpoint
        group. In most cases, this is a result of a transient issue within the
        system (e.g. an inaccessible location) and the system is expected to
        recover automatically. Check the `locations_details` field for more
        details.
      DELETE_FAILED: An attempt to delete the association has failed. This is
        a terminal state and the association is not expected to be usable as
        some of its resources have been deleted. The only permitted operation
        is to retry deleting the association.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    CLOSED = 4
    OUT_OF_SYNC = 5
    DELETE_FAILED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are key/value pairs that help to organize and filter
    resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  interceptEndpointGroup = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  locations = _messages.MessageField('InterceptLocation', 4, repeated=True)
  locationsDetails = _messages.MessageField('InterceptEndpointGroupAssociationLocationDetails', 5, repeated=True)
  name = _messages.StringField(6)
  nccGateway = _messages.StringField(7)
  network = _messages.StringField(8)
  reconciling = _messages.BooleanField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  updateTime = _messages.StringField(11)


class InterceptEndpointGroupAssociationDetails(_messages.Message):
  r"""The endpoint group's view of a connected association.

  Enums:
    StateValueValuesEnum: Output only. Most recent known state of the
      association.

  Fields:
    name: Output only. The connected association's resource name, for example:
      `projects/123456789/locations/global/interceptEndpointGroupAssociations/
      my-ega`. See https://google.aip.dev/124.
    network: Output only. The associated network, for example:
      projects/123456789/global/networks/my-network. See
      https://google.aip.dev/124.
    state: Output only. Most recent known state of the association.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Most recent known state of the association.

    Values:
      STATE_UNSPECIFIED: Not set.
      ACTIVE: The association is ready and in sync with the linked endpoint
        group.
      CREATING: The association is being created.
      DELETING: The association is being deleted.
      CLOSED: The association is disabled due to a breaking change in another
        resource.
      OUT_OF_SYNC: The association is out of sync with the linked endpoint
        group. In most cases, this is a result of a transient issue within the
        system (e.g. an inaccessible location) and the system is expected to
        recover automatically. Check the `locations_details` field for more
        details.
      DELETE_FAILED: An attempt to delete the association has failed. This is
        a terminal state and the association is not expected to be usable as
        some of its resources have been deleted. The only permitted operation
        is to retry deleting the association.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    CLOSED = 4
    OUT_OF_SYNC = 5
    DELETE_FAILED = 6

  name = _messages.StringField(1)
  network = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class InterceptEndpointGroupAssociationLocationDetails(_messages.Message):
  r"""Contains details about the state of an association in a specific cloud
  location.

  Enums:
    StateValueValuesEnum: Output only. The current state of the association in
      this location.

  Fields:
    location: Output only. The cloud location, e.g. "us-central1-a" or "asia-
      south1".
    state: Output only. The current state of the association in this location.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the association in this location.

    Values:
      STATE_UNSPECIFIED: Not set.
      ACTIVE: The association is ready and in sync with the linked endpoint
        group.
      OUT_OF_SYNC: The association is out of sync with the linked endpoint
        group. In most cases, this is a result of a transient issue within the
        system (e.g. an inaccessible location) and the system is expected to
        recover automatically.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    OUT_OF_SYNC = 2

  location = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class InterceptEndpointGroupConnectedDeploymentGroup(_messages.Message):
  r"""The endpoint group's view of a connected deployment group.

  Fields:
    locations: Output only. The list of locations where the deployment group
      is present.
    name: Output only. The connected deployment group's resource name, for
      example:
      `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`.
      See https://google.aip.dev/124.
  """

  locations = _messages.MessageField('InterceptLocation', 1, repeated=True)
  name = _messages.StringField(2)


class InterceptLocation(_messages.Message):
  r"""Details about intercept in a specific cloud location.

  Enums:
    StateValueValuesEnum: Output only. The current state of the association in
      this location.

  Fields:
    location: Output only. The cloud location, e.g. "us-central1-a" or "asia-
      south1".
    state: Output only. The current state of the association in this location.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the association in this location.

    Values:
      STATE_UNSPECIFIED: State not set (this is not a valid state).
      ACTIVE: The resource is ready and in sync in the location.
      OUT_OF_SYNC: The resource is out of sync in the location. In most cases,
        this is a result of a transient issue within the system (e.g. an
        inaccessible location) and the system is expected to recover
        automatically.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    OUT_OF_SYNC = 2

  location = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class ListAddressGroupReferencesResponse(_messages.Message):
  r"""Response of the ListAddressGroupReferences method.

  Fields:
    addressGroupReferences: A list of references that matches the specified
      filter in the request.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
  """

  addressGroupReferences = _messages.MessageField('ListAddressGroupReferencesResponseAddressGroupReference', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAddressGroupReferencesResponseAddressGroupReference(_messages.Message):
  r"""The Reference of AddressGroup.

  Fields:
    firewallPolicy: FirewallPolicy that is using the Address Group.
    rulePriority: Rule priority of the FirewallPolicy that is using the
      Address Group.
    securityPolicy: Cloud Armor SecurityPolicy that is using the Address
      Group.
  """

  firewallPolicy = _messages.StringField(1)
  rulePriority = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  securityPolicy = _messages.StringField(3)


class ListAddressGroupsResponse(_messages.Message):
  r"""Response returned by the ListAddressGroups method.

  Fields:
    addressGroups: List of AddressGroups resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Locations that could not be reached.
  """

  addressGroups = _messages.MessageField('AddressGroup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListAuthorizationPoliciesResponse(_messages.Message):
  r"""Response returned by the ListAuthorizationPolicies method.

  Fields:
    authorizationPolicies: List of AuthorizationPolicies resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
  """

  authorizationPolicies = _messages.MessageField('AuthorizationPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAuthzPoliciesResponse(_messages.Message):
  r"""Message for response to listing `AuthzPolicy` resources.

  Fields:
    authzPolicies: The list of `AuthzPolicy` resources.
    nextPageToken: A token identifying a page of results that the server
      returns.
    unreachable: Locations that could not be reached.
  """

  authzPolicies = _messages.MessageField('AuthzPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBackendAuthenticationConfigsResponse(_messages.Message):
  r"""Response returned by the ListBackendAuthenticationConfigs method.

  Fields:
    backendAuthenticationConfigs: List of BackendAuthenticationConfig
      resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Locations that could not be reached.
  """

  backendAuthenticationConfigs = _messages.MessageField('BackendAuthenticationConfig', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListClientTlsPoliciesResponse(_messages.Message):
  r"""Response returned by the ListClientTlsPolicies method.

  Fields:
    clientTlsPolicies: List of ClientTlsPolicy resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
  """

  clientTlsPolicies = _messages.MessageField('ClientTlsPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDnsThreatDetectorsResponse(_messages.Message):
  r"""Message for response to listing DnsThreatDetectors

  Fields:
    dnsThreatDetectors: The list of DnsThreatDetector resources.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page.
    unreachable: Unordered list. Unreachable `DnsThreatDetector` resources.
  """

  dnsThreatDetectors = _messages.MessageField('DnsThreatDetector', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListFirewallAttachmentsResponse(_messages.Message):
  r"""Message for response to listing Attachments

  Fields:
    firewallAttachments: The list of Attachments
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  firewallAttachments = _messages.MessageField('FirewallAttachment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListFirewallEndpointAssociationsResponse(_messages.Message):
  r"""Message for response to listing Associations

  Fields:
    firewallEndpointAssociations: The list of Association
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  firewallEndpointAssociations = _messages.MessageField('FirewallEndpointAssociation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListFirewallEndpointsResponse(_messages.Message):
  r"""Message for response to listing Endpoints

  Fields:
    firewallEndpoints: The list of Endpoint
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  firewallEndpoints = _messages.MessageField('FirewallEndpoint', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGatewayAttachmentsResponse(_messages.Message):
  r"""Message for response to listing GatewayAttachments

  Fields:
    gatewayAttachments: The list of GatewayAttachment
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  gatewayAttachments = _messages.MessageField('GatewayAttachment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGatewayEndpointsResponse(_messages.Message):
  r"""Message for response to listing GatewayEndpoints

  Fields:
    gatewayEndpoints: The list of GatewayEndpoint
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  gatewayEndpoints = _messages.MessageField('GatewayEndpoint', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGatewaySecurityPoliciesResponse(_messages.Message):
  r"""Response returned by the ListGatewaySecurityPolicies method.

  Fields:
    gatewaySecurityPolicies: List of GatewaySecurityPolicies resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then 'next_page_token' is included. To get the next set of
      results, call this method again using the value of 'next_page_token' as
      'page_token'.
    unreachable: Locations that could not be reached.
  """

  gatewaySecurityPolicies = _messages.MessageField('GatewaySecurityPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGatewaySecurityPolicyRulesResponse(_messages.Message):
  r"""Response returned by the ListGatewaySecurityPolicyRules method.

  Fields:
    gatewaySecurityPolicyRules: List of GatewaySecurityPolicyRule resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then 'next_page_token' is included. To get the next set of
      results, call this method again using the value of 'next_page_token' as
      'page_token'.
    unreachable: Locations that could not be reached.
  """

  gatewaySecurityPolicyRules = _messages.MessageField('GatewaySecurityPolicyRule', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListInterceptDeploymentGroupsResponse(_messages.Message):
  r"""Response message for ListInterceptDeploymentGroups.

  Fields:
    interceptDeploymentGroups: The deployment groups from the specified
      parent.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages. See
      https://google.aip.dev/158 for more details.
  """

  interceptDeploymentGroups = _messages.MessageField('InterceptDeploymentGroup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListInterceptDeploymentsResponse(_messages.Message):
  r"""Response message for ListInterceptDeployments.

  Fields:
    interceptDeployments: The deployments from the specified parent.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages. See
      https://google.aip.dev/158 for more details.
    unreachable: Locations that could not be reached.
  """

  interceptDeployments = _messages.MessageField('InterceptDeployment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListInterceptEndpointGroupAssociationsResponse(_messages.Message):
  r"""Response message for ListInterceptEndpointGroupAssociations.

  Fields:
    interceptEndpointGroupAssociations: The associations from the specified
      parent.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages. See
      https://google.aip.dev/158 for more details.
  """

  interceptEndpointGroupAssociations = _messages.MessageField('InterceptEndpointGroupAssociation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListInterceptEndpointGroupsResponse(_messages.Message):
  r"""Response message for ListInterceptEndpointGroups.

  Fields:
    interceptEndpointGroups: The endpoint groups from the specified parent.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages. See
      https://google.aip.dev/158 for more details.
  """

  interceptEndpointGroups = _messages.MessageField('InterceptEndpointGroup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMirroringDeploymentGroupsResponse(_messages.Message):
  r"""Response message for ListMirroringDeploymentGroups.

  Fields:
    mirroringDeploymentGroups: The deployment groups from the specified
      parent.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages. See
      https://google.aip.dev/158 for more details.
  """

  mirroringDeploymentGroups = _messages.MessageField('MirroringDeploymentGroup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMirroringDeploymentsResponse(_messages.Message):
  r"""Response message for ListMirroringDeployments.

  Fields:
    mirroringDeployments: The deployments from the specified parent.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages. See
      https://google.aip.dev/158 for more details.
    unreachable: Locations that could not be reached.
  """

  mirroringDeployments = _messages.MessageField('MirroringDeployment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMirroringEndpointGroupAssociationsResponse(_messages.Message):
  r"""Response message for ListMirroringEndpointGroupAssociations.

  Fields:
    mirroringEndpointGroupAssociations: The associations from the specified
      parent.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages. See
      https://google.aip.dev/158 for more details.
  """

  mirroringEndpointGroupAssociations = _messages.MessageField('MirroringEndpointGroupAssociation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMirroringEndpointGroupsResponse(_messages.Message):
  r"""Response message for ListMirroringEndpointGroups.

  Fields:
    mirroringEndpointGroups: The endpoint groups from the specified parent.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages. See
      https://google.aip.dev/158 for more details.
  """

  mirroringEndpointGroups = _messages.MessageField('MirroringEndpointGroup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMirroringEndpointsResponse(_messages.Message):
  r"""Message for response to listing mirroring endpoints.

  Fields:
    mirroringEndpoints: The list of mirroring endpoints.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  mirroringEndpoints = _messages.MessageField('MirroringEndpoint', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListPartnerSSEEnvironmentsResponse(_messages.Message):
  r"""Message for response to listing PartnerSSEEnvironments

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    partnerSseEnvironments: The list of PartnerSSEEnvironment
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  partnerSseEnvironments = _messages.MessageField('PartnerSSEEnvironment', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListPartnerSSEGatewaysResponse(_messages.Message):
  r"""Message for response to listing PartnerSSEGateways

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    partnerSseGateways: The list of PartnerSSEGateway
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  partnerSseGateways = _messages.MessageField('PartnerSSEGateway', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListPartnerSSERealmsResponse(_messages.Message):
  r"""Message for response to listing PartnerSSERealms

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    partnerSseRealms: The list of PartnerSSERealm
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  partnerSseRealms = _messages.MessageField('PartnerSSERealm', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListSACAttachmentsResponse(_messages.Message):
  r"""Response for `ListSACAttachments` method.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    sacAttachments: The list of SACAttachments.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  sacAttachments = _messages.MessageField('SACAttachment', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListSACRealmsResponse(_messages.Message):
  r"""Response for `ListSACRealms` method.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    sacRealms: The list of SACRealms.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  sacRealms = _messages.MessageField('SACRealm', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListSSEGatewayReferencesResponse(_messages.Message):
  r"""Message for response to listing SSEGatewayReferences

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    sseGatewayReferences: The list of SSEGatewayReference
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  sseGatewayReferences = _messages.MessageField('SSEGatewayReference', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListSecurityProfileGroupsResponse(_messages.Message):
  r"""Response returned by the ListSecurityProfileGroups method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    securityProfileGroups: List of SecurityProfileGroups resources.
  """

  nextPageToken = _messages.StringField(1)
  securityProfileGroups = _messages.MessageField('SecurityProfileGroup', 2, repeated=True)


class ListSecurityProfilesResponse(_messages.Message):
  r"""Response returned by the ListSecurityProfiles method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    securityProfiles: List of SecurityProfile resources.
  """

  nextPageToken = _messages.StringField(1)
  securityProfiles = _messages.MessageField('SecurityProfile', 2, repeated=True)


class ListServerTlsPoliciesResponse(_messages.Message):
  r"""Response returned by the ListServerTlsPolicies method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    serverTlsPolicies: List of ServerTlsPolicy resources.
    unreachable: Unreachable resources. Populated when the request opts into
      `return_partial_success` and reading across collections e.g. when
      attempting to list all resources across all supported locations.
  """

  nextPageToken = _messages.StringField(1)
  serverTlsPolicies = _messages.MessageField('ServerTlsPolicy', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListTlsInspectionPoliciesResponse(_messages.Message):
  r"""Response returned by the ListTlsInspectionPolicies method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then 'next_page_token' is included. To get the next set of
      results, call this method again using the value of 'next_page_token' as
      'page_token'.
    tlsInspectionPolicies: List of TlsInspectionPolicies resources.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  tlsInspectionPolicies = _messages.MessageField('TlsInspectionPolicy', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListUllMirroredNetworksResponse(_messages.Message):
  r"""Message for response to listing UllMirroredNetworks

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    ullMirroredNetworks: The list of UllMirroredNetwork
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  ullMirroredNetworks = _messages.MessageField('UllMirroredNetwork', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListUllMirroringCollectorsResponse(_messages.Message):
  r"""Message for response to listing UllMirroringCollectors

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    ullMirroringCollectors: The list of UllMirroringCollector
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  ullMirroringCollectors = _messages.MessageField('UllMirroringCollector', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListUllMirroringEnginesResponse(_messages.Message):
  r"""Message for response to listing UllMirroringEngines

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    ullMirroringEngines: The list of UllMirroringEngine
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  ullMirroringEngines = _messages.MessageField('UllMirroringEngine', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListUrlListsResponse(_messages.Message):
  r"""Response returned by the ListUrlLists method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Locations that could not be reached.
    urlLists: List of UrlList resources.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  urlLists = _messages.MessageField('UrlList', 3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MTLSPolicy(_messages.Message):
  r"""Specification of the MTLSPolicy.

  Enums:
    ClientValidationModeValueValuesEnum: When the client presents an invalid
      certificate or no certificate to the load balancer, the
      `client_validation_mode` specifies how the client connection is handled.
      Required if the policy is to be used with the Application Load
      Balancers. For Traffic Director it must be empty.

  Fields:
    clientValidationCa: Required if the policy is to be used with Traffic
      Director. For Application Load Balancers it must be empty. Defines the
      mechanism to obtain the Certificate Authority certificate to validate
      the client certificate.
    clientValidationMode: When the client presents an invalid certificate or
      no certificate to the load balancer, the `client_validation_mode`
      specifies how the client connection is handled. Required if the policy
      is to be used with the Application Load Balancers. For Traffic Director
      it must be empty.
    clientValidationTrustConfig: Reference to the TrustConfig from
      certificatemanager.googleapis.com namespace. If specified, the chain
      validation will be performed against certificates configured in the
      given TrustConfig. Allowed only if the policy is to be used with
      Application Load Balancers.
  """

  class ClientValidationModeValueValuesEnum(_messages.Enum):
    r"""When the client presents an invalid certificate or no certificate to
    the load balancer, the `client_validation_mode` specifies how the client
    connection is handled. Required if the policy is to be used with the
    Application Load Balancers. For Traffic Director it must be empty.

    Values:
      CLIENT_VALIDATION_MODE_UNSPECIFIED: Not allowed.
      ALLOW_INVALID_OR_MISSING_CLIENT_CERT: Allow connection even if
        certificate chain validation of the client certificate failed or no
        client certificate was presented. The proof of possession of the
        private key is always checked if client certificate was presented.
        This mode requires the backend to implement processing of data
        extracted from a client certificate to authenticate the peer, or to
        reject connections if the client certificate fingerprint is missing.
      REJECT_INVALID: Require a client certificate and allow connection to the
        backend only if validation of the client certificate passed. If set,
        requires a reference to non-empty TrustConfig specified in
        `client_validation_trust_config`.
    """
    CLIENT_VALIDATION_MODE_UNSPECIFIED = 0
    ALLOW_INVALID_OR_MISSING_CLIENT_CERT = 1
    REJECT_INVALID = 2

  clientValidationCa = _messages.MessageField('ValidationCA', 1, repeated=True)
  clientValidationMode = _messages.EnumField('ClientValidationModeValueValuesEnum', 2)
  clientValidationTrustConfig = _messages.StringField(3)


class MirroringDeployment(_messages.Message):
  r"""A deployment represents a zonal mirroring backend ready to accept
  GENEVE-encapsulated replica traffic, e.g. a zonal instance group fronted by
  an internal passthrough load balancer. Deployments are always part of a
  global deployment group which represents a global mirroring service.

  Enums:
    StateValueValuesEnum: Output only. The current state of the deployment.
      See https://google.aip.dev/216.

  Messages:
    LabelsValue: Optional. Labels are key/value pairs that help to organize
      and filter resources.

  Fields:
    createTime: Output only. The timestamp when the resource was created. See
      https://google.aip.dev/148#timestamps.
    description: Optional. User-provided description of the deployment. Used
      as additional context for the deployment.
    forwardingRule: Required. Immutable. The regional forwarding rule that
      fronts the mirroring collectors, for example:
      `projects/123456789/regions/us-central1/forwardingRules/my-rule`. See
      https://google.aip.dev/124.
    labels: Optional. Labels are key/value pairs that help to organize and
      filter resources.
    mirroringDeploymentGroup: Required. Immutable. The deployment group that
      this deployment is a part of, for example:
      `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`.
      See https://google.aip.dev/124.
    name: Immutable. Identifier. The resource name of this deployment, for
      example: `projects/123456789/locations/us-
      central1-a/mirroringDeployments/my-dep`. See https://google.aip.dev/122
      for more details.
    reconciling: Output only. The current state of the resource does not match
      the user's intended state, and the system is working to reconcile them.
      This part of the normal operation (e.g. linking a new association to the
      parent group). See https://google.aip.dev/128.
    state: Output only. The current state of the deployment. See
      https://google.aip.dev/216.
    updateTime: Output only. The timestamp when the resource was most recently
      updated. See https://google.aip.dev/148#timestamps.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the deployment. See
    https://google.aip.dev/216.

    Values:
      STATE_UNSPECIFIED: State not set (this is not a valid state).
      ACTIVE: The deployment is ready and in sync with the parent group.
      CREATING: The deployment is being created.
      DELETING: The deployment is being deleted.
      OUT_OF_SYNC: The deployment is out of sync with the parent group. In
        most cases, this is a result of a transient issue within the system
        (e.g. a delayed data-path config) and the system is expected to
        recover automatically. See the parent deployment group's state for
        more details.
      DELETE_FAILED: An attempt to delete the deployment has failed. This is a
        terminal state and the deployment is not expected to recover. The only
        permitted operation is to retry deleting the deployment.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    OUT_OF_SYNC = 4
    DELETE_FAILED = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are key/value pairs that help to organize and filter
    resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  forwardingRule = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  mirroringDeploymentGroup = _messages.StringField(5)
  name = _messages.StringField(6)
  reconciling = _messages.BooleanField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  updateTime = _messages.StringField(9)


class MirroringDeploymentGroup(_messages.Message):
  r"""A deployment group aggregates many zonal mirroring backends
  (deployments) into a single global mirroring service. Consumers can connect
  this service using an endpoint group.

  Enums:
    StateValueValuesEnum: Output only. The current state of the deployment
      group. See https://google.aip.dev/216.

  Messages:
    LabelsValue: Optional. Labels are key/value pairs that help to organize
      and filter resources.

  Fields:
    connectedEndpointGroups: Output only. The list of endpoint groups that are
      connected to this resource.
    createTime: Output only. The timestamp when the resource was created. See
      https://google.aip.dev/148#timestamps.
    description: Optional. User-provided description of the deployment group.
      Used as additional context for the deployment group.
    labels: Optional. Labels are key/value pairs that help to organize and
      filter resources.
    locations: Output only. The list of locations where the deployment group
      is present.
    name: Immutable. Identifier. The resource name of this deployment group,
      for example:
      `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`.
      See https://google.aip.dev/122 for more details.
    nestedDeployments: Output only. The list of Mirroring Deployments that
      belong to this group.
    network: Required. Immutable. The network that will be used for all child
      deployments, for example:
      `projects/{project}/global/networks/{network}`. See
      https://google.aip.dev/124.
    reconciling: Output only. The current state of the resource does not match
      the user's intended state, and the system is working to reconcile them.
      This is part of the normal operation (e.g. adding a new deployment to
      the group) See https://google.aip.dev/128.
    state: Output only. The current state of the deployment group. See
      https://google.aip.dev/216.
    updateTime: Output only. The timestamp when the resource was most recently
      updated. See https://google.aip.dev/148#timestamps.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the deployment group. See
    https://google.aip.dev/216.

    Values:
      STATE_UNSPECIFIED: State not set (this is not a valid state).
      ACTIVE: The deployment group is ready.
      CREATING: The deployment group is being created.
      DELETING: The deployment group is being deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are key/value pairs that help to organize and filter
    resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  connectedEndpointGroups = _messages.MessageField('MirroringDeploymentGroupConnectedEndpointGroup', 1, repeated=True)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  locations = _messages.MessageField('MirroringLocation', 5, repeated=True)
  name = _messages.StringField(6)
  nestedDeployments = _messages.MessageField('MirroringDeploymentGroupDeployment', 7, repeated=True)
  network = _messages.StringField(8)
  reconciling = _messages.BooleanField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  updateTime = _messages.StringField(11)


class MirroringDeploymentGroupConnectedEndpointGroup(_messages.Message):
  r"""An endpoint group connected to this deployment group.

  Fields:
    name: Output only. The connected endpoint group's resource name, for
      example:
      `projects/123456789/locations/global/mirroringEndpointGroups/my-eg`. See
      https://google.aip.dev/124.
  """

  name = _messages.StringField(1)


class MirroringDeploymentGroupDeployment(_messages.Message):
  r"""A deployment belonging to this deployment group.

  Enums:
    StateValueValuesEnum: Output only. Most recent known state of the
      deployment.

  Fields:
    name: Output only. The name of the Mirroring Deployment, in the format: `p
      rojects/{project}/locations/{location}/mirroringDeployments/{mirroring_d
      eployment}`.
    state: Output only. Most recent known state of the deployment.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Most recent known state of the deployment.

    Values:
      STATE_UNSPECIFIED: State not set (this is not a valid state).
      ACTIVE: The deployment is ready and in sync with the parent group.
      CREATING: The deployment is being created.
      DELETING: The deployment is being deleted.
      OUT_OF_SYNC: The deployment is out of sync with the parent group. In
        most cases, this is a result of a transient issue within the system
        (e.g. a delayed data-path config) and the system is expected to
        recover automatically. See the parent deployment group's state for
        more details.
      DELETE_FAILED: An attempt to delete the deployment has failed. This is a
        terminal state and the deployment is not expected to recover. The only
        permitted operation is to retry deleting the deployment.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    OUT_OF_SYNC = 4
    DELETE_FAILED = 5

  name = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class MirroringEndpoint(_messages.Message):
  r"""An endpoint is a managed mirroring collector that provides enhanced
  packet enrichment capabilities and support for multiple replica
  destinations. Endpoints are always part of a global endpoint group which
  represents a global "mirroring broker" service.

  Enums:
    StateValueValuesEnum: Output only. The current state of the endpoint. See
      https://google.aip.dev/216.

  Messages:
    LabelsValue: Optional. Labels are key/value pairs that help to organize
      and filter resources.

  Fields:
    createTime: Output only. The timestamp when the resource was created. See
      https://google.aip.dev/148#timestamps.
    description: Optional. User-provided description of the endpoint. Used as
      additional context for the endpoint.
    labels: Optional. Labels are key/value pairs that help to organize and
      filter resources.
    mirroringEndpointGroup: Required. Immutable. The endpoint group that this
      endpoint belongs to. Format is: `projects/{project}/locations/{location}
      /mirroringEndpointGroups/{mirroringEndpointGroup}`
    name: Immutable. Identifier. The resource name of this endpoint, for
      example: `projects/123456789/locations/us-
      central1-a/mirroringEndpoints/my-endpoint`. See
      https://google.aip.dev/122 for more details.
    reconciling: Output only. The current state of the resource does not match
      the user's intended state, and the system is working to reconcile them.
      This part of the normal operation (e.g. linking a new association to the
      parent group). See https://google.aip.dev/128.
    state: Output only. The current state of the endpoint. See
      https://google.aip.dev/216.
    updateTime: Output only. The timestamp when the resource was most recently
      updated. See https://google.aip.dev/148#timestamps.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the endpoint. See
    https://google.aip.dev/216.

    Values:
      STATE_UNSPECIFIED: State not set (this is not a valid state).
      CREATING: The endpoint is being created.
      ACTIVE: The endpoint is ready and in sync with the parent group.
      DELETING: The endpoint is being deleted.
      DELETE_FAILED: An attempt to delete the endpoint has failed. This is a
        terminal state and the endpoint is not expected to be usable as some
        of its resources have been deleted. The only permitted operation is to
        retry deleting the endpoint.
      OUT_OF_SYNC: The underlying data plane is out of sync with the endpoint.
        The endpoint is not expected to be usable. This state can result in
        undefined behavior.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    DELETE_FAILED = 4
    OUT_OF_SYNC = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are key/value pairs that help to organize and filter
    resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  mirroringEndpointGroup = _messages.StringField(4)
  name = _messages.StringField(5)
  reconciling = _messages.BooleanField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  updateTime = _messages.StringField(8)


class MirroringEndpointGroup(_messages.Message):
  r"""An endpoint group is a consumer frontend for a deployment group
  (backend). In order to configure mirroring for a network, consumers must
  create: - An association between their network and the endpoint group. - A
  security profile that points to the endpoint group. - A mirroring rule that
  references the security profile (group).

  Enums:
    StateValueValuesEnum: Output only. The current state of the endpoint
      group. See https://google.aip.dev/216.
    TypeValueValuesEnum: Required. Immutable. The type of the endpoint group.

  Messages:
    LabelsValue: Optional. Labels are key/value pairs that help to organize
      and filter resources.

  Fields:
    associations: Output only. List of associations to this endpoint group.
    connectedDeploymentGroups: Output only. List of details about the
      connected deployment groups to this endpoint group.
    createTime: Output only. The timestamp when the resource was created. See
      https://google.aip.dev/148#timestamps.
    description: Optional. User-provided description of the endpoint group.
      Used as additional context for the endpoint group.
    labels: Optional. Labels are key/value pairs that help to organize and
      filter resources.
    mirroringDeploymentGroup: Immutable. The deployment group that this DIRECT
      endpoint group is connected to, for example:
      `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`.
      See https://google.aip.dev/124.
    mirroringDeploymentGroups: Immutable. A list of the deployment groups that
      this BROKER endpoint group is connected to, for example:
      `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`.
      See https://google.aip.dev/124.
    name: Immutable. Identifier. The resource name of this endpoint group, for
      example:
      `projects/123456789/locations/global/mirroringEndpointGroups/my-eg`. See
      https://google.aip.dev/122 for more details.
    reconciling: Output only. The current state of the resource does not match
      the user's intended state, and the system is working to reconcile them.
      This is part of the normal operation (e.g. adding a new association to
      the group). See https://google.aip.dev/128.
    state: Output only. The current state of the endpoint group. See
      https://google.aip.dev/216.
    type: Required. Immutable. The type of the endpoint group.
    updateTime: Output only. The timestamp when the resource was most recently
      updated. See https://google.aip.dev/148#timestamps.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the endpoint group. See
    https://google.aip.dev/216.

    Values:
      STATE_UNSPECIFIED: State not set (this is not a valid state).
      ACTIVE: The endpoint group is ready and in sync with the target
        deployment group.
      CLOSED: The deployment group backing this endpoint group has been force-
        deleted. This endpoint group cannot be used and mirroring is
        effectively disabled.
      CREATING: The endpoint group is being created.
      DELETING: The endpoint group is being deleted.
      OUT_OF_SYNC: The endpoint group is out of sync with the backing
        deployment group. In most cases, this is a result of a transient issue
        within the system (e.g. an inaccessible location) and the system is
        expected to recover automatically. See the associations field for
        details per network and location.
      DELETE_FAILED: An attempt to delete the endpoint group has failed. This
        is a terminal state and the endpoint group is not expected to recover.
        The only permitted operation is to retry deleting the endpoint group.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CLOSED = 2
    CREATING = 3
    DELETING = 4
    OUT_OF_SYNC = 5
    DELETE_FAILED = 6

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The type of the endpoint group.

    Values:
      TYPE_UNSPECIFIED: Not set.
      DIRECT: An endpoint group that sends packets to a single deployment
        group.
      BROKER: An endpoint group that serves as a packet broker and may send
        packets to multiple deployment groups.
    """
    TYPE_UNSPECIFIED = 0
    DIRECT = 1
    BROKER = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are key/value pairs that help to organize and filter
    resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  associations = _messages.MessageField('MirroringEndpointGroupAssociationDetails', 1, repeated=True)
  connectedDeploymentGroups = _messages.MessageField('MirroringEndpointGroupConnectedDeploymentGroup', 2, repeated=True)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  mirroringDeploymentGroup = _messages.StringField(6)
  mirroringDeploymentGroups = _messages.StringField(7, repeated=True)
  name = _messages.StringField(8)
  reconciling = _messages.BooleanField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  type = _messages.EnumField('TypeValueValuesEnum', 11)
  updateTime = _messages.StringField(12)


class MirroringEndpointGroupAssociation(_messages.Message):
  r"""An endpoint group association represents a link between a network and an
  endpoint group in the organization. Creating an association creates the
  networking infrastructure linking the network to the endpoint group, but
  does not enable mirroring by itself. To enable mirroring, the user must also
  create a network firewall policy containing mirroring rules and associate it
  with the network.

  Enums:
    StateValueValuesEnum: Output only. Current state of the endpoint group
      association.

  Messages:
    LabelsValue: Optional. Labels are key/value pairs that help to organize
      and filter resources.

  Fields:
    createTime: Output only. The timestamp when the resource was created. See
      https://google.aip.dev/148#timestamps.
    labels: Optional. Labels are key/value pairs that help to organize and
      filter resources.
    locations: Output only. The list of locations where the association is
      configured. This information is retrieved from the linked endpoint
      group.
    locationsDetails: Output only. The list of locations where the association
      is present. This information is retrieved from the linked endpoint
      group, and not configured as part of the association itself.
    mirroringEndpointGroup: Immutable. The endpoint group that this
      association is connected to, for example:
      `projects/123456789/locations/global/mirroringEndpointGroups/my-eg`. See
      https://google.aip.dev/124.
    name: Immutable. Identifier. The resource name of this endpoint group
      association, for example: `projects/123456789/locations/global/mirroring
      EndpointGroupAssociations/my-eg-association`. See
      https://google.aip.dev/122 for more details.
    network: Immutable. The VPC network that is associated. for example:
      `projects/123456789/global/networks/my-network`. See
      https://google.aip.dev/124.
    reconciling: Output only. The current state of the resource does not match
      the user's intended state, and the system is working to reconcile them.
      This part of the normal operation (e.g. adding a new location to the
      target deployment group). See https://google.aip.dev/128.
    state: Output only. Current state of the endpoint group association.
    updateTime: Output only. The timestamp when the resource was most recently
      updated. See https://google.aip.dev/148#timestamps.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the endpoint group association.

    Values:
      STATE_UNSPECIFIED: Not set.
      ACTIVE: The association is ready and in sync with the linked endpoint
        group.
      CREATING: The association is being created.
      DELETING: The association is being deleted.
      CLOSED: The association is disabled due to a breaking change in another
        resource.
      OUT_OF_SYNC: The association is out of sync with the linked endpoint
        group. In most cases, this is a result of a transient issue within the
        system (e.g. an inaccessible location) and the system is expected to
        recover automatically. Check the `locations_details` field for more
        details.
      DELETE_FAILED: An attempt to delete the association has failed. This is
        a terminal state and the association is not expected to be usable as
        some of its resources have been deleted. The only permitted operation
        is to retry deleting the association.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    CLOSED = 4
    OUT_OF_SYNC = 5
    DELETE_FAILED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are key/value pairs that help to organize and filter
    resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locations = _messages.MessageField('MirroringLocation', 3, repeated=True)
  locationsDetails = _messages.MessageField('MirroringEndpointGroupAssociationLocationDetails', 4, repeated=True)
  mirroringEndpointGroup = _messages.StringField(5)
  name = _messages.StringField(6)
  network = _messages.StringField(7)
  reconciling = _messages.BooleanField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  updateTime = _messages.StringField(10)


class MirroringEndpointGroupAssociationDetails(_messages.Message):
  r"""The endpoint group's view of a connected association.

  Enums:
    StateValueValuesEnum: Output only. Most recent known state of the
      association.

  Fields:
    name: Output only. The connected association's resource name, for example:
      `projects/123456789/locations/global/mirroringEndpointGroupAssociations/
      my-ega`. See https://google.aip.dev/124.
    network: Output only. The associated network, for example:
      projects/123456789/global/networks/my-network. See
      https://google.aip.dev/124.
    state: Output only. Most recent known state of the association.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Most recent known state of the association.

    Values:
      STATE_UNSPECIFIED: Not set.
      ACTIVE: The association is ready and in sync with the linked endpoint
        group.
      CREATING: The association is being created.
      DELETING: The association is being deleted.
      CLOSED: The association is disabled due to a breaking change in another
        resource.
      OUT_OF_SYNC: The association is out of sync with the linked endpoint
        group. In most cases, this is a result of a transient issue within the
        system (e.g. an inaccessible location) and the system is expected to
        recover automatically. Check the `locations_details` field for more
        details.
      DELETE_FAILED: An attempt to delete the association has failed. This is
        a terminal state and the association is not expected to be usable as
        some of its resources have been deleted. The only permitted operation
        is to retry deleting the association.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    CLOSED = 4
    OUT_OF_SYNC = 5
    DELETE_FAILED = 6

  name = _messages.StringField(1)
  network = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class MirroringEndpointGroupAssociationLocationDetails(_messages.Message):
  r"""Contains details about the state of an association in a specific cloud
  location.

  Enums:
    StateValueValuesEnum: Output only. The current state of the association in
      this location.

  Fields:
    location: Output only. The cloud location, e.g. "us-central1-a" or "asia-
      south1".
    state: Output only. The current state of the association in this location.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the association in this location.

    Values:
      STATE_UNSPECIFIED: Not set.
      ACTIVE: The association is ready and in sync with the linked endpoint
        group.
      OUT_OF_SYNC: The association is out of sync with the linked endpoint
        group. In most cases, this is a result of a transient issue within the
        system (e.g. an inaccessible location) and the system is expected to
        recover automatically.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    OUT_OF_SYNC = 2

  location = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class MirroringEndpointGroupConnectedDeploymentGroup(_messages.Message):
  r"""The endpoint group's view of a connected deployment group.

  Fields:
    locations: Output only. The list of locations where the deployment group
      is present.
    name: Output only. The connected deployment group's resource name, for
      example:
      `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`.
      See https://google.aip.dev/124.
  """

  locations = _messages.MessageField('MirroringLocation', 1, repeated=True)
  name = _messages.StringField(2)


class MirroringLocation(_messages.Message):
  r"""Details about mirroring in a specific cloud location.

  Enums:
    StateValueValuesEnum: Output only. The current state of the association in
      this location.

  Fields:
    location: Output only. The cloud location, e.g. "us-central1-a" or "asia-
      south1".
    state: Output only. The current state of the association in this location.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the association in this location.

    Values:
      STATE_UNSPECIFIED: State not set (this is not a valid state).
      ACTIVE: The resource is ready and in sync in the location.
      OUT_OF_SYNC: The resource is out of sync in the location. In most cases,
        this is a result of a transient issue within the system (e.g. an
        inaccessible location) and the system is expected to recover
        automatically.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    OUT_OF_SYNC = 2

  location = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class NetworksecurityOrganizationsLocationsAddressGroupsAddItemsRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsAddItemsRequest
  object.

  Fields:
    addAddressGroupItemsRequest: A AddAddressGroupItemsRequest resource to be
      passed as the request body.
    addressGroup: Required. A name of the AddressGroup to add items to. Must
      be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
  """

  addAddressGroupItemsRequest = _messages.MessageField('AddAddressGroupItemsRequest', 1)
  addressGroup = _messages.StringField(2, required=True)


class NetworksecurityOrganizationsLocationsAddressGroupsCloneItemsRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsCloneItemsRequest
  object.

  Fields:
    addressGroup: Required. A name of the AddressGroup to clone items to. Must
      be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
    cloneAddressGroupItemsRequest: A CloneAddressGroupItemsRequest resource to
      be passed as the request body.
  """

  addressGroup = _messages.StringField(1, required=True)
  cloneAddressGroupItemsRequest = _messages.MessageField('CloneAddressGroupItemsRequest', 2)


class NetworksecurityOrganizationsLocationsAddressGroupsCreateRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsCreateRequest
  object.

  Fields:
    addressGroup: A AddressGroup resource to be passed as the request body.
    addressGroupId: Required. Short name of the AddressGroup resource to be
      created. This value should be 1-63 characters long, containing only
      letters, numbers, hyphens, and underscores, and should not start with a
      number. E.g. "authz_policy".
    parent: Required. The parent resource of the AddressGroup. Must be in the
      format `projects/*/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  addressGroup = _messages.MessageField('AddressGroup', 1)
  addressGroupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityOrganizationsLocationsAddressGroupsDeleteRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsDeleteRequest
  object.

  Fields:
    name: Required. A name of the AddressGroup to delete. Must be in the
      format `projects/*/locations/{location}/addressGroups/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityOrganizationsLocationsAddressGroupsGetRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsGetRequest object.

  Fields:
    name: Required. A name of the AddressGroup to get. Must be in the format
      `projects/*/locations/{location}/addressGroups/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityOrganizationsLocationsAddressGroupsListReferencesRequest(_messages.Message):
  r"""A
  NetworksecurityOrganizationsLocationsAddressGroupsListReferencesRequest
  object.

  Fields:
    addressGroup: Required. A name of the AddressGroup to clone items to. Must
      be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
    pageSize: The maximum number of references to return. If unspecified,
      server will pick an appropriate default. Server may return fewer items
      than requested. A caller should only rely on response's next_page_token
      to determine if there are more AddressGroupUsers left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
  """

  addressGroup = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class NetworksecurityOrganizationsLocationsAddressGroupsListRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsListRequest object.

  Fields:
    pageSize: Maximum number of AddressGroups to return per call.
    pageToken: The value returned by the last `ListAddressGroupsResponse`
      Indicates that this is a continuation of a prior `ListAddressGroups`
      call, and that the system should return the next page of data.
    parent: Required. The project and location from which the AddressGroups
      should be listed, specified in the format
      `projects/*/locations/{location}`.
    returnPartialSuccess: Optional. If true, allow partial responses for
      multi-regional Aggregated List requests.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  returnPartialSuccess = _messages.BooleanField(4)


class NetworksecurityOrganizationsLocationsAddressGroupsPatchRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsPatchRequest object.

  Fields:
    addressGroup: A AddressGroup resource to be passed as the request body.
    name: Required. Name of the AddressGroup resource. It matches pattern
      `projects/*/locations/{location}/addressGroups/`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the AddressGroup resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  addressGroup = _messages.MessageField('AddressGroup', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityOrganizationsLocationsAddressGroupsRemoveItemsRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsRemoveItemsRequest
  object.

  Fields:
    addressGroup: Required. A name of the AddressGroup to remove items from.
      Must be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
    removeAddressGroupItemsRequest: A RemoveAddressGroupItemsRequest resource
      to be passed as the request body.
  """

  addressGroup = _messages.StringField(1, required=True)
  removeAddressGroupItemsRequest = _messages.MessageField('RemoveAddressGroupItemsRequest', 2)


class NetworksecurityOrganizationsLocationsFirewallEndpointsCreateRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsFirewallEndpointsCreateRequest
  object.

  Fields:
    firewallEndpoint: A FirewallEndpoint resource to be passed as the request
      body.
    firewallEndpointId: Required. Id of the requesting object. If auto-
      generating Id server-side, remove this field and firewall_endpoint_id
      from the method_signature of Create RPC.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  firewallEndpoint = _messages.MessageField('FirewallEndpoint', 1)
  firewallEndpointId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityOrganizationsLocationsFirewallEndpointsDeleteRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsFirewallEndpointsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityOrganizationsLocationsFirewallEndpointsGetRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsFirewallEndpointsGetRequest
  object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityOrganizationsLocationsFirewallEndpointsListRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsFirewallEndpointsListRequest
  object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListEndpointsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityOrganizationsLocationsFirewallEndpointsPatchRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsFirewallEndpointsPatchRequest
  object.

  Fields:
    firewallEndpoint: A FirewallEndpoint resource to be passed as the request
      body.
    name: Immutable. Identifier. name of resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Endpoint resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  firewallEndpoint = _messages.MessageField('FirewallEndpoint', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityOrganizationsLocationsOperationsCancelRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworksecurityOrganizationsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityOrganizationsLocationsOperationsGetRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityOrganizationsLocationsOperationsListRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class NetworksecurityOrganizationsLocationsSecurityProfileGroupsCreateRequest(_messages.Message):
  r"""A
  NetworksecurityOrganizationsLocationsSecurityProfileGroupsCreateRequest
  object.

  Fields:
    parent: Required. The parent resource of the SecurityProfileGroup. Must be
      in the format `projects|organizations/*/locations/{location}`.
    securityProfileGroup: A SecurityProfileGroup resource to be passed as the
      request body.
    securityProfileGroupId: Required. Short name of the SecurityProfileGroup
      resource to be created. This value should be 1-63 characters long,
      containing only letters, numbers, hyphens, and underscores, and should
      not start with a number. E.g. "security_profile_group1".
  """

  parent = _messages.StringField(1, required=True)
  securityProfileGroup = _messages.MessageField('SecurityProfileGroup', 2)
  securityProfileGroupId = _messages.StringField(3)


class NetworksecurityOrganizationsLocationsSecurityProfileGroupsDeleteRequest(_messages.Message):
  r"""A
  NetworksecurityOrganizationsLocationsSecurityProfileGroupsDeleteRequest
  object.

  Fields:
    etag: Optional. If client provided etag is out of date, delete will return
      FAILED_PRECONDITION error.
    name: Required. A name of the SecurityProfileGroup to delete. Must be in
      the format `projects|organizations/*/locations/{location}/securityProfil
      eGroups/{security_profile_group}`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class NetworksecurityOrganizationsLocationsSecurityProfileGroupsGetRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfileGroupsGetRequest
  object.

  Fields:
    name: Required. A name of the SecurityProfileGroup to get. Must be in the
      format `projects|organizations/*/locations/{location}/securityProfileGro
      ups/{security_profile_group}`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityOrganizationsLocationsSecurityProfileGroupsListRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfileGroupsListRequest
  object.

  Fields:
    pageSize: Maximum number of SecurityProfileGroups to return per call.
    pageToken: The value returned by the last
      `ListSecurityProfileGroupsResponse` Indicates that this is a
      continuation of a prior `ListSecurityProfileGroups` call, and that the
      system should return the next page of data.
    parent: Required. The project or organization and location from which the
      SecurityProfileGroups should be listed, specified in the format
      `projects|organizations/*/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityOrganizationsLocationsSecurityProfileGroupsPatchRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfileGroupsPatchRequest
  object.

  Fields:
    name: Immutable. Identifier. Name of the SecurityProfileGroup resource. It
      matches pattern `projects|organizations/*/locations/{location}/securityP
      rofileGroups/{security_profile_group}`.
    securityProfileGroup: A SecurityProfileGroup resource to be passed as the
      request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the SecurityProfileGroup resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask.
  """

  name = _messages.StringField(1, required=True)
  securityProfileGroup = _messages.MessageField('SecurityProfileGroup', 2)
  updateMask = _messages.StringField(3)


class NetworksecurityOrganizationsLocationsSecurityProfilesCreateRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfilesCreateRequest
  object.

  Fields:
    parent: Required. The parent resource of the SecurityProfile. Must be in
      the format `projects|organizations/*/locations/{location}`.
    securityProfile: A SecurityProfile resource to be passed as the request
      body.
    securityProfileId: Required. Short name of the SecurityProfile resource to
      be created. This value should be 1-63 characters long, containing only
      letters, numbers, hyphens, and underscores, and should not start with a
      number. E.g. "security_profile1".
  """

  parent = _messages.StringField(1, required=True)
  securityProfile = _messages.MessageField('SecurityProfile', 2)
  securityProfileId = _messages.StringField(3)


class NetworksecurityOrganizationsLocationsSecurityProfilesDeleteRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfilesDeleteRequest
  object.

  Fields:
    etag: Optional. If client provided etag is out of date, delete will return
      FAILED_PRECONDITION error.
    name: Required. A name of the SecurityProfile to delete. Must be in the
      format `projects|organizations/*/locations/{location}/securityProfiles/{
      security_profile_id}`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class NetworksecurityOrganizationsLocationsSecurityProfilesGetRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfilesGetRequest
  object.

  Fields:
    name: Required. A name of the SecurityProfile to get. Must be in the
      format `projects|organizations/*/locations/{location}/securityProfiles/{
      security_profile_id}`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityOrganizationsLocationsSecurityProfilesListRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfilesListRequest
  object.

  Fields:
    pageSize: Maximum number of SecurityProfiles to return per call.
    pageToken: The value returned by the last `ListSecurityProfilesResponse`
      Indicates that this is a continuation of a prior `ListSecurityProfiles`
      call, and that the system should return the next page of data.
    parent: Required. The project or organization and location from which the
      SecurityProfiles should be listed, specified in the format
      `projects|organizations/*/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityOrganizationsLocationsSecurityProfilesPatchRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfilesPatchRequest
  object.

  Fields:
    name: Immutable. Identifier. Name of the SecurityProfile resource. It
      matches pattern `projects|organizations/*/locations/{location}/securityP
      rofiles/{security_profile}`.
    securityProfile: A SecurityProfile resource to be passed as the request
      body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the SecurityProfile resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask.
  """

  name = _messages.StringField(1, required=True)
  securityProfile = _messages.MessageField('SecurityProfile', 2)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsAddressGroupsAddItemsRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsAddItemsRequest object.

  Fields:
    addAddressGroupItemsRequest: A AddAddressGroupItemsRequest resource to be
      passed as the request body.
    addressGroup: Required. A name of the AddressGroup to add items to. Must
      be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
  """

  addAddressGroupItemsRequest = _messages.MessageField('AddAddressGroupItemsRequest', 1)
  addressGroup = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAddressGroupsCloneItemsRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsCloneItemsRequest object.

  Fields:
    addressGroup: Required. A name of the AddressGroup to clone items to. Must
      be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
    cloneAddressGroupItemsRequest: A CloneAddressGroupItemsRequest resource to
      be passed as the request body.
  """

  addressGroup = _messages.StringField(1, required=True)
  cloneAddressGroupItemsRequest = _messages.MessageField('CloneAddressGroupItemsRequest', 2)


class NetworksecurityProjectsLocationsAddressGroupsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsCreateRequest object.

  Fields:
    addressGroup: A AddressGroup resource to be passed as the request body.
    addressGroupId: Required. Short name of the AddressGroup resource to be
      created. This value should be 1-63 characters long, containing only
      letters, numbers, hyphens, and underscores, and should not start with a
      number. E.g. "authz_policy".
    parent: Required. The parent resource of the AddressGroup. Must be in the
      format `projects/*/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  addressGroup = _messages.MessageField('AddressGroup', 1)
  addressGroupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsAddressGroupsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsDeleteRequest object.

  Fields:
    name: Required. A name of the AddressGroup to delete. Must be in the
      format `projects/*/locations/{location}/addressGroups/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsAddressGroupsGetIamPolicyRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAddressGroupsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsGetRequest object.

  Fields:
    name: Required. A name of the AddressGroup to get. Must be in the format
      `projects/*/locations/{location}/addressGroups/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsAddressGroupsListReferencesRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsListReferencesRequest
  object.

  Fields:
    addressGroup: Required. A name of the AddressGroup to clone items to. Must
      be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
    pageSize: The maximum number of references to return. If unspecified,
      server will pick an appropriate default. Server may return fewer items
      than requested. A caller should only rely on response's next_page_token
      to determine if there are more AddressGroupUsers left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
  """

  addressGroup = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class NetworksecurityProjectsLocationsAddressGroupsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsListRequest object.

  Fields:
    pageSize: Maximum number of AddressGroups to return per call.
    pageToken: The value returned by the last `ListAddressGroupsResponse`
      Indicates that this is a continuation of a prior `ListAddressGroups`
      call, and that the system should return the next page of data.
    parent: Required. The project and location from which the AddressGroups
      should be listed, specified in the format
      `projects/*/locations/{location}`.
    returnPartialSuccess: Optional. If true, allow partial responses for
      multi-regional Aggregated List requests.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  returnPartialSuccess = _messages.BooleanField(4)


class NetworksecurityProjectsLocationsAddressGroupsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsPatchRequest object.

  Fields:
    addressGroup: A AddressGroup resource to be passed as the request body.
    name: Required. Name of the AddressGroup resource. It matches pattern
      `projects/*/locations/{location}/addressGroups/`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the AddressGroup resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  addressGroup = _messages.MessageField('AddressGroup', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsAddressGroupsRemoveItemsRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsRemoveItemsRequest
  object.

  Fields:
    addressGroup: Required. A name of the AddressGroup to remove items from.
      Must be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
    removeAddressGroupItemsRequest: A RemoveAddressGroupItemsRequest resource
      to be passed as the request body.
  """

  addressGroup = _messages.StringField(1, required=True)
  removeAddressGroupItemsRequest = _messages.MessageField('RemoveAddressGroupItemsRequest', 2)


class NetworksecurityProjectsLocationsAddressGroupsSetIamPolicyRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAddressGroupsTestIamPermissionsRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAuthorizationPoliciesCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthorizationPoliciesCreateRequest
  object.

  Fields:
    authorizationPolicy: A AuthorizationPolicy resource to be passed as the
      request body.
    authorizationPolicyId: Required. Short name of the AuthorizationPolicy
      resource to be created. This value should be 1-63 characters long,
      containing only letters, numbers, hyphens, and underscores, and should
      not start with a number. E.g. "authz_policy".
    parent: Required. The parent resource of the AuthorizationPolicy. Must be
      in the format `projects/{project}/locations/{location}`.
  """

  authorizationPolicy = _messages.MessageField('AuthorizationPolicy', 1)
  authorizationPolicyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsAuthorizationPoliciesDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthorizationPoliciesDeleteRequest
  object.

  Fields:
    name: Required. A name of the AuthorizationPolicy to delete. Must be in
      the format
      `projects/{project}/locations/{location}/authorizationPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsAuthorizationPoliciesGetIamPolicyRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsAuthorizationPoliciesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAuthorizationPoliciesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthorizationPoliciesGetRequest
  object.

  Fields:
    name: Required. A name of the AuthorizationPolicy to get. Must be in the
      format
      `projects/{project}/locations/{location}/authorizationPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsAuthorizationPoliciesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthorizationPoliciesListRequest
  object.

  Fields:
    pageSize: Maximum number of AuthorizationPolicies to return per call.
    pageToken: The value returned by the last
      `ListAuthorizationPoliciesResponse` Indicates that this is a
      continuation of a prior `ListAuthorizationPolicies` call, and that the
      system should return the next page of data.
    parent: Required. The project and location from which the
      AuthorizationPolicies should be listed, specified in the format
      `projects/{project}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsAuthorizationPoliciesPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthorizationPoliciesPatchRequest
  object.

  Fields:
    authorizationPolicy: A AuthorizationPolicy resource to be passed as the
      request body.
    name: Required. Name of the AuthorizationPolicy resource. It matches
      pattern
      `projects/{project}/locations/{location}/authorizationPolicies/`.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the AuthorizationPolicy resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  authorizationPolicy = _messages.MessageField('AuthorizationPolicy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsAuthorizationPoliciesSetIamPolicyRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsAuthorizationPoliciesSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAuthorizationPoliciesTestIamPermissionsRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthorizationPoliciesTestIamPermission
  sRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAuthzPoliciesCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthzPoliciesCreateRequest object.

  Fields:
    authzPolicy: A AuthzPolicy resource to be passed as the request body.
    authzPolicyId: Required. User-provided ID of the `AuthzPolicy` resource to
      be created.
    parent: Required. The parent resource of the `AuthzPolicy` resource. Must
      be in the format `projects/{project}/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      can ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  authzPolicy = _messages.MessageField('AuthzPolicy', 1)
  authzPolicyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsAuthzPoliciesDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthzPoliciesDeleteRequest object.

  Fields:
    name: Required. The name of the `AuthzPolicy` resource to delete. Must be
      in the format
      `projects/{project}/locations/{location}/authzPolicies/{authz_policy}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      can ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsAuthzPoliciesGetIamPolicyRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthzPoliciesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAuthzPoliciesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthzPoliciesGetRequest object.

  Fields:
    name: Required. A name of the `AuthzPolicy` resource to get. Must be in
      the format
      `projects/{project}/locations/{location}/authzPolicies/{authz_policy}`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsAuthzPoliciesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthzPoliciesListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. The server might return fewer
      items than requested. If unspecified, the server picks an appropriate
      default.
    pageToken: Optional. A token identifying a page of results that the server
      returns.
    parent: Required. The project and location from which the `AuthzPolicy`
      resources are listed, specified in the following format:
      `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsAuthzPoliciesPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthzPoliciesPatchRequest object.

  Fields:
    authzPolicy: A AuthzPolicy resource to be passed as the request body.
    name: Required. Identifier. Name of the `AuthzPolicy` resource in the
      following format:
      `projects/{project}/locations/{location}/authzPolicies/{authz_policy}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      can ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Used to specify the fields to be overwritten in the
      `AuthzPolicy` resource by the update. The fields specified in the
      `update_mask` are relative to the resource, not the full request. A
      field is overwritten if it is in the mask. If the user does not specify
      a mask, then all fields are overwritten.
  """

  authzPolicy = _messages.MessageField('AuthzPolicy', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsAuthzPoliciesSetIamPolicyRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthzPoliciesSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAuthzPoliciesTestIamPermissionsRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthzPoliciesTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsBackendAuthenticationConfigsCreateRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsBackendAuthenticationConfigsCreateRequest
  object.

  Fields:
    backendAuthenticationConfig: A BackendAuthenticationConfig resource to be
      passed as the request body.
    backendAuthenticationConfigId: Required. Short name of the
      BackendAuthenticationConfig resource to be created. This value should be
      1-63 characters long, containing only letters, numbers, hyphens, and
      underscores, and should not start with a number. E.g. "backend-auth-
      config".
    parent: Required. The parent resource of the BackendAuthenticationConfig.
      Must be in the format `projects/*/locations/{location}`.
  """

  backendAuthenticationConfig = _messages.MessageField('BackendAuthenticationConfig', 1)
  backendAuthenticationConfigId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsBackendAuthenticationConfigsDeleteRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsBackendAuthenticationConfigsDeleteRequest
  object.

  Fields:
    etag: Optional. Etag of the resource. If this is provided, it must match
      the server's etag.
    name: Required. A name of the BackendAuthenticationConfig to delete. Must
      be in the format
      `projects/*/locations/{location}/backendAuthenticationConfigs/*`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsBackendAuthenticationConfigsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsBackendAuthenticationConfigsGetRequest
  object.

  Fields:
    name: Required. A name of the BackendAuthenticationConfig to get. Must be
      in the format
      `projects/*/locations/{location}/backendAuthenticationConfigs/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsBackendAuthenticationConfigsListRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsBackendAuthenticationConfigsListRequest
  object.

  Fields:
    pageSize: Maximum number of BackendAuthenticationConfigs to return per
      call.
    pageToken: The value returned by the last
      `ListBackendAuthenticationConfigsResponse` Indicates that this is a
      continuation of a prior `ListBackendAuthenticationConfigs` call, and
      that the system should return the next page of data.
    parent: Required. The project and location from which the
      BackendAuthenticationConfigs should be listed, specified in the format
      `projects/*/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsBackendAuthenticationConfigsPatchRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsBackendAuthenticationConfigsPatchRequest
  object.

  Fields:
    backendAuthenticationConfig: A BackendAuthenticationConfig resource to be
      passed as the request body.
    name: Required. Name of the BackendAuthenticationConfig resource. It
      matches the pattern `projects/*/locations/{location}/backendAuthenticati
      onConfigs/{backend_authentication_config}`
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the BackendAuthenticationConfig resource by the update.
      The fields specified in the update_mask are relative to the resource,
      not the full request. A field will be overwritten if it is in the mask.
      If the user does not provide a mask then all fields will be overwritten.
  """

  backendAuthenticationConfig = _messages.MessageField('BackendAuthenticationConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsClientTlsPoliciesCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsClientTlsPoliciesCreateRequest object.

  Fields:
    clientTlsPolicy: A ClientTlsPolicy resource to be passed as the request
      body.
    clientTlsPolicyId: Required. Short name of the ClientTlsPolicy resource to
      be created. This value should be 1-63 characters long, containing only
      letters, numbers, hyphens, and underscores, and should not start with a
      number. E.g. "client_mtls_policy".
    parent: Required. The parent resource of the ClientTlsPolicy. Must be in
      the format `projects/*/locations/{location}`.
  """

  clientTlsPolicy = _messages.MessageField('ClientTlsPolicy', 1)
  clientTlsPolicyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsClientTlsPoliciesDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsClientTlsPoliciesDeleteRequest object.

  Fields:
    name: Required. A name of the ClientTlsPolicy to delete. Must be in the
      format `projects/*/locations/{location}/clientTlsPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsClientTlsPoliciesGetIamPolicyRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsClientTlsPoliciesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsClientTlsPoliciesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsClientTlsPoliciesGetRequest object.

  Fields:
    name: Required. A name of the ClientTlsPolicy to get. Must be in the
      format `projects/*/locations/{location}/clientTlsPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsClientTlsPoliciesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsClientTlsPoliciesListRequest object.

  Fields:
    pageSize: Maximum number of ClientTlsPolicies to return per call.
    pageToken: The value returned by the last `ListClientTlsPoliciesResponse`
      Indicates that this is a continuation of a prior `ListClientTlsPolicies`
      call, and that the system should return the next page of data.
    parent: Required. The project and location from which the
      ClientTlsPolicies should be listed, specified in the format
      `projects/*/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsClientTlsPoliciesPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsClientTlsPoliciesPatchRequest object.

  Fields:
    clientTlsPolicy: A ClientTlsPolicy resource to be passed as the request
      body.
    name: Required. Name of the ClientTlsPolicy resource. It matches the
      pattern `projects/{project}/locations/{location}/clientTlsPolicies/{clie
      nt_tls_policy}`
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the ClientTlsPolicy resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  clientTlsPolicy = _messages.MessageField('ClientTlsPolicy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsClientTlsPoliciesSetIamPolicyRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsClientTlsPoliciesSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsClientTlsPoliciesTestIamPermissionsRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsClientTlsPoliciesTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsDnsThreatDetectorsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsDnsThreatDetectorsCreateRequest
  object.

  Fields:
    dnsThreatDetector: A DnsThreatDetector resource to be passed as the
      request body.
    dnsThreatDetectorId: Optional. Id of the requesting DnsThreatDetector
      object. If this field is not supplied, the service will generate an
      identifier.
    parent: Required. Value for parent of the DnsThreatDetector resource.
  """

  dnsThreatDetector = _messages.MessageField('DnsThreatDetector', 1)
  dnsThreatDetectorId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsDnsThreatDetectorsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsDnsThreatDetectorsDeleteRequest
  object.

  Fields:
    name: Required. Name of the DnsThreatDetector resource.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsDnsThreatDetectorsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsDnsThreatDetectorsGetRequest object.

  Fields:
    name: Required. Name of the DnsThreatDetector resource
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsDnsThreatDetectorsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsDnsThreatDetectorsListRequest object.

  Fields:
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A page token, received from a previous
      `ListDnsThreatDetectorsRequest` call. Provide this to retrieve the
      subsequent page.
    parent: Required. Parent value for ListDnsThreatDetectorsRequest
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsDnsThreatDetectorsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsDnsThreatDetectorsPatchRequest object.

  Fields:
    dnsThreatDetector: A DnsThreatDetector resource to be passed as the
      request body.
    name: Immutable. Identifier. Name of the DnsThreatDetector resource.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the DnsThreatDetector resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the mask
      is not provided then all fields present in the request will be
      overwritten.
  """

  dnsThreatDetector = _messages.MessageField('DnsThreatDetector', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsFirewallAttachmentsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsFirewallAttachmentsCreateRequest
  object.

  Fields:
    firewallAttachment: A FirewallAttachment resource to be passed as the
      request body.
    firewallAttachmentId: Required. Id of the requesting object. If auto-
      generating Id server-side, remove this field and firewall_attachment_id
      from the method_signature of Create RPC.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  firewallAttachment = _messages.MessageField('FirewallAttachment', 1)
  firewallAttachmentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsFirewallAttachmentsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsFirewallAttachmentsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsFirewallAttachmentsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsFirewallAttachmentsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsFirewallAttachmentsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsFirewallAttachmentsListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListAttachmentsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsFirewallAttachmentsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsFirewallAttachmentsPatchRequest
  object.

  Fields:
    firewallAttachment: A FirewallAttachment resource to be passed as the
      request body.
    name: Immutable. Identifier. name of resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Association resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  firewallAttachment = _messages.MessageField('FirewallAttachment', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsFirewallEndpointAssociationsCreateRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsFirewallEndpointAssociationsCreateRequest
  object.

  Fields:
    firewallEndpointAssociation: A FirewallEndpointAssociation resource to be
      passed as the request body.
    firewallEndpointAssociationId: Optional. Id of the requesting object. If
      auto-generating Id server-side, remove this field and
      firewall_endpoint_association_id from the method_signature of Create
      RPC.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  firewallEndpointAssociation = _messages.MessageField('FirewallEndpointAssociation', 1)
  firewallEndpointAssociationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsFirewallEndpointAssociationsDeleteRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsFirewallEndpointAssociationsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsFirewallEndpointAssociationsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsFirewallEndpointAssociationsGetRequest
  object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsFirewallEndpointAssociationsListRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsFirewallEndpointAssociationsListRequest
  object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListAssociationsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsFirewallEndpointAssociationsPatchRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsFirewallEndpointAssociationsPatchRequest
  object.

  Fields:
    firewallEndpointAssociation: A FirewallEndpointAssociation resource to be
      passed as the request body.
    name: Immutable. Identifier. name of resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Association resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  firewallEndpointAssociation = _messages.MessageField('FirewallEndpointAssociation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsGatewayAttachmentsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewayAttachmentsCreateRequest
  object.

  Fields:
    gatewayAttachment: A GatewayAttachment resource to be passed as the
      request body.
    gatewayAttachmentId: Required. Id of the resource
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  gatewayAttachment = _messages.MessageField('GatewayAttachment', 1)
  gatewayAttachmentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsGatewayAttachmentsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewayAttachmentsDeleteRequest
  object.

  Fields:
    force: Optional. If set to true, any GatewayEndpoints that are currently
      attached to the GatewayAttachment will be deleted. If set to false, the
      request will fail if there are any GatewayEndpoints attached to the
      GatewayAttachment.
    name: Required. The resource name of the GatewayAttachment to delete.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsCrea
  teRequest object.

  Fields:
    gatewayEndpoint: A GatewayEndpoint resource to be passed as the request
      body.
    gatewayEndpointId: Required. Id of the requesting object If auto-
      generating Id server-side, remove this field and gateway_endpoint_id
      from the method_signature of Create RPC
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  gatewayEndpoint = _messages.MessageField('GatewayEndpoint', 1)
  gatewayEndpointId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsDele
  teRequest object.

  Fields:
    name: Required. The resource name of the GatewayEndpoint to delete.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsGetRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsGetRequest
  object.

  Fields:
    name: Required. The resource name of the GatewayEndpoint.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsList
  Request object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListGatewayEndpointsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsGatewayAttachmentsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewayAttachmentsGetRequest object.

  Fields:
    name: Required. The resource name of the GatewayAttachment.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsGatewayAttachmentsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewayAttachmentsListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListGatewayAttachmentsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewaySecurityPoliciesCreateRequest
  object.

  Fields:
    gatewaySecurityPolicy: A GatewaySecurityPolicy resource to be passed as
      the request body.
    gatewaySecurityPolicyId: Required. Short name of the GatewaySecurityPolicy
      resource to be created. This value should be 1-63 characters long,
      containing only letters, numbers, hyphens, and underscores, and should
      not start with a number. E.g. "gateway_security_policy1".
    parent: Required. The parent resource of the GatewaySecurityPolicy. Must
      be in the format `projects/{project}/locations/{location}`.
  """

  gatewaySecurityPolicy = _messages.MessageField('GatewaySecurityPolicy', 1)
  gatewaySecurityPolicyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewaySecurityPoliciesDeleteRequest
  object.

  Fields:
    name: Required. A name of the GatewaySecurityPolicy to delete. Must be in
      the format
      `projects/{project}/locations/{location}/gatewaySecurityPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewaySecurityPoliciesGetRequest
  object.

  Fields:
    name: Required. A name of the GatewaySecurityPolicy to get. Must be in the
      format
      `projects/{project}/locations/{location}/gatewaySecurityPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewaySecurityPoliciesListRequest
  object.

  Fields:
    pageSize: Maximum number of GatewaySecurityPolicies to return per call.
    pageToken: The value returned by the last
      'ListGatewaySecurityPoliciesResponse' Indicates that this is a
      continuation of a prior 'ListGatewaySecurityPolicies' call, and that the
      system should return the next page of data.
    parent: Required. The project and location from which the
      GatewaySecurityPolicies should be listed, specified in the format
      `projects/{project}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewaySecurityPoliciesPatchRequest
  object.

  Fields:
    gatewaySecurityPolicy: A GatewaySecurityPolicy resource to be passed as
      the request body.
    name: Required. Name of the resource. Name is of the form projects/{projec
      t}/locations/{location}/gatewaySecurityPolicies/{gateway_security_policy
      } gateway_security_policy should match the
      pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the GatewaySecurityPolicy resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  gatewaySecurityPolicy = _messages.MessageField('GatewaySecurityPolicy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesCreateRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesCreateRequest
  object.

  Fields:
    gatewaySecurityPolicyRule: A GatewaySecurityPolicyRule resource to be
      passed as the request body.
    gatewaySecurityPolicyRuleId: The ID to use for the rule, which will become
      the final component of the rule's resource name. This value should be
      4-63 characters, and valid characters are /a-z-/.
    parent: Required. The parent where this rule will be created. Format :
      projects/{project}/location/{location}/gatewaySecurityPolicies/*
  """

  gatewaySecurityPolicyRule = _messages.MessageField('GatewaySecurityPolicyRule', 1)
  gatewaySecurityPolicyRuleId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesDeleteRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesDeleteRequest
  object.

  Fields:
    name: Required. A name of the GatewaySecurityPolicyRule to delete. Must be
      in the format `projects/{project}/locations/{location}/gatewaySecurityPo
      licies/{gatewaySecurityPolicy}/rules/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesGetRequest
  object.

  Fields:
    name: Required. The name of the GatewaySecurityPolicyRule to retrieve.
      Format:
      projects/{project}/location/{location}/gatewaySecurityPolicies/*/rules/*
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesListRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesListRequest
  object.

  Fields:
    pageSize: Maximum number of GatewaySecurityPolicyRules to return per call.
    pageToken: The value returned by the last
      'ListGatewaySecurityPolicyRulesResponse' Indicates that this is a
      continuation of a prior 'ListGatewaySecurityPolicyRules' call, and that
      the system should return the next page of data.
    parent: Required. The project, location and GatewaySecurityPolicy from
      which the GatewaySecurityPolicyRules should be listed, specified in the
      format `projects/{project}/locations/{location}/gatewaySecurityPolicies/
      {gatewaySecurityPolicy}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesPatchRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesPatchRequest
  object.

  Fields:
    gatewaySecurityPolicyRule: A GatewaySecurityPolicyRule resource to be
      passed as the request body.
    name: Required. Immutable. Name of the resource. ame is the full resource
      name so projects/{project}/locations/{location}/gatewaySecurityPolicies/
      {gateway_security_policy}/rules/{rule} rule should match the pattern:
      (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the GatewaySecurityPolicy resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  gatewaySecurityPolicyRule = _messages.MessageField('GatewaySecurityPolicyRule', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsInterceptDeploymentGroupsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptDeploymentGroupsCreateRequest
  object.

  Fields:
    interceptDeploymentGroup: A InterceptDeploymentGroup resource to be passed
      as the request body.
    interceptDeploymentGroupId: Required. The ID to use for the new deployment
      group, which will become the final component of the deployment group's
      resource name.
    parent: Required. The parent resource where this deployment group will be
      created. Format: projects/{project}/locations/{location}
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  interceptDeploymentGroup = _messages.MessageField('InterceptDeploymentGroup', 1)
  interceptDeploymentGroupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsInterceptDeploymentGroupsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptDeploymentGroupsDeleteRequest
  object.

  Fields:
    name: Required. The deployment group to delete.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsInterceptDeploymentGroupsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptDeploymentGroupsGetRequest
  object.

  Fields:
    name: Required. The name of the deployment group to retrieve. Format: proj
      ects/{project}/locations/{location}/interceptDeploymentGroups/{intercept
      _deployment_group}
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsInterceptDeploymentGroupsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptDeploymentGroupsListRequest
  object.

  Fields:
    filter: Optional. Filter expression. See
      https://google.aip.dev/160#filtering for more details.
    orderBy: Optional. Sort expression. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
      See https://google.aip.dev/158 for more details.
    pageToken: Optional. A page token, received from a previous
      `ListInterceptDeploymentGroups` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListInterceptDeploymentGroups` must match the call that provided the
      page token. See https://google.aip.dev/158 for more details.
    parent: Required. The parent, which owns this collection of deployment
      groups. Example: `projects/123456789/locations/global`. See
      https://google.aip.dev/132 for more details.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsInterceptDeploymentGroupsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptDeploymentGroupsPatchRequest
  object.

  Fields:
    interceptDeploymentGroup: A InterceptDeploymentGroup resource to be passed
      as the request body.
    name: Immutable. Identifier. The resource name of this deployment group,
      for example:
      `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`.
      See https://google.aip.dev/122 for more details.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
    updateMask: Optional. The list of fields to update. Fields are specified
      relative to the deployment group (e.g. `description`; *not*
      `intercept_deployment_group.description`). See
      https://google.aip.dev/161 for more details.
  """

  interceptDeploymentGroup = _messages.MessageField('InterceptDeploymentGroup', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsInterceptDeploymentsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptDeploymentsCreateRequest
  object.

  Fields:
    interceptDeployment: A InterceptDeployment resource to be passed as the
      request body.
    interceptDeploymentId: Required. The ID to use for the new deployment,
      which will become the final component of the deployment's resource name.
    parent: Required. The parent resource where this deployment will be
      created. Format: projects/{project}/locations/{location}
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  interceptDeployment = _messages.MessageField('InterceptDeployment', 1)
  interceptDeploymentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsInterceptDeploymentsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptDeploymentsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsInterceptDeploymentsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptDeploymentsGetRequest object.

  Fields:
    name: Required. The name of the deployment to retrieve. Format: projects/{
      project}/locations/{location}/interceptDeployments/{intercept_deployment
      }
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsInterceptDeploymentsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptDeploymentsListRequest
  object.

  Fields:
    filter: Optional. Filter expression. See
      https://google.aip.dev/160#filtering for more details.
    orderBy: Optional. Sort expression. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
      See https://google.aip.dev/158 for more details.
    pageToken: Optional. A page token, received from a previous
      `ListInterceptDeployments` call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      `ListInterceptDeployments` must match the call that provided the page
      token. See https://google.aip.dev/158 for more details.
    parent: Required. The parent, which owns this collection of deployments.
      Example: `projects/123456789/locations/us-central1-a`. See
      https://google.aip.dev/132 for more details.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsInterceptDeploymentsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptDeploymentsPatchRequest
  object.

  Fields:
    interceptDeployment: A InterceptDeployment resource to be passed as the
      request body.
    name: Immutable. Identifier. The resource name of this deployment, for
      example: `projects/123456789/locations/us-
      central1-a/interceptDeployments/my-dep`. See https://google.aip.dev/122
      for more details.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
    updateMask: Optional. The list of fields to update. Fields are specified
      relative to the deployment (e.g. `description`; *not*
      `intercept_deployment.description`). See https://google.aip.dev/161 for
      more details.
  """

  interceptDeployment = _messages.MessageField('InterceptDeployment', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsCrea
  teRequest object.

  Fields:
    interceptEndpointGroupAssociation: A InterceptEndpointGroupAssociation
      resource to be passed as the request body.
    interceptEndpointGroupAssociationId: Optional. The ID to use for the new
      association, which will become the final component of the endpoint
      group's resource name. If not provided, the server will generate a
      unique ID.
    parent: Required. The parent resource where this association will be
      created. Format: projects/{project}/locations/{location}
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  interceptEndpointGroupAssociation = _messages.MessageField('InterceptEndpointGroupAssociation', 1)
  interceptEndpointGroupAssociationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsDele
  teRequest object.

  Fields:
    name: Required. The association to delete.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsGetRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsGetRequest
  object.

  Fields:
    name: Required. The name of the association to retrieve. Format: projects/
      {project}/locations/{location}/interceptEndpointGroupAssociations/{inter
      cept_endpoint_group_association}
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsList
  Request object.

  Fields:
    filter: Optional. Filter expression. See
      https://google.aip.dev/160#filtering for more details.
    orderBy: Optional. Sort expression. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
      See https://google.aip.dev/158 for more details.
    pageToken: Optional. A page token, received from a previous
      `ListInterceptEndpointGroups` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListInterceptEndpointGroups` must match the call that provided the page
      token. See https://google.aip.dev/158 for more details.
    parent: Required. The parent, which owns this collection of associations.
      Example: `projects/123456789/locations/global`. See
      https://google.aip.dev/132 for more details.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsPatc
  hRequest object.

  Fields:
    interceptEndpointGroupAssociation: A InterceptEndpointGroupAssociation
      resource to be passed as the request body.
    name: Immutable. Identifier. The resource name of this endpoint group
      association, for example: `projects/123456789/locations/global/intercept
      EndpointGroupAssociations/my-eg-association`. See
      https://google.aip.dev/122 for more details.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
    updateMask: Optional. The list of fields to update. Fields are specified
      relative to the association (e.g. `description`; *not*
      `intercept_endpoint_group_association.description`). See
      https://google.aip.dev/161 for more details.
  """

  interceptEndpointGroupAssociation = _messages.MessageField('InterceptEndpointGroupAssociation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsInterceptEndpointGroupsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptEndpointGroupsCreateRequest
  object.

  Fields:
    interceptEndpointGroup: A InterceptEndpointGroup resource to be passed as
      the request body.
    interceptEndpointGroupId: Required. The ID to use for the endpoint group,
      which will become the final component of the endpoint group's resource
      name.
    parent: Required. The parent resource where this endpoint group will be
      created. Format: projects/{project}/locations/{location}
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  interceptEndpointGroup = _messages.MessageField('InterceptEndpointGroup', 1)
  interceptEndpointGroupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsInterceptEndpointGroupsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptEndpointGroupsDeleteRequest
  object.

  Fields:
    name: Required. The endpoint group to delete.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsInterceptEndpointGroupsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptEndpointGroupsGetRequest
  object.

  Fields:
    name: Required. The name of the endpoint group to retrieve. Format: projec
      ts/{project}/locations/{location}/interceptEndpointGroups/{intercept_end
      point_group}
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsInterceptEndpointGroupsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptEndpointGroupsListRequest
  object.

  Fields:
    filter: Optional. Filter expression. See
      https://google.aip.dev/160#filtering for more details.
    orderBy: Optional. Sort expression. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
      See https://google.aip.dev/158 for more details.
    pageToken: Optional. A page token, received from a previous
      `ListInterceptEndpointGroups` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListInterceptEndpointGroups` must match the call that provided the page
      token. See https://google.aip.dev/158 for more details.
    parent: Required. The parent, which owns this collection of endpoint
      groups. Example: `projects/123456789/locations/global`. See
      https://google.aip.dev/132 for more details.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsInterceptEndpointGroupsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsInterceptEndpointGroupsPatchRequest
  object.

  Fields:
    interceptEndpointGroup: A InterceptEndpointGroup resource to be passed as
      the request body.
    name: Immutable. Identifier. The resource name of this endpoint group, for
      example:
      `projects/123456789/locations/global/interceptEndpointGroups/my-eg`. See
      https://google.aip.dev/122 for more details.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
    updateMask: Optional. The list of fields to update. Fields are specified
      relative to the endpoint group (e.g. `description`; *not*
      `intercept_endpoint_group.description`). See https://google.aip.dev/161
      for more details.
  """

  interceptEndpointGroup = _messages.MessageField('InterceptEndpointGroup', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class NetworksecurityProjectsLocationsMirroringDeploymentGroupsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringDeploymentGroupsCreateRequest
  object.

  Fields:
    mirroringDeploymentGroup: A MirroringDeploymentGroup resource to be passed
      as the request body.
    mirroringDeploymentGroupId: Required. The ID to use for the new deployment
      group, which will become the final component of the deployment group's
      resource name.
    parent: Required. The parent resource where this deployment group will be
      created. Format: projects/{project}/locations/{location}
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  mirroringDeploymentGroup = _messages.MessageField('MirroringDeploymentGroup', 1)
  mirroringDeploymentGroupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsMirroringDeploymentGroupsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringDeploymentGroupsDeleteRequest
  object.

  Fields:
    name: Required. The deployment group to delete.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsMirroringDeploymentGroupsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringDeploymentGroupsGetRequest
  object.

  Fields:
    name: Required. The name of the deployment group to retrieve. Format: proj
      ects/{project}/locations/{location}/mirroringDeploymentGroups/{mirroring
      _deployment_group}
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsMirroringDeploymentGroupsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringDeploymentGroupsListRequest
  object.

  Fields:
    filter: Optional. Filter expression. See
      https://google.aip.dev/160#filtering for more details.
    orderBy: Optional. Sort expression. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
      See https://google.aip.dev/158 for more details.
    pageToken: Optional. A page token, received from a previous
      `ListMirroringDeploymentGroups` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListMirroringDeploymentGroups` must match the call that provided the
      page token. See https://google.aip.dev/158 for more details.
    parent: Required. The parent, which owns this collection of deployment
      groups. Example: `projects/123456789/locations/global`. See
      https://google.aip.dev/132 for more details.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsMirroringDeploymentGroupsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringDeploymentGroupsPatchRequest
  object.

  Fields:
    mirroringDeploymentGroup: A MirroringDeploymentGroup resource to be passed
      as the request body.
    name: Immutable. Identifier. The resource name of this deployment group,
      for example:
      `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`.
      See https://google.aip.dev/122 for more details.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
    updateMask: Optional. The list of fields to update. Fields are specified
      relative to the deployment group (e.g. `description`; *not*
      `mirroring_deployment_group.description`). See
      https://google.aip.dev/161 for more details.
  """

  mirroringDeploymentGroup = _messages.MessageField('MirroringDeploymentGroup', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsMirroringDeploymentsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringDeploymentsCreateRequest
  object.

  Fields:
    mirroringDeployment: A MirroringDeployment resource to be passed as the
      request body.
    mirroringDeploymentId: Required. The ID to use for the new deployment,
      which will become the final component of the deployment's resource name.
    parent: Required. The parent resource where this deployment will be
      created. Format: projects/{project}/locations/{location}
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  mirroringDeployment = _messages.MessageField('MirroringDeployment', 1)
  mirroringDeploymentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsMirroringDeploymentsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringDeploymentsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsMirroringDeploymentsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringDeploymentsGetRequest object.

  Fields:
    name: Required. The name of the deployment to retrieve. Format: projects/{
      project}/locations/{location}/mirroringDeployments/{mirroring_deployment
      }
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsMirroringDeploymentsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringDeploymentsListRequest
  object.

  Fields:
    filter: Optional. Filter expression. See
      https://google.aip.dev/160#filtering for more details.
    orderBy: Optional. Sort expression. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
      See https://google.aip.dev/158 for more details.
    pageToken: Optional. A page token, received from a previous
      `ListMirroringDeployments` call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      `ListMirroringDeployments` must match the call that provided the page
      token. See https://google.aip.dev/158 for more details.
    parent: Required. The parent, which owns this collection of deployments.
      Example: `projects/123456789/locations/us-central1-a`. See
      https://google.aip.dev/132 for more details.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsMirroringDeploymentsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringDeploymentsPatchRequest
  object.

  Fields:
    mirroringDeployment: A MirroringDeployment resource to be passed as the
      request body.
    name: Immutable. Identifier. The resource name of this deployment, for
      example: `projects/123456789/locations/us-
      central1-a/mirroringDeployments/my-dep`. See https://google.aip.dev/122
      for more details.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
    updateMask: Optional. The list of fields to update. Fields are specified
      relative to the deployment (e.g. `description`; *not*
      `mirroring_deployment.description`). See https://google.aip.dev/161 for
      more details.
  """

  mirroringDeployment = _messages.MessageField('MirroringDeployment', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsCrea
  teRequest object.

  Fields:
    mirroringEndpointGroupAssociation: A MirroringEndpointGroupAssociation
      resource to be passed as the request body.
    mirroringEndpointGroupAssociationId: Optional. The ID to use for the new
      association, which will become the final component of the endpoint
      group's resource name. If not provided, the server will generate a
      unique ID.
    parent: Required. The parent resource where this association will be
      created. Format: projects/{project}/locations/{location}
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  mirroringEndpointGroupAssociation = _messages.MessageField('MirroringEndpointGroupAssociation', 1)
  mirroringEndpointGroupAssociationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsDele
  teRequest object.

  Fields:
    name: Required. The association to delete.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsGetRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsGetRequest
  object.

  Fields:
    name: Required. The name of the association to retrieve. Format: projects/
      {project}/locations/{location}/mirroringEndpointGroupAssociations/{mirro
      ring_endpoint_group_association}
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsList
  Request object.

  Fields:
    filter: Optional. Filter expression. See
      https://google.aip.dev/160#filtering for more details.
    orderBy: Optional. Sort expression. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
      See https://google.aip.dev/158 for more details.
    pageToken: Optional. A page token, received from a previous
      `ListMirroringEndpointGroups` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListMirroringEndpointGroups` must match the call that provided the page
      token. See https://google.aip.dev/158 for more details.
    parent: Required. The parent, which owns this collection of associations.
      Example: `projects/123456789/locations/global`. See
      https://google.aip.dev/132 for more details.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsPatc
  hRequest object.

  Fields:
    mirroringEndpointGroupAssociation: A MirroringEndpointGroupAssociation
      resource to be passed as the request body.
    name: Immutable. Identifier. The resource name of this endpoint group
      association, for example: `projects/123456789/locations/global/mirroring
      EndpointGroupAssociations/my-eg-association`. See
      https://google.aip.dev/122 for more details.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
    updateMask: Optional. The list of fields to update. Fields are specified
      relative to the association (e.g. `description`; *not*
      `mirroring_endpoint_group_association.description`). See
      https://google.aip.dev/161 for more details.
  """

  mirroringEndpointGroupAssociation = _messages.MessageField('MirroringEndpointGroupAssociation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsMirroringEndpointGroupsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringEndpointGroupsCreateRequest
  object.

  Fields:
    mirroringEndpointGroup: A MirroringEndpointGroup resource to be passed as
      the request body.
    mirroringEndpointGroupId: Required. The ID to use for the endpoint group,
      which will become the final component of the endpoint group's resource
      name.
    parent: Required. The parent resource where this endpoint group will be
      created. Format: projects/{project}/locations/{location}
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  mirroringEndpointGroup = _messages.MessageField('MirroringEndpointGroup', 1)
  mirroringEndpointGroupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsMirroringEndpointGroupsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringEndpointGroupsDeleteRequest
  object.

  Fields:
    name: Required. The endpoint group to delete.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsMirroringEndpointGroupsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringEndpointGroupsGetRequest
  object.

  Fields:
    name: Required. The name of the endpoint group to retrieve. Format: projec
      ts/{project}/locations/{location}/mirroringEndpointGroups/{mirroring_end
      point_group}
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsMirroringEndpointGroupsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringEndpointGroupsListRequest
  object.

  Fields:
    filter: Optional. Filter expression. See
      https://google.aip.dev/160#filtering for more details.
    orderBy: Optional. Sort expression. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
      See https://google.aip.dev/158 for more details.
    pageToken: Optional. A page token, received from a previous
      `ListMirroringEndpointGroups` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListMirroringEndpointGroups` must match the call that provided the page
      token. See https://google.aip.dev/158 for more details.
    parent: Required. The parent, which owns this collection of endpoint
      groups. Example: `projects/123456789/locations/global`. See
      https://google.aip.dev/132 for more details.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsMirroringEndpointGroupsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringEndpointGroupsPatchRequest
  object.

  Fields:
    mirroringEndpointGroup: A MirroringEndpointGroup resource to be passed as
      the request body.
    name: Immutable. Identifier. The resource name of this endpoint group, for
      example:
      `projects/123456789/locations/global/mirroringEndpointGroups/my-eg`. See
      https://google.aip.dev/122 for more details.
    requestId: Optional. A unique identifier for this request. Must be a
      UUID4. This request is only idempotent if a `request_id` is provided.
      See https://google.aip.dev/155 for more details.
    updateMask: Optional. The list of fields to update. Fields are specified
      relative to the endpoint group (e.g. `description`; *not*
      `mirroring_endpoint_group.description`). See https://google.aip.dev/161
      for more details.
  """

  mirroringEndpointGroup = _messages.MessageField('MirroringEndpointGroup', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsMirroringEndpointsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringEndpointsCreateRequest
  object.

  Fields:
    mirroringEndpoint: A MirroringEndpoint resource to be passed as the
      request body.
    mirroringEndpointId: Required. ID for the new endpoint.
    parent: Required. The parent resource name, in the format
      `/projects/{project}/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  mirroringEndpoint = _messages.MessageField('MirroringEndpoint', 1)
  mirroringEndpointId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsMirroringEndpointsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringEndpointsDeleteRequest
  object.

  Fields:
    name: Required. The name of the endpoint.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsMirroringEndpointsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringEndpointsGetRequest object.

  Fields:
    name: Required. The name of the endpoint.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsMirroringEndpointsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringEndpointsListRequest object.

  Fields:
    filter: Optional. A filter to apply to the results in the format defined
      in [AIP-160: Filtering](https://google.aip.dev/160).
    orderBy: Optional. A hint specifying how the results should be sorted. If
      not specified, the results will be sorted in the default order.
    pageSize: Optional. The maximum number of results to return. If not
      specified, a default number will be used. Note that a fewer results may
      be returned.
    pageToken: Optional. A pagination token returned from a previous request
      to list endpoints. Provide this token to retrieve the next page of
      results.
    parent: Required. The parent resource name, in the format
      `/projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsMirroringEndpointsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsMirroringEndpointsPatchRequest object.

  Fields:
    mirroringEndpoint: A MirroringEndpoint resource to be passed as the
      request body.
    name: Immutable. Identifier. The resource name of this endpoint, for
      example: `projects/123456789/locations/us-
      central1-a/mirroringEndpoints/my-endpoint`. See
      https://google.aip.dev/122 for more details.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the MirroringEndpoint resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  mirroringEndpoint = _messages.MessageField('MirroringEndpoint', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class NetworksecurityProjectsLocationsPartnerSSEEnvironmentsAddDNSPeeringZoneRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSEEnvironmentsAddDNSPeeringZon
  eRequest object.

  Fields:
    addDNSPeeringZoneRequest: A AddDNSPeeringZoneRequest resource to be passed
      as the request body.
    name: Required. Name of the PartnerSSEEnvironment to add the DNS peering
      zone to.
  """

  addDNSPeeringZoneRequest = _messages.MessageField('AddDNSPeeringZoneRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsPartnerSSEEnvironmentsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSEEnvironmentsCreateRequest
  object.

  Fields:
    parent: Required. Value for parent.
    partnerSSEEnvironment: A PartnerSSEEnvironment resource to be passed as
      the request body.
    partnerSseEnvironmentId: Required. Id of the requesting object If auto-
      generating Id server-side, remove this field and
      partner_sse_environment_id from the method_signature of Create RPC
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  parent = _messages.StringField(1, required=True)
  partnerSSEEnvironment = _messages.MessageField('PartnerSSEEnvironment', 2)
  partnerSseEnvironmentId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsPartnerSSEEnvironmentsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSEEnvironmentsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsPartnerSSEEnvironmentsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSEEnvironmentsGetRequest
  object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsPartnerSSEEnvironmentsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSEEnvironmentsListRequest
  object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListPartnerSSEEnvironmentsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsPartnerSSEEnvironmentsRemoveDNSPeeringZoneRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSEEnvironmentsRemoveDNSPeering
  ZoneRequest object.

  Fields:
    name: Required. Name of the PartnerSSEEnvironment to remove the DNS
      peering zone from.
    removeDNSPeeringZoneRequest: A RemoveDNSPeeringZoneRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  removeDNSPeeringZoneRequest = _messages.MessageField('RemoveDNSPeeringZoneRequest', 2)


class NetworksecurityProjectsLocationsPartnerSSEGatewaysCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSEGatewaysCreateRequest
  object.

  Fields:
    parent: Required. Value for parent.
    partnerSSEGateway: A PartnerSSEGateway resource to be passed as the
      request body.
    partnerSseGatewayId: Required. Id of the requesting object If auto-
      generating Id server-side, remove this field and partner_sse_gateway_id
      from the method_signature of Create RPC
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  parent = _messages.StringField(1, required=True)
  partnerSSEGateway = _messages.MessageField('PartnerSSEGateway', 2)
  partnerSseGatewayId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsPartnerSSEGatewaysDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSEGatewaysDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsPartnerSSEGatewaysGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSEGatewaysGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsPartnerSSEGatewaysListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSEGatewaysListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListPartnerSSEGatewaysRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsPartnerSSEGatewaysPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSEGatewaysPatchRequest object.

  Fields:
    name: Immutable. name of resource
    partnerSSEGateway: A PartnerSSEGateway resource to be passed as the
      request body.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: The list of fields to update
  """

  name = _messages.StringField(1, required=True)
  partnerSSEGateway = _messages.MessageField('PartnerSSEGateway', 2)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsPartnerSSERealmsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSERealmsCreateRequest object.

  Fields:
    parent: Required. Value for parent.
    partnerSSERealm: A PartnerSSERealm resource to be passed as the request
      body.
    partnerSseRealmId: Required. Id of the requesting object If auto-
      generating Id server-side, remove this field and partner_sse_realm_id
      from the method_signature of Create RPC
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  parent = _messages.StringField(1, required=True)
  partnerSSERealm = _messages.MessageField('PartnerSSERealm', 2)
  partnerSseRealmId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsPartnerSSERealmsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSERealmsDeleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsPartnerSSERealmsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSERealmsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsPartnerSSERealmsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsPartnerSSERealmsListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListPartnerSSERealmsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsSacAttachmentsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSacAttachmentsCreateRequest object.

  Fields:
    parent: Required. The parent, in the form
      `projects/{project}/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    sACAttachment: A SACAttachment resource to be passed as the request body.
    sacAttachmentId: Required. ID of the created attachment. The ID must be
      1-63 characters long, and comply with RFC1035. Specifically, it must be
      1-63 characters long and match the regular expression
      `[a-z]([-a-z0-9]*[a-z0-9])?` which means the first character must be a
      lowercase letter, and all following characters must be a dash, lowercase
      letter, or digit, except the last character, which cannot be a dash.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  sACAttachment = _messages.MessageField('SACAttachment', 3)
  sacAttachmentId = _messages.StringField(4)


class NetworksecurityProjectsLocationsSacAttachmentsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSacAttachmentsDeleteRequest object.

  Fields:
    name: Required. Name of the resource, in the form
      `projects/{project}/locations/{location}/sacAttachments/{sac_attachment}
      `.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsSacAttachmentsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSacAttachmentsGetRequest object.

  Fields:
    name: Required. Name of the resource, in the form
      `projects/{project}/locations/{location}/sacAttachments/{sac_attachment}
      `.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsSacAttachmentsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSacAttachmentsListRequest object.

  Fields:
    filter: Optional. An expression that filters the list of results.
    orderBy: Optional. Sort the results by a certain order.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent, in the form
      `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsSacRealmsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSacRealmsCreateRequest object.

  Fields:
    parent: Required. The parent, in the form
      `projects/{project}/locations/global`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    sACRealm: A SACRealm resource to be passed as the request body.
    sacRealmId: Required. ID of the created realm. The ID must be 1-63
      characters long, and comply with RFC1035. Specifically, it must be 1-63
      characters long and match the regular expression
      `[a-z]([-a-z0-9]*[a-z0-9])?` which means the first character must be a
      lowercase letter, and all following characters must be a dash, lowercase
      letter, or digit, except the last character, which cannot be a dash.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  sACRealm = _messages.MessageField('SACRealm', 3)
  sacRealmId = _messages.StringField(4)


class NetworksecurityProjectsLocationsSacRealmsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSacRealmsDeleteRequest object.

  Fields:
    name: Required. Name of the resource, in the form
      `projects/{project}/locations/global/sacRealms/{sacRealm}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsSacRealmsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSacRealmsGetRequest object.

  Fields:
    name: Required. Name of the resource, in the form
      `projects/{project}/locations/global/sacRealms/{sacRealm}`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsSacRealmsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSacRealmsListRequest object.

  Fields:
    filter: Optional. An expression that filters the list of results.
    orderBy: Optional. Sort the results by a certain order.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent, in the form
      `projects/{project}/locations/global`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsSecurityProfileGroupsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfileGroupsCreateRequest
  object.

  Fields:
    parent: Required. The parent resource of the SecurityProfileGroup. Must be
      in the format `projects|organizations/*/locations/{location}`.
    securityProfileGroup: A SecurityProfileGroup resource to be passed as the
      request body.
    securityProfileGroupId: Required. Short name of the SecurityProfileGroup
      resource to be created. This value should be 1-63 characters long,
      containing only letters, numbers, hyphens, and underscores, and should
      not start with a number. E.g. "security_profile_group1".
  """

  parent = _messages.StringField(1, required=True)
  securityProfileGroup = _messages.MessageField('SecurityProfileGroup', 2)
  securityProfileGroupId = _messages.StringField(3)


class NetworksecurityProjectsLocationsSecurityProfileGroupsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfileGroupsDeleteRequest
  object.

  Fields:
    etag: Optional. If client provided etag is out of date, delete will return
      FAILED_PRECONDITION error.
    name: Required. A name of the SecurityProfileGroup to delete. Must be in
      the format `projects|organizations/*/locations/{location}/securityProfil
      eGroups/{security_profile_group}`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsSecurityProfileGroupsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfileGroupsGetRequest
  object.

  Fields:
    name: Required. A name of the SecurityProfileGroup to get. Must be in the
      format `projects|organizations/*/locations/{location}/securityProfileGro
      ups/{security_profile_group}`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsSecurityProfileGroupsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfileGroupsListRequest
  object.

  Fields:
    pageSize: Maximum number of SecurityProfileGroups to return per call.
    pageToken: The value returned by the last
      `ListSecurityProfileGroupsResponse` Indicates that this is a
      continuation of a prior `ListSecurityProfileGroups` call, and that the
      system should return the next page of data.
    parent: Required. The project or organization and location from which the
      SecurityProfileGroups should be listed, specified in the format
      `projects|organizations/*/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsSecurityProfileGroupsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfileGroupsPatchRequest
  object.

  Fields:
    name: Immutable. Identifier. Name of the SecurityProfileGroup resource. It
      matches pattern `projects|organizations/*/locations/{location}/securityP
      rofileGroups/{security_profile_group}`.
    securityProfileGroup: A SecurityProfileGroup resource to be passed as the
      request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the SecurityProfileGroup resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask.
  """

  name = _messages.StringField(1, required=True)
  securityProfileGroup = _messages.MessageField('SecurityProfileGroup', 2)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsSecurityProfilesCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfilesCreateRequest object.

  Fields:
    parent: Required. The parent resource of the SecurityProfile. Must be in
      the format `projects|organizations/*/locations/{location}`.
    securityProfile: A SecurityProfile resource to be passed as the request
      body.
    securityProfileId: Required. Short name of the SecurityProfile resource to
      be created. This value should be 1-63 characters long, containing only
      letters, numbers, hyphens, and underscores, and should not start with a
      number. E.g. "security_profile1".
  """

  parent = _messages.StringField(1, required=True)
  securityProfile = _messages.MessageField('SecurityProfile', 2)
  securityProfileId = _messages.StringField(3)


class NetworksecurityProjectsLocationsSecurityProfilesDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfilesDeleteRequest object.

  Fields:
    etag: Optional. If client provided etag is out of date, delete will return
      FAILED_PRECONDITION error.
    name: Required. A name of the SecurityProfile to delete. Must be in the
      format `projects|organizations/*/locations/{location}/securityProfiles/{
      security_profile_id}`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsSecurityProfilesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfilesGetRequest object.

  Fields:
    name: Required. A name of the SecurityProfile to get. Must be in the
      format `projects|organizations/*/locations/{location}/securityProfiles/{
      security_profile_id}`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsSecurityProfilesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfilesListRequest object.

  Fields:
    pageSize: Maximum number of SecurityProfiles to return per call.
    pageToken: The value returned by the last `ListSecurityProfilesResponse`
      Indicates that this is a continuation of a prior `ListSecurityProfiles`
      call, and that the system should return the next page of data.
    parent: Required. The project or organization and location from which the
      SecurityProfiles should be listed, specified in the format
      `projects|organizations/*/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsSecurityProfilesPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfilesPatchRequest object.

  Fields:
    name: Immutable. Identifier. Name of the SecurityProfile resource. It
      matches pattern `projects|organizations/*/locations/{location}/securityP
      rofiles/{security_profile}`.
    securityProfile: A SecurityProfile resource to be passed as the request
      body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the SecurityProfile resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask.
  """

  name = _messages.StringField(1, required=True)
  securityProfile = _messages.MessageField('SecurityProfile', 2)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsServerTlsPoliciesCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsServerTlsPoliciesCreateRequest object.

  Fields:
    parent: Required. The parent resource of the ServerTlsPolicy. Must be in
      the format `projects/*/locations/{location}`.
    serverTlsPolicy: A ServerTlsPolicy resource to be passed as the request
      body.
    serverTlsPolicyId: Required. Short name of the ServerTlsPolicy resource to
      be created. This value should be 1-63 characters long, containing only
      letters, numbers, hyphens, and underscores, and should not start with a
      number. E.g. "server_mtls_policy".
  """

  parent = _messages.StringField(1, required=True)
  serverTlsPolicy = _messages.MessageField('ServerTlsPolicy', 2)
  serverTlsPolicyId = _messages.StringField(3)


class NetworksecurityProjectsLocationsServerTlsPoliciesDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsServerTlsPoliciesDeleteRequest object.

  Fields:
    name: Required. A name of the ServerTlsPolicy to delete. Must be in the
      format `projects/*/locations/{location}/serverTlsPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsServerTlsPoliciesGetIamPolicyRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsServerTlsPoliciesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsServerTlsPoliciesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsServerTlsPoliciesGetRequest object.

  Fields:
    name: Required. A name of the ServerTlsPolicy to get. Must be in the
      format `projects/*/locations/{location}/serverTlsPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsServerTlsPoliciesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsServerTlsPoliciesListRequest object.

  Fields:
    pageSize: Maximum number of ServerTlsPolicies to return per call.
    pageToken: The value returned by the last `ListServerTlsPoliciesResponse`
      Indicates that this is a continuation of a prior `ListServerTlsPolicies`
      call, and that the system should return the next page of data.
    parent: Required. The project and location from which the
      ServerTlsPolicies should be listed, specified in the format
      `projects/*/locations/{location}`.
    returnPartialSuccess: Optional. Setting this field to `true` will opt the
      request into returning the resources that are reachable, and into
      including the names of those that were unreachable in the
      [ListServerTlsPoliciesResponse.unreachable] field. This can only be
      `true` when reading across collections e.g. when `parent` is set to
      `"projects/example/locations/-"`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  returnPartialSuccess = _messages.BooleanField(4)


class NetworksecurityProjectsLocationsServerTlsPoliciesPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsServerTlsPoliciesPatchRequest object.

  Fields:
    name: Required. Name of the ServerTlsPolicy resource. It matches the
      pattern
      `projects/*/locations/{location}/serverTlsPolicies/{server_tls_policy}`
    serverTlsPolicy: A ServerTlsPolicy resource to be passed as the request
      body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the ServerTlsPolicy resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  serverTlsPolicy = _messages.MessageField('ServerTlsPolicy', 2)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsServerTlsPoliciesSetIamPolicyRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsServerTlsPoliciesSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsServerTlsPoliciesTestIamPermissionsRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsServerTlsPoliciesTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsSseGatewayReferencesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSseGatewayReferencesGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsSseGatewayReferencesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSseGatewayReferencesListRequest
  object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListSSEGatewayReferencesRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsTlsInspectionPoliciesCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsTlsInspectionPoliciesCreateRequest
  object.

  Fields:
    parent: Required. The parent resource of the TlsInspectionPolicy. Must be
      in the format `projects/{project}/locations/{location}`.
    tlsInspectionPolicy: A TlsInspectionPolicy resource to be passed as the
      request body.
    tlsInspectionPolicyId: Required. Short name of the TlsInspectionPolicy
      resource to be created. This value should be 1-63 characters long,
      containing only letters, numbers, hyphens, and underscores, and should
      not start with a number. E.g. "tls_inspection_policy1".
  """

  parent = _messages.StringField(1, required=True)
  tlsInspectionPolicy = _messages.MessageField('TlsInspectionPolicy', 2)
  tlsInspectionPolicyId = _messages.StringField(3)


class NetworksecurityProjectsLocationsTlsInspectionPoliciesDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsTlsInspectionPoliciesDeleteRequest
  object.

  Fields:
    force: If set to true, any rules for this TlsInspectionPolicy will also be
      deleted. (Otherwise, the request will only work if the
      TlsInspectionPolicy has no rules.)
    name: Required. A name of the TlsInspectionPolicy to delete. Must be in
      the format `projects/{project}/locations/{location}/tlsInspectionPolicie
      s/{tls_inspection_policy}`.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsTlsInspectionPoliciesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsTlsInspectionPoliciesGetRequest
  object.

  Fields:
    name: Required. A name of the TlsInspectionPolicy to get. Must be in the
      format `projects/{project}/locations/{location}/tlsInspectionPolicies/{t
      ls_inspection_policy}`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsTlsInspectionPoliciesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsTlsInspectionPoliciesListRequest
  object.

  Fields:
    pageSize: Maximum number of TlsInspectionPolicies to return per call.
    pageToken: The value returned by the last
      'ListTlsInspectionPoliciesResponse' Indicates that this is a
      continuation of a prior 'ListTlsInspectionPolicies' call, and that the
      system should return the next page of data.
    parent: Required. The project and location from which the
      TlsInspectionPolicies should be listed, specified in the format
      `projects/{project}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsTlsInspectionPoliciesPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsTlsInspectionPoliciesPatchRequest
  object.

  Fields:
    name: Required. Name of the resource. Name is of the form projects/{projec
      t}/locations/{location}/tlsInspectionPolicies/{tls_inspection_policy}
      tls_inspection_policy should match the
      pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    tlsInspectionPolicy: A TlsInspectionPolicy resource to be passed as the
      request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the TlsInspectionPolicy resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  tlsInspectionPolicy = _messages.MessageField('TlsInspectionPolicy', 2)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsUllMirroringCollectorsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUllMirroringCollectorsCreateRequest
  object.

  Fields:
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    ullMirroringCollector: A UllMirroringCollector resource to be passed as
      the request body.
    ullMirroringCollectorId: Required. Id of the requesting object If auto-
      generating Id server-side, remove this field and
      ull_mirroring_collector_id from the method_signature of Create RPC
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  ullMirroringCollector = _messages.MessageField('UllMirroringCollector', 3)
  ullMirroringCollectorId = _messages.StringField(4)


class NetworksecurityProjectsLocationsUllMirroringCollectorsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUllMirroringCollectorsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsUllMirroringCollectorsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUllMirroringCollectorsGetRequest
  object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsUllMirroringCollectorsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUllMirroringCollectorsListRequest
  object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListUllMirroringCollectorsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsUllMirroringCollectorsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUllMirroringCollectorsPatchRequest
  object.

  Fields:
    name: Immutable. Identifier. The name of the UllMirroringCollector.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    ullMirroringCollector: A UllMirroringCollector resource to be passed as
      the request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the UllMirroringCollector resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  ullMirroringCollector = _messages.MessageField('UllMirroringCollector', 3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsUllMirroringEnginesCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUllMirroringEnginesCreateRequest
  object.

  Fields:
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    ullMirroringEngine: A UllMirroringEngine resource to be passed as the
      request body.
    ullMirroringEngineId: Required. Id of the requesting object If auto-
      generating Id server-side, remove this field and ull_mirroring_engine_id
      from the method_signature of Create RPC
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  ullMirroringEngine = _messages.MessageField('UllMirroringEngine', 3)
  ullMirroringEngineId = _messages.StringField(4)


class NetworksecurityProjectsLocationsUllMirroringEnginesDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUllMirroringEnginesDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsUllMirroringEnginesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUllMirroringEnginesGetRequest object.

  Fields:
    name: Required. The resource name of the UllMirroringEngine.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsUllMirroringEnginesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUllMirroringEnginesListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListUllMirroringEnginesRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsUllMirroringEnginesPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUllMirroringEnginesPatchRequest
  object.

  Fields:
    name: Identifier. The name of the resource.
    ullMirroringEngine: A UllMirroringEngine resource to be passed as the
      request body.
    updateMask: Optional. The list of fields to update.
  """

  name = _messages.StringField(1, required=True)
  ullMirroringEngine = _messages.MessageField('UllMirroringEngine', 2)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksC
  reateRequest object.

  Fields:
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    ullMirroredNetwork: A UllMirroredNetwork resource to be passed as the
      request body.
    ullMirroredNetworkId: Required. Id of the requesting object If auto-
      generating Id server-side, remove this field and ull_mirrored_network_id
      from the method_signature of Create RPC
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  ullMirroredNetwork = _messages.MessageField('UllMirroredNetwork', 3)
  ullMirroredNetworkId = _messages.StringField(4)


class NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksD
  eleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksG
  etRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksL
  istRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListUllMirroredNetworksRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsUrlListsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUrlListsCreateRequest object.

  Fields:
    parent: Required. The parent resource of the UrlList. Must be in the
      format `projects/*/locations/{location}`.
    urlList: A UrlList resource to be passed as the request body.
    urlListId: Required. Short name of the UrlList resource to be created.
      This value should be 1-63 characters long, containing only letters,
      numbers, hyphens, and underscores, and should not start with a number.
      E.g. "url_list".
  """

  parent = _messages.StringField(1, required=True)
  urlList = _messages.MessageField('UrlList', 2)
  urlListId = _messages.StringField(3)


class NetworksecurityProjectsLocationsUrlListsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUrlListsDeleteRequest object.

  Fields:
    name: Required. A name of the UrlList to delete. Must be in the format
      `projects/*/locations/{location}/urlLists/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsUrlListsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUrlListsGetRequest object.

  Fields:
    name: Required. A name of the UrlList to get. Must be in the format
      `projects/*/locations/{location}/urlLists/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsUrlListsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUrlListsListRequest object.

  Fields:
    pageSize: Maximum number of UrlLists to return per call.
    pageToken: The value returned by the last `ListUrlListsResponse` Indicates
      that this is a continuation of a prior `ListUrlLists` call, and that the
      system should return the next page of data.
    parent: Required. The project and location from which the UrlLists should
      be listed, specified in the format
      `projects/{project}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsUrlListsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUrlListsPatchRequest object.

  Fields:
    name: Required. Name of the resource provided by the user. Name is of the
      form projects/{project}/locations/{location}/urlLists/{url_list}
      url_list should match the pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the UrlList resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    urlList: A UrlList resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  urlList = _messages.MessageField('UrlList', 3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class PartnerSSEEnvironment(_messages.Message):
  r"""Message describing PartnerSSEEnvironment object.

  Enums:
    SecurityServiceValueValuesEnum: Immutable. Only SYMANTEC_CLOUD_SWG uses
      PartnerSSEEnvironment today.

  Messages:
    LabelsValue: Optional. Labels as key value pair

  Fields:
    createTime: Output only. Create time stamp
    deleteTime: Output only. Delete time stamp
    dnsPeeringZones: Optional. Configured DNS peering zones.
    labels: Optional. Labels as key value pair
    name: Identifier. Name of the Partner SSE Environment in the form of
      "projects/{project}/locations/global/partnerSSEEnvironments/{id}".
    partnerNetwork: Required. Partner-owned network in the partner project
      created for this environment. Supports all user traffic and peers to
      sse_network.
    securityService: Immutable. Only SYMANTEC_CLOUD_SWG uses
      PartnerSSEEnvironment today.
    sseNetwork: Output only. Google-owned VPC in the SSE project created for
      this environment. Supports all user traffic and peers to partner_vpc.
    sseNetworkingRanges: Required. CIDR ranges reserved for Google's use.
      Should be at least a /20 in test environments and at least a /14 in
      environments with customers, but that is not enforced.
    sseProject: Output only. Google-owned project created for this
      environment.
    sseProjectNumber: Output only. CDEN owned project owning sse_network.
    symantecOptions: Optional. Required iff sse_service is SYMANTEC_CLOUD_SWG.
    updateTime: Output only. Update time stamp
  """

  class SecurityServiceValueValuesEnum(_messages.Enum):
    r"""Immutable. Only SYMANTEC_CLOUD_SWG uses PartnerSSEEnvironment today.

    Values:
      SECURITY_SERVICE_UNSPECIFIED: The default value. This value is used if
        the state is omitted.
      PALO_ALTO_PRISMA_ACCESS: [Palo Alto Networks Prisma
        Access](https://www.paloaltonetworks.com/sase/access).
      SYMANTEC_CLOUD_SWG: Symantec Cloud SWG.
    """
    SECURITY_SERVICE_UNSPECIFIED = 0
    PALO_ALTO_PRISMA_ACCESS = 1
    SYMANTEC_CLOUD_SWG = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pair

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  deleteTime = _messages.StringField(2)
  dnsPeeringZones = _messages.MessageField('PartnerSSEEnvironmentDNSPeeringZone', 3, repeated=True)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  partnerNetwork = _messages.StringField(6)
  securityService = _messages.EnumField('SecurityServiceValueValuesEnum', 7)
  sseNetwork = _messages.StringField(8)
  sseNetworkingRanges = _messages.StringField(9, repeated=True)
  sseProject = _messages.StringField(10)
  sseProjectNumber = _messages.IntegerField(11)
  symantecOptions = _messages.MessageField('PartnerSSEEnvironmentSymantecEnvironmentOptions', 12)
  updateTime = _messages.StringField(13)


class PartnerSSEEnvironmentDNSPeeringZone(_messages.Message):
  r"""Message describing a single DNS peering zone from the partner-facing
  network to a target_network.

  Fields:
    dnsSuffix: Required. DNS dns_suffix to add to DNS peering zone.
    targetNetwork: Optional. Full URI of the target VPC network for the DNS
      peering zone.
  """

  dnsSuffix = _messages.StringField(1)
  targetNetwork = _messages.StringField(2)


class PartnerSSEEnvironmentSymantecEnvironmentOptions(_messages.Message):
  r"""Fields that are applicable iff sse_service is SYMANTEC_CLOUD_SWG.

  Fields:
    apiEndpoint: Optional. URL to use for calling the Symantec Locations API.
    testOnlyStandaloneMode: Optional. If true, calls to api_endpoint are
      skipped or mocked. Partner Gateways in this environment will need to be
      manually connected to the Symantec backend. If false (the default), the
      Google backend will call api_endpoint to provision resources for Partner
      Gateways created in this environment.
  """

  apiEndpoint = _messages.StringField(1)
  testOnlyStandaloneMode = _messages.BooleanField(2)


class PartnerSSEGateway(_messages.Message):
  r"""Message describing PartnerSSEGateway object

  Enums:
    StateValueValuesEnum: Output only. State of the gateway.

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    capacityBps: Output only. Copied from the associated NCC resource in
      Symantec NCCGW flows. Used by Symantec API.
    country: Output only. ISO-3166 alpha 2 country code used for localization.
      Filled from the customer SSEGateway, and only for PartnerSSEGateways
      associated with Symantec today.
    createTime: Output only. Create time stamp
    labels: Optional. Labels as key value pairs
    name: Immutable. name of resource
    partnerSseEnvironment: Output only. Full URI of the partner environment
      this PartnerSSEGateway is connected to. Filled from the customer
      SSEGateway, and only for PartnerSSEGateways associated with Symantec
      today.
    partnerSseRealm: Output only. name of PartnerSSERealm owning the
      PartnerSSEGateway
    partnerSubnetRange: Optional. Subnet range of the partner-owned subnet.
    partnerVpcSubnetRange: Optional. Subnet range of the partner_vpc This
      field is deprecated. Use partner_subnet_range instead.
    proberSubnetRanges: Output only. Subnet ranges for Google-issued probe
      packets. It's populated only for Prisma Access partners.
    sseBgpAsn: Output only. ASN of SSE BGP
    sseBgpIps: Output only. IP of SSE BGP
    sseGatewayReferenceId: Required. ID of the SSEGatewayReference that pairs
      with this PartnerSSEGateway
    sseNetwork: Output only. The ID of the network in sse_project containing
      sse_subnet_range. This is also known as the partnerFacingNetwork. Only
      filled for PartnerSSEGateways associated with Symantec today.
    sseProject: Output only. The project owning partner_facing_network. Only
      filled for PartnerSSEGateways associated with Symantec today.
    sseSubnetRange: Optional. Subnet range where SSE GW instances are
      deployed. Default value is set to "************/24". The CIDR suffix
      should be less than or equal to 25.
    sseTargetIp: Output only. Target IP that belongs to sse_subnet_range where
      partner should send the traffic to reach the customer networks.
    sseVpcSubnetRange: Output only. Subnet range of the subnet where partner
      traffic is routed. This field is deprecated. Use sse_subnet_range
      instead.
    sseVpcTargetIp: Output only. This is the IP where the partner traffic
      should be routed to. This field is deprecated. Use sse_target_ip
      instead.
    state: Output only. State of the gateway.
    symantecOptions: Optional. Required iff Partner is Symantec.
    timezone: Output only. tzinfo identifier used for localization. Filled
      from the customer SSEGateway, and only for PartnerSSEGateways associated
      with Symantec today.
    updateTime: Output only. Update time stamp
    vni: Optional. Virtual Network Identifier to use in NCG. Today the only
      partner that depends on it is Symantec.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the gateway.

    Values:
      STATE_UNSPECIFIED: No state specified. This should not be used.
      CUSTOMER_ATTACHED: Attached to a customer. This is the default state
        when a gateway is successfully created.
      CUSTOMER_DETACHED: No longer attached to a customer. This state arises
        when the customer attachment is deleted.
    """
    STATE_UNSPECIFIED = 0
    CUSTOMER_ATTACHED = 1
    CUSTOMER_DETACHED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  capacityBps = _messages.IntegerField(1)
  country = _messages.StringField(2)
  createTime = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  partnerSseEnvironment = _messages.StringField(6)
  partnerSseRealm = _messages.StringField(7)
  partnerSubnetRange = _messages.StringField(8)
  partnerVpcSubnetRange = _messages.StringField(9)
  proberSubnetRanges = _messages.StringField(10, repeated=True)
  sseBgpAsn = _messages.IntegerField(11, variant=_messages.Variant.INT32)
  sseBgpIps = _messages.StringField(12, repeated=True)
  sseGatewayReferenceId = _messages.StringField(13)
  sseNetwork = _messages.StringField(14)
  sseProject = _messages.StringField(15)
  sseSubnetRange = _messages.StringField(16)
  sseTargetIp = _messages.StringField(17)
  sseVpcSubnetRange = _messages.StringField(18)
  sseVpcTargetIp = _messages.StringField(19)
  state = _messages.EnumField('StateValueValuesEnum', 20)
  symantecOptions = _messages.MessageField('PartnerSSEGatewayPartnerSSEGatewaySymantecOptions', 21)
  timezone = _messages.StringField(22)
  updateTime = _messages.StringField(23)
  vni = _messages.IntegerField(24, variant=_messages.Variant.INT32)


class PartnerSSEGatewayPartnerSSEGatewaySymantecOptions(_messages.Message):
  r"""Options specific to gateways connected to Symantec.

  Fields:
    symantecLocationUuid: Output only. UUID of the Symantec Location created
      on the customer's behalf.
    symantecSite: Output only. Symantec data center identifier that this SSEGW
      will connect to. Filled from the customer SSEGateway, and only for
      PartnerSSEGateways associated with Symantec today.
    symantecSiteTargetHost: Optional. Target for the NCGs to send traffic to
      on the Symantec side. Only supports IP address today.
  """

  symantecLocationUuid = _messages.StringField(1)
  symantecSite = _messages.StringField(2)
  symantecSiteTargetHost = _messages.StringField(3)


class PartnerSSERealm(_messages.Message):
  r"""Message describing PartnerSSERealm object

  Enums:
    StateValueValuesEnum: Output only. State of the realm. It can be either
      CUSTOMER_ATTACHED or CUSTOMER_DETACHED.

  Messages:
    LabelsValue: Labels as key value pairs

  Fields:
    createTime: Output only. Create time stamp
    labels: Labels as key value pairs
    name: name of resource
    pairingKey: Required. value of the key to establish global handshake from
      SSERealm
    panOptions: Optional. Required only for PAN.
    partnerNetwork: Optional. Partner-owned network to be peered with CDEN's
      sse_network in sse_project
    partnerVpc: Optional. VPC owned by the partner to be peered with CDEN
      sse_vpc in sse_project This field is deprecated. Use partner_network
      instead.
    sseNetwork: Output only. CDEN-owned network to be peered with
      partner_network
    sseProject: Output only. CDEN owned project owning sse_vpc. It stores
      project id in the TTM flow, but project number in the NCCGW flow. This
      field will be deprecated after the partner migrates from using
      sse_project to using sse_project_number.
    sseProjectNumber: Output only. CDEN owned project owning sse_vpc
    sseVpc: Output only. CDEN owned VPC to be peered with partner_vpc This
      field is deprecated. Use sse_network instead.
    state: Output only. State of the realm. It can be either CUSTOMER_ATTACHED
      or CUSTOMER_DETACHED.
    updateTime: Output only. Update time stamp
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the realm. It can be either CUSTOMER_ATTACHED or
    CUSTOMER_DETACHED.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      CUSTOMER_ATTACHED: This PartnerSSERealm is attached to a customer realm.
        This is the default state when a PartnerSSERealm is successfully
        created.
      CUSTOMER_DETACHED: This PartnerSSERealm is no longer attached to a
        customer realm. This is the state when the customer realm is deleted.
    """
    STATE_UNSPECIFIED = 0
    CUSTOMER_ATTACHED = 1
    CUSTOMER_DETACHED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  pairingKey = _messages.StringField(4)
  panOptions = _messages.MessageField('PartnerSSERealmPartnerSSERealmPanOptions', 5)
  partnerNetwork = _messages.StringField(6)
  partnerVpc = _messages.StringField(7)
  sseNetwork = _messages.StringField(8)
  sseProject = _messages.StringField(9)
  sseProjectNumber = _messages.IntegerField(10)
  sseVpc = _messages.StringField(11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  updateTime = _messages.StringField(13)


class PartnerSSERealmPartnerSSERealmPanOptions(_messages.Message):
  r"""Fields specific to PAN realms.

  Fields:
    serialNumber: Optional. serial_number is provided by PAN to identify GCP
      customer on PAN side.
    tenantId: Optional. tenant_id is provided by PAN to identify GCP customer
      on PAN side.
  """

  serialNumber = _messages.StringField(1)
  tenantId = _messages.StringField(2)


class RemoveAddressGroupItemsRequest(_messages.Message):
  r"""Request used by the RemoveAddressGroupItems method.

  Fields:
    items: Required. List of items to remove.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  items = _messages.StringField(1, repeated=True)
  requestId = _messages.StringField(2)


class RemoveDNSPeeringZoneRequest(_messages.Message):
  r"""Request used by the RemoveDnsPeeringZone method.

  Fields:
    dnsSuffix: Required. DNS suffix to remove from DNS peering zones.
    requestId: Optional. An optional request ID to identify requests.
  """

  dnsSuffix = _messages.StringField(1)
  requestId = _messages.StringField(2)


class Rule(_messages.Message):
  r"""Specification of rules.

  Fields:
    destinations: Optional. List of attributes for the traffic destination.
      All of the destinations must match. A destination is a match if a
      request matches all the specified hosts, ports, methods and headers. If
      not set, the action specified in the 'action' field will be applied
      without any rule checks for the destination.
    sources: Optional. List of attributes for the traffic source. All of the
      sources must match. A source is a match if both principals and ip_blocks
      match. If not set, the action specified in the 'action' field will be
      applied without any rule checks for the source.
  """

  destinations = _messages.MessageField('Destination', 1, repeated=True)
  sources = _messages.MessageField('Source', 2, repeated=True)


class SACAttachment(_messages.Message):
  r"""Represents a Secure Access Connect (SAC) attachment resource. A Secure
  Access Connect attachment enables NCC Gateway to process traffic with an SSE
  product.

  Enums:
    StateValueValuesEnum: Output only. State of the attachment.

  Messages:
    LabelsValue: Optional. Optional list of labels applied to the resource.

  Fields:
    country: Optional. Case-insensitive ISO-3166 alpha-2 country code used for
      localization. Only valid for Symantec attachments.
    createTime: Output only. Timestamp when the attachment was created.
    labels: Optional. Optional list of labels applied to the resource.
    name: Identifier. Resource name, in the form
      `projects/{project}/locations/{location}/sacAttachments/{sac_attachment}
      `.
    nccGateway: Required. NCC Gateway associated with the attachment. This can
      be input as an ID or a full resource name. The output always has the
      form
      `projects/{project_number}/locations/{location}/spokes/{ncc_gateway}`.
    sacRealm: Required. SAC Realm which owns the attachment. This can be input
      as an ID or a full resource name. The output always has the form
      `projects/{project_number}/locations/{location}/sacRealms/{sac_realm}`.
    state: Output only. State of the attachment.
    symantecOptions: Optional. Configuration required for Symantec
      attachments.
    timeZone: Optional. Case-sensitive tzinfo identifier used for
      localization. Only valid for Symantec attachments.
    updateTime: Output only. Timestamp when the attachment was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the attachment.

    Values:
      STATE_UNSPECIFIED: No state specified. This should not be used.
      PENDING_PARTNER_ATTACHMENT: Has never been attached to a partner.
      PARTNER_ATTACHED: Currently attached to a partner.
      PARTNER_DETACHED: Was once attached to a partner but has been detached.
    """
    STATE_UNSPECIFIED = 0
    PENDING_PARTNER_ATTACHMENT = 1
    PARTNER_ATTACHED = 2
    PARTNER_DETACHED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Optional list of labels applied to the resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  country = _messages.StringField(1)
  createTime = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  nccGateway = _messages.StringField(5)
  sacRealm = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  symantecOptions = _messages.MessageField('SACAttachmentSACAttachmentSymantecOptions', 8)
  timeZone = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class SACAttachmentSACAttachmentSymantecOptions(_messages.Message):
  r"""Fields specific to attachments associated with Symantec Cloud SWG.

  Fields:
    symantecLocationName: Immutable. Name to be used when creating a location
      on the customer's behalf in Symantec's Location API. Not to be confused
      with Google Cloud locations.
    symantecSite: Immutable. Symantec data center identifier that this
      attachment will connect to.
  """

  symantecLocationName = _messages.StringField(1)
  symantecSite = _messages.StringField(2)


class SACRealm(_messages.Message):
  r"""Represents a Secure Access Connect (SAC) realm resource. A Secure Access
  Connect realm establishes a connection between your Google Cloud project and
  an SSE service.

  Enums:
    SecurityServiceValueValuesEnum: Immutable. SSE service provider associated
      with the realm.
    StateValueValuesEnum: Output only. State of the realm.

  Messages:
    LabelsValue: Optional. Optional list of labels applied to the resource.

  Fields:
    createTime: Output only. Timestamp when the realm was created.
    labels: Optional. Optional list of labels applied to the resource.
    name: Identifier. Resource name, in the form
      `projects/{project}/locations/global/sacRealms/{sacRealm}`.
    pairingKey: Output only. Key to be shared with SSE service provider during
      pairing.
    partnerEnvironment: Optional. Full URI of environment that this Realm is
      using. Only used in Symantec Realms today.
    securityService: Immutable. SSE service provider associated with the
      realm.
    state: Output only. State of the realm.
    symantecOptions: Optional. Configuration required for Symantec realms.
    updateTime: Output only. Timestamp when the realm was last updated.
  """

  class SecurityServiceValueValuesEnum(_messages.Enum):
    r"""Immutable. SSE service provider associated with the realm.

    Values:
      SECURITY_SERVICE_UNSPECIFIED: The default value. This value is used if
        the state is omitted.
      PALO_ALTO_PRISMA_ACCESS: [Palo Alto Networks Prisma
        Access](https://www.paloaltonetworks.com/sase/access).
      SYMANTEC_CLOUD_SWG: Symantec Cloud SWG.
    """
    SECURITY_SERVICE_UNSPECIFIED = 0
    PALO_ALTO_PRISMA_ACCESS = 1
    SYMANTEC_CLOUD_SWG = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the realm.

    Values:
      STATE_UNSPECIFIED: No state specified. This should not be used.
      PENDING_PARTNER_ATTACHMENT: Has never been attached to a partner. Used
        only for Prisma Access.
      PARTNER_ATTACHED: Currently attached to a partner.
      PARTNER_DETACHED: Was once attached to a partner but has been detached.
      KEY_EXPIRED: Is not attached to a partner and has an expired pairing
        key. Used only for Prisma Access.
    """
    STATE_UNSPECIFIED = 0
    PENDING_PARTNER_ATTACHMENT = 1
    PARTNER_ATTACHED = 2
    PARTNER_DETACHED = 3
    KEY_EXPIRED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Optional list of labels applied to the resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  pairingKey = _messages.MessageField('SACRealmPairingKey', 4)
  partnerEnvironment = _messages.StringField(5)
  securityService = _messages.EnumField('SecurityServiceValueValuesEnum', 6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  symantecOptions = _messages.MessageField('SACRealmSACRealmSymantecOptions', 8)
  updateTime = _messages.StringField(9)


class SACRealmPairingKey(_messages.Message):
  r"""Key to be shared with SSE service provider to establish global
  handshake.

  Fields:
    expireTime: Output only. Timestamp in UTC of when this resource is
      considered expired. It expires 7 days after creation.
    key: Output only. Key value.
  """

  expireTime = _messages.StringField(1)
  key = _messages.StringField(2)


class SACRealmSACRealmSymantecOptions(_messages.Message):
  r"""Fields specific to realms using Symantec Cloud SWG.

  Enums:
    SymantecConnectionStateValueValuesEnum: Output only. Connection status to
      Symantec API.

  Fields:
    availableSymantecSites: Output only. Symantec site IDs which the user can
      choose to connect to.
    secretPath: Optional. API Key used to call Symantec APIs on the user's
      behalf. Required if using Symantec Cloud SWG. P4SA account needs
      permissions granted to read this secret. A secret ID, secret name, or
      secret URI can be specified, but it will be parsed and stored as a
      secret URI in the form `projects/{project_number}/secrets/my-secret`.
    symantecConnectionState: Output only. Connection status to Symantec API.
  """

  class SymantecConnectionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Connection status to Symantec API.

    Values:
      SYMANTEC_CONNECTION_STATE_UNSPECIFIED: No state specified. This should
        not be used.
      SUCCEEDED: Successfully made a request to Symantec API.
      READ_SECRET_FAILED: Cannot access the API key in the provided
        `secret_path`.
      REQUEST_TO_SYMANTEC_FAILED: Failed to get a successful response from
        Symantec API due to an invalid API key or Symantec API unavailability.
    """
    SYMANTEC_CONNECTION_STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    READ_SECRET_FAILED = 2
    REQUEST_TO_SYMANTEC_FAILED = 3

  availableSymantecSites = _messages.StringField(1, repeated=True)
  secretPath = _messages.StringField(2)
  symantecConnectionState = _messages.EnumField('SymantecConnectionStateValueValuesEnum', 3)


class SSEGatewayReference(_messages.Message):
  r"""Message describing SSEGatewayReference object

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    createTime: Output only. Create time stamp
    labels: Optional. Labels as key value pairs
    name: Immutable. name of resource
    partnerSseRealm: Output only. PartnerSSERealm owning the PartnerSSEGateway
      that this SSEGateway intends to connect with
    proberSubnetRanges: Output only. Subnet ranges for Google probe packets.
    updateTime: Output only. Update time stamp
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  partnerSseRealm = _messages.StringField(4)
  proberSubnetRanges = _messages.StringField(5, repeated=True)
  updateTime = _messages.StringField(6)


class SecurityProfile(_messages.Message):
  r"""SecurityProfile is a resource that defines the behavior for one of many
  ProfileTypes.

  Enums:
    TypeValueValuesEnum: Immutable. The single ProfileType that the
      SecurityProfile resource configures.

  Messages:
    LabelsValue: Optional. Labels as key value pairs.

  Fields:
    createTime: Output only. Resource creation timestamp.
    customInterceptProfile: The custom TPPI configuration for the
      SecurityProfile.
    customMirroringProfile: The custom Packet Mirroring v2 configuration for
      the SecurityProfile.
    description: Optional. An optional description of the profile. Max length
      512 characters.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and may be sent on update and delete requests to
      ensure the client has an up-to-date value before proceeding.
    labels: Optional. Labels as key value pairs.
    name: Immutable. Identifier. Name of the SecurityProfile resource. It
      matches pattern `projects|organizations/*/locations/{location}/securityP
      rofiles/{security_profile}`.
    threatPreventionProfile: The threat prevention configuration for the
      SecurityProfile.
    type: Immutable. The single ProfileType that the SecurityProfile resource
      configures.
    updateTime: Output only. Last resource update timestamp.
    urlFilteringProfile: The URL filtering configuration for the
      SecurityProfile.
    wildfireAnalysisProfile: The WildFire Analysis configurations for
      SecurityProfile.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Immutable. The single ProfileType that the SecurityProfile resource
    configures.

    Values:
      PROFILE_TYPE_UNSPECIFIED: Profile type not specified.
      THREAT_PREVENTION: Profile type for threat prevention.
      CUSTOM_MIRRORING: Profile type for packet mirroring v2
      CUSTOM_INTERCEPT: Profile type for TPPI.
      URL_FILTERING: Profile type for URL filtering.
      WILDFIRE_ANALYSIS: Profile type for WildFire Analysis.
    """
    PROFILE_TYPE_UNSPECIFIED = 0
    THREAT_PREVENTION = 1
    CUSTOM_MIRRORING = 2
    CUSTOM_INTERCEPT = 3
    URL_FILTERING = 4
    WILDFIRE_ANALYSIS = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  customInterceptProfile = _messages.MessageField('CustomInterceptProfile', 2)
  customMirroringProfile = _messages.MessageField('CustomMirroringProfile', 3)
  description = _messages.StringField(4)
  etag = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  threatPreventionProfile = _messages.MessageField('ThreatPreventionProfile', 8)
  type = _messages.EnumField('TypeValueValuesEnum', 9)
  updateTime = _messages.StringField(10)
  urlFilteringProfile = _messages.MessageField('UrlFilteringProfile', 11)
  wildfireAnalysisProfile = _messages.MessageField('WildfireAnalysisProfile', 12)


class SecurityProfileGroup(_messages.Message):
  r"""SecurityProfileGroup is a resource that defines the behavior for various
  ProfileTypes.

  Messages:
    LabelsValue: Optional. Labels as key value pairs.

  Fields:
    createTime: Output only. Resource creation timestamp.
    customInterceptProfile: Optional. Reference to a SecurityProfile with the
      CustomIntercept configuration.
    customMirroringProfile: Optional. Reference to a SecurityProfile with the
      CustomMirroring configuration.
    dataPathId: Output only. Identifier used by the data-path. Unique within
      {container, location}.
    description: Optional. An optional description of the profile group. Max
      length 2048 characters.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and may be sent on update and delete requests to
      ensure the client has an up-to-date value before proceeding.
    labels: Optional. Labels as key value pairs.
    name: Immutable. Identifier. Name of the SecurityProfileGroup resource. It
      matches pattern `projects|organizations/*/locations/{location}/securityP
      rofileGroups/{security_profile_group}`.
    threatPreventionProfile: Optional. Reference to a SecurityProfile with the
      ThreatPrevention configuration.
    updateTime: Output only. Last resource update timestamp.
    urlFilteringProfile: Optional. Reference to a SecurityProfile with the
      UrlFiltering configuration.
    wildfireAnalysisProfile: Optional. Reference to a SecurityProfile with the
      WildFire configuration.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  customInterceptProfile = _messages.StringField(2)
  customMirroringProfile = _messages.StringField(3)
  dataPathId = _messages.IntegerField(4, variant=_messages.Variant.UINT64)
  description = _messages.StringField(5)
  etag = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  threatPreventionProfile = _messages.StringField(9)
  updateTime = _messages.StringField(10)
  urlFilteringProfile = _messages.StringField(11)
  wildfireAnalysisProfile = _messages.StringField(12)


class ServerTlsPolicy(_messages.Message):
  r"""ServerTlsPolicy is a resource that specifies how a server should
  authenticate incoming requests. This resource itself does not affect
  configuration unless it is attached to a target HTTPS proxy or endpoint
  config selector resource. ServerTlsPolicy in the form accepted by
  Application Load Balancers can be attached only to TargetHttpsProxy with an
  `EXTERNAL`, `EXTERNAL_MANAGED` or `INTERNAL_MANAGED` load balancing scheme.
  Traffic Director compatible ServerTlsPolicies can be attached to
  EndpointPolicy and TargetHttpsProxy with Traffic Director
  `INTERNAL_SELF_MANAGED` load balancing scheme.

  Messages:
    LabelsValue: Set of label tags associated with the resource.

  Fields:
    allowOpen: This field applies only for Traffic Director policies. It is
      must be set to false for Application Load Balancer policies. Determines
      if server allows plaintext connections. If set to true, server allows
      plain text connections. By default, it is set to false. This setting is
      not exclusive of other encryption modes. For example, if `allow_open`
      and `mtls_policy` are set, server allows both plain text and mTLS
      connections. See documentation of other encryption modes to confirm
      compatibility. Consider using it if you wish to upgrade in place your
      deployment to TLS while having mixed TLS and non-TLS traffic reaching
      port :80.
    createTime: Output only. The timestamp when the resource was created.
    description: Free-text description of the resource.
    labels: Set of label tags associated with the resource.
    mtlsPolicy: This field is required if the policy is used with Application
      Load Balancers. This field can be empty for Traffic Director. Defines a
      mechanism to provision peer validation certificates for peer to peer
      authentication (Mutual TLS - mTLS). If not specified, client certificate
      will not be requested. The connection is treated as TLS and not mTLS. If
      `allow_open` and `mtls_policy` are set, server allows both plain text
      and mTLS connections.
    name: Required. Name of the ServerTlsPolicy resource. It matches the
      pattern
      `projects/*/locations/{location}/serverTlsPolicies/{server_tls_policy}`
    serverCertificate: Optional if policy is to be used with Traffic Director.
      For Application Load Balancers must be empty. Defines a mechanism to
      provision server identity (public and private keys). Cannot be combined
      with `allow_open` as a permissive mode that allows both plain text and
      TLS is not supported.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Set of label tags associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowOpen = _messages.BooleanField(1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  mtlsPolicy = _messages.MessageField('MTLSPolicy', 5)
  name = _messages.StringField(6)
  serverCertificate = _messages.MessageField('GoogleCloudNetworksecurityV1alpha1CertificateProvider', 7)
  updateTime = _messages.StringField(8)


class SeverityOverride(_messages.Message):
  r"""Defines what action to take for a specific severity match.

  Enums:
    ActionValueValuesEnum: Required. Threat action override.
    SeverityValueValuesEnum: Required. Severity level to match.

  Fields:
    action: Required. Threat action override.
    severity: Required. Severity level to match.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. Threat action override.

    Values:
      THREAT_ACTION_UNSPECIFIED: Threat action not specified.
      DEFAULT_ACTION: The default action (as specified by the vendor) is
        taken.
      ALLOW: The packet matching this rule will be allowed to transmit.
      ALERT: The packet matching this rule will be allowed to transmit, but a
        threat_log entry will be sent to the consumer project.
      DENY: The packet matching this rule will be dropped, and a threat_log
        entry will be sent to the consumer project.
    """
    THREAT_ACTION_UNSPECIFIED = 0
    DEFAULT_ACTION = 1
    ALLOW = 2
    ALERT = 3
    DENY = 4

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Required. Severity level to match.

    Values:
      SEVERITY_UNSPECIFIED: Severity level not specified.
      INFORMATIONAL: Suspicious events that do not pose an immediate threat,
        but that are reported to call attention to deeper problems that could
        possibly exist.
      LOW: Warning-level threats that have very little impact on an
        organization's infrastructure. They usually require local or physical
        system access and may often result in victim privacy issues and
        information leakage.
      MEDIUM: Minor threats in which impact is minimized, that do not
        compromise the target or exploits that require an attacker to reside
        on the same local network as the victim, affect only non-standard
        configurations or obscure applications, or provide very limited
        access.
      HIGH: Threats that have the ability to become critical but have
        mitigating factors; for example, they may be difficult to exploit, do
        not result in elevated privileges, or do not have a large victim pool.
      CRITICAL: Serious threats, such as those that affect default
        installations of widely deployed software, result in root compromise
        of servers, and the exploit code is widely available to attackers. The
        attacker usually does not need any special authentication credentials
        or knowledge about the individual victims and the target does not need
        to be manipulated into performing any special functions.
    """
    SEVERITY_UNSPECIFIED = 0
    INFORMATIONAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  severity = _messages.EnumField('SeverityValueValuesEnum', 2)


class Source(_messages.Message):
  r"""Specification of traffic source attributes.

  Fields:
    ipBlocks: Optional. List of CIDR ranges to match based on source IP
      address. At least one IP block should match. Single IP (e.g., "*******")
      and CIDR (e.g., "*******/24") are supported. Authorization based on
      source IP alone should be avoided. The IP addresses of any load
      balancers or proxies should be considered untrusted.
    principals: Optional. List of peer identities to match for authorization.
      At least one principal should match. Each peer can be an exact match, or
      a prefix match (example, "namespace/*") or a suffix match (example,
      "*/service-account") or a presence match "*". Authorization based on the
      principal name without certificate validation (configured by
      ServerTlsPolicy resource) is considered insecure.
  """

  ipBlocks = _messages.StringField(1, repeated=True)
  principals = _messages.StringField(2, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class ThirdPartyEndpointSettings(_messages.Message):
  r"""A ThirdPartyEndpointSettings object.

  Fields:
    targetFirewallAttachment: Optional. URL of the target firewall attachment.
  """

  targetFirewallAttachment = _messages.StringField(1)


class ThreatOverride(_messages.Message):
  r"""Defines what action to take for a specific threat_id match.

  Enums:
    ActionValueValuesEnum: Required. Threat action override. For some threat
      types, only a subset of actions applies.
    TypeValueValuesEnum: Output only. Type of the threat (read only).

  Fields:
    action: Required. Threat action override. For some threat types, only a
      subset of actions applies.
    threatId: Required. Vendor-specific ID of a threat to override.
    type: Output only. Type of the threat (read only).
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. Threat action override. For some threat types, only a subset
    of actions applies.

    Values:
      THREAT_ACTION_UNSPECIFIED: Threat action not specified.
      DEFAULT_ACTION: The default action (as specified by the vendor) is
        taken.
      ALLOW: The packet matching this rule will be allowed to transmit.
      ALERT: The packet matching this rule will be allowed to transmit, but a
        threat_log entry will be sent to the consumer project.
      DENY: The packet matching this rule will be dropped, and a threat_log
        entry will be sent to the consumer project.
    """
    THREAT_ACTION_UNSPECIFIED = 0
    DEFAULT_ACTION = 1
    ALLOW = 2
    ALERT = 3
    DENY = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the threat (read only).

    Values:
      THREAT_TYPE_UNSPECIFIED: Type of threat not specified.
      UNKNOWN: Type of threat is not derivable from threat ID. An override
        will be created for all types. Firewall will ignore overridden
        signature ID's that don't exist in the specific type.
      VULNERABILITY: Threats related to system flaws that an attacker might
        otherwise attempt to exploit.
      ANTIVIRUS: Threats related to viruses and malware found in executables
        and file types.
      SPYWARE: Threats related to command-and-control (C2) activity, where
        spyware on an infected client is collecting data without the user's
        consent and/or communicating with a remote attacker.
      DNS: Threats related to DNS.
    """
    THREAT_TYPE_UNSPECIFIED = 0
    UNKNOWN = 1
    VULNERABILITY = 2
    ANTIVIRUS = 3
    SPYWARE = 4
    DNS = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  threatId = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class ThreatPreventionProfile(_messages.Message):
  r"""ThreatPreventionProfile defines an action for specific threat signatures
  or severity levels.

  Fields:
    antivirusOverrides: Optional. Configuration for overriding antivirus
      actions per protocol.
    antivirusThreatOverrides: Optional. Configuration for overriding VIRUS
      threat actions per protocol.
    severityOverrides: Optional. Configuration for overriding threats actions
      by severity match.
    threatOverrides: Optional. Configuration for overriding threats actions by
      threat_id match. If a threat is matched both by configuration provided
      in severity_overrides and threat_overrides, the threat_overrides action
      is applied.
  """

  antivirusOverrides = _messages.MessageField('AntivirusOverride', 1, repeated=True)
  antivirusThreatOverrides = _messages.MessageField('AntivirusThreatOverride', 2, repeated=True)
  severityOverrides = _messages.MessageField('SeverityOverride', 3, repeated=True)
  threatOverrides = _messages.MessageField('ThreatOverride', 4, repeated=True)


class TlsCertificateFiles(_messages.Message):
  r"""Specification of TLS certificate files.

  Fields:
    certificatePath: Required. The path to the file that has the certificate
      containing public key.
    privateKeyPath: Required. The path to the file that has the private key.
  """

  certificatePath = _messages.StringField(1)
  privateKeyPath = _messages.StringField(2)


class TlsInspectionPolicy(_messages.Message):
  r"""The TlsInspectionPolicy resource contains references to CA pools in
  Certificate Authority Service and associated metadata.

  Enums:
    MinTlsVersionValueValuesEnum: Optional. Minimum TLS version that the
      firewall should use when negotiating connections with both clients and
      servers. If this is not set, then the default value is to allow the
      broadest set of clients and servers (TLS 1.0 or higher). Setting this to
      more restrictive values may improve security, but may also prevent the
      firewall from connecting to some clients or servers. Note that Secure
      Web Proxy does not yet honor this field.
    TlsFeatureProfileValueValuesEnum: Optional. The selected Profile. If this
      is not set, then the default value is to allow the broadest set of
      clients and servers ("PROFILE_COMPATIBLE"). Setting this to more
      restrictive values may improve security, but may also prevent the TLS
      inspection proxy from connecting to some clients or servers. Note that
      Secure Web Proxy does not yet honor this field.

  Fields:
    caPool: Required. A CA pool resource used to issue interception
      certificates. The CA pool string has a relative resource path following
      the form "projects/{project}/locations/{location}/caPools/{ca_pool}".
    createTime: Output only. The timestamp when the resource was created.
    customTlsFeatures: Optional. List of custom TLS cipher suites selected.
      This field is valid only if the selected tls_feature_profile is CUSTOM.
      The compute.SslPoliciesService.ListAvailableFeatures method returns the
      set of features that can be specified in this list. Note that Secure Web
      Proxy does not yet honor this field.
    description: Optional. Free-text description of the resource.
    excludePublicCaSet: Optional. If FALSE (the default), use our default set
      of public CAs in addition to any CAs specified in trust_config. These
      public CAs are currently based on the Mozilla Root Program and are
      subject to change over time. If TRUE, do not accept our default set of
      public CAs. Only CAs specified in trust_config will be accepted. This
      defaults to FALSE (use public CAs in addition to trust_config) for
      backwards compatibility, but trusting public root CAs is *not
      recommended* unless the traffic in question is outbound to public web
      servers. When possible, prefer setting this to "false" and explicitly
      specifying trusted CAs and certificates in a TrustConfig. Note that
      Secure Web Proxy does not yet honor this field.
    minTlsVersion: Optional. Minimum TLS version that the firewall should use
      when negotiating connections with both clients and servers. If this is
      not set, then the default value is to allow the broadest set of clients
      and servers (TLS 1.0 or higher). Setting this to more restrictive values
      may improve security, but may also prevent the firewall from connecting
      to some clients or servers. Note that Secure Web Proxy does not yet
      honor this field.
    name: Required. Name of the resource. Name is of the form projects/{projec
      t}/locations/{location}/tlsInspectionPolicies/{tls_inspection_policy}
      tls_inspection_policy should match the
      pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    tlsFeatureProfile: Optional. The selected Profile. If this is not set,
      then the default value is to allow the broadest set of clients and
      servers ("PROFILE_COMPATIBLE"). Setting this to more restrictive values
      may improve security, but may also prevent the TLS inspection proxy from
      connecting to some clients or servers. Note that Secure Web Proxy does
      not yet honor this field.
    trustConfig: Optional. A TrustConfig resource used when making a
      connection to the TLS server. This is a relative resource path following
      the form
      "projects/{project}/locations/{location}/trustConfigs/{trust_config}".
      This is necessary to intercept TLS connections to servers with
      certificates signed by a private CA or self-signed certificates. Note
      that Secure Web Proxy does not yet honor this field.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  class MinTlsVersionValueValuesEnum(_messages.Enum):
    r"""Optional. Minimum TLS version that the firewall should use when
    negotiating connections with both clients and servers. If this is not set,
    then the default value is to allow the broadest set of clients and servers
    (TLS 1.0 or higher). Setting this to more restrictive values may improve
    security, but may also prevent the firewall from connecting to some
    clients or servers. Note that Secure Web Proxy does not yet honor this
    field.

    Values:
      TLS_VERSION_UNSPECIFIED: Indicates no TLS version was specified.
      TLS_1_0: TLS 1.0
      TLS_1_1: TLS 1.1
      TLS_1_2: TLS 1.2
      TLS_1_3: TLS 1.3
    """
    TLS_VERSION_UNSPECIFIED = 0
    TLS_1_0 = 1
    TLS_1_1 = 2
    TLS_1_2 = 3
    TLS_1_3 = 4

  class TlsFeatureProfileValueValuesEnum(_messages.Enum):
    r"""Optional. The selected Profile. If this is not set, then the default
    value is to allow the broadest set of clients and servers
    ("PROFILE_COMPATIBLE"). Setting this to more restrictive values may
    improve security, but may also prevent the TLS inspection proxy from
    connecting to some clients or servers. Note that Secure Web Proxy does not
    yet honor this field.

    Values:
      PROFILE_UNSPECIFIED: Indicates no profile was specified.
      PROFILE_COMPATIBLE: Compatible profile. Allows the broadest set of
        clients, even those which support only out-of-date SSL features to
        negotiate with the TLS inspection proxy.
      PROFILE_MODERN: Modern profile. Supports a wide set of SSL features,
        allowing modern clients to negotiate SSL with the TLS inspection
        proxy.
      PROFILE_RESTRICTED: Restricted profile. Supports a reduced set of SSL
        features, intended to meet stricter compliance requirements.
      PROFILE_CUSTOM: Custom profile. Allow only the set of allowed SSL
        features specified in the custom_features field of SslPolicy.
    """
    PROFILE_UNSPECIFIED = 0
    PROFILE_COMPATIBLE = 1
    PROFILE_MODERN = 2
    PROFILE_RESTRICTED = 3
    PROFILE_CUSTOM = 4

  caPool = _messages.StringField(1)
  createTime = _messages.StringField(2)
  customTlsFeatures = _messages.StringField(3, repeated=True)
  description = _messages.StringField(4)
  excludePublicCaSet = _messages.BooleanField(5)
  minTlsVersion = _messages.EnumField('MinTlsVersionValueValuesEnum', 6)
  name = _messages.StringField(7)
  tlsFeatureProfile = _messages.EnumField('TlsFeatureProfileValueValuesEnum', 8)
  trustConfig = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class UllMirroredNetwork(_messages.Message):
  r"""Message describing UllMirroredNetwork object

  Enums:
    StateValueValuesEnum: Output only. Current state of the mirrored network.

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    createTime: Output only. [Output only] Create time stamp
    labels: Optional. Labels as key value pairs
    name: Immutable. Identifier. The name of the UllMirroredNetwork.
    network: Required. Immutable. The mirrored network name. e.g.
      "projects/my-project/global/networks/my-network".
    reconciling: Output only. Whether reconciling is in progress, recommended
      per https://google.aip.dev/128.
    state: Output only. Current state of the mirrored network.
    updateTime: Output only. [Output only] Update time stamp
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the mirrored network.

    Values:
      STATE_UNSPECIFIED: Not set.
      ACTIVE: Ready.
      CREATING: Being created.
      DELETING: Being deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  network = _messages.StringField(4)
  reconciling = _messages.BooleanField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  updateTime = _messages.StringField(7)


class UllMirroringCollector(_messages.Message):
  r"""Message describing UllMirroringCollector object

  Enums:
    StateValueValuesEnum: Output only. Current state of the collector.

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    createTime: Output only. [Output only] Create time stamp
    engine: Required. Immutable. The engine resource to which the collector
      points to. Format is: projects/{project}/locations/{location}/ullMirrori
      ngEngines/{ull_mirroring_engine}
    forwardingRule: Required. Immutable. The regional load balancer which the
      mirrored traffic should be forwarded to. Format is:
      projects/{project}/regions/{region}/forwardingRules/{forwardingRule}
    labels: Optional. Labels as key value pairs
    name: Immutable. Identifier. The name of the UllMirroringCollector.
    reconciling: Output only. Whether reconciling is in progress, recommended
      per https://google.aip.dev/128.
    state: Output only. Current state of the collector.
    updateTime: Output only. [Output only] Update time stamp
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the collector.

    Values:
      STATE_UNSPECIFIED: Not set.
      ACTIVE: Ready.
      CREATING: Being created.
      DELETING: Being deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  engine = _messages.StringField(2)
  forwardingRule = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  reconciling = _messages.BooleanField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  updateTime = _messages.StringField(8)


class UllMirroringEngine(_messages.Message):
  r"""UllMirroringEngine is a resource that represents the Market Capture
  engine in a given location.

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    createTime: Output only. [Output only] Create time stamp
    labels: Optional. Labels as key value pairs
    name: Identifier. The name of the resource.
    reconciling: Output only. Whether reconciling is in progress, recommended
      per https://google.aip.dev/128.
    updateTime: Output only. [Output only] Update time stamp
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  reconciling = _messages.BooleanField(4)
  updateTime = _messages.StringField(5)


class UrlFilter(_messages.Message):
  r"""A URL filter defines an action to take for some URL match.

  Enums:
    FilteringActionValueValuesEnum: Required. The action taken when this
      filter is applied.

  Fields:
    filteringAction: Required. The action taken when this filter is applied.
    priority: Required. The priority of this filter within the URL Filtering
      Profile. Lower integers indicate higher priorities. The priority of a
      filter must be unique within a URL Filtering Profile.
    urls: Required. The list of strings that a URL must match with for this
      filter to be applied.
  """

  class FilteringActionValueValuesEnum(_messages.Enum):
    r"""Required. The action taken when this filter is applied.

    Values:
      URL_FILTERING_ACTION_UNSPECIFIED: Filtering action not specified.
      ALLOW: The connection matching this filter will be allowed to transmit.
      DENY: The connection matching this filter will be dropped.
    """
    URL_FILTERING_ACTION_UNSPECIFIED = 0
    ALLOW = 1
    DENY = 2

  filteringAction = _messages.EnumField('FilteringActionValueValuesEnum', 1)
  priority = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  urls = _messages.StringField(3, repeated=True)


class UrlFilteringProfile(_messages.Message):
  r"""UrlFilteringProfile defines filters based on URL.

  Fields:
    urlFilters: Optional. The list of filtering configs in which each config
      defines an action to take for some URL match.
  """

  urlFilters = _messages.MessageField('UrlFilter', 1, repeated=True)


class UrlList(_messages.Message):
  r"""UrlList proto helps users to set reusable, independently manageable
  lists of hosts, host patterns, URLs, URL patterns.

  Fields:
    createTime: Output only. Time when the security policy was created.
    description: Optional. Free-text description of the resource.
    name: Required. Name of the resource provided by the user. Name is of the
      form projects/{project}/locations/{location}/urlLists/{url_list}
      url_list should match the pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    updateTime: Output only. Time when the security policy was updated.
    values: Required. FQDNs and URLs.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  name = _messages.StringField(3)
  updateTime = _messages.StringField(4)
  values = _messages.StringField(5, repeated=True)


class ValidationCA(_messages.Message):
  r"""Specification of ValidationCA. Defines the mechanism to obtain the
  Certificate Authority certificate to validate the peer certificate.

  Fields:
    caCertPath: The path to the file holding the CA certificate to validate
      the client or server certificate.
    certificateProviderInstance: The certificate provider instance
      specification that will be passed to the data plane, which will be used
      to load necessary credential information.
    grpcEndpoint: gRPC specific configuration to access the gRPC server to
      obtain the CA certificate.
  """

  caCertPath = _messages.StringField(1)
  certificateProviderInstance = _messages.MessageField('CertificateProviderInstance', 2)
  grpcEndpoint = _messages.MessageField('GoogleCloudNetworksecurityV1alpha1GrpcEndpoint', 3)


class WildfireAnalysisProfile(_messages.Message):
  r"""WildfireAnalysisProfile defines Palo Alto Networks WildFire behavior.

  Fields:
    wildfireInlineCloudAnalysisRules: Optional. Configuration for WildFire
      inline cloud analysis.
    wildfireInlineMlOverrides: Optional. Configuration for overriding inline
      ML WildFire actions per protocol.
    wildfireInlineMlSettings: Optional. Settings for WildFire Inline ML
      analysis.
    wildfireOverrides: Optional. Configuration for overriding WildFire actions
      per protocol.
    wildfireRealtimeLookup: Optional. Whether to hold the transfer of a file
      while the WildFire real-time signature cloud performs a signature
      lookup. Default value is false.
    wildfireSubmissionRules: Optional. Configurations for WildFire file
      submissions.
  """

  wildfireInlineCloudAnalysisRules = _messages.MessageField('WildfireInlineCloudAnalysisRule', 1, repeated=True)
  wildfireInlineMlOverrides = _messages.MessageField('WildfireInlineMlOverride', 2, repeated=True)
  wildfireInlineMlSettings = _messages.MessageField('WildfireInlineMlSettings', 3, repeated=True)
  wildfireOverrides = _messages.MessageField('WildfireOverride', 4, repeated=True)
  wildfireRealtimeLookup = _messages.BooleanField(5)
  wildfireSubmissionRules = _messages.MessageField('WildfireSubmissionRule', 6, repeated=True)


class WildfireInlineCloudAnalysisRule(_messages.Message):
  r"""The list of file type configurations to be scanned by WildFire Inline
  Cloud Analysis.

  Enums:
    ActionValueValuesEnum: Required. Action to take when a threat is detected
      using WildFire Inline Cloud Analysis. The default Value is DENY.
    DirectionValueValuesEnum: Required. Direction for the file to be analyzed
      by WildFire Inline Cloud Analysis.
    FileSelectionModeValueValuesEnum: Required. File selection mode for
      WildFire inline cloud analysis.

  Fields:
    action: Required. Action to take when a threat is detected using WildFire
      Inline Cloud Analysis. The default Value is DENY.
    customFileTypes: Submit a custom list of file types for WildFire analysis.
    direction: Required. Direction for the file to be analyzed by WildFire
      Inline Cloud Analysis.
    fileSelectionMode: Required. File selection mode for WildFire inline cloud
      analysis.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. Action to take when a threat is detected using WildFire
    Inline Cloud Analysis. The default Value is DENY.

    Values:
      WILDFIRE_INLINE_CLOUD_ANALYSIS_ACTION_UNSPECIFIED: WildFire Inline Cloud
        Analysis action not specified.
      ALLOW: The files that caught by WildFire Inline Cloud Analysis will be
        allowed to transmit.
      DENY: The files that caught by WildFire Inline Cloud Analysis will be
        denied to transmit.
    """
    WILDFIRE_INLINE_CLOUD_ANALYSIS_ACTION_UNSPECIFIED = 0
    ALLOW = 1
    DENY = 2

  class DirectionValueValuesEnum(_messages.Enum):
    r"""Required. Direction for the file to be analyzed by WildFire Inline
    Cloud Analysis.

    Values:
      DIRECTION_UNSPECIFIED: Direction not specified.
      UPLOAD: Upload direction.
      DOWNLOAD: Download direction.
      BOTH: Both upload and download directions.
    """
    DIRECTION_UNSPECIFIED = 0
    UPLOAD = 1
    DOWNLOAD = 2
    BOTH = 3

  class FileSelectionModeValueValuesEnum(_messages.Enum):
    r"""Required. File selection mode for WildFire inline cloud analysis.

    Values:
      FILE_SELECTION_MODE_UNSPECIFIED: File selection mode not specified.
      ALL_FILE_TYPES: Submit all the file types for scan.
      CUSTOM_FILE_TYPES: Submit a custom list of file types for scan.
    """
    FILE_SELECTION_MODE_UNSPECIFIED = 0
    ALL_FILE_TYPES = 1
    CUSTOM_FILE_TYPES = 2

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  customFileTypes = _messages.MessageField('WildfireInlineCloudAnalysisRuleCustomFileTypes', 2)
  direction = _messages.EnumField('DirectionValueValuesEnum', 3)
  fileSelectionMode = _messages.EnumField('FileSelectionModeValueValuesEnum', 4)


class WildfireInlineCloudAnalysisRuleCustomFileTypes(_messages.Message):
  r"""The options to submit a custom list of file types for scan.

  Enums:
    FileTypesValueListEntryValuesEnum:

  Fields:
    fileTypes: Required. File types to be submitted for WildFire inline cloud
      analysis.
  """

  class FileTypesValueListEntryValuesEnum(_messages.Enum):
    r"""FileTypesValueListEntryValuesEnum enum type.

    Values:
      FILE_TYPE_UNSPECIFIED: File type not specified.
      PE: Portable Executable (PE) files.
    """
    FILE_TYPE_UNSPECIFIED = 0
    PE = 1

  fileTypes = _messages.EnumField('FileTypesValueListEntryValuesEnum', 1, repeated=True)


class WildfireInlineMlFileException(_messages.Message):
  r"""Defines the file to exclude from WildFire Inline ML analysis.

  Fields:
    filename: Optional. Name of the file to exclude from WildFire Inline ML
      analysis.
    partialHash: Required. Machine learning partial hash of the file to
      exclude from WildFire Inline ML analysis.
  """

  filename = _messages.StringField(1)
  partialHash = _messages.StringField(2)


class WildfireInlineMlOverride(_messages.Message):
  r"""Defines what action to take for WildFire Inline ML threats per protocol.

  Enums:
    ActionValueValuesEnum: Required. The action to take for WildFire Inline ML
      override.
    ProtocolValueValuesEnum: Required. Protocol to match for WildFire Inline
      ML override.

  Fields:
    action: Required. The action to take for WildFire Inline ML override.
    protocol: Required. Protocol to match for WildFire Inline ML override.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. The action to take for WildFire Inline ML override.

    Values:
      WILDFIRE_THREAT_ACTION_UNSPECIFIED: Threat action not specified.
      WILDFIRE_DEFAULT_ACTION: The default action (as specified by the vendor)
        is taken.
      WILDFIRE_ALLOW: The packet matching this rule will be allowed to
        transmit.
      WILDFIRE_ALERT: The packet matching this rule will be allowed to
        transmit, but a threat_log entry will be sent to the consumer project.
      WILDFIRE_DENY: The packet matching this rule will be dropped, and a
        threat_log entry will be sent to the consumer project.
    """
    WILDFIRE_THREAT_ACTION_UNSPECIFIED = 0
    WILDFIRE_DEFAULT_ACTION = 1
    WILDFIRE_ALLOW = 2
    WILDFIRE_ALERT = 3
    WILDFIRE_DENY = 4

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""Required. Protocol to match for WildFire Inline ML override.

    Values:
      WILDFIRE_PROTOCOL_UNSPECIFIED: Protocol not specified.
      WILDFIRE_SMTP: SMTP protocol
      WILDFIRE_SMB: SMB protocol
      WILDFIRE_POP3: POP3 protocol
      WILDFIRE_IMAP: IMAP protocol
      WILDFIRE_HTTP2: HTTP2 protocol
      WILDFIRE_HTTP: HTTP protocol
      WILDFIRE_FTP: FTP protocol
    """
    WILDFIRE_PROTOCOL_UNSPECIFIED = 0
    WILDFIRE_SMTP = 1
    WILDFIRE_SMB = 2
    WILDFIRE_POP3 = 3
    WILDFIRE_IMAP = 4
    WILDFIRE_HTTP2 = 5
    WILDFIRE_HTTP = 6
    WILDFIRE_FTP = 7

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 2)


class WildfireInlineMlSettings(_messages.Message):
  r"""Defines the settings for WildFire Inline ML analysis.

  Enums:
    InlineMlConfigsValueListEntryValuesEnum:

  Fields:
    fileExceptions: Optional. List of files to exclude from WildFire Inline ML
      analysis.
    inlineMlConfigs: Required. List of Inline ML configs to enable in WildFire
      Inline ML analysis.
  """

  class InlineMlConfigsValueListEntryValuesEnum(_messages.Enum):
    r"""InlineMlConfigsValueListEntryValuesEnum enum type.

    Values:
      INLINE_ML_CONFIG_UNSPECIFIED: Inline ML config not specified.
      WINDOWS_EXECUTABLE: Enable machine learning engine to dynamically detect
        malicious PE files.
      POWERSHELL_SCRIPT1: Enable machine learning engine to dynamically
        identify malicious PowerShell scripts with known length.
      POWERSHELL_SCRIPT2: Enable machine learning engine to dynamically
        identify malicious PowerShell script without known length.
      ELF: Enable machine learning engine to dynamically detect malicious ELF
        files.
      MS_OFFICE: Enable machine learning engine to dynamically detect
        malicious MSOffice (97-03) files.
      SHELL: Enable machine learning engine to dynamically detect malicious
        Shell files.
    """
    INLINE_ML_CONFIG_UNSPECIFIED = 0
    WINDOWS_EXECUTABLE = 1
    POWERSHELL_SCRIPT1 = 2
    POWERSHELL_SCRIPT2 = 3
    ELF = 4
    MS_OFFICE = 5
    SHELL = 6

  fileExceptions = _messages.MessageField('WildfireInlineMlFileException', 1, repeated=True)
  inlineMlConfigs = _messages.EnumField('InlineMlConfigsValueListEntryValuesEnum', 2, repeated=True)


class WildfireOverride(_messages.Message):
  r"""Defines what action to take for WildFire threats per protocol.

  Enums:
    ActionValueValuesEnum: Required. Threat action override. For some threat
      types, only a subset of actions applies.
    ProtocolValueValuesEnum: Required. Protocol to match.

  Fields:
    action: Required. Threat action override. For some threat types, only a
      subset of actions applies.
    protocol: Required. Protocol to match.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. Threat action override. For some threat types, only a subset
    of actions applies.

    Values:
      WILDFIRE_THREAT_ACTION_UNSPECIFIED: Threat action not specified.
      WILDFIRE_DEFAULT_ACTION: The default action (as specified by the vendor)
        is taken.
      WILDFIRE_ALLOW: The packet matching this rule will be allowed to
        transmit.
      WILDFIRE_ALERT: The packet matching this rule will be allowed to
        transmit, but a threat_log entry will be sent to the consumer project.
      WILDFIRE_DENY: The packet matching this rule will be dropped, and a
        threat_log entry will be sent to the consumer project.
    """
    WILDFIRE_THREAT_ACTION_UNSPECIFIED = 0
    WILDFIRE_DEFAULT_ACTION = 1
    WILDFIRE_ALLOW = 2
    WILDFIRE_ALERT = 3
    WILDFIRE_DENY = 4

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""Required. Protocol to match.

    Values:
      WILDFIRE_PROTOCOL_UNSPECIFIED: Protocol not specified.
      WILDFIRE_SMTP: SMTP protocol
      WILDFIRE_SMB: SMB protocol
      WILDFIRE_POP3: POP3 protocol
      WILDFIRE_IMAP: IMAP protocol
      WILDFIRE_HTTP2: HTTP2 protocol
      WILDFIRE_HTTP: HTTP protocol
      WILDFIRE_FTP: FTP protocol
    """
    WILDFIRE_PROTOCOL_UNSPECIFIED = 0
    WILDFIRE_SMTP = 1
    WILDFIRE_SMB = 2
    WILDFIRE_POP3 = 3
    WILDFIRE_IMAP = 4
    WILDFIRE_HTTP2 = 5
    WILDFIRE_HTTP = 6
    WILDFIRE_FTP = 7

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 2)


class WildfireSubmissionRule(_messages.Message):
  r"""Defines the file types to be submitted for WildFire analysis and the
  direction of the traffic.

  Enums:
    DirectionValueValuesEnum: Required. Direction for the files to be analyzed
      by WildFire.
    FileSelectionModeValueValuesEnum: Required. File selection mode for
      WildFire analysis.

  Fields:
    customFileTypes: Submit a custom list of file types for WildFire analysis.
    direction: Required. Direction for the files to be analyzed by WildFire.
    fileSelectionMode: Required. File selection mode for WildFire analysis.
  """

  class DirectionValueValuesEnum(_messages.Enum):
    r"""Required. Direction for the files to be analyzed by WildFire.

    Values:
      DIRECTION_UNSPECIFIED: Direction not specified.
      UPLOAD: Upload direction.
      DOWNLOAD: Download direction.
      BOTH: Both upload and download directions.
    """
    DIRECTION_UNSPECIFIED = 0
    UPLOAD = 1
    DOWNLOAD = 2
    BOTH = 3

  class FileSelectionModeValueValuesEnum(_messages.Enum):
    r"""Required. File selection mode for WildFire analysis.

    Values:
      FILE_SELECTION_MODE_UNSPECIFIED: File selection mode not specified.
      ALL_FILE_TYPES: Submit all the file types for scan.
      CUSTOM_FILE_TYPES: Submit a custom list of file types for scan.
    """
    FILE_SELECTION_MODE_UNSPECIFIED = 0
    ALL_FILE_TYPES = 1
    CUSTOM_FILE_TYPES = 2

  customFileTypes = _messages.MessageField('WildfireSubmissionRuleCustomFileTypes', 1)
  direction = _messages.EnumField('DirectionValueValuesEnum', 2)
  fileSelectionMode = _messages.EnumField('FileSelectionModeValueValuesEnum', 3)


class WildfireSubmissionRuleCustomFileTypes(_messages.Message):
  r"""The options to submit a custom list of file types for scan.

  Enums:
    FileTypesValueListEntryValuesEnum:

  Fields:
    fileTypes: Required. File types to be submitted for WildFire analysis.
  """

  class FileTypesValueListEntryValuesEnum(_messages.Enum):
    r"""FileTypesValueListEntryValuesEnum enum type.

    Values:
      FILE_TYPE_UNSPECIFIED: File type not specified.
      APK: Android Application Package (APK) files.
      ARCHIVE: Roshal Archive (RAR) and 7-Zip (7z) archive files.
      EMAIL_LINK: HTTP/HTTPS links contained in SMTP and POP3 email messages.
      FLASH: Adobe Flash applets and Flash content embedded in web pages.
      JAR: Java applets (JAR/class files types).
      LINUX: Executable and Linkable Format (ELF) files.
      MS_OFFICE: Files used by Microsoft Office.
      PDF: Portable Document Format (PDF) files.
      PE: Portable Executable (PE) files.
      SCRIPT: Various script files. Jscript (JS), VBScript (VBS), PowerShell
        Scripts (PS1), Batch (BAT), HTML Application (HTA).
    """
    FILE_TYPE_UNSPECIFIED = 0
    APK = 1
    ARCHIVE = 2
    EMAIL_LINK = 3
    FLASH = 4
    JAR = 5
    LINUX = 6
    MS_OFFICE = 7
    PDF = 8
    PE = 9
    SCRIPT = 10

  fileTypes = _messages.EnumField('FileTypesValueListEntryValuesEnum', 1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    AuthzPolicyAuthzRule, 'from_', 'from')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    NetworksecurityProjectsLocationsAddressGroupsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    NetworksecurityProjectsLocationsAuthorizationPoliciesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    NetworksecurityProjectsLocationsAuthzPoliciesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    NetworksecurityProjectsLocationsClientTlsPoliciesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    NetworksecurityProjectsLocationsServerTlsPoliciesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
