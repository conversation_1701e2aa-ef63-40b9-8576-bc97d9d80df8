"""Generated client library for networksecurity version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.networksecurity.v1alpha1 import networksecurity_v1alpha1_messages as messages


class NetworksecurityV1alpha1(base_api.BaseApiClient):
  """Generated client library for service networksecurity version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://networksecurity.googleapis.com/'
  MTLS_BASE_URL = 'https://networksecurity.mtls.googleapis.com/'

  _PACKAGE = 'networksecurity'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'NetworksecurityV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new networksecurity handle."""
    url = url or self.BASE_URL
    super(NetworksecurityV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.organizations_locations_addressGroups = self.OrganizationsLocationsAddressGroupsService(self)
    self.organizations_locations_firewallEndpoints = self.OrganizationsLocationsFirewallEndpointsService(self)
    self.organizations_locations_operations = self.OrganizationsLocationsOperationsService(self)
    self.organizations_locations_securityProfileGroups = self.OrganizationsLocationsSecurityProfileGroupsService(self)
    self.organizations_locations_securityProfiles = self.OrganizationsLocationsSecurityProfilesService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_locations_addressGroups = self.ProjectsLocationsAddressGroupsService(self)
    self.projects_locations_authorizationPolicies = self.ProjectsLocationsAuthorizationPoliciesService(self)
    self.projects_locations_authzPolicies = self.ProjectsLocationsAuthzPoliciesService(self)
    self.projects_locations_backendAuthenticationConfigs = self.ProjectsLocationsBackendAuthenticationConfigsService(self)
    self.projects_locations_clientTlsPolicies = self.ProjectsLocationsClientTlsPoliciesService(self)
    self.projects_locations_dnsThreatDetectors = self.ProjectsLocationsDnsThreatDetectorsService(self)
    self.projects_locations_firewallAttachments = self.ProjectsLocationsFirewallAttachmentsService(self)
    self.projects_locations_firewallEndpointAssociations = self.ProjectsLocationsFirewallEndpointAssociationsService(self)
    self.projects_locations_gatewayAttachments_gatewayEndpoints = self.ProjectsLocationsGatewayAttachmentsGatewayEndpointsService(self)
    self.projects_locations_gatewayAttachments = self.ProjectsLocationsGatewayAttachmentsService(self)
    self.projects_locations_gatewaySecurityPolicies_rules = self.ProjectsLocationsGatewaySecurityPoliciesRulesService(self)
    self.projects_locations_gatewaySecurityPolicies = self.ProjectsLocationsGatewaySecurityPoliciesService(self)
    self.projects_locations_interceptDeploymentGroups = self.ProjectsLocationsInterceptDeploymentGroupsService(self)
    self.projects_locations_interceptDeployments = self.ProjectsLocationsInterceptDeploymentsService(self)
    self.projects_locations_interceptEndpointGroupAssociations = self.ProjectsLocationsInterceptEndpointGroupAssociationsService(self)
    self.projects_locations_interceptEndpointGroups = self.ProjectsLocationsInterceptEndpointGroupsService(self)
    self.projects_locations_mirroringDeploymentGroups = self.ProjectsLocationsMirroringDeploymentGroupsService(self)
    self.projects_locations_mirroringDeployments = self.ProjectsLocationsMirroringDeploymentsService(self)
    self.projects_locations_mirroringEndpointGroupAssociations = self.ProjectsLocationsMirroringEndpointGroupAssociationsService(self)
    self.projects_locations_mirroringEndpointGroups = self.ProjectsLocationsMirroringEndpointGroupsService(self)
    self.projects_locations_mirroringEndpoints = self.ProjectsLocationsMirroringEndpointsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_partnerSSEEnvironments = self.ProjectsLocationsPartnerSSEEnvironmentsService(self)
    self.projects_locations_partnerSSEGateways = self.ProjectsLocationsPartnerSSEGatewaysService(self)
    self.projects_locations_partnerSSERealms = self.ProjectsLocationsPartnerSSERealmsService(self)
    self.projects_locations_sacAttachments = self.ProjectsLocationsSacAttachmentsService(self)
    self.projects_locations_sacRealms = self.ProjectsLocationsSacRealmsService(self)
    self.projects_locations_securityProfileGroups = self.ProjectsLocationsSecurityProfileGroupsService(self)
    self.projects_locations_securityProfiles = self.ProjectsLocationsSecurityProfilesService(self)
    self.projects_locations_serverTlsPolicies = self.ProjectsLocationsServerTlsPoliciesService(self)
    self.projects_locations_sseGatewayReferences = self.ProjectsLocationsSseGatewayReferencesService(self)
    self.projects_locations_tlsInspectionPolicies = self.ProjectsLocationsTlsInspectionPoliciesService(self)
    self.projects_locations_ullMirroringCollectors = self.ProjectsLocationsUllMirroringCollectorsService(self)
    self.projects_locations_ullMirroringEngines = self.ProjectsLocationsUllMirroringEnginesService(self)
    self.projects_locations_ullMirroringInfras_ullMirroredNetworks = self.ProjectsLocationsUllMirroringInfrasUllMirroredNetworksService(self)
    self.projects_locations_ullMirroringInfras = self.ProjectsLocationsUllMirroringInfrasService(self)
    self.projects_locations_urlLists = self.ProjectsLocationsUrlListsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class OrganizationsLocationsAddressGroupsService(base_api.BaseApiService):
    """Service class for the organizations_locations_addressGroups resource."""

    _NAME = 'organizations_locations_addressGroups'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.OrganizationsLocationsAddressGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddItems(self, request, global_params=None):
      r"""Adds items to an address group.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsAddItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AddItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:addItems',
        http_method='POST',
        method_id='networksecurity.organizations.locations.addressGroups.addItems',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=[],
        relative_path='v1alpha1/{+addressGroup}:addItems',
        request_field='addAddressGroupItemsRequest',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsAddItemsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def CloneItems(self, request, global_params=None):
      r"""Clones items from one address group to another.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsCloneItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CloneItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    CloneItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:cloneItems',
        http_method='POST',
        method_id='networksecurity.organizations.locations.addressGroups.cloneItems',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=[],
        relative_path='v1alpha1/{+addressGroup}:cloneItems',
        request_field='cloneAddressGroupItemsRequest',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsCloneItemsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new address group in a given project and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups',
        http_method='POST',
        method_id='networksecurity.organizations.locations.addressGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['addressGroupId', 'requestId'],
        relative_path='v1alpha1/{+parent}/addressGroups',
        request_field='addressGroup',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an address group.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}',
        http_method='DELETE',
        method_id='networksecurity.organizations.locations.addressGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single address group.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AddressGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}',
        http_method='GET',
        method_id='networksecurity.organizations.locations.addressGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsGetRequest',
        response_type_name='AddressGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists address groups in a given project and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAddressGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups',
        http_method='GET',
        method_id='networksecurity.organizations.locations.addressGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v1alpha1/{+parent}/addressGroups',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsListRequest',
        response_type_name='ListAddressGroupsResponse',
        supports_download=False,
    )

    def ListReferences(self, request, global_params=None):
      r"""Lists references of an address group.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsListReferencesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAddressGroupReferencesResponse) The response message.
      """
      config = self.GetMethodConfig('ListReferences')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListReferences.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:listReferences',
        http_method='GET',
        method_id='networksecurity.organizations.locations.addressGroups.listReferences',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+addressGroup}:listReferences',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsListReferencesRequest',
        response_type_name='ListAddressGroupReferencesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates parameters of an address group.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}',
        http_method='PATCH',
        method_id='networksecurity.organizations.locations.addressGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='addressGroup',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RemoveItems(self, request, global_params=None):
      r"""Removes items from an address group.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsRemoveItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RemoveItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:removeItems',
        http_method='POST',
        method_id='networksecurity.organizations.locations.addressGroups.removeItems',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=[],
        relative_path='v1alpha1/{+addressGroup}:removeItems',
        request_field='removeAddressGroupItemsRequest',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsRemoveItemsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsLocationsFirewallEndpointsService(base_api.BaseApiService):
    """Service class for the organizations_locations_firewallEndpoints resource."""

    _NAME = 'organizations_locations_firewallEndpoints'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.OrganizationsLocationsFirewallEndpointsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new FirewallEndpoint in a given project and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsFirewallEndpointsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints',
        http_method='POST',
        method_id='networksecurity.organizations.locations.firewallEndpoints.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['firewallEndpointId', 'requestId'],
        relative_path='v1alpha1/{+parent}/firewallEndpoints',
        request_field='firewallEndpoint',
        request_type_name='NetworksecurityOrganizationsLocationsFirewallEndpointsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Endpoint.

      Args:
        request: (NetworksecurityOrganizationsLocationsFirewallEndpointsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints/{firewallEndpointsId}',
        http_method='DELETE',
        method_id='networksecurity.organizations.locations.firewallEndpoints.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsFirewallEndpointsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Endpoint.

      Args:
        request: (NetworksecurityOrganizationsLocationsFirewallEndpointsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FirewallEndpoint) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints/{firewallEndpointsId}',
        http_method='GET',
        method_id='networksecurity.organizations.locations.firewallEndpoints.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsFirewallEndpointsGetRequest',
        response_type_name='FirewallEndpoint',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists FirewallEndpoints in a given project and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsFirewallEndpointsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFirewallEndpointsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints',
        http_method='GET',
        method_id='networksecurity.organizations.locations.firewallEndpoints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/firewallEndpoints',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsFirewallEndpointsListRequest',
        response_type_name='ListFirewallEndpointsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single Endpoint.

      Args:
        request: (NetworksecurityOrganizationsLocationsFirewallEndpointsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints/{firewallEndpointsId}',
        http_method='PATCH',
        method_id='networksecurity.organizations.locations.firewallEndpoints.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='firewallEndpoint',
        request_type_name='NetworksecurityOrganizationsLocationsFirewallEndpointsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_operations resource."""

    _NAME = 'organizations_locations_operations'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.OrganizationsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (NetworksecurityOrganizationsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='networksecurity.organizations.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='NetworksecurityOrganizationsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (NetworksecurityOrganizationsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='networksecurity.organizations.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (NetworksecurityOrganizationsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='networksecurity.organizations.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (NetworksecurityOrganizationsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='networksecurity.organizations.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsSecurityProfileGroupsService(base_api.BaseApiService):
    """Service class for the organizations_locations_securityProfileGroups resource."""

    _NAME = 'organizations_locations_securityProfileGroups'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.OrganizationsLocationsSecurityProfileGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SecurityProfileGroup in a given organization and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfileGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups',
        http_method='POST',
        method_id='networksecurity.organizations.locations.securityProfileGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['securityProfileGroupId'],
        relative_path='v1alpha1/{+parent}/securityProfileGroups',
        request_field='securityProfileGroup',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfileGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single SecurityProfileGroup.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfileGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}',
        http_method='DELETE',
        method_id='networksecurity.organizations.locations.securityProfileGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfileGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single SecurityProfileGroup.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfileGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityProfileGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}',
        http_method='GET',
        method_id='networksecurity.organizations.locations.securityProfileGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfileGroupsGetRequest',
        response_type_name='SecurityProfileGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SecurityProfileGroups in a given organization and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfileGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityProfileGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups',
        http_method='GET',
        method_id='networksecurity.organizations.locations.securityProfileGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/securityProfileGroups',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfileGroupsListRequest',
        response_type_name='ListSecurityProfileGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single SecurityProfileGroup.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfileGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}',
        http_method='PATCH',
        method_id='networksecurity.organizations.locations.securityProfileGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='securityProfileGroup',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfileGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsLocationsSecurityProfilesService(base_api.BaseApiService):
    """Service class for the organizations_locations_securityProfiles resource."""

    _NAME = 'organizations_locations_securityProfiles'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.OrganizationsLocationsSecurityProfilesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SecurityProfile in a given organization and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfilesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles',
        http_method='POST',
        method_id='networksecurity.organizations.locations.securityProfiles.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['securityProfileId'],
        relative_path='v1alpha1/{+parent}/securityProfiles',
        request_field='securityProfile',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfilesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single SecurityProfile.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfilesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}',
        http_method='DELETE',
        method_id='networksecurity.organizations.locations.securityProfiles.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfilesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single SecurityProfile.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfilesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityProfile) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}',
        http_method='GET',
        method_id='networksecurity.organizations.locations.securityProfiles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfilesGetRequest',
        response_type_name='SecurityProfile',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SecurityProfiles in a given organization and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfilesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityProfilesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles',
        http_method='GET',
        method_id='networksecurity.organizations.locations.securityProfiles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/securityProfiles',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfilesListRequest',
        response_type_name='ListSecurityProfilesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single SecurityProfile.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfilesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}',
        http_method='PATCH',
        method_id='networksecurity.organizations.locations.securityProfiles.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='securityProfile',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfilesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsAddressGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_addressGroups resource."""

    _NAME = 'projects_locations_addressGroups'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsAddressGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddItems(self, request, global_params=None):
      r"""Adds items to an address group.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsAddItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AddItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:addItems',
        http_method='POST',
        method_id='networksecurity.projects.locations.addressGroups.addItems',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=[],
        relative_path='v1alpha1/{+addressGroup}:addItems',
        request_field='addAddressGroupItemsRequest',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsAddItemsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def CloneItems(self, request, global_params=None):
      r"""Clones items from one address group to another.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsCloneItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CloneItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    CloneItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:cloneItems',
        http_method='POST',
        method_id='networksecurity.projects.locations.addressGroups.cloneItems',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=[],
        relative_path='v1alpha1/{+addressGroup}:cloneItems',
        request_field='cloneAddressGroupItemsRequest',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsCloneItemsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new address group in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups',
        http_method='POST',
        method_id='networksecurity.projects.locations.addressGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['addressGroupId', 'requestId'],
        relative_path='v1alpha1/{+parent}/addressGroups',
        request_field='addressGroup',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single address group.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.addressGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single address group.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AddressGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.addressGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsGetRequest',
        response_type_name='AddressGroup',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:getIamPolicy',
        http_method='GET',
        method_id='networksecurity.projects.locations.addressGroups.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists address groups in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAddressGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups',
        http_method='GET',
        method_id='networksecurity.projects.locations.addressGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v1alpha1/{+parent}/addressGroups',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsListRequest',
        response_type_name='ListAddressGroupsResponse',
        supports_download=False,
    )

    def ListReferences(self, request, global_params=None):
      r"""Lists references of an address group.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsListReferencesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAddressGroupReferencesResponse) The response message.
      """
      config = self.GetMethodConfig('ListReferences')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListReferences.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:listReferences',
        http_method='GET',
        method_id='networksecurity.projects.locations.addressGroups.listReferences',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+addressGroup}:listReferences',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsListReferencesRequest',
        response_type_name='ListAddressGroupReferencesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single address group.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.addressGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='addressGroup',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RemoveItems(self, request, global_params=None):
      r"""Removes items from an address group.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsRemoveItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RemoveItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:removeItems',
        http_method='POST',
        method_id='networksecurity.projects.locations.addressGroups.removeItems',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=[],
        relative_path='v1alpha1/{+addressGroup}:removeItems',
        request_field='removeAddressGroupItemsRequest',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsRemoveItemsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:setIamPolicy',
        http_method='POST',
        method_id='networksecurity.projects.locations.addressGroups.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:testIamPermissions',
        http_method='POST',
        method_id='networksecurity.projects.locations.addressGroups.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAuthorizationPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_authorizationPolicies resource."""

    _NAME = 'projects_locations_authorizationPolicies'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsAuthorizationPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new AuthorizationPolicy in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies',
        http_method='POST',
        method_id='networksecurity.projects.locations.authorizationPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['authorizationPolicyId'],
        relative_path='v1alpha1/{+parent}/authorizationPolicies',
        request_field='authorizationPolicy',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single AuthorizationPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.authorizationPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single AuthorizationPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AuthorizationPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.authorizationPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesGetRequest',
        response_type_name='AuthorizationPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}:getIamPolicy',
        http_method='GET',
        method_id='networksecurity.projects.locations.authorizationPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists AuthorizationPolicies in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAuthorizationPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies',
        http_method='GET',
        method_id='networksecurity.projects.locations.authorizationPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/authorizationPolicies',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesListRequest',
        response_type_name='ListAuthorizationPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single AuthorizationPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.authorizationPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='authorizationPolicy',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='networksecurity.projects.locations.authorizationPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='networksecurity.projects.locations.authorizationPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAuthzPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_authzPolicies resource."""

    _NAME = 'projects_locations_authzPolicies'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsAuthzPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new AuthzPolicy in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsAuthzPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authzPolicies',
        http_method='POST',
        method_id='networksecurity.projects.locations.authzPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['authzPolicyId', 'requestId'],
        relative_path='v1alpha1/{+parent}/authzPolicies',
        request_field='authzPolicy',
        request_type_name='NetworksecurityProjectsLocationsAuthzPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single AuthzPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsAuthzPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authzPolicies/{authzPoliciesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.authzPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAuthzPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single AuthzPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsAuthzPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AuthzPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authzPolicies/{authzPoliciesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.authzPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAuthzPoliciesGetRequest',
        response_type_name='AuthzPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworksecurityProjectsLocationsAuthzPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authzPolicies/{authzPoliciesId}:getIamPolicy',
        http_method='GET',
        method_id='networksecurity.projects.locations.authzPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAuthzPoliciesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists AuthzPolicies in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsAuthzPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAuthzPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authzPolicies',
        http_method='GET',
        method_id='networksecurity.projects.locations.authzPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/authzPolicies',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAuthzPoliciesListRequest',
        response_type_name='ListAuthzPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single AuthzPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsAuthzPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authzPolicies/{authzPoliciesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.authzPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='authzPolicy',
        request_type_name='NetworksecurityProjectsLocationsAuthzPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworksecurityProjectsLocationsAuthzPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authzPolicies/{authzPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='networksecurity.projects.locations.authzPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='NetworksecurityProjectsLocationsAuthzPoliciesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworksecurityProjectsLocationsAuthzPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authzPolicies/{authzPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='networksecurity.projects.locations.authzPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='NetworksecurityProjectsLocationsAuthzPoliciesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsBackendAuthenticationConfigsService(base_api.BaseApiService):
    """Service class for the projects_locations_backendAuthenticationConfigs resource."""

    _NAME = 'projects_locations_backendAuthenticationConfigs'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsBackendAuthenticationConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new BackendAuthenticationConfig in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsBackendAuthenticationConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/backendAuthenticationConfigs',
        http_method='POST',
        method_id='networksecurity.projects.locations.backendAuthenticationConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['backendAuthenticationConfigId'],
        relative_path='v1alpha1/{+parent}/backendAuthenticationConfigs',
        request_field='backendAuthenticationConfig',
        request_type_name='NetworksecurityProjectsLocationsBackendAuthenticationConfigsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single BackendAuthenticationConfig to BackendAuthenticationConfig.

      Args:
        request: (NetworksecurityProjectsLocationsBackendAuthenticationConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/backendAuthenticationConfigs/{backendAuthenticationConfigsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.backendAuthenticationConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsBackendAuthenticationConfigsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single BackendAuthenticationConfig to BackendAuthenticationConfig.

      Args:
        request: (NetworksecurityProjectsLocationsBackendAuthenticationConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BackendAuthenticationConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/backendAuthenticationConfigs/{backendAuthenticationConfigsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.backendAuthenticationConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsBackendAuthenticationConfigsGetRequest',
        response_type_name='BackendAuthenticationConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BackendAuthenticationConfigs in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsBackendAuthenticationConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBackendAuthenticationConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/backendAuthenticationConfigs',
        http_method='GET',
        method_id='networksecurity.projects.locations.backendAuthenticationConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/backendAuthenticationConfigs',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsBackendAuthenticationConfigsListRequest',
        response_type_name='ListBackendAuthenticationConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single BackendAuthenticationConfig to BackendAuthenticationConfig.

      Args:
        request: (NetworksecurityProjectsLocationsBackendAuthenticationConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/backendAuthenticationConfigs/{backendAuthenticationConfigsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.backendAuthenticationConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='backendAuthenticationConfig',
        request_type_name='NetworksecurityProjectsLocationsBackendAuthenticationConfigsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsClientTlsPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_clientTlsPolicies resource."""

    _NAME = 'projects_locations_clientTlsPolicies'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsClientTlsPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ClientTlsPolicy in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies',
        http_method='POST',
        method_id='networksecurity.projects.locations.clientTlsPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['clientTlsPolicyId'],
        relative_path='v1alpha1/{+parent}/clientTlsPolicies',
        request_field='clientTlsPolicy',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ClientTlsPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.clientTlsPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ClientTlsPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ClientTlsPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.clientTlsPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesGetRequest',
        response_type_name='ClientTlsPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}:getIamPolicy',
        http_method='GET',
        method_id='networksecurity.projects.locations.clientTlsPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ClientTlsPolicies in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListClientTlsPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies',
        http_method='GET',
        method_id='networksecurity.projects.locations.clientTlsPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/clientTlsPolicies',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesListRequest',
        response_type_name='ListClientTlsPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single ClientTlsPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.clientTlsPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='clientTlsPolicy',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='networksecurity.projects.locations.clientTlsPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='networksecurity.projects.locations.clientTlsPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDnsThreatDetectorsService(base_api.BaseApiService):
    """Service class for the projects_locations_dnsThreatDetectors resource."""

    _NAME = 'projects_locations_dnsThreatDetectors'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsDnsThreatDetectorsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new DnsThreatDetector in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsDnsThreatDetectorsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DnsThreatDetector) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dnsThreatDetectors',
        http_method='POST',
        method_id='networksecurity.projects.locations.dnsThreatDetectors.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['dnsThreatDetectorId'],
        relative_path='v1alpha1/{+parent}/dnsThreatDetectors',
        request_field='dnsThreatDetector',
        request_type_name='NetworksecurityProjectsLocationsDnsThreatDetectorsCreateRequest',
        response_type_name='DnsThreatDetector',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single DnsThreatDetector.

      Args:
        request: (NetworksecurityProjectsLocationsDnsThreatDetectorsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dnsThreatDetectors/{dnsThreatDetectorsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.dnsThreatDetectors.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsDnsThreatDetectorsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single DnsThreatDetector.

      Args:
        request: (NetworksecurityProjectsLocationsDnsThreatDetectorsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DnsThreatDetector) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dnsThreatDetectors/{dnsThreatDetectorsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.dnsThreatDetectors.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsDnsThreatDetectorsGetRequest',
        response_type_name='DnsThreatDetector',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists DnsThreatDetectors in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsDnsThreatDetectorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDnsThreatDetectorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dnsThreatDetectors',
        http_method='GET',
        method_id='networksecurity.projects.locations.dnsThreatDetectors.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/dnsThreatDetectors',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsDnsThreatDetectorsListRequest',
        response_type_name='ListDnsThreatDetectorsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single DnsThreatDetector.

      Args:
        request: (NetworksecurityProjectsLocationsDnsThreatDetectorsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DnsThreatDetector) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dnsThreatDetectors/{dnsThreatDetectorsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.dnsThreatDetectors.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='dnsThreatDetector',
        request_type_name='NetworksecurityProjectsLocationsDnsThreatDetectorsPatchRequest',
        response_type_name='DnsThreatDetector',
        supports_download=False,
    )

  class ProjectsLocationsFirewallAttachmentsService(base_api.BaseApiService):
    """Service class for the projects_locations_firewallAttachments resource."""

    _NAME = 'projects_locations_firewallAttachments'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsFirewallAttachmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new FirewallAttachment in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallAttachmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallAttachments',
        http_method='POST',
        method_id='networksecurity.projects.locations.firewallAttachments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['firewallAttachmentId', 'requestId'],
        relative_path='v1alpha1/{+parent}/firewallAttachments',
        request_field='firewallAttachment',
        request_type_name='NetworksecurityProjectsLocationsFirewallAttachmentsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Attachment.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallAttachmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallAttachments/{firewallAttachmentsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.firewallAttachments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsFirewallAttachmentsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Attachment.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallAttachmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FirewallAttachment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallAttachments/{firewallAttachmentsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.firewallAttachments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsFirewallAttachmentsGetRequest',
        response_type_name='FirewallAttachment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists FirewallAttachments in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallAttachmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFirewallAttachmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallAttachments',
        http_method='GET',
        method_id='networksecurity.projects.locations.firewallAttachments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/firewallAttachments',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsFirewallAttachmentsListRequest',
        response_type_name='ListFirewallAttachmentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single FirewallAttachment.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallAttachmentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallAttachments/{firewallAttachmentsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.firewallAttachments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='firewallAttachment',
        request_type_name='NetworksecurityProjectsLocationsFirewallAttachmentsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsFirewallEndpointAssociationsService(base_api.BaseApiService):
    """Service class for the projects_locations_firewallEndpointAssociations resource."""

    _NAME = 'projects_locations_firewallEndpointAssociations'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsFirewallEndpointAssociationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new FirewallEndpointAssociation in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallEndpointAssociationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations',
        http_method='POST',
        method_id='networksecurity.projects.locations.firewallEndpointAssociations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['firewallEndpointAssociationId', 'requestId'],
        relative_path='v1alpha1/{+parent}/firewallEndpointAssociations',
        request_field='firewallEndpointAssociation',
        request_type_name='NetworksecurityProjectsLocationsFirewallEndpointAssociationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single FirewallEndpointAssociation.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallEndpointAssociationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations/{firewallEndpointAssociationsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.firewallEndpointAssociations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsFirewallEndpointAssociationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single FirewallEndpointAssociation.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallEndpointAssociationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FirewallEndpointAssociation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations/{firewallEndpointAssociationsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.firewallEndpointAssociations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsFirewallEndpointAssociationsGetRequest',
        response_type_name='FirewallEndpointAssociation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Associations in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallEndpointAssociationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFirewallEndpointAssociationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations',
        http_method='GET',
        method_id='networksecurity.projects.locations.firewallEndpointAssociations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/firewallEndpointAssociations',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsFirewallEndpointAssociationsListRequest',
        response_type_name='ListFirewallEndpointAssociationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single FirewallEndpointAssociation.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallEndpointAssociationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations/{firewallEndpointAssociationsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.firewallEndpointAssociations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='firewallEndpointAssociation',
        request_type_name='NetworksecurityProjectsLocationsFirewallEndpointAssociationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsGatewayAttachmentsGatewayEndpointsService(base_api.BaseApiService):
    """Service class for the projects_locations_gatewayAttachments_gatewayEndpoints resource."""

    _NAME = 'projects_locations_gatewayAttachments_gatewayEndpoints'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsGatewayAttachmentsGatewayEndpointsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new GatewayEndpoint in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewayAttachments/{gatewayAttachmentsId}/gatewayEndpoints',
        http_method='POST',
        method_id='networksecurity.projects.locations.gatewayAttachments.gatewayEndpoints.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['gatewayEndpointId', 'requestId'],
        relative_path='v1alpha1/{+parent}/gatewayEndpoints',
        request_field='gatewayEndpoint',
        request_type_name='NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single GatewayEndpoint.

      Args:
        request: (NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewayAttachments/{gatewayAttachmentsId}/gatewayEndpoints/{gatewayEndpointsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.gatewayAttachments.gatewayEndpoints.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""GetGatewayEndpoint gets a GatewayEndpoint resource.

      Args:
        request: (NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GatewayEndpoint) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewayAttachments/{gatewayAttachmentsId}/gatewayEndpoints/{gatewayEndpointsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.gatewayAttachments.gatewayEndpoints.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsGetRequest',
        response_type_name='GatewayEndpoint',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists GatewayEndpoints in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGatewayEndpointsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewayAttachments/{gatewayAttachmentsId}/gatewayEndpoints',
        http_method='GET',
        method_id='networksecurity.projects.locations.gatewayAttachments.gatewayEndpoints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/gatewayEndpoints',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewayAttachmentsGatewayEndpointsListRequest',
        response_type_name='ListGatewayEndpointsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGatewayAttachmentsService(base_api.BaseApiService):
    """Service class for the projects_locations_gatewayAttachments resource."""

    _NAME = 'projects_locations_gatewayAttachments'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsGatewayAttachmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new GatewayAttachment in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsGatewayAttachmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewayAttachments',
        http_method='POST',
        method_id='networksecurity.projects.locations.gatewayAttachments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['gatewayAttachmentId', 'requestId'],
        relative_path='v1alpha1/{+parent}/gatewayAttachments',
        request_field='gatewayAttachment',
        request_type_name='NetworksecurityProjectsLocationsGatewayAttachmentsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single GatewayAttachment.

      Args:
        request: (NetworksecurityProjectsLocationsGatewayAttachmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewayAttachments/{gatewayAttachmentsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.gatewayAttachments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewayAttachmentsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""GetGatewayAttachment gets a GatewayAttachment resource.

      Args:
        request: (NetworksecurityProjectsLocationsGatewayAttachmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GatewayAttachment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewayAttachments/{gatewayAttachmentsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.gatewayAttachments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewayAttachmentsGetRequest',
        response_type_name='GatewayAttachment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists GatewayAttachments in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsGatewayAttachmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGatewayAttachmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewayAttachments',
        http_method='GET',
        method_id='networksecurity.projects.locations.gatewayAttachments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/gatewayAttachments',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewayAttachmentsListRequest',
        response_type_name='ListGatewayAttachmentsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGatewaySecurityPoliciesRulesService(base_api.BaseApiService):
    """Service class for the projects_locations_gatewaySecurityPolicies_rules resource."""

    _NAME = 'projects_locations_gatewaySecurityPolicies_rules'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsGatewaySecurityPoliciesRulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new GatewaySecurityPolicy in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules',
        http_method='POST',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.rules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['gatewaySecurityPolicyRuleId'],
        relative_path='v1alpha1/{+parent}/rules',
        request_field='gatewaySecurityPolicyRule',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single GatewaySecurityPolicyRule.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules/{rulesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.rules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single GatewaySecurityPolicyRule.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GatewaySecurityPolicyRule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules/{rulesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.rules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesGetRequest',
        response_type_name='GatewaySecurityPolicyRule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists GatewaySecurityPolicyRules in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGatewaySecurityPolicyRulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules',
        http_method='GET',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.rules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/rules',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesListRequest',
        response_type_name='ListGatewaySecurityPolicyRulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single GatewaySecurityPolicyRule.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules/{rulesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.rules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='gatewaySecurityPolicyRule',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsGatewaySecurityPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_gatewaySecurityPolicies resource."""

    _NAME = 'projects_locations_gatewaySecurityPolicies'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsGatewaySecurityPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new GatewaySecurityPolicy in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies',
        http_method='POST',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['gatewaySecurityPolicyId'],
        relative_path='v1alpha1/{+parent}/gatewaySecurityPolicies',
        request_field='gatewaySecurityPolicy',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single GatewaySecurityPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single GatewaySecurityPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GatewaySecurityPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesGetRequest',
        response_type_name='GatewaySecurityPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists GatewaySecurityPolicies in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGatewaySecurityPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies',
        http_method='GET',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/gatewaySecurityPolicies',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesListRequest',
        response_type_name='ListGatewaySecurityPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single GatewaySecurityPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='gatewaySecurityPolicy',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsInterceptDeploymentGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_interceptDeploymentGroups resource."""

    _NAME = 'projects_locations_interceptDeploymentGroups'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsInterceptDeploymentGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a deployment group in a given project and location. See https://google.aip.dev/133.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptDeploymentGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptDeploymentGroups',
        http_method='POST',
        method_id='networksecurity.projects.locations.interceptDeploymentGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['interceptDeploymentGroupId', 'requestId'],
        relative_path='v1alpha1/{+parent}/interceptDeploymentGroups',
        request_field='interceptDeploymentGroup',
        request_type_name='NetworksecurityProjectsLocationsInterceptDeploymentGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a deployment group. See https://google.aip.dev/135.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptDeploymentGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptDeploymentGroups/{interceptDeploymentGroupsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.interceptDeploymentGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsInterceptDeploymentGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a specific deployment group. See https://google.aip.dev/131.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptDeploymentGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (InterceptDeploymentGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptDeploymentGroups/{interceptDeploymentGroupsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.interceptDeploymentGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsInterceptDeploymentGroupsGetRequest',
        response_type_name='InterceptDeploymentGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists deployment groups in a given project and location. See https://google.aip.dev/132.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptDeploymentGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInterceptDeploymentGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptDeploymentGroups',
        http_method='GET',
        method_id='networksecurity.projects.locations.interceptDeploymentGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/interceptDeploymentGroups',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsInterceptDeploymentGroupsListRequest',
        response_type_name='ListInterceptDeploymentGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a deployment group. See https://google.aip.dev/134.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptDeploymentGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptDeploymentGroups/{interceptDeploymentGroupsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.interceptDeploymentGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='interceptDeploymentGroup',
        request_type_name='NetworksecurityProjectsLocationsInterceptDeploymentGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsInterceptDeploymentsService(base_api.BaseApiService):
    """Service class for the projects_locations_interceptDeployments resource."""

    _NAME = 'projects_locations_interceptDeployments'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsInterceptDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a deployment in a given project and location. See https://google.aip.dev/133.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptDeploymentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptDeployments',
        http_method='POST',
        method_id='networksecurity.projects.locations.interceptDeployments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['interceptDeploymentId', 'requestId'],
        relative_path='v1alpha1/{+parent}/interceptDeployments',
        request_field='interceptDeployment',
        request_type_name='NetworksecurityProjectsLocationsInterceptDeploymentsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a deployment. See https://google.aip.dev/135.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptDeploymentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptDeployments/{interceptDeploymentsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.interceptDeployments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsInterceptDeploymentsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a specific deployment. See https://google.aip.dev/131.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptDeploymentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (InterceptDeployment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptDeployments/{interceptDeploymentsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.interceptDeployments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsInterceptDeploymentsGetRequest',
        response_type_name='InterceptDeployment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists deployments in a given project and location. See https://google.aip.dev/132.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInterceptDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptDeployments',
        http_method='GET',
        method_id='networksecurity.projects.locations.interceptDeployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/interceptDeployments',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsInterceptDeploymentsListRequest',
        response_type_name='ListInterceptDeploymentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a deployment. See https://google.aip.dev/134.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptDeploymentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptDeployments/{interceptDeploymentsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.interceptDeployments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='interceptDeployment',
        request_type_name='NetworksecurityProjectsLocationsInterceptDeploymentsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsInterceptEndpointGroupAssociationsService(base_api.BaseApiService):
    """Service class for the projects_locations_interceptEndpointGroupAssociations resource."""

    _NAME = 'projects_locations_interceptEndpointGroupAssociations'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsInterceptEndpointGroupAssociationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an association in a given project and location. See https://google.aip.dev/133.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroupAssociations',
        http_method='POST',
        method_id='networksecurity.projects.locations.interceptEndpointGroupAssociations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['interceptEndpointGroupAssociationId', 'requestId'],
        relative_path='v1alpha1/{+parent}/interceptEndpointGroupAssociations',
        request_field='interceptEndpointGroupAssociation',
        request_type_name='NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an association. See https://google.aip.dev/135.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroupAssociations/{interceptEndpointGroupAssociationsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.interceptEndpointGroupAssociations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a specific association. See https://google.aip.dev/131.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (InterceptEndpointGroupAssociation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroupAssociations/{interceptEndpointGroupAssociationsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.interceptEndpointGroupAssociations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsGetRequest',
        response_type_name='InterceptEndpointGroupAssociation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists associations in a given project and location. See https://google.aip.dev/132.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInterceptEndpointGroupAssociationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroupAssociations',
        http_method='GET',
        method_id='networksecurity.projects.locations.interceptEndpointGroupAssociations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/interceptEndpointGroupAssociations',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsListRequest',
        response_type_name='ListInterceptEndpointGroupAssociationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an association. See https://google.aip.dev/134.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroupAssociations/{interceptEndpointGroupAssociationsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.interceptEndpointGroupAssociations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='interceptEndpointGroupAssociation',
        request_type_name='NetworksecurityProjectsLocationsInterceptEndpointGroupAssociationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsInterceptEndpointGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_interceptEndpointGroups resource."""

    _NAME = 'projects_locations_interceptEndpointGroups'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsInterceptEndpointGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an endpoint group in a given project and location. See https://google.aip.dev/133.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptEndpointGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroups',
        http_method='POST',
        method_id='networksecurity.projects.locations.interceptEndpointGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['interceptEndpointGroupId', 'requestId'],
        relative_path='v1alpha1/{+parent}/interceptEndpointGroups',
        request_field='interceptEndpointGroup',
        request_type_name='NetworksecurityProjectsLocationsInterceptEndpointGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an endpoint group. See https://google.aip.dev/135.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptEndpointGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroups/{interceptEndpointGroupsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.interceptEndpointGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsInterceptEndpointGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a specific endpoint group. See https://google.aip.dev/131.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptEndpointGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (InterceptEndpointGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroups/{interceptEndpointGroupsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.interceptEndpointGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsInterceptEndpointGroupsGetRequest',
        response_type_name='InterceptEndpointGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists endpoint groups in a given project and location. See https://google.aip.dev/132.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptEndpointGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInterceptEndpointGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroups',
        http_method='GET',
        method_id='networksecurity.projects.locations.interceptEndpointGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/interceptEndpointGroups',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsInterceptEndpointGroupsListRequest',
        response_type_name='ListInterceptEndpointGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an endpoint group. See https://google.aip.dev/134.

      Args:
        request: (NetworksecurityProjectsLocationsInterceptEndpointGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroups/{interceptEndpointGroupsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.interceptEndpointGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='interceptEndpointGroup',
        request_type_name='NetworksecurityProjectsLocationsInterceptEndpointGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMirroringDeploymentGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_mirroringDeploymentGroups resource."""

    _NAME = 'projects_locations_mirroringDeploymentGroups'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsMirroringDeploymentGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a deployment group in a given project and location. See https://google.aip.dev/133.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringDeploymentGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringDeploymentGroups',
        http_method='POST',
        method_id='networksecurity.projects.locations.mirroringDeploymentGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['mirroringDeploymentGroupId', 'requestId'],
        relative_path='v1alpha1/{+parent}/mirroringDeploymentGroups',
        request_field='mirroringDeploymentGroup',
        request_type_name='NetworksecurityProjectsLocationsMirroringDeploymentGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a deployment group. See https://google.aip.dev/135.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringDeploymentGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringDeploymentGroups/{mirroringDeploymentGroupsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.mirroringDeploymentGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringDeploymentGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a specific deployment group. See https://google.aip.dev/131.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringDeploymentGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MirroringDeploymentGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringDeploymentGroups/{mirroringDeploymentGroupsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.mirroringDeploymentGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringDeploymentGroupsGetRequest',
        response_type_name='MirroringDeploymentGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists deployment groups in a given project and location. See https://google.aip.dev/132.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringDeploymentGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMirroringDeploymentGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringDeploymentGroups',
        http_method='GET',
        method_id='networksecurity.projects.locations.mirroringDeploymentGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/mirroringDeploymentGroups',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringDeploymentGroupsListRequest',
        response_type_name='ListMirroringDeploymentGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a deployment group. See https://google.aip.dev/134.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringDeploymentGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringDeploymentGroups/{mirroringDeploymentGroupsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.mirroringDeploymentGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='mirroringDeploymentGroup',
        request_type_name='NetworksecurityProjectsLocationsMirroringDeploymentGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMirroringDeploymentsService(base_api.BaseApiService):
    """Service class for the projects_locations_mirroringDeployments resource."""

    _NAME = 'projects_locations_mirroringDeployments'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsMirroringDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a deployment in a given project and location. See https://google.aip.dev/133.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringDeploymentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringDeployments',
        http_method='POST',
        method_id='networksecurity.projects.locations.mirroringDeployments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['mirroringDeploymentId', 'requestId'],
        relative_path='v1alpha1/{+parent}/mirroringDeployments',
        request_field='mirroringDeployment',
        request_type_name='NetworksecurityProjectsLocationsMirroringDeploymentsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a deployment. See https://google.aip.dev/135.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringDeploymentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringDeployments/{mirroringDeploymentsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.mirroringDeployments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringDeploymentsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a specific deployment. See https://google.aip.dev/131.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringDeploymentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MirroringDeployment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringDeployments/{mirroringDeploymentsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.mirroringDeployments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringDeploymentsGetRequest',
        response_type_name='MirroringDeployment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists deployments in a given project and location. See https://google.aip.dev/132.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMirroringDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringDeployments',
        http_method='GET',
        method_id='networksecurity.projects.locations.mirroringDeployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/mirroringDeployments',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringDeploymentsListRequest',
        response_type_name='ListMirroringDeploymentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a deployment. See https://google.aip.dev/134.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringDeploymentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringDeployments/{mirroringDeploymentsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.mirroringDeployments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='mirroringDeployment',
        request_type_name='NetworksecurityProjectsLocationsMirroringDeploymentsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMirroringEndpointGroupAssociationsService(base_api.BaseApiService):
    """Service class for the projects_locations_mirroringEndpointGroupAssociations resource."""

    _NAME = 'projects_locations_mirroringEndpointGroupAssociations'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsMirroringEndpointGroupAssociationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an association in a given project and location. See https://google.aip.dev/133.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroupAssociations',
        http_method='POST',
        method_id='networksecurity.projects.locations.mirroringEndpointGroupAssociations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['mirroringEndpointGroupAssociationId', 'requestId'],
        relative_path='v1alpha1/{+parent}/mirroringEndpointGroupAssociations',
        request_field='mirroringEndpointGroupAssociation',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an association. See https://google.aip.dev/135.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroupAssociations/{mirroringEndpointGroupAssociationsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.mirroringEndpointGroupAssociations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a specific association. See https://google.aip.dev/131.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MirroringEndpointGroupAssociation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroupAssociations/{mirroringEndpointGroupAssociationsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.mirroringEndpointGroupAssociations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsGetRequest',
        response_type_name='MirroringEndpointGroupAssociation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists associations in a given project and location. See https://google.aip.dev/132.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMirroringEndpointGroupAssociationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroupAssociations',
        http_method='GET',
        method_id='networksecurity.projects.locations.mirroringEndpointGroupAssociations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/mirroringEndpointGroupAssociations',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsListRequest',
        response_type_name='ListMirroringEndpointGroupAssociationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an association. See https://google.aip.dev/134.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroupAssociations/{mirroringEndpointGroupAssociationsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.mirroringEndpointGroupAssociations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='mirroringEndpointGroupAssociation',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointGroupAssociationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMirroringEndpointGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_mirroringEndpointGroups resource."""

    _NAME = 'projects_locations_mirroringEndpointGroups'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsMirroringEndpointGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an endpoint group in a given project and location. See https://google.aip.dev/133.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroups',
        http_method='POST',
        method_id='networksecurity.projects.locations.mirroringEndpointGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['mirroringEndpointGroupId', 'requestId'],
        relative_path='v1alpha1/{+parent}/mirroringEndpointGroups',
        request_field='mirroringEndpointGroup',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an endpoint group. See https://google.aip.dev/135.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroups/{mirroringEndpointGroupsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.mirroringEndpointGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a specific endpoint group. See https://google.aip.dev/131.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MirroringEndpointGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroups/{mirroringEndpointGroupsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.mirroringEndpointGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointGroupsGetRequest',
        response_type_name='MirroringEndpointGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists endpoint groups in a given project and location. See https://google.aip.dev/132.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMirroringEndpointGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroups',
        http_method='GET',
        method_id='networksecurity.projects.locations.mirroringEndpointGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/mirroringEndpointGroups',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointGroupsListRequest',
        response_type_name='ListMirroringEndpointGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an endpoint group. See https://google.aip.dev/134.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroups/{mirroringEndpointGroupsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.mirroringEndpointGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='mirroringEndpointGroup',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMirroringEndpointsService(base_api.BaseApiService):
    """Service class for the projects_locations_mirroringEndpoints resource."""

    _NAME = 'projects_locations_mirroringEndpoints'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsMirroringEndpointsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an endpoint in a given project and location. See https://google.aip.dev/133.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpoints',
        http_method='POST',
        method_id='networksecurity.projects.locations.mirroringEndpoints.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['mirroringEndpointId', 'requestId'],
        relative_path='v1alpha1/{+parent}/mirroringEndpoints',
        request_field='mirroringEndpoint',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an endpoint. See https://google.aip.dev/135.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpoints/{mirroringEndpointsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.mirroringEndpoints.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a specific endpoint. See https://google.aip.dev/131.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MirroringEndpoint) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpoints/{mirroringEndpointsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.mirroringEndpoints.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointsGetRequest',
        response_type_name='MirroringEndpoint',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists endpoints in a given project and location. See https://google.aip.dev/132.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMirroringEndpointsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpoints',
        http_method='GET',
        method_id='networksecurity.projects.locations.mirroringEndpoints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/mirroringEndpoints',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointsListRequest',
        response_type_name='ListMirroringEndpointsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an endpoint group. See https://google.aip.dev/134.

      Args:
        request: (NetworksecurityProjectsLocationsMirroringEndpointsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/mirroringEndpoints/{mirroringEndpointsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.mirroringEndpoints.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='mirroringEndpoint',
        request_type_name='NetworksecurityProjectsLocationsMirroringEndpointsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (NetworksecurityProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='networksecurity.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='NetworksecurityProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (NetworksecurityProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (NetworksecurityProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (NetworksecurityProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='networksecurity.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsPartnerSSEEnvironmentsService(base_api.BaseApiService):
    """Service class for the projects_locations_partnerSSEEnvironments resource."""

    _NAME = 'projects_locations_partnerSSEEnvironments'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsPartnerSSEEnvironmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddDNSPeeringZone(self, request, global_params=None):
      r"""Add DNS Peering Zone.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEEnvironmentsAddDNSPeeringZoneRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AddDNSPeeringZone')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddDNSPeeringZone.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEEnvironments/{partnerSSEEnvironmentsId}:addDNSPeeringZone',
        http_method='POST',
        method_id='networksecurity.projects.locations.partnerSSEEnvironments.addDNSPeeringZone',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:addDNSPeeringZone',
        request_field='addDNSPeeringZoneRequest',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEEnvironmentsAddDNSPeeringZoneRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new PartnerSSEEnvironment in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEEnvironmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEEnvironments',
        http_method='POST',
        method_id='networksecurity.projects.locations.partnerSSEEnvironments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['partnerSseEnvironmentId', 'requestId'],
        relative_path='v1alpha1/{+parent}/partnerSSEEnvironments',
        request_field='partnerSSEEnvironment',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEEnvironmentsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single PartnerSSEEnvironment.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEEnvironmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEEnvironments/{partnerSSEEnvironmentsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.partnerSSEEnvironments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEEnvironmentsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single PartnerSSEEnvironment.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEEnvironmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PartnerSSEEnvironment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEEnvironments/{partnerSSEEnvironmentsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.partnerSSEEnvironments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEEnvironmentsGetRequest',
        response_type_name='PartnerSSEEnvironment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists PartnerSSEEnvironments in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEEnvironmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPartnerSSEEnvironmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEEnvironments',
        http_method='GET',
        method_id='networksecurity.projects.locations.partnerSSEEnvironments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/partnerSSEEnvironments',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEEnvironmentsListRequest',
        response_type_name='ListPartnerSSEEnvironmentsResponse',
        supports_download=False,
    )

    def RemoveDNSPeeringZone(self, request, global_params=None):
      r"""Remove DNS Peering Zone.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEEnvironmentsRemoveDNSPeeringZoneRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RemoveDNSPeeringZone')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveDNSPeeringZone.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEEnvironments/{partnerSSEEnvironmentsId}:removeDNSPeeringZone',
        http_method='POST',
        method_id='networksecurity.projects.locations.partnerSSEEnvironments.removeDNSPeeringZone',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:removeDNSPeeringZone',
        request_field='removeDNSPeeringZoneRequest',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEEnvironmentsRemoveDNSPeeringZoneRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsPartnerSSEGatewaysService(base_api.BaseApiService):
    """Service class for the projects_locations_partnerSSEGateways resource."""

    _NAME = 'projects_locations_partnerSSEGateways'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsPartnerSSEGatewaysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new PartnerSSEGateway in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEGatewaysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEGateways',
        http_method='POST',
        method_id='networksecurity.projects.locations.partnerSSEGateways.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['partnerSseGatewayId', 'requestId'],
        relative_path='v1alpha1/{+parent}/partnerSSEGateways',
        request_field='partnerSSEGateway',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEGatewaysCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single PartnerSSEGateway.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEGatewaysDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEGateways/{partnerSSEGatewaysId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.partnerSSEGateways.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEGatewaysDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single PartnerSSEGateway.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEGatewaysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PartnerSSEGateway) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEGateways/{partnerSSEGatewaysId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.partnerSSEGateways.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEGatewaysGetRequest',
        response_type_name='PartnerSSEGateway',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists PartnerSSEGateways in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEGatewaysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPartnerSSEGatewaysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEGateways',
        http_method='GET',
        method_id='networksecurity.projects.locations.partnerSSEGateways.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/partnerSSEGateways',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEGatewaysListRequest',
        response_type_name='ListPartnerSSEGatewaysResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a single PartnerSSEGateway.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEGatewaysPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEGateways/{partnerSSEGatewaysId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.partnerSSEGateways.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='partnerSSEGateway',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEGatewaysPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsPartnerSSERealmsService(base_api.BaseApiService):
    """Service class for the projects_locations_partnerSSERealms resource."""

    _NAME = 'projects_locations_partnerSSERealms'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsPartnerSSERealmsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new PartnerSSERealm in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSERealmsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSERealms',
        http_method='POST',
        method_id='networksecurity.projects.locations.partnerSSERealms.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['partnerSseRealmId', 'requestId'],
        relative_path='v1alpha1/{+parent}/partnerSSERealms',
        request_field='partnerSSERealm',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSERealmsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single PartnerSSERealm.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSERealmsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSERealms/{partnerSSERealmsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.partnerSSERealms.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSERealmsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single PartnerSSERealm.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSERealmsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PartnerSSERealm) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSERealms/{partnerSSERealmsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.partnerSSERealms.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSERealmsGetRequest',
        response_type_name='PartnerSSERealm',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists PartnerSSERealms in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSERealmsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPartnerSSERealmsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSERealms',
        http_method='GET',
        method_id='networksecurity.projects.locations.partnerSSERealms.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/partnerSSERealms',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSERealmsListRequest',
        response_type_name='ListPartnerSSERealmsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSacAttachmentsService(base_api.BaseApiService):
    """Service class for the projects_locations_sacAttachments resource."""

    _NAME = 'projects_locations_sacAttachments'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsSacAttachmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SACAttachment in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSacAttachmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sacAttachments',
        http_method='POST',
        method_id='networksecurity.projects.locations.sacAttachments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'sacAttachmentId'],
        relative_path='v1alpha1/{+parent}/sacAttachments',
        request_field='sACAttachment',
        request_type_name='NetworksecurityProjectsLocationsSacAttachmentsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified attachment.

      Args:
        request: (NetworksecurityProjectsLocationsSacAttachmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sacAttachments/{sacAttachmentsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.sacAttachments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSacAttachmentsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the specified attachment.

      Args:
        request: (NetworksecurityProjectsLocationsSacAttachmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SACAttachment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sacAttachments/{sacAttachmentsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.sacAttachments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSacAttachmentsGetRequest',
        response_type_name='SACAttachment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SACAttachments in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSacAttachmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSACAttachmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sacAttachments',
        http_method='GET',
        method_id='networksecurity.projects.locations.sacAttachments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/sacAttachments',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSacAttachmentsListRequest',
        response_type_name='ListSACAttachmentsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSacRealmsService(base_api.BaseApiService):
    """Service class for the projects_locations_sacRealms resource."""

    _NAME = 'projects_locations_sacRealms'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsSacRealmsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SACRealm in a given project.

      Args:
        request: (NetworksecurityProjectsLocationsSacRealmsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sacRealms',
        http_method='POST',
        method_id='networksecurity.projects.locations.sacRealms.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'sacRealmId'],
        relative_path='v1alpha1/{+parent}/sacRealms',
        request_field='sACRealm',
        request_type_name='NetworksecurityProjectsLocationsSacRealmsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified realm.

      Args:
        request: (NetworksecurityProjectsLocationsSacRealmsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sacRealms/{sacRealmsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.sacRealms.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSacRealmsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the specified realm.

      Args:
        request: (NetworksecurityProjectsLocationsSacRealmsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SACRealm) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sacRealms/{sacRealmsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.sacRealms.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSacRealmsGetRequest',
        response_type_name='SACRealm',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SACRealms in a given project.

      Args:
        request: (NetworksecurityProjectsLocationsSacRealmsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSACRealmsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sacRealms',
        http_method='GET',
        method_id='networksecurity.projects.locations.sacRealms.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/sacRealms',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSacRealmsListRequest',
        response_type_name='ListSACRealmsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSecurityProfileGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_securityProfileGroups resource."""

    _NAME = 'projects_locations_securityProfileGroups'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsSecurityProfileGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SecurityProfileGroup in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfileGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfileGroups',
        http_method='POST',
        method_id='networksecurity.projects.locations.securityProfileGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['securityProfileGroupId'],
        relative_path='v1alpha1/{+parent}/securityProfileGroups',
        request_field='securityProfileGroup',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfileGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single SecurityProfileGroup.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfileGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.securityProfileGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfileGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single SecurityProfileGroup.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfileGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityProfileGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.securityProfileGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfileGroupsGetRequest',
        response_type_name='SecurityProfileGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SecurityProfileGroups in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfileGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityProfileGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfileGroups',
        http_method='GET',
        method_id='networksecurity.projects.locations.securityProfileGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/securityProfileGroups',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfileGroupsListRequest',
        response_type_name='ListSecurityProfileGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single SecurityProfileGroup.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfileGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.securityProfileGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='securityProfileGroup',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfileGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsSecurityProfilesService(base_api.BaseApiService):
    """Service class for the projects_locations_securityProfiles resource."""

    _NAME = 'projects_locations_securityProfiles'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsSecurityProfilesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SecurityProfile in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfilesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfiles',
        http_method='POST',
        method_id='networksecurity.projects.locations.securityProfiles.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['securityProfileId'],
        relative_path='v1alpha1/{+parent}/securityProfiles',
        request_field='securityProfile',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfilesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single SecurityProfile.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfilesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.securityProfiles.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfilesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single SecurityProfile.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfilesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityProfile) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.securityProfiles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfilesGetRequest',
        response_type_name='SecurityProfile',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SecurityProfiles in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfilesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityProfilesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfiles',
        http_method='GET',
        method_id='networksecurity.projects.locations.securityProfiles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/securityProfiles',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfilesListRequest',
        response_type_name='ListSecurityProfilesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single SecurityProfile.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfilesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.securityProfiles.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='securityProfile',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfilesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsServerTlsPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_serverTlsPolicies resource."""

    _NAME = 'projects_locations_serverTlsPolicies'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsServerTlsPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ServerTlsPolicy in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies',
        http_method='POST',
        method_id='networksecurity.projects.locations.serverTlsPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['serverTlsPolicyId'],
        relative_path='v1alpha1/{+parent}/serverTlsPolicies',
        request_field='serverTlsPolicy',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ServerTlsPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.serverTlsPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ServerTlsPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServerTlsPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.serverTlsPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesGetRequest',
        response_type_name='ServerTlsPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}:getIamPolicy',
        http_method='GET',
        method_id='networksecurity.projects.locations.serverTlsPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ServerTlsPolicies in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServerTlsPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies',
        http_method='GET',
        method_id='networksecurity.projects.locations.serverTlsPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v1alpha1/{+parent}/serverTlsPolicies',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesListRequest',
        response_type_name='ListServerTlsPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single ServerTlsPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.serverTlsPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='serverTlsPolicy',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='networksecurity.projects.locations.serverTlsPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='networksecurity.projects.locations.serverTlsPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSseGatewayReferencesService(base_api.BaseApiService):
    """Service class for the projects_locations_sseGatewayReferences resource."""

    _NAME = 'projects_locations_sseGatewayReferences'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsSseGatewayReferencesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single SSEGatewayReference.

      Args:
        request: (NetworksecurityProjectsLocationsSseGatewayReferencesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SSEGatewayReference) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sseGatewayReferences/{sseGatewayReferencesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.sseGatewayReferences.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSseGatewayReferencesGetRequest',
        response_type_name='SSEGatewayReference',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SSEGatewayReferences in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSseGatewayReferencesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSSEGatewayReferencesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sseGatewayReferences',
        http_method='GET',
        method_id='networksecurity.projects.locations.sseGatewayReferences.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/sseGatewayReferences',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSseGatewayReferencesListRequest',
        response_type_name='ListSSEGatewayReferencesResponse',
        supports_download=False,
    )

  class ProjectsLocationsTlsInspectionPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_tlsInspectionPolicies resource."""

    _NAME = 'projects_locations_tlsInspectionPolicies'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsTlsInspectionPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new TlsInspectionPolicy in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsTlsInspectionPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies',
        http_method='POST',
        method_id='networksecurity.projects.locations.tlsInspectionPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tlsInspectionPolicyId'],
        relative_path='v1alpha1/{+parent}/tlsInspectionPolicies',
        request_field='tlsInspectionPolicy',
        request_type_name='NetworksecurityProjectsLocationsTlsInspectionPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single TlsInspectionPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsTlsInspectionPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies/{tlsInspectionPoliciesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.tlsInspectionPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsTlsInspectionPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single TlsInspectionPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsTlsInspectionPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TlsInspectionPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies/{tlsInspectionPoliciesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.tlsInspectionPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsTlsInspectionPoliciesGetRequest',
        response_type_name='TlsInspectionPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TlsInspectionPolicies in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsTlsInspectionPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTlsInspectionPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies',
        http_method='GET',
        method_id='networksecurity.projects.locations.tlsInspectionPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/tlsInspectionPolicies',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsTlsInspectionPoliciesListRequest',
        response_type_name='ListTlsInspectionPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single TlsInspectionPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsTlsInspectionPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies/{tlsInspectionPoliciesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.tlsInspectionPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='tlsInspectionPolicy',
        request_type_name='NetworksecurityProjectsLocationsTlsInspectionPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsUllMirroringCollectorsService(base_api.BaseApiService):
    """Service class for the projects_locations_ullMirroringCollectors resource."""

    _NAME = 'projects_locations_ullMirroringCollectors'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsUllMirroringCollectorsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new UllMirroringCollector in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsUllMirroringCollectorsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/ullMirroringCollectors',
        http_method='POST',
        method_id='networksecurity.projects.locations.ullMirroringCollectors.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'ullMirroringCollectorId'],
        relative_path='v1alpha1/{+parent}/ullMirroringCollectors',
        request_field='ullMirroringCollector',
        request_type_name='NetworksecurityProjectsLocationsUllMirroringCollectorsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single UllMirroringCollector.

      Args:
        request: (NetworksecurityProjectsLocationsUllMirroringCollectorsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/ullMirroringCollectors/{ullMirroringCollectorsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.ullMirroringCollectors.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUllMirroringCollectorsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single UllMirroringCollector.

      Args:
        request: (NetworksecurityProjectsLocationsUllMirroringCollectorsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UllMirroringCollector) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/ullMirroringCollectors/{ullMirroringCollectorsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.ullMirroringCollectors.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUllMirroringCollectorsGetRequest',
        response_type_name='UllMirroringCollector',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists UllMirroringCollectors in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsUllMirroringCollectorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUllMirroringCollectorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/ullMirroringCollectors',
        http_method='GET',
        method_id='networksecurity.projects.locations.ullMirroringCollectors.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/ullMirroringCollectors',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUllMirroringCollectorsListRequest',
        response_type_name='ListUllMirroringCollectorsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a single UllMirroringCollector.

      Args:
        request: (NetworksecurityProjectsLocationsUllMirroringCollectorsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/ullMirroringCollectors/{ullMirroringCollectorsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.ullMirroringCollectors.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='ullMirroringCollector',
        request_type_name='NetworksecurityProjectsLocationsUllMirroringCollectorsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsUllMirroringEnginesService(base_api.BaseApiService):
    """Service class for the projects_locations_ullMirroringEngines resource."""

    _NAME = 'projects_locations_ullMirroringEngines'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsUllMirroringEnginesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new UllMirroringEngine in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsUllMirroringEnginesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/ullMirroringEngines',
        http_method='POST',
        method_id='networksecurity.projects.locations.ullMirroringEngines.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'ullMirroringEngineId'],
        relative_path='v1alpha1/{+parent}/ullMirroringEngines',
        request_field='ullMirroringEngine',
        request_type_name='NetworksecurityProjectsLocationsUllMirroringEnginesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single UllMirroringEngine.

      Args:
        request: (NetworksecurityProjectsLocationsUllMirroringEnginesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/ullMirroringEngines/{ullMirroringEnginesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.ullMirroringEngines.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUllMirroringEnginesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""GetUllMirroringEngine gets a UllMirroringEngine resource.

      Args:
        request: (NetworksecurityProjectsLocationsUllMirroringEnginesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UllMirroringEngine) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/ullMirroringEngines/{ullMirroringEnginesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.ullMirroringEngines.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUllMirroringEnginesGetRequest',
        response_type_name='UllMirroringEngine',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists UllMirroringEngines in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsUllMirroringEnginesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUllMirroringEnginesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/ullMirroringEngines',
        http_method='GET',
        method_id='networksecurity.projects.locations.ullMirroringEngines.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/ullMirroringEngines',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUllMirroringEnginesListRequest',
        response_type_name='ListUllMirroringEnginesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a UllMirroringEngine resource.

      Args:
        request: (NetworksecurityProjectsLocationsUllMirroringEnginesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/ullMirroringEngines/{ullMirroringEnginesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.ullMirroringEngines.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='ullMirroringEngine',
        request_type_name='NetworksecurityProjectsLocationsUllMirroringEnginesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsUllMirroringInfrasUllMirroredNetworksService(base_api.BaseApiService):
    """Service class for the projects_locations_ullMirroringInfras_ullMirroredNetworks resource."""

    _NAME = 'projects_locations_ullMirroringInfras_ullMirroredNetworks'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsUllMirroringInfrasUllMirroredNetworksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new UllMirroredNetwork in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/ullMirroringInfras/{ullMirroringInfrasId}/ullMirroredNetworks',
        http_method='POST',
        method_id='networksecurity.projects.locations.ullMirroringInfras.ullMirroredNetworks.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'ullMirroredNetworkId'],
        relative_path='v1alpha1/{+parent}/ullMirroredNetworks',
        request_field='ullMirroredNetwork',
        request_type_name='NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single UllMirroredNetwork.

      Args:
        request: (NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/ullMirroringInfras/{ullMirroringInfrasId}/ullMirroredNetworks/{ullMirroredNetworksId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.ullMirroringInfras.ullMirroredNetworks.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single UllMirroredNetwork.

      Args:
        request: (NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UllMirroredNetwork) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/ullMirroringInfras/{ullMirroringInfrasId}/ullMirroredNetworks/{ullMirroredNetworksId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.ullMirroringInfras.ullMirroredNetworks.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksGetRequest',
        response_type_name='UllMirroredNetwork',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists UllMirroredNetworks in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUllMirroredNetworksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/ullMirroringInfras/{ullMirroringInfrasId}/ullMirroredNetworks',
        http_method='GET',
        method_id='networksecurity.projects.locations.ullMirroringInfras.ullMirroredNetworks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/ullMirroredNetworks',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUllMirroringInfrasUllMirroredNetworksListRequest',
        response_type_name='ListUllMirroredNetworksResponse',
        supports_download=False,
    )

  class ProjectsLocationsUllMirroringInfrasService(base_api.BaseApiService):
    """Service class for the projects_locations_ullMirroringInfras resource."""

    _NAME = 'projects_locations_ullMirroringInfras'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsUllMirroringInfrasService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsUrlListsService(base_api.BaseApiService):
    """Service class for the projects_locations_urlLists resource."""

    _NAME = 'projects_locations_urlLists'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsUrlListsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new UrlList in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsUrlListsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/urlLists',
        http_method='POST',
        method_id='networksecurity.projects.locations.urlLists.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['urlListId'],
        relative_path='v1alpha1/{+parent}/urlLists',
        request_field='urlList',
        request_type_name='NetworksecurityProjectsLocationsUrlListsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single UrlList.

      Args:
        request: (NetworksecurityProjectsLocationsUrlListsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/urlLists/{urlListsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.urlLists.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUrlListsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single UrlList.

      Args:
        request: (NetworksecurityProjectsLocationsUrlListsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UrlList) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/urlLists/{urlListsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.urlLists.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUrlListsGetRequest',
        response_type_name='UrlList',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists UrlLists in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsUrlListsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUrlListsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/urlLists',
        http_method='GET',
        method_id='networksecurity.projects.locations.urlLists.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/urlLists',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUrlListsListRequest',
        response_type_name='ListUrlListsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single UrlList.

      Args:
        request: (NetworksecurityProjectsLocationsUrlListsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/urlLists/{urlListsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.urlLists.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='urlList',
        request_type_name='NetworksecurityProjectsLocationsUrlListsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (NetworksecurityProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (NetworksecurityProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='networksecurity.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/locations',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
