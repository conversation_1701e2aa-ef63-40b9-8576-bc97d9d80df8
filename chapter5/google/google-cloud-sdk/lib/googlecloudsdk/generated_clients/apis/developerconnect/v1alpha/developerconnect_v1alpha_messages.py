"""Generated message classes for developerconnect version v1alpha.

Connect third-party source code management to Google
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'developerconnect'


class AccountConnector(_messages.Message):
  r"""AccountConnector encapsulates what a platform administrator needs to
  configure for users to connect to the service providers, which includes,
  among other fields, the OAuth client ID, client secret, and authorization
  and token endpoints.

  Messages:
    AnnotationsValue: Optional. Allows users to store small amounts of
      arbitrary data.
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    annotations: Optional. Allows users to store small amounts of arbitrary
      data.
    createTime: Output only. The timestamp when the accountConnector was
      created.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    labels: Optional. Labels as key value pairs
    name: Identifier. The resource name of the accountConnector, in the format
      `projects/{project}/locations/{location}/accountConnectors/{account_conn
      ector_id}`.
    oauthStartUri: Output only. Start OAuth flow by clicking on this URL.
    providerOauthConfig: Provider OAuth config.
    updateTime: Output only. The timestamp when the accountConnector was
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Allows users to store small amounts of arbitrary data.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  etag = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  oauthStartUri = _messages.StringField(6)
  providerOauthConfig = _messages.MessageField('ProviderOAuthConfig', 7)
  updateTime = _messages.StringField(8)


class BitbucketCloudConfig(_messages.Message):
  r"""Configuration for connections to an instance of Bitbucket Cloud.

  Fields:
    authorizerCredential: Required. An access token with the minimum
      `repository`, `pullrequest` and `webhook` scope access. It can either be
      a workspace, project or repository access token. This is needed to
      create webhooks. It's recommended to use a system account to generate
      these credentials.
    readAuthorizerCredential: Required. An access token with the minimum
      `repository` access. It can either be a workspace, project or repository
      access token. It's recommended to use a system account to generate the
      credentials.
    webhookSecretSecretVersion: Required. Immutable. SecretManager resource
      containing the webhook secret used to verify webhook events, formatted
      as `projects/*/secrets/*/versions/*`. This is used to validate and
      create webhooks.
    workspace: Required. The Bitbucket Cloud Workspace ID to be connected to
      Google Cloud Platform.
  """

  authorizerCredential = _messages.MessageField('UserCredential', 1)
  readAuthorizerCredential = _messages.MessageField('UserCredential', 2)
  webhookSecretSecretVersion = _messages.StringField(3)
  workspace = _messages.StringField(4)


class BitbucketDataCenterConfig(_messages.Message):
  r"""Configuration for connections to an instance of Bitbucket Data Center.

  Fields:
    authorizerCredential: Required. An http access token with the minimum
      `Repository admin` scope access. This is needed to create webhooks. It's
      recommended to use a system account to generate these credentials.
    hostUri: Required. The URI of the Bitbucket Data Center host this
      connection is for.
    readAuthorizerCredential: Required. An http access token with the minimum
      `Repository read` access. It's recommended to use a system account to
      generate the credentials.
    serverVersion: Output only. Version of the Bitbucket Data Center server
      running on the `host_uri`.
    serviceDirectoryConfig: Optional. Configuration for using Service
      Directory to privately connect to a Bitbucket Data Center instance. This
      should only be set if the Bitbucket Data Center is hosted on-premises
      and not reachable by public internet. If this field is left empty, calls
      to the Bitbucket Data Center will be made over the public internet.
    sslCaCertificate: Optional. SSL certificate authority to trust when making
      requests to Bitbucket Data Center.
    webhookSecretSecretVersion: Required. Immutable. SecretManager resource
      containing the webhook secret used to verify webhook events, formatted
      as `projects/*/secrets/*/versions/*`. This is used to validate webhooks.
  """

  authorizerCredential = _messages.MessageField('UserCredential', 1)
  hostUri = _messages.StringField(2)
  readAuthorizerCredential = _messages.MessageField('UserCredential', 3)
  serverVersion = _messages.StringField(4)
  serviceDirectoryConfig = _messages.MessageField('ServiceDirectoryConfig', 5)
  sslCaCertificate = _messages.StringField(6)
  webhookSecretSecretVersion = _messages.StringField(7)


class Connection(_messages.Message):
  r"""Message describing Connection object

  Messages:
    AnnotationsValue: Optional. Allows clients to store small amounts of
      arbitrary data.
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    annotations: Optional. Allows clients to store small amounts of arbitrary
      data.
    bitbucketCloudConfig: Configuration for connections to an instance of
      Bitbucket Clouds.
    bitbucketDataCenterConfig: Configuration for connections to an instance of
      Bitbucket Data Center.
    createTime: Output only. [Output only] Create timestamp
    cryptoKeyConfig: Optional. The crypto key configuration. This field is
      used by the Customer-Managed Encryption Keys (CMEK) feature.
    deleteTime: Output only. [Output only] Delete timestamp
    disabled: Optional. If disabled is set to true, functionality is disabled
      for this connection. Repository based API methods and webhooks
      processing for repositories in this connection will be disabled.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    gitProxyConfig: Optional. Configuration for the git proxy feature.
      Enabling the git proxy allows clients to perform git operations on the
      repositories linked in the connection.
    githubConfig: Configuration for connections to github.com.
    githubEnterpriseConfig: Configuration for connections to an instance of
      GitHub Enterprise.
    gitlabConfig: Configuration for connections to gitlab.com.
    gitlabEnterpriseConfig: Configuration for connections to an instance of
      GitLab Enterprise.
    installationState: Output only. Installation state of the Connection.
    labels: Optional. Labels as key value pairs
    name: Identifier. The resource name of the connection, in the format
      `projects/{project}/locations/{location}/connections/{connection_id}`.
    reconciling: Output only. Set to true when the connection is being set up
      or updated in the background.
    uid: Output only. A system-assigned unique identifier for the Connection.
    updateTime: Output only. [Output only] Update timestamp
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Allows clients to store small amounts of arbitrary data.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  bitbucketCloudConfig = _messages.MessageField('BitbucketCloudConfig', 2)
  bitbucketDataCenterConfig = _messages.MessageField('BitbucketDataCenterConfig', 3)
  createTime = _messages.StringField(4)
  cryptoKeyConfig = _messages.MessageField('CryptoKeyConfig', 5)
  deleteTime = _messages.StringField(6)
  disabled = _messages.BooleanField(7)
  etag = _messages.StringField(8)
  gitProxyConfig = _messages.MessageField('GitProxyConfig', 9)
  githubConfig = _messages.MessageField('GitHubConfig', 10)
  githubEnterpriseConfig = _messages.MessageField('GitHubEnterpriseConfig', 11)
  gitlabConfig = _messages.MessageField('GitLabConfig', 12)
  gitlabEnterpriseConfig = _messages.MessageField('GitLabEnterpriseConfig', 13)
  installationState = _messages.MessageField('InstallationState', 14)
  labels = _messages.MessageField('LabelsValue', 15)
  name = _messages.StringField(16)
  reconciling = _messages.BooleanField(17)
  uid = _messages.StringField(18)
  updateTime = _messages.StringField(19)


class CryptoKeyConfig(_messages.Message):
  r"""The crypto key configuration. This field is used by the Customer-managed
  encryption keys (CMEK) feature.

  Fields:
    keyReference: Required. The name of the key which is used to
      encrypt/decrypt customer data. For key in Cloud KMS, the key should be
      in the format of `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
  """

  keyReference = _messages.StringField(1)


class DeveloperconnectProjectsLocationsAccountConnectorsCreateRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsAccountConnectorsCreateRequest
  object.

  Fields:
    accountConnector: A AccountConnector resource to be passed as the request
      body.
    accountConnectorId: Required. The ID to use for the AccountConnector,
      which will become the final component of the AccountConnector's resource
      name. Its format should adhere to https://google.aip.dev/122#resource-
      id-segments Names must be unique per-project per-location.
    parent: Required. Location resource name as the account_connector's
      parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (********-0000-0000-0000-************).
    validateOnly: Optional. If set, validate the request, but do not actually
      post it.
  """

  accountConnector = _messages.MessageField('AccountConnector', 1)
  accountConnectorId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class DeveloperconnectProjectsLocationsAccountConnectorsDeleteRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsAccountConnectorsDeleteRequest
  object.

  Fields:
    etag: Optional. The current etag of the AccountConnectorn. If an etag is
      provided and does not match the current etag of the AccountConnector,
      deletion will be blocked and an ABORTED error will be returned.
    force: Optional. If set to true, any Users from this AccountConnector will
      also be deleted. (Otherwise, the request will only work if the
      AccountConnector has no Users.)
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (********-0000-0000-0000-************).
    validateOnly: Optional. If set, validate the request, but do not actually
      post it.
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class DeveloperconnectProjectsLocationsAccountConnectorsGetRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsAccountConnectorsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class DeveloperconnectProjectsLocationsAccountConnectorsListRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsAccountConnectorsListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListAccountConnectorsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DeveloperconnectProjectsLocationsAccountConnectorsPatchRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsAccountConnectorsPatchRequest object.

  Fields:
    accountConnector: A AccountConnector resource to be passed as the request
      body.
    allowMissing: Optional. If set to true, and the accountConnector is not
      found a new accountConnector will be created. In this situation
      `update_mask` is ignored. The creation will succeed only if the input
      accountConnector has all the necessary
    name: Identifier. The resource name of the accountConnector, in the format
      `projects/{project}/locations/{location}/accountConnectors/{account_conn
      ector_id}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (********-0000-0000-0000-************).
    updateMask: Optional. The list of fields to be updated.
    validateOnly: Optional. If set, validate the request, but do not actually
      post it.
  """

  accountConnector = _messages.MessageField('AccountConnector', 1)
  allowMissing = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class DeveloperconnectProjectsLocationsAccountConnectorsUsersDeleteRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsAccountConnectorsUsersDeleteRequest
  object.

  Fields:
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (********-0000-0000-0000-************).
    validateOnly: Optional. If set, validate the request, but do not actually
      post it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DeveloperconnectProjectsLocationsAccountConnectorsUsersDeleteSelfRequest(_messages.Message):
  r"""A
  DeveloperconnectProjectsLocationsAccountConnectorsUsersDeleteSelfRequest
  object.

  Fields:
    name: Required. Name of the AccountConnector resource
  """

  name = _messages.StringField(1, required=True)


class DeveloperconnectProjectsLocationsAccountConnectorsUsersFetchAccessTokenRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsAccountConnectorsUsersFetchAccessToke
  nRequest object.

  Fields:
    accountConnector: Required. The resource name of the AccountConnector in
      the format `projects/*/locations/*/accountConnectors/*`.
    fetchAccessTokenRequest: A FetchAccessTokenRequest resource to be passed
      as the request body.
  """

  accountConnector = _messages.StringField(1, required=True)
  fetchAccessTokenRequest = _messages.MessageField('FetchAccessTokenRequest', 2)


class DeveloperconnectProjectsLocationsAccountConnectorsUsersFetchSelfRequest(_messages.Message):
  r"""A
  DeveloperconnectProjectsLocationsAccountConnectorsUsersFetchSelfRequest
  object.

  Fields:
    name: Required. Name of the AccountConnector resource
  """

  name = _messages.StringField(1, required=True)


class DeveloperconnectProjectsLocationsAccountConnectorsUsersListRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsAccountConnectorsUsersListRequest
  object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListUsersRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DeveloperconnectProjectsLocationsConnectionsCreateRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsConnectionsCreateRequest object.

  Fields:
    connection: A Connection resource to be passed as the request body.
    connectionId: Required. Id of the requesting object If auto-generating Id
      server-side, remove this field and connection_id from the
      method_signature of Create RPC
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (********-0000-0000-0000-************).
    validateOnly: Optional. If set, validate the request, but do not actually
      post it.
  """

  connection = _messages.MessageField('Connection', 1)
  connectionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class DeveloperconnectProjectsLocationsConnectionsDeleteRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsConnectionsDeleteRequest object.

  Fields:
    etag: Optional. The current etag of the Connection. If an etag is provided
      and does not match the current etag of the Connection, deletion will be
      blocked and an ABORTED error will be returned.
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (********-0000-0000-0000-************).
    validateOnly: Optional. If set, validate the request, but do not actually
      post it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DeveloperconnectProjectsLocationsConnectionsFetchGitHubInstallationsRequest(_messages.Message):
  r"""A
  DeveloperconnectProjectsLocationsConnectionsFetchGitHubInstallationsRequest
  object.

  Fields:
    connection: Required. The resource name of the connection in the format
      `projects/*/locations/*/connections/*`.
  """

  connection = _messages.StringField(1, required=True)


class DeveloperconnectProjectsLocationsConnectionsFetchLinkableGitRepositoriesRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsConnectionsFetchLinkableGitRepositori
  esRequest object.

  Fields:
    connection: Required. The name of the Connection. Format:
      `projects/*/locations/*/connections/*`.
    pageSize: Optional. Number of results to return in the list. Defaults to
      20.
    pageToken: Optional. Page start.
  """

  connection = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class DeveloperconnectProjectsLocationsConnectionsGetRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsConnectionsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksCreateRequest(_messages.Message):
  r"""A
  DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksCreateRequest
  object.

  Fields:
    gitRepositoryLink: A GitRepositoryLink resource to be passed as the
      request body.
    gitRepositoryLinkId: Required. The ID to use for the repository, which
      will become the final component of the repository's resource name. This
      ID should be unique in the connection. Allows alphanumeric characters
      and any of -._~%!$&'()*+,;=@.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (********-0000-0000-0000-************).
    validateOnly: Optional. If set, validate the request, but do not actually
      post it.
  """

  gitRepositoryLink = _messages.MessageField('GitRepositoryLink', 1)
  gitRepositoryLinkId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksDeleteRequest(_messages.Message):
  r"""A
  DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksDeleteRequest
  object.

  Fields:
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (********-0000-0000-0000-************).
    validateOnly: Optional. If set, validate the request, but do not actually
      post it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksFetchGitRefsRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksFetchGit
  RefsRequest object.

  Enums:
    RefTypeValueValuesEnum: Required. Type of refs to fetch.

  Fields:
    gitRepositoryLink: Required. The resource name of GitRepositoryLink in the
      format `projects/*/locations/*/connections/*/gitRepositoryLinks/*`.
    pageSize: Optional. Number of results to return in the list. Default to
      20.
    pageToken: Optional. Page start.
    refType: Required. Type of refs to fetch.
  """

  class RefTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of refs to fetch.

    Values:
      REF_TYPE_UNSPECIFIED: No type specified.
      TAG: To fetch tags.
      BRANCH: To fetch branches.
    """
    REF_TYPE_UNSPECIFIED = 0
    TAG = 1
    BRANCH = 2

  gitRepositoryLink = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  refType = _messages.EnumField('RefTypeValueValuesEnum', 4)


class DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksFetchReadTokenRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksFetchRea
  dTokenRequest object.

  Fields:
    fetchReadTokenRequest: A FetchReadTokenRequest resource to be passed as
      the request body.
    gitRepositoryLink: Required. The resource name of the gitRepositoryLink in
      the format `projects/*/locations/*/connections/*/gitRepositoryLinks/*`.
  """

  fetchReadTokenRequest = _messages.MessageField('FetchReadTokenRequest', 1)
  gitRepositoryLink = _messages.StringField(2, required=True)


class DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksFetchReadWriteTokenRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksFetchRea
  dWriteTokenRequest object.

  Fields:
    fetchReadWriteTokenRequest: A FetchReadWriteTokenRequest resource to be
      passed as the request body.
    gitRepositoryLink: Required. The resource name of the gitRepositoryLink in
      the format `projects/*/locations/*/connections/*/gitRepositoryLinks/*`.
  """

  fetchReadWriteTokenRequest = _messages.MessageField('FetchReadWriteTokenRequest', 1)
  gitRepositoryLink = _messages.StringField(2, required=True)


class DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksGetRequest(_messages.Message):
  r"""A
  DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksGetRequest
  object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksListRequest(_messages.Message):
  r"""A
  DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksListRequest
  object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListGitRepositoryLinksRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksProcessBitbucketCloudWebhookRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksProcessB
  itbucketCloudWebhookRequest object.

  Fields:
    name: Required. The GitRepositoryLink where the webhook will be received.
      Format: `projects/*/locations/*/connections/*/gitRepositoryLinks/*`.
    processBitbucketCloudWebhookRequest: A ProcessBitbucketCloudWebhookRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  processBitbucketCloudWebhookRequest = _messages.MessageField('ProcessBitbucketCloudWebhookRequest', 2)


class DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksProcessBitbucketDataCenterWebhookRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksProcessB
  itbucketDataCenterWebhookRequest object.

  Fields:
    name: Required. The GitRepositoryLink where the webhook will be received.
      Format: `projects/*/locations/*/connections/*/gitRepositoryLinks/*`.
    processBitbucketDataCenterWebhookRequest: A
      ProcessBitbucketDataCenterWebhookRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  processBitbucketDataCenterWebhookRequest = _messages.MessageField('ProcessBitbucketDataCenterWebhookRequest', 2)


class DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksProcessGitLabEnterpriseWebhookRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksProcessG
  itLabEnterpriseWebhookRequest object.

  Fields:
    name: Required. The GitRepositoryLink resource where the webhook will be
      received. Format:
      `projects/*/locations/*/connections/*/gitRepositoryLinks/*`.
    processGitLabEnterpriseWebhookRequest: A
      ProcessGitLabEnterpriseWebhookRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  processGitLabEnterpriseWebhookRequest = _messages.MessageField('ProcessGitLabEnterpriseWebhookRequest', 2)


class DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksProcessGitLabWebhookRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsConnectionsGitRepositoryLinksProcessG
  itLabWebhookRequest object.

  Fields:
    name: Required. The GitRepositoryLink resource where the webhook will be
      received. Format:
      `projects/*/locations/*/connections/*/gitRepositoryLinks/*`.
    processGitLabWebhookRequest: A ProcessGitLabWebhookRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  processGitLabWebhookRequest = _messages.MessageField('ProcessGitLabWebhookRequest', 2)


class DeveloperconnectProjectsLocationsConnectionsListRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsConnectionsListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListConnectionsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DeveloperconnectProjectsLocationsConnectionsPatchRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsConnectionsPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the connection is not found a
      new connection will be created. In this situation `update_mask` is
      ignored. The creation will succeed only if the input connection has all
      the necessary information (e.g a github_config with both
      user_oauth_token and installation_id properties).
    connection: A Connection resource to be passed as the request body.
    name: Identifier. The resource name of the connection, in the format
      `projects/{project}/locations/{location}/connections/{connection_id}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (********-0000-0000-0000-************).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Connection resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set, validate the request, but do not actually
      post it.
  """

  allowMissing = _messages.BooleanField(1)
  connection = _messages.MessageField('Connection', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class DeveloperconnectProjectsLocationsConnectionsProcessGitHubEnterpriseWebhookRequest(_messages.Message):
  r"""A DeveloperconnectProjectsLocationsConnectionsProcessGitHubEnterpriseWeb
  hookRequest object.

  Fields:
    parent: Required. Project and location where the webhook will be received.
      Format: `projects/*/locations/*`.
    processGitHubEnterpriseWebhookRequest: A
      ProcessGitHubEnterpriseWebhookRequest resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  processGitHubEnterpriseWebhookRequest = _messages.MessageField('ProcessGitHubEnterpriseWebhookRequest', 2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ExchangeError(_messages.Message):
  r"""Message for representing an error from exchanging OAuth tokens.

  Fields:
    code: https://datatracker.ietf.org/doc/html/rfc6749#section-5.2 - error
    description: https://datatracker.ietf.org/doc/html/rfc6749#section-5.2 -
      error_description
  """

  code = _messages.StringField(1)
  description = _messages.StringField(2)


class FetchAccessTokenRequest(_messages.Message):
  r"""Message for fetching an OAuth access token."""


class FetchAccessTokenResponse(_messages.Message):
  r"""Message for responding to getting an OAuth access token.

  Fields:
    exchangeError: The error resulted from exchanging OAuth tokens from the
      service provider.
    expirationTime: Expiration timestamp. Can be empty if unknown or non-
      expiring.
    scopes: The scopes of the access token.
    token: The token content.
  """

  exchangeError = _messages.MessageField('ExchangeError', 1)
  expirationTime = _messages.StringField(2)
  scopes = _messages.StringField(3, repeated=True)
  token = _messages.StringField(4)


class FetchGitHubInstallationsResponse(_messages.Message):
  r"""Response of fetching github installations.

  Fields:
    installations: List of installations available to the OAuth user (for
      github.com) or all the installations (for GitHub enterprise).
  """

  installations = _messages.MessageField('Installation', 1, repeated=True)


class FetchGitRefsResponse(_messages.Message):
  r"""Response for fetching git refs.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    refNames: Name of the refs fetched.
  """

  nextPageToken = _messages.StringField(1)
  refNames = _messages.StringField(2, repeated=True)


class FetchLinkableGitRepositoriesResponse(_messages.Message):
  r"""Response message for FetchLinkableGitRepositories.

  Fields:
    linkableGitRepositories: The git repositories that can be linked to the
      connection.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  linkableGitRepositories = _messages.MessageField('LinkableGitRepository', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class FetchReadTokenRequest(_messages.Message):
  r"""Message for fetching SCM read token."""


class FetchReadTokenResponse(_messages.Message):
  r"""Message for responding to get read token.

  Fields:
    expirationTime: Expiration timestamp. Can be empty if unknown or non-
      expiring.
    gitUsername: The git_username to specify when making a git clone with the
      token. For example, for GitHub GitRepositoryLinks, this would be
      "x-access-token"
    token: The token content.
  """

  expirationTime = _messages.StringField(1)
  gitUsername = _messages.StringField(2)
  token = _messages.StringField(3)


class FetchReadWriteTokenRequest(_messages.Message):
  r"""Message for fetching SCM read/write token."""


class FetchReadWriteTokenResponse(_messages.Message):
  r"""Message for responding to get read/write token.

  Fields:
    expirationTime: Expiration timestamp. Can be empty if unknown or non-
      expiring.
    gitUsername: The git_username to specify when making a git clone with the
      token. For example, for GitHub GitRepositoryLinks, this would be
      "x-access-token"
    token: The token content.
  """

  expirationTime = _messages.StringField(1)
  gitUsername = _messages.StringField(2)
  token = _messages.StringField(3)


class GitHubConfig(_messages.Message):
  r"""Configuration for connections to github.com.

  Enums:
    GithubAppValueValuesEnum: Required. Immutable. The GitHub Application that
      was installed to the GitHub user or organization.

  Fields:
    appInstallationId: Optional. GitHub App installation id.
    authorizerCredential: Optional. OAuth credential of the account that
      authorized the GitHub App. It is recommended to use a robot account
      instead of a human user account. The OAuth token must be tied to the
      GitHub App of this config.
    githubApp: Required. Immutable. The GitHub Application that was installed
      to the GitHub user or organization.
    installationUri: Output only. The URI to navigate to in order to manage
      the installation associated with this GitHubConfig.
  """

  class GithubAppValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The GitHub Application that was installed to the
    GitHub user or organization.

    Values:
      GIT_HUB_APP_UNSPECIFIED: GitHub App not specified.
      DEVELOPER_CONNECT: The Developer Connect GitHub Application.
      FIREBASE: The Firebase GitHub Application.
    """
    GIT_HUB_APP_UNSPECIFIED = 0
    DEVELOPER_CONNECT = 1
    FIREBASE = 2

  appInstallationId = _messages.IntegerField(1)
  authorizerCredential = _messages.MessageField('OAuthCredential', 2)
  githubApp = _messages.EnumField('GithubAppValueValuesEnum', 3)
  installationUri = _messages.StringField(4)


class GitHubEnterpriseConfig(_messages.Message):
  r"""Configuration for connections to an instance of GitHub Enterprise.

  Fields:
    appId: Optional. ID of the GitHub App created from the manifest.
    appInstallationId: Optional. ID of the installation of the GitHub App.
    appSlug: Output only. The URL-friendly name of the GitHub App.
    hostUri: Required. The URI of the GitHub Enterprise host this connection
      is for.
    installationUri: Output only. The URI to navigate to in order to manage
      the installation associated with this GitHubEnterpriseConfig.
    privateKeySecretVersion: Optional. SecretManager resource containing the
      private key of the GitHub App, formatted as
      `projects/*/secrets/*/versions/*`.
    serverVersion: Output only. GitHub Enterprise version installed at the
      host_uri.
    serviceDirectoryConfig: Optional. Configuration for using Service
      Directory to privately connect to a GitHub Enterprise server. This
      should only be set if the GitHub Enterprise server is hosted on-premises
      and not reachable by public internet. If this field is left empty, calls
      to the GitHub Enterprise server will be made over the public internet.
    sslCaCertificate: Optional. SSL certificate to use for requests to GitHub
      Enterprise.
    webhookSecretSecretVersion: Optional. SecretManager resource containing
      the webhook secret of the GitHub App, formatted as
      `projects/*/secrets/*/versions/*`.
  """

  appId = _messages.IntegerField(1)
  appInstallationId = _messages.IntegerField(2)
  appSlug = _messages.StringField(3)
  hostUri = _messages.StringField(4)
  installationUri = _messages.StringField(5)
  privateKeySecretVersion = _messages.StringField(6)
  serverVersion = _messages.StringField(7)
  serviceDirectoryConfig = _messages.MessageField('ServiceDirectoryConfig', 8)
  sslCaCertificate = _messages.StringField(9)
  webhookSecretSecretVersion = _messages.StringField(10)


class GitLabConfig(_messages.Message):
  r"""Configuration for connections to gitlab.com.

  Fields:
    authorizerCredential: Required. A GitLab personal access token with the
      minimum `api` scope access and a minimum role of `maintainer`. The
      GitLab Projects visible to this Personal Access Token will control which
      Projects Developer Connect has access to.
    readAuthorizerCredential: Required. A GitLab personal access token with
      the minimum `read_api` scope access and a minimum role of `reporter`.
      The GitLab Projects visible to this Personal Access Token will control
      which Projects Developer Connect has access to.
    webhookSecretSecretVersion: Required. Immutable. SecretManager resource
      containing the webhook secret of a GitLab project, formatted as
      `projects/*/secrets/*/versions/*`. This is used to validate webhooks.
  """

  authorizerCredential = _messages.MessageField('UserCredential', 1)
  readAuthorizerCredential = _messages.MessageField('UserCredential', 2)
  webhookSecretSecretVersion = _messages.StringField(3)


class GitLabEnterpriseConfig(_messages.Message):
  r"""Configuration for connections to an instance of GitLab Enterprise.

  Fields:
    authorizerCredential: Required. A GitLab personal access token with the
      minimum `api` scope access and a minimum role of `maintainer`. The
      GitLab Projects visible to this Personal Access Token will control which
      Projects Developer Connect has access to.
    hostUri: Required. The URI of the GitLab Enterprise host this connection
      is for.
    readAuthorizerCredential: Required. A GitLab personal access token with
      the minimum `read_api` scope access and a minimum role of `reporter`.
      The GitLab Projects visible to this Personal Access Token will control
      which Projects Developer Connect has access to.
    serverVersion: Output only. Version of the GitLab Enterprise server
      running on the `host_uri`.
    serviceDirectoryConfig: Optional. Configuration for using Service
      Directory to privately connect to a GitLab Enterprise instance. This
      should only be set if the GitLab Enterprise server is hosted on-premises
      and not reachable by public internet. If this field is left empty, calls
      to the GitLab Enterprise server will be made over the public internet.
    sslCaCertificate: Optional. SSL Certificate Authority certificate to use
      for requests to GitLab Enterprise instance.
    webhookSecretSecretVersion: Required. Immutable. SecretManager resource
      containing the webhook secret of a GitLab project, formatted as
      `projects/*/secrets/*/versions/*`. This is used to validate webhooks.
  """

  authorizerCredential = _messages.MessageField('UserCredential', 1)
  hostUri = _messages.StringField(2)
  readAuthorizerCredential = _messages.MessageField('UserCredential', 3)
  serverVersion = _messages.StringField(4)
  serviceDirectoryConfig = _messages.MessageField('ServiceDirectoryConfig', 5)
  sslCaCertificate = _messages.StringField(6)
  webhookSecretSecretVersion = _messages.StringField(7)


class GitProxyConfig(_messages.Message):
  r"""The git proxy configuration.

  Fields:
    enabled: Optional. Setting this to true allows the git proxy to be used
      for performing git operations on the repositories linked in the
      connection.
  """

  enabled = _messages.BooleanField(1)


class GitRepositoryLink(_messages.Message):
  r"""Message describing the GitRepositoryLink object

  Messages:
    AnnotationsValue: Optional. Allows clients to store small amounts of
      arbitrary data.
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    annotations: Optional. Allows clients to store small amounts of arbitrary
      data.
    cloneUri: Required. Git Clone URI.
    createTime: Output only. [Output only] Create timestamp
    deleteTime: Output only. [Output only] Delete timestamp
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    gitProxyUri: Output only. URI to access the linked repository through the
      Git Proxy. This field is only populated if the git proxy is enabled for
      the connection.
    labels: Optional. Labels as key value pairs
    name: Identifier. Resource name of the repository, in the format
      `projects/*/locations/*/connections/*/gitRepositoryLinks/*`.
    reconciling: Output only. Set to true when the connection is being set up
      or updated in the background.
    uid: Output only. A system-assigned unique identifier for the
      GitRepositoryLink.
    updateTime: Output only. [Output only] Update timestamp
    webhookId: Output only. External ID of the webhook created for the
      repository.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Allows clients to store small amounts of arbitrary data.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  cloneUri = _messages.StringField(2)
  createTime = _messages.StringField(3)
  deleteTime = _messages.StringField(4)
  etag = _messages.StringField(5)
  gitProxyUri = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  reconciling = _messages.BooleanField(9)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)
  webhookId = _messages.StringField(12)


class HttpBody(_messages.Message):
  r"""Message that represents an arbitrary HTTP body. It should only be used
  for payload formats that can't be represented as JSON, such as raw binary or
  an HTML page. This message can be used both in streaming and non-streaming
  API methods in the request as well as the response. It can be used as a top-
  level request field, which is convenient if one wants to extract parameters
  from either the URL or HTTP template into the request fields and also want
  access to the raw HTTP body. Example: message GetResourceRequest { // A
  unique request id. string request_id = 1; // The raw HTTP body is bound to
  this field. google.api.HttpBody http_body = 2; } service ResourceService {
  rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc
  UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); }
  Example with streaming methods: service CaldavService { rpc
  GetCalendar(stream google.api.HttpBody) returns (stream
  google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns
  (stream google.api.HttpBody); } Use of this type only changes how the
  request and response bodies are handled, all other features will continue to
  work unchanged.

  Messages:
    ExtensionsValueListEntry: A ExtensionsValueListEntry object.

  Fields:
    contentType: The HTTP Content-Type header value specifying the content
      type of the body.
    data: The HTTP request/response body as raw binary.
    extensions: Application specific response metadata. Must be set in the
      first response for streaming APIs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtensionsValueListEntry(_messages.Message):
    r"""A ExtensionsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        ExtensionsValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtensionsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  contentType = _messages.StringField(1)
  data = _messages.BytesField(2)
  extensions = _messages.MessageField('ExtensionsValueListEntry', 3, repeated=True)


class Installation(_messages.Message):
  r"""Represents an installation of the GitHub App.

  Fields:
    id: ID of the installation in GitHub.
    name: Name of the GitHub user or organization that owns this installation.
    type: Either "user" or "organization".
  """

  id = _messages.IntegerField(1)
  name = _messages.StringField(2)
  type = _messages.StringField(3)


class InstallationState(_messages.Message):
  r"""Describes stage and necessary actions to be taken by the user to
  complete the installation. Used for GitHub and GitHub Enterprise based
  connections.

  Enums:
    StageValueValuesEnum: Output only. Current step of the installation
      process.

  Fields:
    actionUri: Output only. Link to follow for next action. Empty string if
      the installation is already complete.
    message: Output only. Message of what the user should do next to continue
      the installation. Empty string if the installation is already complete.
    stage: Output only. Current step of the installation process.
  """

  class StageValueValuesEnum(_messages.Enum):
    r"""Output only. Current step of the installation process.

    Values:
      STAGE_UNSPECIFIED: No stage specified.
      PENDING_CREATE_APP: Only for GitHub Enterprise. An App creation has been
        requested. The user needs to confirm the creation in their GitHub
        enterprise host.
      PENDING_USER_OAUTH: User needs to authorize the GitHub (or Enterprise)
        App via OAuth.
      PENDING_INSTALL_APP: User needs to follow the link to install the GitHub
        (or Enterprise) App.
      COMPLETE: Installation process has been completed.
    """
    STAGE_UNSPECIFIED = 0
    PENDING_CREATE_APP = 1
    PENDING_USER_OAUTH = 2
    PENDING_INSTALL_APP = 3
    COMPLETE = 4

  actionUri = _messages.StringField(1)
  message = _messages.StringField(2)
  stage = _messages.EnumField('StageValueValuesEnum', 3)


class LinkableGitRepository(_messages.Message):
  r"""LinkableGitRepository represents a git repository that can be linked to
  a connection.

  Fields:
    cloneUri: The clone uri of the repository.
  """

  cloneUri = _messages.StringField(1)


class ListAccountConnectorsResponse(_messages.Message):
  r"""Message for response to listing AccountConnectors

  Fields:
    accountConnectors: The list of AccountConnectors
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  accountConnectors = _messages.MessageField('AccountConnector', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListConnectionsResponse(_messages.Message):
  r"""Message for response to listing Connections

  Fields:
    connections: The list of Connection
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  connections = _messages.MessageField('Connection', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGitRepositoryLinksResponse(_messages.Message):
  r"""Message for response to listing GitRepositoryLinks

  Fields:
    gitRepositoryLinks: The list of GitRepositoryLinks
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  gitRepositoryLinks = _messages.MessageField('GitRepositoryLink', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListUsersResponse(_messages.Message):
  r"""Message for response to listing Users

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
    users: The list of Users
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  users = _messages.MessageField('User', 3, repeated=True)


class OAuthCredential(_messages.Message):
  r"""Represents an OAuth token of the account that authorized the Connection,
  and associated metadata.

  Fields:
    oauthTokenSecretVersion: Required. A SecretManager resource containing the
      OAuth token that authorizes the connection. Format:
      `projects/*/secrets/*/versions/*`.
    username: Output only. The username associated with this token.
  """

  oauthTokenSecretVersion = _messages.StringField(1)
  username = _messages.StringField(2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have google.longrunning.Operation.error value
      with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class ProcessBitbucketCloudWebhookRequest(_messages.Message):
  r"""RPC request object accepted by the ProcessBitbucketCloudWebhook RPC
  method.

  Fields:
    body: Required. HTTP request body.
  """

  body = _messages.MessageField('HttpBody', 1)


class ProcessBitbucketDataCenterWebhookRequest(_messages.Message):
  r"""RPC request object accepted by the ProcessBitbucketDataCenterWebhook RPC
  method.

  Fields:
    body: Required. HTTP request body.
  """

  body = _messages.MessageField('HttpBody', 1)


class ProcessGitHubEnterpriseWebhookRequest(_messages.Message):
  r"""RPC request object accepted by the ProcessGitHubEnterpriseWebhook RPC
  method.

  Fields:
    body: Required. HTTP request body.
  """

  body = _messages.MessageField('HttpBody', 1)


class ProcessGitLabEnterpriseWebhookRequest(_messages.Message):
  r"""RPC request object accepted by the ProcessGitLabEnterpriseWebhook RPC
  method.

  Fields:
    body: Required. HTTP request body.
  """

  body = _messages.MessageField('HttpBody', 1)


class ProcessGitLabWebhookRequest(_messages.Message):
  r"""RPC request object accepted by the ProcessGitLabWebhook RPC method.

  Fields:
    body: Required. HTTP request body.
  """

  body = _messages.MessageField('HttpBody', 1)


class ProviderOAuthConfig(_messages.Message):
  r"""ProviderOAuthConfig is the OAuth config for a provider.

  Enums:
    SystemProviderIdValueValuesEnum: Immutable. Developer Connect provided
      OAuth.

  Fields:
    scopes: Required. User selected scopes to apply to the Oauth config In the
      event of changing scopes, user records under AccountConnector will be
      deleted and users will re-auth again.
    systemProviderId: Immutable. Developer Connect provided OAuth.
  """

  class SystemProviderIdValueValuesEnum(_messages.Enum):
    r"""Immutable. Developer Connect provided OAuth.

    Values:
      SYSTEM_PROVIDER_UNSPECIFIED: No system provider specified.
      GITHUB: GitHub provider. Scopes can be found at
        https://docs.github.com/en/apps/oauth-apps/building-oauth-apps/scopes-
        for-oauth-apps#available-scopes
      GITLAB: GitLab provider. Scopes can be found at
        https://docs.gitlab.com/user/profile/personal_access_tokens/#personal-
        access-token-scopes
      GOOGLE: Google provider. Recommended scopes:
        "https://www.googleapis.com/auth/drive.readonly",
        "https://www.googleapis.com/auth/documents.readonly"
      SENTRY: Sentry provider. Scopes can be found at
        https://docs.sentry.io/api/permissions/
      ROVO: Rovo provider. Must select the "rovo" scope.
      NEW_RELIC: New Relic provider. No scopes are allowed.
      DATASTAX: Datastax provider. No scopes are allowed.
      DYNATRACE: Dynatrace provider.
    """
    SYSTEM_PROVIDER_UNSPECIFIED = 0
    GITHUB = 1
    GITLAB = 2
    GOOGLE = 3
    SENTRY = 4
    ROVO = 5
    NEW_RELIC = 6
    DATASTAX = 7
    DYNATRACE = 8

  scopes = _messages.StringField(1, repeated=True)
  systemProviderId = _messages.EnumField('SystemProviderIdValueValuesEnum', 2)


class ServiceDirectoryConfig(_messages.Message):
  r"""ServiceDirectoryConfig represents Service Directory configuration for a
  connection.

  Fields:
    service: Required. The Service Directory service name. Format: projects/{p
      roject}/locations/{location}/namespaces/{namespace}/services/{service}.
  """

  service = _messages.StringField(1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class User(_messages.Message):
  r"""User represents a user connected to the service providers through a
  AccountConnector.

  Fields:
    createTime: Output only. The timestamp when the user was created.
    displayName: Output only. Developer Connect automatically converts user
      identity to some human readable description, e.g., email address.
    lastTokenRequestTime: Output only. The timestamp when the token was last
      requested.
    name: Identifier. Resource name of the user, in the format
      `projects/*/locations/*/accountConnectors/*/users/*`.
  """

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  lastTokenRequestTime = _messages.StringField(3)
  name = _messages.StringField(4)


class UserCredential(_messages.Message):
  r"""Represents a personal access token that authorized the Connection, and
  associated metadata.

  Fields:
    userTokenSecretVersion: Required. A SecretManager resource containing the
      user token that authorizes the Developer Connect connection. Format:
      `projects/*/secrets/*/versions/*`.
    username: Output only. The username associated with this token.
  """

  userTokenSecretVersion = _messages.StringField(1)
  username = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
