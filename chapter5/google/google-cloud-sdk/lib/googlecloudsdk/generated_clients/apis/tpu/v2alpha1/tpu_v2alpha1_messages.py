"""Generated message classes for tpu version v2alpha1.

TPU API provides customers with access to Google TPU technology.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'tpu'


class AbstractTrafficShape(_messages.Message):
  r"""Represents an abstract traffic shape in the traffic matrix. By "traffic
  shape", we mean a list of coordinates and the directed edges of traffic that
  flow between them. By "abstract", we mean that each traffic shape is defined
  relative to 0-indexed coordinates. These abstract coordinates are converted
  to absolute coordinates when instantiated in `traffic_shape_instantiation`.

  Fields:
    allToAllTraffic: All to all traffic shape.
    nToMTraffic: N to m traffic shape
    ringTraffic: Ring traffic shape.
  """

  allToAllTraffic = _messages.MessageField('AllToAllTraffic', 1)
  nToMTraffic = _messages.MessageField('NToMTraffic', 2)
  ringTraffic = _messages.MessageField('RingTraffic', 3)


class AcceleratorConfig(_messages.Message):
  r"""A TPU accelerator configuration.

  Enums:
    TypeValueValuesEnum: Required. Type of TPU.

  Fields:
    topology: Required. Topology of TPU in chips.
    type: Required. Type of TPU.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of TPU.

    Values:
      TYPE_UNSPECIFIED: Unspecified version.
      V2: TPU v2.
      V3: TPU v3.
      V4: TPU v4.
      V5LITE_POD: TPU v5lite pod.
      V5P: TPU v5.
      V6E: TPU v6e.
      TPU7X: TPU7x.
      V6EA: TPU v6ea.
    """
    TYPE_UNSPECIFIED = 0
    V2 = 1
    V3 = 2
    V4 = 3
    V5LITE_POD = 4
    V5P = 5
    V6E = 6
    TPU7X = 7
    V6EA = 8

  topology = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class AcceleratorType(_messages.Message):
  r"""A accelerator type that a Node can be configured with.

  Fields:
    acceleratorConfigs: The accelerator config.
    name: The resource name.
    type: The accelerator type.
  """

  acceleratorConfigs = _messages.MessageField('AcceleratorConfig', 1, repeated=True)
  name = _messages.StringField(2)
  type = _messages.StringField(3)


class AcceptedData(_messages.Message):
  r"""Further data for the accepted state."""


class AccessConfig(_messages.Message):
  r"""An access config attached to the TPU worker.

  Fields:
    externalIp: Output only. An external IP address associated with the TPU
      worker.
  """

  externalIp = _messages.StringField(1)


class ActiveData(_messages.Message):
  r"""Further data for the active state."""


class AllToAllTraffic(_messages.Message):
  r"""Predefined traffic shape in which each `group` member sends traffic to
  each other `group` member (except self).

  Fields:
    group: List of coordinates participating in the AllToAll traffic exchange.
  """

  group = _messages.MessageField('CoordinateList', 1)


class AttachedDisk(_messages.Message):
  r"""A node-attached disk resource.

  Enums:
    ModeValueValuesEnum: The mode in which to attach this disk. If not
      specified, the default is READ_WRITE mode. Only applicable to
      data_disks.

  Fields:
    mode: The mode in which to attach this disk. If not specified, the default
      is READ_WRITE mode. Only applicable to data_disks.
    sourceDisk: Specifies the full path to an existing disk. For example:
      "projects/my-project/zones/us-central1-c/disks/my-disk".
    workerIds: Optional. The list of worker IDs this disk is attached to.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""The mode in which to attach this disk. If not specified, the default
    is READ_WRITE mode. Only applicable to data_disks.

    Values:
      DISK_MODE_UNSPECIFIED: The disk mode is not known/set.
      READ_WRITE: Attaches the disk in read-write mode. Only one TPU node can
        attach a disk in read-write mode at a time.
      READ_ONLY: Attaches the disk in read-only mode. Multiple TPU nodes can
        attach a disk in read-only mode at a time.
    """
    DISK_MODE_UNSPECIFIED = 0
    READ_WRITE = 1
    READ_ONLY = 2

  mode = _messages.EnumField('ModeValueValuesEnum', 1)
  sourceDisk = _messages.StringField(2)
  workerIds = _messages.StringField(3, repeated=True)


class BestEffort(_messages.Message):
  r"""BestEffort tier definition."""


class BootDiskConfig(_messages.Message):
  r"""Boot disk configurations.

  Fields:
    customerEncryptionKey: Optional. Customer encryption key for boot disk.
    diskSizeGb: Optional. Size of the boot disk in GB. It must be larger than
      or equal to the size of the image.
    enableConfidentialCompute: Optional. Whether the boot disk will be created
      with confidential compute mode.
    provisionedIops: Optional. Indicates how many IOPS to provision for the
      disk. This sets the number of I/O operations per second that the disk
      can handle. To learn more about IOPS, see [Provisioning persistent disk
      performance](https://cloud.google.com/compute/docs/disks/performance#pro
      visioned-iops).
    provisionedThroughput: Optional. Indicates how much throughput to
      provision for the disk. This sets the number of throughput MB per second
      that the disk can handle.
    sourceImage: Optional. Image from which boot disk is to be created. If not
      specified, the default image for the runtime version will be used.
      Example: `projects/$PROJECT_ID/global/images/$IMAGE_NAME`.
  """

  customerEncryptionKey = _messages.MessageField('CustomerEncryptionKey', 1)
  diskSizeGb = _messages.IntegerField(2)
  enableConfidentialCompute = _messages.BooleanField(3)
  provisionedIops = _messages.IntegerField(4)
  provisionedThroughput = _messages.IntegerField(5)
  sourceImage = _messages.StringField(6)


class ChipCoordinate(_messages.Message):
  r"""Represents a single chip in a logical traffic matrix.

  Fields:
    sliceCoordinate: Coordinate of slice that chip is in.
    xCoordinate: X coordinate of chip.
    yCoordinate: Y coordinate of chip.
    zCoordinate: Z coordinate of chip.
  """

  sliceCoordinate = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  xCoordinate = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  yCoordinate = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  zCoordinate = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ChipCoordinateList(_messages.Message):
  r"""Represents a list of individually defined chip coordinates.

  Fields:
    coordinates: List of chip coordinates.
  """

  coordinates = _messages.MessageField('ChipCoordinate', 1, repeated=True)


class ChipCoordinateRangeGenerator(_messages.Message):
  r"""Compactly represents a list of Chip coordinates as the cross-product of
  each term. For example: * slice_coordinates: { 0, 1, 2, 3 } * x_coordinates:
  { 0 } * y_coordinates: { 0, 1 } Represents all the chips in the first column
  (x) and first 2 rows (y) of the first 4 slices.

  Fields:
    sliceCoordinates: Slice coordinates for chip coordinate range
    xCoordinates: X coordinates for chip coordinate range.
    yCoordinates: Y coordinates for chip coordinate range.
    zCoordinates: If specifying 2D coordinates, z_coordinate may be omitted.
  """

  sliceCoordinates = _messages.MessageField('Range', 1)
  xCoordinates = _messages.MessageField('Range', 2)
  yCoordinates = _messages.MessageField('Range', 3)
  zCoordinates = _messages.MessageField('Range', 4)


class CoordinateList(_messages.Message):
  r"""Defines a list of related `src` and/or `dst` coordinates in the traffic
  matrix.

  Fields:
    chipCoordinate: A list of individually defined chip coordinates.
    chipCoordinateRangeGenerator: A list of chip coordinates represented by
      the provided range generator.
  """

  chipCoordinate = _messages.MessageField('ChipCoordinateList', 1)
  chipCoordinateRangeGenerator = _messages.MessageField('ChipCoordinateRangeGenerator', 2)


class CreatingData(_messages.Message):
  r"""Further data for the creating state."""


class CustomTrafficMatrix(_messages.Message):
  r"""Represents a custom traffic matrix passed directly by the calling
  client.

  Fields:
    shapeGeneratedEntry: List of distinct shape generators that describe the
      traffic matrix.
  """

  shapeGeneratedEntry = _messages.MessageField('ShapeGeneratedEntry', 1, repeated=True)


class CustomerEncryptionKey(_messages.Message):
  r"""Customer's encryption key.

  Fields:
    kmsKeyName: The name of the encryption key that is stored in Google Cloud
      KMS. For example: "kmsKeyName":
      "projects/kms_project_id/locations/region/keyRings/
      key_region/cryptoKeys/key The fully-qualifed key name may be returned
      for resource GET requests. For example: "kmsKeyName":
      "projects/kms_project_id/locations/region/keyRings/
      key_region/cryptoKeys/key /cryptoKeyVersions/1
  """

  kmsKeyName = _messages.StringField(1)


class DefaultUniformTrafficMatrix(_messages.Message):
  r"""See notes in `LogicalTrafficMatrix.default_uniform_traffic_matrix`"""


class DeletingData(_messages.Message):
  r"""Further data for the deleting state."""


class DstSliceTraffic(_messages.Message):
  r"""A single edge of traffic directed towards a dst `slice_coord`.

  Fields:
    sliceCoord: Dst slice coordinate.
    traffic: Traffic directed towards this slice.
  """

  sliceCoord = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  traffic = _messages.MessageField('Traffic', 2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class FailedData(_messages.Message):
  r"""Further data for the failed state.

  Fields:
    error: The error that caused the queued resource to enter the FAILED
      state.
  """

  error = _messages.MessageField('Status', 1)


class GenerateServiceIdentityRequest(_messages.Message):
  r"""Request for GenerateServiceIdentity."""


class GenerateServiceIdentityResponse(_messages.Message):
  r"""Response for GenerateServiceIdentity.

  Fields:
    identity: ServiceIdentity that was created or retrieved.
  """

  identity = _messages.MessageField('ServiceIdentity', 1)


class GetGuestAttributesRequest(_messages.Message):
  r"""Request for GetGuestAttributes.

  Fields:
    queryPath: The guest attributes path to be queried.
    workerIds: The 0-based worker ID. If it is empty, all workers'
      GuestAttributes will be returned.
  """

  queryPath = _messages.StringField(1)
  workerIds = _messages.StringField(2, repeated=True)


class GetGuestAttributesResponse(_messages.Message):
  r"""Response for GetGuestAttributes.

  Fields:
    guestAttributes: The guest attributes for the TPU workers.
  """

  guestAttributes = _messages.MessageField('GuestAttributes', 1, repeated=True)


class Guaranteed(_messages.Message):
  r"""Guaranteed tier definition.

  Fields:
    minDuration: Optional. Defines the minimum duration of the guarantee. If
      specified, the requested resources will only be provisioned if they can
      be allocated for at least the given duration.
    reserved: Optional. Specifies the request should be scheduled on reserved
      capacity.
  """

  minDuration = _messages.StringField(1)
  reserved = _messages.BooleanField(2)


class GuestAttributes(_messages.Message):
  r"""A guest attributes.

  Fields:
    queryPath: The path to be queried. This can be the default namespace ('/')
      or a nested namespace ('/\/') or a specified key ('/\/\')
    queryValue: The value of the requested queried path.
  """

  queryPath = _messages.StringField(1)
  queryValue = _messages.MessageField('GuestAttributesValue', 2)


class GuestAttributesEntry(_messages.Message):
  r"""A guest attributes namespace/key/value entry.

  Fields:
    key: Key for the guest attribute entry.
    namespace: Namespace for the guest attribute entry.
    value: Value for the guest attribute entry.
  """

  key = _messages.StringField(1)
  namespace = _messages.StringField(2)
  value = _messages.StringField(3)


class GuestAttributesValue(_messages.Message):
  r"""Array of guest attribute namespace/key/value tuples.

  Fields:
    items: The list of guest attributes entries.
  """

  items = _messages.MessageField('GuestAttributesEntry', 1, repeated=True)


class Interval(_messages.Message):
  r"""Represents a time interval, encoded as a Timestamp start (inclusive) and
  a Timestamp end (exclusive). The start must be less than or equal to the
  end. When the start equals the end, the interval is empty (matches no time).
  When both start and end are unspecified, the interval matches any time.

  Fields:
    endTime: Optional. Exclusive end of the interval. If specified, a
      Timestamp matching this interval will have to be before the end.
    startTime: Optional. Inclusive start of the interval. If specified, a
      Timestamp matching this interval will have to be the same or after the
      start.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class ListAcceleratorTypesResponse(_messages.Message):
  r"""Response for ListAcceleratorTypes.

  Fields:
    acceleratorTypes: The listed nodes.
    nextPageToken: The next page token or empty if none.
    unreachable: Locations that could not be reached.
  """

  acceleratorTypes = _messages.MessageField('AcceleratorType', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListNodesResponse(_messages.Message):
  r"""Response for ListNodes.

  Fields:
    nextPageToken: The next page token or empty if none.
    nodes: The listed nodes.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  nodes = _messages.MessageField('Node', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListQueuedResourcesResponse(_messages.Message):
  r"""Response for ListQueuedResources.

  Fields:
    nextPageToken: The next page token or empty if none.
    queuedResources: The listed queued resources.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  queuedResources = _messages.MessageField('QueuedResource', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListReservationsResponse(_messages.Message):
  r"""Response for ListReservations.

  Fields:
    nextPageToken: The next page token or empty if none.
    reservations: The listed reservations.
  """

  nextPageToken = _messages.StringField(1)
  reservations = _messages.MessageField('Reservation', 2, repeated=True)


class ListRuntimeVersionsResponse(_messages.Message):
  r"""Response for ListRuntimeVersions.

  Fields:
    nextPageToken: The next page token or empty if none.
    runtimeVersions: The listed nodes.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  runtimeVersions = _messages.MessageField('RuntimeVersion', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LogicalTrafficMatrix(_messages.Message):
  r"""Describes the anticipated network traffic pattern between nodes
  (henceforth referred to as "coordinates") participating in a distributed
  computation. We note the following conventions for coordinates: - Slice
  coordinates are 0-indexed and contiguous with respect to their owning
  traffic matrix. - Chip coordinates are 0-indexed and contiguous relative to
  their owning-slice. Chip coordinates map 1:1 to the physical TPU chips
  deployed in their owning slice.

  Fields:
    customTrafficMatrix: Custom generator syntax used to represent chip-chip
      granularity traffic matrix (TM) that is too large to be reasonably
      represented as a simple adjacency list.
    defaultUniformTrafficMatrix: Generates a default uniform traffic matrix,
      functionally equivalent to a slice-to-slice, all-to-all traffic matrix
      where each slice sends the same amount of traffic to all other slices.
      Notes: - Borg uses this default-generated traffic matrix to achieve
      best-effort, superblock-compact placement for the affinity group. - Borg
      may or may not report default-generated demands to the Datacenter
      Network for proactive provisioning; to depend on this behavior use an
      alternate representation such as `slice_to_slice_adjacency_list`. - This
      representation is likely a good fit for "small" workloads (e.g. 4 VLP
      slices) that wish to benefit from best-effort superblock colocation
      without needing to provide a detailed traffic matrix. - This option is
      likely not a good fit for "large" workloads with non-uniform traffic
      patterns (e.g. a 100 VLP slice workload with 10 data parallel replicas
      communicating in an all-to-all pattern and 10 pipeline stages
      communicating in a bidirection ring pattern), as it does not give Borg
      enough detail on which of the slices should be colocated in the same
      superblock as other slices. At the largest (e.g. cell-wide) scale, this
      devolves into "random" placement.
    sliceToSliceAdjacencyList: Adjacency list representation of a slice->slice
      granularity TM. Slice-to-slice encoding assumes traffic flows out of
      each slice uniformly. This assumption is inconsequential for superblock
      (SB) Network Aware Scheduling (NAS) of slices that fit within a SB (e.g.
      VLP).
  """

  customTrafficMatrix = _messages.MessageField('CustomTrafficMatrix', 1)
  defaultUniformTrafficMatrix = _messages.MessageField('DefaultUniformTrafficMatrix', 2)
  sliceToSliceAdjacencyList = _messages.MessageField('SliceToSliceAdjacencyList', 3)


class MultiNodeParams(_messages.Message):
  r"""Parameters to specify for multi-node QueuedResource requests. This field
  must be populated in case of multi-node requests instead of node_id. It's an
  error to specify both node_id and multi_node_params.

  Enums:
    WorkloadTypeValueValuesEnum: Optional. The workload type for the multi-
      node request.

  Fields:
    nodeCount: Required. Number of nodes with this spec. The system will
      attempt to provison "node_count" nodes as part of the request. This
      needs to be > 1.
    nodeIdPrefix: Prefix of node_ids in case of multi-node request Should
      follow the `^[A-Za-z0-9_.~+%-]+$` regex format. If node_count = 3 and
      node_id_prefix = "np", node ids of nodes created will be "np-0", "np-1",
      "np-2". If this field is not provided we use queued_resource_id as the
      node_id_prefix.
    workloadType: Optional. The workload type for the multi-node request.
  """

  class WorkloadTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The workload type for the multi-node request.

    Values:
      WORKLOAD_TYPE_UNSPECIFIED: Not specified.
      THROUGHPUT_OPTIMIZED: All of the nodes are available most of the time.
        Recommended for training workloads.
      AVAILABILITY_OPTIMIZED: Most of the nodes are available all of the time.
        Recommended for serving workloads.
    """
    WORKLOAD_TYPE_UNSPECIFIED = 0
    THROUGHPUT_OPTIMIZED = 1
    AVAILABILITY_OPTIMIZED = 2

  nodeCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  nodeIdPrefix = _messages.StringField(2)
  workloadType = _messages.EnumField('WorkloadTypeValueValuesEnum', 3)


class NToMTraffic(_messages.Message):
  r"""Predefined traffic shape in which each `src_group` member sends traffic
  to each `dst_group` member. For example: * 1 entry in `src` and many entries
  in `dst` represent `One to Many` traffic. * 1 entry in `dst` and many
  entries in `src` represent `Many to One` traffic.

  Enums:
    TrafficDirectionValueValuesEnum: If UNSPECIFIED or UNIDIRECTIONAL, each
      `src_group` member sends traffic to each `dst_group` member. If
      BIDIRECTIONAL, each `dst_group` member additionally sends traffic to
      each `src_group` member.

  Fields:
    dstGroup: `dst` coordinates receiving data from all coordinates in
      `src_group`.
    srcGroup: `src` coordinates sending data to all coordinates in
      `dst_group`.
    trafficDirection: If UNSPECIFIED or UNIDIRECTIONAL, each `src_group`
      member sends traffic to each `dst_group` member. If BIDIRECTIONAL, each
      `dst_group` member additionally sends traffic to each `src_group`
      member.
  """

  class TrafficDirectionValueValuesEnum(_messages.Enum):
    r"""If UNSPECIFIED or UNIDIRECTIONAL, each `src_group` member sends
    traffic to each `dst_group` member. If BIDIRECTIONAL, each `dst_group`
    member additionally sends traffic to each `src_group` member.

    Values:
      TRAFFIC_DIRECTION_UNSPECIFIED: Traffic direction is not specified
      TRAFFIC_DIRECTION_UNIDIRECTIONAL: Traffic is sent in one direction.
      TRAFFIC_DIRECTION_BIDIRECTIONAL: Traffic is sent in both directions.
    """
    TRAFFIC_DIRECTION_UNSPECIFIED = 0
    TRAFFIC_DIRECTION_UNIDIRECTIONAL = 1
    TRAFFIC_DIRECTION_BIDIRECTIONAL = 2

  dstGroup = _messages.MessageField('CoordinateList', 1)
  srcGroup = _messages.MessageField('CoordinateList', 2)
  trafficDirection = _messages.EnumField('TrafficDirectionValueValuesEnum', 3)


class NetworkConfig(_messages.Message):
  r"""Network related configurations.

  Fields:
    canIpForward: Allows the TPU node to send and receive packets with non-
      matching destination or source IPs. This is required if you plan to use
      the TPU workers to forward routes.
    enableExternalIps: Indicates that external IP addresses would be
      associated with the TPU workers. If set to false, the specified
      subnetwork or network should have Private Google Access enabled.
    network: The name of the network for the TPU node. It must be a
      preexisting Google Compute Engine network. If none is provided,
      "default" will be used.
    queueCount: Optional. Specifies networking queue count for TPU VM
      instance's network interface.
    subnetwork: The name of the subnetwork for the TPU node. It must be a
      preexisting Google Compute Engine subnetwork. If none is provided,
      "default" will be used.
  """

  canIpForward = _messages.BooleanField(1)
  enableExternalIps = _messages.BooleanField(2)
  network = _messages.StringField(3)
  queueCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  subnetwork = _messages.StringField(5)


class NetworkEndpoint(_messages.Message):
  r"""A network endpoint over which a TPU worker can be reached.

  Fields:
    accessConfig: The access config for the TPU worker.
    ipAddress: The internal IP address of this network endpoint.
    port: The port of this network endpoint.
  """

  accessConfig = _messages.MessageField('AccessConfig', 1)
  ipAddress = _messages.StringField(2)
  port = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class Node(_messages.Message):
  r"""A TPU instance.

  Enums:
    ApiVersionValueValuesEnum: Output only. The API version that created this
      Node.
    HealthValueValuesEnum: The health status of the TPU node.
    StateValueValuesEnum: Output only. The current state for the TPU Node.

  Messages:
    LabelsValue: Resource labels to represent user-provided metadata.
    MetadataValue: Custom metadata to apply to the TPU Node. Can set startup-
      script and shutdown-script

  Fields:
    acceleratorConfig: The AccleratorConfig for the TPU Node.
    acceleratorType: The type of hardware accelerators associated with this
      node.
    apiVersion: Output only. The API version that created this Node.
    autocheckpointEnabled: Optional. Whether Autocheckpoint is enabled.
    bootDiskConfig: Optional. Boot disk configuration.
    cidrBlock: The CIDR block that the TPU node will use when selecting an IP
      address. This CIDR block must be a /29 block; the Compute Engine
      networks API forbids a smaller block, and using a larger block would be
      wasteful (a node can only consume one IP address). Errors will occur if
      the CIDR block has already been used for a currently existing TPU node,
      the CIDR block conflicts with any subnetworks in the user's provided
      network, or the provided network is peered with another network that is
      using that CIDR block.
    createTime: Output only. The time when the node was created.
    dataDisks: The additional data disks for the Node.
    description: The user-supplied description of the TPU. Maximum of 512
      characters.
    health: The health status of the TPU node.
    healthDescription: Output only. If this field is populated, it contains a
      description of why the TPU Node is unhealthy.
    id: Output only. The unique identifier for the TPU Node.
    labels: Resource labels to represent user-provided metadata.
    metadata: Custom metadata to apply to the TPU Node. Can set startup-script
      and shutdown-script
    multisliceNode: Output only. Whether the Node belongs to a Multislice
      group.
    name: Output only. Immutable. The name of the TPU.
    networkConfig: Network configurations for the TPU node. network_config and
      network_configs are mutually exclusive, you can only specify one of
      them. If both are specified, an error will be returned.
    networkConfigs: Optional. Repeated network configurations for the TPU
      node. This field is used to specify multiple networks configs for the
      TPU node. network_config and network_configs are mutually exclusive, you
      can only specify one of them. If both are specified, an error will be
      returned.
    networkEndpoints: Output only. The network endpoints where TPU workers can
      be accessed and sent work. It is recommended that runtime clients of the
      node reach out to the 0th entry in this map first.
    queuedResource: Output only. The qualified name of the QueuedResource that
      requested this Node.
    runtimeVersion: Required. The runtime version running in the Node.
    schedulingConfig: The scheduling options for this node.
    serviceAccount: The Google Cloud Platform Service Account to be used by
      the TPU node VMs. If None is specified, the default compute service
      account will be used.
    shieldedInstanceConfig: Shielded Instance options.
    state: Output only. The current state for the TPU Node.
    symptoms: Output only. The Symptoms that have occurred to the TPU Node.
    tags: Tags to apply to the TPU Node. Tags are used to identify valid
      sources or targets for network firewalls.
    upcomingMaintenance: Output only. Upcoming maintenance on this TPU node.
  """

  class ApiVersionValueValuesEnum(_messages.Enum):
    r"""Output only. The API version that created this Node.

    Values:
      API_VERSION_UNSPECIFIED: API version is unknown.
      V1_ALPHA1: TPU API V1Alpha1 version.
      V1: TPU API V1 version.
      V2_ALPHA1: TPU API V2Alpha1 version.
    """
    API_VERSION_UNSPECIFIED = 0
    V1_ALPHA1 = 1
    V1 = 2
    V2_ALPHA1 = 3

  class HealthValueValuesEnum(_messages.Enum):
    r"""The health status of the TPU node.

    Values:
      HEALTH_UNSPECIFIED: Health status is unknown: not initialized or failed
        to retrieve.
      HEALTHY: The resource is healthy.
      TIMEOUT: The resource is unresponsive.
      UNHEALTHY_TENSORFLOW: The in-guest ML stack is unhealthy.
      UNHEALTHY_MAINTENANCE: The node is under maintenance/priority boost
        caused rescheduling and will resume running once rescheduled.
    """
    HEALTH_UNSPECIFIED = 0
    HEALTHY = 1
    TIMEOUT = 2
    UNHEALTHY_TENSORFLOW = 3
    UNHEALTHY_MAINTENANCE = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state for the TPU Node.

    Values:
      STATE_UNSPECIFIED: TPU node state is not known/set.
      CREATING: TPU node is being created.
      READY: TPU node has been created.
      RESTARTING: TPU node is restarting.
      REIMAGING: TPU node is undergoing reimaging.
      DELETING: TPU node is being deleted.
      REPAIRING: TPU node is being repaired and may be unusable. Details can
        be found in the 'help_description' field.
      STOPPED: TPU node is stopped.
      STOPPING: TPU node is currently stopping.
      STARTING: TPU node is currently starting.
      PREEMPTED: TPU node has been preempted. Only applies to Preemptible TPU
        Nodes.
      TERMINATED: TPU node has been terminated due to maintenance or has
        reached the end of its life cycle (for preemptible nodes).
      HIDING: TPU node is currently hiding.
      HIDDEN: TPU node has been hidden.
      UNHIDING: TPU node is currently unhiding.
      UNKNOWN: TPU node has unknown state after a failed repair.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    RESTARTING = 3
    REIMAGING = 4
    DELETING = 5
    REPAIRING = 6
    STOPPED = 7
    STOPPING = 8
    STARTING = 9
    PREEMPTED = 10
    TERMINATED = 11
    HIDING = 12
    HIDDEN = 13
    UNHIDING = 14
    UNKNOWN = 15

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels to represent user-provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Custom metadata to apply to the TPU Node. Can set startup-script and
    shutdown-script

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  acceleratorConfig = _messages.MessageField('AcceleratorConfig', 1)
  acceleratorType = _messages.StringField(2)
  apiVersion = _messages.EnumField('ApiVersionValueValuesEnum', 3)
  autocheckpointEnabled = _messages.BooleanField(4)
  bootDiskConfig = _messages.MessageField('BootDiskConfig', 5)
  cidrBlock = _messages.StringField(6)
  createTime = _messages.StringField(7)
  dataDisks = _messages.MessageField('AttachedDisk', 8, repeated=True)
  description = _messages.StringField(9)
  health = _messages.EnumField('HealthValueValuesEnum', 10)
  healthDescription = _messages.StringField(11)
  id = _messages.IntegerField(12)
  labels = _messages.MessageField('LabelsValue', 13)
  metadata = _messages.MessageField('MetadataValue', 14)
  multisliceNode = _messages.BooleanField(15)
  name = _messages.StringField(16)
  networkConfig = _messages.MessageField('NetworkConfig', 17)
  networkConfigs = _messages.MessageField('NetworkConfig', 18, repeated=True)
  networkEndpoints = _messages.MessageField('NetworkEndpoint', 19, repeated=True)
  queuedResource = _messages.StringField(20)
  runtimeVersion = _messages.StringField(21)
  schedulingConfig = _messages.MessageField('SchedulingConfig', 22)
  serviceAccount = _messages.MessageField('ServiceAccount', 23)
  shieldedInstanceConfig = _messages.MessageField('ShieldedInstanceConfig', 24)
  state = _messages.EnumField('StateValueValuesEnum', 25)
  symptoms = _messages.MessageField('Symptom', 26, repeated=True)
  tags = _messages.StringField(27, repeated=True)
  upcomingMaintenance = _messages.MessageField('UpcomingMaintenance', 28)


class NodeSpec(_messages.Message):
  r"""Details of the TPU node(s) being requested. Users can request either a
  single node or multiple nodes. NodeSpec provides the specification for
  node(s) to be created.

  Fields:
    multiNodeParams: Optional. Fields to specify in case of multi-node
      request.
    node: Required. The node.
    nodeId: The unqualified resource name. Should follow the
      `^[A-Za-z0-9_.~+%-]+$` regex format. This is only specified when
      requesting a single node. In case of multi-node requests,
      multi_node_params must be populated instead. It's an error to specify
      both node_id and multi_node_params.
    parent: Required. The parent resource name.
  """

  multiNodeParams = _messages.MessageField('MultiNodeParams', 1)
  node = _messages.MessageField('Node', 2)
  nodeId = _messages.StringField(3)
  parent = _messages.StringField(4)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Metadata describing an Operation

  Fields:
    apiVersion: API version.
    cancelRequested: Specifies if cancellation was requested for the
      operation.
    createTime: The time the operation was created.
    endTime: The time the operation finished running.
    statusDetail: Human-readable status of the operation, if any.
    target: Target of the operation - for example
      projects/project-1/connectivityTests/test-1
    verb: Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class PeakTraffic(_messages.Message):
  r"""Expected peak traffic between two coordinates.

  Fields:
    peakTrafficGbps: Gigabits per second.
  """

  peakTrafficGbps = _messages.FloatField(1)


class PerformMaintenanceQueuedResourceRequest(_messages.Message):
  r"""Request for PerformMaintenanceQueuedResource.

  Fields:
    nodeNames: The names of the nodes to perform maintenance on.
  """

  nodeNames = _messages.StringField(1, repeated=True)


class PerformMaintenanceRequest(_messages.Message):
  r"""Request for PerformMaintenance."""


class ProvisioningData(_messages.Message):
  r"""Further data for the provisioning state."""


class QueuedResource(_messages.Message):
  r"""A QueuedResource represents a request for resources that will be placed
  in a queue and fulfilled when the necessary resources are available.

  Enums:
    ProvisioningModelValueValuesEnum: Optional. The provisioning model for the
      resource.

  Fields:
    bestEffort: The BestEffort tier.
    createTime: Output only. The time when the QueuedResource was created.
    guaranteed: The Guaranteed tier.
    name: Output only. Immutable. The name of the QueuedResource.
    provisioningModel: Optional. The provisioning model for the resource.
    queueingPolicy: The queueing policy of the QueuedRequest.
    reservationName: Name of the reservation in which the resource should be
      provisioned. Format:
      projects/{project}/locations/{zone}/reservations/{reservation}
    runDuration: Optional. The duration of the requested resource.
    spot: Optional. The Spot tier.
    state: Output only. State of the QueuedResource request.
    tpu: Defines a TPU resource.
    trafficConfig: Network traffic configuration.
  """

  class ProvisioningModelValueValuesEnum(_messages.Enum):
    r"""Optional. The provisioning model for the resource.

    Values:
      PROVISIONING_MODEL_UNSPECIFIED: Provisioning model is unknown.
      STANDARD: Standard provisioning with user controlled runtime.
      SPOT: Spot provisioning with no guaranteed runtime.
      RESERVATION_BOUND: Reservation provisioning with runtime bound to the
        lifetime of the consumed reservation.
      FLEX_START: Provisioning with DWS Flex Start with max run duration.
    """
    PROVISIONING_MODEL_UNSPECIFIED = 0
    STANDARD = 1
    SPOT = 2
    RESERVATION_BOUND = 3
    FLEX_START = 4

  bestEffort = _messages.MessageField('BestEffort', 1)
  createTime = _messages.StringField(2)
  guaranteed = _messages.MessageField('Guaranteed', 3)
  name = _messages.StringField(4)
  provisioningModel = _messages.EnumField('ProvisioningModelValueValuesEnum', 5)
  queueingPolicy = _messages.MessageField('QueueingPolicy', 6)
  reservationName = _messages.StringField(7)
  runDuration = _messages.MessageField('RunDuration', 8)
  spot = _messages.MessageField('Spot', 9)
  state = _messages.MessageField('QueuedResourceState', 10)
  tpu = _messages.MessageField('Tpu', 11)
  trafficConfig = _messages.MessageField('TrafficConfig', 12)


class QueuedResourceState(_messages.Message):
  r"""QueuedResourceState defines the details of the QueuedResource request.

  Enums:
    StateValueValuesEnum: State of the QueuedResource request.
    StateInitiatorValueValuesEnum: Output only. The initiator of the
      QueuedResources's current state. Used to indicate whether the
      SUSPENDING/SUSPENDED state was initiated by the user or the service.

  Fields:
    acceptedData: Further data for the accepted state.
    activeData: Further data for the active state.
    creatingData: Further data for the creating state.
    deletingData: Further data for the deleting state.
    failedData: Further data for the failed state.
    provisioningData: Further data for the provisioning state.
    state: State of the QueuedResource request.
    stateInitiator: Output only. The initiator of the QueuedResources's
      current state. Used to indicate whether the SUSPENDING/SUSPENDED state
      was initiated by the user or the service.
    suspendedData: Further data for the suspended state.
    suspendingData: Further data for the suspending state.
  """

  class StateInitiatorValueValuesEnum(_messages.Enum):
    r"""Output only. The initiator of the QueuedResources's current state.
    Used to indicate whether the SUSPENDING/SUSPENDED state was initiated by
    the user or the service.

    Values:
      STATE_INITIATOR_UNSPECIFIED: The state initiator is unspecified.
      USER: The current QueuedResource state was initiated by the user.
      SERVICE: The current QueuedResource state was initiated by the service.
    """
    STATE_INITIATOR_UNSPECIFIED = 0
    USER = 1
    SERVICE = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the QueuedResource request.

    Values:
      STATE_UNSPECIFIED: State of the QueuedResource request is not known/set.
      CREATING: The QueuedResource request has been received. We're still
        working on determining if we will be able to honor this request.
      ACCEPTED: The QueuedResource request has passed initial
        validation/admission control and has been persisted in the queue.
      PROVISIONING: The QueuedResource request has been selected. The
        associated resources are currently being provisioned (or very soon
        will begin provisioning).
      FAILED: The request could not be completed. This may be due to some
        late-discovered problem with the request itself, or due to
        unavailability of resources within the constraints of the request
        (e.g., the 'valid until' start timing constraint expired).
      DELETING: The QueuedResource is being deleted.
      ACTIVE: The resources specified in the QueuedResource request have been
        provisioned and are ready for use by the end-user/consumer.
      SUSPENDING: The resources specified in the QueuedResource request are
        being deleted. This may have been initiated by the user, or the Cloud
        TPU service. Inspect the state data for more details.
      SUSPENDED: The resources specified in the QueuedResource request have
        been deleted.
      WAITING_FOR_RESOURCES: The QueuedResource request has passed initial
        validation and has been persisted in the queue. It will remain in this
        state until there are sufficient free resources to begin provisioning
        your request. Wait times will vary significantly depending on demand
        levels. When demand is high, not all requests can be immediately
        provisioned. If you need more reliable obtainability of TPUs consider
        purchasing a reservation. To put a limit on how long you are willing
        to wait, use [timing
        constraints](https://cloud.google.com/tpu/docs/queued-
        resources#request_a_queued_resource_before_a_specified_time).
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACCEPTED = 2
    PROVISIONING = 3
    FAILED = 4
    DELETING = 5
    ACTIVE = 6
    SUSPENDING = 7
    SUSPENDED = 8
    WAITING_FOR_RESOURCES = 9

  acceptedData = _messages.MessageField('AcceptedData', 1)
  activeData = _messages.MessageField('ActiveData', 2)
  creatingData = _messages.MessageField('CreatingData', 3)
  deletingData = _messages.MessageField('DeletingData', 4)
  failedData = _messages.MessageField('FailedData', 5)
  provisioningData = _messages.MessageField('ProvisioningData', 6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  stateInitiator = _messages.EnumField('StateInitiatorValueValuesEnum', 8)
  suspendedData = _messages.MessageField('SuspendedData', 9)
  suspendingData = _messages.MessageField('SuspendingData', 10)


class QueueingPolicy(_messages.Message):
  r"""Defines the policy of the QueuedRequest.

  Fields:
    validAfterDuration: A relative time after which resources may be created.
    validAfterTime: An absolute time at which resources may be created.
    validInterval: An absolute time interval within which resources may be
      created.
    validUntilDuration: A relative time after which resources should not be
      created. If the request cannot be fulfilled by this time the request
      will be failed.
    validUntilTime: An absolute time after which resources should not be
      created. If the request cannot be fulfilled by this time the request
      will be failed.
  """

  validAfterDuration = _messages.StringField(1)
  validAfterTime = _messages.StringField(2)
  validInterval = _messages.MessageField('Interval', 3)
  validUntilDuration = _messages.StringField(4)
  validUntilTime = _messages.StringField(5)


class Range(_messages.Message):
  r"""Compactly represents a range of numbers. The parameters take inspiration
  from Python's "range(start, stop, step)" function and can be described with
  the following pseudocode: `for (int i = start; i < end; i += step) { add i
  to list }` E.g. { start: 0, end: 10, step: 2 } can be used to describe the
  even numbers between 0 and 9.

  Fields:
    end: End of the range (exclusive). Required.
    start: Start of the range. Required.
    step: Describes the distance between elements for sparse ranges. Optional
      term defaulted to `1` if absent.
  """

  end = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  start = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  step = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class Reservation(_messages.Message):
  r"""A reservation describes the amount of a resource 'allotted' for a
  defined period of time.

  Enums:
    StateValueValuesEnum: Output only. The state of the Reservation.

  Fields:
    name: The reservation name with the format:
      projects/{projectID}/locations/{location}/reservations/{reservationID}
    standard: A standard reservation.
    state: Output only. The state of the Reservation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the Reservation.

    Values:
      STATE_UNSPECIFIED: The Reservation state is unspecified.
      APPROVED: The Reservation has been approved.
      PROVISIONING: The Reservation is being provisioned.
      ACTIVE: The Reservation is active.
      DEPROVISIONING: The Reservation is being deprovisioned.
      EXPIRED: The Reservation is past its end date.
      FAILED: The Reservation encountered a failure during mutation.
    """
    STATE_UNSPECIFIED = 0
    APPROVED = 1
    PROVISIONING = 2
    ACTIVE = 3
    DEPROVISIONING = 4
    EXPIRED = 5
    FAILED = 6

  name = _messages.StringField(1)
  standard = _messages.MessageField('Standard', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class ResetQueuedResourceRequest(_messages.Message):
  r"""Request for ResetQueuedResource."""


class RingTraffic(_messages.Message):
  r"""Predefined traffic shape in which each (i_th) `group` member sends
  traffic to the subsequent (i_th+1) `group` member, looping around at the
  end.

  Enums:
    TrafficDirectionValueValuesEnum: If UNSPECIFIED or UNIDIRECTIONAL, the
      i_th member sends traffic to the i+1_th member, looping at the end. If
      BIDIRECTIONAL, the i_th member additionally sends traffic to the i-1_th
      member, looping at the start.

  Fields:
    group: Sorted list of coordinates participating in the Ring Traffic
      exchange.
    trafficDirection: If UNSPECIFIED or UNIDIRECTIONAL, the i_th member sends
      traffic to the i+1_th member, looping at the end. If BIDIRECTIONAL, the
      i_th member additionally sends traffic to the i-1_th member, looping at
      the start.
  """

  class TrafficDirectionValueValuesEnum(_messages.Enum):
    r"""If UNSPECIFIED or UNIDIRECTIONAL, the i_th member sends traffic to the
    i+1_th member, looping at the end. If BIDIRECTIONAL, the i_th member
    additionally sends traffic to the i-1_th member, looping at the start.

    Values:
      TRAFFIC_DIRECTION_UNSPECIFIED: Traffic direction is not specified
      TRAFFIC_DIRECTION_UNIDIRECTIONAL: Traffic is sent in one direction.
      TRAFFIC_DIRECTION_BIDIRECTIONAL: Traffic is sent in both directions.
    """
    TRAFFIC_DIRECTION_UNSPECIFIED = 0
    TRAFFIC_DIRECTION_UNIDIRECTIONAL = 1
    TRAFFIC_DIRECTION_BIDIRECTIONAL = 2

  group = _messages.MessageField('CoordinateList', 1)
  trafficDirection = _messages.EnumField('TrafficDirectionValueValuesEnum', 2)


class RunDuration(_messages.Message):
  r"""Defines the maximum lifetime of the requested resource.

  Fields:
    maxRunDuration: The maximum duration of the requested resource.
    terminationTime: The time at which the requested resource will be
      terminated.
  """

  maxRunDuration = _messages.StringField(1)
  terminationTime = _messages.StringField(2)


class RuntimeVersion(_messages.Message):
  r"""A runtime version that a Node can be configured with.

  Fields:
    name: The resource name.
    version: The runtime version.
  """

  name = _messages.StringField(1)
  version = _messages.StringField(2)


class SchedulingConfig(_messages.Message):
  r"""Sets the scheduling options for this node.

  Enums:
    ProvisioningModelValueValuesEnum: Optional. Defines the provisioning model
      for the node.

  Fields:
    preemptible: Defines whether the node is preemptible.
    provisioningModel: Optional. Defines the provisioning model for the node.
    reservationName: Optional. Name of the reservation in which the node
      should be provisioned.
    reserved: Whether the node is created under a reservation.
    spot: Optional. Defines whether the node is Spot VM.
    terminationTimestamp: Output only. The time at which the node will be
      terminated.
  """

  class ProvisioningModelValueValuesEnum(_messages.Enum):
    r"""Optional. Defines the provisioning model for the node.

    Values:
      PROVISIONING_MODEL_UNSPECIFIED: Provisioning model is unknown.
      STANDARD: Standard provisioning with user controlled runtime.
      SPOT: Spot provisioning with no guaranteed runtime.
      RESERVATION_BOUND: Reservation provisioning with runtime bound to the
        lifetime of the consumed reservation.
    """
    PROVISIONING_MODEL_UNSPECIFIED = 0
    STANDARD = 1
    SPOT = 2
    RESERVATION_BOUND = 3

  preemptible = _messages.BooleanField(1)
  provisioningModel = _messages.EnumField('ProvisioningModelValueValuesEnum', 2)
  reservationName = _messages.StringField(3)
  reserved = _messages.BooleanField(4)
  spot = _messages.BooleanField(5)
  terminationTimestamp = _messages.StringField(6)


class ServiceAccount(_messages.Message):
  r"""A service account.

  Fields:
    email: Email address of the service account. If empty, default Compute
      service account will be used.
    scope: The list of scopes to be made available for this service account.
      If empty, access to all Cloud APIs will be allowed.
  """

  email = _messages.StringField(1)
  scope = _messages.StringField(2, repeated=True)


class ServiceIdentity(_messages.Message):
  r"""The per-product per-project service identity for Cloud TPU service.

  Fields:
    email: The email address of the service identity.
  """

  email = _messages.StringField(1)


class ShapeGeneratedEntry(_messages.Message):
  r"""Shape-based generator that efficiently encodes uniform traffic shapes
  (e.g. "horizontal ring traffic between all chips in two neighboring
  slices"). A single entry represents all the instantiations of a unique
  traffic shape in the traffic matrix.

  Fields:
    abstractTrafficShape: 0-indexed "abstract" traffic shape. See the
      definition of `AbstractTrafficShape` for details.
    traffic: Anticipated traffic across each edge in the concrete traffic
      shape defined above.
    trafficShapeInstantiation: List of coordinates in which we instantiate
      copies of `abstract_traffic_shape`. Conceptually, each coordinate in
      `traffic_shape_instantiation` represents an offset that converts a
      0-indexed "abstract" traffic shape into a concrete traffic shape with
      absolute coordinates in the traffic matrix.
  """

  abstractTrafficShape = _messages.MessageField('AbstractTrafficShape', 1)
  traffic = _messages.MessageField('Traffic', 2)
  trafficShapeInstantiation = _messages.MessageField('CoordinateList', 3)


class ShieldedInstanceConfig(_messages.Message):
  r"""A set of Shielded Instance options.

  Fields:
    enableSecureBoot: Defines whether the instance has Secure Boot enabled.
  """

  enableSecureBoot = _messages.BooleanField(1)


class SimulateMaintenanceEventRequest(_messages.Message):
  r"""Request for SimulateMaintenanceEvent.

  Fields:
    workerIds: The 0-based worker ID. If it is empty, worker ID 0 will be
      selected for maintenance event simulation. A maintenance event will only
      be fired on the first specified worker ID. Future implementations may
      support firing on multiple workers.
  """

  workerIds = _messages.StringField(1, repeated=True)


class SliceToSliceAdjacencyList(_messages.Message):
  r"""Adjacency list representation of a slice-to-slice traffic matrix.

  Fields:
    srcTraffic: One entry per slice containing the traffic leaving that slice.
  """

  srcTraffic = _messages.MessageField('SrcSliceTraffic', 1, repeated=True)


class Spot(_messages.Message):
  r"""Spot tier definition."""


class SrcSliceTraffic(_messages.Message):
  r"""All the non-zero edges of traffic leaving a src `slice_coord` directed
  towards dst slices.

  Fields:
    dstTraffic: List of traffic edges directed towards dst slices.
    sliceCoord: Src slice coordinate.
  """

  dstTraffic = _messages.MessageField('DstSliceTraffic', 1, repeated=True)
  sliceCoord = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class Standard(_messages.Message):
  r"""Details of a standard reservation.

  Enums:
    CapacityUnitsValueValuesEnum: Capacity units this reservation is measured
      in.

  Fields:
    capacityUnits: Capacity units this reservation is measured in.
    interval: The start and end time of the reservation.
    resourceType: The resource type of the reservation.
    size: The size of the reservation, in the units specified in the
      'capacity_units' field.
    usage: The current usage of the reservation.
  """

  class CapacityUnitsValueValuesEnum(_messages.Enum):
    r"""Capacity units this reservation is measured in.

    Values:
      CAPACITY_UNITS_UNSPECIFIED: The capacity units is not known/set.
      CORES: The capacity unit is set to CORES.
      CHIPS: The capacity unit is set to CHIPS.
    """
    CAPACITY_UNITS_UNSPECIFIED = 0
    CORES = 1
    CHIPS = 2

  capacityUnits = _messages.EnumField('CapacityUnitsValueValuesEnum', 1)
  interval = _messages.MessageField('Interval', 2)
  resourceType = _messages.StringField(3)
  size = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  usage = _messages.MessageField('Usage', 5)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartNodeRequest(_messages.Message):
  r"""Request for StartNode."""


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StopNodeRequest(_messages.Message):
  r"""Request for StopNode."""


class SuspendedData(_messages.Message):
  r"""Further data for the suspended state."""


class SuspendingData(_messages.Message):
  r"""Further data for the suspending state."""


class Symptom(_messages.Message):
  r"""A Symptom instance.

  Enums:
    SymptomTypeValueValuesEnum: Type of the Symptom.

  Fields:
    createTime: Timestamp when the Symptom is created.
    details: Detailed information of the current Symptom.
    symptomType: Type of the Symptom.
    workerId: A string used to uniquely distinguish a worker within a TPU
      node.
  """

  class SymptomTypeValueValuesEnum(_messages.Enum):
    r"""Type of the Symptom.

    Values:
      SYMPTOM_TYPE_UNSPECIFIED: Unspecified symptom.
      LOW_MEMORY: TPU VM memory is low.
      OUT_OF_MEMORY: TPU runtime is out of memory.
      EXECUTE_TIMED_OUT: TPU runtime execution has timed out.
      MESH_BUILD_FAIL: TPU runtime fails to construct a mesh that recognizes
        each TPU device's neighbors.
      HBM_OUT_OF_MEMORY: TPU HBM is out of memory.
      PROJECT_ABUSE: Abusive behaviors have been identified on the current
        project.
    """
    SYMPTOM_TYPE_UNSPECIFIED = 0
    LOW_MEMORY = 1
    OUT_OF_MEMORY = 2
    EXECUTE_TIMED_OUT = 3
    MESH_BUILD_FAIL = 4
    HBM_OUT_OF_MEMORY = 5
    PROJECT_ABUSE = 6

  createTime = _messages.StringField(1)
  details = _messages.StringField(2)
  symptomType = _messages.EnumField('SymptomTypeValueValuesEnum', 3)
  workerId = _messages.StringField(4)


class Tpu(_messages.Message):
  r"""Details of the TPU resource(s) being requested.

  Fields:
    nodeSpec: The TPU node(s) being requested.
  """

  nodeSpec = _messages.MessageField('NodeSpec', 1, repeated=True)


class TpuProjectsLocationsAcceleratorTypesGetRequest(_messages.Message):
  r"""A TpuProjectsLocationsAcceleratorTypesGetRequest object.

  Fields:
    name: Required. The resource name.
  """

  name = _messages.StringField(1, required=True)


class TpuProjectsLocationsAcceleratorTypesListRequest(_messages.Message):
  r"""A TpuProjectsLocationsAcceleratorTypesListRequest object.

  Fields:
    filter: List filter.
    orderBy: Sort results.
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class TpuProjectsLocationsGenerateServiceIdentityRequest(_messages.Message):
  r"""A TpuProjectsLocationsGenerateServiceIdentityRequest object.

  Fields:
    generateServiceIdentityRequest: A GenerateServiceIdentityRequest resource
      to be passed as the request body.
    parent: Required. The parent resource name.
  """

  generateServiceIdentityRequest = _messages.MessageField('GenerateServiceIdentityRequest', 1)
  parent = _messages.StringField(2, required=True)


class TpuProjectsLocationsGetRequest(_messages.Message):
  r"""A TpuProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class TpuProjectsLocationsListRequest(_messages.Message):
  r"""A TpuProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class TpuProjectsLocationsNodesCreateRequest(_messages.Message):
  r"""A TpuProjectsLocationsNodesCreateRequest object.

  Fields:
    node: A Node resource to be passed as the request body.
    nodeId: The unqualified resource name.
    parent: Required. The parent resource name.
    requestId: Idempotent request UUID.
  """

  node = _messages.MessageField('Node', 1)
  nodeId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class TpuProjectsLocationsNodesDeleteRequest(_messages.Message):
  r"""A TpuProjectsLocationsNodesDeleteRequest object.

  Fields:
    name: Required. The resource name.
    requestId: Idempotent request UUID.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class TpuProjectsLocationsNodesGetGuestAttributesRequest(_messages.Message):
  r"""A TpuProjectsLocationsNodesGetGuestAttributesRequest object.

  Fields:
    getGuestAttributesRequest: A GetGuestAttributesRequest resource to be
      passed as the request body.
    name: Required. The resource name.
  """

  getGuestAttributesRequest = _messages.MessageField('GetGuestAttributesRequest', 1)
  name = _messages.StringField(2, required=True)


class TpuProjectsLocationsNodesGetRequest(_messages.Message):
  r"""A TpuProjectsLocationsNodesGetRequest object.

  Fields:
    name: Required. The resource name.
  """

  name = _messages.StringField(1, required=True)


class TpuProjectsLocationsNodesListRequest(_messages.Message):
  r"""A TpuProjectsLocationsNodesListRequest object.

  Fields:
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class TpuProjectsLocationsNodesPatchRequest(_messages.Message):
  r"""A TpuProjectsLocationsNodesPatchRequest object.

  Fields:
    name: Output only. Immutable. The name of the TPU.
    node: A Node resource to be passed as the request body.
    updateMask: Required. Mask of fields from Node to update. Supported
      fields: [description, tags, labels, metadata,
      network_config.enable_external_ips].
  """

  name = _messages.StringField(1, required=True)
  node = _messages.MessageField('Node', 2)
  updateMask = _messages.StringField(3)


class TpuProjectsLocationsNodesPerformMaintenanceRequest(_messages.Message):
  r"""A TpuProjectsLocationsNodesPerformMaintenanceRequest object.

  Fields:
    name: Required. The resource name.
    performMaintenanceRequest: A PerformMaintenanceRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  performMaintenanceRequest = _messages.MessageField('PerformMaintenanceRequest', 2)


class TpuProjectsLocationsNodesSimulateMaintenanceEventRequest(_messages.Message):
  r"""A TpuProjectsLocationsNodesSimulateMaintenanceEventRequest object.

  Fields:
    name: Required. The resource name.
    simulateMaintenanceEventRequest: A SimulateMaintenanceEventRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  simulateMaintenanceEventRequest = _messages.MessageField('SimulateMaintenanceEventRequest', 2)


class TpuProjectsLocationsNodesStartRequest(_messages.Message):
  r"""A TpuProjectsLocationsNodesStartRequest object.

  Fields:
    name: Required. The resource name.
    startNodeRequest: A StartNodeRequest resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  startNodeRequest = _messages.MessageField('StartNodeRequest', 2)


class TpuProjectsLocationsNodesStopRequest(_messages.Message):
  r"""A TpuProjectsLocationsNodesStopRequest object.

  Fields:
    name: Required. The resource name.
    stopNodeRequest: A StopNodeRequest resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  stopNodeRequest = _messages.MessageField('StopNodeRequest', 2)


class TpuProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A TpuProjectsLocationsOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class TpuProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A TpuProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class TpuProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A TpuProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class TpuProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A TpuProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class TpuProjectsLocationsQueuedResourcesCreateRequest(_messages.Message):
  r"""A TpuProjectsLocationsQueuedResourcesCreateRequest object.

  Fields:
    parent: Required. The parent resource name.
    queuedResource: A QueuedResource resource to be passed as the request
      body.
    queuedResourceId: The unqualified resource name. Should follow the
      `^[A-Za-z0-9_.~+%-]+$` regex format.
    requestId: Idempotent request UUID.
  """

  parent = _messages.StringField(1, required=True)
  queuedResource = _messages.MessageField('QueuedResource', 2)
  queuedResourceId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class TpuProjectsLocationsQueuedResourcesDeleteRequest(_messages.Message):
  r"""A TpuProjectsLocationsQueuedResourcesDeleteRequest object.

  Fields:
    force: If set to true, all running nodes belonging to this queued resource
      will be deleted first and then the queued resource will be deleted.
      Otherwise (i.e. force=false), the queued resource will only be deleted
      if its nodes have already been deleted or the queued resource is in the
      ACCEPTED, FAILED, or SUSPENDED state.
    name: Required. The resource name.
    requestId: Idempotent request UUID.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class TpuProjectsLocationsQueuedResourcesGetRequest(_messages.Message):
  r"""A TpuProjectsLocationsQueuedResourcesGetRequest object.

  Fields:
    name: Required. The resource name.
  """

  name = _messages.StringField(1, required=True)


class TpuProjectsLocationsQueuedResourcesListRequest(_messages.Message):
  r"""A TpuProjectsLocationsQueuedResourcesListRequest object.

  Fields:
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class TpuProjectsLocationsQueuedResourcesPerformMaintenanceQueuedResourceRequest(_messages.Message):
  r"""A
  TpuProjectsLocationsQueuedResourcesPerformMaintenanceQueuedResourceRequest
  object.

  Fields:
    name: Required. The name of the QueuedResource which holds the nodes to
      perform maintenance on.
    performMaintenanceQueuedResourceRequest: A
      PerformMaintenanceQueuedResourceRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  performMaintenanceQueuedResourceRequest = _messages.MessageField('PerformMaintenanceQueuedResourceRequest', 2)


class TpuProjectsLocationsQueuedResourcesResetRequest(_messages.Message):
  r"""A TpuProjectsLocationsQueuedResourcesResetRequest object.

  Fields:
    name: Required. The name of the queued resource.
    resetQueuedResourceRequest: A ResetQueuedResourceRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  resetQueuedResourceRequest = _messages.MessageField('ResetQueuedResourceRequest', 2)


class TpuProjectsLocationsReservationsListRequest(_messages.Message):
  r"""A TpuProjectsLocationsReservationsListRequest object.

  Fields:
    pageSize: The maximum number of items to return. Defaults to 0 if not
      specified, which means no limit.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent for reservations.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class TpuProjectsLocationsRuntimeVersionsGetRequest(_messages.Message):
  r"""A TpuProjectsLocationsRuntimeVersionsGetRequest object.

  Fields:
    name: Required. The resource name.
  """

  name = _messages.StringField(1, required=True)


class TpuProjectsLocationsRuntimeVersionsListRequest(_messages.Message):
  r"""A TpuProjectsLocationsRuntimeVersionsListRequest object.

  Fields:
    filter: List filter.
    orderBy: Sort results.
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class Traffic(_messages.Message):
  r"""Expected traffic between two coordinates.

  Fields:
    peakTraffic: Expected peak traffic.
  """

  peakTraffic = _messages.MessageField('PeakTraffic', 1)


class TrafficConfig(_messages.Message):
  r"""Network traffic configuration.

  Fields:
    anticipatedTrafficMatrix: Traffic Matrix for anticipated network traffic
  """

  anticipatedTrafficMatrix = _messages.MessageField('LogicalTrafficMatrix', 1)


class UpcomingMaintenance(_messages.Message):
  r"""Upcoming Maintenance notification information.

  Enums:
    MaintenanceStatusValueValuesEnum: The status of the maintenance.
    TypeValueValuesEnum: Defines the type of maintenance.

  Fields:
    canReschedule: Indicates if the maintenance can be customer triggered.
    latestWindowStartTime: The latest time for the planned maintenance window
      to start. This timestamp value is in RFC3339 text format.
    maintenanceStatus: The status of the maintenance.
    type: Defines the type of maintenance.
    windowEndTime: The time by which the maintenance disruption will be
      completed. This timestamp value is in RFC3339 text format.
    windowStartTime: The current start time of the maintenance window. This
      timestamp value is in RFC3339 text format.
  """

  class MaintenanceStatusValueValuesEnum(_messages.Enum):
    r"""The status of the maintenance.

    Values:
      UNKNOWN: Unknown maintenance status. Do not use this value.
      PENDING: There is pending maintenance.
      ONGOING: There is ongoing maintenance on this VM.
    """
    UNKNOWN = 0
    PENDING = 1
    ONGOING = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""Defines the type of maintenance.

    Values:
      UNKNOWN_TYPE: No type specified. Do not use this value.
      SCHEDULED: Scheduled maintenance (e.g. maintenance after uptime
        guarantee is complete).
      UNSCHEDULED: Unscheduled maintenance (e.g. emergency maintenance during
        uptime guarantee).
    """
    UNKNOWN_TYPE = 0
    SCHEDULED = 1
    UNSCHEDULED = 2

  canReschedule = _messages.BooleanField(1)
  latestWindowStartTime = _messages.StringField(2)
  maintenanceStatus = _messages.EnumField('MaintenanceStatusValueValuesEnum', 3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)
  windowEndTime = _messages.StringField(5)
  windowStartTime = _messages.StringField(6)


class Usage(_messages.Message):
  r"""Usage details of a reservation.

  Fields:
    total: The real-time value of usage within the reservation, with the unit
      specified in field capacity_units.
  """

  total = _messages.IntegerField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
