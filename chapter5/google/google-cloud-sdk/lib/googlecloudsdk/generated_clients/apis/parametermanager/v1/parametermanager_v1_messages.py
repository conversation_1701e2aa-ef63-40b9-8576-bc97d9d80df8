"""Generated message classes for parametermanager version v1.

Parameter Manager is a single source of truth to store, access and manage the
lifecycle of your workload parameters. Parameter Manager aims to make
management of sensitive application parameters effortless for customers
without diminishing focus on security.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'parametermanager'


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListParameterVersionsResponse(_messages.Message):
  r"""Message for response to listing ParameterVersions

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    parameterVersions: The list of ParameterVersions
    unreachable: Unordered list. Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  parameterVersions = _messages.MessageField('ParameterVersion', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListParametersResponse(_messages.Message):
  r"""Message for response to listing Parameters

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    parameters: The list of Parameters
    unreachable: Unordered list. Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  parameters = _messages.MessageField('Parameter', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Parameter(_messages.Message):
  r"""Message describing Parameter resource

  Enums:
    FormatValueValuesEnum: Optional. Specifies the format of a Parameter.

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    createTime: Output only. [Output only] Create time stamp
    format: Optional. Specifies the format of a Parameter.
    kmsKey: Optional. Customer managed encryption key (CMEK) to use for
      encrypting the Parameter Versions. If not set, the default Google-
      managed encryption key will be used. Cloud KMS CryptoKeys must reside in
      the same location as the Parameter. The expected format is
      `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
    labels: Optional. Labels as key value pairs
    name: Identifier. [Output only] The resource name of the Parameter in the
      format `projects/*/locations/*/parameters/*`.
    policyMember: Output only. [Output-only] policy member strings of a Google
      Cloud resource.
    updateTime: Output only. [Output only] Update time stamp
  """

  class FormatValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the format of a Parameter.

    Values:
      PARAMETER_FORMAT_UNSPECIFIED: The default / unset value. The API will
        default to the UNFORMATTED format.
      UNFORMATTED: Unformatted.
      YAML: YAML format.
      JSON: JSON format.
    """
    PARAMETER_FORMAT_UNSPECIFIED = 0
    UNFORMATTED = 1
    YAML = 2
    JSON = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  format = _messages.EnumField('FormatValueValuesEnum', 2)
  kmsKey = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  policyMember = _messages.MessageField('ResourcePolicyMember', 6)
  updateTime = _messages.StringField(7)


class ParameterVersion(_messages.Message):
  r"""Message describing ParameterVersion resource

  Fields:
    createTime: Output only. [Output only] Create time stamp
    disabled: Optional. Disabled boolean to determine if a ParameterVersion
      acts as a metadata only resource (payload is never returned if disabled
      is true). If true any calls will always default to BASIC view even if
      the user explicitly passes FULL view as part of the request. A render
      call on a disabled resource fails with an error. Default value is False.
    kmsKeyVersion: Optional. Output only. [Output only] The resource name of
      the KMS key version used to encrypt the ParameterVersion payload. This
      field is populated only if the Parameter resource has customer managed
      encryption key (CMEK) configured.
    name: Identifier. [Output only] The resource name of the ParameterVersion
      in the format `projects/*/locations/*/parameters/*/versions/*`.
    payload: Required. Immutable. Payload content of a ParameterVersion
      resource. This is only returned when the request provides the View value
      of FULL (default for GET request).
    updateTime: Output only. [Output only] Update time stamp
  """

  createTime = _messages.StringField(1)
  disabled = _messages.BooleanField(2)
  kmsKeyVersion = _messages.StringField(3)
  name = _messages.StringField(4)
  payload = _messages.MessageField('ParameterVersionPayload', 5)
  updateTime = _messages.StringField(6)


class ParameterVersionPayload(_messages.Message):
  r"""Message for storing a ParameterVersion resource's payload data

  Fields:
    data: Required. bytes data for storing payload.
  """

  data = _messages.BytesField(1)


class ParametermanagerProjectsLocationsGetRequest(_messages.Message):
  r"""A ParametermanagerProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ParametermanagerProjectsLocationsListRequest(_messages.Message):
  r"""A ParametermanagerProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class ParametermanagerProjectsLocationsParametersCreateRequest(_messages.Message):
  r"""A ParametermanagerProjectsLocationsParametersCreateRequest object.

  Fields:
    parameter: A Parameter resource to be passed as the request body.
    parameterId: Required. Id of the Parameter resource
    parent: Required. Value for parent in the format `projects/*/locations/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  parameter = _messages.MessageField('Parameter', 1)
  parameterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class ParametermanagerProjectsLocationsParametersDeleteRequest(_messages.Message):
  r"""A ParametermanagerProjectsLocationsParametersDeleteRequest object.

  Fields:
    name: Required. Name of the resource in the format
      `projects/*/locations/*/parameters/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class ParametermanagerProjectsLocationsParametersGetRequest(_messages.Message):
  r"""A ParametermanagerProjectsLocationsParametersGetRequest object.

  Fields:
    name: Required. Name of the resource in the format
      `projects/*/locations/*/parameters/*`.
  """

  name = _messages.StringField(1, required=True)


class ParametermanagerProjectsLocationsParametersListRequest(_messages.Message):
  r"""A ParametermanagerProjectsLocationsParametersListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListParametersRequest in the format
      `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ParametermanagerProjectsLocationsParametersPatchRequest(_messages.Message):
  r"""A ParametermanagerProjectsLocationsParametersPatchRequest object.

  Fields:
    name: Identifier. [Output only] The resource name of the Parameter in the
      format `projects/*/locations/*/parameters/*`.
    parameter: A Parameter resource to be passed as the request body.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Parameter resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A mutable field will be overwritten if it is in the mask. If
      the user does not provide a mask then all mutable fields present in the
      request will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  parameter = _messages.MessageField('Parameter', 2)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class ParametermanagerProjectsLocationsParametersVersionsCreateRequest(_messages.Message):
  r"""A ParametermanagerProjectsLocationsParametersVersionsCreateRequest
  object.

  Fields:
    parameterVersion: A ParameterVersion resource to be passed as the request
      body.
    parameterVersionId: Required. Id of the ParameterVersion resource
    parent: Required. Value for parent in the format
      `projects/*/locations/*/parameters/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  parameterVersion = _messages.MessageField('ParameterVersion', 1)
  parameterVersionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class ParametermanagerProjectsLocationsParametersVersionsDeleteRequest(_messages.Message):
  r"""A ParametermanagerProjectsLocationsParametersVersionsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource in the format
      `projects/*/locations/*/parameters/*/versions/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class ParametermanagerProjectsLocationsParametersVersionsGetRequest(_messages.Message):
  r"""A ParametermanagerProjectsLocationsParametersVersionsGetRequest object.

  Enums:
    ViewValueValuesEnum: Optional. View of the ParameterVersion. In the
      default FULL view, all metadata & payload associated with the
      ParameterVersion will be returned.

  Fields:
    name: Required. Name of the resource in the format
      `projects/*/locations/*/parameters/*/versions/*`.
    view: Optional. View of the ParameterVersion. In the default FULL view,
      all metadata & payload associated with the ParameterVersion will be
      returned.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. View of the ParameterVersion. In the default FULL view, all
    metadata & payload associated with the ParameterVersion will be returned.

    Values:
      VIEW_UNSPECIFIED: The default / unset value. The API will default to the
        FULL view..
      BASIC: Include only the metadata for the resource.
      FULL: Include metadata & other relevant payload data as well. This is
        the default view.
    """
    VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class ParametermanagerProjectsLocationsParametersVersionsListRequest(_messages.Message):
  r"""A ParametermanagerProjectsLocationsParametersVersionsListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListParameterVersionsRequest in the
      format `projects/*/locations/*/parameters/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ParametermanagerProjectsLocationsParametersVersionsPatchRequest(_messages.Message):
  r"""A ParametermanagerProjectsLocationsParametersVersionsPatchRequest
  object.

  Fields:
    name: Identifier. [Output only] The resource name of the ParameterVersion
      in the format `projects/*/locations/*/parameters/*/versions/*`.
    parameterVersion: A ParameterVersion resource to be passed as the request
      body.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the ParameterVersion resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A mutable field will be overwritten if it is in the mask. If
      the user does not provide a mask then all mutable fields present in the
      request will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  parameterVersion = _messages.MessageField('ParameterVersion', 2)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class ParametermanagerProjectsLocationsParametersVersionsRenderRequest(_messages.Message):
  r"""A ParametermanagerProjectsLocationsParametersVersionsRenderRequest
  object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class RenderParameterVersionResponse(_messages.Message):
  r"""Message describing RenderParameterVersionResponse resource

  Fields:
    parameterVersion: Output only. Resource identifier of a ParameterVersion
      in the format `projects/*/locations/*/parameters/*/versions/*`.
    payload: Payload content of a ParameterVersion resource.
    renderedPayload: Output only. Server generated rendered version of the
      user provided payload data (ParameterVersionPayload) which has
      substitutions of all (if any) references to a SecretManager
      SecretVersion resources. This substitution only works for a Parameter
      which is in JSON or YAML format.
  """

  parameterVersion = _messages.StringField(1)
  payload = _messages.MessageField('ParameterVersionPayload', 2)
  renderedPayload = _messages.BytesField(3)


class ResourcePolicyMember(_messages.Message):
  r"""Output-only policy member strings of a Google Cloud resource's built-in
  identity.

  Fields:
    iamPolicyNamePrincipal: Output only. IAM policy binding member referring
      to a Google Cloud resource by user-assigned name
      (https://google.aip.dev/122). If a resource is deleted and recreated
      with the same name, the binding will be applicable to the new resource.
      Example: `principal://parametermanager.googleapis.com/projects/12345/nam
      e/locations/us-central1-a/parameters/my-parameter`
    iamPolicyUidPrincipal: Output only. IAM policy binding member referring to
      a Google Cloud resource by system-assigned unique identifier
      (https://google.aip.dev/148#uid). If a resource is deleted and recreated
      with the same name, the binding will not be applicable to the new
      resource Example: `principal://parametermanager.googleapis.com/projects/
      12345/uid/locations/us-central1-a/parameters/a918fed5`
  """

  iamPolicyNamePrincipal = _messages.StringField(1)
  iamPolicyUidPrincipal = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
