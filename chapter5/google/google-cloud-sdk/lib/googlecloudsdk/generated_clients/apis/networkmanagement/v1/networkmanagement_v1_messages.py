"""Generated message classes for networkmanagement version v1.

The Network Management API provides a collection of network performance
monitoring and diagnostic capabilities.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'networkmanagement'


class AbortInfo(_messages.Message):
  r"""Details of the final state "abort" and associated resource.

  Enums:
    CauseValueValuesEnum: Causes that the analysis is aborted.

  Fields:
    cause: Causes that the analysis is aborted.
    ipAddress: IP address that caused the abort.
    projectsMissingPermission: List of project IDs the user specified in the
      request but lacks access to. In this case, analysis is aborted with the
      PERMISSION_DENIED cause.
    resourceUri: URI of the resource that caused the abort.
  """

  class CauseValueValuesEnum(_messages.Enum):
    r"""Causes that the analysis is aborted.

    Values:
      CAUSE_UNSPECIFIED: Cause is unspecified.
      UNKNOWN_NETWORK: Aborted due to unknown network. Deprecated, not used in
        the new tests.
      UNKNOWN_PROJECT: Aborted because no project information can be derived
        from the test input. Deprecated, not used in the new tests.
      NO_EXTERNAL_IP: Aborted because traffic is sent from a public IP to an
        instance without an external IP. Deprecated, not used in the new
        tests.
      UNINTENDED_DESTINATION: Aborted because none of the traces matches
        destination information specified in the input test request.
        Deprecated, not used in the new tests.
      SOURCE_ENDPOINT_NOT_FOUND: Aborted because the source endpoint could not
        be found. Deprecated, not used in the new tests.
      MISMATCHED_SOURCE_NETWORK: Aborted because the source network does not
        match the source endpoint. Deprecated, not used in the new tests.
      DESTINATION_ENDPOINT_NOT_FOUND: Aborted because the destination endpoint
        could not be found. Deprecated, not used in the new tests.
      MISMATCHED_DESTINATION_NETWORK: Aborted because the destination network
        does not match the destination endpoint. Deprecated, not used in the
        new tests.
      UNKNOWN_IP: Aborted because no endpoint with the packet's destination IP
        address is found.
      GOOGLE_MANAGED_SERVICE_UNKNOWN_IP: Aborted because no endpoint with the
        packet's destination IP is found in the Google-managed project.
      SOURCE_IP_ADDRESS_NOT_IN_SOURCE_NETWORK: Aborted because the source IP
        address doesn't belong to any of the subnets of the source VPC
        network.
      PERMISSION_DENIED: Aborted because user lacks permission to access all
        or part of the network configurations required to run the test.
      PERMISSION_DENIED_NO_CLOUD_NAT_CONFIGS: Aborted because user lacks
        permission to access Cloud NAT configs required to run the test.
      PERMISSION_DENIED_NO_NEG_ENDPOINT_CONFIGS: Aborted because user lacks
        permission to access Network endpoint group endpoint configs required
        to run the test.
      PERMISSION_DENIED_NO_CLOUD_ROUTER_CONFIGS: Aborted because user lacks
        permission to access Cloud Router configs required to run the test.
      NO_SOURCE_LOCATION: Aborted because no valid source or destination
        endpoint is derived from the input test request.
      INVALID_ARGUMENT: Aborted because the source or destination endpoint
        specified in the request is invalid. Some examples: - The request
        might contain malformed resource URI, project ID, or IP address. - The
        request might contain inconsistent information (for example, the
        request might include both the instance and the network, but the
        instance might not have a NIC in that network).
      TRACE_TOO_LONG: Aborted because the number of steps in the trace exceeds
        a certain limit. It might be caused by a routing loop.
      INTERNAL_ERROR: Aborted due to internal server error.
      UNSUPPORTED: Aborted because the test scenario is not supported.
      MISMATCHED_IP_VERSION: Aborted because the source and destination
        resources have no common IP version.
      GKE_KONNECTIVITY_PROXY_UNSUPPORTED: Aborted because the connection
        between the control plane and the node of the source cluster is
        initiated by the node and managed by the Konnectivity proxy.
      RESOURCE_CONFIG_NOT_FOUND: Aborted because expected resource
        configuration was missing.
      VM_INSTANCE_CONFIG_NOT_FOUND: Aborted because expected VM instance
        configuration was missing.
      NETWORK_CONFIG_NOT_FOUND: Aborted because expected network configuration
        was missing.
      FIREWALL_CONFIG_NOT_FOUND: Aborted because expected firewall
        configuration was missing.
      ROUTE_CONFIG_NOT_FOUND: Aborted because expected route configuration was
        missing.
      GOOGLE_MANAGED_SERVICE_AMBIGUOUS_PSC_ENDPOINT: Aborted because PSC
        endpoint selection for the Google-managed service is ambiguous
        (several PSC endpoints satisfy test input).
      GOOGLE_MANAGED_SERVICE_AMBIGUOUS_ENDPOINT: Aborted because endpoint
        selection for the Google-managed service is ambiguous (several
        endpoints satisfy test input).
      SOURCE_PSC_CLOUD_SQL_UNSUPPORTED: Aborted because tests with a PSC-based
        Cloud SQL instance as a source are not supported.
      SOURCE_REDIS_CLUSTER_UNSUPPORTED: Aborted because tests with a Redis
        Cluster as a source are not supported.
      SOURCE_REDIS_INSTANCE_UNSUPPORTED: Aborted because tests with a Redis
        Instance as a source are not supported.
      SOURCE_FORWARDING_RULE_UNSUPPORTED: Aborted because tests with a
        forwarding rule as a source are not supported.
      NON_ROUTABLE_IP_ADDRESS: Aborted because one of the endpoints is a non-
        routable IP address (loopback, link-local, etc).
      UNKNOWN_ISSUE_IN_GOOGLE_MANAGED_PROJECT: Aborted due to an unknown issue
        in the Google-managed project.
      UNSUPPORTED_GOOGLE_MANAGED_PROJECT_CONFIG: Aborted due to an unsupported
        configuration of the Google-managed project.
      NO_SERVERLESS_IP_RANGES: Aborted because the source endpoint is a Cloud
        Run revision with direct VPC access enabled, but there are no reserved
        serverless IP ranges.
    """
    CAUSE_UNSPECIFIED = 0
    UNKNOWN_NETWORK = 1
    UNKNOWN_PROJECT = 2
    NO_EXTERNAL_IP = 3
    UNINTENDED_DESTINATION = 4
    SOURCE_ENDPOINT_NOT_FOUND = 5
    MISMATCHED_SOURCE_NETWORK = 6
    DESTINATION_ENDPOINT_NOT_FOUND = 7
    MISMATCHED_DESTINATION_NETWORK = 8
    UNKNOWN_IP = 9
    GOOGLE_MANAGED_SERVICE_UNKNOWN_IP = 10
    SOURCE_IP_ADDRESS_NOT_IN_SOURCE_NETWORK = 11
    PERMISSION_DENIED = 12
    PERMISSION_DENIED_NO_CLOUD_NAT_CONFIGS = 13
    PERMISSION_DENIED_NO_NEG_ENDPOINT_CONFIGS = 14
    PERMISSION_DENIED_NO_CLOUD_ROUTER_CONFIGS = 15
    NO_SOURCE_LOCATION = 16
    INVALID_ARGUMENT = 17
    TRACE_TOO_LONG = 18
    INTERNAL_ERROR = 19
    UNSUPPORTED = 20
    MISMATCHED_IP_VERSION = 21
    GKE_KONNECTIVITY_PROXY_UNSUPPORTED = 22
    RESOURCE_CONFIG_NOT_FOUND = 23
    VM_INSTANCE_CONFIG_NOT_FOUND = 24
    NETWORK_CONFIG_NOT_FOUND = 25
    FIREWALL_CONFIG_NOT_FOUND = 26
    ROUTE_CONFIG_NOT_FOUND = 27
    GOOGLE_MANAGED_SERVICE_AMBIGUOUS_PSC_ENDPOINT = 28
    GOOGLE_MANAGED_SERVICE_AMBIGUOUS_ENDPOINT = 29
    SOURCE_PSC_CLOUD_SQL_UNSUPPORTED = 30
    SOURCE_REDIS_CLUSTER_UNSUPPORTED = 31
    SOURCE_REDIS_INSTANCE_UNSUPPORTED = 32
    SOURCE_FORWARDING_RULE_UNSUPPORTED = 33
    NON_ROUTABLE_IP_ADDRESS = 34
    UNKNOWN_ISSUE_IN_GOOGLE_MANAGED_PROJECT = 35
    UNSUPPORTED_GOOGLE_MANAGED_PROJECT_CONFIG = 36
    NO_SERVERLESS_IP_RANGES = 37

  cause = _messages.EnumField('CauseValueValuesEnum', 1)
  ipAddress = _messages.StringField(2)
  projectsMissingPermission = _messages.StringField(3, repeated=True)
  resourceUri = _messages.StringField(4)


class AppEngineVersionEndpoint(_messages.Message):
  r"""Wrapper for the App Engine service version attributes.

  Fields:
    uri: An [App Engine](https://cloud.google.com/appengine) [service
      version](https://cloud.google.com/appengine/docs/admin-
      api/reference/rest/v1/apps.services.versions) name.
  """

  uri = _messages.StringField(1)


class AppEngineVersionInfo(_messages.Message):
  r"""For display only. Metadata associated with an App Engine version.

  Fields:
    displayName: Name of an App Engine version.
    environment: App Engine execution environment for a version.
    runtime: Runtime of the App Engine version.
    uri: URI of an App Engine version.
  """

  displayName = _messages.StringField(1)
  environment = _messages.StringField(2)
  runtime = _messages.StringField(3)
  uri = _messages.StringField(4)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CloudFunctionEndpoint(_messages.Message):
  r"""Wrapper for Cloud Function attributes.

  Fields:
    uri: A [Cloud Function](https://cloud.google.com/functions) name.
  """

  uri = _messages.StringField(1)


class CloudFunctionInfo(_messages.Message):
  r"""For display only. Metadata associated with a Cloud Function.

  Fields:
    displayName: Name of a Cloud Function.
    location: Location in which the Cloud Function is deployed.
    uri: URI of a Cloud Function.
    versionId: Latest successfully deployed version id of the Cloud Function.
  """

  displayName = _messages.StringField(1)
  location = _messages.StringField(2)
  uri = _messages.StringField(3)
  versionId = _messages.IntegerField(4)


class CloudRunRevisionEndpoint(_messages.Message):
  r"""Wrapper for Cloud Run revision attributes.

  Fields:
    uri: A [Cloud Run](https://cloud.google.com/run) [revision](https://cloud.
      google.com/run/docs/reference/rest/v1/namespaces.revisions/get) URI. The
      format is: projects/{project}/locations/{location}/revisions/{revision}
  """

  uri = _messages.StringField(1)


class CloudRunRevisionInfo(_messages.Message):
  r"""For display only. Metadata associated with a Cloud Run revision.

  Fields:
    displayName: Name of a Cloud Run revision.
    location: Location in which this revision is deployed.
    serviceUri: URI of Cloud Run service this revision belongs to.
    uri: URI of a Cloud Run revision.
  """

  displayName = _messages.StringField(1)
  location = _messages.StringField(2)
  serviceUri = _messages.StringField(3)
  uri = _messages.StringField(4)


class CloudSQLInstanceInfo(_messages.Message):
  r"""For display only. Metadata associated with a Cloud SQL instance.

  Fields:
    displayName: Name of a Cloud SQL instance.
    externalIp: External IP address of a Cloud SQL instance.
    internalIp: Internal IP address of a Cloud SQL instance.
    networkUri: URI of a Cloud SQL instance network or empty string if the
      instance does not have one.
    region: Region in which the Cloud SQL instance is running.
    uri: URI of a Cloud SQL instance.
  """

  displayName = _messages.StringField(1)
  externalIp = _messages.StringField(2)
  internalIp = _messages.StringField(3)
  networkUri = _messages.StringField(4)
  region = _messages.StringField(5)
  uri = _messages.StringField(6)


class ConnectivityTest(_messages.Message):
  r"""A Connectivity Test for a network reachability analysis.

  Messages:
    LabelsValue: Resource labels to represent user-provided metadata.

  Fields:
    bypassFirewallChecks: Whether the analysis should skip firewall checking.
      Default value is false.
    createTime: Output only. The time the test was created.
    description: The user-supplied description of the Connectivity Test.
      Maximum of 512 characters.
    destination: Required. Destination specification of the Connectivity Test.
      You can use a combination of destination IP address, URI of a supported
      endpoint, project ID, or VPC network to identify the destination
      location. Reachability analysis proceeds even if the destination
      location is ambiguous. However, the test result might include endpoints
      or use a destination that you don't intend to test.
    displayName: Output only. The display name of a Connectivity Test.
    labels: Resource labels to represent user-provided metadata.
    name: Identifier. Unique name of the resource using the form:
      `projects/{project_id}/locations/global/connectivityTests/{test_id}`
    probingDetails: Output only. The probing details of this test from the
      latest run, present for applicable tests only. The details are updated
      when creating a new test, updating an existing test, or triggering a
      one-time rerun of an existing test.
    protocol: IP Protocol of the test. When not provided, "TCP" is assumed.
    reachabilityDetails: Output only. The reachability details of this test
      from the latest run. The details are updated when creating a new test,
      updating an existing test, or triggering a one-time rerun of an existing
      test.
    relatedProjects: Other projects that may be relevant for reachability
      analysis. This is applicable to scenarios where a test can cross project
      boundaries.
    returnReachabilityDetails: Output only. The reachability details of this
      test from the latest run for the return path. The details are updated
      when creating a new test, updating an existing test, or triggering a
      one-time rerun of an existing test.
    roundTrip: Whether run analysis for the return path from destination to
      source. Default value is false.
    source: Required. Source specification of the Connectivity Test. You can
      use a combination of source IP address, URI of a supported endpoint,
      project ID, or VPC network to identify the source location. Reachability
      analysis might proceed even if the source location is ambiguous.
      However, the test result might include endpoints or use a source that
      you don't intend to test.
    updateTime: Output only. The time the test's configuration was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels to represent user-provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bypassFirewallChecks = _messages.BooleanField(1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  destination = _messages.MessageField('Endpoint', 4)
  displayName = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  probingDetails = _messages.MessageField('ProbingDetails', 8)
  protocol = _messages.StringField(9)
  reachabilityDetails = _messages.MessageField('ReachabilityDetails', 10)
  relatedProjects = _messages.StringField(11, repeated=True)
  returnReachabilityDetails = _messages.MessageField('ReachabilityDetails', 12)
  roundTrip = _messages.BooleanField(13)
  source = _messages.MessageField('Endpoint', 14)
  updateTime = _messages.StringField(15)


class DeliverInfo(_messages.Message):
  r"""Details of the final state "deliver" and associated resource.

  Enums:
    TargetValueValuesEnum: Target type where the packet is delivered to.

  Fields:
    ipAddress: IP address of the target (if applicable).
    pscGoogleApiTarget: PSC Google API target the packet is delivered to (if
      applicable).
    resourceUri: URI of the resource that the packet is delivered to.
    storageBucket: Name of the Cloud Storage Bucket the packet is delivered to
      (if applicable).
    target: Target type where the packet is delivered to.
  """

  class TargetValueValuesEnum(_messages.Enum):
    r"""Target type where the packet is delivered to.

    Values:
      TARGET_UNSPECIFIED: Target not specified.
      INSTANCE: Target is a Compute Engine instance.
      INTERNET: Target is the internet.
      GOOGLE_API: Target is a Google API.
      GKE_MASTER: Target is a Google Kubernetes Engine cluster master.
      CLOUD_SQL_INSTANCE: Target is a Cloud SQL instance.
      PSC_PUBLISHED_SERVICE: Target is a published service that uses [Private
        Service Connect](https://cloud.google.com/vpc/docs/configure-private-
        service-connect-services).
      PSC_GOOGLE_API: Target is Google APIs that use [Private Service
        Connect](https://cloud.google.com/vpc/docs/configure-private-service-
        connect-apis).
      PSC_VPC_SC: Target is a VPC-SC that uses [Private Service
        Connect](https://cloud.google.com/vpc/docs/configure-private-service-
        connect-apis).
      SERVERLESS_NEG: Target is a serverless network endpoint group.
      STORAGE_BUCKET: Target is a Cloud Storage bucket.
      PRIVATE_NETWORK: Target is a private network. Used only for return
        traces.
      CLOUD_FUNCTION: Target is a Cloud Function. Used only for return traces.
      APP_ENGINE_VERSION: Target is a App Engine service version. Used only
        for return traces.
      CLOUD_RUN_REVISION: Target is a Cloud Run revision. Used only for return
        traces.
      GOOGLE_MANAGED_SERVICE: Target is a Google-managed service. Used only
        for return traces.
      REDIS_INSTANCE: Target is a Redis Instance.
      REDIS_CLUSTER: Target is a Redis Cluster.
    """
    TARGET_UNSPECIFIED = 0
    INSTANCE = 1
    INTERNET = 2
    GOOGLE_API = 3
    GKE_MASTER = 4
    CLOUD_SQL_INSTANCE = 5
    PSC_PUBLISHED_SERVICE = 6
    PSC_GOOGLE_API = 7
    PSC_VPC_SC = 8
    SERVERLESS_NEG = 9
    STORAGE_BUCKET = 10
    PRIVATE_NETWORK = 11
    CLOUD_FUNCTION = 12
    APP_ENGINE_VERSION = 13
    CLOUD_RUN_REVISION = 14
    GOOGLE_MANAGED_SERVICE = 15
    REDIS_INSTANCE = 16
    REDIS_CLUSTER = 17

  ipAddress = _messages.StringField(1)
  pscGoogleApiTarget = _messages.StringField(2)
  resourceUri = _messages.StringField(3)
  storageBucket = _messages.StringField(4)
  target = _messages.EnumField('TargetValueValuesEnum', 5)


class DirectVpcEgressConnectionInfo(_messages.Message):
  r"""For display only. Metadata associated with a serverless direct VPC
  egress connection.

  Fields:
    networkUri: URI of direct access network.
    region: Region in which the Direct VPC egress is deployed.
    selectedIpAddress: Selected starting IP address, from the selected IP
      range.
    selectedIpRange: Selected IP range.
    subnetworkUri: URI of direct access subnetwork.
  """

  networkUri = _messages.StringField(1)
  region = _messages.StringField(2)
  selectedIpAddress = _messages.StringField(3)
  selectedIpRange = _messages.StringField(4)
  subnetworkUri = _messages.StringField(5)


class DropInfo(_messages.Message):
  r"""Details of the final state "drop" and associated resource.

  Enums:
    CauseValueValuesEnum: Cause that the packet is dropped.

  Fields:
    cause: Cause that the packet is dropped.
    destinationIp: Destination IP address of the dropped packet (if relevant).
    region: Region of the dropped packet (if relevant).
    resourceUri: URI of the resource that caused the drop.
    sourceIp: Source IP address of the dropped packet (if relevant).
  """

  class CauseValueValuesEnum(_messages.Enum):
    r"""Cause that the packet is dropped.

    Values:
      CAUSE_UNSPECIFIED: Cause is unspecified.
      UNKNOWN_EXTERNAL_ADDRESS: Destination external address cannot be
        resolved to a known target. If the address is used in a Google Cloud
        project, provide the project ID as test input.
      FOREIGN_IP_DISALLOWED: A Compute Engine instance can only send or
        receive a packet with a foreign IP address if ip_forward is enabled.
      FIREWALL_RULE: Dropped due to a firewall rule, unless allowed due to
        connection tracking.
      NO_ROUTE: Dropped due to no matching routes.
      ROUTE_BLACKHOLE: Dropped due to invalid route. Route's next hop is a
        blackhole.
      ROUTE_WRONG_NETWORK: Packet is sent to a wrong (unintended) network.
        Example: you trace a packet from VM1:Network1 to VM2:Network2,
        however, the route configured in Network1 sends the packet destined
        for VM2's IP address to Network3.
      ROUTE_NEXT_HOP_IP_ADDRESS_NOT_RESOLVED: Route's next hop IP address
        cannot be resolved to a GCP resource.
      ROUTE_NEXT_HOP_RESOURCE_NOT_FOUND: Route's next hop resource is not
        found.
      ROUTE_NEXT_HOP_INSTANCE_WRONG_NETWORK: Route's next hop instance doesn't
        have a NIC in the route's network.
      ROUTE_NEXT_HOP_INSTANCE_NON_PRIMARY_IP: Route's next hop IP address is
        not a primary IP address of the next hop instance.
      ROUTE_NEXT_HOP_FORWARDING_RULE_IP_MISMATCH: Route's next hop forwarding
        rule doesn't match next hop IP address.
      ROUTE_NEXT_HOP_VPN_TUNNEL_NOT_ESTABLISHED: Route's next hop VPN tunnel
        is down (does not have valid IKE SAs).
      ROUTE_NEXT_HOP_FORWARDING_RULE_TYPE_INVALID: Route's next hop forwarding
        rule type is invalid (it's not a forwarding rule of the internal
        passthrough load balancer).
      NO_ROUTE_FROM_INTERNET_TO_PRIVATE_IPV6_ADDRESS: Packet is sent from the
        Internet or Google service to the private IPv6 address.
      NO_ROUTE_FROM_EXTERNAL_IPV6_SOURCE_TO_PRIVATE_IPV6_ADDRESS: Packet is
        sent from the external IPv6 source address of an instance to the
        private IPv6 address of an instance.
      VPN_TUNNEL_LOCAL_SELECTOR_MISMATCH: The packet does not match a policy-
        based VPN tunnel local selector.
      VPN_TUNNEL_REMOTE_SELECTOR_MISMATCH: The packet does not match a policy-
        based VPN tunnel remote selector.
      PRIVATE_TRAFFIC_TO_INTERNET: Packet with internal destination address
        sent to the internet gateway.
      PRIVATE_GOOGLE_ACCESS_DISALLOWED: Endpoint with only an internal IP
        address tries to access Google API and services, but Private Google
        Access is not enabled in the subnet or is not applicable.
      PRIVATE_GOOGLE_ACCESS_VIA_VPN_TUNNEL_UNSUPPORTED: Source endpoint tries
        to access Google API and services through the VPN tunnel to another
        network, but Private Google Access needs to be enabled in the source
        endpoint network.
      NO_EXTERNAL_ADDRESS: Endpoint with only an internal IP address tries to
        access external hosts, but there is no matching Cloud NAT gateway in
        the subnet.
      UNKNOWN_INTERNAL_ADDRESS: Destination internal address cannot be
        resolved to a known target. If this is a shared VPC scenario, verify
        if the service project ID is provided as test input. Otherwise, verify
        if the IP address is being used in the project.
      FORWARDING_RULE_MISMATCH: Forwarding rule's protocol and ports do not
        match the packet header.
      FORWARDING_RULE_NO_INSTANCES: Forwarding rule does not have backends
        configured.
      FIREWALL_BLOCKING_LOAD_BALANCER_BACKEND_HEALTH_CHECK: Firewalls block
        the health check probes to the backends and cause the backends to be
        unavailable for traffic from the load balancer. For more details, see
        [Health check firewall rules](https://cloud.google.com/load-
        balancing/docs/health-checks#firewall_rules).
      INGRESS_FIREWALL_TAGS_UNSUPPORTED_BY_DIRECT_VPC_EGRESS: Matching ingress
        firewall rules by network tags for packets sent via serverless VPC
        direct egress is unsupported. Behavior is undefined.
        https://cloud.google.com/run/docs/configuring/vpc-direct-
        vpc#limitations
      INSTANCE_NOT_RUNNING: Packet is sent from or to a Compute Engine
        instance that is not in a running state.
      GKE_CLUSTER_NOT_RUNNING: Packet sent from or to a GKE cluster that is
        not in running state.
      CLOUD_SQL_INSTANCE_NOT_RUNNING: Packet sent from or to a Cloud SQL
        instance that is not in running state.
      REDIS_INSTANCE_NOT_RUNNING: Packet sent from or to a Redis Instance that
        is not in running state.
      REDIS_CLUSTER_NOT_RUNNING: Packet sent from or to a Redis Cluster that
        is not in running state.
      TRAFFIC_TYPE_BLOCKED: The type of traffic is blocked and the user cannot
        configure a firewall rule to enable it. See [Always blocked
        traffic](https://cloud.google.com/vpc/docs/firewalls#blockedtraffic)
        for more details.
      GKE_MASTER_UNAUTHORIZED_ACCESS: Access to Google Kubernetes Engine
        cluster master's endpoint is not authorized. See [Access to the
        cluster endpoints](https://cloud.google.com/kubernetes-
        engine/docs/how-to/private-clusters#access_to_the_cluster_endpoints)
        for more details.
      CLOUD_SQL_INSTANCE_UNAUTHORIZED_ACCESS: Access to the Cloud SQL instance
        endpoint is not authorized. See [Authorizing with authorized
        networks](https://cloud.google.com/sql/docs/mysql/authorize-networks)
        for more details.
      DROPPED_INSIDE_GKE_SERVICE: Packet was dropped inside Google Kubernetes
        Engine Service.
      DROPPED_INSIDE_CLOUD_SQL_SERVICE: Packet was dropped inside Cloud SQL
        Service.
      GOOGLE_MANAGED_SERVICE_NO_PEERING: Packet was dropped because there is
        no peering between the originating network and the Google Managed
        Services Network.
      GOOGLE_MANAGED_SERVICE_NO_PSC_ENDPOINT: Packet was dropped because the
        Google-managed service uses Private Service Connect (PSC), but the PSC
        endpoint is not found in the project.
      GKE_PSC_ENDPOINT_MISSING: Packet was dropped because the GKE cluster
        uses Private Service Connect (PSC), but the PSC endpoint is not found
        in the project.
      CLOUD_SQL_INSTANCE_NO_IP_ADDRESS: Packet was dropped because the Cloud
        SQL instance has neither a private nor a public IP address.
      GKE_CONTROL_PLANE_REGION_MISMATCH: Packet was dropped because a GKE
        cluster private endpoint is unreachable from a region different from
        the cluster's region.
      PUBLIC_GKE_CONTROL_PLANE_TO_PRIVATE_DESTINATION: Packet sent from a
        public GKE cluster control plane to a private IP address.
      GKE_CONTROL_PLANE_NO_ROUTE: Packet was dropped because there is no route
        from a GKE cluster control plane to a destination network.
      CLOUD_SQL_INSTANCE_NOT_CONFIGURED_FOR_EXTERNAL_TRAFFIC: Packet sent from
        a Cloud SQL instance to an external IP address is not allowed. The
        Cloud SQL instance is not configured to send packets to external IP
        addresses.
      PUBLIC_CLOUD_SQL_INSTANCE_TO_PRIVATE_DESTINATION: Packet sent from a
        Cloud SQL instance with only a public IP address to a private IP
        address.
      CLOUD_SQL_INSTANCE_NO_ROUTE: Packet was dropped because there is no
        route from a Cloud SQL instance to a destination network.
      CLOUD_SQL_CONNECTOR_REQUIRED: Packet was dropped because the Cloud SQL
        instance requires all connections to use Cloud SQL connectors and to
        target the Cloud SQL proxy port (3307).
      CLOUD_FUNCTION_NOT_ACTIVE: Packet could be dropped because the Cloud
        Function is not in an active status.
      VPC_CONNECTOR_NOT_SET: Packet could be dropped because no VPC connector
        is set.
      VPC_CONNECTOR_NOT_RUNNING: Packet could be dropped because the VPC
        connector is not in a running state.
      VPC_CONNECTOR_SERVERLESS_TRAFFIC_BLOCKED: Packet could be dropped
        because the traffic from the serverless service to the VPC connector
        is not allowed.
      VPC_CONNECTOR_HEALTH_CHECK_TRAFFIC_BLOCKED: Packet could be dropped
        because the health check traffic to the VPC connector is not allowed.
      FORWARDING_RULE_REGION_MISMATCH: Packet could be dropped because it was
        sent from a different region to a regional forwarding without global
        access.
      PSC_CONNECTION_NOT_ACCEPTED: The Private Service Connect endpoint is in
        a project that is not approved to connect to the service.
      PSC_ENDPOINT_ACCESSED_FROM_PEERED_NETWORK: The packet is sent to the
        Private Service Connect endpoint over the peering, but [it's not
        supported](https://cloud.google.com/vpc/docs/configure-private-
        service-connect-services#on-premises).
      PSC_NEG_PRODUCER_ENDPOINT_NO_GLOBAL_ACCESS: The packet is sent to the
        Private Service Connect backend (network endpoint group), but the
        producer PSC forwarding rule does not have global access enabled.
      PSC_NEG_PRODUCER_FORWARDING_RULE_MULTIPLE_PORTS: The packet is sent to
        the Private Service Connect backend (network endpoint group), but the
        producer PSC forwarding rule has multiple ports specified.
      CLOUD_SQL_PSC_NEG_UNSUPPORTED: The packet is sent to the Private Service
        Connect backend (network endpoint group) targeting a Cloud SQL service
        attachment, but this configuration is not supported.
      NO_NAT_SUBNETS_FOR_PSC_SERVICE_ATTACHMENT: No NAT subnets are defined
        for the PSC service attachment.
      PSC_TRANSITIVITY_NOT_PROPAGATED: PSC endpoint is accessed via NCC, but
        PSC transitivity configuration is not yet propagated.
      HYBRID_NEG_NON_DYNAMIC_ROUTE_MATCHED: The packet sent from the hybrid
        NEG proxy matches a non-dynamic route, but such a configuration is not
        supported.
      HYBRID_NEG_NON_LOCAL_DYNAMIC_ROUTE_MATCHED: The packet sent from the
        hybrid NEG proxy matches a dynamic route with a next hop in a
        different region, but such a configuration is not supported.
      CLOUD_RUN_REVISION_NOT_READY: Packet sent from a Cloud Run revision that
        is not ready.
      DROPPED_INSIDE_PSC_SERVICE_PRODUCER: Packet was dropped inside Private
        Service Connect service producer.
      LOAD_BALANCER_HAS_NO_PROXY_SUBNET: Packet sent to a load balancer, which
        requires a proxy-only subnet and the subnet is not found.
      CLOUD_NAT_NO_ADDRESSES: Packet sent to Cloud Nat without active NAT IPs.
      ROUTING_LOOP: Packet is stuck in a routing loop.
      DROPPED_INSIDE_GOOGLE_MANAGED_SERVICE: Packet is dropped inside a
        Google-managed service due to being delivered in return trace to an
        endpoint that doesn't match the endpoint the packet was sent from in
        forward trace. Used only for return traces.
      LOAD_BALANCER_BACKEND_INVALID_NETWORK: Packet is dropped due to a load
        balancer backend instance not having a network interface in the
        network expected by the load balancer.
      BACKEND_SERVICE_NAMED_PORT_NOT_DEFINED: Packet is dropped due to a
        backend service named port not being defined on the instance group
        level.
      DESTINATION_IS_PRIVATE_NAT_IP_RANGE: Packet is dropped due to a
        destination IP range being part of a Private NAT IP range.
      DROPPED_INSIDE_REDIS_INSTANCE_SERVICE: Generic drop cause for a packet
        being dropped inside a Redis Instance service project.
      REDIS_INSTANCE_UNSUPPORTED_PORT: Packet is dropped due to an unsupported
        port being used to connect to a Redis Instance. Port 6379 should be
        used to connect to a Redis Instance.
      REDIS_INSTANCE_CONNECTING_FROM_PUPI_ADDRESS: Packet is dropped due to
        connecting from PUPI address to a PSA based Redis Instance.
      REDIS_INSTANCE_NO_ROUTE_TO_DESTINATION_NETWORK: Packet is dropped due to
        no route to the destination network.
      REDIS_INSTANCE_NO_EXTERNAL_IP: Redis Instance does not have an external
        IP address.
      REDIS_INSTANCE_UNSUPPORTED_PROTOCOL: Packet is dropped due to an
        unsupported protocol being used to connect to a Redis Instance. Only
        TCP connections are accepted by a Redis Instance.
      DROPPED_INSIDE_REDIS_CLUSTER_SERVICE: Generic drop cause for a packet
        being dropped inside a Redis Cluster service project.
      REDIS_CLUSTER_UNSUPPORTED_PORT: Packet is dropped due to an unsupported
        port being used to connect to a Redis Cluster. Ports 6379 and 11000 to
        13047 should be used to connect to a Redis Cluster.
      REDIS_CLUSTER_NO_EXTERNAL_IP: Redis Cluster does not have an external IP
        address.
      REDIS_CLUSTER_UNSUPPORTED_PROTOCOL: Packet is dropped due to an
        unsupported protocol being used to connect to a Redis Cluster. Only
        TCP connections are accepted by a Redis Cluster.
      NO_ADVERTISED_ROUTE_TO_GCP_DESTINATION: Packet from the non-GCP (on-
        prem) or unknown GCP network is dropped due to the destination IP
        address not belonging to any IP prefix advertised via BGP by the Cloud
        Router.
      NO_TRAFFIC_SELECTOR_TO_GCP_DESTINATION: Packet from the non-GCP (on-
        prem) or unknown GCP network is dropped due to the destination IP
        address not belonging to any IP prefix included to the local traffic
        selector of the VPN tunnel.
      NO_KNOWN_ROUTE_FROM_PEERED_NETWORK_TO_DESTINATION: Packet from the
        unknown peered network is dropped due to no known route from the
        source network to the destination IP address.
      PRIVATE_NAT_TO_PSC_ENDPOINT_UNSUPPORTED: Sending packets processed by
        the Private NAT Gateways to the Private Service Connect endpoints is
        not supported.
      PSC_PORT_MAPPING_PORT_MISMATCH: Packet is sent to the PSC port mapping
        service, but its destination port does not match any port mapping
        rules.
      PSC_PORT_MAPPING_WITHOUT_PSC_CONNECTION_UNSUPPORTED: Sending packets
        directly to the PSC port mapping service without going through the PSC
        connection is not supported.
      UNSUPPORTED_ROUTE_MATCHED_FOR_NAT64_DESTINATION: Packet with destination
        IP address within the reserved NAT64 range is dropped due to matching
        a route of an unsupported type.
      TRAFFIC_FROM_HYBRID_ENDPOINT_TO_INTERNET_DISALLOWED: Packet could be
        dropped because hybrid endpoint like a VPN gateway or Interconnect is
        not allowed to send traffic to the Internet.
      NO_MATCHING_NAT64_GATEWAY: Packet with destination IP address within the
        reserved NAT64 range is dropped due to no matching NAT gateway in the
        subnet.
      LOAD_BALANCER_BACKEND_IP_VERSION_MISMATCH: Packet is dropped due to
        being sent to a backend of a passthrough load balancer that doesn't
        use the same IP version as the frontend.
      NO_KNOWN_ROUTE_FROM_NCC_NETWORK_TO_DESTINATION: Packet from the unknown
        NCC network is dropped due to no known route from the source network
        to the destination IP address.
    """
    CAUSE_UNSPECIFIED = 0
    UNKNOWN_EXTERNAL_ADDRESS = 1
    FOREIGN_IP_DISALLOWED = 2
    FIREWALL_RULE = 3
    NO_ROUTE = 4
    ROUTE_BLACKHOLE = 5
    ROUTE_WRONG_NETWORK = 6
    ROUTE_NEXT_HOP_IP_ADDRESS_NOT_RESOLVED = 7
    ROUTE_NEXT_HOP_RESOURCE_NOT_FOUND = 8
    ROUTE_NEXT_HOP_INSTANCE_WRONG_NETWORK = 9
    ROUTE_NEXT_HOP_INSTANCE_NON_PRIMARY_IP = 10
    ROUTE_NEXT_HOP_FORWARDING_RULE_IP_MISMATCH = 11
    ROUTE_NEXT_HOP_VPN_TUNNEL_NOT_ESTABLISHED = 12
    ROUTE_NEXT_HOP_FORWARDING_RULE_TYPE_INVALID = 13
    NO_ROUTE_FROM_INTERNET_TO_PRIVATE_IPV6_ADDRESS = 14
    NO_ROUTE_FROM_EXTERNAL_IPV6_SOURCE_TO_PRIVATE_IPV6_ADDRESS = 15
    VPN_TUNNEL_LOCAL_SELECTOR_MISMATCH = 16
    VPN_TUNNEL_REMOTE_SELECTOR_MISMATCH = 17
    PRIVATE_TRAFFIC_TO_INTERNET = 18
    PRIVATE_GOOGLE_ACCESS_DISALLOWED = 19
    PRIVATE_GOOGLE_ACCESS_VIA_VPN_TUNNEL_UNSUPPORTED = 20
    NO_EXTERNAL_ADDRESS = 21
    UNKNOWN_INTERNAL_ADDRESS = 22
    FORWARDING_RULE_MISMATCH = 23
    FORWARDING_RULE_NO_INSTANCES = 24
    FIREWALL_BLOCKING_LOAD_BALANCER_BACKEND_HEALTH_CHECK = 25
    INGRESS_FIREWALL_TAGS_UNSUPPORTED_BY_DIRECT_VPC_EGRESS = 26
    INSTANCE_NOT_RUNNING = 27
    GKE_CLUSTER_NOT_RUNNING = 28
    CLOUD_SQL_INSTANCE_NOT_RUNNING = 29
    REDIS_INSTANCE_NOT_RUNNING = 30
    REDIS_CLUSTER_NOT_RUNNING = 31
    TRAFFIC_TYPE_BLOCKED = 32
    GKE_MASTER_UNAUTHORIZED_ACCESS = 33
    CLOUD_SQL_INSTANCE_UNAUTHORIZED_ACCESS = 34
    DROPPED_INSIDE_GKE_SERVICE = 35
    DROPPED_INSIDE_CLOUD_SQL_SERVICE = 36
    GOOGLE_MANAGED_SERVICE_NO_PEERING = 37
    GOOGLE_MANAGED_SERVICE_NO_PSC_ENDPOINT = 38
    GKE_PSC_ENDPOINT_MISSING = 39
    CLOUD_SQL_INSTANCE_NO_IP_ADDRESS = 40
    GKE_CONTROL_PLANE_REGION_MISMATCH = 41
    PUBLIC_GKE_CONTROL_PLANE_TO_PRIVATE_DESTINATION = 42
    GKE_CONTROL_PLANE_NO_ROUTE = 43
    CLOUD_SQL_INSTANCE_NOT_CONFIGURED_FOR_EXTERNAL_TRAFFIC = 44
    PUBLIC_CLOUD_SQL_INSTANCE_TO_PRIVATE_DESTINATION = 45
    CLOUD_SQL_INSTANCE_NO_ROUTE = 46
    CLOUD_SQL_CONNECTOR_REQUIRED = 47
    CLOUD_FUNCTION_NOT_ACTIVE = 48
    VPC_CONNECTOR_NOT_SET = 49
    VPC_CONNECTOR_NOT_RUNNING = 50
    VPC_CONNECTOR_SERVERLESS_TRAFFIC_BLOCKED = 51
    VPC_CONNECTOR_HEALTH_CHECK_TRAFFIC_BLOCKED = 52
    FORWARDING_RULE_REGION_MISMATCH = 53
    PSC_CONNECTION_NOT_ACCEPTED = 54
    PSC_ENDPOINT_ACCESSED_FROM_PEERED_NETWORK = 55
    PSC_NEG_PRODUCER_ENDPOINT_NO_GLOBAL_ACCESS = 56
    PSC_NEG_PRODUCER_FORWARDING_RULE_MULTIPLE_PORTS = 57
    CLOUD_SQL_PSC_NEG_UNSUPPORTED = 58
    NO_NAT_SUBNETS_FOR_PSC_SERVICE_ATTACHMENT = 59
    PSC_TRANSITIVITY_NOT_PROPAGATED = 60
    HYBRID_NEG_NON_DYNAMIC_ROUTE_MATCHED = 61
    HYBRID_NEG_NON_LOCAL_DYNAMIC_ROUTE_MATCHED = 62
    CLOUD_RUN_REVISION_NOT_READY = 63
    DROPPED_INSIDE_PSC_SERVICE_PRODUCER = 64
    LOAD_BALANCER_HAS_NO_PROXY_SUBNET = 65
    CLOUD_NAT_NO_ADDRESSES = 66
    ROUTING_LOOP = 67
    DROPPED_INSIDE_GOOGLE_MANAGED_SERVICE = 68
    LOAD_BALANCER_BACKEND_INVALID_NETWORK = 69
    BACKEND_SERVICE_NAMED_PORT_NOT_DEFINED = 70
    DESTINATION_IS_PRIVATE_NAT_IP_RANGE = 71
    DROPPED_INSIDE_REDIS_INSTANCE_SERVICE = 72
    REDIS_INSTANCE_UNSUPPORTED_PORT = 73
    REDIS_INSTANCE_CONNECTING_FROM_PUPI_ADDRESS = 74
    REDIS_INSTANCE_NO_ROUTE_TO_DESTINATION_NETWORK = 75
    REDIS_INSTANCE_NO_EXTERNAL_IP = 76
    REDIS_INSTANCE_UNSUPPORTED_PROTOCOL = 77
    DROPPED_INSIDE_REDIS_CLUSTER_SERVICE = 78
    REDIS_CLUSTER_UNSUPPORTED_PORT = 79
    REDIS_CLUSTER_NO_EXTERNAL_IP = 80
    REDIS_CLUSTER_UNSUPPORTED_PROTOCOL = 81
    NO_ADVERTISED_ROUTE_TO_GCP_DESTINATION = 82
    NO_TRAFFIC_SELECTOR_TO_GCP_DESTINATION = 83
    NO_KNOWN_ROUTE_FROM_PEERED_NETWORK_TO_DESTINATION = 84
    PRIVATE_NAT_TO_PSC_ENDPOINT_UNSUPPORTED = 85
    PSC_PORT_MAPPING_PORT_MISMATCH = 86
    PSC_PORT_MAPPING_WITHOUT_PSC_CONNECTION_UNSUPPORTED = 87
    UNSUPPORTED_ROUTE_MATCHED_FOR_NAT64_DESTINATION = 88
    TRAFFIC_FROM_HYBRID_ENDPOINT_TO_INTERNET_DISALLOWED = 89
    NO_MATCHING_NAT64_GATEWAY = 90
    LOAD_BALANCER_BACKEND_IP_VERSION_MISMATCH = 91
    NO_KNOWN_ROUTE_FROM_NCC_NETWORK_TO_DESTINATION = 92

  cause = _messages.EnumField('CauseValueValuesEnum', 1)
  destinationIp = _messages.StringField(2)
  region = _messages.StringField(3)
  resourceUri = _messages.StringField(4)
  sourceIp = _messages.StringField(5)


class EdgeLocation(_messages.Message):
  r"""Representation of a network edge location as per
  https://cloud.google.com/vpc/docs/edge-locations.

  Fields:
    metropolitanArea: Name of the metropolitan area.
  """

  metropolitanArea = _messages.StringField(1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Endpoint(_messages.Message):
  r"""Source or destination of the Connectivity Test.

  Enums:
    ForwardingRuleTargetValueValuesEnum: Output only. Specifies the type of
      the target of the forwarding rule.
    LoadBalancerTypeValueValuesEnum: Output only. Type of the load balancer
      the forwarding rule points to.
    NetworkTypeValueValuesEnum: Type of the network where the endpoint is
      located. Applicable only to source endpoint, as destination network type
      can be inferred from the source.

  Fields:
    appEngineVersion: An [App Engine](https://cloud.google.com/appengine)
      [service version](https://cloud.google.com/appengine/docs/admin-
      api/reference/rest/v1/apps.services.versions). Applicable only to source
      endpoint.
    cloudFunction: A [Cloud Function](https://cloud.google.com/functions).
      Applicable only to source endpoint.
    cloudRunRevision: A [Cloud Run](https://cloud.google.com/run) [revision](h
      ttps://cloud.google.com/run/docs/reference/rest/v1/namespaces.revisions/
      get) Applicable only to source endpoint.
    cloudSqlInstance: A [Cloud SQL](https://cloud.google.com/sql) instance
      URI.
    forwardingRule: A forwarding rule and its corresponding IP address
      represent the frontend configuration of a Google Cloud load balancer.
      Forwarding rules are also used for protocol forwarding, Private Service
      Connect and other network services to provide forwarding information in
      the control plane. Applicable only to destination endpoint. Format:
      projects/{project}/global/forwardingRules/{id} or
      projects/{project}/regions/{region}/forwardingRules/{id}
    forwardingRuleTarget: Output only. Specifies the type of the target of the
      forwarding rule.
    fqdn: DNS endpoint of [Google Kubernetes Engine cluster control
      plane](https://cloud.google.com/kubernetes-engine/docs/concepts/cluster-
      architecture). Requires gke_master_cluster to be set, can't be used
      simultaneoulsly with ip_address or network. Applicable only to
      destination endpoint.
    gkeMasterCluster: A cluster URI for [Google Kubernetes Engine cluster
      control plane](https://cloud.google.com/kubernetes-
      engine/docs/concepts/cluster-architecture).
    instance: A Compute Engine instance URI.
    ipAddress: The IP address of the endpoint, which can be an external or
      internal IP.
    loadBalancerId: Output only. ID of the load balancer the forwarding rule
      points to. Empty for forwarding rules not related to load balancers.
    loadBalancerType: Output only. Type of the load balancer the forwarding
      rule points to.
    network: A VPC network URI.
    networkType: Type of the network where the endpoint is located. Applicable
      only to source endpoint, as destination network type can be inferred
      from the source.
    port: The IP protocol port of the endpoint. Only applicable when protocol
      is TCP or UDP.
    projectId: Project ID where the endpoint is located. The project ID can be
      derived from the URI if you provide a endpoint or network URI. The
      following are two cases where you may need to provide the project ID: 1.
      Only the IP address is specified, and the IP address is within a Google
      Cloud project. 2. When you are using Shared VPC and the IP address that
      you provide is from the service project. In this case, the network that
      the IP address resides in is defined in the host project.
    redisCluster: A [Redis
      Cluster](https://cloud.google.com/memorystore/docs/cluster) URI.
      Applicable only to destination endpoint.
    redisInstance: A [Redis
      Instance](https://cloud.google.com/memorystore/docs/redis) URI.
      Applicable only to destination endpoint.
  """

  class ForwardingRuleTargetValueValuesEnum(_messages.Enum):
    r"""Output only. Specifies the type of the target of the forwarding rule.

    Values:
      FORWARDING_RULE_TARGET_UNSPECIFIED: Forwarding rule target is unknown.
      INSTANCE: Compute Engine instance for protocol forwarding.
      LOAD_BALANCER: Load Balancer. The specific type can be found from
        load_balancer_type.
      VPN_GATEWAY: Classic Cloud VPN Gateway.
      PSC: Forwarding Rule is a Private Service Connect endpoint.
    """
    FORWARDING_RULE_TARGET_UNSPECIFIED = 0
    INSTANCE = 1
    LOAD_BALANCER = 2
    VPN_GATEWAY = 3
    PSC = 4

  class LoadBalancerTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the load balancer the forwarding rule points to.

    Values:
      LOAD_BALANCER_TYPE_UNSPECIFIED: Forwarding rule points to a different
        target than a load balancer or a load balancer type is unknown.
      HTTPS_ADVANCED_LOAD_BALANCER: Global external HTTP(S) load balancer.
      HTTPS_LOAD_BALANCER: Global external HTTP(S) load balancer (classic)
      REGIONAL_HTTPS_LOAD_BALANCER: Regional external HTTP(S) load balancer.
      INTERNAL_HTTPS_LOAD_BALANCER: Internal HTTP(S) load balancer.
      SSL_PROXY_LOAD_BALANCER: External SSL proxy load balancer.
      TCP_PROXY_LOAD_BALANCER: External TCP proxy load balancer.
      INTERNAL_TCP_PROXY_LOAD_BALANCER: Internal regional TCP proxy load
        balancer.
      NETWORK_LOAD_BALANCER: External TCP/UDP Network load balancer.
      LEGACY_NETWORK_LOAD_BALANCER: Target-pool based external TCP/UDP Network
        load balancer.
      TCP_UDP_INTERNAL_LOAD_BALANCER: Internal TCP/UDP load balancer.
    """
    LOAD_BALANCER_TYPE_UNSPECIFIED = 0
    HTTPS_ADVANCED_LOAD_BALANCER = 1
    HTTPS_LOAD_BALANCER = 2
    REGIONAL_HTTPS_LOAD_BALANCER = 3
    INTERNAL_HTTPS_LOAD_BALANCER = 4
    SSL_PROXY_LOAD_BALANCER = 5
    TCP_PROXY_LOAD_BALANCER = 6
    INTERNAL_TCP_PROXY_LOAD_BALANCER = 7
    NETWORK_LOAD_BALANCER = 8
    LEGACY_NETWORK_LOAD_BALANCER = 9
    TCP_UDP_INTERNAL_LOAD_BALANCER = 10

  class NetworkTypeValueValuesEnum(_messages.Enum):
    r"""Type of the network where the endpoint is located. Applicable only to
    source endpoint, as destination network type can be inferred from the
    source.

    Values:
      NETWORK_TYPE_UNSPECIFIED: Default type if unspecified.
      GCP_NETWORK: A network hosted within Google Cloud. To receive more
        detailed output, specify the URI for the source or destination
        network.
      NON_GCP_NETWORK: A network hosted outside of Google Cloud. This can be
        an on-premises network, an internet resource or a network hosted by
        another cloud provider.
    """
    NETWORK_TYPE_UNSPECIFIED = 0
    GCP_NETWORK = 1
    NON_GCP_NETWORK = 2

  appEngineVersion = _messages.MessageField('AppEngineVersionEndpoint', 1)
  cloudFunction = _messages.MessageField('CloudFunctionEndpoint', 2)
  cloudRunRevision = _messages.MessageField('CloudRunRevisionEndpoint', 3)
  cloudSqlInstance = _messages.StringField(4)
  forwardingRule = _messages.StringField(5)
  forwardingRuleTarget = _messages.EnumField('ForwardingRuleTargetValueValuesEnum', 6)
  fqdn = _messages.StringField(7)
  gkeMasterCluster = _messages.StringField(8)
  instance = _messages.StringField(9)
  ipAddress = _messages.StringField(10)
  loadBalancerId = _messages.StringField(11)
  loadBalancerType = _messages.EnumField('LoadBalancerTypeValueValuesEnum', 12)
  network = _messages.StringField(13)
  networkType = _messages.EnumField('NetworkTypeValueValuesEnum', 14)
  port = _messages.IntegerField(15, variant=_messages.Variant.INT32)
  projectId = _messages.StringField(16)
  redisCluster = _messages.StringField(17)
  redisInstance = _messages.StringField(18)


class EndpointInfo(_messages.Message):
  r"""For display only. The specification of the endpoints for the test.
  EndpointInfo is derived from source and destination Endpoint and validated
  by the backend data plane model.

  Fields:
    destinationIp: Destination IP address.
    destinationNetworkUri: URI of the network where this packet is sent to.
    destinationPort: Destination port. Only valid when protocol is TCP or UDP.
    protocol: IP protocol in string format, for example: "TCP", "UDP", "ICMP".
    sourceAgentUri: URI of the source telemetry agent this packet originates
      from.
    sourceIp: Source IP address.
    sourceNetworkUri: URI of the network where this packet originates from.
    sourcePort: Source port. Only valid when protocol is TCP or UDP.
  """

  destinationIp = _messages.StringField(1)
  destinationNetworkUri = _messages.StringField(2)
  destinationPort = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  protocol = _messages.StringField(4)
  sourceAgentUri = _messages.StringField(5)
  sourceIp = _messages.StringField(6)
  sourceNetworkUri = _messages.StringField(7)
  sourcePort = _messages.IntegerField(8, variant=_messages.Variant.INT32)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FirewallInfo(_messages.Message):
  r"""For display only. Metadata associated with a VPC firewall rule, an
  implied VPC firewall rule, or a firewall policy rule.

  Enums:
    FirewallRuleTypeValueValuesEnum: The firewall rule's type.

  Fields:
    action: Possible values: ALLOW, DENY, APPLY_SECURITY_PROFILE_GROUP
    direction: Possible values: INGRESS, EGRESS
    displayName: The display name of the firewall rule. This field might be
      empty for firewall policy rules.
    firewallRuleType: The firewall rule's type.
    networkUri: The URI of the VPC network that the firewall rule is
      associated with. This field is not applicable to hierarchical firewall
      policy rules.
    policy: The name of the firewall policy that this rule is associated with.
      This field is not applicable to VPC firewall rules and implied VPC
      firewall rules.
    policyUri: The URI of the firewall policy that this rule is associated
      with. This field is not applicable to VPC firewall rules and implied VPC
      firewall rules.
    priority: The priority of the firewall rule.
    targetServiceAccounts: The target service accounts specified by the
      firewall rule.
    targetTags: The target tags defined by the VPC firewall rule. This field
      is not applicable to firewall policy rules.
    uri: The URI of the firewall rule. This field is not applicable to implied
      VPC firewall rules.
  """

  class FirewallRuleTypeValueValuesEnum(_messages.Enum):
    r"""The firewall rule's type.

    Values:
      FIREWALL_RULE_TYPE_UNSPECIFIED: Unspecified type.
      HIERARCHICAL_FIREWALL_POLICY_RULE: Hierarchical firewall policy rule.
        For details, see [Hierarchical firewall policies
        overview](https://cloud.google.com/vpc/docs/firewall-policies).
      VPC_FIREWALL_RULE: VPC firewall rule. For details, see [VPC firewall
        rules overview](https://cloud.google.com/vpc/docs/firewalls).
      IMPLIED_VPC_FIREWALL_RULE: Implied VPC firewall rule. For details, see
        [Implied rules](https://cloud.google.com/vpc/docs/firewalls#default_fi
        rewall_rules).
      SERVERLESS_VPC_ACCESS_MANAGED_FIREWALL_RULE: Implicit firewall rules
        that are managed by serverless VPC access to allow ingress access.
        They are not visible in the Google Cloud console. For details, see
        [VPC connector's implicit
        rules](https://cloud.google.com/functions/docs/networking/connecting-
        vpc#restrict-access).
      NETWORK_FIREWALL_POLICY_RULE: Global network firewall policy rule. For
        details, see [Network firewall
        policies](https://cloud.google.com/vpc/docs/network-firewall-
        policies).
      NETWORK_REGIONAL_FIREWALL_POLICY_RULE: Regional network firewall policy
        rule. For details, see [Regional network firewall
        policies](https://cloud.google.com/firewall/docs/regional-firewall-
        policies).
      UNSUPPORTED_FIREWALL_POLICY_RULE: Firewall policy rule containing
        attributes not yet supported in Connectivity tests. Firewall analysis
        is skipped if such a rule can potentially be matched. Please see the
        [list of unsupported configurations](https://cloud.google.com/network-
        intelligence-center/docs/connectivity-
        tests/concepts/overview#unsupported-configs).
      TRACKING_STATE: Tracking state for response traffic created when request
        traffic goes through allow firewall rule. For details, see [firewall
        rules specifications](https://cloud.google.com/firewall/docs/firewalls
        #specifications)
      ANALYSIS_SKIPPED: Firewall analysis was skipped due to executing
        Connectivity Test in the BypassFirewallChecks mode
    """
    FIREWALL_RULE_TYPE_UNSPECIFIED = 0
    HIERARCHICAL_FIREWALL_POLICY_RULE = 1
    VPC_FIREWALL_RULE = 2
    IMPLIED_VPC_FIREWALL_RULE = 3
    SERVERLESS_VPC_ACCESS_MANAGED_FIREWALL_RULE = 4
    NETWORK_FIREWALL_POLICY_RULE = 5
    NETWORK_REGIONAL_FIREWALL_POLICY_RULE = 6
    UNSUPPORTED_FIREWALL_POLICY_RULE = 7
    TRACKING_STATE = 8
    ANALYSIS_SKIPPED = 9

  action = _messages.StringField(1)
  direction = _messages.StringField(2)
  displayName = _messages.StringField(3)
  firewallRuleType = _messages.EnumField('FirewallRuleTypeValueValuesEnum', 4)
  networkUri = _messages.StringField(5)
  policy = _messages.StringField(6)
  policyUri = _messages.StringField(7)
  priority = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  targetServiceAccounts = _messages.StringField(9, repeated=True)
  targetTags = _messages.StringField(10, repeated=True)
  uri = _messages.StringField(11)


class ForwardInfo(_messages.Message):
  r"""Details of the final state "forward" and associated resource.

  Enums:
    TargetValueValuesEnum: Target type where this packet is forwarded to.

  Fields:
    ipAddress: IP address of the target (if applicable).
    resourceUri: URI of the resource that the packet is forwarded to.
    target: Target type where this packet is forwarded to.
  """

  class TargetValueValuesEnum(_messages.Enum):
    r"""Target type where this packet is forwarded to.

    Values:
      TARGET_UNSPECIFIED: Target not specified.
      PEERING_VPC: Forwarded to a VPC peering network.
      VPN_GATEWAY: Forwarded to a Cloud VPN gateway.
      INTERCONNECT: Forwarded to a Cloud Interconnect connection.
      GKE_MASTER: Forwarded to a Google Kubernetes Engine Container cluster
        master.
      IMPORTED_CUSTOM_ROUTE_NEXT_HOP: Forwarded to the next hop of a custom
        route imported from a peering VPC.
      CLOUD_SQL_INSTANCE: Forwarded to a Cloud SQL instance.
      ANOTHER_PROJECT: Forwarded to a VPC network in another project.
      NCC_HUB: Forwarded to an NCC Hub.
      ROUTER_APPLIANCE: Forwarded to a router appliance.
    """
    TARGET_UNSPECIFIED = 0
    PEERING_VPC = 1
    VPN_GATEWAY = 2
    INTERCONNECT = 3
    GKE_MASTER = 4
    IMPORTED_CUSTOM_ROUTE_NEXT_HOP = 5
    CLOUD_SQL_INSTANCE = 6
    ANOTHER_PROJECT = 7
    NCC_HUB = 8
    ROUTER_APPLIANCE = 9

  ipAddress = _messages.StringField(1)
  resourceUri = _messages.StringField(2)
  target = _messages.EnumField('TargetValueValuesEnum', 3)


class ForwardingRuleInfo(_messages.Message):
  r"""For display only. Metadata associated with a Compute Engine forwarding
  rule.

  Fields:
    displayName: Name of the forwarding rule.
    loadBalancerName: Name of the load balancer the forwarding rule belongs
      to. Empty for forwarding rules not related to load balancers (like PSC
      forwarding rules).
    matchedPortRange: Port range defined in the forwarding rule that matches
      the packet.
    matchedProtocol: Protocol defined in the forwarding rule that matches the
      packet.
    networkUri: Network URI.
    pscGoogleApiTarget: PSC Google API target this forwarding rule targets (if
      applicable).
    pscServiceAttachmentUri: URI of the PSC service attachment this forwarding
      rule targets (if applicable).
    region: Region of the forwarding rule. Set only for regional forwarding
      rules.
    target: Target type of the forwarding rule.
    uri: URI of the forwarding rule.
    vip: VIP of the forwarding rule.
  """

  displayName = _messages.StringField(1)
  loadBalancerName = _messages.StringField(2)
  matchedPortRange = _messages.StringField(3)
  matchedProtocol = _messages.StringField(4)
  networkUri = _messages.StringField(5)
  pscGoogleApiTarget = _messages.StringField(6)
  pscServiceAttachmentUri = _messages.StringField(7)
  region = _messages.StringField(8)
  target = _messages.StringField(9)
  uri = _messages.StringField(10)
  vip = _messages.StringField(11)


class GKEMasterInfo(_messages.Message):
  r"""For display only. Metadata associated with a Google Kubernetes Engine
  (GKE) cluster master.

  Fields:
    clusterNetworkUri: URI of a GKE cluster network.
    clusterUri: URI of a GKE cluster.
    dnsEndpoint: DNS endpoint of a GKE cluster control plane.
    externalIp: External IP address of a GKE cluster control plane.
    internalIp: Internal IP address of a GKE cluster control plane.
  """

  clusterNetworkUri = _messages.StringField(1)
  clusterUri = _messages.StringField(2)
  dnsEndpoint = _messages.StringField(3)
  externalIp = _messages.StringField(4)
  internalIp = _messages.StringField(5)


class GoogleServiceInfo(_messages.Message):
  r"""For display only. Details of a Google Service sending packets to a VPC
  network. Although the source IP might be a publicly routable address, some
  Google Services use special routes within Google production infrastructure
  to reach Compute Engine Instances.
  https://cloud.google.com/vpc/docs/routes#special_return_paths

  Enums:
    GoogleServiceTypeValueValuesEnum: Recognized type of a Google Service.

  Fields:
    googleServiceType: Recognized type of a Google Service.
    sourceIp: Source IP address.
  """

  class GoogleServiceTypeValueValuesEnum(_messages.Enum):
    r"""Recognized type of a Google Service.

    Values:
      GOOGLE_SERVICE_TYPE_UNSPECIFIED: Unspecified Google Service.
      IAP: Identity aware proxy. https://cloud.google.com/iap/docs/using-tcp-
        forwarding
      GFE_PROXY_OR_HEALTH_CHECK_PROBER: One of two services sharing IP ranges:
        * Load Balancer proxy * Centralized Health Check prober
        https://cloud.google.com/load-balancing/docs/firewall-rules
      CLOUD_DNS: Connectivity from Cloud DNS to forwarding targets or
        alternate name servers that use private routing.
        https://cloud.google.com/dns/docs/zones/forwarding-zones#firewall-
        rules https://cloud.google.com/dns/docs/policies#firewall-rules
      GOOGLE_API: private.googleapis.com and restricted.googleapis.com
      GOOGLE_API_PSC: Google API via Private Service Connect.
        https://cloud.google.com/vpc/docs/configure-private-service-connect-
        apis
      GOOGLE_API_VPC_SC: Google API via VPC Service Controls.
        https://cloud.google.com/vpc/docs/configure-private-service-connect-
        apis
      SERVERLESS_VPC_ACCESS: Google API via Serverless VPC Access.
        https://cloud.google.com/vpc/docs/serverless-vpc-access
    """
    GOOGLE_SERVICE_TYPE_UNSPECIFIED = 0
    IAP = 1
    GFE_PROXY_OR_HEALTH_CHECK_PROBER = 2
    CLOUD_DNS = 3
    GOOGLE_API = 4
    GOOGLE_API_PSC = 5
    GOOGLE_API_VPC_SC = 6
    SERVERLESS_VPC_ACCESS = 7

  googleServiceType = _messages.EnumField('GoogleServiceTypeValueValuesEnum', 1)
  sourceIp = _messages.StringField(2)


class InstanceInfo(_messages.Message):
  r"""For display only. Metadata associated with a Compute Engine instance.

  Fields:
    displayName: Name of a Compute Engine instance.
    externalIp: External IP address of the network interface.
    interface: Name of the network interface of a Compute Engine instance.
    internalIp: Internal IP address of the network interface.
    networkTags: Network tags configured on the instance.
    networkUri: URI of a Compute Engine network.
    pscNetworkAttachmentUri: URI of the PSC network attachment the NIC is
      attached to (if relevant).
    running: Indicates whether the Compute Engine instance is running.
    serviceAccount: Service account authorized for the instance.
    uri: URI of a Compute Engine instance.
  """

  displayName = _messages.StringField(1)
  externalIp = _messages.StringField(2)
  interface = _messages.StringField(3)
  internalIp = _messages.StringField(4)
  networkTags = _messages.StringField(5, repeated=True)
  networkUri = _messages.StringField(6)
  pscNetworkAttachmentUri = _messages.StringField(7)
  running = _messages.BooleanField(8)
  serviceAccount = _messages.StringField(9)
  uri = _messages.StringField(10)


class LatencyDistribution(_messages.Message):
  r"""Describes measured latency distribution.

  Fields:
    latencyPercentiles: Representative latency percentiles.
  """

  latencyPercentiles = _messages.MessageField('LatencyPercentile', 1, repeated=True)


class LatencyPercentile(_messages.Message):
  r"""Latency percentile rank and value.

  Fields:
    latencyMicros: percent-th percentile of latency observed, in microseconds.
      Fraction of percent/100 of samples have latency lower or equal to the
      value of this field.
    percent: Percentage of samples this data point applies to.
  """

  latencyMicros = _messages.IntegerField(1)
  percent = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class ListConnectivityTestsResponse(_messages.Message):
  r"""Response for the `ListConnectivityTests` method.

  Fields:
    nextPageToken: Page token to fetch the next set of Connectivity Tests.
    resources: List of Connectivity Tests.
    unreachable: Locations that could not be reached (when querying all
      locations with `-`).
  """

  nextPageToken = _messages.StringField(1)
  resources = _messages.MessageField('ConnectivityTest', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListVpcFlowLogsConfigsResponse(_messages.Message):
  r"""Response for the `ListVpcFlowLogsConfigs` method.

  Fields:
    nextPageToken: Page token to fetch the next set of configurations.
    unreachable: Locations that could not be reached (when querying all
      locations with `-`).
    vpcFlowLogsConfigs: List of VPC Flow Log configurations.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  vpcFlowLogsConfigs = _messages.MessageField('VpcFlowLogsConfig', 3, repeated=True)


class LoadBalancerBackend(_messages.Message):
  r"""For display only. Metadata associated with a specific load balancer
  backend.

  Enums:
    HealthCheckFirewallStateValueValuesEnum: State of the health check
      firewall configuration.

  Fields:
    displayName: Name of a Compute Engine instance or network endpoint.
    healthCheckAllowingFirewallRules: A list of firewall rule URIs allowing
      probes from health check IP ranges.
    healthCheckBlockingFirewallRules: A list of firewall rule URIs blocking
      probes from health check IP ranges.
    healthCheckFirewallState: State of the health check firewall
      configuration.
    uri: URI of a Compute Engine instance or network endpoint.
  """

  class HealthCheckFirewallStateValueValuesEnum(_messages.Enum):
    r"""State of the health check firewall configuration.

    Values:
      HEALTH_CHECK_FIREWALL_STATE_UNSPECIFIED: State is unspecified. Default
        state if not populated.
      CONFIGURED: There are configured firewall rules to allow health check
        probes to the backend.
      MISCONFIGURED: There are firewall rules configured to allow partial
        health check ranges or block all health check ranges. If a health
        check probe is sent from denied IP ranges, the health check to the
        backend will fail. Then, the backend will be marked unhealthy and will
        not receive traffic sent to the load balancer.
    """
    HEALTH_CHECK_FIREWALL_STATE_UNSPECIFIED = 0
    CONFIGURED = 1
    MISCONFIGURED = 2

  displayName = _messages.StringField(1)
  healthCheckAllowingFirewallRules = _messages.StringField(2, repeated=True)
  healthCheckBlockingFirewallRules = _messages.StringField(3, repeated=True)
  healthCheckFirewallState = _messages.EnumField('HealthCheckFirewallStateValueValuesEnum', 4)
  uri = _messages.StringField(5)


class LoadBalancerBackendInfo(_messages.Message):
  r"""For display only. Metadata associated with the load balancer backend.

  Enums:
    HealthCheckFirewallsConfigStateValueValuesEnum: Output only. Health check
      firewalls configuration state for the backend. This is a result of the
      static firewall analysis (verifying that health check traffic from
      required IP ranges to the backend is allowed or not). The backend might
      still be unhealthy even if these firewalls are configured. Please refer
      to the documentation for more information:
      https://cloud.google.com/load-balancing/docs/firewall-rules

  Fields:
    backendBucketUri: URI of the backend bucket this backend targets (if
      applicable).
    backendServiceUri: URI of the backend service this backend belongs to (if
      applicable).
    healthCheckFirewallsConfigState: Output only. Health check firewalls
      configuration state for the backend. This is a result of the static
      firewall analysis (verifying that health check traffic from required IP
      ranges to the backend is allowed or not). The backend might still be
      unhealthy even if these firewalls are configured. Please refer to the
      documentation for more information: https://cloud.google.com/load-
      balancing/docs/firewall-rules
    healthCheckUri: URI of the health check attached to this backend (if
      applicable).
    instanceGroupUri: URI of the instance group this backend belongs to (if
      applicable).
    instanceUri: URI of the backend instance (if applicable). Populated for
      instance group backends, and zonal NEG backends.
    name: Display name of the backend. For example, it might be an instance
      name for the instance group backends, or an IP address and port for
      zonal network endpoint group backends.
    networkEndpointGroupUri: URI of the network endpoint group this backend
      belongs to (if applicable).
    pscGoogleApiTarget: PSC Google API target this PSC NEG backend targets (if
      applicable).
    pscServiceAttachmentUri: URI of the PSC service attachment this PSC NEG
      backend targets (if applicable).
  """

  class HealthCheckFirewallsConfigStateValueValuesEnum(_messages.Enum):
    r"""Output only. Health check firewalls configuration state for the
    backend. This is a result of the static firewall analysis (verifying that
    health check traffic from required IP ranges to the backend is allowed or
    not). The backend might still be unhealthy even if these firewalls are
    configured. Please refer to the documentation for more information:
    https://cloud.google.com/load-balancing/docs/firewall-rules

    Values:
      HEALTH_CHECK_FIREWALLS_CONFIG_STATE_UNSPECIFIED: Configuration state
        unspecified. It usually means that the backend has no health check
        attached, or there was an unexpected configuration error preventing
        Connectivity tests from verifying health check configuration.
      FIREWALLS_CONFIGURED: Firewall rules (policies) allowing health check
        traffic from all required IP ranges to the backend are configured.
      FIREWALLS_PARTIALLY_CONFIGURED: Firewall rules (policies) allow health
        check traffic only from a part of required IP ranges.
      FIREWALLS_NOT_CONFIGURED: Firewall rules (policies) deny health check
        traffic from all required IP ranges to the backend.
      FIREWALLS_UNSUPPORTED: The network contains firewall rules of
        unsupported types, so Connectivity tests were not able to verify
        health check configuration status. Please refer to the documentation
        for the list of unsupported configurations:
        https://cloud.google.com/network-intelligence-
        center/docs/connectivity-tests/concepts/overview#unsupported-configs
    """
    HEALTH_CHECK_FIREWALLS_CONFIG_STATE_UNSPECIFIED = 0
    FIREWALLS_CONFIGURED = 1
    FIREWALLS_PARTIALLY_CONFIGURED = 2
    FIREWALLS_NOT_CONFIGURED = 3
    FIREWALLS_UNSUPPORTED = 4

  backendBucketUri = _messages.StringField(1)
  backendServiceUri = _messages.StringField(2)
  healthCheckFirewallsConfigState = _messages.EnumField('HealthCheckFirewallsConfigStateValueValuesEnum', 3)
  healthCheckUri = _messages.StringField(4)
  instanceGroupUri = _messages.StringField(5)
  instanceUri = _messages.StringField(6)
  name = _messages.StringField(7)
  networkEndpointGroupUri = _messages.StringField(8)
  pscGoogleApiTarget = _messages.StringField(9)
  pscServiceAttachmentUri = _messages.StringField(10)


class LoadBalancerInfo(_messages.Message):
  r"""For display only. Metadata associated with a load balancer.

  Enums:
    BackendTypeValueValuesEnum: Type of load balancer's backend configuration.
    LoadBalancerTypeValueValuesEnum: Type of the load balancer.

  Fields:
    backendType: Type of load balancer's backend configuration.
    backendUri: Backend configuration URI.
    backends: Information for the loadbalancer backends.
    healthCheckUri: URI of the health check for the load balancer. Deprecated
      and no longer populated as different load balancer backends might have
      different health checks.
    loadBalancerType: Type of the load balancer.
  """

  class BackendTypeValueValuesEnum(_messages.Enum):
    r"""Type of load balancer's backend configuration.

    Values:
      BACKEND_TYPE_UNSPECIFIED: Type is unspecified.
      BACKEND_SERVICE: Backend Service as the load balancer's backend.
      TARGET_POOL: Target Pool as the load balancer's backend.
      TARGET_INSTANCE: Target Instance as the load balancer's backend.
    """
    BACKEND_TYPE_UNSPECIFIED = 0
    BACKEND_SERVICE = 1
    TARGET_POOL = 2
    TARGET_INSTANCE = 3

  class LoadBalancerTypeValueValuesEnum(_messages.Enum):
    r"""Type of the load balancer.

    Values:
      LOAD_BALANCER_TYPE_UNSPECIFIED: Type is unspecified.
      INTERNAL_TCP_UDP: Internal TCP/UDP load balancer.
      NETWORK_TCP_UDP: Network TCP/UDP load balancer.
      HTTP_PROXY: HTTP(S) proxy load balancer.
      TCP_PROXY: TCP proxy load balancer.
      SSL_PROXY: SSL proxy load balancer.
    """
    LOAD_BALANCER_TYPE_UNSPECIFIED = 0
    INTERNAL_TCP_UDP = 1
    NETWORK_TCP_UDP = 2
    HTTP_PROXY = 3
    TCP_PROXY = 4
    SSL_PROXY = 5

  backendType = _messages.EnumField('BackendTypeValueValuesEnum', 1)
  backendUri = _messages.StringField(2)
  backends = _messages.MessageField('LoadBalancerBackend', 3, repeated=True)
  healthCheckUri = _messages.StringField(4)
  loadBalancerType = _messages.EnumField('LoadBalancerTypeValueValuesEnum', 5)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class NatInfo(_messages.Message):
  r"""For display only. Metadata associated with NAT.

  Enums:
    TypeValueValuesEnum: Type of NAT.

  Fields:
    natGatewayName: The name of Cloud NAT Gateway. Only valid when type is
      CLOUD_NAT.
    networkUri: URI of the network where NAT translation takes place.
    newDestinationIp: Destination IP address after NAT translation.
    newDestinationPort: Destination port after NAT translation. Only valid
      when protocol is TCP or UDP.
    newSourceIp: Source IP address after NAT translation.
    newSourcePort: Source port after NAT translation. Only valid when protocol
      is TCP or UDP.
    oldDestinationIp: Destination IP address before NAT translation.
    oldDestinationPort: Destination port before NAT translation. Only valid
      when protocol is TCP or UDP.
    oldSourceIp: Source IP address before NAT translation.
    oldSourcePort: Source port before NAT translation. Only valid when
      protocol is TCP or UDP.
    protocol: IP protocol in string format, for example: "TCP", "UDP", "ICMP".
    routerUri: Uri of the Cloud Router. Only valid when type is CLOUD_NAT.
    type: Type of NAT.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of NAT.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      INTERNAL_TO_EXTERNAL: From Compute Engine instance's internal address to
        external address.
      EXTERNAL_TO_INTERNAL: From Compute Engine instance's external address to
        internal address.
      CLOUD_NAT: Cloud NAT Gateway.
      PRIVATE_SERVICE_CONNECT: Private service connect NAT.
    """
    TYPE_UNSPECIFIED = 0
    INTERNAL_TO_EXTERNAL = 1
    EXTERNAL_TO_INTERNAL = 2
    CLOUD_NAT = 3
    PRIVATE_SERVICE_CONNECT = 4

  natGatewayName = _messages.StringField(1)
  networkUri = _messages.StringField(2)
  newDestinationIp = _messages.StringField(3)
  newDestinationPort = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  newSourceIp = _messages.StringField(5)
  newSourcePort = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  oldDestinationIp = _messages.StringField(7)
  oldDestinationPort = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  oldSourceIp = _messages.StringField(9)
  oldSourcePort = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  protocol = _messages.StringField(11)
  routerUri = _messages.StringField(12)
  type = _messages.EnumField('TypeValueValuesEnum', 13)


class NetworkInfo(_messages.Message):
  r"""For display only. Metadata associated with a Compute Engine network.
  Next ID: 7

  Fields:
    displayName: Name of a Compute Engine network.
    matchedIpRange: The IP range of the subnet matching the source IP address
      of the test.
    matchedSubnetUri: URI of the subnet matching the source IP address of the
      test.
    region: The region of the subnet matching the source IP address of the
      test.
    uri: URI of a Compute Engine network.
  """

  displayName = _messages.StringField(1)
  matchedIpRange = _messages.StringField(2)
  matchedSubnetUri = _messages.StringField(3)
  region = _messages.StringField(4)
  uri = _messages.StringField(5)


class NetworkmanagementProjectsLocationsGetRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsCreateRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalConnectivityTestsCreateRequest
  object.

  Fields:
    connectivityTest: A ConnectivityTest resource to be passed as the request
      body.
    parent: Required. The parent resource of the Connectivity Test to create:
      `projects/{project_id}/locations/global`
    testId: Required. The logical name of the Connectivity Test in your
      project with the following restrictions: * Must contain only lowercase
      letters, numbers, and hyphens. * Must start with a letter. * Must be
      between 1-40 characters. * Must end with a number or a letter. * Must be
      unique within the customer project
  """

  connectivityTest = _messages.MessageField('ConnectivityTest', 1)
  parent = _messages.StringField(2, required=True)
  testId = _messages.StringField(3)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsDeleteRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalConnectivityTestsDeleteRequest
  object.

  Fields:
    name: Required. Connectivity Test resource name using the form:
      `projects/{project_id}/locations/global/connectivityTests/{test_id}`
  """

  name = _messages.StringField(1, required=True)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsGetIamPolicyRequest(_messages.Message):
  r"""A
  NetworkmanagementProjectsLocationsGlobalConnectivityTestsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsGetRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalConnectivityTestsGetRequest
  object.

  Fields:
    name: Required. `ConnectivityTest` resource name using the form:
      `projects/{project_id}/locations/global/connectivityTests/{test_id}`
  """

  name = _messages.StringField(1, required=True)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsListRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalConnectivityTestsListRequest
  object.

  Fields:
    filter: Lists the `ConnectivityTests` that match the filter expression. A
      filter expression filters the resources listed in the response. The
      expression must be of the form ` ` where operators: `<`, `>`, `<=`,
      `>=`, `!=`, `=`, `:` are supported (colon `:` represents a HAS operator
      which is roughly synonymous with equality). can refer to a proto or JSON
      field, or a synthetic field. Field names can be camelCase or snake_case.
      Examples: - Filter by name: name =
      "projects/proj-1/locations/global/connectivityTests/test-1 - Filter by
      labels: - Resources that have a key called `foo` labels.foo:* -
      Resources that have a key called `foo` whose value is `bar` labels.foo =
      bar
    orderBy: Field to use to sort the list.
    pageSize: Number of `ConnectivityTests` to return.
    pageToken: Page token from an earlier query, as returned in
      `next_page_token`.
    parent: Required. The parent resource of the Connectivity Tests:
      `projects/{project_id}/locations/global`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsPatchRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalConnectivityTestsPatchRequest
  object.

  Fields:
    connectivityTest: A ConnectivityTest resource to be passed as the request
      body.
    name: Identifier. Unique name of the resource using the form:
      `projects/{project_id}/locations/global/connectivityTests/{test_id}`
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field.
  """

  connectivityTest = _messages.MessageField('ConnectivityTest', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsRerunRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalConnectivityTestsRerunRequest
  object.

  Fields:
    name: Required. Connectivity Test resource name using the form:
      `projects/{project_id}/locations/global/connectivityTests/{test_id}`
    rerunConnectivityTestRequest: A RerunConnectivityTestRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  rerunConnectivityTestRequest = _messages.MessageField('RerunConnectivityTestRequest', 2)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsSetIamPolicyRequest(_messages.Message):
  r"""A
  NetworkmanagementProjectsLocationsGlobalConnectivityTestsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsTestIamPermissionsRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalConnectivityTestsTestIamPermis
  sionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NetworkmanagementProjectsLocationsGlobalOperationsCancelRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalOperationsCancelRequest
  object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworkmanagementProjectsLocationsGlobalOperationsDeleteRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalOperationsDeleteRequest
  object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class NetworkmanagementProjectsLocationsGlobalOperationsGetRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class NetworkmanagementProjectsLocationsGlobalOperationsListRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class NetworkmanagementProjectsLocationsListRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class NetworkmanagementProjectsLocationsVpcFlowLogsConfigsCreateRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsVpcFlowLogsConfigsCreateRequest
  object.

  Fields:
    parent: Required. The parent resource of the VPC Flow Logs configuration
      to create: `projects/{project_id}/locations/global`
    vpcFlowLogsConfig: A VpcFlowLogsConfig resource to be passed as the
      request body.
    vpcFlowLogsConfigId: Required. ID of the `VpcFlowLogsConfig`.
  """

  parent = _messages.StringField(1, required=True)
  vpcFlowLogsConfig = _messages.MessageField('VpcFlowLogsConfig', 2)
  vpcFlowLogsConfigId = _messages.StringField(3)


class NetworkmanagementProjectsLocationsVpcFlowLogsConfigsDeleteRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsVpcFlowLogsConfigsDeleteRequest
  object.

  Fields:
    name: Required. `VpcFlowLogsConfig` resource name using the form: `project
      s/{project_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config
      }`
  """

  name = _messages.StringField(1, required=True)


class NetworkmanagementProjectsLocationsVpcFlowLogsConfigsGetRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsVpcFlowLogsConfigsGetRequest object.

  Fields:
    name: Required. `VpcFlowLogsConfig` resource name using the form: `project
      s/{project_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config
      }`
  """

  name = _messages.StringField(1, required=True)


class NetworkmanagementProjectsLocationsVpcFlowLogsConfigsListRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsVpcFlowLogsConfigsListRequest
  object.

  Fields:
    filter: Optional. Lists the `VpcFlowLogsConfigs` that match the filter
      expression. A filter expression must use the supported [CEL logic
      operators] (https://cloud.google.com/vpc/docs/about-flow-logs-
      records#supported_cel_logic_operators).
    orderBy: Optional. Field to use to sort the list.
    pageSize: Optional. Number of `VpcFlowLogsConfigs` to return.
    pageToken: Optional. Page token from an earlier query, as returned in
      `next_page_token`.
    parent: Required. The parent resource of the VpcFlowLogsConfig:
      `projects/{project_id}/locations/global`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkmanagementProjectsLocationsVpcFlowLogsConfigsPatchRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsVpcFlowLogsConfigsPatchRequest
  object.

  Fields:
    name: Identifier. Unique name of the configuration using the form: `projec
      ts/{project_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_confi
      g_id}`
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field.
    vpcFlowLogsConfig: A VpcFlowLogsConfig resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  vpcFlowLogsConfig = _messages.MessageField('VpcFlowLogsConfig', 3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Metadata describing an Operation

  Fields:
    apiVersion: API version.
    cancelRequested: Specifies if cancellation was requested for the
      operation.
    createTime: The time the operation was created.
    endTime: The time the operation finished running.
    statusDetail: Human-readable status of the operation, if any.
    target: Target of the operation - for example
      projects/project-1/locations/global/connectivityTests/test-1
    verb: Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ProbingDetails(_messages.Message):
  r"""Results of active probing from the last run of the test.

  Enums:
    AbortCauseValueValuesEnum: The reason probing was aborted.
    ResultValueValuesEnum: The overall result of active probing.

  Fields:
    abortCause: The reason probing was aborted.
    destinationEgressLocation: The EdgeLocation from which a packet, destined
      to the internet, will egress the Google network. This will only be
      populated for a connectivity test which has an internet destination
      address. The absence of this field *must not* be used as an indication
      that the destination is part of the Google network.
    edgeResponses: Probing results for all edge devices.
    endpointInfo: The source and destination endpoints derived from the test
      input and used for active probing.
    error: Details about an internal failure or the cancellation of active
      probing.
    probedAllDevices: Whether all relevant edge devices were probed.
    probingLatency: Latency as measured by active probing in one direction:
      from the source to the destination endpoint.
    result: The overall result of active probing.
    sentProbeCount: Number of probes sent.
    successfulProbeCount: Number of probes that reached the destination.
    verifyTime: The time that reachability was assessed through active
      probing.
  """

  class AbortCauseValueValuesEnum(_messages.Enum):
    r"""The reason probing was aborted.

    Values:
      PROBING_ABORT_CAUSE_UNSPECIFIED: No reason was specified.
      PERMISSION_DENIED: The user lacks permission to access some of the
        network resources required to run the test.
      NO_SOURCE_LOCATION: No valid source endpoint could be derived from the
        request.
    """
    PROBING_ABORT_CAUSE_UNSPECIFIED = 0
    PERMISSION_DENIED = 1
    NO_SOURCE_LOCATION = 2

  class ResultValueValuesEnum(_messages.Enum):
    r"""The overall result of active probing.

    Values:
      PROBING_RESULT_UNSPECIFIED: No result was specified.
      REACHABLE: At least 95% of packets reached the destination.
      UNREACHABLE: No packets reached the destination.
      REACHABILITY_INCONSISTENT: Less than 95% of packets reached the
        destination.
      UNDETERMINED: Reachability could not be determined. Possible reasons
        are: * The user lacks permission to access some of the network
        resources required to run the test. * No valid source endpoint could
        be derived from the request. * An internal error occurred.
    """
    PROBING_RESULT_UNSPECIFIED = 0
    REACHABLE = 1
    UNREACHABLE = 2
    REACHABILITY_INCONSISTENT = 3
    UNDETERMINED = 4

  abortCause = _messages.EnumField('AbortCauseValueValuesEnum', 1)
  destinationEgressLocation = _messages.MessageField('EdgeLocation', 2)
  edgeResponses = _messages.MessageField('SingleEdgeResponse', 3, repeated=True)
  endpointInfo = _messages.MessageField('EndpointInfo', 4)
  error = _messages.MessageField('Status', 5)
  probedAllDevices = _messages.BooleanField(6)
  probingLatency = _messages.MessageField('LatencyDistribution', 7)
  result = _messages.EnumField('ResultValueValuesEnum', 8)
  sentProbeCount = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  successfulProbeCount = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  verifyTime = _messages.StringField(11)


class ProxyConnectionInfo(_messages.Message):
  r"""For display only. Metadata associated with ProxyConnection.

  Fields:
    networkUri: URI of the network where connection is proxied.
    newDestinationIp: Destination IP address of a new connection.
    newDestinationPort: Destination port of a new connection. Only valid when
      protocol is TCP or UDP.
    newSourceIp: Source IP address of a new connection.
    newSourcePort: Source port of a new connection. Only valid when protocol
      is TCP or UDP.
    oldDestinationIp: Destination IP address of an original connection
    oldDestinationPort: Destination port of an original connection. Only valid
      when protocol is TCP or UDP.
    oldSourceIp: Source IP address of an original connection.
    oldSourcePort: Source port of an original connection. Only valid when
      protocol is TCP or UDP.
    protocol: IP protocol in string format, for example: "TCP", "UDP", "ICMP".
    subnetUri: Uri of proxy subnet.
  """

  networkUri = _messages.StringField(1)
  newDestinationIp = _messages.StringField(2)
  newDestinationPort = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  newSourceIp = _messages.StringField(4)
  newSourcePort = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  oldDestinationIp = _messages.StringField(6)
  oldDestinationPort = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  oldSourceIp = _messages.StringField(8)
  oldSourcePort = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  protocol = _messages.StringField(10)
  subnetUri = _messages.StringField(11)


class ReachabilityDetails(_messages.Message):
  r"""Results of the configuration analysis from the last run of the test.

  Enums:
    ResultValueValuesEnum: The overall result of the test's configuration
      analysis.

  Fields:
    error: The details of a failure or a cancellation of reachability
      analysis.
    result: The overall result of the test's configuration analysis.
    traces: Result may contain a list of traces if a test has multiple
      possible paths in the network, such as when destination endpoint is a
      load balancer with multiple backends.
    verifyTime: The time of the configuration analysis.
  """

  class ResultValueValuesEnum(_messages.Enum):
    r"""The overall result of the test's configuration analysis.

    Values:
      RESULT_UNSPECIFIED: No result was specified.
      REACHABLE: Possible scenarios are: * The configuration analysis
        determined that a packet originating from the source is expected to
        reach the destination. * The analysis didn't complete because the user
        lacks permission for some of the resources in the trace. However, at
        the time the user's permission became insufficient, the trace had been
        successful so far.
      UNREACHABLE: A packet originating from the source is expected to be
        dropped before reaching the destination.
      AMBIGUOUS: The source and destination endpoints do not uniquely identify
        the test location in the network, and the reachability result contains
        multiple traces. For some traces, a packet could be delivered, and for
        others, it would not be. This result is also assigned to configuration
        analysis of return path if on its own it should be REACHABLE, but
        configuration analysis of forward path is AMBIGUOUS.
      UNDETERMINED: The configuration analysis did not complete. Possible
        reasons are: * A permissions error occurred--for example, the user
        might not have read permission for all of the resources named in the
        test. * An internal error occurred. * The analyzer received an invalid
        or unsupported argument or was unable to identify a known endpoint.
    """
    RESULT_UNSPECIFIED = 0
    REACHABLE = 1
    UNREACHABLE = 2
    AMBIGUOUS = 3
    UNDETERMINED = 4

  error = _messages.MessageField('Status', 1)
  result = _messages.EnumField('ResultValueValuesEnum', 2)
  traces = _messages.MessageField('Trace', 3, repeated=True)
  verifyTime = _messages.StringField(4)


class RedisClusterInfo(_messages.Message):
  r"""For display only. Metadata associated with a Redis Cluster.

  Fields:
    discoveryEndpointIpAddress: Discovery endpoint IP address of a Redis
      Cluster.
    displayName: Name of a Redis Cluster.
    location: Name of the region in which the Redis Cluster is defined. For
      example, "us-central1".
    networkUri: URI of the network containing the Redis Cluster endpoints in
      format "projects/{project_id}/global/networks/{network_id}".
    secondaryEndpointIpAddress: Secondary endpoint IP address of a Redis
      Cluster.
    uri: URI of a Redis Cluster in format
      "projects/{project_id}/locations/{location}/clusters/{cluster_id}"
  """

  discoveryEndpointIpAddress = _messages.StringField(1)
  displayName = _messages.StringField(2)
  location = _messages.StringField(3)
  networkUri = _messages.StringField(4)
  secondaryEndpointIpAddress = _messages.StringField(5)
  uri = _messages.StringField(6)


class RedisInstanceInfo(_messages.Message):
  r"""For display only. Metadata associated with a Cloud Redis Instance.

  Fields:
    displayName: Name of a Cloud Redis Instance.
    networkUri: URI of a Cloud Redis Instance network.
    primaryEndpointIp: Primary endpoint IP address of a Cloud Redis Instance.
    readEndpointIp: Read endpoint IP address of a Cloud Redis Instance (if
      applicable).
    region: Region in which the Cloud Redis Instance is defined.
    uri: URI of a Cloud Redis Instance.
  """

  displayName = _messages.StringField(1)
  networkUri = _messages.StringField(2)
  primaryEndpointIp = _messages.StringField(3)
  readEndpointIp = _messages.StringField(4)
  region = _messages.StringField(5)
  uri = _messages.StringField(6)


class RerunConnectivityTestRequest(_messages.Message):
  r"""Request for the `RerunConnectivityTest` method."""


class RouteInfo(_messages.Message):
  r"""For display only. Metadata associated with a Compute Engine route.

  Enums:
    NextHopTypeValueValuesEnum: Type of next hop.
    RouteScopeValueValuesEnum: Indicates where route is applicable.
      Deprecated, routes with NCC_HUB scope are not included in the trace in
      new tests.
    RouteTypeValueValuesEnum: Type of route.

  Fields:
    advertisedRouteNextHopUri: For ADVERTISED routes, the URI of their next
      hop, i.e. the URI of the hybrid endpoint (VPN tunnel, Interconnect
      attachment, NCC router appliance) the advertised prefix is advertised
      through, or URI of the source peered network. Deprecated in favor of the
      next_hop_uri field, not used in new tests.
    advertisedRouteSourceRouterUri: For ADVERTISED dynamic routes, the URI of
      the Cloud Router that advertised the corresponding IP prefix.
    destIpRange: Destination IP range of the route.
    destPortRanges: Destination port ranges of the route. POLICY_BASED routes
      only.
    displayName: Name of a route.
    instanceTags: Instance tags of the route.
    nccHubRouteUri: For PEERING_SUBNET and PEERING_DYNAMIC routes that are
      advertised by NCC Hub, the URI of the corresponding route in NCC Hub's
      routing table.
    nccHubUri: URI of the NCC Hub the route is advertised by. PEERING_SUBNET
      and PEERING_DYNAMIC routes that are advertised by NCC Hub only.
    nccSpokeUri: URI of the destination NCC Spoke. PEERING_SUBNET and
      PEERING_DYNAMIC routes that are advertised by NCC Hub only.
    networkUri: URI of a VPC network where route is located.
    nextHop: String type of the next hop of the route (for example, "VPN
      tunnel"). Deprecated in favor of the next_hop_type and next_hop_uri
      fields, not used in new tests.
    nextHopNetworkUri: URI of a VPC network where the next hop resource is
      located.
    nextHopType: Type of next hop.
    nextHopUri: URI of the next hop resource.
    originatingRouteDisplayName: For PEERING_SUBNET, PEERING_STATIC and
      PEERING_DYNAMIC routes, the name of the originating
      SUBNET/STATIC/DYNAMIC route.
    originatingRouteUri: For PEERING_SUBNET and PEERING_STATIC routes, the URI
      of the originating SUBNET/STATIC route.
    priority: Priority of the route.
    protocols: Protocols of the route. POLICY_BASED routes only.
    region: Region of the route. DYNAMIC, PEERING_DYNAMIC, POLICY_BASED and
      ADVERTISED routes only. If set for POLICY_BASED route, this is a region
      of VLAN attachments for Cloud Interconnect the route applies to.
    routeScope: Indicates where route is applicable. Deprecated, routes with
      NCC_HUB scope are not included in the trace in new tests.
    routeType: Type of route.
    srcIpRange: Source IP address range of the route. POLICY_BASED routes
      only.
    srcPortRanges: Source port ranges of the route. POLICY_BASED routes only.
    uri: URI of a route. SUBNET, STATIC, PEERING_SUBNET (only for peering
      network) and POLICY_BASED routes only.
  """

  class NextHopTypeValueValuesEnum(_messages.Enum):
    r"""Type of next hop.

    Values:
      NEXT_HOP_TYPE_UNSPECIFIED: Unspecified type. Default value.
      NEXT_HOP_IP: Next hop is an IP address.
      NEXT_HOP_INSTANCE: Next hop is a Compute Engine instance.
      NEXT_HOP_NETWORK: Next hop is a VPC network gateway.
      NEXT_HOP_PEERING: Next hop is a peering VPC. This scenario only happens
        when the user doesn't have permissions to the project where the next
        hop resource is located.
      NEXT_HOP_INTERCONNECT: Next hop is an interconnect.
      NEXT_HOP_VPN_TUNNEL: Next hop is a VPN tunnel.
      NEXT_HOP_VPN_GATEWAY: Next hop is a VPN gateway. This scenario only
        happens when tracing connectivity from an on-premises network to
        Google Cloud through a VPN. The analysis simulates a packet departing
        from the on-premises network through a VPN tunnel and arriving at a
        Cloud VPN gateway.
      NEXT_HOP_INTERNET_GATEWAY: Next hop is an internet gateway.
      NEXT_HOP_BLACKHOLE: Next hop is blackhole; that is, the next hop either
        does not exist or is unusable.
      NEXT_HOP_ILB: Next hop is the forwarding rule of an Internal Load
        Balancer.
      NEXT_HOP_ROUTER_APPLIANCE: Next hop is a [router appliance
        instance](https://cloud.google.com/network-connectivity/docs/network-
        connectivity-center/concepts/ra-overview).
      NEXT_HOP_NCC_HUB: Next hop is an NCC hub. This scenario only happens
        when the user doesn't have permissions to the project where the next
        hop resource is located.
    """
    NEXT_HOP_TYPE_UNSPECIFIED = 0
    NEXT_HOP_IP = 1
    NEXT_HOP_INSTANCE = 2
    NEXT_HOP_NETWORK = 3
    NEXT_HOP_PEERING = 4
    NEXT_HOP_INTERCONNECT = 5
    NEXT_HOP_VPN_TUNNEL = 6
    NEXT_HOP_VPN_GATEWAY = 7
    NEXT_HOP_INTERNET_GATEWAY = 8
    NEXT_HOP_BLACKHOLE = 9
    NEXT_HOP_ILB = 10
    NEXT_HOP_ROUTER_APPLIANCE = 11
    NEXT_HOP_NCC_HUB = 12

  class RouteScopeValueValuesEnum(_messages.Enum):
    r"""Indicates where route is applicable. Deprecated, routes with NCC_HUB
    scope are not included in the trace in new tests.

    Values:
      ROUTE_SCOPE_UNSPECIFIED: Unspecified scope. Default value.
      NETWORK: Route is applicable to packets in Network.
      NCC_HUB: Route is applicable to packets using NCC Hub's routing table.
    """
    ROUTE_SCOPE_UNSPECIFIED = 0
    NETWORK = 1
    NCC_HUB = 2

  class RouteTypeValueValuesEnum(_messages.Enum):
    r"""Type of route.

    Values:
      ROUTE_TYPE_UNSPECIFIED: Unspecified type. Default value.
      SUBNET: Route is a subnet route automatically created by the system.
      STATIC: Static route created by the user, including the default route to
        the internet.
      DYNAMIC: Dynamic route exchanged between BGP peers.
      PEERING_SUBNET: A subnet route received from peering network or NCC Hub.
      PEERING_STATIC: A static route received from peering network.
      PEERING_DYNAMIC: A dynamic route received from peering network or NCC
        Hub.
      POLICY_BASED: Policy based route.
      ADVERTISED: Advertised route. Synthetic route which is used to
        transition from the StartFromPrivateNetwork state in Connectivity
        tests.
    """
    ROUTE_TYPE_UNSPECIFIED = 0
    SUBNET = 1
    STATIC = 2
    DYNAMIC = 3
    PEERING_SUBNET = 4
    PEERING_STATIC = 5
    PEERING_DYNAMIC = 6
    POLICY_BASED = 7
    ADVERTISED = 8

  advertisedRouteNextHopUri = _messages.StringField(1)
  advertisedRouteSourceRouterUri = _messages.StringField(2)
  destIpRange = _messages.StringField(3)
  destPortRanges = _messages.StringField(4, repeated=True)
  displayName = _messages.StringField(5)
  instanceTags = _messages.StringField(6, repeated=True)
  nccHubRouteUri = _messages.StringField(7)
  nccHubUri = _messages.StringField(8)
  nccSpokeUri = _messages.StringField(9)
  networkUri = _messages.StringField(10)
  nextHop = _messages.StringField(11)
  nextHopNetworkUri = _messages.StringField(12)
  nextHopType = _messages.EnumField('NextHopTypeValueValuesEnum', 13)
  nextHopUri = _messages.StringField(14)
  originatingRouteDisplayName = _messages.StringField(15)
  originatingRouteUri = _messages.StringField(16)
  priority = _messages.IntegerField(17, variant=_messages.Variant.INT32)
  protocols = _messages.StringField(18, repeated=True)
  region = _messages.StringField(19)
  routeScope = _messages.EnumField('RouteScopeValueValuesEnum', 20)
  routeType = _messages.EnumField('RouteTypeValueValuesEnum', 21)
  srcIpRange = _messages.StringField(22)
  srcPortRanges = _messages.StringField(23, repeated=True)
  uri = _messages.StringField(24)


class ServerlessExternalConnectionInfo(_messages.Message):
  r"""For display only. Metadata associated with a serverless public
  connection.

  Fields:
    selectedIpAddress: Selected starting IP address, from the Google dynamic
      address pool.
  """

  selectedIpAddress = _messages.StringField(1)


class ServerlessNegInfo(_messages.Message):
  r"""For display only. Metadata associated with the serverless network
  endpoint group backend.

  Fields:
    negUri: URI of the serverless network endpoint group.
  """

  negUri = _messages.StringField(1)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SingleEdgeResponse(_messages.Message):
  r"""Probing results for a single edge device.

  Enums:
    ResultValueValuesEnum: The overall result of active probing for this
      egress device.

  Fields:
    destinationEgressLocation: The EdgeLocation from which a packet, destined
      to the internet, will egress the Google network. This will only be
      populated for a connectivity test which has an internet destination
      address. The absence of this field *must not* be used as an indication
      that the destination is part of the Google network.
    destinationRouter: Router name in the format '{router}.{metroshard}'. For
      example: pf01.aaa01, pr02.aaa01.
    probingLatency: Latency as measured by active probing in one direction:
      from the source to the destination endpoint.
    result: The overall result of active probing for this egress device.
    sentProbeCount: Number of probes sent.
    successfulProbeCount: Number of probes that reached the destination.
  """

  class ResultValueValuesEnum(_messages.Enum):
    r"""The overall result of active probing for this egress device.

    Values:
      PROBING_RESULT_UNSPECIFIED: No result was specified.
      REACHABLE: At least 95% of packets reached the destination.
      UNREACHABLE: No packets reached the destination.
      REACHABILITY_INCONSISTENT: Less than 95% of packets reached the
        destination.
      UNDETERMINED: Reachability could not be determined. Possible reasons
        are: * The user lacks permission to access some of the network
        resources required to run the test. * No valid source endpoint could
        be derived from the request. * An internal error occurred.
    """
    PROBING_RESULT_UNSPECIFIED = 0
    REACHABLE = 1
    UNREACHABLE = 2
    REACHABILITY_INCONSISTENT = 3
    UNDETERMINED = 4

  destinationEgressLocation = _messages.MessageField('EdgeLocation', 1)
  destinationRouter = _messages.StringField(2)
  probingLatency = _messages.MessageField('LatencyDistribution', 3)
  result = _messages.EnumField('ResultValueValuesEnum', 4)
  sentProbeCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  successfulProbeCount = _messages.IntegerField(6, variant=_messages.Variant.INT32)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Step(_messages.Message):
  r"""A simulated forwarding path is composed of multiple steps. Each step has
  a well-defined state and an associated configuration.

  Enums:
    StateValueValuesEnum: Each step is in one of the pre-defined states.

  Fields:
    abort: Display information of the final state "abort" and reason.
    appEngineVersion: Display information of an App Engine service version.
    causesDrop: This is a step that leads to the final state Drop.
    cloudFunction: Display information of a Cloud Function.
    cloudRunRevision: Display information of a Cloud Run revision.
    cloudSqlInstance: Display information of a Cloud SQL instance.
    deliver: Display information of the final state "deliver" and reason.
    description: A description of the step. Usually this is a summary of the
      state.
    directVpcEgressConnection: Display information of a serverless direct VPC
      egress connection.
    drop: Display information of the final state "drop" and reason.
    endpoint: Display information of the source and destination under
      analysis. The endpoint information in an intermediate state may differ
      with the initial input, as it might be modified by state like NAT, or
      Connection Proxy.
    firewall: Display information of a Compute Engine firewall rule.
    forward: Display information of the final state "forward" and reason.
    forwardingRule: Display information of a Compute Engine forwarding rule.
    gkeMaster: Display information of a Google Kubernetes Engine cluster
      master.
    googleService: Display information of a Google service
    instance: Display information of a Compute Engine instance.
    loadBalancer: Display information of the load balancers. Deprecated in
      favor of the `load_balancer_backend_info` field, not used in new tests.
    loadBalancerBackendInfo: Display information of a specific load balancer
      backend.
    nat: Display information of a NAT.
    network: Display information of a Google Cloud network.
    projectId: Project ID that contains the configuration this step is
      validating.
    proxyConnection: Display information of a ProxyConnection.
    redisCluster: Display information of a Redis Cluster.
    redisInstance: Display information of a Redis Instance.
    route: Display information of a Compute Engine route.
    serverlessExternalConnection: Display information of a serverless public
      (external) connection.
    serverlessNeg: Display information of a Serverless network endpoint group
      backend. Used only for return traces.
    state: Each step is in one of the pre-defined states.
    storageBucket: Display information of a Storage Bucket. Used only for
      return traces.
    vpcConnector: Display information of a VPC connector.
    vpnGateway: Display information of a Compute Engine VPN gateway.
    vpnTunnel: Display information of a Compute Engine VPN tunnel.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Each step is in one of the pre-defined states.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      START_FROM_INSTANCE: Initial state: packet originating from a Compute
        Engine instance. An InstanceInfo is populated with starting instance
        information.
      START_FROM_INTERNET: Initial state: packet originating from the
        internet. The endpoint information is populated.
      START_FROM_GOOGLE_SERVICE: Initial state: packet originating from a
        Google service. The google_service information is populated.
      START_FROM_PRIVATE_NETWORK: Initial state: packet originating from a VPC
        or on-premises network with internal source IP. If the source is a VPC
        network visible to the user, a NetworkInfo is populated with details
        of the network.
      START_FROM_GKE_MASTER: Initial state: packet originating from a Google
        Kubernetes Engine cluster master. A GKEMasterInfo is populated with
        starting instance information.
      START_FROM_CLOUD_SQL_INSTANCE: Initial state: packet originating from a
        Cloud SQL instance. A CloudSQLInstanceInfo is populated with starting
        instance information.
      START_FROM_REDIS_INSTANCE: Initial state: packet originating from a
        Redis instance. A RedisInstanceInfo is populated with starting
        instance information.
      START_FROM_REDIS_CLUSTER: Initial state: packet originating from a Redis
        Cluster. A RedisClusterInfo is populated with starting Cluster
        information.
      START_FROM_CLOUD_FUNCTION: Initial state: packet originating from a
        Cloud Function. A CloudFunctionInfo is populated with starting
        function information.
      START_FROM_APP_ENGINE_VERSION: Initial state: packet originating from an
        App Engine service version. An AppEngineVersionInfo is populated with
        starting version information.
      START_FROM_CLOUD_RUN_REVISION: Initial state: packet originating from a
        Cloud Run revision. A CloudRunRevisionInfo is populated with starting
        revision information.
      START_FROM_STORAGE_BUCKET: Initial state: packet originating from a
        Storage Bucket. Used only for return traces. The storage_bucket
        information is populated.
      START_FROM_PSC_PUBLISHED_SERVICE: Initial state: packet originating from
        a published service that uses Private Service Connect. Used only for
        return traces.
      START_FROM_SERVERLESS_NEG: Initial state: packet originating from a
        serverless network endpoint group backend. Used only for return
        traces. The serverless_neg information is populated.
      APPLY_INGRESS_FIREWALL_RULE: Config checking state: verify ingress
        firewall rule.
      APPLY_EGRESS_FIREWALL_RULE: Config checking state: verify egress
        firewall rule.
      APPLY_ROUTE: Config checking state: verify route.
      APPLY_FORWARDING_RULE: Config checking state: match forwarding rule.
      ANALYZE_LOAD_BALANCER_BACKEND: Config checking state: verify load
        balancer backend configuration.
      SPOOFING_APPROVED: Config checking state: packet sent or received under
        foreign IP address and allowed.
      ARRIVE_AT_INSTANCE: Forwarding state: arriving at a Compute Engine
        instance.
      ARRIVE_AT_INTERNAL_LOAD_BALANCER: Forwarding state: arriving at a
        Compute Engine internal load balancer.
      ARRIVE_AT_EXTERNAL_LOAD_BALANCER: Forwarding state: arriving at a
        Compute Engine external load balancer.
      ARRIVE_AT_VPN_GATEWAY: Forwarding state: arriving at a Cloud VPN
        gateway.
      ARRIVE_AT_VPN_TUNNEL: Forwarding state: arriving at a Cloud VPN tunnel.
      ARRIVE_AT_VPC_CONNECTOR: Forwarding state: arriving at a VPC connector.
      DIRECT_VPC_EGRESS_CONNECTION: Forwarding state: for packets originating
        from a serverless endpoint forwarded through Direct VPC egress.
      SERVERLESS_EXTERNAL_CONNECTION: Forwarding state: for packets
        originating from a serverless endpoint forwarded through public
        (external) connectivity.
      NAT: Transition state: packet header translated.
      PROXY_CONNECTION: Transition state: original connection is terminated
        and a new proxied connection is initiated.
      DELIVER: Final state: packet could be delivered.
      DROP: Final state: packet could be dropped.
      FORWARD: Final state: packet could be forwarded to a network with an
        unknown configuration.
      ABORT: Final state: analysis is aborted.
      VIEWER_PERMISSION_MISSING: Special state: viewer of the test result does
        not have permission to see the configuration in this step.
    """
    STATE_UNSPECIFIED = 0
    START_FROM_INSTANCE = 1
    START_FROM_INTERNET = 2
    START_FROM_GOOGLE_SERVICE = 3
    START_FROM_PRIVATE_NETWORK = 4
    START_FROM_GKE_MASTER = 5
    START_FROM_CLOUD_SQL_INSTANCE = 6
    START_FROM_REDIS_INSTANCE = 7
    START_FROM_REDIS_CLUSTER = 8
    START_FROM_CLOUD_FUNCTION = 9
    START_FROM_APP_ENGINE_VERSION = 10
    START_FROM_CLOUD_RUN_REVISION = 11
    START_FROM_STORAGE_BUCKET = 12
    START_FROM_PSC_PUBLISHED_SERVICE = 13
    START_FROM_SERVERLESS_NEG = 14
    APPLY_INGRESS_FIREWALL_RULE = 15
    APPLY_EGRESS_FIREWALL_RULE = 16
    APPLY_ROUTE = 17
    APPLY_FORWARDING_RULE = 18
    ANALYZE_LOAD_BALANCER_BACKEND = 19
    SPOOFING_APPROVED = 20
    ARRIVE_AT_INSTANCE = 21
    ARRIVE_AT_INTERNAL_LOAD_BALANCER = 22
    ARRIVE_AT_EXTERNAL_LOAD_BALANCER = 23
    ARRIVE_AT_VPN_GATEWAY = 24
    ARRIVE_AT_VPN_TUNNEL = 25
    ARRIVE_AT_VPC_CONNECTOR = 26
    DIRECT_VPC_EGRESS_CONNECTION = 27
    SERVERLESS_EXTERNAL_CONNECTION = 28
    NAT = 29
    PROXY_CONNECTION = 30
    DELIVER = 31
    DROP = 32
    FORWARD = 33
    ABORT = 34
    VIEWER_PERMISSION_MISSING = 35

  abort = _messages.MessageField('AbortInfo', 1)
  appEngineVersion = _messages.MessageField('AppEngineVersionInfo', 2)
  causesDrop = _messages.BooleanField(3)
  cloudFunction = _messages.MessageField('CloudFunctionInfo', 4)
  cloudRunRevision = _messages.MessageField('CloudRunRevisionInfo', 5)
  cloudSqlInstance = _messages.MessageField('CloudSQLInstanceInfo', 6)
  deliver = _messages.MessageField('DeliverInfo', 7)
  description = _messages.StringField(8)
  directVpcEgressConnection = _messages.MessageField('DirectVpcEgressConnectionInfo', 9)
  drop = _messages.MessageField('DropInfo', 10)
  endpoint = _messages.MessageField('EndpointInfo', 11)
  firewall = _messages.MessageField('FirewallInfo', 12)
  forward = _messages.MessageField('ForwardInfo', 13)
  forwardingRule = _messages.MessageField('ForwardingRuleInfo', 14)
  gkeMaster = _messages.MessageField('GKEMasterInfo', 15)
  googleService = _messages.MessageField('GoogleServiceInfo', 16)
  instance = _messages.MessageField('InstanceInfo', 17)
  loadBalancer = _messages.MessageField('LoadBalancerInfo', 18)
  loadBalancerBackendInfo = _messages.MessageField('LoadBalancerBackendInfo', 19)
  nat = _messages.MessageField('NatInfo', 20)
  network = _messages.MessageField('NetworkInfo', 21)
  projectId = _messages.StringField(22)
  proxyConnection = _messages.MessageField('ProxyConnectionInfo', 23)
  redisCluster = _messages.MessageField('RedisClusterInfo', 24)
  redisInstance = _messages.MessageField('RedisInstanceInfo', 25)
  route = _messages.MessageField('RouteInfo', 26)
  serverlessExternalConnection = _messages.MessageField('ServerlessExternalConnectionInfo', 27)
  serverlessNeg = _messages.MessageField('ServerlessNegInfo', 28)
  state = _messages.EnumField('StateValueValuesEnum', 29)
  storageBucket = _messages.MessageField('StorageBucketInfo', 30)
  vpcConnector = _messages.MessageField('VpcConnectorInfo', 31)
  vpnGateway = _messages.MessageField('VpnGatewayInfo', 32)
  vpnTunnel = _messages.MessageField('VpnTunnelInfo', 33)


class StorageBucketInfo(_messages.Message):
  r"""For display only. Metadata associated with Storage Bucket.

  Fields:
    bucket: Cloud Storage Bucket name.
  """

  bucket = _messages.StringField(1)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class Trace(_messages.Message):
  r"""Trace represents one simulated packet forwarding path. * Each trace
  contains multiple ordered steps. * Each step is in a particular state with
  associated configuration. * State is categorized as final or non-final
  states. * Each final state has a reason associated. * Each trace must end
  with a final state (the last step). ```
  |---------------------Trace----------------------| Step1(State) Step2(State)
  --- StepN(State(final)) ```

  Fields:
    endpointInfo: Derived from the source and destination endpoints definition
      specified by user request, and validated by the data plane model. If
      there are multiple traces starting from different source locations, then
      the endpoint_info may be different between traces.
    forwardTraceId: ID of trace. For forward traces, this ID is unique for
      each trace. For return traces, it matches ID of associated forward
      trace. A single forward trace can be associated with none, one or more
      than one return trace.
    steps: A trace of a test contains multiple steps from the initial state to
      the final state (delivered, dropped, forwarded, or aborted). The steps
      are ordered by the processing sequence within the simulated network
      state machine. It is critical to preserve the order of the steps and
      avoid reordering or sorting them.
  """

  endpointInfo = _messages.MessageField('EndpointInfo', 1)
  forwardTraceId = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  steps = _messages.MessageField('Step', 3, repeated=True)


class VpcConnectorInfo(_messages.Message):
  r"""For display only. Metadata associated with a VPC connector.

  Fields:
    displayName: Name of a VPC connector.
    location: Location in which the VPC connector is deployed.
    uri: URI of a VPC connector.
  """

  displayName = _messages.StringField(1)
  location = _messages.StringField(2)
  uri = _messages.StringField(3)


class VpcFlowLogsConfig(_messages.Message):
  r"""A configuration to generate VPC Flow Logs.

  Enums:
    AggregationIntervalValueValuesEnum: Optional. The aggregation interval for
      the logs. Default value is INTERVAL_5_SEC.
    MetadataValueValuesEnum: Optional. Configures whether all, none or a
      subset of metadata fields should be added to the reported VPC flow logs.
      Default value is INCLUDE_ALL_METADATA.
    StateValueValuesEnum: Optional. The state of the VPC Flow Log
      configuration. Default value is ENABLED. When creating a new
      configuration, it must be enabled. Setting state=DISABLED will pause the
      log generation for this config.
    TargetResourceStateValueValuesEnum: Output only. A diagnostic bit -
      describes the state of the configured target resource for diagnostic
      purposes.

  Messages:
    LabelsValue: Optional. Resource labels to represent user-provided
      metadata.

  Fields:
    aggregationInterval: Optional. The aggregation interval for the logs.
      Default value is INTERVAL_5_SEC.
    createTime: Output only. The time the config was created.
    description: Optional. The user-supplied description of the VPC Flow Logs
      configuration. Maximum of 512 characters.
    filterExpr: Optional. Export filter used to define which VPC Flow Logs
      should be logged.
    flowSampling: Optional. The value of the field must be in (0, 1]. The
      sampling rate of VPC Flow Logs where 1.0 means all collected logs are
      reported. Setting the sampling rate to 0.0 is not allowed. If you want
      to disable VPC Flow Logs, use the state field instead. Default value is
      1.0.
    interconnectAttachment: Traffic will be logged from the Interconnect
      Attachment. Format:
      projects/{project_id}/regions/{region}/interconnectAttachments/{name}
    labels: Optional. Resource labels to represent user-provided metadata.
    metadata: Optional. Configures whether all, none or a subset of metadata
      fields should be added to the reported VPC flow logs. Default value is
      INCLUDE_ALL_METADATA.
    metadataFields: Optional. Custom metadata fields to include in the
      reported VPC flow logs. Can only be specified if "metadata" was set to
      CUSTOM_METADATA.
    name: Identifier. Unique name of the configuration using the form: `projec
      ts/{project_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_confi
      g_id}`
    state: Optional. The state of the VPC Flow Log configuration. Default
      value is ENABLED. When creating a new configuration, it must be enabled.
      Setting state=DISABLED will pause the log generation for this config.
    targetResourceState: Output only. A diagnostic bit - describes the state
      of the configured target resource for diagnostic purposes.
    updateTime: Output only. The time the config was updated.
    vpnTunnel: Traffic will be logged from the VPN Tunnel. Format:
      projects/{project_id}/regions/{region}/vpnTunnels/{name}
  """

  class AggregationIntervalValueValuesEnum(_messages.Enum):
    r"""Optional. The aggregation interval for the logs. Default value is
    INTERVAL_5_SEC.

    Values:
      AGGREGATION_INTERVAL_UNSPECIFIED: If not specified, will default to
        INTERVAL_5_SEC.
      INTERVAL_5_SEC: Aggregate logs in 5s intervals.
      INTERVAL_30_SEC: Aggregate logs in 30s intervals.
      INTERVAL_1_MIN: Aggregate logs in 1m intervals.
      INTERVAL_5_MIN: Aggregate logs in 5m intervals.
      INTERVAL_10_MIN: Aggregate logs in 10m intervals.
      INTERVAL_15_MIN: Aggregate logs in 15m intervals.
    """
    AGGREGATION_INTERVAL_UNSPECIFIED = 0
    INTERVAL_5_SEC = 1
    INTERVAL_30_SEC = 2
    INTERVAL_1_MIN = 3
    INTERVAL_5_MIN = 4
    INTERVAL_10_MIN = 5
    INTERVAL_15_MIN = 6

  class MetadataValueValuesEnum(_messages.Enum):
    r"""Optional. Configures whether all, none or a subset of metadata fields
    should be added to the reported VPC flow logs. Default value is
    INCLUDE_ALL_METADATA.

    Values:
      METADATA_UNSPECIFIED: If not specified, will default to
        INCLUDE_ALL_METADATA.
      INCLUDE_ALL_METADATA: Include all metadata fields.
      EXCLUDE_ALL_METADATA: Exclude all metadata fields.
      CUSTOM_METADATA: Include only custom fields (specified in
        metadata_fields).
    """
    METADATA_UNSPECIFIED = 0
    INCLUDE_ALL_METADATA = 1
    EXCLUDE_ALL_METADATA = 2
    CUSTOM_METADATA = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""Optional. The state of the VPC Flow Log configuration. Default value
    is ENABLED. When creating a new configuration, it must be enabled. Setting
    state=DISABLED will pause the log generation for this config.

    Values:
      STATE_UNSPECIFIED: If not specified, will default to ENABLED.
      ENABLED: When ENABLED, this configuration will generate logs.
      DISABLED: When DISABLED, this configuration will not generate logs.
    """
    STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class TargetResourceStateValueValuesEnum(_messages.Enum):
    r"""Output only. A diagnostic bit - describes the state of the configured
    target resource for diagnostic purposes.

    Values:
      TARGET_RESOURCE_STATE_UNSPECIFIED: Unspecified target resource state.
      TARGET_RESOURCE_EXISTS: Indicates that the target resource exists.
      TARGET_RESOURCE_DOES_NOT_EXIST: Indicates that the target resource does
        not exist.
    """
    TARGET_RESOURCE_STATE_UNSPECIFIED = 0
    TARGET_RESOURCE_EXISTS = 1
    TARGET_RESOURCE_DOES_NOT_EXIST = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user-provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  aggregationInterval = _messages.EnumField('AggregationIntervalValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  filterExpr = _messages.StringField(4)
  flowSampling = _messages.FloatField(5, variant=_messages.Variant.FLOAT)
  interconnectAttachment = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  metadata = _messages.EnumField('MetadataValueValuesEnum', 8)
  metadataFields = _messages.StringField(9, repeated=True)
  name = _messages.StringField(10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  targetResourceState = _messages.EnumField('TargetResourceStateValueValuesEnum', 12)
  updateTime = _messages.StringField(13)
  vpnTunnel = _messages.StringField(14)


class VpnGatewayInfo(_messages.Message):
  r"""For display only. Metadata associated with a Compute Engine VPN gateway.

  Fields:
    displayName: Name of a VPN gateway.
    ipAddress: IP address of the VPN gateway.
    networkUri: URI of a Compute Engine network where the VPN gateway is
      configured.
    region: Name of a Google Cloud region where this VPN gateway is
      configured.
    uri: URI of a VPN gateway.
    vpnTunnelUri: A VPN tunnel that is associated with this VPN gateway. There
      may be multiple VPN tunnels configured on a VPN gateway, and only the
      one relevant to the test is displayed.
  """

  displayName = _messages.StringField(1)
  ipAddress = _messages.StringField(2)
  networkUri = _messages.StringField(3)
  region = _messages.StringField(4)
  uri = _messages.StringField(5)
  vpnTunnelUri = _messages.StringField(6)


class VpnTunnelInfo(_messages.Message):
  r"""For display only. Metadata associated with a Compute Engine VPN tunnel.

  Enums:
    RoutingTypeValueValuesEnum: Type of the routing policy.

  Fields:
    displayName: Name of a VPN tunnel.
    networkUri: URI of a Compute Engine network where the VPN tunnel is
      configured.
    region: Name of a Google Cloud region where this VPN tunnel is configured.
    remoteGateway: URI of a VPN gateway at remote end of the tunnel.
    remoteGatewayIp: Remote VPN gateway's IP address.
    routingType: Type of the routing policy.
    sourceGateway: URI of the VPN gateway at local end of the tunnel.
    sourceGatewayIp: Local VPN gateway's IP address.
    uri: URI of a VPN tunnel.
  """

  class RoutingTypeValueValuesEnum(_messages.Enum):
    r"""Type of the routing policy.

    Values:
      ROUTING_TYPE_UNSPECIFIED: Unspecified type. Default value.
      ROUTE_BASED: Route based VPN.
      POLICY_BASED: Policy based routing.
      DYNAMIC: Dynamic (BGP) routing.
    """
    ROUTING_TYPE_UNSPECIFIED = 0
    ROUTE_BASED = 1
    POLICY_BASED = 2
    DYNAMIC = 3

  displayName = _messages.StringField(1)
  networkUri = _messages.StringField(2)
  region = _messages.StringField(3)
  remoteGateway = _messages.StringField(4)
  remoteGatewayIp = _messages.StringField(5)
  routingType = _messages.EnumField('RoutingTypeValueValuesEnum', 6)
  sourceGateway = _messages.StringField(7)
  sourceGatewayIp = _messages.StringField(8)
  uri = _messages.StringField(9)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    NetworkmanagementProjectsLocationsGlobalConnectivityTestsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
