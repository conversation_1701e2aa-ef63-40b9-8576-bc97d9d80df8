"""Generated message classes for policytroubleshooter version v3alpha.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'policytroubleshooter'


class GoogleApiExprEnumValue(_messages.Message):
  r"""An enum value.

  Fields:
    type: The fully qualified name of the enum type.
    value: The value of the enum.
  """

  type = _messages.StringField(1)
  value = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleApiExprListValue(_messages.Message):
  r"""A list. Wrapped in a message so 'not set' and empty can be
  differentiated, which is required for use in a 'oneof'.

  Fields:
    values: The ordered values in the list.
  """

  values = _messages.MessageField('GoogleApiExprValue', 1, repeated=True)


class GoogleApiExprMapValue(_messages.Message):
  r"""A map. Wrapped in a message so 'not set' and empty can be
  differentiated, which is required for use in a 'oneof'.

  Fields:
    entries: The set of map entries. CEL has fewer restrictions on keys, so a
      protobuf map representation cannot be used.
  """

  entries = _messages.MessageField('GoogleApiExprMapValueEntry', 1, repeated=True)


class GoogleApiExprMapValueEntry(_messages.Message):
  r"""A GoogleApiExprMapValueEntry object.

  Fields:
    key: The key. Must be unique with in the map. Currently only boolean, int,
      uint, and string values can be keys.
    value: The value.
  """

  key = _messages.MessageField('GoogleApiExprValue', 1)
  value = _messages.MessageField('GoogleApiExprValue', 2)


class GoogleApiExprValue(_messages.Message):
  r"""Represents a CEL value. This is similar to `google.protobuf.Value`, but
  can represent CEL's full range of values.

  Enums:
    NullValueValueValuesEnum: Null value.

  Messages:
    ObjectValueValue: The proto message backing an object value.

  Fields:
    boolValue: Boolean value.
    bytesValue: Byte string value.
    doubleValue: Floating point value.
    enumValue: An enum value.
    int64Value: Signed integer value.
    listValue: List value.
    mapValue: Map value.
    nullValue: Null value.
    objectValue: The proto message backing an object value.
    stringValue: UTF-8 string value.
    typeValue: Type value.
    uint64Value: Unsigned integer value.
  """

  class NullValueValueValuesEnum(_messages.Enum):
    r"""Null value.

    Values:
      NULL_VALUE: Null value.
    """
    NULL_VALUE = 0

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ObjectValueValue(_messages.Message):
    r"""The proto message backing an object value.

    Messages:
      AdditionalProperty: An additional property for a ObjectValueValue
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ObjectValueValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  boolValue = _messages.BooleanField(1)
  bytesValue = _messages.BytesField(2)
  doubleValue = _messages.FloatField(3)
  enumValue = _messages.MessageField('GoogleApiExprEnumValue', 4)
  int64Value = _messages.IntegerField(5)
  listValue = _messages.MessageField('GoogleApiExprListValue', 6)
  mapValue = _messages.MessageField('GoogleApiExprMapValue', 7)
  nullValue = _messages.EnumField('NullValueValueValuesEnum', 8)
  objectValue = _messages.MessageField('ObjectValueValue', 9)
  stringValue = _messages.StringField(10)
  typeValue = _messages.StringField(11)
  uint64Value = _messages.IntegerField(12, variant=_messages.Variant.UINT64)


class GoogleCloudAuditAuthorizationLoggingOptions(_messages.Message):
  r"""Authorization-related information used by Cloud Audit Logging.

  Enums:
    PermissionTypeValueValuesEnum: The type of the permission that was
      checked.

  Fields:
    permissionType: The type of the permission that was checked.
  """

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type of the permission that was checked.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: A read of admin (meta) data.
      ADMIN_WRITE: A write of admin (meta) data.
      DATA_READ: A read of standard data.
      DATA_WRITE: A write of standard data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 1)


class GoogleCloudPolicytroubleshooterGcpuseraccessbindingV3alphaGcpUserAccessBindingExplanation(_messages.Message):
  r"""The explanation of the GcpUserAccessBinding. NextTAG: 4

  Enums:
    AccessLevelEvaluationStatesValueListEntryValuesEnum:
    EvalStateValueValuesEnum: Output only. Evaluation state of this
      GcpUserAccessBinding.
    PrincipalStateValueValuesEnum: Output only. Principal evaluation states
      indicating whether the principals match.

  Fields:
    accessLevelEvaluationStates: Output only. Access level evaluation states.
    evalState: Output only. Evaluation state of this GcpUserAccessBinding.
    principalState: Output only. Principal evaluation states indicating
      whether the principals match.
  """

  class AccessLevelEvaluationStatesValueListEntryValuesEnum(_messages.Enum):
    r"""AccessLevelEvaluationStatesValueListEntryValuesEnum enum type.

    Values:
      ACCESS_LEVEL_EVAL_STATE_UNSPECIFIED: Not used
      ACCESS_LEVEL_EVAL_STATE_SATISFIED: The access level is satisfied
      ACCESS_LEVEL_EVAL_STATE_UNSATISFIED: The access level is unsatisfied
      ACCESS_LEVEL_EVAL_STATE_ERROR: The access level is not satisfied nor
        unsatisfied
      ACCESS_LEVEL_EVAL_STATE_NOT_EXIST: The access level does not exist
      ACCESS_LEVEL_EVAL_STATE_INFO_DENIED: No permission to read access
        levels.
    """
    ACCESS_LEVEL_EVAL_STATE_UNSPECIFIED = 0
    ACCESS_LEVEL_EVAL_STATE_SATISFIED = 1
    ACCESS_LEVEL_EVAL_STATE_UNSATISFIED = 2
    ACCESS_LEVEL_EVAL_STATE_ERROR = 3
    ACCESS_LEVEL_EVAL_STATE_NOT_EXIST = 4
    ACCESS_LEVEL_EVAL_STATE_INFO_DENIED = 5

  class EvalStateValueValuesEnum(_messages.Enum):
    r"""Output only. Evaluation state of this GcpUserAccessBinding.

    Values:
      EVAL_STATE_UNSPECIFIED: Not used
      EVAL_STATE_GRANTED: The GcpUserAccessBinding grants the request.
      EVAL_STATE_DENIED: The GcpUserAccessBinding denies the request.
      EVAL_STATE_NOT_APPLICABLE: The GcpUserAccessBinding is not applicable
        for the principal.
      EVAL_STATE_UNKNOWN: / No enough information to get a conclusion.
    """
    EVAL_STATE_UNSPECIFIED = 0
    EVAL_STATE_GRANTED = 1
    EVAL_STATE_DENIED = 2
    EVAL_STATE_NOT_APPLICABLE = 3
    EVAL_STATE_UNKNOWN = 4

  class PrincipalStateValueValuesEnum(_messages.Enum):
    r"""Output only. Principal evaluation states indicating whether the
    principals match.

    Values:
      PRINCIPAL_STATE_UNSPECIFIED: Not used
      PRINCIPAL_STATE_MATCHED: Principal matches the GcpUserAccessBinding
        principal.
      PRINCIPAL_STATE_UNMATCHED: Principal does not match the
        GcpUserAccessBinding principal.
      PRINCIPAL_STATE_NOT_FOUND: GcpUserAccessBinding principal does not
        exist.
      PRINCIPAL_STATE_INFO_DENIED: Principal does not have enough permission
        to read the GcpUserAccessBinding principal.
      PRINCIPAL_STATE_UNSUPPORTED: Denied or target principal is not supported
        to troubleshoot.
    """
    PRINCIPAL_STATE_UNSPECIFIED = 0
    PRINCIPAL_STATE_MATCHED = 1
    PRINCIPAL_STATE_UNMATCHED = 2
    PRINCIPAL_STATE_NOT_FOUND = 3
    PRINCIPAL_STATE_INFO_DENIED = 4
    PRINCIPAL_STATE_UNSUPPORTED = 5

  accessLevelEvaluationStates = _messages.EnumField('AccessLevelEvaluationStatesValueListEntryValuesEnum', 1, repeated=True)
  evalState = _messages.EnumField('EvalStateValueValuesEnum', 2)
  principalState = _messages.EnumField('PrincipalStateValueValuesEnum', 3)


class GoogleCloudPolicytroubleshooterGcpuseraccessbindingV3alphaTroubleshootGcpUserAccessBindingRequest(_messages.Message):
  r"""Request to troubleshoot GcpUserAccessBinding.

  Fields:
    troubleshootingToken: Optional. The troubleshooting token can be generated
      when customers get access denied by the GcpUserAccessBinding.
  """

  troubleshootingToken = _messages.StringField(1)


class GoogleCloudPolicytroubleshooterGcpuseraccessbindingV3alphaTroubleshootGcpUserAccessBindingResponse(_messages.Message):
  r"""Response for troubleshooting GcpUserAccessBinding.

  Enums:
    AccessStateValueValuesEnum: Output only. The access state of the request.

  Fields:
    accessState: Output only. The access state of the request.
    gcpUserAccessBindingExplanations: The explanation of the
      GcpUserAccessBinding.
    principal: The principal email address of the caller.
  """

  class AccessStateValueValuesEnum(_messages.Enum):
    r"""Output only. The access state of the request.

    Values:
      ACCESS_STATE_UNSPECIFIED: Not used
      ACCESS_STATE_GRANTED: The request is granted by GcpUserAccessBinding.
      ACCESS_STATE_DENIED: The request is denied by GcpUserAccessBinding.
      ACCESS_STATE_NOT_APPLICABLE: GcpUserAccessBinding are not applicable to
        principal.
      ACCESS_STATE_UNKNOWN: No enough information to get a conclusion.
    """
    ACCESS_STATE_UNSPECIFIED = 0
    ACCESS_STATE_GRANTED = 1
    ACCESS_STATE_DENIED = 2
    ACCESS_STATE_NOT_APPLICABLE = 3
    ACCESS_STATE_UNKNOWN = 4

  accessState = _messages.EnumField('AccessStateValueValuesEnum', 1)
  gcpUserAccessBindingExplanations = _messages.MessageField('GoogleCloudPolicytroubleshooterGcpuseraccessbindingV3alphaGcpUserAccessBindingExplanation', 2, repeated=True)
  principal = _messages.StringField(3)


class GoogleCloudPolicytroubleshooterIamV3alphaAccessTuple(_messages.Message):
  r"""Information about the principal, resource, and permission to check.

  Fields:
    conditionContext: Optional. Additional context for the request, such as
      the request time or IP address. This context allows Policy
      Troubleshooter to troubleshoot conditional role bindings and deny rules.
    fullResourceName: Required. The full resource name that identifies the
      resource. For example, `//compute.googleapis.com/projects/my-
      project/zones/us-central1-a/instances/my-instance`. For examples of full
      resource names for Google Cloud services, see
      https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
    permission: Required. The IAM permission to check for, either in the `v1`
      permission format or the `v2` permission format. For a complete list of
      IAM permissions in the `v1` format, see
      https://cloud.google.com/iam/help/permissions/reference. For a list of
      IAM permissions in the `v2` format, see
      https://cloud.google.com/iam/help/deny/supported-permissions. For a
      complete list of predefined IAM roles and the permissions in each role,
      see https://cloud.google.com/iam/help/roles/reference.
    permissionFqdn: Output only. The permission that Policy Troubleshooter
      checked for, in the `v2` format.
    principal: Required. The email address of the principal whose access you
      want to check. For example, `<EMAIL>` or `my-service-
      <EMAIL>`. The principal must be a
      Google Account or a service account. Other types of principals are not
      supported.
  """

  conditionContext = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaConditionContext', 1)
  fullResourceName = _messages.StringField(2)
  permission = _messages.StringField(3)
  permissionFqdn = _messages.StringField(4)
  principal = _messages.StringField(5)


class GoogleCloudPolicytroubleshooterIamV3alphaAllowBindingExplanation(_messages.Message):
  r"""Details about how a role binding in an allow policy affects a
  principal's ability to use a permission.

  Enums:
    AllowAccessStateValueValuesEnum: Required. Indicates whether _this role
      binding_ gives the specified permission to the specified principal on
      the specified resource. This field does _not_ indicate whether the
      principal actually has the permission on the resource. There might be
      another role binding that overrides this role binding. To determine
      whether the principal actually has the permission, use the
      `overall_access_state` field in the TroubleshootIamPolicyResponse.
    RelevanceValueValuesEnum: The relevance of this role binding to the
      overall determination for the entire policy.
    RolePermissionValueValuesEnum: Indicates whether the role granted by this
      role binding contains the specified permission.
    RolePermissionRelevanceValueValuesEnum: The relevance of the permission's
      existence, or nonexistence, in the role to the overall determination for
      the entire policy.

  Messages:
    MembershipsValue: Indicates whether each role binding includes the
      principal specified in the request, either directly or indirectly. Each
      key identifies a principal in the role binding, and each value indicates
      whether the principal in the role binding includes the principal in the
      request. For example, suppose that a role binding includes the following
      principals: * `user:<EMAIL>` * `group:<EMAIL>`
      You want to troubleshoot access for `user:<EMAIL>`. This user is
      a member of the group `group:<EMAIL>`. For the first
      principal in the role binding, the key is `user:<EMAIL>`, and
      the `membership` field in the value is set to `NOT_INCLUDED`. For the
      second principal in the role binding, the key is `group:product-
      <EMAIL>`, and the `membership` field in the value is set to
      `INCLUDED`.

  Fields:
    allowAccessState: Required. Indicates whether _this role binding_ gives
      the specified permission to the specified principal on the specified
      resource. This field does _not_ indicate whether the principal actually
      has the permission on the resource. There might be another role binding
      that overrides this role binding. To determine whether the principal
      actually has the permission, use the `overall_access_state` field in the
      TroubleshootIamPolicyResponse.
    combinedMembership: The combined result of all memberships. Indicates if
      the principal is included in any role binding, either directly or
      indirectly.
    condition: A condition expression that specifies when the role binding
      grants access. To learn about IAM Conditions, see
      https://cloud.google.com/iam/help/conditions/overview.
    conditionExplanation: Condition evaluation state for this role binding.
    memberships: Indicates whether each role binding includes the principal
      specified in the request, either directly or indirectly. Each key
      identifies a principal in the role binding, and each value indicates
      whether the principal in the role binding includes the principal in the
      request. For example, suppose that a role binding includes the following
      principals: * `user:<EMAIL>` * `group:<EMAIL>`
      You want to troubleshoot access for `user:<EMAIL>`. This user is
      a member of the group `group:<EMAIL>`. For the first
      principal in the role binding, the key is `user:<EMAIL>`, and
      the `membership` field in the value is set to `NOT_INCLUDED`. For the
      second principal in the role binding, the key is `group:product-
      <EMAIL>`, and the `membership` field in the value is set to
      `INCLUDED`.
    relevance: The relevance of this role binding to the overall determination
      for the entire policy.
    role: The role that this role binding grants. For example,
      `roles/compute.admin`. For a complete list of predefined IAM roles, as
      well as the permissions in each role, see
      https://cloud.google.com/iam/help/roles/reference.
    rolePermission: Indicates whether the role granted by this role binding
      contains the specified permission.
    rolePermissionRelevance: The relevance of the permission's existence, or
      nonexistence, in the role to the overall determination for the entire
      policy.
  """

  class AllowAccessStateValueValuesEnum(_messages.Enum):
    r"""Required. Indicates whether _this role binding_ gives the specified
    permission to the specified principal on the specified resource. This
    field does _not_ indicate whether the principal actually has the
    permission on the resource. There might be another role binding that
    overrides this role binding. To determine whether the principal actually
    has the permission, use the `overall_access_state` field in the
    TroubleshootIamPolicyResponse.

    Values:
      ALLOW_ACCESS_STATE_UNSPECIFIED: Not specified.
      ALLOW_ACCESS_STATE_GRANTED: The allow policy gives the principal the
        permission.
      ALLOW_ACCESS_STATE_NOT_GRANTED: The allow policy doesn't give the
        principal the permission.
      ALLOW_ACCESS_STATE_UNKNOWN_CONDITIONAL: The allow policy gives the
        principal the permission if a condition expression evaluate to `true`.
        However, the sender of the request didn't provide enough context for
        Policy Troubleshooter to evaluate the condition expression.
      ALLOW_ACCESS_STATE_UNKNOWN_INFO: The sender of the request doesn't have
        access to all of the allow policies that Policy Troubleshooter needs
        to evaluate the principal's access.
    """
    ALLOW_ACCESS_STATE_UNSPECIFIED = 0
    ALLOW_ACCESS_STATE_GRANTED = 1
    ALLOW_ACCESS_STATE_NOT_GRANTED = 2
    ALLOW_ACCESS_STATE_UNKNOWN_CONDITIONAL = 3
    ALLOW_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this role binding to the overall determination for
    the entire policy.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  class RolePermissionRelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the permission's existence, or nonexistence, in the
    role to the overall determination for the entire policy.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  class RolePermissionValueValuesEnum(_messages.Enum):
    r"""Indicates whether the role granted by this role binding contains the
    specified permission.

    Values:
      ROLE_PERMISSION_INCLUSION_STATE_UNSPECIFIED: Not specified.
      ROLE_PERMISSION_INCLUDED: The permission is included in the role.
      ROLE_PERMISSION_NOT_INCLUDED: The permission is not included in the
        role.
      ROLE_PERMISSION_UNKNOWN_INFO: The sender of the request is not allowed
        to access the role definition.
    """
    ROLE_PERMISSION_INCLUSION_STATE_UNSPECIFIED = 0
    ROLE_PERMISSION_INCLUDED = 1
    ROLE_PERMISSION_NOT_INCLUDED = 2
    ROLE_PERMISSION_UNKNOWN_INFO = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MembershipsValue(_messages.Message):
    r"""Indicates whether each role binding includes the principal specified
    in the request, either directly or indirectly. Each key identifies a
    principal in the role binding, and each value indicates whether the
    principal in the role binding includes the principal in the request. For
    example, suppose that a role binding includes the following principals: *
    `user:<EMAIL>` * `group:<EMAIL>` You want to
    troubleshoot access for `user:<EMAIL>`. This user is a member of
    the group `group:<EMAIL>`. For the first principal in the
    role binding, the key is `user:<EMAIL>`, and the `membership`
    field in the value is set to `NOT_INCLUDED`. For the second principal in
    the role binding, the key is `group:<EMAIL>`, and the
    `membership` field in the value is set to `INCLUDED`.

    Messages:
      AdditionalProperty: An additional property for a MembershipsValue
        object.

    Fields:
      additionalProperties: Additional properties of type MembershipsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MembershipsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudPolicytroubleshooterIamV3alphaAllowBindingExplanat
          ionAnnotatedAllowMembership attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaAllowBindingExplanationAnnotatedAllowMembership', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowAccessState = _messages.EnumField('AllowAccessStateValueValuesEnum', 1)
  combinedMembership = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaAllowBindingExplanationAnnotatedAllowMembership', 2)
  condition = _messages.MessageField('GoogleTypeExpr', 3)
  conditionExplanation = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaConditionExplanation', 4)
  memberships = _messages.MessageField('MembershipsValue', 5)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 6)
  role = _messages.StringField(7)
  rolePermission = _messages.EnumField('RolePermissionValueValuesEnum', 8)
  rolePermissionRelevance = _messages.EnumField('RolePermissionRelevanceValueValuesEnum', 9)


class GoogleCloudPolicytroubleshooterIamV3alphaAllowBindingExplanationAnnotatedAllowMembership(_messages.Message):
  r"""Details about whether the role binding includes the principal.

  Enums:
    MembershipValueValuesEnum: Indicates whether the role binding includes the
      principal.
    RelevanceValueValuesEnum: The relevance of the principal's status to the
      overall determination for the role binding.

  Fields:
    membership: Indicates whether the role binding includes the principal.
    relevance: The relevance of the principal's status to the overall
      determination for the role binding.
  """

  class MembershipValueValuesEnum(_messages.Enum):
    r"""Indicates whether the role binding includes the principal.

    Values:
      MEMBERSHIP_MATCHING_STATE_UNSPECIFIED: Not specified.
      MEMBERSHIP_MATCHED: The principal in the request matches the principal
        in the policy. The principal can be included directly or indirectly: *
        A principal is included directly if that principal is listed in the
        role binding. * A principal is included indirectly if that principal
        is in a Google group, Google Workspace account, or Cloud Identity
        domain that is listed in the policy.
      MEMBERSHIP_NOT_MATCHED: The principal in the request doesn't match the
        principal in the policy.
      MEMBERSHIP_UNKNOWN_INFO: The principal in the policy is a group or
        domain, and the sender of the request doesn't have permission to view
        whether the principal in the request is a member of the group or
        domain.
      MEMBERSHIP_UNKNOWN_UNSUPPORTED: The principal is an unsupported type.
    """
    MEMBERSHIP_MATCHING_STATE_UNSPECIFIED = 0
    MEMBERSHIP_MATCHED = 1
    MEMBERSHIP_NOT_MATCHED = 2
    MEMBERSHIP_UNKNOWN_INFO = 3
    MEMBERSHIP_UNKNOWN_UNSUPPORTED = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the principal's status to the overall determination
    for the role binding.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  membership = _messages.EnumField('MembershipValueValuesEnum', 1)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 2)


class GoogleCloudPolicytroubleshooterIamV3alphaAllowPolicyExplanation(_messages.Message):
  r"""Details about how the relevant IAM allow policies affect the final
  access state.

  Enums:
    AllowAccessStateValueValuesEnum: Indicates whether the principal has the
      specified permission for the specified resource, based on evaluating all
      applicable IAM allow policies.
    RelevanceValueValuesEnum: The relevance of the allow policy type to the
      overall access state.

  Fields:
    allowAccessState: Indicates whether the principal has the specified
      permission for the specified resource, based on evaluating all
      applicable IAM allow policies.
    explainedPolicies: List of IAM allow policies that were evaluated to check
      the principal's permissions, with annotations to indicate how each
      policy contributed to the final result. The list of policies includes
      the policy for the resource itself, as well as allow policies that are
      inherited from higher levels of the resource hierarchy, including the
      organization, the folder, and the project. To learn more about the
      resource hierarchy, see https://cloud.google.com/iam/help/resource-
      hierarchy.
    relevance: The relevance of the allow policy type to the overall access
      state.
  """

  class AllowAccessStateValueValuesEnum(_messages.Enum):
    r"""Indicates whether the principal has the specified permission for the
    specified resource, based on evaluating all applicable IAM allow policies.

    Values:
      ALLOW_ACCESS_STATE_UNSPECIFIED: Not specified.
      ALLOW_ACCESS_STATE_GRANTED: The allow policy gives the principal the
        permission.
      ALLOW_ACCESS_STATE_NOT_GRANTED: The allow policy doesn't give the
        principal the permission.
      ALLOW_ACCESS_STATE_UNKNOWN_CONDITIONAL: The allow policy gives the
        principal the permission if a condition expression evaluate to `true`.
        However, the sender of the request didn't provide enough context for
        Policy Troubleshooter to evaluate the condition expression.
      ALLOW_ACCESS_STATE_UNKNOWN_INFO: The sender of the request doesn't have
        access to all of the allow policies that Policy Troubleshooter needs
        to evaluate the principal's access.
    """
    ALLOW_ACCESS_STATE_UNSPECIFIED = 0
    ALLOW_ACCESS_STATE_GRANTED = 1
    ALLOW_ACCESS_STATE_NOT_GRANTED = 2
    ALLOW_ACCESS_STATE_UNKNOWN_CONDITIONAL = 3
    ALLOW_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the allow policy type to the overall access state.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  allowAccessState = _messages.EnumField('AllowAccessStateValueValuesEnum', 1)
  explainedPolicies = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaExplainedAllowPolicy', 2, repeated=True)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 3)


class GoogleCloudPolicytroubleshooterIamV3alphaConditionContext(_messages.Message):
  r"""Additional context for troubleshooting conditional role bindings and
  deny rules.

  Fields:
    destination: The destination of a network activity, such as accepting a
      TCP connection. In a multi-hop network activity, the destination
      represents the receiver of the last hop.
    effectiveTags: Output only. The effective tags on the resource. The
      effective tags are fetched during troubleshooting.
    request: Represents a network request, such as an HTTP request.
    resource: Represents a target resource that is involved with a network
      activity. If multiple resources are involved with an activity, this must
      be the primary one.
  """

  destination = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaConditionContextPeer', 1)
  effectiveTags = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaConditionContextEffectiveTag', 2, repeated=True)
  request = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaConditionContextRequest', 3)
  resource = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaConditionContextResource', 4)


class GoogleCloudPolicytroubleshooterIamV3alphaConditionContextEffectiveTag(_messages.Message):
  r"""A tag that applies to a resource during policy evaluation. Tags can be
  either directly bound to a resource or inherited from its ancestor.
  `EffectiveTag` contains the `name` and `namespaced_name` of the tag value
  and tag key, with additional fields of `inherited` to indicate the
  inheritance status of the effective tag.

  Fields:
    inherited: Output only. Indicates the inheritance status of a tag value
      attached to the given resource. If the tag value is inherited from one
      of the resource's ancestors, inherited will be true. If false, then the
      tag value is directly attached to the resource, inherited will be false.
    namespacedTagKey: Output only. The namespaced name of the TagKey. Can be
      in the form `{organization_id}/{tag_key_short_name}` or
      `{project_id}/{tag_key_short_name}` or
      `{project_number}/{tag_key_short_name}`.
    namespacedTagValue: Output only. The namespaced name of the TagValue. Can
      be in the form
      `{organization_id}/{tag_key_short_name}/{tag_value_short_name}` or
      `{project_id}/{tag_key_short_name}/{tag_value_short_name}` or
      `{project_number}/{tag_key_short_name}/{tag_value_short_name}`.
    tagKey: Output only. The name of the TagKey, in the format `tagKeys/{id}`,
      such as `tagKeys/123`.
    tagKeyParentName: The parent name of the tag key. Must be in the format
      `organizations/{organization_id}` or `projects/{project_number}`
    tagValue: Output only. Resource name for TagValue in the format
      `tagValues/456`.
  """

  inherited = _messages.BooleanField(1)
  namespacedTagKey = _messages.StringField(2)
  namespacedTagValue = _messages.StringField(3)
  tagKey = _messages.StringField(4)
  tagKeyParentName = _messages.StringField(5)
  tagValue = _messages.StringField(6)


class GoogleCloudPolicytroubleshooterIamV3alphaConditionContextPeer(_messages.Message):
  r"""This message defines attributes for a node that handles a network
  request. The node can be either a service or an application that sends,
  forwards, or receives the request. Service peers should fill in `principal`
  and `labels` as appropriate.

  Fields:
    ip: The IPv4 or IPv6 address of the peer.
    port: The network port of the peer.
  """

  ip = _messages.StringField(1)
  port = _messages.IntegerField(2)


class GoogleCloudPolicytroubleshooterIamV3alphaConditionContextRequest(_messages.Message):
  r"""This message defines attributes for an HTTP request. If the actual
  request is not an HTTP request, the runtime system should try to map the
  actual request to an equivalent HTTP request.

  Fields:
    receiveTime: Optional. The timestamp when the destination service receives
      the first byte of the request.
    satisfiedAccessLevels: Optional. The information for access levels that
      are satisfied for the given access tuple.
    unsatisfiedAccessLevels: Optional. The information for access levels that
      are unsatisfied for the given access tuple.
  """

  receiveTime = _messages.StringField(1)
  satisfiedAccessLevels = _messages.StringField(2, repeated=True)
  unsatisfiedAccessLevels = _messages.StringField(3, repeated=True)


class GoogleCloudPolicytroubleshooterIamV3alphaConditionContextResource(_messages.Message):
  r"""Core attributes for a resource. A resource is an addressable (named)
  entity provided by the destination service. For example, a Compute Engine
  instance.

  Fields:
    name: The stable identifier (name) of a resource on the `service`. A
      resource can be logically identified as
      `//{resource.service}/{resource.name}`. Unlike the resource URI, the
      resource name doesn't contain any protocol and version information. For
      a list of full resource name formats, see
      https://cloud.google.com/iam/help/troubleshooter/full-resource-names
    service: The name of the service that this resource belongs to, such as
      `compute.googleapis.com`. The service name might not match the DNS
      hostname that actually serves the request. For a full list of resource
      service values, see
      https://cloud.google.com/iam/help/conditions/resource-services
    type: The type of the resource, in the format `{service}/{kind}`. For a
      full list of resource type values, see
      https://cloud.google.com/iam/help/conditions/resource-types
  """

  name = _messages.StringField(1)
  service = _messages.StringField(2)
  type = _messages.StringField(3)


class GoogleCloudPolicytroubleshooterIamV3alphaConditionExplanation(_messages.Message):
  r"""Explanation for how a condition affects a principal's access

  Fields:
    errors: Any errors that prevented complete evaluation of the condition
      expression.
    evaluationStates: The value of each statement of the condition expression.
      The value can be `true`, `false`, or `null`. The value is `null` if the
      statement can't be evaluated.
    value: Value of the condition.
  """

  errors = _messages.MessageField('GoogleRpcStatus', 1, repeated=True)
  evaluationStates = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaConditionExplanationEvaluationState', 2, repeated=True)
  value = _messages.MessageField('extra_types.JsonValue', 3)


class GoogleCloudPolicytroubleshooterIamV3alphaConditionExplanationEvaluationState(_messages.Message):
  r"""Evaluated state of a condition expression.

  Fields:
    end: End position of an expression in the condition, by character, end
      included, for example: the end position of the first part of `a==b ||
      c==d` would be 4.
    errors: Any errors that prevented complete evaluation of the condition
      expression.
    start: Start position of an expression in the condition, by character.
    value: Value of this expression.
  """

  end = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  errors = _messages.MessageField('GoogleRpcStatus', 2, repeated=True)
  start = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  value = _messages.MessageField('extra_types.JsonValue', 4)


class GoogleCloudPolicytroubleshooterIamV3alphaDenyPolicyExplanation(_messages.Message):
  r"""Details about how the relevant IAM deny policies affect the final access
  state.

  Enums:
    DenyAccessStateValueValuesEnum: Indicates whether the principal is denied
      the specified permission for the specified resource, based on evaluating
      all applicable IAM deny policies.
    RelevanceValueValuesEnum: The relevance of the deny policy result to the
      overall access state.

  Fields:
    denyAccessState: Indicates whether the principal is denied the specified
      permission for the specified resource, based on evaluating all
      applicable IAM deny policies.
    explainedResources: List of resources with IAM deny policies that were
      evaluated to check the principal's denied permissions, with annotations
      to indicate how each policy contributed to the final result. The list of
      resources includes the policy for the resource itself, as well as
      policies that are inherited from higher levels of the resource
      hierarchy, including the organization, the folder, and the project. The
      order of the resources starts from the resource and climbs up the
      resource hierarchy. To learn more about the resource hierarchy, see
      https://cloud.google.com/iam/help/resource-hierarchy.
    permissionDeniable: Indicates whether the permission to troubleshoot is
      supported in deny policies.
    relevance: The relevance of the deny policy result to the overall access
      state.
  """

  class DenyAccessStateValueValuesEnum(_messages.Enum):
    r"""Indicates whether the principal is denied the specified permission for
    the specified resource, based on evaluating all applicable IAM deny
    policies.

    Values:
      DENY_ACCESS_STATE_UNSPECIFIED: Not specified.
      DENY_ACCESS_STATE_DENIED: The deny policy denies the principal the
        permission.
      DENY_ACCESS_STATE_NOT_DENIED: The deny policy doesn't deny the principal
        the permission.
      DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL: The deny policy denies the
        principal the permission if a condition expression evaluates to
        `true`. However, the sender of the request didn't provide enough
        context for Policy Troubleshooter to evaluate the condition
        expression.
      DENY_ACCESS_STATE_UNKNOWN_INFO: The sender of the request does not have
        access to all of the deny policies that Policy Troubleshooter needs to
        evaluate the principal's access.
    """
    DENY_ACCESS_STATE_UNSPECIFIED = 0
    DENY_ACCESS_STATE_DENIED = 1
    DENY_ACCESS_STATE_NOT_DENIED = 2
    DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL = 3
    DENY_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the deny policy result to the overall access state.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  denyAccessState = _messages.EnumField('DenyAccessStateValueValuesEnum', 1)
  explainedResources = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaExplainedDenyResource', 2, repeated=True)
  permissionDeniable = _messages.BooleanField(3)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 4)


class GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanation(_messages.Message):
  r"""Details about how a deny rule in a deny policy affects a principal's
  ability to use a permission.

  Enums:
    DenyAccessStateValueValuesEnum: Required. Indicates whether _this rule_
      denies the specified permission to the specified principal for the
      specified resource. This field does _not_ indicate whether the principal
      is actually denied on the permission for the resource. There might be
      another rule that overrides this rule. To determine whether the
      principal actually has the permission, use the `overall_access_state`
      field in the TroubleshootIamPolicyResponse.
    RelevanceValueValuesEnum: The relevance of this role binding to the
      overall determination for the entire policy.

  Messages:
    DeniedPermissionsValue: Lists all denied permissions in the deny rule and
      indicates whether each permission matches the permission in the request.
      Each key identifies a denied permission in the rule, and each value
      indicates whether the denied permission matches the permission in the
      request.
    DeniedPrincipalsValue: Lists all denied principals in the deny rule and
      indicates whether each principal matches the principal in the request,
      either directly or through membership in a principal set. Each key
      identifies a denied principal in the rule, and each value indicates
      whether the denied principal matches the principal in the request.
    ExceptionPermissionsValue: Lists all exception permissions in the deny
      rule and indicates whether each permission matches the permission in the
      request. Each key identifies a exception permission in the rule, and
      each value indicates whether the exception permission matches the
      permission in the request.
    ExceptionPrincipalsValue: Lists all exception principals in the deny rule
      and indicates whether each principal matches the principal in the
      request, either directly or through membership in a principal set. Each
      key identifies a exception principal in the rule, and each value
      indicates whether the exception principal matches the principal in the
      request.

  Fields:
    combinedDeniedPermission: Indicates whether the permission in the request
      is listed as a denied permission in the deny rule.
    combinedDeniedPrincipal: Indicates whether the principal is listed as a
      denied principal in the deny rule, either directly or through membership
      in a principal set.
    combinedExceptionPermission: Indicates whether the permission in the
      request is listed as an exception permission in the deny rule.
    combinedExceptionPrincipal: Indicates whether the principal is listed as
      an exception principal in the deny rule, either directly or through
      membership in a principal set.
    condition: A condition expression that specifies when the deny rule denies
      the principal access. To learn about IAM Conditions, see
      https://cloud.google.com/iam/help/conditions/overview.
    conditionExplanation: Condition evaluation state for this role binding.
    deniedPermissions: Lists all denied permissions in the deny rule and
      indicates whether each permission matches the permission in the request.
      Each key identifies a denied permission in the rule, and each value
      indicates whether the denied permission matches the permission in the
      request.
    deniedPrincipals: Lists all denied principals in the deny rule and
      indicates whether each principal matches the principal in the request,
      either directly or through membership in a principal set. Each key
      identifies a denied principal in the rule, and each value indicates
      whether the denied principal matches the principal in the request.
    denyAccessState: Required. Indicates whether _this rule_ denies the
      specified permission to the specified principal for the specified
      resource. This field does _not_ indicate whether the principal is
      actually denied on the permission for the resource. There might be
      another rule that overrides this rule. To determine whether the
      principal actually has the permission, use the `overall_access_state`
      field in the TroubleshootIamPolicyResponse.
    exceptionPermissions: Lists all exception permissions in the deny rule and
      indicates whether each permission matches the permission in the request.
      Each key identifies a exception permission in the rule, and each value
      indicates whether the exception permission matches the permission in the
      request.
    exceptionPrincipals: Lists all exception principals in the deny rule and
      indicates whether each principal matches the principal in the request,
      either directly or through membership in a principal set. Each key
      identifies a exception principal in the rule, and each value indicates
      whether the exception principal matches the principal in the request.
    relevance: The relevance of this role binding to the overall determination
      for the entire policy.
  """

  class DenyAccessStateValueValuesEnum(_messages.Enum):
    r"""Required. Indicates whether _this rule_ denies the specified
    permission to the specified principal for the specified resource. This
    field does _not_ indicate whether the principal is actually denied on the
    permission for the resource. There might be another rule that overrides
    this rule. To determine whether the principal actually has the permission,
    use the `overall_access_state` field in the TroubleshootIamPolicyResponse.

    Values:
      DENY_ACCESS_STATE_UNSPECIFIED: Not specified.
      DENY_ACCESS_STATE_DENIED: The deny policy denies the principal the
        permission.
      DENY_ACCESS_STATE_NOT_DENIED: The deny policy doesn't deny the principal
        the permission.
      DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL: The deny policy denies the
        principal the permission if a condition expression evaluates to
        `true`. However, the sender of the request didn't provide enough
        context for Policy Troubleshooter to evaluate the condition
        expression.
      DENY_ACCESS_STATE_UNKNOWN_INFO: The sender of the request does not have
        access to all of the deny policies that Policy Troubleshooter needs to
        evaluate the principal's access.
    """
    DENY_ACCESS_STATE_UNSPECIFIED = 0
    DENY_ACCESS_STATE_DENIED = 1
    DENY_ACCESS_STATE_NOT_DENIED = 2
    DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL = 3
    DENY_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this role binding to the overall determination for
    the entire policy.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DeniedPermissionsValue(_messages.Message):
    r"""Lists all denied permissions in the deny rule and indicates whether
    each permission matches the permission in the request. Each key identifies
    a denied permission in the rule, and each value indicates whether the
    denied permission matches the permission in the request.

    Messages:
      AdditionalProperty: An additional property for a DeniedPermissionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        DeniedPermissionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DeniedPermissionsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanationA
          nnotatedPermissionMatching attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanationAnnotatedPermissionMatching', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DeniedPrincipalsValue(_messages.Message):
    r"""Lists all denied principals in the deny rule and indicates whether
    each principal matches the principal in the request, either directly or
    through membership in a principal set. Each key identifies a denied
    principal in the rule, and each value indicates whether the denied
    principal matches the principal in the request.

    Messages:
      AdditionalProperty: An additional property for a DeniedPrincipalsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        DeniedPrincipalsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DeniedPrincipalsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanationA
          nnotatedDenyPrincipalMatching attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanationAnnotatedDenyPrincipalMatching', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExceptionPermissionsValue(_messages.Message):
    r"""Lists all exception permissions in the deny rule and indicates whether
    each permission matches the permission in the request. Each key identifies
    a exception permission in the rule, and each value indicates whether the
    exception permission matches the permission in the request.

    Messages:
      AdditionalProperty: An additional property for a
        ExceptionPermissionsValue object.

    Fields:
      additionalProperties: Additional properties of type
        ExceptionPermissionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExceptionPermissionsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanationA
          nnotatedPermissionMatching attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanationAnnotatedPermissionMatching', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExceptionPrincipalsValue(_messages.Message):
    r"""Lists all exception principals in the deny rule and indicates whether
    each principal matches the principal in the request, either directly or
    through membership in a principal set. Each key identifies a exception
    principal in the rule, and each value indicates whether the exception
    principal matches the principal in the request.

    Messages:
      AdditionalProperty: An additional property for a
        ExceptionPrincipalsValue object.

    Fields:
      additionalProperties: Additional properties of type
        ExceptionPrincipalsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExceptionPrincipalsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanationA
          nnotatedDenyPrincipalMatching attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanationAnnotatedDenyPrincipalMatching', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  combinedDeniedPermission = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanationAnnotatedPermissionMatching', 1)
  combinedDeniedPrincipal = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanationAnnotatedDenyPrincipalMatching', 2)
  combinedExceptionPermission = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanationAnnotatedPermissionMatching', 3)
  combinedExceptionPrincipal = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanationAnnotatedDenyPrincipalMatching', 4)
  condition = _messages.MessageField('GoogleTypeExpr', 5)
  conditionExplanation = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaConditionExplanation', 6)
  deniedPermissions = _messages.MessageField('DeniedPermissionsValue', 7)
  deniedPrincipals = _messages.MessageField('DeniedPrincipalsValue', 8)
  denyAccessState = _messages.EnumField('DenyAccessStateValueValuesEnum', 9)
  exceptionPermissions = _messages.MessageField('ExceptionPermissionsValue', 10)
  exceptionPrincipals = _messages.MessageField('ExceptionPrincipalsValue', 11)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 12)


class GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanationAnnotatedDenyPrincipalMatching(_messages.Message):
  r"""Details about whether the principal in the request is listed as a denied
  principal in the deny rule, either directly or through membership in a
  principal set.

  Enums:
    MembershipValueValuesEnum: Indicates whether the principal is listed as a
      denied principal in the deny rule, either directly or through membership
      in a principal set.
    RelevanceValueValuesEnum: The relevance of the principal's status to the
      overall determination for the role binding.

  Fields:
    membership: Indicates whether the principal is listed as a denied
      principal in the deny rule, either directly or through membership in a
      principal set.
    relevance: The relevance of the principal's status to the overall
      determination for the role binding.
  """

  class MembershipValueValuesEnum(_messages.Enum):
    r"""Indicates whether the principal is listed as a denied principal in the
    deny rule, either directly or through membership in a principal set.

    Values:
      MEMBERSHIP_MATCHING_STATE_UNSPECIFIED: Not specified.
      MEMBERSHIP_MATCHED: The principal in the request matches the principal
        in the policy. The principal can be included directly or indirectly: *
        A principal is included directly if that principal is listed in the
        role binding. * A principal is included indirectly if that principal
        is in a Google group, Google Workspace account, or Cloud Identity
        domain that is listed in the policy.
      MEMBERSHIP_NOT_MATCHED: The principal in the request doesn't match the
        principal in the policy.
      MEMBERSHIP_UNKNOWN_INFO: The principal in the policy is a group or
        domain, and the sender of the request doesn't have permission to view
        whether the principal in the request is a member of the group or
        domain.
      MEMBERSHIP_UNKNOWN_UNSUPPORTED: The principal is an unsupported type.
    """
    MEMBERSHIP_MATCHING_STATE_UNSPECIFIED = 0
    MEMBERSHIP_MATCHED = 1
    MEMBERSHIP_NOT_MATCHED = 2
    MEMBERSHIP_UNKNOWN_INFO = 3
    MEMBERSHIP_UNKNOWN_UNSUPPORTED = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the principal's status to the overall determination
    for the role binding.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  membership = _messages.EnumField('MembershipValueValuesEnum', 1)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 2)


class GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanationAnnotatedPermissionMatching(_messages.Message):
  r"""Details about whether the permission in the request is denied by the
  deny rule.

  Enums:
    PermissionMatchingStateValueValuesEnum: Indicates whether the permission
      in the request is denied by the deny rule.
    RelevanceValueValuesEnum: The relevance of the permission status to the
      overall determination for the rule.

  Fields:
    permissionMatchingState: Indicates whether the permission in the request
      is denied by the deny rule.
    relevance: The relevance of the permission status to the overall
      determination for the rule.
  """

  class PermissionMatchingStateValueValuesEnum(_messages.Enum):
    r"""Indicates whether the permission in the request is denied by the deny
    rule.

    Values:
      PERMISSION_PATTERN_MATCHING_STATE_UNSPECIFIED: Not specified.
      PERMISSION_PATTERN_MATCHED: The permission in the request matches the
        permission in the policy.
      PERMISSION_PATTERN_NOT_MATCHED: The permission in the request matches
        the permission in the policy.
    """
    PERMISSION_PATTERN_MATCHING_STATE_UNSPECIFIED = 0
    PERMISSION_PATTERN_MATCHED = 1
    PERMISSION_PATTERN_NOT_MATCHED = 2

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the permission status to the overall determination
    for the rule.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  permissionMatchingState = _messages.EnumField('PermissionMatchingStateValueValuesEnum', 1)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 2)


class GoogleCloudPolicytroubleshooterIamV3alphaExplainedAllowPolicy(_messages.Message):
  r"""Details about how a specific IAM allow policy contributed to the final
  access state.

  Enums:
    AllowAccessStateValueValuesEnum: Required. Indicates whether _this policy_
      provides the specified permission to the specified principal for the
      specified resource. This field does _not_ indicate whether the principal
      actually has the permission for the resource. There might be another
      policy that overrides this policy. To determine whether the principal
      actually has the permission, use the `overall_access_state` field in the
      TroubleshootIamPolicyResponse.
    RelevanceValueValuesEnum: The relevance of this policy to the overall
      access state in the TroubleshootIamPolicyResponse. If the sender of the
      request does not have access to the policy, this field is omitted.

  Fields:
    allowAccessState: Required. Indicates whether _this policy_ provides the
      specified permission to the specified principal for the specified
      resource. This field does _not_ indicate whether the principal actually
      has the permission for the resource. There might be another policy that
      overrides this policy. To determine whether the principal actually has
      the permission, use the `overall_access_state` field in the
      TroubleshootIamPolicyResponse.
    bindingExplanations: Details about how each role binding in the policy
      affects the principal's ability, or inability, to use the permission for
      the resource. The order of the role bindings matches the role binding
      order in the policy. If the sender of the request does not have access
      to the policy, this field is omitted.
    fullResourceName: The full resource name that identifies the resource. For
      example, `//compute.googleapis.com/projects/my-project/zones/us-
      central1-a/instances/my-instance`. If the sender of the request does not
      have access to the policy, this field is omitted. For examples of full
      resource names for Google Cloud services, see
      https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
    policy: The IAM allow policy attached to the resource. If the sender of
      the request does not have access to the policy, this field is empty.
    relevance: The relevance of this policy to the overall access state in the
      TroubleshootIamPolicyResponse. If the sender of the request does not
      have access to the policy, this field is omitted.
  """

  class AllowAccessStateValueValuesEnum(_messages.Enum):
    r"""Required. Indicates whether _this policy_ provides the specified
    permission to the specified principal for the specified resource. This
    field does _not_ indicate whether the principal actually has the
    permission for the resource. There might be another policy that overrides
    this policy. To determine whether the principal actually has the
    permission, use the `overall_access_state` field in the
    TroubleshootIamPolicyResponse.

    Values:
      ALLOW_ACCESS_STATE_UNSPECIFIED: Not specified.
      ALLOW_ACCESS_STATE_GRANTED: The allow policy gives the principal the
        permission.
      ALLOW_ACCESS_STATE_NOT_GRANTED: The allow policy doesn't give the
        principal the permission.
      ALLOW_ACCESS_STATE_UNKNOWN_CONDITIONAL: The allow policy gives the
        principal the permission if a condition expression evaluate to `true`.
        However, the sender of the request didn't provide enough context for
        Policy Troubleshooter to evaluate the condition expression.
      ALLOW_ACCESS_STATE_UNKNOWN_INFO: The sender of the request doesn't have
        access to all of the allow policies that Policy Troubleshooter needs
        to evaluate the principal's access.
    """
    ALLOW_ACCESS_STATE_UNSPECIFIED = 0
    ALLOW_ACCESS_STATE_GRANTED = 1
    ALLOW_ACCESS_STATE_NOT_GRANTED = 2
    ALLOW_ACCESS_STATE_UNKNOWN_CONDITIONAL = 3
    ALLOW_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this policy to the overall access state in the
    TroubleshootIamPolicyResponse. If the sender of the request does not have
    access to the policy, this field is omitted.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  allowAccessState = _messages.EnumField('AllowAccessStateValueValuesEnum', 1)
  bindingExplanations = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaAllowBindingExplanation', 2, repeated=True)
  fullResourceName = _messages.StringField(3)
  policy = _messages.MessageField('GoogleIamV1Policy', 4)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 5)


class GoogleCloudPolicytroubleshooterIamV3alphaExplainedDenyPolicy(_messages.Message):
  r"""Details about how a specific IAM deny policy Policy contributed to the
  access check.

  Enums:
    DenyAccessStateValueValuesEnum: Required. Indicates whether _this policy_
      denies the specified permission to the specified principal for the
      specified resource. This field does _not_ indicate whether the principal
      actually has the permission for the resource. There might be another
      policy that overrides this policy. To determine whether the principal
      actually has the permission, use the `overall_access_state` field in the
      TroubleshootIamPolicyResponse.
    RelevanceValueValuesEnum: The relevance of this policy to the overall
      access state in the TroubleshootIamPolicyResponse. If the sender of the
      request does not have access to the policy, this field is omitted.

  Fields:
    denyAccessState: Required. Indicates whether _this policy_ denies the
      specified permission to the specified principal for the specified
      resource. This field does _not_ indicate whether the principal actually
      has the permission for the resource. There might be another policy that
      overrides this policy. To determine whether the principal actually has
      the permission, use the `overall_access_state` field in the
      TroubleshootIamPolicyResponse.
    policy: The IAM deny policy attached to the resource. If the sender of the
      request does not have access to the policy, this field is omitted.
    relevance: The relevance of this policy to the overall access state in the
      TroubleshootIamPolicyResponse. If the sender of the request does not
      have access to the policy, this field is omitted.
    ruleExplanations: Details about how each rule in the policy affects the
      principal's inability to use the permission for the resource. The order
      of the deny rule matches the order of the rules in the deny policy. If
      the sender of the request does not have access to the policy, this field
      is omitted.
  """

  class DenyAccessStateValueValuesEnum(_messages.Enum):
    r"""Required. Indicates whether _this policy_ denies the specified
    permission to the specified principal for the specified resource. This
    field does _not_ indicate whether the principal actually has the
    permission for the resource. There might be another policy that overrides
    this policy. To determine whether the principal actually has the
    permission, use the `overall_access_state` field in the
    TroubleshootIamPolicyResponse.

    Values:
      DENY_ACCESS_STATE_UNSPECIFIED: Not specified.
      DENY_ACCESS_STATE_DENIED: The deny policy denies the principal the
        permission.
      DENY_ACCESS_STATE_NOT_DENIED: The deny policy doesn't deny the principal
        the permission.
      DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL: The deny policy denies the
        principal the permission if a condition expression evaluates to
        `true`. However, the sender of the request didn't provide enough
        context for Policy Troubleshooter to evaluate the condition
        expression.
      DENY_ACCESS_STATE_UNKNOWN_INFO: The sender of the request does not have
        access to all of the deny policies that Policy Troubleshooter needs to
        evaluate the principal's access.
    """
    DENY_ACCESS_STATE_UNSPECIFIED = 0
    DENY_ACCESS_STATE_DENIED = 1
    DENY_ACCESS_STATE_NOT_DENIED = 2
    DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL = 3
    DENY_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this policy to the overall access state in the
    TroubleshootIamPolicyResponse. If the sender of the request does not have
    access to the policy, this field is omitted.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  denyAccessState = _messages.EnumField('DenyAccessStateValueValuesEnum', 1)
  policy = _messages.MessageField('GoogleIamV2Policy', 2)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 3)
  ruleExplanations = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaDenyRuleExplanation', 4, repeated=True)


class GoogleCloudPolicytroubleshooterIamV3alphaExplainedDenyResource(_messages.Message):
  r"""Details about how a specific resource contributed to the deny policy
  evaluation.

  Enums:
    DenyAccessStateValueValuesEnum: Required. Indicates whether any policies
      attached to _this resource_ deny the specific permission to the
      specified principal for the specified resource. This field does _not_
      indicate whether the principal actually has the permission for the
      resource. There might be another policy that overrides this policy. To
      determine whether the principal actually has the permission, use the
      `overall_access_state` field in the TroubleshootIamPolicyResponse.
    RelevanceValueValuesEnum: The relevance of this policy to the overall
      access state in the TroubleshootIamPolicyResponse. If the sender of the
      request does not have access to the policy, this field is omitted.

  Fields:
    denyAccessState: Required. Indicates whether any policies attached to
      _this resource_ deny the specific permission to the specified principal
      for the specified resource. This field does _not_ indicate whether the
      principal actually has the permission for the resource. There might be
      another policy that overrides this policy. To determine whether the
      principal actually has the permission, use the `overall_access_state`
      field in the TroubleshootIamPolicyResponse.
    explainedPolicies: List of IAM deny policies that were evaluated to check
      the principal's denied permissions, with annotations to indicate how
      each policy contributed to the final result.
    fullResourceName: The full resource name that identifies the resource. For
      example, `//compute.googleapis.com/projects/my-project/zones/us-
      central1-a/instances/my-instance`. If the sender of the request does not
      have access to the policy, this field is omitted. For examples of full
      resource names for Google Cloud services, see
      https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
    relevance: The relevance of this policy to the overall access state in the
      TroubleshootIamPolicyResponse. If the sender of the request does not
      have access to the policy, this field is omitted.
  """

  class DenyAccessStateValueValuesEnum(_messages.Enum):
    r"""Required. Indicates whether any policies attached to _this resource_
    deny the specific permission to the specified principal for the specified
    resource. This field does _not_ indicate whether the principal actually
    has the permission for the resource. There might be another policy that
    overrides this policy. To determine whether the principal actually has the
    permission, use the `overall_access_state` field in the
    TroubleshootIamPolicyResponse.

    Values:
      DENY_ACCESS_STATE_UNSPECIFIED: Not specified.
      DENY_ACCESS_STATE_DENIED: The deny policy denies the principal the
        permission.
      DENY_ACCESS_STATE_NOT_DENIED: The deny policy doesn't deny the principal
        the permission.
      DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL: The deny policy denies the
        principal the permission if a condition expression evaluates to
        `true`. However, the sender of the request didn't provide enough
        context for Policy Troubleshooter to evaluate the condition
        expression.
      DENY_ACCESS_STATE_UNKNOWN_INFO: The sender of the request does not have
        access to all of the deny policies that Policy Troubleshooter needs to
        evaluate the principal's access.
    """
    DENY_ACCESS_STATE_UNSPECIFIED = 0
    DENY_ACCESS_STATE_DENIED = 1
    DENY_ACCESS_STATE_NOT_DENIED = 2
    DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL = 3
    DENY_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this policy to the overall access state in the
    TroubleshootIamPolicyResponse. If the sender of the request does not have
    access to the policy, this field is omitted.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  denyAccessState = _messages.EnumField('DenyAccessStateValueValuesEnum', 1)
  explainedPolicies = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaExplainedDenyPolicy', 2, repeated=True)
  fullResourceName = _messages.StringField(3)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 4)


class GoogleCloudPolicytroubleshooterIamV3alphaExplainedPABBindingAndPolicy(_messages.Message):
  r"""Details about how a principal access boundary binding and policy
  contributes to the principal access boundary explanation, with annotations
  to indicate how the binding and policy contribute to the overall access
  state.

  Enums:
    BindingAndPolicyAccessStateValueValuesEnum: Output only. Indicates whether
      the principal is allowed to access the specified resource based on
      evaluating the binding and policy.
    RelevanceValueValuesEnum: The relevance of this principal access boundary
      binding and policy to the overall access state.

  Fields:
    bindingAndPolicyAccessState: Output only. Indicates whether the principal
      is allowed to access the specified resource based on evaluating the
      binding and policy.
    explainedPolicy: Optional. Details about how this policy contributes to
      the principal access boundary explanation, with annotations to indicate
      how the policy contributes to the overall access state. If the caller
      doesn't have permission to view the policy in the binding, this field is
      omitted.
    explainedPolicyBinding: Details about how this binding contributes to the
      principal access boundary explanation, with annotations to indicate how
      the binding contributes to the overall access state.
    relevance: The relevance of this principal access boundary binding and
      policy to the overall access state.
  """

  class BindingAndPolicyAccessStateValueValuesEnum(_messages.Enum):
    r"""Output only. Indicates whether the principal is allowed to access the
    specified resource based on evaluating the binding and policy.

    Values:
      PAB_ACCESS_STATE_UNSPECIFIED: Not specified.
      PAB_ACCESS_STATE_ALLOWED: The PAB component allows the principal's
        access to the specified resource.
      PAB_ACCESS_STATE_NOT_ALLOWED: The PAB component doesn't allow the
        principal's access to the specified resource.
      PAB_ACCESS_STATE_NOT_ENFORCED: The PAB component is not enforced on the
        principal, or the specified resource. This state refers to the
        following scenarios: - IAM doesn't enforce the specified permission at
        the PAB policy's [enforcement
        version](https://cloud.google.com/iam/help/pab/enforcement-versions),
        so the PAB policy can't block access. - The binding doesn't apply to
        the principal, so the policy is not enforced. - The PAB policy doesn't
        have any rules
      PAB_ACCESS_STATE_UNKNOWN_INFO: The sender of the request does not have
        access to the PAB component, or the relevant data to explain the PAB
        component.
    """
    PAB_ACCESS_STATE_UNSPECIFIED = 0
    PAB_ACCESS_STATE_ALLOWED = 1
    PAB_ACCESS_STATE_NOT_ALLOWED = 2
    PAB_ACCESS_STATE_NOT_ENFORCED = 3
    PAB_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this principal access boundary binding and policy to
    the overall access state.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  bindingAndPolicyAccessState = _messages.EnumField('BindingAndPolicyAccessStateValueValuesEnum', 1)
  explainedPolicy = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaExplainedPABPolicy', 2)
  explainedPolicyBinding = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaExplainedPolicyBinding', 3)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 4)


class GoogleCloudPolicytroubleshooterIamV3alphaExplainedPABPolicy(_messages.Message):
  r"""Details about how a principal access boundary policy contributes to the
  explanation, with annotations to indicate how the policy contributes to the
  overall access state.

  Enums:
    PolicyAccessStateValueValuesEnum: Output only. Indicates whether the
      policy allows access to the specified resource.
    RelevanceValueValuesEnum: The relevance of this policy to the overall
      access state.

  Fields:
    explainedRules: List of principal access boundary rules that were
      explained to check the principal's access to specified resource, with
      annotations to indicate how each rule contributes to the overall access
      state.
    policy: The policy that is explained.
    policyAccessState: Output only. Indicates whether the policy allows access
      to the specified resource.
    policyVersion: Output only. Explanation of the principal access boundary
      policy's version.
    relevance: The relevance of this policy to the overall access state.
  """

  class PolicyAccessStateValueValuesEnum(_messages.Enum):
    r"""Output only. Indicates whether the policy allows access to the
    specified resource.

    Values:
      PAB_ACCESS_STATE_UNSPECIFIED: Not specified.
      PAB_ACCESS_STATE_ALLOWED: The PAB component allows the principal's
        access to the specified resource.
      PAB_ACCESS_STATE_NOT_ALLOWED: The PAB component doesn't allow the
        principal's access to the specified resource.
      PAB_ACCESS_STATE_NOT_ENFORCED: The PAB component is not enforced on the
        principal, or the specified resource. This state refers to the
        following scenarios: - IAM doesn't enforce the specified permission at
        the PAB policy's [enforcement
        version](https://cloud.google.com/iam/help/pab/enforcement-versions),
        so the PAB policy can't block access. - The binding doesn't apply to
        the principal, so the policy is not enforced. - The PAB policy doesn't
        have any rules
      PAB_ACCESS_STATE_UNKNOWN_INFO: The sender of the request does not have
        access to the PAB component, or the relevant data to explain the PAB
        component.
    """
    PAB_ACCESS_STATE_UNSPECIFIED = 0
    PAB_ACCESS_STATE_ALLOWED = 1
    PAB_ACCESS_STATE_NOT_ALLOWED = 2
    PAB_ACCESS_STATE_NOT_ENFORCED = 3
    PAB_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this policy to the overall access state.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  explainedRules = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaExplainedPABRule', 1, repeated=True)
  policy = _messages.MessageField('GoogleIamV3PrincipalAccessBoundaryPolicy', 2)
  policyAccessState = _messages.EnumField('PolicyAccessStateValueValuesEnum', 3)
  policyVersion = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaExplainedPABPolicyVersion', 4)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 5)


class GoogleCloudPolicytroubleshooterIamV3alphaExplainedPABPolicyVersion(_messages.Message):
  r"""Details about how a principal access boundary policy's version
  contributes to the policy's enforcement state.

  Enums:
    EnforcementStateValueValuesEnum: Output only. Indicates whether the policy
      is enforced based on its version.

  Fields:
    enforcementState: Output only. Indicates whether the policy is enforced
      based on its version.
    version: Output only. The actual version of the policy. - If the policy
      uses static version, this field is the chosen static version. - If the
      policy uses dynamic version, this field is the effective latest version.
  """

  class EnforcementStateValueValuesEnum(_messages.Enum):
    r"""Output only. Indicates whether the policy is enforced based on its
    version.

    Values:
      PAB_POLICY_ENFORCEMENT_STATE_UNSPECIFIED: An error occurred when
        checking whether a principal access boundary policy is enforced based
        on its version.
      PAB_POLICY_ENFORCEMENT_STATE_ENFORCED: The principal access boundary
        policy is enforced based on its version.
      PAB_POLICY_ENFORCEMENT_STATE_NOT_ENFORCED: The principal access boundary
        policy is not enforced based on its version.
    """
    PAB_POLICY_ENFORCEMENT_STATE_UNSPECIFIED = 0
    PAB_POLICY_ENFORCEMENT_STATE_ENFORCED = 1
    PAB_POLICY_ENFORCEMENT_STATE_NOT_ENFORCED = 2

  enforcementState = _messages.EnumField('EnforcementStateValueValuesEnum', 1)
  version = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudPolicytroubleshooterIamV3alphaExplainedPABRule(_messages.Message):
  r"""Details about how a principal access boundary rule contributes to the
  explanation, with annotations to indicate how the rule contributes to the
  overall access state.

  Enums:
    CombinedResourceInclusionStateValueValuesEnum: Output only. Indicates
      whether any resource of the rule is the specified resource or includes
      the specified resource.
    EffectValueValuesEnum: Required. The effect of the rule which describes
      the access relationship.
    RelevanceValueValuesEnum: The relevance of this rule to the overall access
      state.
    RuleAccessStateValueValuesEnum: Output only. Indicates whether the rule
      allows access to the specified resource.

  Fields:
    combinedResourceInclusionState: Output only. Indicates whether any
      resource of the rule is the specified resource or includes the specified
      resource.
    effect: Required. The effect of the rule which describes the access
      relationship.
    explainedResources: List of resources that were explained to check the
      principal's access to specified resource, with annotations to indicate
      how each resource contributes to the overall access state.
    relevance: The relevance of this rule to the overall access state.
    ruleAccessState: Output only. Indicates whether the rule allows access to
      the specified resource.
  """

  class CombinedResourceInclusionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Indicates whether any resource of the rule is the
    specified resource or includes the specified resource.

    Values:
      RESOURCE_INCLUSION_STATE_UNSPECIFIED: An error occurred when checking
        whether the resource includes the specified resource.
      RESOURCE_INCLUSION_STATE_INCLUDED: The resource includes the specified
        resource.
      RESOURCE_INCLUSION_STATE_NOT_INCLUDED: The resource doesn't include the
        specified resource.
      RESOURCE_INCLUSION_STATE_UNKNOWN_INFO: The sender of the request does
        not have access to the relevant data to check whether the resource
        includes the specified resource.
      RESOURCE_INCLUSION_STATE_UNKNOWN_UNSUPPORTED: The resource is of an
        unsupported type, such as non-CRM resources.
    """
    RESOURCE_INCLUSION_STATE_UNSPECIFIED = 0
    RESOURCE_INCLUSION_STATE_INCLUDED = 1
    RESOURCE_INCLUSION_STATE_NOT_INCLUDED = 2
    RESOURCE_INCLUSION_STATE_UNKNOWN_INFO = 3
    RESOURCE_INCLUSION_STATE_UNKNOWN_UNSUPPORTED = 4

  class EffectValueValuesEnum(_messages.Enum):
    r"""Required. The effect of the rule which describes the access
    relationship.

    Values:
      EFFECT_UNSPECIFIED: Effect unspecified.
      ALLOW: Allows access to the resources in this rule.
    """
    EFFECT_UNSPECIFIED = 0
    ALLOW = 1

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this rule to the overall access state.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  class RuleAccessStateValueValuesEnum(_messages.Enum):
    r"""Output only. Indicates whether the rule allows access to the specified
    resource.

    Values:
      PAB_ACCESS_STATE_UNSPECIFIED: Not specified.
      PAB_ACCESS_STATE_ALLOWED: The PAB component allows the principal's
        access to the specified resource.
      PAB_ACCESS_STATE_NOT_ALLOWED: The PAB component doesn't allow the
        principal's access to the specified resource.
      PAB_ACCESS_STATE_NOT_ENFORCED: The PAB component is not enforced on the
        principal, or the specified resource. This state refers to the
        following scenarios: - IAM doesn't enforce the specified permission at
        the PAB policy's [enforcement
        version](https://cloud.google.com/iam/help/pab/enforcement-versions),
        so the PAB policy can't block access. - The binding doesn't apply to
        the principal, so the policy is not enforced. - The PAB policy doesn't
        have any rules
      PAB_ACCESS_STATE_UNKNOWN_INFO: The sender of the request does not have
        access to the PAB component, or the relevant data to explain the PAB
        component.
    """
    PAB_ACCESS_STATE_UNSPECIFIED = 0
    PAB_ACCESS_STATE_ALLOWED = 1
    PAB_ACCESS_STATE_NOT_ALLOWED = 2
    PAB_ACCESS_STATE_NOT_ENFORCED = 3
    PAB_ACCESS_STATE_UNKNOWN_INFO = 4

  combinedResourceInclusionState = _messages.EnumField('CombinedResourceInclusionStateValueValuesEnum', 1)
  effect = _messages.EnumField('EffectValueValuesEnum', 2)
  explainedResources = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaExplainedPABRuleExplainedResource', 3, repeated=True)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 4)
  ruleAccessState = _messages.EnumField('RuleAccessStateValueValuesEnum', 5)


class GoogleCloudPolicytroubleshooterIamV3alphaExplainedPABRuleExplainedResource(_messages.Message):
  r"""Details about how a resource contributes to the explanation, with
  annotations to indicate how the resource contributes to the overall access
  state.

  Enums:
    RelevanceValueValuesEnum: The relevance of this resource to the overall
      access state.
    ResourceInclusionStateValueValuesEnum: Output only. Indicates whether the
      resource is the specified resource or includes the specified resource.

  Fields:
    relevance: The relevance of this resource to the overall access state.
    resource: The [full resource name](https://cloud.google.com/iam/docs/full-
      resource-names) that identifies the resource that is explained. This can
      only be a project, a folder, or an organization which is what a PAB rule
      accepts.
    resourceInclusionState: Output only. Indicates whether the resource is the
      specified resource or includes the specified resource.
  """

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this resource to the overall access state.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  class ResourceInclusionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Indicates whether the resource is the specified resource
    or includes the specified resource.

    Values:
      RESOURCE_INCLUSION_STATE_UNSPECIFIED: An error occurred when checking
        whether the resource includes the specified resource.
      RESOURCE_INCLUSION_STATE_INCLUDED: The resource includes the specified
        resource.
      RESOURCE_INCLUSION_STATE_NOT_INCLUDED: The resource doesn't include the
        specified resource.
      RESOURCE_INCLUSION_STATE_UNKNOWN_INFO: The sender of the request does
        not have access to the relevant data to check whether the resource
        includes the specified resource.
      RESOURCE_INCLUSION_STATE_UNKNOWN_UNSUPPORTED: The resource is of an
        unsupported type, such as non-CRM resources.
    """
    RESOURCE_INCLUSION_STATE_UNSPECIFIED = 0
    RESOURCE_INCLUSION_STATE_INCLUDED = 1
    RESOURCE_INCLUSION_STATE_NOT_INCLUDED = 2
    RESOURCE_INCLUSION_STATE_UNKNOWN_INFO = 3
    RESOURCE_INCLUSION_STATE_UNKNOWN_UNSUPPORTED = 4

  relevance = _messages.EnumField('RelevanceValueValuesEnum', 1)
  resource = _messages.StringField(2)
  resourceInclusionState = _messages.EnumField('ResourceInclusionStateValueValuesEnum', 3)


class GoogleCloudPolicytroubleshooterIamV3alphaExplainedPolicyBinding(_messages.Message):
  r"""Details about how a policy binding contributes to the policy
  explanation, with annotations to indicate how the policy binding contributes
  to the overall access state.

  Enums:
    PolicyBindingStateValueValuesEnum: Output only. Indicates whether the
      policy binding takes effect.
    RelevanceValueValuesEnum: The relevance of this policy binding to the
      overall access state.

  Fields:
    conditionExplanation: Optional. Explanation of the condition in the policy
      binding. If the policy binding doesn't have a condition, this field is
      omitted.
    policyBinding: The policy binding that is explained.
    policyBindingState: Output only. Indicates whether the policy binding
      takes effect.
    relevance: The relevance of this policy binding to the overall access
      state.
  """

  class PolicyBindingStateValueValuesEnum(_messages.Enum):
    r"""Output only. Indicates whether the policy binding takes effect.

    Values:
      POLICY_BINDING_STATE_UNSPECIFIED: An error occurred when checking
        whether the policy binding is enforced.
      POLICY_BINDING_STATE_ENFORCED: The policy binding is enforced.
      POLICY_BINDING_STATE_NOT_ENFORCED: The policy binding is not enforced.
    """
    POLICY_BINDING_STATE_UNSPECIFIED = 0
    POLICY_BINDING_STATE_ENFORCED = 1
    POLICY_BINDING_STATE_NOT_ENFORCED = 2

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this policy binding to the overall access state.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  conditionExplanation = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaConditionExplanation', 1)
  policyBinding = _messages.MessageField('GoogleIamV3PolicyBinding', 2)
  policyBindingState = _messages.EnumField('PolicyBindingStateValueValuesEnum', 3)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 4)


class GoogleCloudPolicytroubleshooterIamV3alphaPABPolicyExplanation(_messages.Message):
  r"""Details about how the relevant principal access boundary policies affect
  the overall access state.

  Enums:
    PrincipalAccessBoundaryAccessStateValueValuesEnum: Output only. Indicates
      whether the principal is allowed to access specified resource, based on
      evaluating all applicable principal access boundary bindings and
      policies.
    RelevanceValueValuesEnum: The relevance of the principal access boundary
      access state to the overall access state.

  Fields:
    explainedBindingsAndPolicies: List of principal access boundary policies
      and bindings that are applicable to the principal's access state, with
      annotations to indicate how each binding and policy contributes to the
      overall access state.
    principalAccessBoundaryAccessState: Output only. Indicates whether the
      principal is allowed to access specified resource, based on evaluating
      all applicable principal access boundary bindings and policies.
    relevance: The relevance of the principal access boundary access state to
      the overall access state.
  """

  class PrincipalAccessBoundaryAccessStateValueValuesEnum(_messages.Enum):
    r"""Output only. Indicates whether the principal is allowed to access
    specified resource, based on evaluating all applicable principal access
    boundary bindings and policies.

    Values:
      PAB_ACCESS_STATE_UNSPECIFIED: Not specified.
      PAB_ACCESS_STATE_ALLOWED: The PAB component allows the principal's
        access to the specified resource.
      PAB_ACCESS_STATE_NOT_ALLOWED: The PAB component doesn't allow the
        principal's access to the specified resource.
      PAB_ACCESS_STATE_NOT_ENFORCED: The PAB component is not enforced on the
        principal, or the specified resource. This state refers to the
        following scenarios: - IAM doesn't enforce the specified permission at
        the PAB policy's [enforcement
        version](https://cloud.google.com/iam/help/pab/enforcement-versions),
        so the PAB policy can't block access. - The binding doesn't apply to
        the principal, so the policy is not enforced. - The PAB policy doesn't
        have any rules
      PAB_ACCESS_STATE_UNKNOWN_INFO: The sender of the request does not have
        access to the PAB component, or the relevant data to explain the PAB
        component.
    """
    PAB_ACCESS_STATE_UNSPECIFIED = 0
    PAB_ACCESS_STATE_ALLOWED = 1
    PAB_ACCESS_STATE_NOT_ALLOWED = 2
    PAB_ACCESS_STATE_NOT_ENFORCED = 3
    PAB_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the principal access boundary access state to the
    overall access state.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  explainedBindingsAndPolicies = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaExplainedPABBindingAndPolicy', 1, repeated=True)
  principalAccessBoundaryAccessState = _messages.EnumField('PrincipalAccessBoundaryAccessStateValueValuesEnum', 2)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 3)


class GoogleCloudPolicytroubleshooterIamV3alphaTroubleshootIamPolicyRequest(_messages.Message):
  r"""Request for TroubleshootIamPolicy.

  Fields:
    accessTuple: The information to use for checking whether a principal has a
      permission for a resource.
  """

  accessTuple = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaAccessTuple', 1)


class GoogleCloudPolicytroubleshooterIamV3alphaTroubleshootIamPolicyResponse(_messages.Message):
  r"""Response for TroubleshootIamPolicy.

  Enums:
    OverallAccessStateValueValuesEnum: Indicates whether the principal has the
      specified permission for the specified resource, based on evaluating all
      types of the applicable IAM policies.

  Fields:
    accessTuple: The access tuple from the request, including any provided
      context used to evaluate the condition.
    allowPolicyExplanation: An explanation of how the applicable IAM allow
      policies affect the final access state.
    denyPolicyExplanation: An explanation of how the applicable IAM deny
      policies affect the final access state.
    overallAccessState: Indicates whether the principal has the specified
      permission for the specified resource, based on evaluating all types of
      the applicable IAM policies.
    pabPolicyExplanation: An explanation of how the applicable principal
      access boundary policies affect the final access state.
  """

  class OverallAccessStateValueValuesEnum(_messages.Enum):
    r"""Indicates whether the principal has the specified permission for the
    specified resource, based on evaluating all types of the applicable IAM
    policies.

    Values:
      OVERALL_ACCESS_STATE_UNSPECIFIED: Not specified.
      CAN_ACCESS: The principal has the permission.
      CANNOT_ACCESS: The principal doesn't have the permission.
      UNKNOWN_INFO: The principal might have the permission, but the sender
        can't access all of the information needed to fully evaluate the
        principal's access.
      UNKNOWN_CONDITIONAL: The principal might have the permission, but Policy
        Troubleshooter can't fully evaluate the principal's access because the
        sender didn't provide the required context to evaluate the condition.
    """
    OVERALL_ACCESS_STATE_UNSPECIFIED = 0
    CAN_ACCESS = 1
    CANNOT_ACCESS = 2
    UNKNOWN_INFO = 3
    UNKNOWN_CONDITIONAL = 4

  accessTuple = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaAccessTuple', 1)
  allowPolicyExplanation = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaAllowPolicyExplanation', 2)
  denyPolicyExplanation = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaDenyPolicyExplanation', 3)
  overallAccessState = _messages.EnumField('OverallAccessStateValueValuesEnum', 4)
  pabPolicyExplanation = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3alphaPABPolicyExplanation', 5)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaAccessPolicyExplanation(_messages.Message):
  r"""Explanation of an access policy NextTAG: 6

  Enums:
    DeviceEnforcementStateValueValuesEnum: The device enforcement state of the
      access policy. It indicates whether the device is used for access level
      evaluation.

  Fields:
    accessLevelDetailedExplanations: Detailed explanations of access levels
      from the Access Level Troubleshooter Frontend Service
    accessPolicy: The full resource name of an access policy Format:
      `accessPolicies/{access_policy}`
    deviceEnforcementState: The device enforcement state of the access policy.
      It indicates whether the device is used for access level evaluation.
    servicePerimeterExplanations: The explanations for the service perimeters
      in order
    servicePerimeters: The service perimeter definitions
  """

  class DeviceEnforcementStateValueValuesEnum(_messages.Enum):
    r"""The device enforcement state of the access policy. It indicates
    whether the device is used for access level evaluation.

    Values:
      CONTEXT_ENFORCEMENT_STATE_UNSPECIFIED: Not used
      NOT_ENFORCED: Current context is not used for access level evaluation.
      ENFORCED: Current context is used for access level evaluation. Device is
        valid and not stale.
    """
    CONTEXT_ENFORCEMENT_STATE_UNSPECIFIED = 0
    NOT_ENFORCED = 1
    ENFORCED = 2

  accessLevelDetailedExplanations = _messages.MessageField('IdentityCaaIntelFrontendAccessLevelExplanation', 1, repeated=True)
  accessPolicy = _messages.StringField(2)
  deviceEnforcementState = _messages.EnumField('DeviceEnforcementStateValueValuesEnum', 3)
  servicePerimeterExplanations = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaServicePerimeterExplanation', 4, repeated=True)
  servicePerimeters = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeter', 5, repeated=True)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaDeviceContext(_messages.Message):
  r"""Detailed device context. NextTAG: 8

  Enums:
    EncryptionStateValueValuesEnum: Output only. Status of disk encryption on
      device.
    OsTypeValueValuesEnum: The Operating System type of the device.

  Fields:
    encryptionState: Output only. Status of disk encryption on device.
    isAdminApprovedDevice: Whether the device has been approved by the domain
      admin.
    isCorpOwnedDevice: Whether the device is company (corp) owned.
    isScreenlockEnabled: If the device has enabled screen lock.
    osType: The Operating System type of the device.
    osVersion: Ex: "3.0"
    verifiedChromeOs: Whether the request comes from a device with a verified
      Chrome OS.
  """

  class EncryptionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Status of disk encryption on device.

    Values:
      DEVICE_ENCRYPTION_STATE_UNSPECIFIED: The encryption state of the device
        is not specified or not known.
      UNSUPPORTED: The device does not support encryption.
      UNENCRYPTED: The device supports encryption, but is currently
        unencrypted.
      ENCRYPTED: The device is encrypted.
    """
    DEVICE_ENCRYPTION_STATE_UNSPECIFIED = 0
    UNSUPPORTED = 1
    UNENCRYPTED = 2
    ENCRYPTED = 3

  class OsTypeValueValuesEnum(_messages.Enum):
    r"""The Operating System type of the device.

    Values:
      OS_TYPE_UNSPECIFIED: The operating system of the device is not specified
        or not known.
      DESKTOP_MAC: A desktop Mac operating system.
      DESKTOP_WINDOWS: A desktop Windows operating system.
      DESKTOP_LINUX: A desktop Linux operating system.
      ANDROID: An Android operating system.
      IOS: An iOS operating system.
      DESKTOP_CHROME_OS: A desktop ChromeOS operating system.
    """
    OS_TYPE_UNSPECIFIED = 0
    DESKTOP_MAC = 1
    DESKTOP_WINDOWS = 2
    DESKTOP_LINUX = 3
    ANDROID = 4
    IOS = 5
    DESKTOP_CHROME_OS = 6

  encryptionState = _messages.EnumField('EncryptionStateValueValuesEnum', 1)
  isAdminApprovedDevice = _messages.BooleanField(2)
  isCorpOwnedDevice = _messages.BooleanField(3)
  isScreenlockEnabled = _messages.BooleanField(4)
  osType = _messages.EnumField('OsTypeValueValuesEnum', 5)
  osVersion = _messages.StringField(6)
  verifiedChromeOs = _messages.BooleanField(7)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaDeviceInfo(_messages.Message):
  r"""Detailed Device Information. NextTAG: 5

  Enums:
    DeviceStateValueValuesEnum: The device condition.

  Fields:
    deviceContext: Device details.
    deviceId: DDS Device id. It is visible publicly in MDM
    deviceLastSyncTime: The last sync time of the device
    deviceState: The device condition.
  """

  class DeviceStateValueValuesEnum(_messages.Enum):
    r"""The device condition.

    Values:
      DEVICE_STATE_UNSPECIFIED: Reserved
      INFO_NOT_AUTHORIZED: Client doesn't have permission to fetch device
        details
      DEVICE_NOT_FOUND: Unable to find the device.
      NORMAL: Device state is valid
      STALE: Device expires
      INTERNAL_ERROR: Internal error to be used for cases where a specific
        device state doesn't exist
    """
    DEVICE_STATE_UNSPECIFIED = 0
    INFO_NOT_AUTHORIZED = 1
    DEVICE_NOT_FOUND = 2
    NORMAL = 3
    STALE = 4
    INTERNAL_ERROR = 5

  deviceContext = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaDeviceContext', 1)
  deviceId = _messages.StringField(2)
  deviceLastSyncTime = _messages.StringField(3)
  deviceState = _messages.EnumField('DeviceStateValueValuesEnum', 4)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaEgressPoliciesExplanation(_messages.Message):
  r"""Explanation of a pair of source and target resources evaluated against
  all egress policies. NextTAG: 5

  Enums:
    EgressPolicyEvalStatesValueListEntryValuesEnum:

  Fields:
    egressPolicyEvalStates: Evaluation states for the pair of source and
      target resources evaluated against all the egress policies in the
      service perimeter
    egressPolicyExplanations: Explanation details about how the pair of source
      and target resources are evaluated against all the egress policies in
      the service perimeter
    sourceResource: The source resource that egress_from policies are
      evaluated against
    targetResource: The target resource that egress_to policies are evaluated
      against
  """

  class EgressPolicyEvalStatesValueListEntryValuesEnum(_messages.Enum):
    r"""EgressPolicyEvalStatesValueListEntryValuesEnum enum type.

    Values:
      EGRESS_POLICY_EVAL_STATE_UNSPECIFIED: Not used
      EGRESS_POLICY_EVAL_STATE_IN_SAME_SERVICE_PERIMETER: The resources are in
        the same regular service perimeter
      EGRESS_POLICY_EVAL_STATE_GRANTED_OVER_BRIDGE: The resources are in the
        same bridge service perimeter
      EGRESS_POLICY_EVAL_STATE_GRANTED_BY_POLICY: The request is granted by
        the egress policy
      EGRESS_POLICY_EVAL_STATE_DENIED_BY_POLICY: The request is denied by the
        egress policy
      EGRESS_POLICY_EVAL_STATE_NOT_APPLICABLE: The egress policy is not
        applicable for the request
    """
    EGRESS_POLICY_EVAL_STATE_UNSPECIFIED = 0
    EGRESS_POLICY_EVAL_STATE_IN_SAME_SERVICE_PERIMETER = 1
    EGRESS_POLICY_EVAL_STATE_GRANTED_OVER_BRIDGE = 2
    EGRESS_POLICY_EVAL_STATE_GRANTED_BY_POLICY = 3
    EGRESS_POLICY_EVAL_STATE_DENIED_BY_POLICY = 4
    EGRESS_POLICY_EVAL_STATE_NOT_APPLICABLE = 5

  egressPolicyEvalStates = _messages.EnumField('EgressPolicyEvalStatesValueListEntryValuesEnum', 1, repeated=True)
  egressPolicyExplanations = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaEgressPolicyExplanation', 2, repeated=True)
  sourceResource = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaResource', 3)
  targetResource = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaResource', 4)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaEgressPolicyExplanation(_messages.Message):
  r"""Explanation of an egress policy NextTAG: 8

  Enums:
    ApiOperationEvalStatesValueListEntryValuesEnum:
    EgressPolicyEvalStateValueValuesEnum: The overall evaluation state of the
      egress policy
    ExternalResourceEvalStatesValueListEntryValuesEnum:
    IdentityTypeEvalStateValueValuesEnum: Details of the evaluation state of
      the identity type
    ResourceEvalStatesValueListEntryValuesEnum:

  Fields:
    apiOperationEvalStates: Details of the evaluation states of api operations
    egressPolicyEvalState: The overall evaluation state of the egress policy
    externalResourceEvalStates: Details of the evaluation states of external
      resources
    identityExplanations: Detailed explanation of the identities.
    identityTypeEvalState: Details of the evaluation state of the identity
      type
    resourceEvalStates: Details of the evaluation states of resources
  """

  class ApiOperationEvalStatesValueListEntryValuesEnum(_messages.Enum):
    r"""ApiOperationEvalStatesValueListEntryValuesEnum enum type.

    Values:
      API_OPERATION_EVAL_STATE_UNSPECIFIED: Not used
      API_OPERATION_EVAL_STATE_MATCH: The request matches the api operation
      API_OPERATION_EVAL_STATE_NOT_MATCH: The request doesn't match the api
        operation
    """
    API_OPERATION_EVAL_STATE_UNSPECIFIED = 0
    API_OPERATION_EVAL_STATE_MATCH = 1
    API_OPERATION_EVAL_STATE_NOT_MATCH = 2

  class EgressPolicyEvalStateValueValuesEnum(_messages.Enum):
    r"""The overall evaluation state of the egress policy

    Values:
      EGRESS_POLICY_EVAL_STATE_UNSPECIFIED: Not used
      EGRESS_POLICY_EVAL_STATE_IN_SAME_SERVICE_PERIMETER: The resources are in
        the same regular service perimeter
      EGRESS_POLICY_EVAL_STATE_GRANTED_OVER_BRIDGE: The resources are in the
        same bridge service perimeter
      EGRESS_POLICY_EVAL_STATE_GRANTED_BY_POLICY: The request is granted by
        the egress policy
      EGRESS_POLICY_EVAL_STATE_DENIED_BY_POLICY: The request is denied by the
        egress policy
      EGRESS_POLICY_EVAL_STATE_NOT_APPLICABLE: The egress policy is not
        applicable for the request
    """
    EGRESS_POLICY_EVAL_STATE_UNSPECIFIED = 0
    EGRESS_POLICY_EVAL_STATE_IN_SAME_SERVICE_PERIMETER = 1
    EGRESS_POLICY_EVAL_STATE_GRANTED_OVER_BRIDGE = 2
    EGRESS_POLICY_EVAL_STATE_GRANTED_BY_POLICY = 3
    EGRESS_POLICY_EVAL_STATE_DENIED_BY_POLICY = 4
    EGRESS_POLICY_EVAL_STATE_NOT_APPLICABLE = 5

  class ExternalResourceEvalStatesValueListEntryValuesEnum(_messages.Enum):
    r"""ExternalResourceEvalStatesValueListEntryValuesEnum enum type.

    Values:
      RESOURCE_EVAL_STATE_UNSPECIFIED: Not used
      RESOURCE_EVAL_STATE_MATCH: The request matches the resource
      RESOURCE_EVAL_STATE_NOT_MATCH: The request doesn't match the resource
    """
    RESOURCE_EVAL_STATE_UNSPECIFIED = 0
    RESOURCE_EVAL_STATE_MATCH = 1
    RESOURCE_EVAL_STATE_NOT_MATCH = 2

  class IdentityTypeEvalStateValueValuesEnum(_messages.Enum):
    r"""Details of the evaluation state of the identity type

    Values:
      IDENTITY_TYPE_EVAL_STATE_UNSPECIFIED: Not used
      IDENTITY_TYPE_EVAL_STATE_GRANTED: The request type matches the identity
      IDENTITY_TYPE_EVAL_STATE_NOT_GRANTED: The request type doesn't match the
        identity
      IDENTITY_TYPE_EVAL_STATE_NOT_SUPPORTED: The identity type is not
        supported
    """
    IDENTITY_TYPE_EVAL_STATE_UNSPECIFIED = 0
    IDENTITY_TYPE_EVAL_STATE_GRANTED = 1
    IDENTITY_TYPE_EVAL_STATE_NOT_GRANTED = 2
    IDENTITY_TYPE_EVAL_STATE_NOT_SUPPORTED = 3

  class ResourceEvalStatesValueListEntryValuesEnum(_messages.Enum):
    r"""ResourceEvalStatesValueListEntryValuesEnum enum type.

    Values:
      RESOURCE_EVAL_STATE_UNSPECIFIED: Not used
      RESOURCE_EVAL_STATE_MATCH: The request matches the resource
      RESOURCE_EVAL_STATE_NOT_MATCH: The request doesn't match the resource
    """
    RESOURCE_EVAL_STATE_UNSPECIFIED = 0
    RESOURCE_EVAL_STATE_MATCH = 1
    RESOURCE_EVAL_STATE_NOT_MATCH = 2

  apiOperationEvalStates = _messages.EnumField('ApiOperationEvalStatesValueListEntryValuesEnum', 1, repeated=True)
  egressPolicyEvalState = _messages.EnumField('EgressPolicyEvalStateValueValuesEnum', 2)
  externalResourceEvalStates = _messages.EnumField('ExternalResourceEvalStatesValueListEntryValuesEnum', 3, repeated=True)
  identityExplanations = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaIdentityExplanation', 4, repeated=True)
  identityTypeEvalState = _messages.EnumField('IdentityTypeEvalStateValueValuesEnum', 5)
  resourceEvalStates = _messages.EnumField('ResourceEvalStatesValueListEntryValuesEnum', 6, repeated=True)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaIdentityExplanation(_messages.Message):
  r"""Explanation of an identity. NextTAG: 3

  Enums:
    IdentityEvalStateValueValuesEnum: Details about the evaluation state of
      the identity set in policy.

  Fields:
    identityEvalState: Details about the evaluation state of the identity set
      in policy.
  """

  class IdentityEvalStateValueValuesEnum(_messages.Enum):
    r"""Details about the evaluation state of the identity set in policy.

    Values:
      IDENTITY_EVAL_STATE_UNSPECIFIED: Not used
      MATCH: The request matches the identity
      NOT_MATCH: The request doesn't match the identity
      NOT_SUPPORTED: The identity is not supported
      INFO_DENIED: The sender of the request is not allowed to verify the
        identity.
    """
    IDENTITY_EVAL_STATE_UNSPECIFIED = 0
    MATCH = 1
    NOT_MATCH = 2
    NOT_SUPPORTED = 3
    INFO_DENIED = 4

  identityEvalState = _messages.EnumField('IdentityEvalStateValueValuesEnum', 1)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaIngressPoliciesExplanation(_messages.Message):
  r"""Explanation of ingress policies NextTAG: 5

  Enums:
    IngressPolicyEvalStatesValueListEntryValuesEnum:
    TopLevelAccessLevelsEvalStateValueValuesEnum: The overall evaluation state
      of the top level access levels

  Fields:
    ingressPolicyEvalStates: Details about the evaluation state of the ingress
      policy
    ingressPolicyExplanations: Explanations of ingress policies
    targetResource: The target resource to ingress to
    topLevelAccessLevelsEvalState: The overall evaluation state of the top
      level access levels
  """

  class IngressPolicyEvalStatesValueListEntryValuesEnum(_messages.Enum):
    r"""IngressPolicyEvalStatesValueListEntryValuesEnum enum type.

    Values:
      INGRESS_POLICY_EVAL_STATE_UNSPECIFIED: Not used
      INGRESS_POLICY_EVAL_STATE_IN_SAME_SERVICE_PERIMETER: The resources are
        in the same regular service perimeter
      INGRESS_POLICY_EVAL_STATE_GRANTED_OVER_BRIDGE: The resources are in the
        same bridge service perimeter
      INGRESS_POLICY_EVAL_STATE_GRANTED_BY_POLICY: The request is granted by
        the ingress policy
      INGRESS_POLICY_EVAL_STATE_DENIED_BY_POLICY: The request is denied by the
        ingress policy
      INGRESS_POLICY_EVAL_STATE_NOT_APPLICABLE: The ingress policy is not
        applicable for the request
    """
    INGRESS_POLICY_EVAL_STATE_UNSPECIFIED = 0
    INGRESS_POLICY_EVAL_STATE_IN_SAME_SERVICE_PERIMETER = 1
    INGRESS_POLICY_EVAL_STATE_GRANTED_OVER_BRIDGE = 2
    INGRESS_POLICY_EVAL_STATE_GRANTED_BY_POLICY = 3
    INGRESS_POLICY_EVAL_STATE_DENIED_BY_POLICY = 4
    INGRESS_POLICY_EVAL_STATE_NOT_APPLICABLE = 5

  class TopLevelAccessLevelsEvalStateValueValuesEnum(_messages.Enum):
    r"""The overall evaluation state of the top level access levels

    Values:
      TOP_LEVEL_ACCESS_LEVELS_EVAL_STATE_UNSPECIFIED: Not used
      NOT_APPLICABLE: The overall evaluation state of the top level access
        levels is not applicable
      GRANTED: The overall evaluation state of the top level access levels is
        granted
      DENIED: The overall evaluation state of the top level access levels is
        denied
    """
    TOP_LEVEL_ACCESS_LEVELS_EVAL_STATE_UNSPECIFIED = 0
    NOT_APPLICABLE = 1
    GRANTED = 2
    DENIED = 3

  ingressPolicyEvalStates = _messages.EnumField('IngressPolicyEvalStatesValueListEntryValuesEnum', 1, repeated=True)
  ingressPolicyExplanations = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaIngressPolicyExplanation', 2, repeated=True)
  targetResource = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaResource', 3)
  topLevelAccessLevelsEvalState = _messages.EnumField('TopLevelAccessLevelsEvalStateValueValuesEnum', 4)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaIngressPolicyExplanation(_messages.Message):
  r"""Explanation of an ingress policy NextTAG: 8

  Enums:
    ApiOperationEvalStatesValueListEntryValuesEnum:
    IdentityTypeEvalStateValueValuesEnum: Details of the evaluation state of
      the identity type
    IngressPolicyEvalStateValueValuesEnum: The overall evaluation state of the
      ingress policy
    IngressSourceEvalStatesValueListEntryValuesEnum:
    ResourceEvalStatesValueListEntryValuesEnum:

  Fields:
    apiOperationEvalStates: Details of the evaluation states of api operations
    identityExplanations: Detailed explanation of the identities.
    identityTypeEvalState: Details of the evaluation state of the identity
      type
    ingressPolicyEvalState: The overall evaluation state of the ingress policy
    ingressSourceEvalStates: Details of the evaluation states of ingress
      sources
    resourceEvalStates: Details of the evaluation states of resources
  """

  class ApiOperationEvalStatesValueListEntryValuesEnum(_messages.Enum):
    r"""ApiOperationEvalStatesValueListEntryValuesEnum enum type.

    Values:
      API_OPERATION_EVAL_STATE_UNSPECIFIED: Not used
      API_OPERATION_EVAL_STATE_MATCH: The request matches the api operation
      API_OPERATION_EVAL_STATE_NOT_MATCH: The request doesn't match the api
        operation
    """
    API_OPERATION_EVAL_STATE_UNSPECIFIED = 0
    API_OPERATION_EVAL_STATE_MATCH = 1
    API_OPERATION_EVAL_STATE_NOT_MATCH = 2

  class IdentityTypeEvalStateValueValuesEnum(_messages.Enum):
    r"""Details of the evaluation state of the identity type

    Values:
      IDENTITY_TYPE_EVAL_STATE_UNSPECIFIED: Not used
      IDENTITY_TYPE_EVAL_STATE_GRANTED: The request type matches the identity
      IDENTITY_TYPE_EVAL_STATE_NOT_GRANTED: The request type doesn't match the
        identity
      IDENTITY_TYPE_EVAL_STATE_NOT_SUPPORTED: The identity type is not
        supported
    """
    IDENTITY_TYPE_EVAL_STATE_UNSPECIFIED = 0
    IDENTITY_TYPE_EVAL_STATE_GRANTED = 1
    IDENTITY_TYPE_EVAL_STATE_NOT_GRANTED = 2
    IDENTITY_TYPE_EVAL_STATE_NOT_SUPPORTED = 3

  class IngressPolicyEvalStateValueValuesEnum(_messages.Enum):
    r"""The overall evaluation state of the ingress policy

    Values:
      INGRESS_POLICY_EVAL_STATE_UNSPECIFIED: Not used
      INGRESS_POLICY_EVAL_STATE_IN_SAME_SERVICE_PERIMETER: The resources are
        in the same regular service perimeter
      INGRESS_POLICY_EVAL_STATE_GRANTED_OVER_BRIDGE: The resources are in the
        same bridge service perimeter
      INGRESS_POLICY_EVAL_STATE_GRANTED_BY_POLICY: The request is granted by
        the ingress policy
      INGRESS_POLICY_EVAL_STATE_DENIED_BY_POLICY: The request is denied by the
        ingress policy
      INGRESS_POLICY_EVAL_STATE_NOT_APPLICABLE: The ingress policy is not
        applicable for the request
    """
    INGRESS_POLICY_EVAL_STATE_UNSPECIFIED = 0
    INGRESS_POLICY_EVAL_STATE_IN_SAME_SERVICE_PERIMETER = 1
    INGRESS_POLICY_EVAL_STATE_GRANTED_OVER_BRIDGE = 2
    INGRESS_POLICY_EVAL_STATE_GRANTED_BY_POLICY = 3
    INGRESS_POLICY_EVAL_STATE_DENIED_BY_POLICY = 4
    INGRESS_POLICY_EVAL_STATE_NOT_APPLICABLE = 5

  class IngressSourceEvalStatesValueListEntryValuesEnum(_messages.Enum):
    r"""IngressSourceEvalStatesValueListEntryValuesEnum enum type.

    Values:
      INGRESS_SOURCE_EVAL_STATE_UNSPECIFIED: Not used
      INGRESS_SOURCE_EVAL_STATE_MATCH: The request matches the ingress source
      INGRESS_SOURCE_EVAL_STATE_NOT_MATCH: The request doesn't match the
        ingress source
    """
    INGRESS_SOURCE_EVAL_STATE_UNSPECIFIED = 0
    INGRESS_SOURCE_EVAL_STATE_MATCH = 1
    INGRESS_SOURCE_EVAL_STATE_NOT_MATCH = 2

  class ResourceEvalStatesValueListEntryValuesEnum(_messages.Enum):
    r"""ResourceEvalStatesValueListEntryValuesEnum enum type.

    Values:
      RESOURCE_EVAL_STATE_UNSPECIFIED: Not used
      RESOURCE_EVAL_STATE_MATCH: The request matches the resource
      RESOURCE_EVAL_STATE_NOT_MATCH: The request doesn't match the resource
    """
    RESOURCE_EVAL_STATE_UNSPECIFIED = 0
    RESOURCE_EVAL_STATE_MATCH = 1
    RESOURCE_EVAL_STATE_NOT_MATCH = 2

  apiOperationEvalStates = _messages.EnumField('ApiOperationEvalStatesValueListEntryValuesEnum', 1, repeated=True)
  identityExplanations = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaIdentityExplanation', 2, repeated=True)
  identityTypeEvalState = _messages.EnumField('IdentityTypeEvalStateValueValuesEnum', 3)
  ingressPolicyEvalState = _messages.EnumField('IngressPolicyEvalStateValueValuesEnum', 4)
  ingressSourceEvalStates = _messages.EnumField('IngressSourceEvalStatesValueListEntryValuesEnum', 5, repeated=True)
  resourceEvalStates = _messages.EnumField('ResourceEvalStatesValueListEntryValuesEnum', 6, repeated=True)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaResolvedResource(_messages.Message):
  r"""The details of a resolved resource. NextTAG: 13

  Enums:
    ResolvedStateValueValuesEnum: The resolved resource's state
    ResourceTypeValueValuesEnum: The resource type of the resource.

  Fields:
    bridgeServicePerimeters: Full resource names of the bridge service
      perimeters that restrict the resource Format:
      `accessPolicies/{access_policy}/servicePerimeters/{service_perimeter}`
    dryrunBridgeServicePerimeters: Full resource names of the dryrun bridge
      service perimeters that restrict the resource Format:
      `accessPolicies/{access_policy}/servicePerimeters/{service_perimeter}`
    dryrunRegularServicePerimeters: Full resource name of the dry run regular
      service perimeters that restricts the resource Format:
      `accessPolicies/{access_policy}/servicePerimeters/{service_perimeter}`
    permissions: The iam permission names attached to this resource. This only
      applies to resources generated from resource containers.
    projectId: Project string identifier, in the format of
      "projects/{project_id}". e.g. "projects/my-project-123".
    projectInfo: Details of the project associated with this resolved
      resource.
    projectNumber: The project number of the project associated with this
      resolved resource. In the format of "projects/{project_number}".
    regularServicePerimeters: Full resource name of the regular service
      perimeters that restricts the resource Format:
      `accessPolicies/{access_policy}/servicePerimeters/{service_perimeter}`
    resolvedState: The resolved resource's state
    resource: Details of the resource
    resourceNames: The resource names belonging to this resource. For network
      resource, this is its network full name or redacted name.
    resourceType: The resource type of the resource.
  """

  class ResolvedStateValueValuesEnum(_messages.Enum):
    r"""The resolved resource's state

    Values:
      RESOLVED_STATE_UNSPECIFIED: Not used
      INFO_DENIED: The caller doesn't have permission to resolve this resource
      COMPLETED: The resource has been fully resolved
      NOT_APPLICABLE: The resource cannot be restricted by service perimeters
      ERROR: The resource cannot be resolved due to an error.
    """
    RESOLVED_STATE_UNSPECIFIED = 0
    INFO_DENIED = 1
    COMPLETED = 2
    NOT_APPLICABLE = 3
    ERROR = 4

  class ResourceTypeValueValuesEnum(_messages.Enum):
    r"""The resource type of the resource.

    Values:
      RESOURCE_TYPE_UNSPECIFIED: Not used
      NETWORK: Network resource type.
    """
    RESOURCE_TYPE_UNSPECIFIED = 0
    NETWORK = 1

  bridgeServicePerimeters = _messages.StringField(1, repeated=True)
  dryrunBridgeServicePerimeters = _messages.StringField(2, repeated=True)
  dryrunRegularServicePerimeters = _messages.StringField(3, repeated=True)
  permissions = _messages.StringField(4, repeated=True)
  projectId = _messages.StringField(5)
  projectInfo = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaResolvedResourceProjectInfo', 6)
  projectNumber = _messages.StringField(7)
  regularServicePerimeters = _messages.StringField(8, repeated=True)
  resolvedState = _messages.EnumField('ResolvedStateValueValuesEnum', 9)
  resource = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaResource', 10)
  resourceNames = _messages.StringField(11, repeated=True)
  resourceType = _messages.EnumField('ResourceTypeValueValuesEnum', 12)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaResolvedResourceProjectInfo(_messages.Message):
  r"""The details of a project. NextTAG: 2

  Fields:
    projectId: Project string identifier, e.g. "my-project-123".
  """

  projectId = _messages.StringField(1)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaResource(_messages.Message):
  r"""Resource checked by service perimeter check NextTAG: 3

  Fields:
    name: The name of the resource
    permissions: The iam permission names
  """

  name = _messages.StringField(1)
  permissions = _messages.StringField(2, repeated=True)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaServicePerimeterConfigExplanation(_messages.Message):
  r"""Explanation of service perimeter config NextTAG: 10

  Enums:
    AccessLevelsEvalStatesValueListEntryValuesEnum:
    EvalStateValueValuesEnum: Details about the evaluation state of a service
      perimeter config
    OverallEgressPoliciesEvalStateValueValuesEnum: Overall evaluation state of
      the egress policies
    OverallIngressPoliciesEvalStateValueValuesEnum: Overall evaluation state
      of the ingress policies
    RestrictedServicesEvalStateValueValuesEnum: Eval state of the restricted
      services

  Fields:
    accessLevelsEvalStates: Details about the evaluation state of access
      levels
    egressPoliciesExplanations: Explanation of egress policies
    evalState: Details about the evaluation state of a service perimeter
      config
    ingressPoliciesExplanations: Explanation of ingress policies
    overallEgressPoliciesEvalState: Overall evaluation state of the egress
      policies
    overallIngressPoliciesEvalState: Overall evaluation state of the ingress
      policies
    restrictedResources: Resources that are restricted by this service
      perimeter config
    restrictedServicesEvalState: Eval state of the restricted services
    vpcAccessibleServicesExplanation: Explanation of the vpc accessible
      service policy
  """

  class AccessLevelsEvalStatesValueListEntryValuesEnum(_messages.Enum):
    r"""AccessLevelsEvalStatesValueListEntryValuesEnum enum type.

    Values:
      ACCESS_LEVEL_EVAL_STATE_UNSPECIFIED: Not used
      ACCESS_LEVEL_EVAL_STATE_SATISFIED: The access level is satisfied
      ACCESS_LEVEL_EVAL_STATE_UNSATISFIED: The access level is unsatisfied
      ACCESS_LEVEL_EVAL_STATE_ERROR: The access level is not satisfied nor
        unsatisfied
      ACCESS_LEVEL_EVAL_STATE_NOT_EXIST: The access level does not exist
    """
    ACCESS_LEVEL_EVAL_STATE_UNSPECIFIED = 0
    ACCESS_LEVEL_EVAL_STATE_SATISFIED = 1
    ACCESS_LEVEL_EVAL_STATE_UNSATISFIED = 2
    ACCESS_LEVEL_EVAL_STATE_ERROR = 3
    ACCESS_LEVEL_EVAL_STATE_NOT_EXIST = 4

  class EvalStateValueValuesEnum(_messages.Enum):
    r"""Details about the evaluation state of a service perimeter config

    Values:
      EVAL_STATE_UNSPECIFIED: Not used
      NOT_APPLICABLE: The evaluation state of a service perimeter config is
        not applicable
      GRANTED: The service perimeter config grants the request
      DENIED: The service perimeter config denies the request
      INHERITED_FROM_ACTIVE: The service perimeter dry run config is inherited
        from the active service perimeter. The dry run evaluation state is the
        same as the active service perimeter evaluation state. This should
        only be set for dry run service perimeter config.
    """
    EVAL_STATE_UNSPECIFIED = 0
    NOT_APPLICABLE = 1
    GRANTED = 2
    DENIED = 3
    INHERITED_FROM_ACTIVE = 4

  class OverallEgressPoliciesEvalStateValueValuesEnum(_messages.Enum):
    r"""Overall evaluation state of the egress policies

    Values:
      OVERALL_EGRESS_POLICIES_EVAL_STATE_UNSPECIFIED: Not used
      OVERALL_EGRESS_POLICIES_EVAL_STATE_GRANTED: The request is granted by
        the egress policies
      OVERALL_EGRESS_POLICIES_EVAL_STATE_DENIED: The request is denied by the
        egress policies
      OVERALL_EGRESS_POLICIES_EVAL_STATE_NOT_APPLICABLE: The egress policies
        are applicable for the request
      OVERALL_EGRESS_POLICIES_EVAL_STATE_SKIPPED: The request skips the egress
        policies check
    """
    OVERALL_EGRESS_POLICIES_EVAL_STATE_UNSPECIFIED = 0
    OVERALL_EGRESS_POLICIES_EVAL_STATE_GRANTED = 1
    OVERALL_EGRESS_POLICIES_EVAL_STATE_DENIED = 2
    OVERALL_EGRESS_POLICIES_EVAL_STATE_NOT_APPLICABLE = 3
    OVERALL_EGRESS_POLICIES_EVAL_STATE_SKIPPED = 4

  class OverallIngressPoliciesEvalStateValueValuesEnum(_messages.Enum):
    r"""Overall evaluation state of the ingress policies

    Values:
      OVERALL_INGRESS_POLICIES_EVAL_STATE_UNSPECIFIED: Not used
      OVERALL_INGRESS_POLICIES_EVAL_STATE_GRANTED: The request is granted by
        the ingress policies
      OVERALL_INGRESS_POLICIES_EVAL_STATE_DENIED: The request is denied by the
        ingress policies
      OVERALL_INGRESS_POLICIES_EVAL_STATE_NOT_APPLICABLE: The ingress policies
        are applicable for the request
      OVERALL_INGRESS_POLICIES_EVAL_STATE_SKIPPED: The request skips the
        ingress policies check
    """
    OVERALL_INGRESS_POLICIES_EVAL_STATE_UNSPECIFIED = 0
    OVERALL_INGRESS_POLICIES_EVAL_STATE_GRANTED = 1
    OVERALL_INGRESS_POLICIES_EVAL_STATE_DENIED = 2
    OVERALL_INGRESS_POLICIES_EVAL_STATE_NOT_APPLICABLE = 3
    OVERALL_INGRESS_POLICIES_EVAL_STATE_SKIPPED = 4

  class RestrictedServicesEvalStateValueValuesEnum(_messages.Enum):
    r"""Eval state of the restricted services

    Values:
      RESTRICTED_SERVICES_EVAL_STATE_UNSPECIFIED: Not used
      IS_RESTRICTED: The request service is restricted
      IS_NOT_RESTRICTED: The request service is not restricted
    """
    RESTRICTED_SERVICES_EVAL_STATE_UNSPECIFIED = 0
    IS_RESTRICTED = 1
    IS_NOT_RESTRICTED = 2

  accessLevelsEvalStates = _messages.EnumField('AccessLevelsEvalStatesValueListEntryValuesEnum', 1, repeated=True)
  egressPoliciesExplanations = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaEgressPoliciesExplanation', 2, repeated=True)
  evalState = _messages.EnumField('EvalStateValueValuesEnum', 3)
  ingressPoliciesExplanations = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaIngressPoliciesExplanation', 4, repeated=True)
  overallEgressPoliciesEvalState = _messages.EnumField('OverallEgressPoliciesEvalStateValueValuesEnum', 5)
  overallIngressPoliciesEvalState = _messages.EnumField('OverallIngressPoliciesEvalStateValueValuesEnum', 6)
  restrictedResources = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaResource', 7, repeated=True)
  restrictedServicesEvalState = _messages.EnumField('RestrictedServicesEvalStateValueValuesEnum', 8)
  vpcAccessibleServicesExplanation = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaVpcAccessibleServicesExplanation', 9)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaServicePerimeterExplanation(_messages.Message):
  r"""Explanation of a service perimeter NextTAG: 4

  Fields:
    dryrunServicePerimeterConfigExplanation: Explanation of a dryrun service
      perimeter config
    name: The full name of the service perimeter
    servicePerimeterConfigExplanation: Explanation of a service perimeter
      config
  """

  dryrunServicePerimeterConfigExplanation = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaServicePerimeterConfigExplanation', 1)
  name = _messages.StringField(2)
  servicePerimeterConfigExplanation = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaServicePerimeterConfigExplanation', 3)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaTroubleshootServicePerimeterRequest(_messages.Message):
  r"""Request to troubleshoot service perimeters

  Fields:
    troubleshootingToken: The troubleshooting token can be generated when
      customers get access denied by the service perimeter
  """

  troubleshootingToken = _messages.StringField(1)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaTroubleshootServicePerimeterResponse(_messages.Message):
  r"""Response to troubleshoot service perimeters NextTAG: 14

  Enums:
    AccessStateValueValuesEnum: The access state of the active service
      perimeters.
    DryrunAccessStateValueValuesEnum: The access state of the dry run service
      perimeters

  Fields:
    accessPolicyExplanations: Explanation of access policies
    accessState: The access state of the active service perimeters.
    deviceInfo: Device information of the device from troubleshoot token.
    dryrunAccessState: The access state of the dry run service perimeters
    operation: Fully qualified name of the operation.
    principal: The principal email address of the violation principal from
      troubleshoot token.
    principalIp: The ip address of the violation principal from troubleshoot
      token.
    principalIpRegion: The region code of the principal ip address from
      troubleshoot token.
    requestTime: The request_time from troubleshooting token. It captures when
      the request generating the token was made. The violation time when token
      is logged because of the VPC SC violation.
    resolvedResources: Details about the resolved resources.
    service: The service name as specified in its service configuration. For
      example, `"pubsub.googleapis.com"`. See
      [google.api.Service](https://cloud.google.com/service-
      management/reference/rpc/google.api#google.api.Service) for the
      definition of a service name.
    supportedService: Supported service that indicates the current VPC-SC
      integration status.
  """

  class AccessStateValueValuesEnum(_messages.Enum):
    r"""The access state of the active service perimeters.

    Values:
      ACCESS_STATE_UNSPECIFIED: Not used
      NOT_APPLICABLE: The request is not restricted by any service perimeters
      GRANTED: The request is granted by service perimeters
      DENIED: The request is denied by service perimeters
    """
    ACCESS_STATE_UNSPECIFIED = 0
    NOT_APPLICABLE = 1
    GRANTED = 2
    DENIED = 3

  class DryrunAccessStateValueValuesEnum(_messages.Enum):
    r"""The access state of the dry run service perimeters

    Values:
      ACCESS_STATE_UNSPECIFIED: Not used
      NOT_APPLICABLE: The request is not restricted by any service perimeters
      GRANTED: The request is granted by service perimeters
      DENIED: The request is denied by service perimeters
    """
    ACCESS_STATE_UNSPECIFIED = 0
    NOT_APPLICABLE = 1
    GRANTED = 2
    DENIED = 3

  accessPolicyExplanations = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaAccessPolicyExplanation', 1, repeated=True)
  accessState = _messages.EnumField('AccessStateValueValuesEnum', 2)
  deviceInfo = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaDeviceInfo', 3)
  dryrunAccessState = _messages.EnumField('DryrunAccessStateValueValuesEnum', 4)
  operation = _messages.StringField(5)
  principal = _messages.StringField(6)
  principalIp = _messages.StringField(7)
  principalIpRegion = _messages.StringField(8)
  requestTime = _messages.StringField(9)
  resolvedResources = _messages.MessageField('GoogleCloudPolicytroubleshooterServiceperimeterV3alphaResolvedResource', 10, repeated=True)
  service = _messages.StringField(11)
  supportedService = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1SupportedService', 12)


class GoogleCloudPolicytroubleshooterServiceperimeterV3alphaVpcAccessibleServicesExplanation(_messages.Message):
  r"""Explanation of the vpc accessible service policy NextTAG: 2

  Enums:
    EvalStateValueValuesEnum: Details about the evaluation state of the vpc
      accessible service policy

  Fields:
    evalState: Details about the evaluation state of the vpc accessible
      service policy
  """

  class EvalStateValueValuesEnum(_messages.Enum):
    r"""Details about the evaluation state of the vpc accessible service
    policy

    Values:
      EVAL_STATE_UNSPECIFIED: Not used
      NOT_APPLICABLE: Vpc accessible service evaluation is not applicable
      GRANTED: Vpc accessible service policy grants the request
      DENIED: Vpc accessible service policy denies the request
      INTERNAL: It is an internal traffic
    """
    EVAL_STATE_UNSPECIFIED = 0
    NOT_APPLICABLE = 1
    GRANTED = 2
    DENIED = 3
    INTERNAL = 4

  evalState = _messages.EnumField('EvalStateValueValuesEnum', 1)


class GoogleIamV1AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('GoogleIamV1AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class GoogleIamV1AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    ignoreChildExemptions: A boolean attribute.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  ignoreChildExemptions = _messages.BooleanField(2)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 3)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    bindingId: A string attribute.
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=********************1`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=********************1`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=********************1`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  bindingId = _messages.StringField(1)
  condition = _messages.MessageField('GoogleTypeExpr', 2)
  members = _messages.StringField(3, repeated=True)
  role = _messages.StringField(4)


class GoogleIamV1Condition(_messages.Message):
  r"""A condition to be met.

  Enums:
    IamValueValuesEnum: Trusted attributes supplied by the IAM system.
    OpValueValuesEnum: An operator to apply the subject with.
    SysValueValuesEnum: Trusted attributes supplied by any service that owns
      resources and uses the IAM system for access control.

  Fields:
    iam: Trusted attributes supplied by the IAM system.
    op: An operator to apply the subject with.
    svc: Trusted attributes discharged by the service.
    sys: Trusted attributes supplied by any service that owns resources and
      uses the IAM system for access control.
    values: The objects of the condition.
  """

  class IamValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by the IAM system.

    Values:
      NO_ATTR: Default non-attribute.
      AUTHORITY: Either principal or (if present) authority selector.
      ATTRIBUTION: The principal (even if an authority selector is present),
        which must only be used for attribution, not authorization.
      SECURITY_REALM: Any of the security realms in the IAMContext
        (go/security-realms). When used with IN, the condition indicates "any
        of the request's realms match one of the given values; with NOT_IN,
        "none of the realms match any of the given values". Note that a value
        can be: - 'self:campus' (i.e., clients that are in the same campus) -
        'self:metro' (i.e., clients that are in the same metro) - 'self:cloud-
        region' (i.e., allow connections from clients that are in the same
        cloud region) - 'self:prod-region' (i.e., allow connections from
        clients that are in the same prod region) - 'guardians' (i.e., allow
        connections from its guardian realms. See go/security-realms-
        glossary#guardian for more information.) - 'cryto_core_guardians'
        (i.e., allow connections from its crypto core guardian realms. See
        go/security-realms-glossary#guardian for more information.) Crypto
        Core coverage is a super-set of Default coverage, containing
        information about coverage between higher tier data centers (e.g.,
        YAWNs). Most services should use Default coverage and only use Crypto
        Core coverage if the service is involved in greenfield turnup of new
        higher tier data centers (e.g., credential infrastructure, machine/job
        management systems, etc.). - 'self' [DEPRECATED] (i.e., allow
        connections from clients that are in the same security realm, which is
        currently but not guaranteed to be campus-sized) - a realm (e.g.,
        'campus-abc') - a realm group (e.g., 'realms-for-borg-cell-xx', see:
        go/realm-groups) A match is determined by a realm group membership
        check performed by a RealmAclRep object (go/realm-acl-howto). It is
        not permitted to grant access based on the *absence* of a realm, so
        realm conditions can only be used in a "positive" context (e.g.,
        ALLOW/IN or DENY/NOT_IN).
      APPROVER: An approver (distinct from the requester) that has authorized
        this request. When used with IN, the condition indicates that one of
        the approvers associated with the request matches the specified
        principal, or is a member of the specified group. Approvers can only
        grant additional access, and are thus only used in a strictly positive
        context (e.g. ALLOW/IN or DENY/NOT_IN).
      JUSTIFICATION_TYPE: What types of justifications have been supplied with
        this request. String values should match enum names from
        security.credentials.JustificationType, e.g. "MANUAL_STRING". It is
        not permitted to grant access based on the *absence* of a
        justification, so justification conditions can only be used in a
        "positive" context (e.g., ALLOW/IN or DENY/NOT_IN). Multiple
        justifications, e.g., a Buganizer ID and a manually-entered reason,
        are normal and supported.
      CREDENTIALS_TYPE: What type of credentials have been supplied with this
        request. String values should match enum names from
        security_loas_l2.CredentialsType - currently, only
        CREDS_TYPE_EMERGENCY is supported. It is not permitted to grant access
        based on the *absence* of a credentials type, so the conditions can
        only be used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
      CREDS_ASSERTION: Properties of the credentials supplied with this
        request. See http://go/rpcsp-credential-assertions?polyglot=rpcsp-v1-0
        The conditions can only be used in a "positive" context (e.g.,
        ALLOW/IN or DENY/NOT_IN).
    """
    NO_ATTR = 0
    AUTHORITY = 1
    ATTRIBUTION = 2
    SECURITY_REALM = 3
    APPROVER = 4
    JUSTIFICATION_TYPE = 5
    CREDENTIALS_TYPE = 6
    CREDS_ASSERTION = 7

  class OpValueValuesEnum(_messages.Enum):
    r"""An operator to apply the subject with.

    Values:
      NO_OP: Default no-op.
      EQUALS: DEPRECATED. Use IN instead.
      NOT_EQUALS: DEPRECATED. Use NOT_IN instead.
      IN: The condition is true if the subject (or any element of it if it is
        a set) matches any of the supplied values.
      NOT_IN: The condition is true if the subject (or every element of it if
        it is a set) matches none of the supplied values.
      DISCHARGED: Subject is discharged
    """
    NO_OP = 0
    EQUALS = 1
    NOT_EQUALS = 2
    IN = 3
    NOT_IN = 4
    DISCHARGED = 5

  class SysValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by any service that owns resources and
    uses the IAM system for access control.

    Values:
      NO_ATTR: Default non-attribute type
      REGION: Region of the resource
      SERVICE: Service name
      NAME: Resource name
      IP: IP address of the caller
    """
    NO_ATTR = 0
    REGION = 1
    SERVICE = 2
    NAME = 3
    IP = 4

  iam = _messages.EnumField('IamValueValuesEnum', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  svc = _messages.StringField(3)
  sys = _messages.EnumField('SysValueValuesEnum', 4)
  values = _messages.StringField(5, repeated=True)


class GoogleIamV1LogConfig(_messages.Message):
  r"""Specifies what kind of log the caller must write

  Fields:
    cloudAudit: Cloud audit options.
    counter: Counter options.
    dataAccess: Data access options.
  """

  cloudAudit = _messages.MessageField('GoogleIamV1LogConfigCloudAuditOptions', 1)
  counter = _messages.MessageField('GoogleIamV1LogConfigCounterOptions', 2)
  dataAccess = _messages.MessageField('GoogleIamV1LogConfigDataAccessOptions', 3)


class GoogleIamV1LogConfigCloudAuditOptions(_messages.Message):
  r"""Write a Cloud Audit log

  Enums:
    LogNameValueValuesEnum: The log_name to populate in the Cloud Audit
      Record.
    PermissionTypeValueValuesEnum: The type associated with the permission.

  Fields:
    authorizationLoggingOptions: Information used by the Cloud Audit Logging
      pipeline. Will be deprecated once the migration to PermissionType is
      complete (b/201806118).
    logName: The log_name to populate in the Cloud Audit Record.
    permissionType: The type associated with the permission.
  """

  class LogNameValueValuesEnum(_messages.Enum):
    r"""The log_name to populate in the Cloud Audit Record.

    Values:
      UNSPECIFIED_LOG_NAME: Default. Should not be used.
      ADMIN_ACTIVITY: Corresponds to "cloudaudit.googleapis.com/activity"
      DATA_ACCESS: Corresponds to "cloudaudit.googleapis.com/data_access"
    """
    UNSPECIFIED_LOG_NAME = 0
    ADMIN_ACTIVITY = 1
    DATA_ACCESS = 2

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type associated with the permission.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: Permissions that gate reading resource configuration or
        metadata.
      ADMIN_WRITE: Permissions that gate modification of resource
        configuration or metadata.
      DATA_READ: Permissions that gate reading user-provided data.
      DATA_WRITE: Permissions that gate writing user-provided data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  authorizationLoggingOptions = _messages.MessageField('GoogleCloudAuditAuthorizationLoggingOptions', 1)
  logName = _messages.EnumField('LogNameValueValuesEnum', 2)
  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 3)


class GoogleIamV1LogConfigCounterOptions(_messages.Message):
  r"""Increment a streamz counter with the specified metric and field names.
  Metric names should start with a '/', generally be lowercase-only, and end
  in "_count". Field names should not contain an initial slash. The actual
  exported metric names will have "/iam/policy" prepended. Field names
  correspond to IAM request parameters and field values are their respective
  values. Supported field names: - "authority", which is "[token]" if
  IAMContext.token is present, otherwise the value of
  IAMContext.authority_selector if present, and otherwise a representation of
  IAMContext.principal; or - "iam_principal", a representation of
  IAMContext.principal even if a token or authority selector is present; or -
  "" (empty string), resulting in a counter with no fields. Examples: counter
  { metric: "/debug_access_count" field: "iam_principal" } ==> increment
  counter /iam/policy/debug_access_count {iam_principal=[value of
  IAMContext.principal]}

  Fields:
    customFields: Custom fields.
    field: The field value to attribute.
    metric: The metric to update.
  """

  customFields = _messages.MessageField('GoogleIamV1LogConfigCounterOptionsCustomField', 1, repeated=True)
  field = _messages.StringField(2)
  metric = _messages.StringField(3)


class GoogleIamV1LogConfigCounterOptionsCustomField(_messages.Message):
  r"""Custom fields. These can be used to create a counter with arbitrary
  field/value pairs. See: go/rpcsp-custom-fields.

  Fields:
    name: Name is the field name.
    value: Value is the field value. It is important that in contrast to the
      CounterOptions.field, the value here is a constant that is not derived
      from the IAMContext.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleIamV1LogConfigDataAccessOptions(_messages.Message):
  r"""Write a Data Access (Gin) log

  Enums:
    LogModeValueValuesEnum:

  Fields:
    isDirectAuth: Indicates that access was granted by a regular grant policy
    logMode: A LogModeValueValuesEnum attribute.
  """

  class LogModeValueValuesEnum(_messages.Enum):
    r"""LogModeValueValuesEnum enum type.

    Values:
      LOG_MODE_UNSPECIFIED: Client is not required to write a partial Gin log
        immediately after the authorization check. If client chooses to write
        one and it fails, client may either fail open (allow the operation to
        continue) or fail closed (handle as a DENY outcome).
      LOG_FAIL_CLOSED: The application's operation in the context of which
        this authorization check is being made may only be performed if it is
        successfully logged to Gin. For instance, the authorization library
        may satisfy this obligation by emitting a partial log entry at
        authorization check time and only returning ALLOW to the application
        if it succeeds. If a matching Rule has this directive, but the client
        has not indicated that it will honor such requirements, then the IAM
        check will result in authorization failure by setting
        CheckPolicyResponse.success=false.
    """
    LOG_MODE_UNSPECIFIED = 0
    LOG_FAIL_CLOSED = 1

  isDirectAuth = _messages.BooleanField(1)
  logMode = _messages.EnumField('LogModeValueValuesEnum', 2)


class GoogleIamV1Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    rules: If more than one rule is specified, the rules are applied in the
      following manner: - All matching LOG rules are always applied. - If any
      DENY/DENY_WITH_LOG rule matches, permission is denied. Logging will be
      applied if one or more matching rule requires logging. - Otherwise, if
      any ALLOW/ALLOW_WITH_LOG rule matches, permission is granted. Logging
      will be applied if one or more matching rule requires logging. -
      Otherwise, if no rule applies, permission is denied.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('GoogleIamV1AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  rules = _messages.MessageField('GoogleIamV1Rule', 4, repeated=True)
  version = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class GoogleIamV1Rule(_messages.Message):
  r"""A rule to be applied in a Policy.

  Enums:
    ActionValueValuesEnum: Required

  Fields:
    action: Required
    conditions: Additional restrictions that must be met. All conditions must
      pass for the rule to match.
    description: Human-readable description of the rule.
    in_: If one or more 'in' clauses are specified, the rule matches if the
      PRINCIPAL/AUTHORITY_SELECTOR is in at least one of these entries.
    logConfig: The config returned to callers of CheckPolicy for any entries
      that match the LOG action.
    notIn: If one or more 'not_in' clauses are specified, the rule matches if
      the PRINCIPAL/AUTHORITY_SELECTOR is in none of the entries. The format
      for in and not_in entries can be found at in the Local IAM documentation
      (see go/local-iam#features).
    permissions: A permission is a string of form `..` (e.g.,
      'storage.buckets.list'). A value of '*' matches all permissions, and a
      verb part of '*' (e.g., 'storage.buckets.*') matches all verbs.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required

    Values:
      NO_ACTION: Default no action.
      ALLOW: Matching 'Entries' grant access.
      ALLOW_WITH_LOG: Matching 'Entries' grant access and the caller promises
        to log the request per the returned log_configs.
      DENY: Matching 'Entries' deny access.
      DENY_WITH_LOG: Matching 'Entries' deny access and the caller promises to
        log the request per the returned log_configs.
      LOG: Matching 'Entries' tell IAM.Check callers to generate logs.
    """
    NO_ACTION = 0
    ALLOW = 1
    ALLOW_WITH_LOG = 2
    DENY = 3
    DENY_WITH_LOG = 4
    LOG = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  conditions = _messages.MessageField('GoogleIamV1Condition', 2, repeated=True)
  description = _messages.StringField(3)
  in_ = _messages.StringField(4, repeated=True)
  logConfig = _messages.MessageField('GoogleIamV1LogConfig', 5, repeated=True)
  notIn = _messages.StringField(6, repeated=True)
  permissions = _messages.StringField(7, repeated=True)


class GoogleIamV2AccessBoundaryRule(_messages.Message):
  r"""An IAM access boundary rule, which defines an upper bound of IAM
  permissions on a single resource. All access boundary rules in an access
  boundary policy are evaluated together as a union. Even if this access
  boundary rule does not allow access to the resource, another access boundary
  rule might allow access.

  Fields:
    availabilityCondition: Optional. An availability condition that further
      constrains the access allowed by the access boundary rule. If the
      condition evaluates to `true`, then this access boundary rule will
      provide access to the specified resource, assuming the principal has the
      required permissions for the resource. If the condition does not
      evaluate to `true`, then access to the specified resource will not be
      available. The condition can only evaluate the access level for the
      request. Access levels use the format
      `accessPolicies/{policy_name}/accessLevels/{access_level_shortname}`.
    availablePermissions: Required. A list of permissions that may be allowed
      for use on the specified resource. The only supported value is `*`,
      which represents all permissions.
    availableResource: Required. The full resource name of a Google Cloud
      resource. The format is defined at
      https://cloud.google.com/apis/design/resource_names. The only supported
      value is `*`, which represents all resources.
  """

  availabilityCondition = _messages.MessageField('GoogleTypeExpr', 1)
  availablePermissions = _messages.StringField(2, repeated=True)
  availableResource = _messages.StringField(3)


class GoogleIamV2DenyRule(_messages.Message):
  r"""A deny rule in an IAM deny policy.

  Fields:
    denialCondition: The condition that determines whether this deny rule
      applies to a request. If the condition expression evaluates to `true`,
      then the deny rule is applied; otherwise, the deny rule is not applied.
      Each deny rule is evaluated independently. If this deny rule does not
      apply to a request, other deny rules might still apply. The condition
      can use CEL functions that evaluate [resource
      tags](https://cloud.google.com/iam/help/conditions/resource-tags). Other
      functions and operators are not supported.
    deniedPermissions: The permissions that are explicitly denied by this
      rule. Each permission uses the format
      `{service_fqdn}/{resource}.{verb}`, where `{service_fqdn}` is the fully
      qualified domain name for the service. For example,
      `iam.googleapis.com/roles.list`.
    deniedPrincipals:  The identities that are prevented from using one or
      more permissions on Google Cloud resources. This field can contain the
      following values: * `principal://goog/subject/{email_id}`: A specific
      Google Account. Includes Gmail, Cloud Identity, and Google Workspace
      user accounts. For example,
      `principal://goog/subject/<EMAIL>`. * `principal://iam.googlea
      pis.com/projects/-/serviceAccounts/{service_account_id}`: A Google Cloud
      service account. For example,
      `principal://iam.googleapis.com/projects/-/serviceAccounts/my-service-
      <EMAIL>`. *
      `principalSet://goog/group/{group_id}`: A Google group. For example,
      `principalSet://goog/group/<EMAIL>`. *
      `principalSet://goog/public:all`: A special identifier that represents
      any principal that is on the internet, even if they do not have a Google
      Account or are not logged in. *
      `principalSet://goog/cloudIdentityCustomerId/{customer_id}`: All of the
      principals associated with the specified Google Workspace or Cloud
      Identity customer ID. For example,
      `principalSet://goog/cloudIdentityCustomerId/C01Abc35`. * `principal://i
      am.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{sub
      ject_attribute_value}`: A single identity in a workforce identity pool.
      * `principalSet://iam.googleapis.com/locations/global/workforcePools/{po
      ol_id}/group/{group_id}`: All workforce identities in a group. * `princi
      palSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/at
      tribute.{attribute_name}/{attribute_value}`: All workforce identities
      with a specific attribute value. * `principalSet://iam.googleapis.com/lo
      cations/global/workforcePools/{pool_id}/*`: All identities in a
      workforce identity pool. * `principal://iam.googleapis.com/projects/{pro
      ject_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{s
      ubject_attribute_value}`: A single identity in a workload identity pool.
      * `principalSet://iam.googleapis.com/projects/{project_number}/locations
      /global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload
      identity pool group. * `principalSet://iam.googleapis.com/projects/{proj
      ect_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{
      attribute_name}/{attribute_value}`: All identities in a workload
      identity pool with a certain attribute. * `principalSet://iam.googleapis
      .com/projects/{project_number}/locations/global/workloadIdentityPools/{p
      ool_id}/*`: All identities in a workload identity pool. * `principalSet:
      //cloudresourcemanager.googleapis.com/[projects|folders|organizations]/{
      project_number|folder_number|org_number}/type/ServiceAccount`: All
      service accounts grouped under a resource (project, folder, or
      organization). * `principalSet://cloudresourcemanager.googleapis.com/[pr
      ojects|folders|organizations]/{project_number|folder_number|org_number}/
      type/ServiceAgent`: All service agents grouped under a resource
      (project, folder, or organization). *
      `deleted:principal://goog/subject/{email_id}?uid={uid}`: A specific
      Google Account that was deleted recently. For example,
      `deleted:principal://goog/subject/<EMAIL>?uid=**********`. If
      the Google Account is recovered, this identifier reverts to the standard
      identifier for a Google Account. *
      `deleted:principalSet://goog/group/{group_id}?uid={uid}`: A Google group
      that was deleted recently. For example,
      `deleted:principalSet://goog/group/<EMAIL>?uid=**********`.
      If the Google group is restored, this identifier reverts to the standard
      identifier for a Google group. * `deleted:principal://iam.googleapis.com
      /projects/-/serviceAccounts/{service_account_id}?uid={uid}`: A Google
      Cloud service account that was deleted recently. For example,
      `deleted:principal://iam.googleapis.com/projects/-/serviceAccounts/my-
      <EMAIL>?uid=**********`. If the service
      account is undeleted, this identifier reverts to the standard identifier
      for a service account. * `deleted:principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`:
      Deleted single identity in a workforce identity pool. For example, `dele
      ted:principal://iam.googleapis.com/locations/global/workforcePools/my-
      pool-id/subject/my-subject-attribute-value`.
    exceptionPermissions: Specifies the permissions that this rule excludes
      from the set of denied permissions given by `denied_permissions`. If a
      permission appears in `denied_permissions` _and_ in
      `exception_permissions` then it will _not_ be denied. The excluded
      permissions can be specified using the same syntax as
      `denied_permissions`.
    exceptionPrincipals: The identities that are excluded from the deny rule,
      even if they are listed in the `denied_principals`. For example, you
      could add a Google group to the `denied_principals`, then exclude
      specific users who belong to that group. This field can contain the same
      values as the `denied_principals` field, excluding
      `principalSet://goog/public:all`, which represents all users on the
      internet.
    exemptedCredentialLevels: A list of credential levels that are excluded
      from this rule. If a request contains _any_ of the
      exempted_credential_levels, it will _not_ be denied.
  """

  denialCondition = _messages.MessageField('GoogleTypeExpr', 1)
  deniedPermissions = _messages.StringField(2, repeated=True)
  deniedPrincipals = _messages.StringField(3, repeated=True)
  exceptionPermissions = _messages.StringField(4, repeated=True)
  exceptionPrincipals = _messages.StringField(5, repeated=True)
  exemptedCredentialLevels = _messages.StringField(6, repeated=True)


class GoogleIamV2Policy(_messages.Message):
  r"""Data for an IAM policy.

  Messages:
    AnnotationsValue: A key-value map to store arbitrary metadata for the
      `Policy`. Keys can be up to 63 characters. Values can be up to 255
      characters.

  Fields:
    annotations: A key-value map to store arbitrary metadata for the `Policy`.
      Keys can be up to 63 characters. Values can be up to 255 characters.
    createTime: Output only. The time when the `Policy` was created.
    deleteTime: Output only. The time when the `Policy` was deleted. Empty if
      the policy is not deleted.
    displayName: A user-specified description of the `Policy`. This value can
      be up to 63 characters.
    etag: An opaque tag that identifies the current version of the `Policy`.
      IAM uses this value to help manage concurrent updates, so they do not
      cause one update to be overwritten by another. If this field is present
      in a CreatePolicyRequest, the value is ignored.
    kind: Output only. The kind of the `Policy`. Always contains the value
      `DenyPolicy`.
    managingAuthority: Immutable. Specifies that this policy is managed by an
      authority and can only be modified by that authority. Usage is
      restricted.
    name: Immutable. The resource name of the `Policy`, which must be unique.
      Format: `policies/{attachment_point}/denypolicies/{policy_id}` The
      attachment point is identified by its URL-encoded full resource name,
      which means that the forward-slash character, `/`, must be written as
      `%2F`. For example,
      `policies/cloudresourcemanager.googleapis.com%2Fprojects%2Fmy-
      project/denypolicies/my-deny-policy`. For organizations and folders, use
      the numeric ID in the full resource name. For projects, requests can use
      the alphanumeric or the numeric ID. Responses always contain the numeric
      ID.
    rules: A list of rules that specify the behavior of the `Policy`. All of
      the rules should be of the `kind` specified in the `Policy`.
    uid: Immutable. The globally unique ID of the `Policy`. Assigned
      automatically when the `Policy` is created.
    updateTime: Output only. The time when the `Policy` was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""A key-value map to store arbitrary metadata for the `Policy`. Keys can
    be up to 63 characters. Values can be up to 255 characters.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  deleteTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  kind = _messages.StringField(6)
  managingAuthority = _messages.StringField(7)
  name = _messages.StringField(8)
  rules = _messages.MessageField('GoogleIamV2PolicyRule', 9, repeated=True)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class GoogleIamV2PolicyRule(_messages.Message):
  r"""A single rule in a `Policy`.

  Fields:
    accessBoundaryRule: A rule for an access boundary policy.
    denyRule: A rule for a deny policy.
    description: A user-specified description of the rule. This value can be
      up to 256 characters.
  """

  accessBoundaryRule = _messages.MessageField('GoogleIamV2AccessBoundaryRule', 1)
  denyRule = _messages.MessageField('GoogleIamV2DenyRule', 2)
  description = _messages.StringField(3)


class GoogleIamV3PolicyBinding(_messages.Message):
  r"""IAM policy binding resource.

  Enums:
    PolicyKindValueValuesEnum: Immutable. The kind of the policy to attach in
      this binding. This field must be one of the following: - Left empty
      (will be automatically set to the policy kind) - The input policy kind

  Messages:
    AnnotationsValue: Optional. User-defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations

  Fields:
    annotations: Optional. User-defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations
    condition: Optional. The condition to apply to the policy binding. When
      set, the `expression` field in the `Expr` must include from 1 to 10
      subexpressions, joined by the "||"(Logical OR), "&&"(Logical AND) or
      "!"(Logical NOT) operators and cannot contain more than 250 characters.
      The condition is currently only supported when bound to policies of kind
      principal access boundary. When the bound policy is a principal access
      boundary policy, the only supported attributes in any subexpression are
      `principal.type` and `principal.subject`. An example expression is:
      "principal.type == 'iam.googleapis.com/ServiceAccount'" or
      "principal.subject == '<EMAIL>'". Allowed operations for
      `principal.subject`: - `principal.subject == ` - `principal.subject != `
      - `principal.subject in []` - `principal.subject.startsWith()` -
      `principal.subject.endsWith()` Allowed operations for `principal.type`:
      - `principal.type == ` - `principal.type != ` - `principal.type in []`
      Supported principal types are Workspace, Workforce Pool, Workload Pool
      and Service Account. Allowed string must be one of: -
      iam.googleapis.com/WorkspaceIdentity -
      iam.googleapis.com/WorkforcePoolIdentity -
      iam.googleapis.com/WorkloadPoolIdentity -
      iam.googleapis.com/ServiceAccount
    createTime: Output only. The time when the policy binding was created.
    displayName: Optional. The description of the policy binding. Must be less
      than or equal to 63 characters.
    etag: Optional. The etag for the policy binding. If this is provided on
      update, it must match the server's etag.
    name: Identifier. The name of the policy binding, in the format
      `{binding_parent/locations/{location}/policyBindings/{policy_binding_id}
      `. The binding parent is the closest Resource Manager resource (project,
      folder, or organization) to the binding target. Format: * `projects/{pro
      ject_id}/locations/{location}/policyBindings/{policy_binding_id}` * `pro
      jects/{project_number}/locations/{location}/policyBindings/{policy_bindi
      ng_id}` * `folders/{folder_id}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `organizations/{organization_id}/locations/{location}/
      policyBindings/{policy_binding_id}`
    policy: Required. Immutable. The resource name of the policy to be bound.
      The binding parent and policy must belong to the same organization.
    policyKind: Immutable. The kind of the policy to attach in this binding.
      This field must be one of the following: - Left empty (will be
      automatically set to the policy kind) - The input policy kind
    policyUid: Output only. The globally unique ID of the policy to be bound.
    target: Required. Immutable. Target is the full resource name of the
      resource to which the policy will be bound. Immutable once set.
    uid: Output only. The globally unique ID of the policy binding. Assigned
      when the policy binding is created.
    updateTime: Output only. The time when the policy binding was most
      recently updated.
  """

  class PolicyKindValueValuesEnum(_messages.Enum):
    r"""Immutable. The kind of the policy to attach in this binding. This
    field must be one of the following: - Left empty (will be automatically
    set to the policy kind) - The input policy kind

    Values:
      POLICY_KIND_UNSPECIFIED: Unspecified policy kind; Not a valid state
      PRINCIPAL_ACCESS_BOUNDARY: Principal access boundary policy kind
      ACCESS: Access policy kind. Keep behind visibility label until Access
        Policy launch.
    """
    POLICY_KIND_UNSPECIFIED = 0
    PRINCIPAL_ACCESS_BOUNDARY = 1
    ACCESS = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User-defined annotations. See
    https://google.aip.dev/148#annotations for more details such as format and
    size limitations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  condition = _messages.MessageField('GoogleTypeExpr', 2)
  createTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  policy = _messages.StringField(7)
  policyKind = _messages.EnumField('PolicyKindValueValuesEnum', 8)
  policyUid = _messages.StringField(9)
  target = _messages.MessageField('GoogleIamV3PolicyBindingTarget', 10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class GoogleIamV3PolicyBindingTarget(_messages.Message):
  r"""Target is the full resource name of the resource to which the policy
  will be bound. Immutable once set.

  Fields:
    principalSet: Immutable. Full Resource Name used for principal access
      boundary policy bindings. The principal set must be directly parented by
      the policy binding's parent or same as the parent if the target is a
      project/folder/organization. Examples: * For binding's parented by an
      organization: * Organization:
      `//cloudresourcemanager.googleapis.com/organizations/ORGANIZATION_ID` *
      Workforce Identity:
      `//iam.googleapis.com/locations/global/workforcePools/WORKFORCE_POOL_ID`
      * Workspace Identity:
      `//iam.googleapis.com/locations/global/workspace/WORKSPACE_ID` * For
      binding's parented by a folder: * Folder:
      `//cloudresourcemanager.googleapis.com/folders/FOLDER_ID` * For
      binding's parented by a project: * Project: *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_NUMBER` *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_ID` * Workload
      Identity Pool: `//iam.googleapis.com/projects/PROJECT_NUMBER/locations/L
      OCATION/workloadIdentityPools/WORKLOAD_POOL_ID`
    resource: Immutable. Full Resource Name used for access policy bindings
      Examples: * Organization:
      `//cloudresourcemanager.googleapis.com/organizations/ORGANIZATION_ID` *
      Folder: `//cloudresourcemanager.googleapis.com/folders/FOLDER_ID` *
      Project: *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_NUMBER` *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_ID`
  """

  principalSet = _messages.StringField(1)
  resource = _messages.StringField(2)


class GoogleIamV3PrincipalAccessBoundaryPolicy(_messages.Message):
  r"""An IAM principal access boundary policy resource.

  Messages:
    AnnotationsValue: Optional. User defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations

  Fields:
    annotations: Optional. User defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations
    createTime: Output only. The time when the principal access boundary
      policy was created.
    details: Optional. The details for the principal access boundary policy.
    displayName: Optional. The description of the principal access boundary
      policy. Must be less than or equal to 63 characters.
    etag: Optional. The etag for the principal access boundary. If this is
      provided on update, it must match the server's etag.
    name: Identifier. The resource name of the principal access boundary
      policy. The following format is supported: `organizations/{organization_
      id}/locations/{location}/principalAccessBoundaryPolicies/{policy_id}`
    uid: Output only. The globally unique ID of the principal access boundary
      policy.
    updateTime: Output only. The time when the principal access boundary
      policy was most recently updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User defined annotations. See
    https://google.aip.dev/148#annotations for more details such as format and
    size limitations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  details = _messages.MessageField('GoogleIamV3PrincipalAccessBoundaryPolicyDetails', 3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  uid = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class GoogleIamV3PrincipalAccessBoundaryPolicyDetails(_messages.Message):
  r"""Principal access boundary policy details

  Fields:
    enforcementVersion: Optional. The version number (for example, `1` or
      `latest`) that indicates which permissions are able to be blocked by the
      policy. If empty, the PAB policy version will be set to the most recent
      version number at the time of the policy's creation.
    rules: Required. A list of principal access boundary policy rules. The
      number of rules in a policy is limited to 500.
  """

  enforcementVersion = _messages.StringField(1)
  rules = _messages.MessageField('GoogleIamV3PrincipalAccessBoundaryPolicyRule', 2, repeated=True)


class GoogleIamV3PrincipalAccessBoundaryPolicyRule(_messages.Message):
  r"""Principal access boundary policy rule that defines the resource
  boundary.

  Enums:
    EffectValueValuesEnum: Required. The access relationship of principals to
      the resources in this rule.

  Fields:
    description: Optional. The description of the principal access boundary
      policy rule. Must be less than or equal to 256 characters.
    effect: Required. The access relationship of principals to the resources
      in this rule.
    resources: Required. A list of Resource Manager resources. If a resource
      is listed in the rule, then the rule applies for that resource and its
      descendants. The number of resources in a policy is limited to 500
      across all rules in the policy. The following resource types are
      supported: * Organizations, such as
      `//cloudresourcemanager.googleapis.com/organizations/123`. * Folders,
      such as `//cloudresourcemanager.googleapis.com/folders/123`. * Projects,
      such as `//cloudresourcemanager.googleapis.com/projects/123` or
      `//cloudresourcemanager.googleapis.com/projects/my-project-id`.
  """

  class EffectValueValuesEnum(_messages.Enum):
    r"""Required. The access relationship of principals to the resources in
    this rule.

    Values:
      EFFECT_UNSPECIFIED: Effect unspecified.
      ALLOW: Allows access to the resources in this rule.
    """
    EFFECT_UNSPECIFIED = 0
    ALLOW = 1

  description = _messages.StringField(1)
  effect = _messages.EnumField('EffectValueValuesEnum', 2)
  resources = _messages.StringField(3, repeated=True)


class GoogleIdentityAccesscontextmanagerV1ServicePerimeter(_messages.Message):
  r"""`ServicePerimeter` describes a set of Google Cloud resources which can
  freely import and export data amongst themselves, but not export outside of
  the `ServicePerimeter`. If a request with a source within this
  `ServicePerimeter` has a target outside of the `ServicePerimeter`, the
  request will be blocked. Otherwise the request is allowed. There are two
  types of Service Perimeter - Regular and Bridge. Regular Service Perimeters
  cannot overlap, a single Google Cloud project or VPC network can only belong
  to a single regular Service Perimeter. Service Perimeter Bridges can contain
  only Google Cloud projects as members, a single Google Cloud project may
  belong to multiple Service Perimeter Bridges.

  Enums:
    PerimeterTypeValueValuesEnum: Perimeter type indicator. A single project
      or VPC network is allowed to be a member of single regular perimeter,
      but multiple service perimeter bridges. A project cannot be a included
      in a perimeter bridge without being included in regular perimeter. For
      perimeter bridges, the restricted service list as well as access level
      lists must be empty.

  Fields:
    description: Description of the `ServicePerimeter` and its use. Does not
      affect behavior.
    etag: Optional. An opaque identifier for the current version of the
      `ServicePerimeter`. This identifier does not follow any specific format.
      If an etag is not provided, the operation will be performed as if a
      valid etag is provided.
    name: Identifier. Resource name for the `ServicePerimeter`. Format:
      `accessPolicies/{access_policy}/servicePerimeters/{service_perimeter}`.
      The `service_perimeter` component must begin with a letter, followed by
      alphanumeric characters or `_`. After you create a `ServicePerimeter`,
      you cannot change its `name`.
    perimeterType: Perimeter type indicator. A single project or VPC network
      is allowed to be a member of single regular perimeter, but multiple
      service perimeter bridges. A project cannot be a included in a perimeter
      bridge without being included in regular perimeter. For perimeter
      bridges, the restricted service list as well as access level lists must
      be empty.
    spec: Proposed (or dry run) ServicePerimeter configuration. This
      configuration allows to specify and test ServicePerimeter configuration
      without enforcing actual access restrictions. Only allowed to be set
      when the "use_explicit_dry_run_spec" flag is set.
    status: Current ServicePerimeter configuration. Specifies sets of
      resources, restricted services and access levels that determine
      perimeter content and boundaries.
    title: Human readable title. Must be unique within the Policy.
    useExplicitDryRunSpec: Use explicit dry run spec flag. Ordinarily, a dry-
      run spec implicitly exists for all Service Perimeters, and that spec is
      identical to the status for those Service Perimeters. When this flag is
      set, it inhibits the generation of the implicit spec, thereby allowing
      the user to explicitly provide a configuration ("spec") to use in a dry-
      run version of the Service Perimeter. This allows the user to test
      changes to the enforced config ("status") without actually enforcing
      them. This testing is done through analyzing the differences between
      currently enforced and suggested restrictions. use_explicit_dry_run_spec
      must bet set to True if any of the fields in the spec are set to non-
      default values.
    weakenedForTesting: Indicates this Perimeter is intentionally weakened for
      Google internal testing. This will cause the Perimeter to accept non-
      prod P4 accounts as if they were prod accounts.
  """

  class PerimeterTypeValueValuesEnum(_messages.Enum):
    r"""Perimeter type indicator. A single project or VPC network is allowed
    to be a member of single regular perimeter, but multiple service perimeter
    bridges. A project cannot be a included in a perimeter bridge without
    being included in regular perimeter. For perimeter bridges, the restricted
    service list as well as access level lists must be empty.

    Values:
      PERIMETER_TYPE_REGULAR: Regular Perimeter. When no value is specified,
        the perimeter uses this type.
      PERIMETER_TYPE_BRIDGE: Perimeter Bridge.
    """
    PERIMETER_TYPE_REGULAR = 0
    PERIMETER_TYPE_BRIDGE = 1

  description = _messages.StringField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3)
  perimeterType = _messages.EnumField('PerimeterTypeValueValuesEnum', 4)
  spec = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfig', 5)
  status = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfig', 6)
  title = _messages.StringField(7)
  useExplicitDryRunSpec = _messages.BooleanField(8)
  weakenedForTesting = _messages.BooleanField(9)


class GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfig(_messages.Message):
  r"""`ServicePerimeterConfig` specifies a set of Google Cloud resources that
  describe specific Service Perimeter configuration.

  Fields:
    accessLevels: A list of `AccessLevel` resource names that allow resources
      within the `ServicePerimeter` to be accessed from the internet.
      `AccessLevels` listed must be in the same policy as this
      `ServicePerimeter`. Referencing a nonexistent `AccessLevel` is a syntax
      error. If no `AccessLevel` names are listed, resources within the
      perimeter can only be accessed via Google Cloud calls with request
      origins within the perimeter. Example:
      `"accessPolicies/MY_POLICY/accessLevels/MY_LEVEL"`. For Service
      Perimeter Bridge, must be empty.
    egressPolicies: List of EgressPolicies to apply to the perimeter. A
      perimeter may have multiple EgressPolicies, each of which is evaluated
      separately. Access is granted if any EgressPolicy grants it. Must be
      empty for a perimeter bridge.
    ingressPolicies: List of IngressPolicies to apply to the perimeter. A
      perimeter may have multiple IngressPolicies, each of which is evaluated
      separately. Access is granted if any Ingress Policy grants it. Must be
      empty for a perimeter bridge.
    resources: A list of Google Cloud resources that are inside of the service
      perimeter. Currently only projects and VPCs are allowed. Project format:
      `projects/{project_number}` VPC network format:
      `//compute.googleapis.com/projects/{PROJECT_ID}/global/networks/{NAME}`.
    restrictedServices: Google Cloud services that are subject to the Service
      Perimeter restrictions. For example, if `storage.googleapis.com` is
      specified, access to the storage buckets inside the perimeter must meet
      the perimeter's access restrictions.
    vpcAccessibleServices: Configuration for APIs allowed within Perimeter.
  """

  accessLevels = _messages.StringField(1, repeated=True)
  egressPolicies = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigEgressPolicy', 2, repeated=True)
  ingressPolicies = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigIngressPolicy', 3, repeated=True)
  resources = _messages.StringField(4, repeated=True)
  restrictedServices = _messages.StringField(5, repeated=True)
  vpcAccessibleServices = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigVpcAccessibleServices', 6)


class GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigApiOperation(_messages.Message):
  r"""Identification for an API Operation.

  Fields:
    methodSelectors: API methods or permissions to allow. Method or permission
      must belong to the service specified by `service_name` field. A single
      MethodSelector entry with `*` specified for the `method` field will
      allow all methods AND permissions for the service specified in
      `service_name`.
    serviceName: The name of the API whose methods or permissions the
      IngressPolicy or EgressPolicy want to allow. A single ApiOperation with
      `service_name` field set to `*` will allow all methods AND permissions
      for all services.
  """

  methodSelectors = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigMethodSelector', 1, repeated=True)
  serviceName = _messages.StringField(2)


class GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigEgressFrom(_messages.Message):
  r"""Defines the conditions under which an EgressPolicy matches a request.
  Conditions based on information about the source of the request. Note that
  if the destination of the request is also protected by a ServicePerimeter,
  then that ServicePerimeter must have an IngressPolicy which allows access in
  order for this request to succeed.

  Enums:
    IdentityTypeValueValuesEnum: Specifies the type of identities that are
      allowed access to outside the perimeter. If left unspecified, then
      members of `identities` field will be allowed access.
    SourceRestrictionValueValuesEnum: Whether to enforce traffic restrictions
      based on `sources` field. If the `sources` fields is non-empty, then
      this field must be set to `SOURCE_RESTRICTION_ENABLED`.

  Fields:
    identities: A list of identities that are allowed access through
      [EgressPolicy]. Identities can be an individual user, service account,
      Google group, or third-party identity. For third-party identity, only
      single identities are supported and other identity types are not
      supported. The `v1` identities that have the prefix `user`, `group`,
      `serviceAccount`, and `principal` in
      https://cloud.google.com/iam/docs/principal-identifiers#v1 are
      supported.
    identityType: Specifies the type of identities that are allowed access to
      outside the perimeter. If left unspecified, then members of `identities`
      field will be allowed access.
    sourceRestriction: Whether to enforce traffic restrictions based on
      `sources` field. If the `sources` fields is non-empty, then this field
      must be set to `SOURCE_RESTRICTION_ENABLED`.
    sources: Sources that this EgressPolicy authorizes access from. If this
      field is not empty, then `source_restriction` must be set to
      `SOURCE_RESTRICTION_ENABLED`.
  """

  class IdentityTypeValueValuesEnum(_messages.Enum):
    r"""Specifies the type of identities that are allowed access to outside
    the perimeter. If left unspecified, then members of `identities` field
    will be allowed access.

    Values:
      IDENTITY_TYPE_UNSPECIFIED: No blanket identity group specified.
      ANY_IDENTITY: Authorize access from all identities outside the
        perimeter.
      ANY_USER_ACCOUNT: Authorize access from all human users outside the
        perimeter.
      ANY_SERVICE_ACCOUNT: Authorize access from all service accounts outside
        the perimeter.
    """
    IDENTITY_TYPE_UNSPECIFIED = 0
    ANY_IDENTITY = 1
    ANY_USER_ACCOUNT = 2
    ANY_SERVICE_ACCOUNT = 3

  class SourceRestrictionValueValuesEnum(_messages.Enum):
    r"""Whether to enforce traffic restrictions based on `sources` field. If
    the `sources` fields is non-empty, then this field must be set to
    `SOURCE_RESTRICTION_ENABLED`.

    Values:
      SOURCE_RESTRICTION_UNSPECIFIED: Enforcement preference unspecified, will
        not enforce traffic restrictions based on `sources` in EgressFrom.
      SOURCE_RESTRICTION_ENABLED: Enforcement preference enabled, traffic
        restrictions will be enforced based on `sources` in EgressFrom.
      SOURCE_RESTRICTION_DISABLED: Enforcement preference disabled, will not
        enforce traffic restrictions based on `sources` in EgressFrom.
    """
    SOURCE_RESTRICTION_UNSPECIFIED = 0
    SOURCE_RESTRICTION_ENABLED = 1
    SOURCE_RESTRICTION_DISABLED = 2

  identities = _messages.StringField(1, repeated=True)
  identityType = _messages.EnumField('IdentityTypeValueValuesEnum', 2)
  sourceRestriction = _messages.EnumField('SourceRestrictionValueValuesEnum', 3)
  sources = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigEgressSource', 4, repeated=True)


class GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigEgressPolicy(_messages.Message):
  r"""Policy for egress from perimeter. EgressPolicies match requests based on
  `egress_from` and `egress_to` stanzas. For an EgressPolicy to match, both
  `egress_from` and `egress_to` stanzas must be matched. If an EgressPolicy
  matches a request, the request is allowed to span the ServicePerimeter
  boundary. For example, an EgressPolicy can be used to allow VMs on networks
  within the ServicePerimeter to access a defined set of projects outside the
  perimeter in certain contexts (e.g. to read data from a Cloud Storage bucket
  or query against a BigQuery dataset). EgressPolicies are concerned with the
  *resources* that a request relates as well as the API services and API
  actions being used. They do not related to the direction of data movement.
  More detailed documentation for this concept can be found in the
  descriptions of EgressFrom and EgressTo.

  Fields:
    egressFrom: Defines conditions on the source of a request causing this
      EgressPolicy to apply.
    egressTo: Defines the conditions on the ApiOperation and destination
      resources that cause this EgressPolicy to apply.
    title: Optional. Human-readable title for the egress rule. The title must
      be unique within the perimeter and can not exceed 100 characters. Within
      the access policy, the combined length of all rule titles must not
      exceed 240,000 characters.
  """

  egressFrom = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigEgressFrom', 1)
  egressTo = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigEgressTo', 2)
  title = _messages.StringField(3)


class GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigEgressSource(_messages.Message):
  r"""The source that EgressPolicy authorizes access from inside the
  ServicePerimeter to somewhere outside the ServicePerimeter boundaries.

  Fields:
    accessLevel: An AccessLevel resource name that allows protected resources
      inside the ServicePerimeters to access outside the ServicePerimeter
      boundaries. AccessLevels listed must be in the same policy as this
      ServicePerimeter. Referencing a nonexistent AccessLevel will cause an
      error. If an AccessLevel name is not specified, only resources within
      the perimeter can be accessed through Google Cloud calls with request
      origins within the perimeter. Example:
      `accessPolicies/MY_POLICY/accessLevels/MY_LEVEL`. If a single `*` is
      specified for `access_level`, then all EgressSources will be allowed.
    resource: A Google Cloud resource from the service perimeter that you want
      to allow to access data outside the perimeter. This field supports only
      projects. The project format is `projects/{project_number}`. You can't
      use `*` in this field to allow all Google Cloud resources.
  """

  accessLevel = _messages.StringField(1)
  resource = _messages.StringField(2)


class GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigEgressTo(_messages.Message):
  r"""Defines the conditions under which an EgressPolicy matches a request.
  Conditions are based on information about the ApiOperation intended to be
  performed on the `resources` specified. Note that if the destination of the
  request is also protected by a ServicePerimeter, then that ServicePerimeter
  must have an IngressPolicy which allows access in order for this request to
  succeed. The request must match `operations` AND `resources` fields in order
  to be allowed egress out of the perimeter.

  Fields:
    externalResources: A list of external resources that are allowed to be
      accessed. Only AWS and Azure resources are supported. For Amazon S3, the
      supported formats are s3://BUCKET_NAME, s3a://BUCKET_NAME, and
      s3n://BUCKET_NAME. For Azure Storage, the supported format is
      azure://myaccount.blob.core.windows.net/CONTAINER_NAME. A request
      matches if it contains an external resource in this list (Example:
      s3://bucket/path). Currently '*' is not allowed.
    operations: A list of ApiOperations allowed to be performed by the sources
      specified in the corresponding EgressFrom. A request matches if it uses
      an operation/service in this list.
    resources: A list of resources, currently only projects in the form
      `projects/`, that are allowed to be accessed by sources defined in the
      corresponding EgressFrom. A request matches if it contains a resource in
      this list. If `*` is specified for `resources`, then this EgressTo rule
      will authorize access to all resources outside the perimeter.
    roles: IAM roles that represent the set of operations that the sources
      specified in the corresponding EgressFrom. are allowed to perform in
      this ServicePerimeter.
  """

  externalResources = _messages.StringField(1, repeated=True)
  operations = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigApiOperation', 2, repeated=True)
  resources = _messages.StringField(3, repeated=True)
  roles = _messages.StringField(4, repeated=True)


class GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigIngressFrom(_messages.Message):
  r"""Defines the conditions under which an IngressPolicy matches a request.
  Conditions are based on information about the source of the request. The
  request must satisfy what is defined in `sources` AND identity related
  fields in order to match.

  Enums:
    IdentityTypeValueValuesEnum: Specifies the type of identities that are
      allowed access from outside the perimeter. If left unspecified, then
      members of `identities` field will be allowed access.

  Fields:
    identities: A list of identities that are allowed access through
      [IngressPolicy]. Identities can be an individual user, service account,
      Google group, or third-party identity. For third-party identity, only
      single identities are supported and other identity types are not
      supported. The `v1` identities that have the prefix `user`, `group`,
      `serviceAccount`, and `principal` in
      https://cloud.google.com/iam/docs/principal-identifiers#v1 are
      supported.
    identityType: Specifies the type of identities that are allowed access
      from outside the perimeter. If left unspecified, then members of
      `identities` field will be allowed access.
    sources: Sources that this IngressPolicy authorizes access from.
  """

  class IdentityTypeValueValuesEnum(_messages.Enum):
    r"""Specifies the type of identities that are allowed access from outside
    the perimeter. If left unspecified, then members of `identities` field
    will be allowed access.

    Values:
      IDENTITY_TYPE_UNSPECIFIED: No blanket identity group specified.
      ANY_IDENTITY: Authorize access from all identities outside the
        perimeter.
      ANY_USER_ACCOUNT: Authorize access from all human users outside the
        perimeter.
      ANY_SERVICE_ACCOUNT: Authorize access from all service accounts outside
        the perimeter.
    """
    IDENTITY_TYPE_UNSPECIFIED = 0
    ANY_IDENTITY = 1
    ANY_USER_ACCOUNT = 2
    ANY_SERVICE_ACCOUNT = 3

  identities = _messages.StringField(1, repeated=True)
  identityType = _messages.EnumField('IdentityTypeValueValuesEnum', 2)
  sources = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigIngressSource', 3, repeated=True)


class GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigIngressPolicy(_messages.Message):
  r"""Policy for ingress into ServicePerimeter. IngressPolicies match requests
  based on `ingress_from` and `ingress_to` stanzas. For an ingress policy to
  match, both the `ingress_from` and `ingress_to` stanzas must be matched. If
  an IngressPolicy matches a request, the request is allowed through the
  perimeter boundary from outside the perimeter. For example, access from the
  internet can be allowed either based on an AccessLevel or, for traffic
  hosted on Google Cloud, the project of the source network. For access from
  private networks, using the project of the hosting network is required.
  Individual ingress policies can be limited by restricting which services
  and/or actions they match using the `ingress_to` field.

  Fields:
    ingressFrom: Defines the conditions on the source of a request causing
      this IngressPolicy to apply.
    ingressTo: Defines the conditions on the ApiOperation and request
      destination that cause this IngressPolicy to apply.
    title: Optional. Human-readable title for the ingress rule. The title must
      be unique within the perimeter and can not exceed 100 characters. Within
      the access policy, the combined length of all rule titles must not
      exceed 240,000 characters.
  """

  ingressFrom = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigIngressFrom', 1)
  ingressTo = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigIngressTo', 2)
  title = _messages.StringField(3)


class GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigIngressSource(_messages.Message):
  r"""The source that IngressPolicy authorizes access from.

  Fields:
    accessLevel: An AccessLevel resource name that allow resources within the
      ServicePerimeters to be accessed from the internet. AccessLevels listed
      must be in the same policy as this ServicePerimeter. Referencing a
      nonexistent AccessLevel will cause an error. If no AccessLevel names are
      listed, resources within the perimeter can only be accessed via Google
      Cloud calls with request origins within the perimeter. Example:
      `accessPolicies/MY_POLICY/accessLevels/MY_LEVEL`. If a single `*` is
      specified for `access_level`, then all IngressSources will be allowed.
    resource: A Google Cloud resource that is allowed to ingress the
      perimeter. Requests from these resources will be allowed to access
      perimeter data. Currently only projects and VPCs are allowed. Project
      format: `projects/{project_number}` VPC network format:
      `//compute.googleapis.com/projects/{PROJECT_ID}/global/networks/{NAME}`.
      The project may be in any Google Cloud organization, not just the
      organization that the perimeter is defined in. `*` is not allowed, the
      case of allowing all Google Cloud resources only is not supported.
  """

  accessLevel = _messages.StringField(1)
  resource = _messages.StringField(2)


class GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigIngressTo(_messages.Message):
  r"""Defines the conditions under which an IngressPolicy matches a request.
  Conditions are based on information about the ApiOperation intended to be
  performed on the target resource of the request. The request must satisfy
  what is defined in `operations` AND `resources` in order to match.

  Fields:
    operations: A list of ApiOperations allowed to be performed by the sources
      specified in corresponding IngressFrom in this ServicePerimeter.
    resources: A list of resources, currently only projects in the form
      `projects/`, protected by this ServicePerimeter that are allowed to be
      accessed by sources defined in the corresponding IngressFrom. If a
      single `*` is specified, then access to all resources inside the
      perimeter are allowed.
    roles: IAM roles that represent the set of operations that the sources
      specified in the corresponding IngressFrom are allowed to perform in
      this ServicePerimeter.
  """

  operations = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigApiOperation', 1, repeated=True)
  resources = _messages.StringField(2, repeated=True)
  roles = _messages.StringField(3, repeated=True)


class GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigMethodSelector(_messages.Message):
  r"""An allowed method or permission of a service specified in ApiOperation.

  Fields:
    method: A valid method name for the corresponding `service_name` in
      ApiOperation. If `*` is used as the value for the `method`, then ALL
      methods and permissions are allowed.
    permission: A valid Cloud IAM permission for the corresponding
      `service_name` in ApiOperation.
  """

  method = _messages.StringField(1)
  permission = _messages.StringField(2)


class GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigVpcAccessibleServices(_messages.Message):
  r"""Specifies how APIs are allowed to communicate within the Service
  Perimeter.

  Fields:
    allowedServices: The list of APIs usable within the Service Perimeter.
      Must be empty unless 'enable_restriction' is True. You can specify a
      list of individual services, as well as include the 'RESTRICTED-
      SERVICES' value, which automatically includes all of the services
      protected by the perimeter.
    enableRestriction: Whether to restrict API calls within the Service
      Perimeter to the list of APIs specified in 'allowed_services'.
  """

  allowedServices = _messages.StringField(1, repeated=True)
  enableRestriction = _messages.BooleanField(2)


class GoogleIdentityAccesscontextmanagerV1SupportedService(_messages.Message):
  r"""`SupportedService` specifies the VPC Service Controls and its
  properties.

  Enums:
    ServiceSupportStageValueValuesEnum: The support stage of the service.
    SupportStageValueValuesEnum: The support stage of the service.

  Fields:
    availableOnRestrictedVip: True if the service is available on the
      restricted VIP. Services on the restricted VIP typically either support
      VPC Service Controls or are core infrastructure services required for
      the functioning of Google Cloud.
    knownLimitations: True if the service is supported with some limitations.
      Check [documentation](https://cloud.google.com/vpc-service-
      controls/docs/supported-products) for details.
    name: The service name or address of the supported service, such as
      `service.googleapis.com`.
    serviceSupportStage: The support stage of the service.
    supportStage: The support stage of the service.
    supportedMethods: The list of the supported methods. This field exists
      only in response to GetSupportedService
    title: The name of the supported product, such as 'Cloud Product API'.
  """

  class ServiceSupportStageValueValuesEnum(_messages.Enum):
    r"""The support stage of the service.

    Values:
      SERVICE_SUPPORT_STAGE_UNSPECIFIED: Do not use this default value.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      PREVIEW: PREVIEW indicates a pre-release stage where the product is
        functionally complete but undergoing real-world testing.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed.
    """
    SERVICE_SUPPORT_STAGE_UNSPECIFIED = 0
    GA = 1
    PREVIEW = 2
    DEPRECATED = 3

  class SupportStageValueValuesEnum(_messages.Enum):
    r"""The support stage of the service.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  availableOnRestrictedVip = _messages.BooleanField(1)
  knownLimitations = _messages.BooleanField(2)
  name = _messages.StringField(3)
  serviceSupportStage = _messages.EnumField('ServiceSupportStageValueValuesEnum', 4)
  supportStage = _messages.EnumField('SupportStageValueValuesEnum', 5)
  supportedMethods = _messages.MessageField('GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfigMethodSelector', 6, repeated=True)
  title = _messages.StringField(7)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeExpr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class IdentityCaaIntelFrontendAccessLevelExplanation(_messages.Message):
  r"""Explanation of access level, including the original access level defined
  by customers, evaluation results and metadata NextTAG: 12

  Enums:
    AccessLevelStateValueValuesEnum: Evaluation state of an access level

  Messages:
    NodeMapValue: Map between node.id and cel node Node id: Expr.id
      (google/api/expr/syntax.proto)
    NodeNegTroubleshootingMetadataMapValue: Map between node.id and
      troubleshooting metadata of this node when the state of this access
      level is expected to be not_granted
    NodePosTroubleshootingMetadataMapValue: Map between node.id and
      troubleshooting metadata of this node when the state of this access
      level is expected to be granted

  Fields:
    accessLevelState: Evaluation state of an access level
    basicLevelExplanation: A IdentityCaaIntelFrontendBasicLevelExplanation
      attribute.
    customLevelExplanation: A IdentityCaaIntelFrontendCustomLevelExplanation
      attribute.
    name: Resource name for the Access Level. Format:
      `accessPolicies/{policy_id}/accessLevels/{short_name}`
    nodeMap: Map between node.id and cel node Node id: Expr.id
      (google/api/expr/syntax.proto)
    nodeNegTroubleshootingMetadataMap: Map between node.id and troubleshooting
      metadata of this node when the state of this access level is expected to
      be not_granted
    nodePosTroubleshootingMetadataMap: Map between node.id and troubleshooting
      metadata of this node when the state of this access level is expected to
      be granted
    title: Access Level's title
  """

  class AccessLevelStateValueValuesEnum(_messages.Enum):
    r"""Evaluation state of an access level

    Values:
      ACCESS_LEVEL_STATE_UNSPECIFIED: Reserved
      ACCESS_LEVEL_STATE_GRANTED: The access level state is granted
      ACCESS_LEVEL_STATE_NOT_GRANTED: The access level state is not granted
      ACCESS_LEVEL_STATE_ERROR: Encounter error when evaluating this access
        level. Note that such error is on the critical path that blocks the
        evaluation; e.g. False || -> ACCESS_LEVEL_STATE_NOT_GRANTED True && ->
        ACCESS_LEVEL_STATE_ERROR
      ACCESS_LEVEL_NOT_EXIST: The access level doesn't exist
    """
    ACCESS_LEVEL_STATE_UNSPECIFIED = 0
    ACCESS_LEVEL_STATE_GRANTED = 1
    ACCESS_LEVEL_STATE_NOT_GRANTED = 2
    ACCESS_LEVEL_STATE_ERROR = 3
    ACCESS_LEVEL_NOT_EXIST = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NodeMapValue(_messages.Message):
    r"""Map between node.id and cel node Node id: Expr.id
    (google/api/expr/syntax.proto)

    Messages:
      AdditionalProperty: An additional property for a NodeMapValue object.

    Fields:
      additionalProperties: Additional properties of type NodeMapValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NodeMapValue object.

      Fields:
        key: Name of the additional property.
        value: A IdentityCaaIntelFrontendCelNode attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('IdentityCaaIntelFrontendCelNode', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NodeNegTroubleshootingMetadataMapValue(_messages.Message):
    r"""Map between node.id and troubleshooting metadata of this node when the
    state of this access level is expected to be not_granted

    Messages:
      AdditionalProperty: An additional property for a
        NodeNegTroubleshootingMetadataMapValue object.

    Fields:
      additionalProperties: Additional properties of type
        NodeNegTroubleshootingMetadataMapValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NodeNegTroubleshootingMetadataMapValue
      object.

      Fields:
        key: Name of the additional property.
        value: A IdentityCaaIntelFrontendTroubleshootingMetadata attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('IdentityCaaIntelFrontendTroubleshootingMetadata', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NodePosTroubleshootingMetadataMapValue(_messages.Message):
    r"""Map between node.id and troubleshooting metadata of this node when the
    state of this access level is expected to be granted

    Messages:
      AdditionalProperty: An additional property for a
        NodePosTroubleshootingMetadataMapValue object.

    Fields:
      additionalProperties: Additional properties of type
        NodePosTroubleshootingMetadataMapValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NodePosTroubleshootingMetadataMapValue
      object.

      Fields:
        key: Name of the additional property.
        value: A IdentityCaaIntelFrontendTroubleshootingMetadata attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('IdentityCaaIntelFrontendTroubleshootingMetadata', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accessLevelState = _messages.EnumField('AccessLevelStateValueValuesEnum', 1)
  basicLevelExplanation = _messages.MessageField('IdentityCaaIntelFrontendBasicLevelExplanation', 2)
  customLevelExplanation = _messages.MessageField('IdentityCaaIntelFrontendCustomLevelExplanation', 3)
  name = _messages.StringField(4)
  nodeMap = _messages.MessageField('NodeMapValue', 5)
  nodeNegTroubleshootingMetadataMap = _messages.MessageField('NodeNegTroubleshootingMetadataMapValue', 6)
  nodePosTroubleshootingMetadataMap = _messages.MessageField('NodePosTroubleshootingMetadataMapValue', 7)
  title = _messages.StringField(8)


class IdentityCaaIntelFrontendBasicLevelExplanation(_messages.Message):
  r"""The Explanation of a Basic Level, which contains the explanation in
  Struct NextTAG: 2

  Messages:
    ExplanationValue: A ExplanationValue object.

  Fields:
    explanation: A ExplanationValue attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExplanationValue(_messages.Message):
    r"""A ExplanationValue object.

    Messages:
      AdditionalProperty: An additional property for a ExplanationValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExplanationValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  explanation = _messages.MessageField('ExplanationValue', 1)


class IdentityCaaIntelFrontendCelNode(_messages.Message):
  r"""Cel node, including evaluation results and metadata NextTAG: 7

  Fields:
    endPosition: Optional, it exists if it is CustomLevel Access Level. End
      position of an expression in the original condition, by character, end
      included
    nodeValues: Repeated as one node id may correspond to multiple evaluation
      values. e.g.in comprehension expr, [1,2,3].all(x, x > 0), call_expr
      "_>_" has 3 values corresponding to the evaluation of list values
      individually sequentially
    startPosition: Optional, it exists if it is CustomLevel Access Level.
      Start position of an expression in the original condition, by character
  """

  endPosition = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  nodeValues = _messages.MessageField('IdentityCaaIntelFrontendNodeValue', 2, repeated=True)
  startPosition = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class IdentityCaaIntelFrontendCustomLevelExplanation(_messages.Message):
  r"""The Explanation of a Custom Level, which contains the original cel
  expression and the custom level explanation tree NextTAG: 3

  Fields:
    explanation: Custom Level Explanation Tree
    expression: The raw cel expression from customers
  """

  explanation = _messages.MessageField('IdentityCaaIntelFrontendCustomLevelNode', 1)
  expression = _messages.StringField(2)


class IdentityCaaIntelFrontendCustomLevelNode(_messages.Message):
  r"""Custom Level Node Tree for the Logical Expression Tree NextTAG: 4

  Enums:
    NodeTypeValueValuesEnum: Node type, indicate if it's an expression or
      AND/OR/NOT Logical Operator Node

  Fields:
    nodeId: Node id, used to map to its NodeValue and Troubleshooting metadata
    nodeType: Node type, indicate if it's an expression or AND/OR/NOT Logical
      Operator Node
    nodes: Child nodes
  """

  class NodeTypeValueValuesEnum(_messages.Enum):
    r"""Node type, indicate if it's an expression or AND/OR/NOT Logical
    Operator Node

    Values:
      CUSTOM_LEVEL_NODE_UNSPECIFIED: Reserved
      CUSTOM_LEVEL_NODE_EXPRESSION: Custom level Expression node
      CUSTOM_LEVEL_NODE_AND: Custom level AND node
      CUSTOM_LEVEL_NODE_OR: Custom level OR node
      CUSTOM_LEVEL_NODE_NOT: Custom level NOT node
    """
    CUSTOM_LEVEL_NODE_UNSPECIFIED = 0
    CUSTOM_LEVEL_NODE_EXPRESSION = 1
    CUSTOM_LEVEL_NODE_AND = 2
    CUSTOM_LEVEL_NODE_OR = 3
    CUSTOM_LEVEL_NODE_NOT = 4

  nodeId = _messages.IntegerField(1)
  nodeType = _messages.EnumField('NodeTypeValueValuesEnum', 2)
  nodes = _messages.MessageField('IdentityCaaIntelFrontendCustomLevelNode', 3, repeated=True)


class IdentityCaaIntelFrontendNodeValue(_messages.Message):
  r"""Evaluation result of a cel AST node NextTAG: 7

  Enums:
    CriticalNodeErrorsValueListEntryValuesEnum:
    NodeStateValueValuesEnum: Evaluation state of this node
    NonCriticalNodeErrorsValueListEntryValuesEnum:

  Fields:
    criticalNodeErrors: The errors included depend on the context. It is
      applicable when node_state is NODE_STATE_ERROR
    nodeState: Evaluation state of this node
    nonCriticalNodeErrors: The errors included depend on the context. Note:
      ACCESS_LEVEL_STATE_GRANTED/ACCESS_LEVEL_STATE_NOT_GRANTED access levels
      may have non_critical_node_errors errors underneath that don't block the
      evaluation.
    value: Evaluation result of this node, It is applicable when node_state is
      NODE_STATE_NORMAL.
  """

  class CriticalNodeErrorsValueListEntryValuesEnum(_messages.Enum):
    r"""CriticalNodeErrorsValueListEntryValuesEnum enum type.

    Values:
      NODE_ERROR_UNSPECIFIED: Reserved
      NODE_ERROR_INTERNAL_ERROR: Internal error If there is no matching error
        below, use it by default
      NODE_ERROR_DEVICE_NOT_FOUND: Device not found
      NODE_ERROR_DEVICE_STALE: Device is out of sync
      NODE_ERROR_DEVICE_CROSS_ORG: It is a cross-org device
      NODE_ERROR_DEVICE_INFO_NOT_AUTHORIZED: Caller doesn't have permission to
        device info
      NODE_ERROR_INVALID_SOURCE_IP: Source ip is not valid, from inIpRange
        function
      NODE_ERROR_INVALID_IP_SUBNETS: Ip subnets are not valid, from inIpRange
        function
      NODE_ERROR_INVALID_DEVICE_VERSION: Device min verion is not valid, from
        versionAtLeast function
      NODE_ERROR_NO_MATCHING_OVERLOADED_FUNC: Expr error from a supported
        function type with invalid parameters e.g. 1 == true
      NODE_ERROR_AUTH_SESSION_INFO_NOT_AUTHORIZED: Caller doesn't have
        permission to auth session info
      NODE_ERROR_NO_BCE_LICENSE: User is not assigned a BCE license.
      NODE_ERROR_INVALID_NETWORK: The network in the list for `in_vpc_network`
        is not valid.
      NODE_ERROR_UNRECOGNIZED_NETWORK: The network in the request for
        `in_vpc_network` is missing.
      NODE_ERROR_UNMATCHED_NETWORK_PROJECT: The network project in the request
        for `in_vpc_network` does not match with the project in policy.
      NODE_ERROR_UNRECOGNIZED_NETWORK_PROJECT: The network project in the
        request for `in_vpc_network` is missing. TODO(b/382592764) Add support
        for NODE_ERROR_UNKNOWN_REGION
    """
    NODE_ERROR_UNSPECIFIED = 0
    NODE_ERROR_INTERNAL_ERROR = 1
    NODE_ERROR_DEVICE_NOT_FOUND = 2
    NODE_ERROR_DEVICE_STALE = 3
    NODE_ERROR_DEVICE_CROSS_ORG = 4
    NODE_ERROR_DEVICE_INFO_NOT_AUTHORIZED = 5
    NODE_ERROR_INVALID_SOURCE_IP = 6
    NODE_ERROR_INVALID_IP_SUBNETS = 7
    NODE_ERROR_INVALID_DEVICE_VERSION = 8
    NODE_ERROR_NO_MATCHING_OVERLOADED_FUNC = 9
    NODE_ERROR_AUTH_SESSION_INFO_NOT_AUTHORIZED = 10
    NODE_ERROR_NO_BCE_LICENSE = 11
    NODE_ERROR_INVALID_NETWORK = 12
    NODE_ERROR_UNRECOGNIZED_NETWORK = 13
    NODE_ERROR_UNMATCHED_NETWORK_PROJECT = 14
    NODE_ERROR_UNRECOGNIZED_NETWORK_PROJECT = 15

  class NodeStateValueValuesEnum(_messages.Enum):
    r"""Evaluation state of this node

    Values:
      NODE_STATE_UNSPECIFIED: Reserved
      NODE_STATE_NORMAL: The node state is normal, which means the evaluation
        of this node succeeds However, it doesn't mean the evaluated result is
        a boolean value.
      NODE_STATE_ERROR: Encounter error when evaluating the result of this
        node. Only return error if it is in the critical path of evaluation.
        For example, `( || true) && ` -> ``, ` || true` -> `true` `.foo` -> ``
        `foo()` -> `` ` + 1` -> ``
    """
    NODE_STATE_UNSPECIFIED = 0
    NODE_STATE_NORMAL = 1
    NODE_STATE_ERROR = 2

  class NonCriticalNodeErrorsValueListEntryValuesEnum(_messages.Enum):
    r"""NonCriticalNodeErrorsValueListEntryValuesEnum enum type.

    Values:
      NODE_ERROR_UNSPECIFIED: Reserved
      NODE_ERROR_INTERNAL_ERROR: Internal error If there is no matching error
        below, use it by default
      NODE_ERROR_DEVICE_NOT_FOUND: Device not found
      NODE_ERROR_DEVICE_STALE: Device is out of sync
      NODE_ERROR_DEVICE_CROSS_ORG: It is a cross-org device
      NODE_ERROR_DEVICE_INFO_NOT_AUTHORIZED: Caller doesn't have permission to
        device info
      NODE_ERROR_INVALID_SOURCE_IP: Source ip is not valid, from inIpRange
        function
      NODE_ERROR_INVALID_IP_SUBNETS: Ip subnets are not valid, from inIpRange
        function
      NODE_ERROR_INVALID_DEVICE_VERSION: Device min verion is not valid, from
        versionAtLeast function
      NODE_ERROR_NO_MATCHING_OVERLOADED_FUNC: Expr error from a supported
        function type with invalid parameters e.g. 1 == true
      NODE_ERROR_AUTH_SESSION_INFO_NOT_AUTHORIZED: Caller doesn't have
        permission to auth session info
      NODE_ERROR_NO_BCE_LICENSE: User is not assigned a BCE license.
      NODE_ERROR_INVALID_NETWORK: The network in the list for `in_vpc_network`
        is not valid.
      NODE_ERROR_UNRECOGNIZED_NETWORK: The network in the request for
        `in_vpc_network` is missing.
      NODE_ERROR_UNMATCHED_NETWORK_PROJECT: The network project in the request
        for `in_vpc_network` does not match with the project in policy.
      NODE_ERROR_UNRECOGNIZED_NETWORK_PROJECT: The network project in the
        request for `in_vpc_network` is missing. TODO(b/382592764) Add support
        for NODE_ERROR_UNKNOWN_REGION
    """
    NODE_ERROR_UNSPECIFIED = 0
    NODE_ERROR_INTERNAL_ERROR = 1
    NODE_ERROR_DEVICE_NOT_FOUND = 2
    NODE_ERROR_DEVICE_STALE = 3
    NODE_ERROR_DEVICE_CROSS_ORG = 4
    NODE_ERROR_DEVICE_INFO_NOT_AUTHORIZED = 5
    NODE_ERROR_INVALID_SOURCE_IP = 6
    NODE_ERROR_INVALID_IP_SUBNETS = 7
    NODE_ERROR_INVALID_DEVICE_VERSION = 8
    NODE_ERROR_NO_MATCHING_OVERLOADED_FUNC = 9
    NODE_ERROR_AUTH_SESSION_INFO_NOT_AUTHORIZED = 10
    NODE_ERROR_NO_BCE_LICENSE = 11
    NODE_ERROR_INVALID_NETWORK = 12
    NODE_ERROR_UNRECOGNIZED_NETWORK = 13
    NODE_ERROR_UNMATCHED_NETWORK_PROJECT = 14
    NODE_ERROR_UNRECOGNIZED_NETWORK_PROJECT = 15

  criticalNodeErrors = _messages.EnumField('CriticalNodeErrorsValueListEntryValuesEnum', 1, repeated=True)
  nodeState = _messages.EnumField('NodeStateValueValuesEnum', 2)
  nonCriticalNodeErrors = _messages.EnumField('NonCriticalNodeErrorsValueListEntryValuesEnum', 3, repeated=True)
  value = _messages.MessageField('GoogleApiExprValue', 4)


class IdentityCaaIntelFrontendTroubleshootingMetadata(_messages.Message):
  r"""NextTAG: 3

  Enums:
    CriticalLevelValueValuesEnum: If it is a critical failed node that blocks
      the expected state of this access level. It is valid only for boolean
      expression nodes and when the node's expected value doesn't equal to
      actual value
    LogicalNodeExpectedValueValueValuesEnum: If it is a logical node, it will
      be TRUE or FALSE.

  Fields:
    criticalLevel: If it is a critical failed node that blocks the expected
      state of this access level. It is valid only for boolean expression
      nodes and when the node's expected value doesn't equal to actual value
    logicalNodeExpectedValue: If it is a logical node, it will be TRUE or
      FALSE.
  """

  class CriticalLevelValueValuesEnum(_messages.Enum):
    r"""If it is a critical failed node that blocks the expected state of this
    access level. It is valid only for boolean expression nodes and when the
    node's expected value doesn't equal to actual value

    Values:
      CRITICAL_LEVEL_UNSPECIFIED: reserved
      CRITICAL_LEVEL_LOW: The node is not on the critical path to the expected
        state of this access level.
      CRITICAL_LEVEL_HIGH: The node is on the critical path to the expected
        state of this access level.
    """
    CRITICAL_LEVEL_UNSPECIFIED = 0
    CRITICAL_LEVEL_LOW = 1
    CRITICAL_LEVEL_HIGH = 2

  class LogicalNodeExpectedValueValueValuesEnum(_messages.Enum):
    r"""If it is a logical node, it will be TRUE or FALSE.

    Values:
      LOGICAL_NODE_EXPECTED_VALUE_UNSPECIFIED: Reserved
      LOGICAL_NODE_EXPECTED_VALUE_TRUE: True
      LOGICAL_NODE_EXPECTED_VALUE_FALSE: False
    """
    LOGICAL_NODE_EXPECTED_VALUE_UNSPECIFIED = 0
    LOGICAL_NODE_EXPECTED_VALUE_TRUE = 1
    LOGICAL_NODE_EXPECTED_VALUE_FALSE = 2

  criticalLevel = _messages.EnumField('CriticalLevelValueValuesEnum', 1)
  logicalNodeExpectedValue = _messages.EnumField('LogicalNodeExpectedValueValueValuesEnum', 2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    GoogleIamV1Rule, 'in_', 'in')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
