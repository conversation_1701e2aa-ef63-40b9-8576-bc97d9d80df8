"""Generated message classes for workflowexecutions version v1.

Execute workflows created with Workflows API.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'workflowexecutions'


class Callback(_messages.Message):
  r"""An instance of a Callback created by an execution.

  Fields:
    availablePayloads: Output only. The payloads received by the callback that
      have not been processed by a waiting execution step.
    method: Output only. The method accepted by the callback. For example:
      GET, POST, PUT.
    name: Output only. The resource name of the callback. Format: projects/{pr
      oject}/locations/{location}/workflows/{workflow}/executions/{execution}/
      callback/{callback}
    waiters: Output only. Number of execution steps waiting on this callback.
  """

  availablePayloads = _messages.StringField(1, repeated=True)
  method = _messages.StringField(2)
  name = _messages.StringField(3)
  waiters = _messages.IntegerField(4)


class CancelExecutionRequest(_messages.Message):
  r"""Request for the CancelExecution method."""


class DeleteExecutionHistoryRequest(_messages.Message):
  r"""Request for the DeleteExecutionHistory method."""


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Error(_messages.Message):
  r"""Error describes why the execution was abnormally terminated.

  Fields:
    context: Human-readable stack trace string.
    payload: Error message and data returned represented as a JSON string.
    stackTrace: Stack trace with detailed information of where error was
      generated.
  """

  context = _messages.StringField(1)
  payload = _messages.StringField(2)
  stackTrace = _messages.MessageField('StackTrace', 3)


class Exception(_messages.Message):
  r"""Exception describes why the step entry failed.

  Fields:
    payload: Error message represented as a JSON string.
  """

  payload = _messages.StringField(1)


class Execution(_messages.Message):
  r"""A running instance of a
  [Workflow](/workflows/docs/reference/rest/v1/projects.locations.workflows).

  Enums:
    CallLogLevelValueValuesEnum: The call logging level associated to this
      execution.
    ExecutionHistoryLevelValueValuesEnum: Optional. Describes the execution
      history level to apply to this execution. If not specified, the
      execution history level is determined by its workflow's execution
      history level. If the levels are different, the executionHistoryLevel
      overrides the workflow's execution history level for this execution.
    StateValueValuesEnum: Output only. Current state of the execution.

  Messages:
    LabelsValue: Labels associated with this execution. Labels can contain at
      most 64 entries. Keys and values can be no longer than 63 characters and
      can only contain lowercase letters, numeric characters, underscores, and
      dashes. Label keys must start with a letter. International characters
      are allowed. By default, labels are inherited from the workflow but are
      overridden by any labels associated with the execution.

  Fields:
    argument: Input parameters of the execution represented as a JSON string.
      The size limit is 32KB. *Note*: If you are using the REST API directly
      to run your workflow, you must escape any JSON string value of
      `argument`. Example:
      `'{"argument":"{\"firstName\":\"FIRST\",\"lastName\":\"LAST\"}"}'`
    callLogLevel: The call logging level associated to this execution.
    createTime: Output only. Marks the creation of the execution.
    disableConcurrencyQuotaOverflowBuffering: Optional. If set to true, the
      execution will not be backlogged when the concurrency quota is
      exhausted. The backlog execution starts when the concurrency quota
      becomes available.
    duration: Output only. Measures the duration of the execution.
    endTime: Output only. Marks the end of execution, successful or not.
    error: Output only. The error which caused the execution to finish
      prematurely. The value is only present if the execution's state is
      `FAILED` or `CANCELLED`.
    executionHistoryLevel: Optional. Describes the execution history level to
      apply to this execution. If not specified, the execution history level
      is determined by its workflow's execution history level. If the levels
      are different, the executionHistoryLevel overrides the workflow's
      execution history level for this execution.
    labels: Labels associated with this execution. Labels can contain at most
      64 entries. Keys and values can be no longer than 63 characters and can
      only contain lowercase letters, numeric characters, underscores, and
      dashes. Label keys must start with a letter. International characters
      are allowed. By default, labels are inherited from the workflow but are
      overridden by any labels associated with the execution.
    name: Output only. The resource name of the execution. Format: projects/{p
      roject}/locations/{location}/workflows/{workflow}/executions/{execution}
    result: Output only. Output of the execution represented as a JSON string.
      The value can only be present if the execution's state is `SUCCEEDED`.
    startTime: Output only. Marks the beginning of execution. Note that this
      will be the same as `createTime` for executions that start immediately.
    state: Output only. Current state of the execution.
    stateError: Output only. Error regarding the state of the Execution
      resource. For example, this field will have error details if the
      execution data is unavailable due to revoked KMS key permissions.
    status: Output only. Status tracks the current steps and progress data of
      this execution.
    workflowRevisionId: Output only. Revision of the workflow this execution
      is using.
  """

  class CallLogLevelValueValuesEnum(_messages.Enum):
    r"""The call logging level associated to this execution.

    Values:
      CALL_LOG_LEVEL_UNSPECIFIED: No call logging level specified.
      LOG_ALL_CALLS: Log all call steps within workflows, all call returns,
        and all exceptions raised.
      LOG_ERRORS_ONLY: Log only exceptions that are raised from call steps
        within workflows.
      LOG_NONE: Explicitly log nothing.
    """
    CALL_LOG_LEVEL_UNSPECIFIED = 0
    LOG_ALL_CALLS = 1
    LOG_ERRORS_ONLY = 2
    LOG_NONE = 3

  class ExecutionHistoryLevelValueValuesEnum(_messages.Enum):
    r"""Optional. Describes the execution history level to apply to this
    execution. If not specified, the execution history level is determined by
    its workflow's execution history level. If the levels are different, the
    executionHistoryLevel overrides the workflow's execution history level for
    this execution.

    Values:
      EXECUTION_HISTORY_LEVEL_UNSPECIFIED: The default/unset value.
      EXECUTION_HISTORY_BASIC: Enable execution history basic feature for this
        execution.
      EXECUTION_HISTORY_DETAILED: Enable execution history detailed feature
        for this execution.
    """
    EXECUTION_HISTORY_LEVEL_UNSPECIFIED = 0
    EXECUTION_HISTORY_BASIC = 1
    EXECUTION_HISTORY_DETAILED = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the execution.

    Values:
      STATE_UNSPECIFIED: Invalid state.
      ACTIVE: The execution is in progress.
      SUCCEEDED: The execution finished successfully.
      FAILED: The execution failed with an error.
      CANCELLED: The execution was stopped intentionally.
      UNAVAILABLE: Execution data is unavailable. See the `state_error` field.
      QUEUED: Request has been placed in the backlog for processing at a later
        time.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    SUCCEEDED = 2
    FAILED = 3
    CANCELLED = 4
    UNAVAILABLE = 5
    QUEUED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this execution. Labels can contain at most 64
    entries. Keys and values can be no longer than 63 characters and can only
    contain lowercase letters, numeric characters, underscores, and dashes.
    Label keys must start with a letter. International characters are allowed.
    By default, labels are inherited from the workflow but are overridden by
    any labels associated with the execution.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  argument = _messages.StringField(1)
  callLogLevel = _messages.EnumField('CallLogLevelValueValuesEnum', 2)
  createTime = _messages.StringField(3)
  disableConcurrencyQuotaOverflowBuffering = _messages.BooleanField(4)
  duration = _messages.StringField(5)
  endTime = _messages.StringField(6)
  error = _messages.MessageField('Error', 7)
  executionHistoryLevel = _messages.EnumField('ExecutionHistoryLevelValueValuesEnum', 8)
  labels = _messages.MessageField('LabelsValue', 9)
  name = _messages.StringField(10)
  result = _messages.StringField(11)
  startTime = _messages.StringField(12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  stateError = _messages.MessageField('StateError', 14)
  status = _messages.MessageField('Status', 15)
  workflowRevisionId = _messages.StringField(16)


class ExportDataResponse(_messages.Message):
  r"""Response for the ExportData method.

  Fields:
    data: The JSON string with customer data and metadata for an execution
      with the given name
  """

  data = _messages.StringField(1)


class ListCallbacksResponse(_messages.Message):
  r"""RPC response object for the ListCallbacks method.

  Fields:
    callbacks: The callbacks which match the request.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  callbacks = _messages.MessageField('Callback', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListExecutionsResponse(_messages.Message):
  r"""Response for the ListExecutions method.

  Fields:
    executions: The executions which match the request.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  executions = _messages.MessageField('Execution', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListStepEntriesResponse(_messages.Message):
  r"""Response message for ExecutionHistory.ListStepEntries.

  Fields:
    nextPageToken: A token to retrieve next page of results. Pass this value
      in the ListStepEntriesRequest.page_token field in the subsequent call to
      `ListStepEntries` method to retrieve the next page of results.
    stepEntries: The list of entries.
    totalSize: Indicates the total number of StepEntries that matched the
      request filter. For running executions, this number shows the number of
      StepEntries that are executed thus far.
  """

  nextPageToken = _messages.StringField(1)
  stepEntries = _messages.MessageField('StepEntry', 2, repeated=True)
  totalSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class NavigationInfo(_messages.Message):
  r"""NavigationInfo describes what steps if any come before or after this
  step, or what steps are parents or children of this step.

  Fields:
    children: Step entries that can be reached by "stepping into" e.g. a
      subworkflow call.
    next: The index of the next step in the current workflow, if any.
    parent: The step entry, if any, that can be reached by "stepping out" of
      the current workflow being executed.
    previous: The index of the previous step in the current workflow, if any.
  """

  children = _messages.IntegerField(1, repeated=True)
  next = _messages.IntegerField(2)
  parent = _messages.IntegerField(3)
  previous = _messages.IntegerField(4)


class Position(_messages.Message):
  r"""Position contains source position information about the stack trace
  element such as line number, column number and length of the code block in
  bytes.

  Fields:
    column: The source code column position (of the line) the current
      instruction was generated from.
    length: The number of bytes of source code making up this stack trace
      element.
    line: The source code line number the current instruction was generated
      from.
  """

  column = _messages.IntegerField(1)
  length = _messages.IntegerField(2)
  line = _messages.IntegerField(3)


class PubsubMessage(_messages.Message):
  r"""A message that is published by publishers and consumed by subscribers.
  The message must contain either a non-empty data field or at least one
  attribute. Note that client libraries represent this object differently
  depending on the language. See the corresponding [client library
  documentation](https://cloud.google.com/pubsub/docs/reference/libraries) for
  more information. See [quotas and limits]
  (https://cloud.google.com/pubsub/quotas) for more information about message
  limits.

  Messages:
    AttributesValue: Optional. Attributes for this message. If this field is
      empty, the message must contain non-empty data. This can be used to
      filter messages on the subscription.

  Fields:
    attributes: Optional. Attributes for this message. If this field is empty,
      the message must contain non-empty data. This can be used to filter
      messages on the subscription.
    data: Optional. The message data field. If this field is empty, the
      message must contain at least one attribute.
    messageId: ID of this message, assigned by the server when the message is
      published. Guaranteed to be unique within the topic. This value may be
      read by a subscriber that receives a `PubsubMessage` via a `Pull` call
      or a push delivery. It must not be populated by the publisher in a
      `Publish` call.
    orderingKey: Optional. If non-empty, identifies related messages for which
      publish order should be respected. If a `Subscription` has
      `enable_message_ordering` set to `true`, messages published with the
      same non-empty `ordering_key` value will be delivered to subscribers in
      the order in which they are received by the Pub/Sub system. All
      `PubsubMessage`s published in a given `PublishRequest` must specify the
      same `ordering_key` value. For more information, see [ordering
      messages](https://cloud.google.com/pubsub/docs/ordering).
    publishTime: The time at which the message was published, populated by the
      server when it receives the `Publish` call. It must not be populated by
      the publisher in a `Publish` call.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional. Attributes for this message. If this field is empty, the
    message must contain non-empty data. This can be used to filter messages
    on the subscription.

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('AttributesValue', 1)
  data = _messages.BytesField(2)
  messageId = _messages.StringField(3)
  orderingKey = _messages.StringField(4)
  publishTime = _messages.StringField(5)


class StackTrace(_messages.Message):
  r"""A collection of stack elements (frames) where an error occurred.

  Fields:
    elements: An array of stack elements.
  """

  elements = _messages.MessageField('StackTraceElement', 1, repeated=True)


class StackTraceElement(_messages.Message):
  r"""A single stack element (frame) where an error occurred.

  Fields:
    position: The source position information of the stack trace element.
    routine: The routine where the error occurred.
    step: The step the error occurred at.
  """

  position = _messages.MessageField('Position', 1)
  routine = _messages.StringField(2)
  step = _messages.StringField(3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StateError(_messages.Message):
  r"""Describes an error related to the current state of the Execution
  resource.

  Enums:
    TypeValueValuesEnum: The type of this state error.

  Fields:
    details: Provides specifics about the error.
    type: The type of this state error.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of this state error.

    Values:
      TYPE_UNSPECIFIED: No type specified.
      KMS_ERROR: Caused by an issue with KMS.
    """
    TYPE_UNSPECIFIED = 0
    KMS_ERROR = 1

  details = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class Status(_messages.Message):
  r"""Represents the current status of this execution.

  Fields:
    currentSteps: A list of currently executing or last executed step names
      for the workflow execution currently running. If the workflow has
      succeeded or failed, this is the last attempted or executed step.
      Presently, if the current step is inside a subworkflow, the list only
      includes that step. In the future, the list will contain items for each
      step in the call stack, starting with the outermost step in the `main`
      subworkflow, and ending with the most deeply nested step.
  """

  currentSteps = _messages.MessageField('Step', 1, repeated=True)


class Step(_messages.Message):
  r"""Represents a step of the workflow this execution is running.

  Fields:
    routine: Name of a routine within the workflow.
    step: Name of a step within the routine.
  """

  routine = _messages.StringField(1)
  step = _messages.StringField(2)


class StepEntry(_messages.Message):
  r"""An StepEntry contains debugging information for a step transition in a
  workflow execution.

  Enums:
    StateValueValuesEnum: Output only. The state of the step entry.
    StepTypeValueValuesEnum: Output only. The type of the step this step entry
      belongs to.

  Fields:
    createTime: Output only. The creation time of the step entry.
    entryId: Output only. The numeric ID of this step entry, used for
      navigation.
    exception: Output only. The exception thrown by the step entry.
    name: Output only. The full resource name of the step entry. Each step
      entry has a unique entry ID, which is a monotonically increasing
      counter. Step entry names have the format: `projects/{project}/locations
      /{location}/workflows/{workflow}/executions/{execution}/stepEntries/{ste
      p_entry}`.
    navigationInfo: Output only. The NavigationInfo associated with this step.
    routine: Output only. The name of the routine this step entry belongs to.
      A routine name is the subworkflow name defined in the YAML source code.
      The top level routine name is `main`.
    state: Output only. The state of the step entry.
    step: Output only. The name of the step this step entry belongs to.
    stepEntryMetadata: Output only. The StepEntryMetadata associated with this
      step.
    stepType: Output only. The type of the step this step entry belongs to.
    updateTime: Output only. The most recently updated time of the step entry.
    variableData: Output only. The VariableData associated with this step.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the step entry.

    Values:
      STATE_UNSPECIFIED: Invalid state.
      STATE_IN_PROGRESS: The step entry is in progress.
      STATE_SUCCEEDED: The step entry finished successfully.
      STATE_FAILED: The step entry failed with an error.
      STATE_CANCELLED: The step entry is cancelled.
    """
    STATE_UNSPECIFIED = 0
    STATE_IN_PROGRESS = 1
    STATE_SUCCEEDED = 2
    STATE_FAILED = 3
    STATE_CANCELLED = 4

  class StepTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the step this step entry belongs to.

    Values:
      STEP_TYPE_UNSPECIFIED: Invalid step type.
      STEP_ASSIGN: The step entry assigns new variable(s).
      STEP_STD_LIB_CALL: The step entry calls a standard library routine.
      STEP_CONNECTOR_CALL: The step entry calls a connector.
      STEP_SUBWORKFLOW_CALL: The step entry calls a subworklfow.
      STEP_CALL: The step entry calls a subworkflow/stdlib.
      STEP_SWITCH: The step entry executes a switch-case block.
      STEP_CONDITION: The step entry executes a condition inside a switch.
      STEP_FOR: The step entry executes a for loop.
      STEP_FOR_ITERATION: The step entry executes a iteration of a for loop.
      STEP_PARALLEL_FOR: The step entry executes a parallel for loop.
      STEP_PARALLEL_BRANCH: The step entry executes a series of parallel
        branch(es).
      STEP_PARALLEL_BRANCH_ENTRY: The step entry executes a branch of a
        parallel branch.
      STEP_TRY_RETRY_EXCEPT: The step entry executes a try/retry/except block.
      STEP_TRY: The step entry executes the try part of a try/retry/except
        block.
      STEP_RETRY: The step entry executes the retry part of a try/retry/except
        block.
      STEP_EXCEPT: The step entry executes the except part of a
        try/retry/except block.
      STEP_RETURN: The step entry returns.
      STEP_RAISE: The step entry raises an error.
      STEP_GOTO: The step entry jumps to another step.
    """
    STEP_TYPE_UNSPECIFIED = 0
    STEP_ASSIGN = 1
    STEP_STD_LIB_CALL = 2
    STEP_CONNECTOR_CALL = 3
    STEP_SUBWORKFLOW_CALL = 4
    STEP_CALL = 5
    STEP_SWITCH = 6
    STEP_CONDITION = 7
    STEP_FOR = 8
    STEP_FOR_ITERATION = 9
    STEP_PARALLEL_FOR = 10
    STEP_PARALLEL_BRANCH = 11
    STEP_PARALLEL_BRANCH_ENTRY = 12
    STEP_TRY_RETRY_EXCEPT = 13
    STEP_TRY = 14
    STEP_RETRY = 15
    STEP_EXCEPT = 16
    STEP_RETURN = 17
    STEP_RAISE = 18
    STEP_GOTO = 19

  createTime = _messages.StringField(1)
  entryId = _messages.IntegerField(2)
  exception = _messages.MessageField('Exception', 3)
  name = _messages.StringField(4)
  navigationInfo = _messages.MessageField('NavigationInfo', 5)
  routine = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  step = _messages.StringField(8)
  stepEntryMetadata = _messages.MessageField('StepEntryMetadata', 9)
  stepType = _messages.EnumField('StepTypeValueValuesEnum', 10)
  updateTime = _messages.StringField(11)
  variableData = _messages.MessageField('VariableData', 12)


class StepEntryMetadata(_messages.Message):
  r"""StepEntryMetadata contains metadata information about this step.

  Enums:
    ProgressTypeValueValuesEnum: Progress type of this step entry.

  Fields:
    expectedIteration: Expected iteration represents the expected number of
      iterations in the step's progress.
    progressNumber: Progress number represents the current state of the
      current progress. eg: A step entry represents the 4th iteration in a
      progress of PROGRESS_TYPE_FOR. Note: This field is only populated when
      an iteration exists and the starting value is 1.
    progressType: Progress type of this step entry.
    threadId: Child thread id that this step entry belongs to.
  """

  class ProgressTypeValueValuesEnum(_messages.Enum):
    r"""Progress type of this step entry.

    Values:
      PROGRESS_TYPE_UNSPECIFIED: Current step entry does not have any progress
        data.
      PROGRESS_TYPE_FOR: Current step entry is in progress of a FOR step.
      PROGRESS_TYPE_SWITCH: Current step entry is in progress of a SWITCH
        step.
      PROGRESS_TYPE_RETRY: Current step entry is in progress of a RETRY step.
      PROGRESS_TYPE_PARALLEL_FOR: Current step entry is in progress of a
        PARALLEL FOR step.
      PROGRESS_TYPE_PARALLEL_BRANCH: Current step entry is in progress of a
        PARALLEL BRANCH step.
    """
    PROGRESS_TYPE_UNSPECIFIED = 0
    PROGRESS_TYPE_FOR = 1
    PROGRESS_TYPE_SWITCH = 2
    PROGRESS_TYPE_RETRY = 3
    PROGRESS_TYPE_PARALLEL_FOR = 4
    PROGRESS_TYPE_PARALLEL_BRANCH = 5

  expectedIteration = _messages.IntegerField(1)
  progressNumber = _messages.IntegerField(2)
  progressType = _messages.EnumField('ProgressTypeValueValuesEnum', 3)
  threadId = _messages.StringField(4)


class TriggerPubsubExecutionRequest(_messages.Message):
  r"""Request for the TriggerPubsubExecution method.

  Fields:
    GCPCloudEventsMode: Required. LINT: LEGACY_NAMES The query parameter value
      for __GCP_CloudEventsMode, set by the Eventarc service when configuring
      triggers.
    deliveryAttempt: The number of attempts that have been made to deliver
      this message. This is set by Pub/Sub for subscriptions that have the
      "dead letter" feature enabled, and hence provided here for
      compatibility, but is ignored by Workflows.
    message: Required. The message of the Pub/Sub push notification.
    subscription: Required. The subscription of the Pub/Sub push notification.
      Format: projects/{project}/subscriptions/{sub}
  """

  GCPCloudEventsMode = _messages.StringField(1)
  deliveryAttempt = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  message = _messages.MessageField('PubsubMessage', 3)
  subscription = _messages.StringField(4)


class VariableData(_messages.Message):
  r"""VariableData contains the variable data for this step.

  Messages:
    VariablesValue: Variables that are associated with this step.

  Fields:
    variables: Variables that are associated with this step.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class VariablesValue(_messages.Message):
    r"""Variables that are associated with this step.

    Messages:
      AdditionalProperty: An additional property for a VariablesValue object.

    Fields:
      additionalProperties: Additional properties of type VariablesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a VariablesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  variables = _messages.MessageField('VariablesValue', 1)


class WorkflowexecutionsProjectsLocationsWorkflowsExecutionsCallbacksListRequest(_messages.Message):
  r"""A
  WorkflowexecutionsProjectsLocationsWorkflowsExecutionsCallbacksListRequest
  object.

  Fields:
    pageSize: Maximum number of callbacks to return per call. The default
      value is 100 and is also the maximum value.
    pageToken: A page token, received from a previous `ListCallbacks` call.
      Provide this to retrieve the subsequent page. Note that pagination is
      applied to dynamic data. The list of callbacks returned can change
      between page requests if callbacks are created or deleted.
    parent: Required. Name of the execution for which the callbacks should be
      listed. Format: projects/{project}/locations/{location}/workflows/{workf
      low}/executions/{execution}
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class WorkflowexecutionsProjectsLocationsWorkflowsExecutionsCancelRequest(_messages.Message):
  r"""A WorkflowexecutionsProjectsLocationsWorkflowsExecutionsCancelRequest
  object.

  Fields:
    cancelExecutionRequest: A CancelExecutionRequest resource to be passed as
      the request body.
    name: Required. Name of the execution to be cancelled. Format: projects/{p
      roject}/locations/{location}/workflows/{workflow}/executions/{execution}
  """

  cancelExecutionRequest = _messages.MessageField('CancelExecutionRequest', 1)
  name = _messages.StringField(2, required=True)


class WorkflowexecutionsProjectsLocationsWorkflowsExecutionsCreateRequest(_messages.Message):
  r"""A WorkflowexecutionsProjectsLocationsWorkflowsExecutionsCreateRequest
  object.

  Fields:
    execution: A Execution resource to be passed as the request body.
    parent: Required. Name of the workflow for which an execution should be
      created. Format:
      projects/{project}/locations/{location}/workflows/{workflow} The latest
      revision of the workflow will be used.
  """

  execution = _messages.MessageField('Execution', 1)
  parent = _messages.StringField(2, required=True)


class WorkflowexecutionsProjectsLocationsWorkflowsExecutionsDeleteExecutionHistoryRequest(_messages.Message):
  r"""A WorkflowexecutionsProjectsLocationsWorkflowsExecutionsDeleteExecutionH
  istoryRequest object.

  Fields:
    deleteExecutionHistoryRequest: A DeleteExecutionHistoryRequest resource to
      be passed as the request body.
    name: Required. Name of the execution for which step entries should be
      deleted. Format: projects/{project}/locations/{location}/workflows/{work
      flow}/executions/{execution}
  """

  deleteExecutionHistoryRequest = _messages.MessageField('DeleteExecutionHistoryRequest', 1)
  name = _messages.StringField(2, required=True)


class WorkflowexecutionsProjectsLocationsWorkflowsExecutionsExportDataRequest(_messages.Message):
  r"""A
  WorkflowexecutionsProjectsLocationsWorkflowsExecutionsExportDataRequest
  object.

  Fields:
    name: Required. Name of the execution for which data is to be exported.
      Format: projects/{project}/locations/{location}/workflows/{workflow}/exe
      cutions/{execution}
  """

  name = _messages.StringField(1, required=True)


class WorkflowexecutionsProjectsLocationsWorkflowsExecutionsGetRequest(_messages.Message):
  r"""A WorkflowexecutionsProjectsLocationsWorkflowsExecutionsGetRequest
  object.

  Enums:
    ViewValueValuesEnum: Optional. A view defining which fields should be
      filled in the returned execution. The API will default to the FULL view.

  Fields:
    name: Required. Name of the execution to be retrieved. Format: projects/{p
      roject}/locations/{location}/workflows/{workflow}/executions/{execution}
    view: Optional. A view defining which fields should be filled in the
      returned execution. The API will default to the FULL view.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. A view defining which fields should be filled in the
    returned execution. The API will default to the FULL view.

    Values:
      EXECUTION_VIEW_UNSPECIFIED: The default / unset value.
      BASIC: Includes only basic metadata about the execution. The following
        fields are returned: name, start_time, end_time, duration, state, and
        workflow_revision_id.
      FULL: Includes all data.
    """
    EXECUTION_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class WorkflowexecutionsProjectsLocationsWorkflowsExecutionsListRequest(_messages.Message):
  r"""A WorkflowexecutionsProjectsLocationsWorkflowsExecutionsListRequest
  object.

  Enums:
    ViewValueValuesEnum: Optional. A view defining which fields should be
      filled in the returned executions. The API will default to the BASIC
      view.

  Fields:
    filter: Optional. Filters applied to the `[Executions.ListExecutions]`
      results. The following fields are supported for filtering:
      `executionId`, `state`, `createTime`, `startTime`, `endTime`,
      `duration`, `workflowRevisionId`, `stepName`, `label`, and
      `disableConcurrencyQuotaOverflowBuffering`. For details, see AIP-160.
      For more information, see Filter executions. For example, if you are
      using the Google APIs Explorer: `state="SUCCEEDED"` or
      `startTime>"2023-08-01" AND state="FAILED"`
    orderBy: Optional. Comma-separated list of fields that specify the
      ordering applied to the `[Executions.ListExecutions]` results. By
      default the ordering is based on descending `createTime`. The following
      fields are supported for ordering: `executionId`, `state`, `createTime`,
      `startTime`, `endTime`, `duration`, and `workflowRevisionId`. For
      details, see AIP-132.
    pageSize: Maximum number of executions to return per call. Max supported
      value depends on the selected Execution view: it's 1000 for BASIC and
      100 for FULL. The default value used if the field is not specified is
      100, regardless of the selected view. Values greater than the max value
      will be coerced down to it.
    pageToken: A page token, received from a previous `ListExecutions` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListExecutions` must match the call that
      provided the page token. Note that pagination is applied to dynamic
      data. The list of executions returned can change between page requests.
    parent: Required. Name of the workflow for which the executions should be
      listed. Format:
      projects/{project}/locations/{location}/workflows/{workflow}
    view: Optional. A view defining which fields should be filled in the
      returned executions. The API will default to the BASIC view.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. A view defining which fields should be filled in the
    returned executions. The API will default to the BASIC view.

    Values:
      EXECUTION_VIEW_UNSPECIFIED: The default / unset value.
      BASIC: Includes only basic metadata about the execution. The following
        fields are returned: name, start_time, end_time, duration, state, and
        workflow_revision_id.
      FULL: Includes all data.
    """
    EXECUTION_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 6)


class WorkflowexecutionsProjectsLocationsWorkflowsExecutionsStepEntriesGetRequest(_messages.Message):
  r"""A
  WorkflowexecutionsProjectsLocationsWorkflowsExecutionsStepEntriesGetRequest
  object.

  Enums:
    ViewValueValuesEnum: Deprecated field.

  Fields:
    name: Required. The name of the step entry to retrieve. Format: projects/{
      project}/locations/{location}/workflows/{workflow}/executions/{execution
      }/stepEntries/{step_entry}
    view: Deprecated field.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Deprecated field.

    Values:
      EXECUTION_ENTRY_VIEW_UNSPECIFIED: The default/unset value.
      EXECUTION_ENTRY_VIEW_BASIC: Include basic information in the step
        entries. All fields in StepEntry are returned except for
        variable_data.
      EXECUTION_ENTRY_VIEW_DETAILED: Include all data.
    """
    EXECUTION_ENTRY_VIEW_UNSPECIFIED = 0
    EXECUTION_ENTRY_VIEW_BASIC = 1
    EXECUTION_ENTRY_VIEW_DETAILED = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class WorkflowexecutionsProjectsLocationsWorkflowsExecutionsStepEntriesListRequest(_messages.Message):
  r"""A
  WorkflowexecutionsProjectsLocationsWorkflowsExecutionsStepEntriesListRequest
  object.

  Enums:
    ViewValueValuesEnum: Deprecated field.

  Fields:
    filter: Optional. Filters applied to the `[StepEntries.ListStepEntries]`
      results. The following fields are supported for filtering: `entryId`,
      `createTime`, `updateTime`, `routine`, `step`, `stepType`, `parent`,
      `state`. For details, see AIP-160. For example, if you are using the
      Google APIs Explorer: `state="SUCCEEDED"` or `createTime>"2023-08-01"
      AND state="FAILED"`
    orderBy: Optional. Comma-separated list of fields that specify the
      ordering applied to the `[StepEntries.ListStepEntries]` results. By
      default the ordering is based on ascending `entryId`. The following
      fields are supported for ordering: `entryId`, `createTime`,
      `updateTime`, `routine`, `step`, `stepType`, `state`. For details, see
      AIP-132.
    pageSize: Optional. Number of step entries to return per call. The default
      max is 1000.
    pageToken: Optional. A page token, received from a previous
      `ListStepEntries` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListStepEntries` must
      match the call that provided the page token.
    parent: Required. Name of the workflow execution to list entries for.
      Format: projects/{project}/locations/{location}/workflows/{workflow}/exe
      cutions/{execution}
    skip: Optional. The number of step entries to skip. It can be used with or
      without a pageToken. If used with a pageToken, then it indicates the
      number of step entries to skip starting from the requested page.
    view: Deprecated field.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Deprecated field.

    Values:
      EXECUTION_ENTRY_VIEW_UNSPECIFIED: The default/unset value.
      EXECUTION_ENTRY_VIEW_BASIC: Include basic information in the step
        entries. All fields in StepEntry are returned except for
        variable_data.
      EXECUTION_ENTRY_VIEW_DETAILED: Include all data.
    """
    EXECUTION_ENTRY_VIEW_UNSPECIFIED = 0
    EXECUTION_ENTRY_VIEW_BASIC = 1
    EXECUTION_ENTRY_VIEW_DETAILED = 2

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  skip = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  view = _messages.EnumField('ViewValueValuesEnum', 7)


class WorkflowexecutionsProjectsLocationsWorkflowsTriggerPubsubExecutionRequest(_messages.Message):
  r"""A
  WorkflowexecutionsProjectsLocationsWorkflowsTriggerPubsubExecutionRequest
  object.

  Fields:
    triggerPubsubExecutionRequest: A TriggerPubsubExecutionRequest resource to
      be passed as the request body.
    workflow: Required. Name of the workflow for which an execution should be
      created. Format:
      projects/{project}/locations/{location}/workflows/{workflow}
  """

  triggerPubsubExecutionRequest = _messages.MessageField('TriggerPubsubExecutionRequest', 1)
  workflow = _messages.StringField(2, required=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
