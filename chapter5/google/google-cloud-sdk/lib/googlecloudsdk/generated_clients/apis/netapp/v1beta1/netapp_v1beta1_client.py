"""Generated client library for netapp version v1beta1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.netapp.v1beta1 import netapp_v1beta1_messages as messages


class NetappV1beta1(base_api.BaseApiClient):
  """Generated client library for service netapp version v1beta1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://netapp.googleapis.com/'
  MTLS_BASE_URL = 'https://netapp.mtls.googleapis.com/'

  _PACKAGE = 'netapp'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1beta1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'NetappV1beta1'
  _URL_VERSION = 'v1beta1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new netapp handle."""
    url = url or self.BASE_URL
    super(NetappV1beta1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_activeDirectories = self.ProjectsLocationsActiveDirectoriesService(self)
    self.projects_locations_backupPolicies = self.ProjectsLocationsBackupPoliciesService(self)
    self.projects_locations_backupVaults_backups = self.ProjectsLocationsBackupVaultsBackupsService(self)
    self.projects_locations_backupVaults = self.ProjectsLocationsBackupVaultsService(self)
    self.projects_locations_hostGroups = self.ProjectsLocationsHostGroupsService(self)
    self.projects_locations_kmsConfigs = self.ProjectsLocationsKmsConfigsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_storagePools = self.ProjectsLocationsStoragePoolsService(self)
    self.projects_locations_volumes_quotaRules = self.ProjectsLocationsVolumesQuotaRulesService(self)
    self.projects_locations_volumes_replications = self.ProjectsLocationsVolumesReplicationsService(self)
    self.projects_locations_volumes_snapshots = self.ProjectsLocationsVolumesSnapshotsService(self)
    self.projects_locations_volumes = self.ProjectsLocationsVolumesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsActiveDirectoriesService(base_api.BaseApiService):
    """Service class for the projects_locations_activeDirectories resource."""

    _NAME = 'projects_locations_activeDirectories'

    def __init__(self, client):
      super(NetappV1beta1.ProjectsLocationsActiveDirectoriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""CreateActiveDirectory Creates the active directory specified in the request.

      Args:
        request: (NetappProjectsLocationsActiveDirectoriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/activeDirectories',
        http_method='POST',
        method_id='netapp.projects.locations.activeDirectories.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['activeDirectoryId'],
        relative_path='v1beta1/{+parent}/activeDirectories',
        request_field='activeDirectory',
        request_type_name='NetappProjectsLocationsActiveDirectoriesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete the active directory specified in the request.

      Args:
        request: (NetappProjectsLocationsActiveDirectoriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/activeDirectories/{activeDirectoriesId}',
        http_method='DELETE',
        method_id='netapp.projects.locations.activeDirectories.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsActiveDirectoriesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Describes a specified active directory.

      Args:
        request: (NetappProjectsLocationsActiveDirectoriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ActiveDirectory) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/activeDirectories/{activeDirectoriesId}',
        http_method='GET',
        method_id='netapp.projects.locations.activeDirectories.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsActiveDirectoriesGetRequest',
        response_type_name='ActiveDirectory',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists active directories.

      Args:
        request: (NetappProjectsLocationsActiveDirectoriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListActiveDirectoriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/activeDirectories',
        http_method='GET',
        method_id='netapp.projects.locations.activeDirectories.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/activeDirectories',
        request_field='',
        request_type_name='NetappProjectsLocationsActiveDirectoriesListRequest',
        response_type_name='ListActiveDirectoriesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update the parameters of an active directories.

      Args:
        request: (NetappProjectsLocationsActiveDirectoriesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/activeDirectories/{activeDirectoriesId}',
        http_method='PATCH',
        method_id='netapp.projects.locations.activeDirectories.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='activeDirectory',
        request_type_name='NetappProjectsLocationsActiveDirectoriesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsBackupPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_backupPolicies resource."""

    _NAME = 'projects_locations_backupPolicies'

    def __init__(self, client):
      super(NetappV1beta1.ProjectsLocationsBackupPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates new backup policy.

      Args:
        request: (NetappProjectsLocationsBackupPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupPolicies',
        http_method='POST',
        method_id='netapp.projects.locations.backupPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['backupPolicyId'],
        relative_path='v1beta1/{+parent}/backupPolicies',
        request_field='backupPolicy',
        request_type_name='NetappProjectsLocationsBackupPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Warning! This operation will permanently delete the backup policy.

      Args:
        request: (NetappProjectsLocationsBackupPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupPolicies/{backupPoliciesId}',
        http_method='DELETE',
        method_id='netapp.projects.locations.backupPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsBackupPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the description of the specified backup policy by backup_policy_id.

      Args:
        request: (NetappProjectsLocationsBackupPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BackupPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupPolicies/{backupPoliciesId}',
        http_method='GET',
        method_id='netapp.projects.locations.backupPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsBackupPoliciesGetRequest',
        response_type_name='BackupPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns list of all available backup policies.

      Args:
        request: (NetappProjectsLocationsBackupPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBackupPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupPolicies',
        http_method='GET',
        method_id='netapp.projects.locations.backupPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/backupPolicies',
        request_field='',
        request_type_name='NetappProjectsLocationsBackupPoliciesListRequest',
        response_type_name='ListBackupPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates settings of a specific backup policy.

      Args:
        request: (NetappProjectsLocationsBackupPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupPolicies/{backupPoliciesId}',
        http_method='PATCH',
        method_id='netapp.projects.locations.backupPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='backupPolicy',
        request_type_name='NetappProjectsLocationsBackupPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsBackupVaultsBackupsService(base_api.BaseApiService):
    """Service class for the projects_locations_backupVaults_backups resource."""

    _NAME = 'projects_locations_backupVaults_backups'

    def __init__(self, client):
      super(NetappV1beta1.ProjectsLocationsBackupVaultsBackupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a backup from the volume specified in the request The backup can be created from the given snapshot if specified in the request. If no snapshot specified, there'll be a new snapshot taken to initiate the backup creation.

      Args:
        request: (NetappProjectsLocationsBackupVaultsBackupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/backups',
        http_method='POST',
        method_id='netapp.projects.locations.backupVaults.backups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['backupId'],
        relative_path='v1beta1/{+parent}/backups',
        request_field='backup',
        request_type_name='NetappProjectsLocationsBackupVaultsBackupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Warning! This operation will permanently delete the backup.

      Args:
        request: (NetappProjectsLocationsBackupVaultsBackupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/backups/{backupsId}',
        http_method='DELETE',
        method_id='netapp.projects.locations.backupVaults.backups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsBackupVaultsBackupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the description of the specified backup.

      Args:
        request: (NetappProjectsLocationsBackupVaultsBackupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Backup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/backups/{backupsId}',
        http_method='GET',
        method_id='netapp.projects.locations.backupVaults.backups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsBackupVaultsBackupsGetRequest',
        response_type_name='Backup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns descriptions of all backups for a backupVault.

      Args:
        request: (NetappProjectsLocationsBackupVaultsBackupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBackupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/backups',
        http_method='GET',
        method_id='netapp.projects.locations.backupVaults.backups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/backups',
        request_field='',
        request_type_name='NetappProjectsLocationsBackupVaultsBackupsListRequest',
        response_type_name='ListBackupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update backup with full spec.

      Args:
        request: (NetappProjectsLocationsBackupVaultsBackupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/backups/{backupsId}',
        http_method='PATCH',
        method_id='netapp.projects.locations.backupVaults.backups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='backup',
        request_type_name='NetappProjectsLocationsBackupVaultsBackupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsBackupVaultsService(base_api.BaseApiService):
    """Service class for the projects_locations_backupVaults resource."""

    _NAME = 'projects_locations_backupVaults'

    def __init__(self, client):
      super(NetappV1beta1.ProjectsLocationsBackupVaultsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates new backup vault.

      Args:
        request: (NetappProjectsLocationsBackupVaultsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupVaults',
        http_method='POST',
        method_id='netapp.projects.locations.backupVaults.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['backupVaultId'],
        relative_path='v1beta1/{+parent}/backupVaults',
        request_field='backupVault',
        request_type_name='NetappProjectsLocationsBackupVaultsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Warning! This operation will permanently delete the backup vault.

      Args:
        request: (NetappProjectsLocationsBackupVaultsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}',
        http_method='DELETE',
        method_id='netapp.projects.locations.backupVaults.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsBackupVaultsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the description of the specified backup vault.

      Args:
        request: (NetappProjectsLocationsBackupVaultsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BackupVault) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}',
        http_method='GET',
        method_id='netapp.projects.locations.backupVaults.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsBackupVaultsGetRequest',
        response_type_name='BackupVault',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns list of all available backup vaults.

      Args:
        request: (NetappProjectsLocationsBackupVaultsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBackupVaultsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupVaults',
        http_method='GET',
        method_id='netapp.projects.locations.backupVaults.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/backupVaults',
        request_field='',
        request_type_name='NetappProjectsLocationsBackupVaultsListRequest',
        response_type_name='ListBackupVaultsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the settings of a specific backup vault.

      Args:
        request: (NetappProjectsLocationsBackupVaultsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}',
        http_method='PATCH',
        method_id='netapp.projects.locations.backupVaults.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='backupVault',
        request_type_name='NetappProjectsLocationsBackupVaultsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsHostGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_hostGroups resource."""

    _NAME = 'projects_locations_hostGroups'

    def __init__(self, client):
      super(NetappV1beta1.ProjectsLocationsHostGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new host group.

      Args:
        request: (NetappProjectsLocationsHostGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/hostGroups',
        http_method='POST',
        method_id='netapp.projects.locations.hostGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['hostGroupId'],
        relative_path='v1beta1/{+parent}/hostGroups',
        request_field='hostGroup',
        request_type_name='NetappProjectsLocationsHostGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a host group.

      Args:
        request: (NetappProjectsLocationsHostGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/hostGroups/{hostGroupsId}',
        http_method='DELETE',
        method_id='netapp.projects.locations.hostGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsHostGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns details of the specified host group.

      Args:
        request: (NetappProjectsLocationsHostGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HostGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/hostGroups/{hostGroupsId}',
        http_method='GET',
        method_id='netapp.projects.locations.hostGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsHostGroupsGetRequest',
        response_type_name='HostGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of host groups in a location. Use '-' as location to list host groups across all locations.

      Args:
        request: (NetappProjectsLocationsHostGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListHostGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/hostGroups',
        http_method='GET',
        method_id='netapp.projects.locations.hostGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/hostGroups',
        request_field='',
        request_type_name='NetappProjectsLocationsHostGroupsListRequest',
        response_type_name='ListHostGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing host group.

      Args:
        request: (NetappProjectsLocationsHostGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/hostGroups/{hostGroupsId}',
        http_method='PATCH',
        method_id='netapp.projects.locations.hostGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='hostGroup',
        request_type_name='NetappProjectsLocationsHostGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsKmsConfigsService(base_api.BaseApiService):
    """Service class for the projects_locations_kmsConfigs resource."""

    _NAME = 'projects_locations_kmsConfigs'

    def __init__(self, client):
      super(NetappV1beta1.ProjectsLocationsKmsConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new KMS config.

      Args:
        request: (NetappProjectsLocationsKmsConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/kmsConfigs',
        http_method='POST',
        method_id='netapp.projects.locations.kmsConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['kmsConfigId'],
        relative_path='v1beta1/{+parent}/kmsConfigs',
        request_field='kmsConfig',
        request_type_name='NetappProjectsLocationsKmsConfigsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Warning! This operation will permanently delete the Kms config.

      Args:
        request: (NetappProjectsLocationsKmsConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/kmsConfigs/{kmsConfigsId}',
        http_method='DELETE',
        method_id='netapp.projects.locations.kmsConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsKmsConfigsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Encrypt(self, request, global_params=None):
      r"""Encrypt the existing volumes without CMEK encryption with the desired the KMS config for the whole region.

      Args:
        request: (NetappProjectsLocationsKmsConfigsEncryptRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Encrypt')
      return self._RunMethod(
          config, request, global_params=global_params)

    Encrypt.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/kmsConfigs/{kmsConfigsId}:encrypt',
        http_method='POST',
        method_id='netapp.projects.locations.kmsConfigs.encrypt',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:encrypt',
        request_field='encryptVolumesRequest',
        request_type_name='NetappProjectsLocationsKmsConfigsEncryptRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the description of the specified KMS config by kms_config_id.

      Args:
        request: (NetappProjectsLocationsKmsConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (KmsConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/kmsConfigs/{kmsConfigsId}',
        http_method='GET',
        method_id='netapp.projects.locations.kmsConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsKmsConfigsGetRequest',
        response_type_name='KmsConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns descriptions of all KMS configs owned by the caller.

      Args:
        request: (NetappProjectsLocationsKmsConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListKmsConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/kmsConfigs',
        http_method='GET',
        method_id='netapp.projects.locations.kmsConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/kmsConfigs',
        request_field='',
        request_type_name='NetappProjectsLocationsKmsConfigsListRequest',
        response_type_name='ListKmsConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the Kms config properties with the full spec.

      Args:
        request: (NetappProjectsLocationsKmsConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/kmsConfigs/{kmsConfigsId}',
        http_method='PATCH',
        method_id='netapp.projects.locations.kmsConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='kmsConfig',
        request_type_name='NetappProjectsLocationsKmsConfigsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Verify(self, request, global_params=None):
      r"""Verifies KMS config reachability.

      Args:
        request: (NetappProjectsLocationsKmsConfigsVerifyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VerifyKmsConfigResponse) The response message.
      """
      config = self.GetMethodConfig('Verify')
      return self._RunMethod(
          config, request, global_params=global_params)

    Verify.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/kmsConfigs/{kmsConfigsId}:verify',
        http_method='POST',
        method_id='netapp.projects.locations.kmsConfigs.verify',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:verify',
        request_field='verifyKmsConfigRequest',
        request_type_name='NetappProjectsLocationsKmsConfigsVerifyRequest',
        response_type_name='VerifyKmsConfigResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(NetappV1beta1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (NetappProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='netapp.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='NetappProjectsLocationsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (NetappProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='netapp.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (NetappProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='netapp.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (NetappProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='netapp.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+name}/operations',
        request_field='',
        request_type_name='NetappProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsStoragePoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_storagePools resource."""

    _NAME = 'projects_locations_storagePools'

    def __init__(self, client):
      super(NetappV1beta1.ProjectsLocationsStoragePoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new storage pool.

      Args:
        request: (NetappProjectsLocationsStoragePoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/storagePools',
        http_method='POST',
        method_id='netapp.projects.locations.storagePools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['storagePoolId'],
        relative_path='v1beta1/{+parent}/storagePools',
        request_field='storagePool',
        request_type_name='NetappProjectsLocationsStoragePoolsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Warning! This operation will permanently delete the storage pool.

      Args:
        request: (NetappProjectsLocationsStoragePoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/storagePools/{storagePoolsId}',
        http_method='DELETE',
        method_id='netapp.projects.locations.storagePools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsStoragePoolsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the description of the specified storage pool by poolId.

      Args:
        request: (NetappProjectsLocationsStoragePoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (StoragePool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/storagePools/{storagePoolsId}',
        http_method='GET',
        method_id='netapp.projects.locations.storagePools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsStoragePoolsGetRequest',
        response_type_name='StoragePool',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns descriptions of all storage pools owned by the caller.

      Args:
        request: (NetappProjectsLocationsStoragePoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListStoragePoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/storagePools',
        http_method='GET',
        method_id='netapp.projects.locations.storagePools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/storagePools',
        request_field='',
        request_type_name='NetappProjectsLocationsStoragePoolsListRequest',
        response_type_name='ListStoragePoolsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the storage pool properties with the full spec.

      Args:
        request: (NetappProjectsLocationsStoragePoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/storagePools/{storagePoolsId}',
        http_method='PATCH',
        method_id='netapp.projects.locations.storagePools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='storagePool',
        request_type_name='NetappProjectsLocationsStoragePoolsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Switch(self, request, global_params=None):
      r"""This operation will switch the active/replica zone for a regional storagePool.

      Args:
        request: (NetappProjectsLocationsStoragePoolsSwitchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Switch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Switch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/storagePools/{storagePoolsId}:switch',
        http_method='POST',
        method_id='netapp.projects.locations.storagePools.switch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:switch',
        request_field='switchActiveReplicaZoneRequest',
        request_type_name='NetappProjectsLocationsStoragePoolsSwitchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ValidateDirectoryService(self, request, global_params=None):
      r"""ValidateDirectoryService does a connectivity check for a directory service policy attached to the storage pool.

      Args:
        request: (NetappProjectsLocationsStoragePoolsValidateDirectoryServiceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ValidateDirectoryService')
      return self._RunMethod(
          config, request, global_params=global_params)

    ValidateDirectoryService.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/storagePools/{storagePoolsId}:validateDirectoryService',
        http_method='POST',
        method_id='netapp.projects.locations.storagePools.validateDirectoryService',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:validateDirectoryService',
        request_field='validateDirectoryServiceRequest',
        request_type_name='NetappProjectsLocationsStoragePoolsValidateDirectoryServiceRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsVolumesQuotaRulesService(base_api.BaseApiService):
    """Service class for the projects_locations_volumes_quotaRules resource."""

    _NAME = 'projects_locations_volumes_quotaRules'

    def __init__(self, client):
      super(NetappV1beta1.ProjectsLocationsVolumesQuotaRulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new quota rule.

      Args:
        request: (NetappProjectsLocationsVolumesQuotaRulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/quotaRules',
        http_method='POST',
        method_id='netapp.projects.locations.volumes.quotaRules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['quotaRuleId'],
        relative_path='v1beta1/{+parent}/quotaRules',
        request_field='quotaRule',
        request_type_name='NetappProjectsLocationsVolumesQuotaRulesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a quota rule.

      Args:
        request: (NetappProjectsLocationsVolumesQuotaRulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/quotaRules/{quotaRulesId}',
        http_method='DELETE',
        method_id='netapp.projects.locations.volumes.quotaRules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsVolumesQuotaRulesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns details of the specified quota rule.

      Args:
        request: (NetappProjectsLocationsVolumesQuotaRulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QuotaRule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/quotaRules/{quotaRulesId}',
        http_method='GET',
        method_id='netapp.projects.locations.volumes.quotaRules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsVolumesQuotaRulesGetRequest',
        response_type_name='QuotaRule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns list of all quota rules in a location.

      Args:
        request: (NetappProjectsLocationsVolumesQuotaRulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListQuotaRulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/quotaRules',
        http_method='GET',
        method_id='netapp.projects.locations.volumes.quotaRules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/quotaRules',
        request_field='',
        request_type_name='NetappProjectsLocationsVolumesQuotaRulesListRequest',
        response_type_name='ListQuotaRulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a quota rule.

      Args:
        request: (NetappProjectsLocationsVolumesQuotaRulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/quotaRules/{quotaRulesId}',
        http_method='PATCH',
        method_id='netapp.projects.locations.volumes.quotaRules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='quotaRule',
        request_type_name='NetappProjectsLocationsVolumesQuotaRulesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsVolumesReplicationsService(base_api.BaseApiService):
    """Service class for the projects_locations_volumes_replications resource."""

    _NAME = 'projects_locations_volumes_replications'

    def __init__(self, client):
      super(NetappV1beta1.ProjectsLocationsVolumesReplicationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new replication for a volume.

      Args:
        request: (NetappProjectsLocationsVolumesReplicationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/replications',
        http_method='POST',
        method_id='netapp.projects.locations.volumes.replications.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['replicationId'],
        relative_path='v1beta1/{+parent}/replications',
        request_field='replication',
        request_type_name='NetappProjectsLocationsVolumesReplicationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a replication.

      Args:
        request: (NetappProjectsLocationsVolumesReplicationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/replications/{replicationsId}',
        http_method='DELETE',
        method_id='netapp.projects.locations.volumes.replications.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsVolumesReplicationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def EstablishPeering(self, request, global_params=None):
      r"""Establish replication peering.

      Args:
        request: (NetappProjectsLocationsVolumesReplicationsEstablishPeeringRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('EstablishPeering')
      return self._RunMethod(
          config, request, global_params=global_params)

    EstablishPeering.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/replications/{replicationsId}:establishPeering',
        http_method='POST',
        method_id='netapp.projects.locations.volumes.replications.establishPeering',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:establishPeering',
        request_field='establishPeeringRequest',
        request_type_name='NetappProjectsLocationsVolumesReplicationsEstablishPeeringRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Describe a replication for a volume.

      Args:
        request: (NetappProjectsLocationsVolumesReplicationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Replication) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/replications/{replicationsId}',
        http_method='GET',
        method_id='netapp.projects.locations.volumes.replications.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsVolumesReplicationsGetRequest',
        response_type_name='Replication',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns descriptions of all replications for a volume.

      Args:
        request: (NetappProjectsLocationsVolumesReplicationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListReplicationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/replications',
        http_method='GET',
        method_id='netapp.projects.locations.volumes.replications.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/replications',
        request_field='',
        request_type_name='NetappProjectsLocationsVolumesReplicationsListRequest',
        response_type_name='ListReplicationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the settings of a specific replication.

      Args:
        request: (NetappProjectsLocationsVolumesReplicationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/replications/{replicationsId}',
        http_method='PATCH',
        method_id='netapp.projects.locations.volumes.replications.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='replication',
        request_type_name='NetappProjectsLocationsVolumesReplicationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Resume(self, request, global_params=None):
      r"""Resume Cross Region Replication.

      Args:
        request: (NetappProjectsLocationsVolumesReplicationsResumeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Resume')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resume.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/replications/{replicationsId}:resume',
        http_method='POST',
        method_id='netapp.projects.locations.volumes.replications.resume',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:resume',
        request_field='resumeReplicationRequest',
        request_type_name='NetappProjectsLocationsVolumesReplicationsResumeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ReverseDirection(self, request, global_params=None):
      r"""Reverses direction of replication. Source becomes destination and destination becomes source.

      Args:
        request: (NetappProjectsLocationsVolumesReplicationsReverseDirectionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ReverseDirection')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReverseDirection.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/replications/{replicationsId}:reverseDirection',
        http_method='POST',
        method_id='netapp.projects.locations.volumes.replications.reverseDirection',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:reverseDirection',
        request_field='reverseReplicationDirectionRequest',
        request_type_name='NetappProjectsLocationsVolumesReplicationsReverseDirectionRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stop Cross Region Replication.

      Args:
        request: (NetappProjectsLocationsVolumesReplicationsStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/replications/{replicationsId}:stop',
        http_method='POST',
        method_id='netapp.projects.locations.volumes.replications.stop',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:stop',
        request_field='stopReplicationRequest',
        request_type_name='NetappProjectsLocationsVolumesReplicationsStopRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Sync(self, request, global_params=None):
      r"""Syncs the replication. This will invoke one time volume data transfer from source to destination.

      Args:
        request: (NetappProjectsLocationsVolumesReplicationsSyncRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Sync')
      return self._RunMethod(
          config, request, global_params=global_params)

    Sync.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/replications/{replicationsId}:sync',
        http_method='POST',
        method_id='netapp.projects.locations.volumes.replications.sync',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:sync',
        request_field='syncReplicationRequest',
        request_type_name='NetappProjectsLocationsVolumesReplicationsSyncRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsVolumesSnapshotsService(base_api.BaseApiService):
    """Service class for the projects_locations_volumes_snapshots resource."""

    _NAME = 'projects_locations_volumes_snapshots'

    def __init__(self, client):
      super(NetappV1beta1.ProjectsLocationsVolumesSnapshotsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new snapshot for a volume.

      Args:
        request: (NetappProjectsLocationsVolumesSnapshotsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/snapshots',
        http_method='POST',
        method_id='netapp.projects.locations.volumes.snapshots.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['snapshotId'],
        relative_path='v1beta1/{+parent}/snapshots',
        request_field='snapshot',
        request_type_name='NetappProjectsLocationsVolumesSnapshotsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a snapshot.

      Args:
        request: (NetappProjectsLocationsVolumesSnapshotsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/snapshots/{snapshotsId}',
        http_method='DELETE',
        method_id='netapp.projects.locations.volumes.snapshots.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsVolumesSnapshotsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Describe a snapshot for a volume.

      Args:
        request: (NetappProjectsLocationsVolumesSnapshotsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Snapshot) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/snapshots/{snapshotsId}',
        http_method='GET',
        method_id='netapp.projects.locations.volumes.snapshots.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsVolumesSnapshotsGetRequest',
        response_type_name='Snapshot',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns descriptions of all snapshots for a volume.

      Args:
        request: (NetappProjectsLocationsVolumesSnapshotsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSnapshotsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/snapshots',
        http_method='GET',
        method_id='netapp.projects.locations.volumes.snapshots.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/snapshots',
        request_field='',
        request_type_name='NetappProjectsLocationsVolumesSnapshotsListRequest',
        response_type_name='ListSnapshotsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the settings of a specific snapshot.

      Args:
        request: (NetappProjectsLocationsVolumesSnapshotsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/snapshots/{snapshotsId}',
        http_method='PATCH',
        method_id='netapp.projects.locations.volumes.snapshots.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='snapshot',
        request_type_name='NetappProjectsLocationsVolumesSnapshotsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsVolumesService(base_api.BaseApiService):
    """Service class for the projects_locations_volumes resource."""

    _NAME = 'projects_locations_volumes'

    def __init__(self, client):
      super(NetappV1beta1.ProjectsLocationsVolumesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Volume in a given project and location.

      Args:
        request: (NetappProjectsLocationsVolumesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes',
        http_method='POST',
        method_id='netapp.projects.locations.volumes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['volumeId'],
        relative_path='v1beta1/{+parent}/volumes',
        request_field='volume',
        request_type_name='NetappProjectsLocationsVolumesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Volume.

      Args:
        request: (NetappProjectsLocationsVolumesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}',
        http_method='DELETE',
        method_id='netapp.projects.locations.volumes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsVolumesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def EstablishPeering(self, request, global_params=None):
      r"""Establish volume peering. This is used to establish cluster and svm peerings between the GCNV and OnPrem clusters.

      Args:
        request: (NetappProjectsLocationsVolumesEstablishPeeringRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('EstablishPeering')
      return self._RunMethod(
          config, request, global_params=global_params)

    EstablishPeering.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}:establishPeering',
        http_method='POST',
        method_id='netapp.projects.locations.volumes.establishPeering',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:establishPeering',
        request_field='establishVolumePeeringRequest',
        request_type_name='NetappProjectsLocationsVolumesEstablishPeeringRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Volume.

      Args:
        request: (NetappProjectsLocationsVolumesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Volume) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}',
        http_method='GET',
        method_id='netapp.projects.locations.volumes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsVolumesGetRequest',
        response_type_name='Volume',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Volumes in a given project.

      Args:
        request: (NetappProjectsLocationsVolumesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListVolumesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes',
        http_method='GET',
        method_id='netapp.projects.locations.volumes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/volumes',
        request_field='',
        request_type_name='NetappProjectsLocationsVolumesListRequest',
        response_type_name='ListVolumesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Volume.

      Args:
        request: (NetappProjectsLocationsVolumesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}',
        http_method='PATCH',
        method_id='netapp.projects.locations.volumes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='volume',
        request_type_name='NetappProjectsLocationsVolumesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Restore(self, request, global_params=None):
      r"""Restore files from a backup to a volume.

      Args:
        request: (NetappProjectsLocationsVolumesRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Restore')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restore.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}:restore',
        http_method='POST',
        method_id='netapp.projects.locations.volumes.restore',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:restore',
        request_field='restoreBackupFilesRequest',
        request_type_name='NetappProjectsLocationsVolumesRestoreRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Revert(self, request, global_params=None):
      r"""Revert an existing volume to a specified snapshot. Warning! This operation will permanently revert all changes made after the snapshot was created.

      Args:
        request: (NetappProjectsLocationsVolumesRevertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Revert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Revert.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}:revert',
        http_method='POST',
        method_id='netapp.projects.locations.volumes.revert',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:revert',
        request_field='revertVolumeRequest',
        request_type_name='NetappProjectsLocationsVolumesRevertRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(NetappV1beta1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (NetappProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='netapp.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='NetappProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (NetappProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='netapp.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+name}/locations',
        request_field='',
        request_type_name='NetappProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(NetappV1beta1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
