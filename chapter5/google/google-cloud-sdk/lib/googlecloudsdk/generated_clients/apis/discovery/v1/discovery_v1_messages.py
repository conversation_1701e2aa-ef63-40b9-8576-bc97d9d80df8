"""Generated message classes for discovery version v1.

Lets you discover information about other Google APIs, such as what APIs are
available, the resource and method details for each API
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'discovery'


class DirectoryList(_messages.Message):
  r"""A DirectoryList object.

  Messages:
    ItemsValueListEntry: A ItemsValueListEntry object.

  Fields:
    discoveryVersion: Indicate the version of the Discovery API used to
      generate this doc.
    items: The individual directory entries. One entry per api/version pair.
    kind: The kind for this response.
  """

  class ItemsValueListEntry(_messages.Message):
    r"""A ItemsValueListEntry object.

    Messages:
      IconsValue: Links to 16x16 and 32x32 icons representing the API.

    Fields:
      description: The description of this API.
      discoveryLink: A link to the discovery document.
      discoveryRestUrl: The url for the discovery REST document.
      documentationLink: A link to human readable documentation for the API.
      icons: Links to 16x16 and 32x32 icons representing the API.
      id: The id of this API.
      kind: The kind for this response.
      labels: Labels for the status of this API, such as labs or deprecated.
      name: The name of the API.
      preferred: True if this version is the preferred version to use.
      title: The title of this API.
      version: The version of the API.
    """

    class IconsValue(_messages.Message):
      r"""Links to 16x16 and 32x32 icons representing the API.

      Fields:
        x16: The url of the 16x16 icon.
        x32: The url of the 32x32 icon.
      """

      x16 = _messages.StringField(1)
      x32 = _messages.StringField(2)

    description = _messages.StringField(1)
    discoveryLink = _messages.StringField(2)
    discoveryRestUrl = _messages.StringField(3)
    documentationLink = _messages.StringField(4)
    icons = _messages.MessageField('IconsValue', 5)
    id = _messages.StringField(6)
    kind = _messages.StringField(7, default='discovery#directoryItem')
    labels = _messages.StringField(8, repeated=True)
    name = _messages.StringField(9)
    preferred = _messages.BooleanField(10)
    title = _messages.StringField(11)
    version = _messages.StringField(12)

  discoveryVersion = _messages.StringField(1, default='v1')
  items = _messages.MessageField('ItemsValueListEntry', 2, repeated=True)
  kind = _messages.StringField(3, default='discovery#directoryList')


class DiscoveryApisGetRestRequest(_messages.Message):
  r"""A DiscoveryApisGetRestRequest object.

  Fields:
    api: The name of the API.
    version: The version of the API.
  """

  api = _messages.StringField(1, required=True)
  version = _messages.StringField(2, required=True)


class DiscoveryApisListRequest(_messages.Message):
  r"""A DiscoveryApisListRequest object.

  Enums:
    LabelValueValuesEnum: Only include APIs with a matching label, such as
      'graduated' or 'labs'.

  Fields:
    label: Only include APIs with a matching label, such as 'graduated' or
      'labs'.
    name: Only include APIs with the given name.
    preferred: Return only the preferred version of an API.
  """

  class LabelValueValuesEnum(_messages.Enum):
    r"""Only include APIs with a matching label, such as 'graduated' or
    'labs'.

    Values:
      deprecated: APIs that have been deprecated.
      graduated: Supported APIs that have graduated from labs.
      labs: APIs that are experimental
    """
    deprecated = 0
    graduated = 1
    labs = 2

  label = _messages.EnumField('LabelValueValuesEnum', 1)
  name = _messages.StringField(2)
  preferred = _messages.BooleanField(3, default=False)


class JsonSchema(_messages.Message):
  r"""A JsonSchema object.

  Messages:
    AnnotationsValue: Additional information about this property.
    PropertiesValue: If this is a schema for an object, list the schema for
      each property of this object.

  Fields:
    _ref: A reference to another schema. The value of this property is the
      "id" of another schema.
    additionalProperties: If this is a schema for an object, this property is
      the schema for any additional properties with dynamic keys on this
      object.
    annotations: Additional information about this property.
    default: The default value of this property (if one exists).
    description: A description of this object.
    enum: Values this parameter may take (if it is an enum).
    enumDescriptions: The descriptions for the enums. Each position maps to
      the corresponding value in the "enum" array.
    format: An additional regular expression or key that helps constrain the
      value. For more details see: http://tools.ietf.org/html/draft-zyp-json-
      schema-03#section-5.23
    id: Unique identifier for this schema.
    items: If this is a schema for an array, this property is the schema for
      each element in the array.
    location: Whether this parameter goes in the query or the path for REST
      requests.
    maximum: The maximum value of this parameter.
    minimum: The minimum value of this parameter.
    pattern: The regular expression this parameter must conform to. Uses Java
      6 regex format:
      http://docs.oracle.com/javase/6/docs/api/java/util/regex/Pattern.html
    properties: If this is a schema for an object, list the schema for each
      property of this object.
    readOnly: The value is read-only, generated by the service. The value can
      not be modified by the client. It the value is included in a POST, PUT
      or PATCH request, it will be ignored by the service.
    repeated: Whether this parameter may appear multiple times.
    required: Whether the parameter is required.
    type: The value type for this schema. A list of values can be found here:
      http://tools.ietf.org/html/draft-zyp-json-schema-03#section-5.1
  """

  class AnnotationsValue(_messages.Message):
    r"""Additional information about this property.

    Fields:
      required: A list of methods for which this property is required on
        requests.
    """

    required = _messages.StringField(1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PropertiesValue(_messages.Message):
    r"""If this is a schema for an object, list the schema for each property
    of this object.

    Messages:
      AdditionalProperty: An additional property for a PropertiesValue object.

    Fields:
      additionalProperties: A single property of this object. The value is
        itself a JSON Schema object describing this property.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A JsonSchema attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('JsonSchema', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  _ref = _messages.StringField(1)
  additionalProperties = _messages.MessageField('JsonSchema', 2)
  annotations = _messages.MessageField('AnnotationsValue', 3)
  default = _messages.StringField(4)
  description = _messages.StringField(5)
  enum = _messages.StringField(6, repeated=True)
  enumDescriptions = _messages.StringField(7, repeated=True)
  format = _messages.StringField(8)
  id = _messages.StringField(9)
  items = _messages.MessageField('JsonSchema', 10)
  location = _messages.StringField(11)
  maximum = _messages.StringField(12)
  minimum = _messages.StringField(13)
  pattern = _messages.StringField(14)
  properties = _messages.MessageField('PropertiesValue', 15)
  readOnly = _messages.BooleanField(16)
  repeated = _messages.BooleanField(17)
  required = _messages.BooleanField(18)
  type = _messages.StringField(19)


class RestDescription(_messages.Message):
  r"""A RestDescription object.

  Messages:
    AuthValue: Authentication information.
    IconsValue: Links to 16x16 and 32x32 icons representing the API.
    MethodsValue: API-level methods for this API.
    ParametersValue: Common parameters that apply across all apis.
    ResourcesValue: The resources in this API.
    SchemasValue: The schemas for this API.

  Fields:
    auth: Authentication information.
    basePath: [DEPRECATED] The base path for REST requests.
    baseUrl: [DEPRECATED] The base URL for REST requests.
    batchPath: The path for REST batch requests.
    canonicalName: Indicates how the API name should be capitalized and split
      into various parts. Useful for generating pretty class names.
    description: The description of this API.
    discoveryVersion: Indicate the version of the Discovery API used to
      generate this doc.
    documentationLink: A link to human readable documentation for the API.
    etag: The etag for this response.
    features: A list of supported features for this API.
    icons: Links to 16x16 and 32x32 icons representing the API.
    id: The id of this API.
    kind: The kind for this response.
    labels: Labels for the status of this API, such as labs or deprecated.
    methods: API-level methods for this API.
    name: The name of this API.
    parameters: Common parameters that apply across all apis.
    protocol: The protocol described by this document.
    resources: The resources in this API.
    revision: The version of this API.
    rootUrl: The root url under which all API services live.
    schemas: The schemas for this API.
    servicePath: The base path for all REST requests.
    title: The title of this API.
    version: The version of this API.
  """

  class AuthValue(_messages.Message):
    r"""Authentication information.

    Messages:
      Oauth2Value: OAuth 2.0 authentication information.

    Fields:
      oauth2: OAuth 2.0 authentication information.
    """

    class Oauth2Value(_messages.Message):
      r"""OAuth 2.0 authentication information.

      Messages:
        ScopesValue: Available OAuth 2.0 scopes.

      Fields:
        scopes: Available OAuth 2.0 scopes.
      """

      @encoding.MapUnrecognizedFields('additionalProperties')
      class ScopesValue(_messages.Message):
        r"""Available OAuth 2.0 scopes.

        Messages:
          AdditionalProperty: An additional property for a ScopesValue object.

        Fields:
          additionalProperties: The scope value.
        """

        class AdditionalProperty(_messages.Message):
          r"""An additional property for a ScopesValue object.

          Messages:
            ValueValue: A ValueValue object.

          Fields:
            key: Name of the additional property.
            value: A ValueValue attribute.
          """

          class ValueValue(_messages.Message):
            r"""A ValueValue object.

            Fields:
              description: Description of scope.
            """

            description = _messages.StringField(1)

          key = _messages.StringField(1)
          value = _messages.MessageField('ValueValue', 2)

        additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

      scopes = _messages.MessageField('ScopesValue', 1)

    oauth2 = _messages.MessageField('Oauth2Value', 1)

  class IconsValue(_messages.Message):
    r"""Links to 16x16 and 32x32 icons representing the API.

    Fields:
      x16: The url of the 16x16 icon.
      x32: The url of the 32x32 icon.
    """

    x16 = _messages.StringField(1)
    x32 = _messages.StringField(2)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MethodsValue(_messages.Message):
    r"""API-level methods for this API.

    Messages:
      AdditionalProperty: An additional property for a MethodsValue object.

    Fields:
      additionalProperties: An individual method description.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MethodsValue object.

      Fields:
        key: Name of the additional property.
        value: A RestMethod attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('RestMethod', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParametersValue(_messages.Message):
    r"""Common parameters that apply across all apis.

    Messages:
      AdditionalProperty: An additional property for a ParametersValue object.

    Fields:
      additionalProperties: Description of a single parameter.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A JsonSchema attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('JsonSchema', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourcesValue(_messages.Message):
    r"""The resources in this API.

    Messages:
      AdditionalProperty: An additional property for a ResourcesValue object.

    Fields:
      additionalProperties: An individual resource description. Contains
        methods and sub-resources related to this resource.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourcesValue object.

      Fields:
        key: Name of the additional property.
        value: A RestResource attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('RestResource', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SchemasValue(_messages.Message):
    r"""The schemas for this API.

    Messages:
      AdditionalProperty: An additional property for a SchemasValue object.

    Fields:
      additionalProperties: An individual schema description.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SchemasValue object.

      Fields:
        key: Name of the additional property.
        value: A JsonSchema attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('JsonSchema', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  auth = _messages.MessageField('AuthValue', 1)
  basePath = _messages.StringField(2)
  baseUrl = _messages.StringField(3)
  batchPath = _messages.StringField(4, default='batch')
  canonicalName = _messages.StringField(5)
  description = _messages.StringField(6)
  discoveryVersion = _messages.StringField(7, default='v1')
  documentationLink = _messages.StringField(8)
  etag = _messages.StringField(9)
  features = _messages.StringField(10, repeated=True)
  icons = _messages.MessageField('IconsValue', 11)
  id = _messages.StringField(12)
  kind = _messages.StringField(13, default='discovery#restDescription')
  labels = _messages.StringField(14, repeated=True)
  methods = _messages.MessageField('MethodsValue', 15)
  name = _messages.StringField(16)
  parameters = _messages.MessageField('ParametersValue', 17)
  protocol = _messages.StringField(18, default='rest')
  resources = _messages.MessageField('ResourcesValue', 19)
  revision = _messages.StringField(20)
  rootUrl = _messages.StringField(21)
  schemas = _messages.MessageField('SchemasValue', 22)
  servicePath = _messages.StringField(23)
  title = _messages.StringField(24)
  version = _messages.StringField(25)


class RestMethod(_messages.Message):
  r"""A RestMethod object.

  Messages:
    MediaUploadValue: Media upload parameters.
    ParametersValue: Details for all parameters in this method.
    RequestValue: The schema for the request.
    ResponseValue: The schema for the response.

  Fields:
    description: Description of this method.
    etagRequired: Whether this method requires an ETag to be specified. The
      ETag is sent as an HTTP If-Match or If-None-Match header.
    httpMethod: HTTP method used by this method.
    id: A unique ID for this method. This property can be used to match
      methods between different versions of Discovery.
    mediaUpload: Media upload parameters.
    parameterOrder: Ordered list of required parameters, serves as a hint to
      clients on how to structure their method signatures. The array is
      ordered such that the "most-significant" parameter appears first.
    parameters: Details for all parameters in this method.
    path: The URI path of this REST method. Should be used in conjunction with
      the basePath property at the api-level.
    request: The schema for the request.
    response: The schema for the response.
    scopes: OAuth 2.0 scopes applicable to this method.
    supportsMediaDownload: Whether this method supports media downloads.
    supportsMediaUpload: Whether this method supports media uploads.
    supportsSubscription: Whether this method supports subscriptions.
  """

  class MediaUploadValue(_messages.Message):
    r"""Media upload parameters.

    Messages:
      ProtocolsValue: Supported upload protocols.

    Fields:
      accept: MIME Media Ranges for acceptable media uploads to this method.
      maxSize: Maximum size of a media upload, such as "1MB", "2GB" or "3TB".
      protocols: Supported upload protocols.
    """

    class ProtocolsValue(_messages.Message):
      r"""Supported upload protocols.

      Messages:
        ResumableValue: Supports the Resumable Media Upload protocol.
        SimpleValue: Supports uploading as a single HTTP request.

      Fields:
        resumable: Supports the Resumable Media Upload protocol.
        simple: Supports uploading as a single HTTP request.
      """

      class ResumableValue(_messages.Message):
        r"""Supports the Resumable Media Upload protocol.

        Fields:
          multipart: True if this endpoint supports uploading multipart media.
          path: The URI path to be used for upload. Should be used in
            conjunction with the basePath property at the api-level.
        """

        multipart = _messages.BooleanField(1, default=True)
        path = _messages.StringField(2)

      class SimpleValue(_messages.Message):
        r"""Supports uploading as a single HTTP request.

        Fields:
          multipart: True if this endpoint supports upload multipart media.
          path: The URI path to be used for upload. Should be used in
            conjunction with the basePath property at the api-level.
        """

        multipart = _messages.BooleanField(1, default=True)
        path = _messages.StringField(2)

      resumable = _messages.MessageField('ResumableValue', 1)
      simple = _messages.MessageField('SimpleValue', 2)

    accept = _messages.StringField(1, repeated=True)
    maxSize = _messages.StringField(2)
    protocols = _messages.MessageField('ProtocolsValue', 3)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParametersValue(_messages.Message):
    r"""Details for all parameters in this method.

    Messages:
      AdditionalProperty: An additional property for a ParametersValue object.

    Fields:
      additionalProperties: Details for a single parameter in this method.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A JsonSchema attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('JsonSchema', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  class RequestValue(_messages.Message):
    r"""The schema for the request.

    Fields:
      _ref: Schema ID for the request schema.
    """

    _ref = _messages.StringField(1)

  class ResponseValue(_messages.Message):
    r"""The schema for the response.

    Fields:
      _ref: Schema ID for the response schema.
    """

    _ref = _messages.StringField(1)

  description = _messages.StringField(1)
  etagRequired = _messages.BooleanField(2)
  httpMethod = _messages.StringField(3)
  id = _messages.StringField(4)
  mediaUpload = _messages.MessageField('MediaUploadValue', 5)
  parameterOrder = _messages.StringField(6, repeated=True)
  parameters = _messages.MessageField('ParametersValue', 7)
  path = _messages.StringField(8)
  request = _messages.MessageField('RequestValue', 9)
  response = _messages.MessageField('ResponseValue', 10)
  scopes = _messages.StringField(11, repeated=True)
  supportsMediaDownload = _messages.BooleanField(12)
  supportsMediaUpload = _messages.BooleanField(13)
  supportsSubscription = _messages.BooleanField(14)


class RestResource(_messages.Message):
  r"""A RestResource object.

  Messages:
    MethodsValue: Methods on this resource.
    ResourcesValue: Sub-resources on this resource.

  Fields:
    methods: Methods on this resource.
    resources: Sub-resources on this resource.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MethodsValue(_messages.Message):
    r"""Methods on this resource.

    Messages:
      AdditionalProperty: An additional property for a MethodsValue object.

    Fields:
      additionalProperties: Description for any methods on this resource.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MethodsValue object.

      Fields:
        key: Name of the additional property.
        value: A RestMethod attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('RestMethod', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourcesValue(_messages.Message):
    r"""Sub-resources on this resource.

    Messages:
      AdditionalProperty: An additional property for a ResourcesValue object.

    Fields:
      additionalProperties: Description for any sub-resources on this
        resource.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourcesValue object.

      Fields:
        key: Name of the additional property.
        value: A RestResource attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('RestResource', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  methods = _messages.MessageField('MethodsValue', 1)
  resources = _messages.MessageField('ResourcesValue', 2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    AltValueValuesEnum: Data format for the response.

  Fields:
    alt: Data format for the response.
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters. Overrides userIp if both are provided.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    userIp: IP address of the site where the request originates. Use this if
      you want to enforce per-user limits.
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for the response.

    Values:
      json: Responses with Content-Type of application/json
    """
    json = 0

  alt = _messages.EnumField('AltValueValuesEnum', 1, default='json')
  fields = _messages.StringField(2)
  key = _messages.StringField(3)
  oauth_token = _messages.StringField(4)
  prettyPrint = _messages.BooleanField(5, default=True)
  quotaUser = _messages.StringField(6)
  trace = _messages.StringField(7)
  userIp = _messages.StringField(8)


encoding.AddCustomJsonFieldMapping(
    JsonSchema, '_ref', '$ref')
encoding.AddCustomJsonFieldMapping(
    RestMethod.RequestValue, '_ref', '$ref')
encoding.AddCustomJsonFieldMapping(
    RestMethod.ResponseValue, '_ref', '$ref')
