"""Generated message classes for publicca version v1alpha1.

The Public Certificate Authority API may be used to create and manage ACME
external account binding keys associated with Google Trust Services' publicly
trusted certificate authority.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'publicca'


class ExternalAccountKey(_messages.Message):
  r"""A representation of an ExternalAccountKey used for [external account
  binding](https://tools.ietf.org/html/rfc8555#section-7.3.4) within ACME.

  Fields:
    b64MacKey: Output only. Base64-URL-encoded HS256 key. It is generated by
      the PublicCertificateAuthorityService when the ExternalAccountKey is
      created
    keyId: Output only. Key ID. It is generated by the
      PublicCertificateAuthorityService when the ExternalAccountKey is created
    name: Output only. Resource name.
      projects/{project}/locations/{location}/externalAccountKeys/{key_id}
  """

  b64MacKey = _messages.BytesField(1)
  keyId = _messages.StringField(2)
  name = _messages.StringField(3)


class PubliccaProjectsLocationsExternalAccountKeysCreateRequest(_messages.Message):
  r"""A PubliccaProjectsLocationsExternalAccountKeysCreateRequest object.

  Fields:
    externalAccountKey: A ExternalAccountKey resource to be passed as the
      request body.
    parent: Required. The parent resource where this external_account_key will
      be created. Format:
      projects/[project_id]/locations/[location]/externalAccountKeys.
  """

  externalAccountKey = _messages.MessageField('ExternalAccountKey', 1)
  parent = _messages.StringField(2, required=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
