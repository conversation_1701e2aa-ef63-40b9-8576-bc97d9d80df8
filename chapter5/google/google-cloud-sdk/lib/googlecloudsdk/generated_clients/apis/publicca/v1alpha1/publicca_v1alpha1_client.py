"""Generated client library for publicca version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.publicca.v1alpha1 import publicca_v1alpha1_messages as messages


class PubliccaV1alpha1(base_api.BaseApiClient):
  """Generated client library for service publicca version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://publicca.googleapis.com/'
  MTLS_BASE_URL = 'https://publicca.mtls.googleapis.com/'

  _PACKAGE = 'publicca'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'PubliccaV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new publicca handle."""
    url = url or self.BASE_URL
    super(PubliccaV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_externalAccountKeys = self.ProjectsLocationsExternalAccountKeysService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsExternalAccountKeysService(base_api.BaseApiService):
    """Service class for the projects_locations_externalAccountKeys resource."""

    _NAME = 'projects_locations_externalAccountKeys'

    def __init__(self, client):
      super(PubliccaV1alpha1.ProjectsLocationsExternalAccountKeysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ExternalAccountKey bound to the project.

      Args:
        request: (PubliccaProjectsLocationsExternalAccountKeysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ExternalAccountKey) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/externalAccountKeys',
        http_method='POST',
        method_id='publicca.projects.locations.externalAccountKeys.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/externalAccountKeys',
        request_field='externalAccountKey',
        request_type_name='PubliccaProjectsLocationsExternalAccountKeysCreateRequest',
        response_type_name='ExternalAccountKey',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(PubliccaV1alpha1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(PubliccaV1alpha1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
