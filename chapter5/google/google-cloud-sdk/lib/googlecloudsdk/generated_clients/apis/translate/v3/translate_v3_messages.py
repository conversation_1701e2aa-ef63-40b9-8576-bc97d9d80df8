"""Generated message classes for translate version v3.

Integrates text translation into your website or application.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'translate'


class AdaptiveMtDataset(_messages.Message):
  r"""An Adaptive MT Dataset.

  Fields:
    createTime: Output only. Timestamp when this dataset was created.
    displayName: The name of the dataset to show in the interface. The name
      can be up to 32 characters long and can consist only of ASCII Latin
      letters A-Z and a-z, underscores (_), and ASCII digits 0-9.
    exampleCount: The number of examples in the dataset.
    name: Required. The resource name of the dataset, in form of
      `projects/{project-number-or-
      id}/locations/{location_id}/adaptiveMtDatasets/{dataset_id}`
    sourceLanguageCode: The BCP-47 language code of the source language.
    targetLanguageCode: The BCP-47 language code of the target language.
    updateTime: Output only. Timestamp when this dataset was last updated.
  """

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  exampleCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  name = _messages.StringField(4)
  sourceLanguageCode = _messages.StringField(5)
  targetLanguageCode = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class AdaptiveMtFile(_messages.Message):
  r"""An AdaptiveMtFile.

  Fields:
    createTime: Output only. Timestamp when this file was created.
    displayName: The file's display name.
    entryCount: The number of entries that the file contains.
    name: Required. The resource name of the file, in form of
      `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatas
      ets/{dataset}/adaptiveMtFiles/{file}`
    updateTime: Output only. Timestamp when this file was last updated.
  """

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  entryCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  name = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class AdaptiveMtSentence(_messages.Message):
  r"""An AdaptiveMt sentence entry.

  Fields:
    createTime: Output only. Timestamp when this sentence was created.
    name: Required. The resource name of the file, in form of
      `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatas
      ets/{dataset}/adaptiveMtFiles/{file}/adaptiveMtSentences/{sentence}`
    sourceSentence: Required. The source sentence.
    targetSentence: Required. The target sentence.
    updateTime: Output only. Timestamp when this sentence was last updated.
  """

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  sourceSentence = _messages.StringField(3)
  targetSentence = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class AdaptiveMtTranslateRequest(_messages.Message):
  r"""The request for sending an AdaptiveMt translation query.

  Fields:
    content: Required. The content of the input in string format.
    dataset: Required. The resource name for the dataset to use for adaptive
      MT. `projects/{project}/locations/{location-
      id}/adaptiveMtDatasets/{dataset}`
    glossaryConfig: Optional. Glossary to be applied. The glossary must be
      within the same region (have the same location-id) as the model,
      otherwise an INVALID_ARGUMENT (400) error is returned.
    referenceSentenceConfig: Configuration for caller provided reference
      sentences.
  """

  content = _messages.StringField(1, repeated=True)
  dataset = _messages.StringField(2)
  glossaryConfig = _messages.MessageField('GlossaryConfig', 3)
  referenceSentenceConfig = _messages.MessageField('ReferenceSentenceConfig', 4)


class AdaptiveMtTranslateResponse(_messages.Message):
  r"""An AdaptiveMtTranslate response.

  Fields:
    glossaryTranslations: Text translation response if a glossary is provided
      in the request. This could be the same as 'translation' above if no
      terms apply.
    languageCode: Output only. The translation's language code.
    translations: Output only. The translation.
  """

  glossaryTranslations = _messages.MessageField('AdaptiveMtTranslation', 1, repeated=True)
  languageCode = _messages.StringField(2)
  translations = _messages.MessageField('AdaptiveMtTranslation', 3, repeated=True)


class AdaptiveMtTranslation(_messages.Message):
  r"""An AdaptiveMt translation.

  Fields:
    translatedText: Output only. The translated text.
  """

  translatedText = _messages.StringField(1)


class BatchDocumentInputConfig(_messages.Message):
  r"""Input configuration for BatchTranslateDocument request.

  Fields:
    gcsSource: Google Cloud Storage location for the source input. This can be
      a single file (for example, `gs://translation-test/input.docx`) or a
      wildcard (for example, `gs://translation-test/*`). File mime type is
      determined based on extension. Supported mime type includes: - `pdf`,
      application/pdf - `docx`, application/vnd.openxmlformats-
      officedocument.wordprocessingml.document - `pptx`,
      application/vnd.openxmlformats-
      officedocument.presentationml.presentation - `xlsx`,
      application/vnd.openxmlformats-officedocument.spreadsheetml.sheet The
      max file size to support for `.docx`, `.pptx` and `.xlsx` is 100MB. The
      max file size to support for `.pdf` is 1GB and the max page limit is
      1000 pages. The max file size to support for all input documents is 1GB.
  """

  gcsSource = _messages.MessageField('GcsSource', 1)


class BatchDocumentOutputConfig(_messages.Message):
  r"""Output configuration for BatchTranslateDocument request.

  Fields:
    gcsDestination: Google Cloud Storage destination for output content. For
      every single input document (for example, gs://a/b/c.[extension]), we
      generate at most 2 * n output files. (n is the # of
      target_language_codes in the BatchTranslateDocumentRequest). While the
      input documents are being processed, we write/update an index file
      `index.csv` under `gcs_destination.output_uri_prefix` (for example,
      gs://translation_output/index.csv) The index file is generated/updated
      as new files are being translated. The format is:
      input_document,target_language_code,translation_output,error_output,
      glossary_translation_output,glossary_error_output `input_document` is
      one file we matched using gcs_source.input_uri. `target_language_code`
      is provided in the request. `translation_output` contains the
      translations. (details provided below) `error_output` contains the error
      message during processing of the file. Both translations_file and
      errors_file could be empty strings if we have no content to output.
      `glossary_translation_output` and `glossary_error_output` are the
      translated output/error when we apply glossaries. They could also be
      empty if we have no content to output. Once a row is present in
      index.csv, the input/output matching never changes. Callers should also
      expect all the content in input_file are processed and ready to be
      consumed (that is, no partial output file is written). Since index.csv
      will be keeping updated during the process, please make sure there is no
      custom retention policy applied on the output bucket that may avoid file
      updating. (https://cloud.google.com/storage/docs/bucket-lock#retention-
      policy) The naming format of translation output files follows (for
      target language code [trg]): `translation_output`:
      `gs://translation_output/a_b_c_[trg]_translation.[extension]`
      `glossary_translation_output`:
      `gs://translation_test/a_b_c_[trg]_glossary_translation.[extension]`.
      The output document will maintain the same file format as the input
      document. The naming format of error output files follows (for target
      language code [trg]): `error_output`:
      `gs://translation_test/a_b_c_[trg]_errors.txt` `glossary_error_output`:
      `gs://translation_test/a_b_c_[trg]_glossary_translation.txt`. The error
      output is a txt file containing error details.
  """

  gcsDestination = _messages.MessageField('GcsDestination', 1)


class BatchTranslateDocumentRequest(_messages.Message):
  r"""The BatchTranslateDocument request.

  Messages:
    FormatConversionsValue: Optional. The file format conversion map that is
      applied to all input files. The map key is the original mime_type. The
      map value is the target mime_type of translated documents. Supported
      file format conversion includes: - `application/pdf` to
      `application/vnd.openxmlformats-
      officedocument.wordprocessingml.document` If nothing specified, output
      files will be in the same format as the original file.
    GlossariesValue: Optional. Glossaries to be applied. It's keyed by target
      language code.
    ModelsValue: Optional. The models to use for translation. Map's key is
      target language code. Map's value is the model name. Value can be a
      built-in general model, or an AutoML Translation model. The value format
      depends on model type: - AutoML Translation models: `projects/{project-
      number-or-id}/locations/{location-id}/models/{model-id}` - General
      (built-in) models: `projects/{project-number-or-id}/locations/{location-
      id}/models/general/nmt`, If the map is empty or a specific model is not
      requested for a language pair, then default google model (nmt) is used.

  Fields:
    customizedAttribution: Optional. This flag is to support user customized
      attribution. If not provided, the default is `Machine Translated by
      Google`. Customized attribution should follow rules in
      https://cloud.google.com/translate/attribution#attribution_and_logos
    enableRotationCorrection: Optional. If true, enable auto rotation
      correction in DVS.
    enableShadowRemovalNativePdf: Optional. If true, use the text removal
      server to remove the shadow text on background image for native pdf
      translation. Shadow removal feature can only be enabled when
      is_translate_native_pdf_only: false && pdf_native_only: false
    formatConversions: Optional. The file format conversion map that is
      applied to all input files. The map key is the original mime_type. The
      map value is the target mime_type of translated documents. Supported
      file format conversion includes: - `application/pdf` to
      `application/vnd.openxmlformats-
      officedocument.wordprocessingml.document` If nothing specified, output
      files will be in the same format as the original file.
    glossaries: Optional. Glossaries to be applied. It's keyed by target
      language code.
    inputConfigs: Required. Input configurations. The total number of files
      matched should be <= 100. The total content size to translate should be
      <= 100M Unicode codepoints. The files must use UTF-8 encoding.
    models: Optional. The models to use for translation. Map's key is target
      language code. Map's value is the model name. Value can be a built-in
      general model, or an AutoML Translation model. The value format depends
      on model type: - AutoML Translation models: `projects/{project-number-
      or-id}/locations/{location-id}/models/{model-id}` - General (built-in)
      models: `projects/{project-number-or-id}/locations/{location-
      id}/models/general/nmt`, If the map is empty or a specific model is not
      requested for a language pair, then default google model (nmt) is used.
    outputConfig: Required. Output configuration. If 2 input configs match to
      the same file (that is, same input path), we don't generate output for
      duplicate inputs.
    sourceLanguageCode: Required. The ISO-639 language code of the input
      document if known, for example, "en-US" or "sr-Latn". Supported language
      codes are listed in [Language
      Support](https://cloud.google.com/translate/docs/languages).
    targetLanguageCodes: Required. The ISO-639 language code to use for
      translation of the input document. Specify up to 10 language codes here.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FormatConversionsValue(_messages.Message):
    r"""Optional. The file format conversion map that is applied to all input
    files. The map key is the original mime_type. The map value is the target
    mime_type of translated documents. Supported file format conversion
    includes: - `application/pdf` to `application/vnd.openxmlformats-
    officedocument.wordprocessingml.document` If nothing specified, output
    files will be in the same format as the original file.

    Messages:
      AdditionalProperty: An additional property for a FormatConversionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        FormatConversionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FormatConversionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class GlossariesValue(_messages.Message):
    r"""Optional. Glossaries to be applied. It's keyed by target language
    code.

    Messages:
      AdditionalProperty: An additional property for a GlossariesValue object.

    Fields:
      additionalProperties: Additional properties of type GlossariesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a GlossariesValue object.

      Fields:
        key: Name of the additional property.
        value: A TranslateTextGlossaryConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('TranslateTextGlossaryConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModelsValue(_messages.Message):
    r"""Optional. The models to use for translation. Map's key is target
    language code. Map's value is the model name. Value can be a built-in
    general model, or an AutoML Translation model. The value format depends on
    model type: - AutoML Translation models: `projects/{project-number-or-
    id}/locations/{location-id}/models/{model-id}` - General (built-in)
    models: `projects/{project-number-or-id}/locations/{location-
    id}/models/general/nmt`, If the map is empty or a specific model is not
    requested for a language pair, then default google model (nmt) is used.

    Messages:
      AdditionalProperty: An additional property for a ModelsValue object.

    Fields:
      additionalProperties: Additional properties of type ModelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customizedAttribution = _messages.StringField(1)
  enableRotationCorrection = _messages.BooleanField(2)
  enableShadowRemovalNativePdf = _messages.BooleanField(3)
  formatConversions = _messages.MessageField('FormatConversionsValue', 4)
  glossaries = _messages.MessageField('GlossariesValue', 5)
  inputConfigs = _messages.MessageField('BatchDocumentInputConfig', 6, repeated=True)
  models = _messages.MessageField('ModelsValue', 7)
  outputConfig = _messages.MessageField('BatchDocumentOutputConfig', 8)
  sourceLanguageCode = _messages.StringField(9)
  targetLanguageCodes = _messages.StringField(10, repeated=True)


class BatchTranslateTextRequest(_messages.Message):
  r"""The batch translation request.

  Messages:
    GlossariesValue: Optional. Glossaries to be applied for translation. It's
      keyed by target language code.
    LabelsValue: Optional. The labels with user-defined metadata for the
      request. Label keys and values can be no longer than 63 characters
      (Unicode codepoints), can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter.
      See https://cloud.google.com/translate/docs/advanced/labels for more
      information.
    ModelsValue: Optional. The models to use for translation. Map's key is
      target language code. Map's value is model name. Value can be a built-in
      general model, or an AutoML Translation model. The value format depends
      on model type: - AutoML Translation models: `projects/{project-number-
      or-id}/locations/{location-id}/models/{model-id}` - General (built-in)
      models: `projects/{project-number-or-id}/locations/{location-
      id}/models/general/nmt`, If the map is empty or a specific model is not
      requested for a language pair, then default google model (nmt) is used.

  Fields:
    glossaries: Optional. Glossaries to be applied for translation. It's keyed
      by target language code.
    inputConfigs: Required. Input configurations. The total number of files
      matched should be <= 100. The total content size should be <= 100M
      Unicode codepoints. The files must use UTF-8 encoding.
    labels: Optional. The labels with user-defined metadata for the request.
      Label keys and values can be no longer than 63 characters (Unicode
      codepoints), can only contain lowercase letters, numeric characters,
      underscores and dashes. International characters are allowed. Label
      values are optional. Label keys must start with a letter. See
      https://cloud.google.com/translate/docs/advanced/labels for more
      information.
    models: Optional. The models to use for translation. Map's key is target
      language code. Map's value is model name. Value can be a built-in
      general model, or an AutoML Translation model. The value format depends
      on model type: - AutoML Translation models: `projects/{project-number-
      or-id}/locations/{location-id}/models/{model-id}` - General (built-in)
      models: `projects/{project-number-or-id}/locations/{location-
      id}/models/general/nmt`, If the map is empty or a specific model is not
      requested for a language pair, then default google model (nmt) is used.
    outputConfig: Required. Output configuration. If 2 input configs match to
      the same file (that is, same input path), we don't generate output for
      duplicate inputs.
    sourceLanguageCode: Required. Source language code.
    targetLanguageCodes: Required. Specify up to 10 language codes here.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class GlossariesValue(_messages.Message):
    r"""Optional. Glossaries to be applied for translation. It's keyed by
    target language code.

    Messages:
      AdditionalProperty: An additional property for a GlossariesValue object.

    Fields:
      additionalProperties: Additional properties of type GlossariesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a GlossariesValue object.

      Fields:
        key: Name of the additional property.
        value: A TranslateTextGlossaryConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('TranslateTextGlossaryConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels with user-defined metadata for the request. Label
    keys and values can be no longer than 63 characters (Unicode codepoints),
    can only contain lowercase letters, numeric characters, underscores and
    dashes. International characters are allowed. Label values are optional.
    Label keys must start with a letter. See
    https://cloud.google.com/translate/docs/advanced/labels for more
    information.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModelsValue(_messages.Message):
    r"""Optional. The models to use for translation. Map's key is target
    language code. Map's value is model name. Value can be a built-in general
    model, or an AutoML Translation model. The value format depends on model
    type: - AutoML Translation models: `projects/{project-number-or-
    id}/locations/{location-id}/models/{model-id}` - General (built-in)
    models: `projects/{project-number-or-id}/locations/{location-
    id}/models/general/nmt`, If the map is empty or a specific model is not
    requested for a language pair, then default google model (nmt) is used.

    Messages:
      AdditionalProperty: An additional property for a ModelsValue object.

    Fields:
      additionalProperties: Additional properties of type ModelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  glossaries = _messages.MessageField('GlossariesValue', 1)
  inputConfigs = _messages.MessageField('InputConfig', 2, repeated=True)
  labels = _messages.MessageField('LabelsValue', 3)
  models = _messages.MessageField('ModelsValue', 4)
  outputConfig = _messages.MessageField('OutputConfig', 5)
  sourceLanguageCode = _messages.StringField(6)
  targetLanguageCodes = _messages.StringField(7, repeated=True)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Dataset(_messages.Message):
  r"""A dataset that hosts the examples (sentence pairs) used for translation
  models.

  Fields:
    createTime: Output only. Timestamp when this dataset was created.
    displayName: The name of the dataset to show in the interface. The name
      can be up to 32 characters long and can consist only of ASCII Latin
      letters A-Z and a-z, underscores (_), and ASCII digits 0-9.
    exampleCount: Output only. The number of examples in the dataset.
    name: The resource name of the dataset, in form of `projects/{project-
      number-or-id}/locations/{location_id}/datasets/{dataset_id}`
    sourceLanguageCode: The BCP-47 language code of the source language.
    targetLanguageCode: The BCP-47 language code of the target language.
    testExampleCount: Output only. Number of test examples (sentence pairs).
    trainExampleCount: Output only. Number of training examples (sentence
      pairs).
    updateTime: Output only. Timestamp when this dataset was last updated.
    validateExampleCount: Output only. Number of validation examples (sentence
      pairs).
  """

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  exampleCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  name = _messages.StringField(4)
  sourceLanguageCode = _messages.StringField(5)
  targetLanguageCode = _messages.StringField(6)
  testExampleCount = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  trainExampleCount = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  updateTime = _messages.StringField(9)
  validateExampleCount = _messages.IntegerField(10, variant=_messages.Variant.INT32)


class DatasetInputConfig(_messages.Message):
  r"""Input configuration for datasets.

  Fields:
    inputFiles: Files containing the sentence pairs to be imported to the
      dataset.
  """

  inputFiles = _messages.MessageField('InputFile', 1, repeated=True)


class DatasetOutputConfig(_messages.Message):
  r"""Output configuration for datasets.

  Fields:
    gcsDestination: Google Cloud Storage destination to write the output.
  """

  gcsDestination = _messages.MessageField('GcsOutputDestination', 1)


class DetectLanguageRequest(_messages.Message):
  r"""The request message for language detection.

  Messages:
    LabelsValue: Optional. The labels with user-defined metadata for the
      request. Label keys and values can be no longer than 63 characters
      (Unicode codepoints), can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter.
      See https://cloud.google.com/translate/docs/advanced/labels for more
      information.

  Fields:
    content: The content of the input stored as a string.
    labels: Optional. The labels with user-defined metadata for the request.
      Label keys and values can be no longer than 63 characters (Unicode
      codepoints), can only contain lowercase letters, numeric characters,
      underscores and dashes. International characters are allowed. Label
      values are optional. Label keys must start with a letter. See
      https://cloud.google.com/translate/docs/advanced/labels for more
      information.
    mimeType: Optional. The format of the source text, for example,
      "text/html", "text/plain". If left blank, the MIME type defaults to
      "text/html".
    model: Optional. The language detection model to be used. Format:
      `projects/{project-number-or-id}/locations/{location-
      id}/models/language-detection/{model-id}` Only one language detection
      model is currently supported: `projects/{project-number-or-
      id}/locations/{location-id}/models/language-detection/default`. If not
      specified, the default model is used.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels with user-defined metadata for the request. Label
    keys and values can be no longer than 63 characters (Unicode codepoints),
    can only contain lowercase letters, numeric characters, underscores and
    dashes. International characters are allowed. Label values are optional.
    Label keys must start with a letter. See
    https://cloud.google.com/translate/docs/advanced/labels for more
    information.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  content = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  mimeType = _messages.StringField(3)
  model = _messages.StringField(4)


class DetectLanguageResponse(_messages.Message):
  r"""The response message for language detection.

  Fields:
    languages: The most probable language detected by the Translation API. For
      each request, the Translation API will always return only one result.
  """

  languages = _messages.MessageField('DetectedLanguage', 1, repeated=True)


class DetectedLanguage(_messages.Message):
  r"""The response message for language detection.

  Fields:
    confidence: The confidence of the detection result for this language.
    languageCode: The ISO-639 language code of the source content in the
      request, detected automatically.
  """

  confidence = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  languageCode = _messages.StringField(2)


class DocumentInputConfig(_messages.Message):
  r"""A document translation request input config.

  Fields:
    content: Document's content represented as a stream of bytes.
    gcsSource: Google Cloud Storage location. This must be a single file. For
      example: gs://example_bucket/example_file.pdf
    mimeType: Specifies the input document's mime_type. If not specified it
      will be determined using the file extension for gcs_source provided
      files. For a file provided through bytes content the mime_type must be
      provided. Currently supported mime types are: - application/pdf -
      application/vnd.openxmlformats-officedocument.wordprocessingml.document
      - application/vnd.openxmlformats-
      officedocument.presentationml.presentation -
      application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
  """

  content = _messages.BytesField(1)
  gcsSource = _messages.MessageField('GcsSource', 2)
  mimeType = _messages.StringField(3)


class DocumentOutputConfig(_messages.Message):
  r"""A document translation request output config.

  Fields:
    gcsDestination: Optional. Google Cloud Storage destination for the
      translation output, e.g., `gs://my_bucket/my_directory/`. The
      destination directory provided does not have to be empty, but the bucket
      must exist. If a file with the same name as the output file already
      exists in the destination an error will be returned. For a
      DocumentInputConfig.contents provided document, the output file will
      have the name "output_[trg]_translations.[ext]", where - [trg]
      corresponds to the translated file's language code, - [ext] corresponds
      to the translated file's extension according to its mime type. For a
      DocumentInputConfig.gcs_uri provided document, the output file will have
      a name according to its URI. For example: an input file with URI:
      `gs://a/b/c.[extension]` stored in a gcs_destination bucket with name
      "my_bucket" will have an output URI:
      `gs://my_bucket/a_b_c_[trg]_translations.[ext]`, where - [trg]
      corresponds to the translated file's language code, - [ext] corresponds
      to the translated file's extension according to its mime type. If the
      document was directly provided through the request, then the output
      document will have the format:
      `gs://my_bucket/translated_document_[trg]_translations.[ext]`, where -
      [trg] corresponds to the translated file's language code, - [ext]
      corresponds to the translated file's extension according to its mime
      type. If a glossary was provided, then the output URI for the glossary
      translation will be equal to the default output URI but have
      `glossary_translations` instead of `translations`. For the previous
      example, its glossary URI would be:
      `gs://my_bucket/a_b_c_[trg]_glossary_translations.[ext]`. Thus the max
      number of output files will be 2 (Translated document, Glossary
      translated document). Callers should expect no partial outputs. If there
      is any error during document translation, no output will be stored in
      the Cloud Storage bucket.
    mimeType: Optional. Specifies the translated document's mime_type. If not
      specified, the translated file's mime type will be the same as the input
      file's mime type. Currently only support the output mime type to be the
      same as input mime type. - application/pdf -
      application/vnd.openxmlformats-officedocument.wordprocessingml.document
      - application/vnd.openxmlformats-
      officedocument.presentationml.presentation -
      application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
  """

  gcsDestination = _messages.MessageField('GcsDestination', 1)
  mimeType = _messages.StringField(2)


class DocumentTranslation(_messages.Message):
  r"""A translated document message.

  Fields:
    byteStreamOutputs: The array of translated documents. It is expected to be
      size 1 for now. We may produce multiple translated documents in the
      future for other type of file formats.
    detectedLanguageCode: The detected language for the input document. If the
      user did not provide the source language for the input document, this
      field will have the language code automatically detected. If the source
      language was passed, auto-detection of the language does not occur and
      this field is empty.
    mimeType: The translated document's mime type.
  """

  byteStreamOutputs = _messages.BytesField(1, repeated=True)
  detectedLanguageCode = _messages.StringField(2)
  mimeType = _messages.StringField(3)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Example(_messages.Message):
  r"""A sentence pair.

  Fields:
    name: Output only. The resource name of the example, in form of
      `projects/{project-number-or-
      id}/locations/{location_id}/datasets/{dataset_id}/examples/{example_id}`
    sourceText: Sentence in source language.
    targetText: Sentence in target language.
    usage: Output only. Usage of the sentence pair. Options are
      TRAIN|VALIDATION|TEST.
  """

  name = _messages.StringField(1)
  sourceText = _messages.StringField(2)
  targetText = _messages.StringField(3)
  usage = _messages.StringField(4)


class ExportDataRequest(_messages.Message):
  r"""Request message for ExportData.

  Fields:
    outputConfig: Required. The config for the output content.
  """

  outputConfig = _messages.MessageField('DatasetOutputConfig', 1)


class FileInputSource(_messages.Message):
  r"""An inlined file.

  Fields:
    content: Required. The file's byte contents.
    displayName: Required. The file's display name.
    mimeType: Required. The file's mime type.
  """

  content = _messages.BytesField(1)
  displayName = _messages.StringField(2)
  mimeType = _messages.StringField(3)


class GcsDestination(_messages.Message):
  r"""The Google Cloud Storage location for the output content.

  Fields:
    outputUriPrefix: Required. The bucket used in 'output_uri_prefix' must
      exist and there must be no files under 'output_uri_prefix'.
      'output_uri_prefix' must end with "/" and start with "gs://". One
      'output_uri_prefix' can only be used by one batch translation job at a
      time. Otherwise an INVALID_ARGUMENT (400) error is returned.
  """

  outputUriPrefix = _messages.StringField(1)


class GcsInputSource(_messages.Message):
  r"""The Google Cloud Storage location for the input content.

  Fields:
    inputUri: Required. Source data URI. For example,
      `gs://my_bucket/my_object`.
  """

  inputUri = _messages.StringField(1)


class GcsOutputDestination(_messages.Message):
  r"""The Google Cloud Storage location for the output content.

  Fields:
    outputUriPrefix: Required. Google Cloud Storage URI to output directory.
      For example, `gs://bucket/directory`. The requesting user must have
      write permission to the bucket. The directory will be created if it
      doesn't exist.
  """

  outputUriPrefix = _messages.StringField(1)


class GcsSource(_messages.Message):
  r"""The Google Cloud Storage location for the input content.

  Fields:
    inputUri: Required. Source data URI. For example,
      `gs://my_bucket/my_object`.
  """

  inputUri = _messages.StringField(1)


class Glossary(_messages.Message):
  r"""Represents a glossary built from user-provided data.

  Fields:
    displayName: Optional. The display name of the glossary.
    endTime: Output only. When the glossary creation was finished.
    entryCount: Output only. The number of entries defined in the glossary.
    inputConfig: Required. Provides examples to build the glossary from. Total
      glossary must not exceed 10M Unicode codepoints.
    languageCodesSet: Used with equivalent term set glossaries.
    languagePair: Used with unidirectional glossaries.
    name: Required. The resource name of the glossary. Glossary names have the
      form `projects/{project-number-or-id}/locations/{location-
      id}/glossaries/{glossary-id}`.
    submitTime: Output only. When CreateGlossary was called.
  """

  displayName = _messages.StringField(1)
  endTime = _messages.StringField(2)
  entryCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  inputConfig = _messages.MessageField('GlossaryInputConfig', 4)
  languageCodesSet = _messages.MessageField('LanguageCodesSet', 5)
  languagePair = _messages.MessageField('LanguageCodePair', 6)
  name = _messages.StringField(7)
  submitTime = _messages.StringField(8)


class GlossaryConfig(_messages.Message):
  r"""Configures which glossary is used for a specific target language and
  defines options for applying that glossary.

  Fields:
    contextualTranslationEnabled: Optional. If set to true, the glossary will
      be used for contextual translation.
    glossary: Required. The `glossary` to be applied for this translation. The
      format depends on the glossary: - User-provided custom glossary:
      `projects/{project-number-or-id}/locations/{location-
      id}/glossaries/{glossary-id}`
    ignoreCase: Optional. Indicates match is case insensitive. The default
      value is `false` if missing.
  """

  contextualTranslationEnabled = _messages.BooleanField(1)
  glossary = _messages.StringField(2)
  ignoreCase = _messages.BooleanField(3)


class GlossaryEntry(_messages.Message):
  r"""Represents a single entry in a glossary.

  Fields:
    description: Describes the glossary entry.
    name: Identifier. The resource name of the entry. Format:
      `projects/*/locations/*/glossaries/*/glossaryEntries/*`
    termsPair: Used for an unidirectional glossary.
    termsSet: Used for an equivalent term sets glossary.
  """

  description = _messages.StringField(1)
  name = _messages.StringField(2)
  termsPair = _messages.MessageField('GlossaryTermsPair', 3)
  termsSet = _messages.MessageField('GlossaryTermsSet', 4)


class GlossaryInputConfig(_messages.Message):
  r"""Input configuration for glossaries.

  Fields:
    gcsSource: Required. Google Cloud Storage location of glossary data. File
      format is determined based on the filename extension. API returns
      [google.rpc.Code.INVALID_ARGUMENT] for unsupported URI-s and file
      formats. Wildcards are not allowed. This must be a single file in one of
      the following formats: For unidirectional glossaries: - TSV/CSV
      (`.tsv`/`.csv`): Two column file, tab- or comma-separated. The first
      column is source text. The second column is target text. No headers in
      this file. The first row contains data and not column names. - TMX
      (`.tmx`): TMX file with parallel data defining source/target term pairs.
      For equivalent term sets glossaries: - CSV (`.csv`): Multi-column CSV
      file defining equivalent glossary terms in multiple languages. See
      documentation for more information -
      [glossaries](https://cloud.google.com/translate/docs/advanced/glossary).
  """

  gcsSource = _messages.MessageField('GcsSource', 1)


class GlossaryTerm(_messages.Message):
  r"""Represents a single glossary term

  Fields:
    languageCode: The language for this glossary term.
    text: The text for the glossary term.
  """

  languageCode = _messages.StringField(1)
  text = _messages.StringField(2)


class GlossaryTermsPair(_messages.Message):
  r"""Represents a single entry for an unidirectional glossary.

  Fields:
    sourceTerm: The source term is the term that will get match in the text,
    targetTerm: The term that will replace the match source term.
  """

  sourceTerm = _messages.MessageField('GlossaryTerm', 1)
  targetTerm = _messages.MessageField('GlossaryTerm', 2)


class GlossaryTermsSet(_messages.Message):
  r"""Represents a single entry for an equivalent term set glossary. This is
  used for equivalent term sets where each term can be replaced by the other
  terms in the set.

  Fields:
    terms: Each term in the set represents a term that can be replaced by the
      other terms.
  """

  terms = _messages.MessageField('GlossaryTerm', 1, repeated=True)


class ImportAdaptiveMtFileRequest(_messages.Message):
  r"""The request for importing an AdaptiveMt file along with its sentences.

  Fields:
    fileInputSource: Inline file source.
    gcsInputSource: Google Cloud Storage file source.
  """

  fileInputSource = _messages.MessageField('FileInputSource', 1)
  gcsInputSource = _messages.MessageField('GcsInputSource', 2)


class ImportAdaptiveMtFileResponse(_messages.Message):
  r"""The response for importing an AdaptiveMtFile

  Fields:
    adaptiveMtFile: Output only. The Adaptive MT file that was imported.
  """

  adaptiveMtFile = _messages.MessageField('AdaptiveMtFile', 1)


class ImportDataRequest(_messages.Message):
  r"""Request message for ImportData.

  Fields:
    inputConfig: Required. The config for the input content.
  """

  inputConfig = _messages.MessageField('DatasetInputConfig', 1)


class InputConfig(_messages.Message):
  r"""Input configuration for BatchTranslateText request.

  Fields:
    gcsSource: Required. Google Cloud Storage location for the source input.
      This can be a single file (for example, `gs://translation-
      test/input.tsv`) or a wildcard (for example, `gs://translation-test/*`).
      If a file extension is `.tsv`, it can contain either one or two columns.
      The first column (optional) is the id of the text request. If the first
      column is missing, we use the row number (0-based) from the input file
      as the ID in the output file. The second column is the actual text to be
      translated. We recommend each row be <= 10K Unicode codepoints,
      otherwise an error might be returned. Note that the input tsv must be
      RFC 4180 compliant. You could use https://github.com/Clever/csvlint to
      check potential formatting errors in your tsv file. csvlint
      --delimiter='\t' your_input_file.tsv The other supported file extensions
      are `.txt` or `.html`, which is treated as a single large chunk of text.
    mimeType: Optional. Can be "text/plain" or "text/html". For `.tsv`,
      "text/html" is used if mime_type is missing. For `.html`, this field
      must be "text/html" or empty. For `.txt`, this field must be
      "text/plain" or empty.
  """

  gcsSource = _messages.MessageField('GcsSource', 1)
  mimeType = _messages.StringField(2)


class InputFile(_messages.Message):
  r"""An input file.

  Fields:
    gcsSource: Google Cloud Storage file source.
    usage: Optional. Usage of the file contents. Options are
      TRAIN|VALIDATION|TEST, or UNASSIGNED (by default) for auto split.
  """

  gcsSource = _messages.MessageField('GcsInputSource', 1)
  usage = _messages.StringField(2)


class LanguageCodePair(_messages.Message):
  r"""Used with unidirectional glossaries.

  Fields:
    sourceLanguageCode: Required. The ISO-639 language code of the input text,
      for example, "en-US". Expected to be an exact match for
      GlossaryTerm.language_code.
    targetLanguageCode: Required. The ISO-639 language code for translation
      output, for example, "zh-CN". Expected to be an exact match for
      GlossaryTerm.language_code.
  """

  sourceLanguageCode = _messages.StringField(1)
  targetLanguageCode = _messages.StringField(2)


class LanguageCodesSet(_messages.Message):
  r"""Used with equivalent term set glossaries.

  Fields:
    languageCodes: The ISO-639 language code(s) for terms defined in the
      glossary. All entries are unique. The list contains at least two
      entries. Expected to be an exact match for GlossaryTerm.language_code.
  """

  languageCodes = _messages.StringField(1, repeated=True)


class ListAdaptiveMtDatasetsResponse(_messages.Message):
  r"""A list of AdaptiveMtDatasets.

  Fields:
    adaptiveMtDatasets: Output only. A list of Adaptive MT datasets.
    nextPageToken: Optional. A token to retrieve a page of results. Pass this
      value in the [ListAdaptiveMtDatasetsRequest.page_token] field in the
      subsequent call to `ListAdaptiveMtDatasets` method to retrieve the next
      page of results.
  """

  adaptiveMtDatasets = _messages.MessageField('AdaptiveMtDataset', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAdaptiveMtFilesResponse(_messages.Message):
  r"""The response for listing all AdaptiveMt files under a given dataset.

  Fields:
    adaptiveMtFiles: Output only. The Adaptive MT files.
    nextPageToken: Optional. A token to retrieve a page of results. Pass this
      value in the ListAdaptiveMtFilesRequest.page_token field in the
      subsequent call to `ListAdaptiveMtFiles` method to retrieve the next
      page of results.
  """

  adaptiveMtFiles = _messages.MessageField('AdaptiveMtFile', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAdaptiveMtSentencesResponse(_messages.Message):
  r"""List AdaptiveMt sentences response.

  Fields:
    adaptiveMtSentences: Output only. The list of AdaptiveMtSentences.
    nextPageToken: Optional.
  """

  adaptiveMtSentences = _messages.MessageField('AdaptiveMtSentence', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDatasetsResponse(_messages.Message):
  r"""Response message for ListDatasets.

  Fields:
    datasets: The datasets read.
    nextPageToken: A token to retrieve next page of results. Pass this token
      to the page_token field in the ListDatasetsRequest to obtain the
      corresponding page.
  """

  datasets = _messages.MessageField('Dataset', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListExamplesResponse(_messages.Message):
  r"""Response message for ListExamples.

  Fields:
    examples: The sentence pairs.
    nextPageToken: A token to retrieve next page of results. Pass this token
      to the page_token field in the ListExamplesRequest to obtain the
      corresponding page.
  """

  examples = _messages.MessageField('Example', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListGlossariesResponse(_messages.Message):
  r"""Response message for ListGlossaries.

  Fields:
    glossaries: The list of glossaries for a project.
    nextPageToken: A token to retrieve a page of results. Pass this value in
      the [ListGlossariesRequest.page_token] field in the subsequent call to
      `ListGlossaries` method to retrieve the next page of results.
  """

  glossaries = _messages.MessageField('Glossary', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListGlossaryEntriesResponse(_messages.Message):
  r"""Response message for ListGlossaryEntries

  Fields:
    glossaryEntries: Optional. The Glossary Entries
    nextPageToken: Optional. A token to retrieve a page of results. Pass this
      value in the [ListGLossaryEntriesRequest.page_token] field in the
      subsequent calls.
  """

  glossaryEntries = _messages.MessageField('GlossaryEntry', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListModelsResponse(_messages.Message):
  r"""Response message for ListModels.

  Fields:
    models: The models read.
    nextPageToken: A token to retrieve next page of results. Pass this token
      to the page_token field in the ListModelsRequest to obtain the
      corresponding page.
  """

  models = _messages.MessageField('Model', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Model(_messages.Message):
  r"""A trained translation model.

  Fields:
    createTime: Output only. Timestamp when the model resource was created,
      which is also when the training started.
    dataset: The dataset from which the model is trained, in form of
      `projects/{project-number-or-
      id}/locations/{location_id}/datasets/{dataset_id}`
    displayName: The name of the model to show in the interface. The name can
      be up to 32 characters long and can consist only of ASCII Latin letters
      A-Z and a-z, underscores (_), and ASCII digits 0-9.
    name: The resource name of the model, in form of `projects/{project-
      number-or-id}/locations/{location_id}/models/{model_id}`
    sourceLanguageCode: Output only. The BCP-47 language code of the source
      language.
    targetLanguageCode: Output only. The BCP-47 language code of the target
      language.
    testExampleCount: Output only. Number of examples (sentence pairs) used to
      test the model.
    trainExampleCount: Output only. Number of examples (sentence pairs) used
      to train the model.
    updateTime: Output only. Timestamp when this model was last updated.
    validateExampleCount: Output only. Number of examples (sentence pairs)
      used to validate the model.
  """

  createTime = _messages.StringField(1)
  dataset = _messages.StringField(2)
  displayName = _messages.StringField(3)
  name = _messages.StringField(4)
  sourceLanguageCode = _messages.StringField(5)
  targetLanguageCode = _messages.StringField(6)
  testExampleCount = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  trainExampleCount = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  updateTime = _messages.StringField(9)
  validateExampleCount = _messages.IntegerField(10, variant=_messages.Variant.INT32)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OutputConfig(_messages.Message):
  r"""Output configuration for BatchTranslateText request.

  Fields:
    gcsDestination: Google Cloud Storage destination for output content. For
      every single input file (for example, gs://a/b/c.[extension]), we
      generate at most 2 * n output files. (n is the # of
      target_language_codes in the BatchTranslateTextRequest). Output files
      (tsv) generated are compliant with RFC 4180 except that record
      delimiters are '\n' instead of '\r\n'. We don't provide any way to
      change record delimiters. While the input files are being processed, we
      write/update an index file 'index.csv' under 'output_uri_prefix' (for
      example, gs://translation-test/index.csv) The index file is
      generated/updated as new files are being translated. The format is:
      input_file,target_language_code,translations_file,errors_file,
      glossary_translations_file,glossary_errors_file input_file is one file
      we matched using gcs_source.input_uri. target_language_code is provided
      in the request. translations_file contains the translations. (details
      provided below) errors_file contains the errors during processing of the
      file. (details below). Both translations_file and errors_file could be
      empty strings if we have no content to output.
      glossary_translations_file and glossary_errors_file are always empty
      strings if the input_file is tsv. They could also be empty if we have no
      content to output. Once a row is present in index.csv, the input/output
      matching never changes. Callers should also expect all the content in
      input_file are processed and ready to be consumed (that is, no partial
      output file is written). Since index.csv will be keeping updated during
      the process, please make sure there is no custom retention policy
      applied on the output bucket that may avoid file updating.
      (https://cloud.google.com/storage/docs/bucket-lock#retention-policy) The
      format of translations_file (for target language code 'trg') is:
      `gs://translation_test/a_b_c_'trg'_translations.[extension]` If the
      input file extension is tsv, the output has the following columns:
      Column 1: ID of the request provided in the input, if it's not provided
      in the input, then the input row number is used (0-based). Column 2:
      source sentence. Column 3: translation without applying a glossary.
      Empty string if there is an error. Column 4 (only present if a glossary
      is provided in the request): translation after applying the glossary.
      Empty string if there is an error applying the glossary. Could be same
      string as column 3 if there is no glossary applied. If input file
      extension is a txt or html, the translation is directly written to the
      output file. If glossary is requested, a separate
      glossary_translations_file has format of
      `gs://translation_test/a_b_c_'trg'_glossary_translations.[extension]`
      The format of errors file (for target language code 'trg') is:
      `gs://translation_test/a_b_c_'trg'_errors.[extension]` If the input file
      extension is tsv, errors_file contains the following: Column 1: ID of
      the request provided in the input, if it's not provided in the input,
      then the input row number is used (0-based). Column 2: source sentence.
      Column 3: Error detail for the translation. Could be empty. Column 4
      (only present if a glossary is provided in the request): Error when
      applying the glossary. If the input file extension is txt or html,
      glossary_error_file will be generated that contains error details.
      glossary_error_file has format of
      `gs://translation_test/a_b_c_'trg'_glossary_errors.[extension]`
  """

  gcsDestination = _messages.MessageField('GcsDestination', 1)


class ReferenceSentenceConfig(_messages.Message):
  r"""Message of caller-provided reference configuration.

  Fields:
    referenceSentencePairLists: Reference sentences pair lists. Each list will
      be used as the references to translate the sentence under "content"
      field at the corresponding index. Length of the list is required to be
      equal to the length of "content" field.
    sourceLanguageCode: Source language code.
    targetLanguageCode: Target language code.
  """

  referenceSentencePairLists = _messages.MessageField('ReferenceSentencePairList', 1, repeated=True)
  sourceLanguageCode = _messages.StringField(2)
  targetLanguageCode = _messages.StringField(3)


class ReferenceSentencePair(_messages.Message):
  r"""A pair of sentences used as reference in source and target languages.

  Fields:
    sourceSentence: Source sentence in the sentence pair.
    targetSentence: Target sentence in the sentence pair.
  """

  sourceSentence = _messages.StringField(1)
  targetSentence = _messages.StringField(2)


class ReferenceSentencePairList(_messages.Message):
  r"""A list of reference sentence pairs.

  Fields:
    referenceSentencePairs: Reference sentence pairs.
  """

  referenceSentencePairs = _messages.MessageField('ReferenceSentencePair', 1, repeated=True)


class Romanization(_messages.Message):
  r"""A single romanization response.

  Fields:
    detectedLanguageCode: The ISO-639 language code of source text in the
      initial request, detected automatically, if no source language was
      passed within the initial request. If the source language was passed,
      auto-detection of the language does not occur and this field is empty.
    romanizedText: Romanized text. If an error occurs during romanization,
      this field might be excluded from the response.
  """

  detectedLanguageCode = _messages.StringField(1)
  romanizedText = _messages.StringField(2)


class RomanizeTextRequest(_messages.Message):
  r"""The request message for synchronous romanization.

  Fields:
    contents: Required. The content of the input in string format.
    sourceLanguageCode: Optional. The ISO-639 language code of the input text
      if known, for example, "hi" or "zh". If the source language isn't
      specified, the API attempts to identify the source language
      automatically and returns the source language for each content in the
      response.
  """

  contents = _messages.StringField(1, repeated=True)
  sourceLanguageCode = _messages.StringField(2)


class RomanizeTextResponse(_messages.Message):
  r"""The response message for synchronous romanization.

  Fields:
    romanizations: Text romanization responses. This field has the same length
      as `contents`.
  """

  romanizations = _messages.MessageField('Romanization', 1, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class SupportedLanguage(_messages.Message):
  r"""A single supported language response corresponds to information related
  to one supported language.

  Fields:
    displayName: Human-readable name of the language localized in the display
      language specified in the request.
    languageCode: Supported language code, generally consisting of its ISO
      639-1 identifier, for example, 'en', 'ja'. In certain cases, ISO-639
      codes including language and region identifiers are returned (for
      example, 'zh-TW' and 'zh-CN').
    supportSource: Can be used as a source language.
    supportTarget: Can be used as a target language.
  """

  displayName = _messages.StringField(1)
  languageCode = _messages.StringField(2)
  supportSource = _messages.BooleanField(3)
  supportTarget = _messages.BooleanField(4)


class SupportedLanguages(_messages.Message):
  r"""The response message for discovering supported languages.

  Fields:
    languages: A list of supported language responses. This list contains an
      entry for each language the Translation API supports.
  """

  languages = _messages.MessageField('SupportedLanguage', 1, repeated=True)


class TranslateDocumentRequest(_messages.Message):
  r"""A document translation request.

  Messages:
    LabelsValue: Optional. The labels with user-defined metadata for the
      request. Label keys and values can be no longer than 63 characters
      (Unicode codepoints), can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter.
      See https://cloud.google.com/translate/docs/advanced/labels for more
      information.

  Fields:
    customizedAttribution: Optional. This flag is to support user customized
      attribution. If not provided, the default is `Machine Translated by
      Google`. Customized attribution should follow rules in
      https://cloud.google.com/translate/attribution#attribution_and_logos
    documentInputConfig: Required. Input configurations.
    documentOutputConfig: Optional. Output configurations. Defines if the
      output file should be stored within Cloud Storage as well as the desired
      output format. If not provided the translated file will only be returned
      through a byte-stream and its output mime type will be the same as the
      input file's mime type.
    enableRotationCorrection: Optional. If true, enable auto rotation
      correction in DVS.
    enableShadowRemovalNativePdf: Optional. If true, use the text removal
      server to remove the shadow text on background image for native pdf
      translation. Shadow removal feature can only be enabled when
      is_translate_native_pdf_only: false && pdf_native_only: false
    glossaryConfig: Optional. Glossary to be applied. The glossary must be
      within the same region (have the same location-id) as the model,
      otherwise an INVALID_ARGUMENT (400) error is returned.
    isTranslateNativePdfOnly: Optional. is_translate_native_pdf_only field for
      external customers. If true, the page limit of online native pdf
      translation is 300 and only native pdf pages will be translated.
    labels: Optional. The labels with user-defined metadata for the request.
      Label keys and values can be no longer than 63 characters (Unicode
      codepoints), can only contain lowercase letters, numeric characters,
      underscores and dashes. International characters are allowed. Label
      values are optional. Label keys must start with a letter. See
      https://cloud.google.com/translate/docs/advanced/labels for more
      information.
    model: Optional. The `model` type requested for this translation. The
      format depends on model type: - AutoML Translation models:
      `projects/{project-number-or-id}/locations/{location-id}/models/{model-
      id}` - General (built-in) models: `projects/{project-number-or-
      id}/locations/{location-id}/models/general/nmt`, If not provided, the
      default Google model (NMT) will be used for translation.
    sourceLanguageCode: Optional. The ISO-639 language code of the input
      document if known, for example, "en-US" or "sr-Latn". Supported language
      codes are listed in Language Support. If the source language isn't
      specified, the API attempts to identify the source language
      automatically and returns the source language within the response.
      Source language must be specified if the request contains a glossary or
      a custom model.
    targetLanguageCode: Required. The ISO-639 language code to use for
      translation of the input document, set to one of the language codes
      listed in Language Support.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels with user-defined metadata for the request. Label
    keys and values can be no longer than 63 characters (Unicode codepoints),
    can only contain lowercase letters, numeric characters, underscores and
    dashes. International characters are allowed. Label values are optional.
    Label keys must start with a letter. See
    https://cloud.google.com/translate/docs/advanced/labels for more
    information.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customizedAttribution = _messages.StringField(1)
  documentInputConfig = _messages.MessageField('DocumentInputConfig', 2)
  documentOutputConfig = _messages.MessageField('DocumentOutputConfig', 3)
  enableRotationCorrection = _messages.BooleanField(4)
  enableShadowRemovalNativePdf = _messages.BooleanField(5)
  glossaryConfig = _messages.MessageField('TranslateTextGlossaryConfig', 6)
  isTranslateNativePdfOnly = _messages.BooleanField(7)
  labels = _messages.MessageField('LabelsValue', 8)
  model = _messages.StringField(9)
  sourceLanguageCode = _messages.StringField(10)
  targetLanguageCode = _messages.StringField(11)


class TranslateDocumentResponse(_messages.Message):
  r"""A translated document response message.

  Fields:
    documentTranslation: Translated document.
    glossaryConfig: The `glossary_config` used for this translation.
    glossaryDocumentTranslation: The document's translation output if a
      glossary is provided in the request. This can be the same as
      [TranslateDocumentResponse.document_translation] if no glossary terms
      apply.
    model: Only present when 'model' is present in the request. 'model' is
      normalized to have a project number. For example: If the 'model' field
      in TranslateDocumentRequest is: `projects/{project-
      id}/locations/{location-id}/models/general/nmt` then `model` here would
      be normalized to `projects/{project-number}/locations/{location-
      id}/models/general/nmt`.
  """

  documentTranslation = _messages.MessageField('DocumentTranslation', 1)
  glossaryConfig = _messages.MessageField('TranslateTextGlossaryConfig', 2)
  glossaryDocumentTranslation = _messages.MessageField('DocumentTranslation', 3)
  model = _messages.StringField(4)


class TranslateProjectsDetectLanguageRequest(_messages.Message):
  r"""A TranslateProjectsDetectLanguageRequest object.

  Fields:
    detectLanguageRequest: A DetectLanguageRequest resource to be passed as
      the request body.
    parent: Required. Project or location to make a call. Must refer to a
      caller's project. Format: `projects/{project-number-or-
      id}/locations/{location-id}` or `projects/{project-number-or-id}`. For
      global calls, use `projects/{project-number-or-id}/locations/global` or
      `projects/{project-number-or-id}`. Only models within the same region
      (has same location-id) can be used. Otherwise an INVALID_ARGUMENT (400)
      error is returned.
  """

  detectLanguageRequest = _messages.MessageField('DetectLanguageRequest', 1)
  parent = _messages.StringField(2, required=True)


class TranslateProjectsGetSupportedLanguagesRequest(_messages.Message):
  r"""A TranslateProjectsGetSupportedLanguagesRequest object.

  Fields:
    displayLanguageCode: Optional. The language to use to return localized,
      human readable names of supported languages. If missing, then display
      names are not returned in a response.
    model: Optional. Get supported languages of this model. The format depends
      on model type: - AutoML Translation models: `projects/{project-number-
      or-id}/locations/{location-id}/models/{model-id}` - General (built-in)
      models: `projects/{project-number-or-id}/locations/{location-
      id}/models/general/nmt`, Returns languages supported by the specified
      model. If missing, we get supported languages of Google general NMT
      model.
    parent: Required. Project or location to make a call. Must refer to a
      caller's project. Format: `projects/{project-number-or-id}` or
      `projects/{project-number-or-id}/locations/{location-id}`. For global
      calls, use `projects/{project-number-or-id}/locations/global` or
      `projects/{project-number-or-id}`. Non-global location is required for
      AutoML models. Only models within the same region (have same location-
      id) can be used, otherwise an INVALID_ARGUMENT (400) error is returned.
  """

  displayLanguageCode = _messages.StringField(1)
  model = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class TranslateProjectsLocationsAdaptiveMtDatasetsAdaptiveMtFilesAdaptiveMtSentencesListRequest(_messages.Message):
  r"""A TranslateProjectsLocationsAdaptiveMtDatasetsAdaptiveMtFilesAdaptiveMtS
  entencesListRequest object.

  Fields:
    pageSize: A integer attribute.
    pageToken: A token identifying a page of results the server should return.
      Typically, this is the value of
      ListAdaptiveMtSentencesRequest.next_page_token returned from the
      previous call to `ListTranslationMemories` method. The first page is
      returned if `page_token` is empty or missing.
    parent: Required. The resource name of the project from which to list the
      Adaptive MT files. The following format lists all sentences under a
      file. `projects/{project}/locations/{location}/adaptiveMtDatasets/{datas
      et}/adaptiveMtFiles/{file}` The following format lists all sentences
      within a dataset.
      `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class TranslateProjectsLocationsAdaptiveMtDatasetsAdaptiveMtFilesDeleteRequest(_messages.Message):
  r"""A
  TranslateProjectsLocationsAdaptiveMtDatasetsAdaptiveMtFilesDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the file to delete, in form of
      `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatas
      ets/{dataset}/adaptiveMtFiles/{file}`
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsAdaptiveMtDatasetsAdaptiveMtFilesGetRequest(_messages.Message):
  r"""A TranslateProjectsLocationsAdaptiveMtDatasetsAdaptiveMtFilesGetRequest
  object.

  Fields:
    name: Required. The resource name of the file, in form of
      `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatas
      ets/{dataset}/adaptiveMtFiles/{file}`
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsAdaptiveMtDatasetsAdaptiveMtFilesListRequest(_messages.Message):
  r"""A TranslateProjectsLocationsAdaptiveMtDatasetsAdaptiveMtFilesListRequest
  object.

  Fields:
    pageSize: Optional.
    pageToken: Optional. A token identifying a page of results the server
      should return. Typically, this is the value of
      ListAdaptiveMtFilesResponse.next_page_token returned from the previous
      call to `ListAdaptiveMtFiles` method. The first page is returned if
      `page_token`is empty or missing.
    parent: Required. The resource name of the project from which to list the
      Adaptive MT files.
      `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class TranslateProjectsLocationsAdaptiveMtDatasetsAdaptiveMtSentencesListRequest(_messages.Message):
  r"""A
  TranslateProjectsLocationsAdaptiveMtDatasetsAdaptiveMtSentencesListRequest
  object.

  Fields:
    pageSize: A integer attribute.
    pageToken: A token identifying a page of results the server should return.
      Typically, this is the value of
      ListAdaptiveMtSentencesRequest.next_page_token returned from the
      previous call to `ListTranslationMemories` method. The first page is
      returned if `page_token` is empty or missing.
    parent: Required. The resource name of the project from which to list the
      Adaptive MT files. The following format lists all sentences under a
      file. `projects/{project}/locations/{location}/adaptiveMtDatasets/{datas
      et}/adaptiveMtFiles/{file}` The following format lists all sentences
      within a dataset.
      `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class TranslateProjectsLocationsAdaptiveMtDatasetsCreateRequest(_messages.Message):
  r"""A TranslateProjectsLocationsAdaptiveMtDatasetsCreateRequest object.

  Fields:
    adaptiveMtDataset: A AdaptiveMtDataset resource to be passed as the
      request body.
    parent: Required. Name of the parent project. In form of
      `projects/{project-number-or-id}/locations/{location-id}`
  """

  adaptiveMtDataset = _messages.MessageField('AdaptiveMtDataset', 1)
  parent = _messages.StringField(2, required=True)


class TranslateProjectsLocationsAdaptiveMtDatasetsDeleteRequest(_messages.Message):
  r"""A TranslateProjectsLocationsAdaptiveMtDatasetsDeleteRequest object.

  Fields:
    name: Required. Name of the dataset. In the form of `projects/{project-
      number-or-id}/locations/{location-id}/adaptiveMtDatasets/{adaptive-mt-
      dataset-id}`
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsAdaptiveMtDatasetsGetRequest(_messages.Message):
  r"""A TranslateProjectsLocationsAdaptiveMtDatasetsGetRequest object.

  Fields:
    name: Required. Name of the dataset. In the form of `projects/{project-
      number-or-id}/locations/{location-id}/adaptiveMtDatasets/{adaptive-mt-
      dataset-id}`
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsAdaptiveMtDatasetsImportAdaptiveMtFileRequest(_messages.Message):
  r"""A
  TranslateProjectsLocationsAdaptiveMtDatasetsImportAdaptiveMtFileRequest
  object.

  Fields:
    importAdaptiveMtFileRequest: A ImportAdaptiveMtFileRequest resource to be
      passed as the request body.
    parent: Required. The resource name of the file, in form of
      `projects/{project-number-or-
      id}/locations/{location_id}/adaptiveMtDatasets/{dataset}`
  """

  importAdaptiveMtFileRequest = _messages.MessageField('ImportAdaptiveMtFileRequest', 1)
  parent = _messages.StringField(2, required=True)


class TranslateProjectsLocationsAdaptiveMtDatasetsListRequest(_messages.Message):
  r"""A TranslateProjectsLocationsAdaptiveMtDatasetsListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the request.
      Filter is not supported yet.
    pageSize: Optional. Requested page size. The server may return fewer
      results than requested. If unspecified, the server picks an appropriate
      default.
    pageToken: Optional. A token identifying a page of results the server
      should return. Typically, this is the value of
      ListAdaptiveMtDatasetsResponse.next_page_token returned from the
      previous call to `ListAdaptiveMtDatasets` method. The first page is
      returned if `page_token`is empty or missing.
    parent: Required. The resource name of the project from which to list the
      Adaptive MT datasets. `projects/{project-number-or-
      id}/locations/{location-id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class TranslateProjectsLocationsAdaptiveMtTranslateRequest(_messages.Message):
  r"""A TranslateProjectsLocationsAdaptiveMtTranslateRequest object.

  Fields:
    adaptiveMtTranslateRequest: A AdaptiveMtTranslateRequest resource to be
      passed as the request body.
    parent: Required. Location to make a regional call. Format:
      `projects/{project-number-or-id}/locations/{location-id}`.
  """

  adaptiveMtTranslateRequest = _messages.MessageField('AdaptiveMtTranslateRequest', 1)
  parent = _messages.StringField(2, required=True)


class TranslateProjectsLocationsBatchTranslateDocumentRequest(_messages.Message):
  r"""A TranslateProjectsLocationsBatchTranslateDocumentRequest object.

  Fields:
    batchTranslateDocumentRequest: A BatchTranslateDocumentRequest resource to
      be passed as the request body.
    parent: Required. Location to make a regional call. Format:
      `projects/{project-number-or-id}/locations/{location-id}`. The `global`
      location is not supported for batch translation. Only AutoML Translation
      models or glossaries within the same region (have the same location-id)
      can be used, otherwise an INVALID_ARGUMENT (400) error is returned.
  """

  batchTranslateDocumentRequest = _messages.MessageField('BatchTranslateDocumentRequest', 1)
  parent = _messages.StringField(2, required=True)


class TranslateProjectsLocationsBatchTranslateTextRequest(_messages.Message):
  r"""A TranslateProjectsLocationsBatchTranslateTextRequest object.

  Fields:
    batchTranslateTextRequest: A BatchTranslateTextRequest resource to be
      passed as the request body.
    parent: Required. Location to make a call. Must refer to a caller's
      project. Format: `projects/{project-number-or-id}/locations/{location-
      id}`. The `global` location is not supported for batch translation. Only
      AutoML Translation models or glossaries within the same region (have the
      same location-id) can be used, otherwise an INVALID_ARGUMENT (400) error
      is returned.
  """

  batchTranslateTextRequest = _messages.MessageField('BatchTranslateTextRequest', 1)
  parent = _messages.StringField(2, required=True)


class TranslateProjectsLocationsDatasetsCreateRequest(_messages.Message):
  r"""A TranslateProjectsLocationsDatasetsCreateRequest object.

  Fields:
    dataset: A Dataset resource to be passed as the request body.
    parent: Required. The project name.
  """

  dataset = _messages.MessageField('Dataset', 1)
  parent = _messages.StringField(2, required=True)


class TranslateProjectsLocationsDatasetsDeleteRequest(_messages.Message):
  r"""A TranslateProjectsLocationsDatasetsDeleteRequest object.

  Fields:
    name: Required. The name of the dataset to delete.
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsDatasetsExamplesListRequest(_messages.Message):
  r"""A TranslateProjectsLocationsDatasetsExamplesListRequest object.

  Fields:
    filter: Optional. An expression for filtering the examples that will be
      returned. Example filter: * `usage=TRAIN`
    pageSize: Optional. Requested page size. The server can return fewer
      results than requested.
    pageToken: Optional. A token identifying a page of results for the server
      to return. Typically obtained from next_page_token field in the response
      of a ListExamples call.
    parent: Required. Name of the parent dataset. In form of
      `projects/{project-number-or-id}/locations/{location-
      id}/datasets/{dataset-id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class TranslateProjectsLocationsDatasetsExportDataRequest(_messages.Message):
  r"""A TranslateProjectsLocationsDatasetsExportDataRequest object.

  Fields:
    dataset: Required. Name of the dataset. In form of `projects/{project-
      number-or-id}/locations/{location-id}/datasets/{dataset-id}`
    exportDataRequest: A ExportDataRequest resource to be passed as the
      request body.
  """

  dataset = _messages.StringField(1, required=True)
  exportDataRequest = _messages.MessageField('ExportDataRequest', 2)


class TranslateProjectsLocationsDatasetsGetRequest(_messages.Message):
  r"""A TranslateProjectsLocationsDatasetsGetRequest object.

  Fields:
    name: Required. The resource name of the dataset to retrieve.
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsDatasetsImportDataRequest(_messages.Message):
  r"""A TranslateProjectsLocationsDatasetsImportDataRequest object.

  Fields:
    dataset: Required. Name of the dataset. In form of `projects/{project-
      number-or-id}/locations/{location-id}/datasets/{dataset-id}`
    importDataRequest: A ImportDataRequest resource to be passed as the
      request body.
  """

  dataset = _messages.StringField(1, required=True)
  importDataRequest = _messages.MessageField('ImportDataRequest', 2)


class TranslateProjectsLocationsDatasetsListRequest(_messages.Message):
  r"""A TranslateProjectsLocationsDatasetsListRequest object.

  Fields:
    pageSize: Optional. Requested page size. The server can return fewer
      results than requested.
    pageToken: Optional. A token identifying a page of results for the server
      to return. Typically obtained from next_page_token field in the response
      of a ListDatasets call.
    parent: Required. Name of the parent project. In form of
      `projects/{project-number-or-id}/locations/{location-id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class TranslateProjectsLocationsDetectLanguageRequest(_messages.Message):
  r"""A TranslateProjectsLocationsDetectLanguageRequest object.

  Fields:
    detectLanguageRequest: A DetectLanguageRequest resource to be passed as
      the request body.
    parent: Required. Project or location to make a call. Must refer to a
      caller's project. Format: `projects/{project-number-or-
      id}/locations/{location-id}` or `projects/{project-number-or-id}`. For
      global calls, use `projects/{project-number-or-id}/locations/global` or
      `projects/{project-number-or-id}`. Only models within the same region
      (has same location-id) can be used. Otherwise an INVALID_ARGUMENT (400)
      error is returned.
  """

  detectLanguageRequest = _messages.MessageField('DetectLanguageRequest', 1)
  parent = _messages.StringField(2, required=True)


class TranslateProjectsLocationsGetRequest(_messages.Message):
  r"""A TranslateProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsGetSupportedLanguagesRequest(_messages.Message):
  r"""A TranslateProjectsLocationsGetSupportedLanguagesRequest object.

  Fields:
    displayLanguageCode: Optional. The language to use to return localized,
      human readable names of supported languages. If missing, then display
      names are not returned in a response.
    model: Optional. Get supported languages of this model. The format depends
      on model type: - AutoML Translation models: `projects/{project-number-
      or-id}/locations/{location-id}/models/{model-id}` - General (built-in)
      models: `projects/{project-number-or-id}/locations/{location-
      id}/models/general/nmt`, Returns languages supported by the specified
      model. If missing, we get supported languages of Google general NMT
      model.
    parent: Required. Project or location to make a call. Must refer to a
      caller's project. Format: `projects/{project-number-or-id}` or
      `projects/{project-number-or-id}/locations/{location-id}`. For global
      calls, use `projects/{project-number-or-id}/locations/global` or
      `projects/{project-number-or-id}`. Non-global location is required for
      AutoML models. Only models within the same region (have same location-
      id) can be used, otherwise an INVALID_ARGUMENT (400) error is returned.
  """

  displayLanguageCode = _messages.StringField(1)
  model = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class TranslateProjectsLocationsGlossariesCreateRequest(_messages.Message):
  r"""A TranslateProjectsLocationsGlossariesCreateRequest object.

  Fields:
    glossary: A Glossary resource to be passed as the request body.
    parent: Required. The project name.
  """

  glossary = _messages.MessageField('Glossary', 1)
  parent = _messages.StringField(2, required=True)


class TranslateProjectsLocationsGlossariesDeleteRequest(_messages.Message):
  r"""A TranslateProjectsLocationsGlossariesDeleteRequest object.

  Fields:
    name: Required. The name of the glossary to delete.
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsGlossariesGetRequest(_messages.Message):
  r"""A TranslateProjectsLocationsGlossariesGetRequest object.

  Fields:
    name: Required. The name of the glossary to retrieve.
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsGlossariesGlossaryEntriesCreateRequest(_messages.Message):
  r"""A TranslateProjectsLocationsGlossariesGlossaryEntriesCreateRequest
  object.

  Fields:
    glossaryEntry: A GlossaryEntry resource to be passed as the request body.
    parent: Required. The resource name of the glossary to create the entry
      under.
  """

  glossaryEntry = _messages.MessageField('GlossaryEntry', 1)
  parent = _messages.StringField(2, required=True)


class TranslateProjectsLocationsGlossariesGlossaryEntriesDeleteRequest(_messages.Message):
  r"""A TranslateProjectsLocationsGlossariesGlossaryEntriesDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the glossary entry to delete
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsGlossariesGlossaryEntriesGetRequest(_messages.Message):
  r"""A TranslateProjectsLocationsGlossariesGlossaryEntriesGetRequest object.

  Fields:
    name: Required. The resource name of the glossary entry to get
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsGlossariesGlossaryEntriesListRequest(_messages.Message):
  r"""A TranslateProjectsLocationsGlossariesGlossaryEntriesListRequest object.

  Fields:
    pageSize: Optional. Requested page size. The server may return fewer
      glossary entries than requested. If unspecified, the server picks an
      appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return. Typically, this is the value of
      [ListGlossaryEntriesResponse.next_page_token] returned from the previous
      call. The first page is returned if `page_token`is empty or missing.
    parent: Required. The parent glossary resource name for listing the
      glossary's entries.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class TranslateProjectsLocationsGlossariesListRequest(_messages.Message):
  r"""A TranslateProjectsLocationsGlossariesListRequest object.

  Fields:
    filter: Optional. Filter specifying constraints of a list operation.
      Specify the constraint by the format of "key=value", where key must be
      "src" or "tgt", and the value must be a valid language code. For
      multiple restrictions, concatenate them by "AND" (uppercase only), such
      as: "src=en-US AND tgt=zh-CN". Notice that the exact match is used here,
      which means using 'en-US' and 'en' can lead to different results, which
      depends on the language code you used when you create the glossary. For
      the unidirectional glossaries, the "src" and "tgt" add restrictions on
      the source and target language code separately. For the equivalent term
      set glossaries, the "src" and/or "tgt" add restrictions on the term set.
      For example: "src=en-US AND tgt=zh-CN" will only pick the unidirectional
      glossaries which exactly match the source language code as "en-US" and
      the target language code "zh-CN", but all equivalent term set glossaries
      which contain "en-US" and "zh-CN" in their language set will be picked.
      If missing, no filtering is performed.
    pageSize: Optional. Requested page size. The server may return fewer
      glossaries than requested. If unspecified, the server picks an
      appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return. Typically, this is the value of
      [ListGlossariesResponse.next_page_token] returned from the previous call
      to `ListGlossaries` method. The first page is returned if `page_token`is
      empty or missing.
    parent: Required. The name of the project from which to list all of the
      glossaries.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class TranslateProjectsLocationsGlossariesPatchRequest(_messages.Message):
  r"""A TranslateProjectsLocationsGlossariesPatchRequest object.

  Fields:
    glossary: A Glossary resource to be passed as the request body.
    name: Required. The resource name of the glossary. Glossary names have the
      form `projects/{project-number-or-id}/locations/{location-
      id}/glossaries/{glossary-id}`.
    updateMask: The list of fields to be updated. Currently only
      `display_name` and 'input_config'
  """

  glossary = _messages.MessageField('Glossary', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class TranslateProjectsLocationsListRequest(_messages.Message):
  r"""A TranslateProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class TranslateProjectsLocationsModelsCreateRequest(_messages.Message):
  r"""A TranslateProjectsLocationsModelsCreateRequest object.

  Fields:
    model: A Model resource to be passed as the request body.
    parent: Required. The project name, in form of
      `projects/{project}/locations/{location}`
  """

  model = _messages.MessageField('Model', 1)
  parent = _messages.StringField(2, required=True)


class TranslateProjectsLocationsModelsDeleteRequest(_messages.Message):
  r"""A TranslateProjectsLocationsModelsDeleteRequest object.

  Fields:
    name: Required. The name of the model to delete.
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsModelsGetRequest(_messages.Message):
  r"""A TranslateProjectsLocationsModelsGetRequest object.

  Fields:
    name: Required. The resource name of the model to retrieve.
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsModelsListRequest(_messages.Message):
  r"""A TranslateProjectsLocationsModelsListRequest object.

  Fields:
    filter: Optional. An expression for filtering the models that will be
      returned. Supported filter: `dataset_id=${dataset_id}`
    pageSize: Optional. Requested page size. The server can return fewer
      results than requested.
    pageToken: Optional. A token identifying a page of results for the server
      to return. Typically obtained from next_page_token field in the response
      of a ListModels call.
    parent: Required. Name of the parent project. In form of
      `projects/{project-number-or-id}/locations/{location-id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class TranslateProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A TranslateProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class TranslateProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A TranslateProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A TranslateProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class TranslateProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A TranslateProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class TranslateProjectsLocationsOperationsWaitRequest(_messages.Message):
  r"""A TranslateProjectsLocationsOperationsWaitRequest object.

  Fields:
    name: The name of the operation resource to wait on.
    waitOperationRequest: A WaitOperationRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  waitOperationRequest = _messages.MessageField('WaitOperationRequest', 2)


class TranslateProjectsLocationsRomanizeTextRequest(_messages.Message):
  r"""A TranslateProjectsLocationsRomanizeTextRequest object.

  Fields:
    parent: Required. Project or location to make a call. Must refer to a
      caller's project. Format: `projects/{project-number-or-
      id}/locations/{location-id}` or `projects/{project-number-or-id}`. For
      global calls, use `projects/{project-number-or-id}/locations/global` or
      `projects/{project-number-or-id}`.
    romanizeTextRequest: A RomanizeTextRequest resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  romanizeTextRequest = _messages.MessageField('RomanizeTextRequest', 2)


class TranslateProjectsLocationsTranslateDocumentRequest(_messages.Message):
  r"""A TranslateProjectsLocationsTranslateDocumentRequest object.

  Fields:
    parent: Required. Location to make a regional call. Format:
      `projects/{project-number-or-id}/locations/{location-id}`. For global
      calls, use `projects/{project-number-or-id}/locations/global` or
      `projects/{project-number-or-id}`. Non-global location is required for
      requests using AutoML models or custom glossaries. Models and glossaries
      must be within the same region (have the same location-id), otherwise an
      INVALID_ARGUMENT (400) error is returned.
    translateDocumentRequest: A TranslateDocumentRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  translateDocumentRequest = _messages.MessageField('TranslateDocumentRequest', 2)


class TranslateProjectsLocationsTranslateTextRequest(_messages.Message):
  r"""A TranslateProjectsLocationsTranslateTextRequest object.

  Fields:
    parent: Required. Project or location to make a call. Must refer to a
      caller's project. Format: `projects/{project-number-or-id}` or
      `projects/{project-number-or-id}/locations/{location-id}`. For global
      calls, use `projects/{project-number-or-id}/locations/global` or
      `projects/{project-number-or-id}`. Non-global location is required for
      requests using AutoML models or custom glossaries. Models and glossaries
      must be within the same region (have same location-id), otherwise an
      INVALID_ARGUMENT (400) error is returned.
    translateTextRequest: A TranslateTextRequest resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  translateTextRequest = _messages.MessageField('TranslateTextRequest', 2)


class TranslateProjectsRomanizeTextRequest(_messages.Message):
  r"""A TranslateProjectsRomanizeTextRequest object.

  Fields:
    parent: Required. Project or location to make a call. Must refer to a
      caller's project. Format: `projects/{project-number-or-
      id}/locations/{location-id}` or `projects/{project-number-or-id}`. For
      global calls, use `projects/{project-number-or-id}/locations/global` or
      `projects/{project-number-or-id}`.
    romanizeTextRequest: A RomanizeTextRequest resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  romanizeTextRequest = _messages.MessageField('RomanizeTextRequest', 2)


class TranslateProjectsTranslateTextRequest(_messages.Message):
  r"""A TranslateProjectsTranslateTextRequest object.

  Fields:
    parent: Required. Project or location to make a call. Must refer to a
      caller's project. Format: `projects/{project-number-or-id}` or
      `projects/{project-number-or-id}/locations/{location-id}`. For global
      calls, use `projects/{project-number-or-id}/locations/global` or
      `projects/{project-number-or-id}`. Non-global location is required for
      requests using AutoML models or custom glossaries. Models and glossaries
      must be within the same region (have same location-id), otherwise an
      INVALID_ARGUMENT (400) error is returned.
    translateTextRequest: A TranslateTextRequest resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  translateTextRequest = _messages.MessageField('TranslateTextRequest', 2)


class TranslateTextGlossaryConfig(_messages.Message):
  r"""Configures which glossary is used for a specific target language and
  defines options for applying that glossary.

  Fields:
    contextualTranslationEnabled: Optional. If set to true, the glossary will
      be used for contextual translation.
    glossary: Required. The `glossary` to be applied for this translation. The
      format depends on the glossary: - User-provided custom glossary:
      `projects/{project-number-or-id}/locations/{location-
      id}/glossaries/{glossary-id}`
    ignoreCase: Optional. Indicates match is case insensitive. The default
      value is `false` if missing.
  """

  contextualTranslationEnabled = _messages.BooleanField(1)
  glossary = _messages.StringField(2)
  ignoreCase = _messages.BooleanField(3)


class TranslateTextRequest(_messages.Message):
  r"""The request message for synchronous translation.

  Messages:
    LabelsValue: Optional. The labels with user-defined metadata for the
      request. Label keys and values can be no longer than 63 characters
      (Unicode codepoints), can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter.
      See https://cloud.google.com/translate/docs/advanced/labels for more
      information.

  Fields:
    contents: Required. The content of the input in string format. We
      recommend the total content be less than 30,000 codepoints. The max
      length of this field is 1024. Use BatchTranslateText for larger text.
    glossaryConfig: Optional. Glossary to be applied. The glossary must be
      within the same region (have the same location-id) as the model,
      otherwise an INVALID_ARGUMENT (400) error is returned.
    labels: Optional. The labels with user-defined metadata for the request.
      Label keys and values can be no longer than 63 characters (Unicode
      codepoints), can only contain lowercase letters, numeric characters,
      underscores and dashes. International characters are allowed. Label
      values are optional. Label keys must start with a letter. See
      https://cloud.google.com/translate/docs/advanced/labels for more
      information.
    mimeType: Optional. The format of the source text, for example,
      "text/html", "text/plain". If left blank, the MIME type defaults to
      "text/html".
    model: Optional. The `model` type requested for this translation. The
      format depends on model type: - AutoML Translation models:
      `projects/{project-number-or-id}/locations/{location-id}/models/{model-
      id}` - General (built-in) models: `projects/{project-number-or-
      id}/locations/{location-id}/models/general/nmt`, - Translation LLM
      models: `projects/{project-number-or-id}/locations/{location-
      id}/models/general/translation-llm`, For global (non-regionalized)
      requests, use `location-id` `global`. For example, `projects/{project-
      number-or-id}/locations/global/models/general/nmt`. If not provided, the
      default Google model (NMT) will be used
    sourceLanguageCode: Optional. The ISO-639 language code of the input text
      if known, for example, "en-US" or "sr-Latn". Supported language codes
      are listed in Language Support. If the source language isn't specified,
      the API attempts to identify the source language automatically and
      returns the source language within the response.
    targetLanguageCode: Required. The ISO-639 language code to use for
      translation of the input text, set to one of the language codes listed
      in Language Support.
    transliterationConfig: Optional. Transliteration to be applied.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels with user-defined metadata for the request. Label
    keys and values can be no longer than 63 characters (Unicode codepoints),
    can only contain lowercase letters, numeric characters, underscores and
    dashes. International characters are allowed. Label values are optional.
    Label keys must start with a letter. See
    https://cloud.google.com/translate/docs/advanced/labels for more
    information.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  contents = _messages.StringField(1, repeated=True)
  glossaryConfig = _messages.MessageField('TranslateTextGlossaryConfig', 2)
  labels = _messages.MessageField('LabelsValue', 3)
  mimeType = _messages.StringField(4)
  model = _messages.StringField(5)
  sourceLanguageCode = _messages.StringField(6)
  targetLanguageCode = _messages.StringField(7)
  transliterationConfig = _messages.MessageField('TransliterationConfig', 8)


class TranslateTextResponse(_messages.Message):
  r"""A TranslateTextResponse object.

  Fields:
    glossaryTranslations: Text translation responses if a glossary is provided
      in the request. This can be the same as `translations` if no terms
      apply. This field has the same length as `contents`.
    translations: Text translation responses with no glossary applied. This
      field has the same length as `contents`.
  """

  glossaryTranslations = _messages.MessageField('Translation', 1, repeated=True)
  translations = _messages.MessageField('Translation', 2, repeated=True)


class Translation(_messages.Message):
  r"""A single translation response.

  Fields:
    detectedLanguageCode: The ISO-639 language code of source text in the
      initial request, detected automatically, if no source language was
      passed within the initial request. If the source language was passed,
      auto-detection of the language does not occur and this field is empty.
    glossaryConfig: The `glossary_config` used for this translation.
    model: Only present when `model` is present in the request. `model` here
      is normalized to have project number. For example: If the `model`
      requested in TranslationTextRequest is `projects/{project-
      id}/locations/{location-id}/models/general/nmt` then `model` here would
      be normalized to `projects/{project-number}/locations/{location-
      id}/models/general/nmt`.
    translatedText: Text translated into the target language. If an error
      occurs during translation, this field might be excluded from the
      response.
  """

  detectedLanguageCode = _messages.StringField(1)
  glossaryConfig = _messages.MessageField('TranslateTextGlossaryConfig', 2)
  model = _messages.StringField(3)
  translatedText = _messages.StringField(4)


class TransliterationConfig(_messages.Message):
  r"""Configures transliteration feature on top of translation.

  Fields:
    enableTransliteration: If true, source text in romanized form can be
      translated to the target language.
  """

  enableTransliteration = _messages.BooleanField(1)


class WaitOperationRequest(_messages.Message):
  r"""The request message for Operations.WaitOperation.

  Fields:
    timeout: The maximum duration to wait before timing out. If left blank,
      the wait will be at most the time permitted by the underlying HTTP/RPC
      protocol. If RPC context deadline is also specified, the shorter one
      will be used.
  """

  timeout = _messages.StringField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
