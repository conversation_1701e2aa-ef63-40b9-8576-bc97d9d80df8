"""Generated client library for privateca version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.privateca.v1 import privateca_v1_messages as messages


class PrivatecaV1(base_api.BaseApiClient):
  """Generated client library for service privateca version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://privateca.googleapis.com/'
  MTLS_BASE_URL = 'https://privateca.mtls.googleapis.com/'

  _PACKAGE = 'privateca'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'PrivatecaV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new privateca handle."""
    url = url or self.BASE_URL
    super(PrivatecaV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_caPools_certificateAuthorities_certificateRevocationLists = self.ProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsService(self)
    self.projects_locations_caPools_certificateAuthorities = self.ProjectsLocationsCaPoolsCertificateAuthoritiesService(self)
    self.projects_locations_caPools_certificates = self.ProjectsLocationsCaPoolsCertificatesService(self)
    self.projects_locations_caPools = self.ProjectsLocationsCaPoolsService(self)
    self.projects_locations_certificateTemplates = self.ProjectsLocationsCertificateTemplatesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsService(base_api.BaseApiService):
    """Service class for the projects_locations_caPools_certificateAuthorities_certificateRevocationLists resource."""

    _NAME = 'projects_locations_caPools_certificateAuthorities_certificateRevocationLists'

    def __init__(self, client):
      super(PrivatecaV1.ProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Returns a CertificateRevocationList.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CertificateRevocationList) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities/{certificateAuthoritiesId}/certificateRevocationLists/{certificateRevocationListsId}',
        http_method='GET',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.certificateRevocationLists.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsGetRequest',
        response_type_name='CertificateRevocationList',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities/{certificateAuthoritiesId}/certificateRevocationLists/{certificateRevocationListsId}:getIamPolicy',
        http_method='GET',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.certificateRevocationLists.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists CertificateRevocationLists.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCertificateRevocationListsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities/{certificateAuthoritiesId}/certificateRevocationLists',
        http_method='GET',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.certificateRevocationLists.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/certificateRevocationLists',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsListRequest',
        response_type_name='ListCertificateRevocationListsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a CertificateRevocationList.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities/{certificateAuthoritiesId}/certificateRevocationLists/{certificateRevocationListsId}',
        http_method='PATCH',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.certificateRevocationLists.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='certificateRevocationList',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities/{certificateAuthoritiesId}/certificateRevocationLists/{certificateRevocationListsId}:setIamPolicy',
        http_method='POST',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.certificateRevocationLists.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities/{certificateAuthoritiesId}/certificateRevocationLists/{certificateRevocationListsId}:testIamPermissions',
        http_method='POST',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.certificateRevocationLists.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesCertificateRevocationListsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsCaPoolsCertificateAuthoritiesService(base_api.BaseApiService):
    """Service class for the projects_locations_caPools_certificateAuthorities resource."""

    _NAME = 'projects_locations_caPools_certificateAuthorities'

    def __init__(self, client):
      super(PrivatecaV1.ProjectsLocationsCaPoolsCertificateAuthoritiesService, self).__init__(client)
      self._upload_configs = {
          }

    def Activate(self, request, global_params=None):
      r"""Activate a CertificateAuthority that is in state AWAITING_USER_ACTIVATION and is of type SUBORDINATE. After the parent Certificate Authority signs a certificate signing request from FetchCertificateAuthorityCsr, this method can complete the activation process.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesActivateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Activate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Activate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities/{certificateAuthoritiesId}:activate',
        http_method='POST',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.activate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:activate',
        request_field='activateCertificateAuthorityRequest',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesActivateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Create a new CertificateAuthority in a given Project and Location.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities',
        http_method='POST',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['certificateAuthorityId', 'requestId'],
        relative_path='v1/{+parent}/certificateAuthorities',
        request_field='certificateAuthority',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a CertificateAuthority.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities/{certificateAuthoritiesId}',
        http_method='DELETE',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['ignoreActiveCertificates', 'ignoreDependentResources', 'requestId', 'skipGracePeriod'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Disable(self, request, global_params=None):
      r"""Disable a CertificateAuthority.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesDisableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Disable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Disable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities/{certificateAuthoritiesId}:disable',
        http_method='POST',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.disable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:disable',
        request_field='disableCertificateAuthorityRequest',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesDisableRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Enable(self, request, global_params=None):
      r"""Enable a CertificateAuthority.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesEnableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Enable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities/{certificateAuthoritiesId}:enable',
        http_method='POST',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.enable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:enable',
        request_field='enableCertificateAuthorityRequest',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesEnableRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Fetch(self, request, global_params=None):
      r"""Fetch a certificate signing request (CSR) from a CertificateAuthority that is in state AWAITING_USER_ACTIVATION and is of type SUBORDINATE. The CSR must then be signed by the desired parent Certificate Authority, which could be another CertificateAuthority resource, or could be an on-prem certificate authority. See also ActivateCertificateAuthority.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesFetchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FetchCertificateAuthorityCsrResponse) The response message.
      """
      config = self.GetMethodConfig('Fetch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Fetch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities/{certificateAuthoritiesId}:fetch',
        http_method='GET',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.fetch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:fetch',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesFetchRequest',
        response_type_name='FetchCertificateAuthorityCsrResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns a CertificateAuthority.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CertificateAuthority) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities/{certificateAuthoritiesId}',
        http_method='GET',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesGetRequest',
        response_type_name='CertificateAuthority',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists CertificateAuthorities.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCertificateAuthoritiesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities',
        http_method='GET',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/certificateAuthorities',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesListRequest',
        response_type_name='ListCertificateAuthoritiesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a CertificateAuthority.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities/{certificateAuthoritiesId}',
        http_method='PATCH',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='certificateAuthority',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undelete a CertificateAuthority that has been deleted.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificateAuthorities/{certificateAuthoritiesId}:undelete',
        http_method='POST',
        method_id='privateca.projects.locations.caPools.certificateAuthorities.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeleteCertificateAuthorityRequest',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificateAuthoritiesUndeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsCaPoolsCertificatesService(base_api.BaseApiService):
    """Service class for the projects_locations_caPools_certificates resource."""

    _NAME = 'projects_locations_caPools_certificates'

    def __init__(self, client):
      super(PrivatecaV1.ProjectsLocationsCaPoolsCertificatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new Certificate in a given Project, Location from a particular CaPool.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Certificate) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificates',
        http_method='POST',
        method_id='privateca.projects.locations.caPools.certificates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['certificateId', 'issuingCertificateAuthorityId', 'requestId', 'validateOnly'],
        relative_path='v1/{+parent}/certificates',
        request_field='certificate',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificatesCreateRequest',
        response_type_name='Certificate',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns a Certificate.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Certificate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificates/{certificatesId}',
        http_method='GET',
        method_id='privateca.projects.locations.caPools.certificates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificatesGetRequest',
        response_type_name='Certificate',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Certificates.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCertificatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificates',
        http_method='GET',
        method_id='privateca.projects.locations.caPools.certificates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/certificates',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificatesListRequest',
        response_type_name='ListCertificatesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a Certificate. Currently, the only field you can update is the labels field.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificatesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Certificate) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificates/{certificatesId}',
        http_method='PATCH',
        method_id='privateca.projects.locations.caPools.certificates.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='certificate',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificatesPatchRequest',
        response_type_name='Certificate',
        supports_download=False,
    )

    def Revoke(self, request, global_params=None):
      r"""Revoke a Certificate.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCertificatesRevokeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Certificate) The response message.
      """
      config = self.GetMethodConfig('Revoke')
      return self._RunMethod(
          config, request, global_params=global_params)

    Revoke.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}/certificates/{certificatesId}:revoke',
        http_method='POST',
        method_id='privateca.projects.locations.caPools.certificates.revoke',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:revoke',
        request_field='revokeCertificateRequest',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCertificatesRevokeRequest',
        response_type_name='Certificate',
        supports_download=False,
    )

  class ProjectsLocationsCaPoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_caPools resource."""

    _NAME = 'projects_locations_caPools'

    def __init__(self, client):
      super(PrivatecaV1.ProjectsLocationsCaPoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a CaPool.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools',
        http_method='POST',
        method_id='privateca.projects.locations.caPools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['caPoolId', 'requestId'],
        relative_path='v1/{+parent}/caPools',
        request_field='caPool',
        request_type_name='PrivatecaProjectsLocationsCaPoolsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a CaPool.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}',
        http_method='DELETE',
        method_id='privateca.projects.locations.caPools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['ignoreDependentResources', 'requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCaPoolsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def FetchCaCerts(self, request, global_params=None):
      r"""FetchCaCerts returns the current trust anchor for the CaPool. This will include CA certificate chains for all certificate authorities in the ENABLED, DISABLED, or STAGED states.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsFetchCaCertsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FetchCaCertsResponse) The response message.
      """
      config = self.GetMethodConfig('FetchCaCerts')
      return self._RunMethod(
          config, request, global_params=global_params)

    FetchCaCerts.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}:fetchCaCerts',
        http_method='POST',
        method_id='privateca.projects.locations.caPools.fetchCaCerts',
        ordered_params=['caPool'],
        path_params=['caPool'],
        query_params=[],
        relative_path='v1/{+caPool}:fetchCaCerts',
        request_field='fetchCaCertsRequest',
        request_type_name='PrivatecaProjectsLocationsCaPoolsFetchCaCertsRequest',
        response_type_name='FetchCaCertsResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns a CaPool.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CaPool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}',
        http_method='GET',
        method_id='privateca.projects.locations.caPools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCaPoolsGetRequest',
        response_type_name='CaPool',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}:getIamPolicy',
        http_method='GET',
        method_id='privateca.projects.locations.caPools.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCaPoolsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists CaPools.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCaPoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools',
        http_method='GET',
        method_id='privateca.projects.locations.caPools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/caPools',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCaPoolsListRequest',
        response_type_name='ListCaPoolsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a CaPool.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}',
        http_method='PATCH',
        method_id='privateca.projects.locations.caPools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='caPool',
        request_type_name='PrivatecaProjectsLocationsCaPoolsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}:setIamPolicy',
        http_method='POST',
        method_id='privateca.projects.locations.caPools.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='PrivatecaProjectsLocationsCaPoolsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (PrivatecaProjectsLocationsCaPoolsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/caPools/{caPoolsId}:testIamPermissions',
        http_method='POST',
        method_id='privateca.projects.locations.caPools.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='PrivatecaProjectsLocationsCaPoolsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsCertificateTemplatesService(base_api.BaseApiService):
    """Service class for the projects_locations_certificateTemplates resource."""

    _NAME = 'projects_locations_certificateTemplates'

    def __init__(self, client):
      super(PrivatecaV1.ProjectsLocationsCertificateTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new CertificateTemplate in a given Project and Location.

      Args:
        request: (PrivatecaProjectsLocationsCertificateTemplatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/certificateTemplates',
        http_method='POST',
        method_id='privateca.projects.locations.certificateTemplates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['certificateTemplateId', 'requestId'],
        relative_path='v1/{+parent}/certificateTemplates',
        request_field='certificateTemplate',
        request_type_name='PrivatecaProjectsLocationsCertificateTemplatesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""DeleteCertificateTemplate deletes a CertificateTemplate.

      Args:
        request: (PrivatecaProjectsLocationsCertificateTemplatesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/certificateTemplates/{certificateTemplatesId}',
        http_method='DELETE',
        method_id='privateca.projects.locations.certificateTemplates.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCertificateTemplatesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns a CertificateTemplate.

      Args:
        request: (PrivatecaProjectsLocationsCertificateTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CertificateTemplate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/certificateTemplates/{certificateTemplatesId}',
        http_method='GET',
        method_id='privateca.projects.locations.certificateTemplates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCertificateTemplatesGetRequest',
        response_type_name='CertificateTemplate',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (PrivatecaProjectsLocationsCertificateTemplatesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/certificateTemplates/{certificateTemplatesId}:getIamPolicy',
        http_method='GET',
        method_id='privateca.projects.locations.certificateTemplates.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCertificateTemplatesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists CertificateTemplates.

      Args:
        request: (PrivatecaProjectsLocationsCertificateTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCertificateTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/certificateTemplates',
        http_method='GET',
        method_id='privateca.projects.locations.certificateTemplates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/certificateTemplates',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsCertificateTemplatesListRequest',
        response_type_name='ListCertificateTemplatesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a CertificateTemplate.

      Args:
        request: (PrivatecaProjectsLocationsCertificateTemplatesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/certificateTemplates/{certificateTemplatesId}',
        http_method='PATCH',
        method_id='privateca.projects.locations.certificateTemplates.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='certificateTemplate',
        request_type_name='PrivatecaProjectsLocationsCertificateTemplatesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (PrivatecaProjectsLocationsCertificateTemplatesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/certificateTemplates/{certificateTemplatesId}:setIamPolicy',
        http_method='POST',
        method_id='privateca.projects.locations.certificateTemplates.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='PrivatecaProjectsLocationsCertificateTemplatesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (PrivatecaProjectsLocationsCertificateTemplatesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/certificateTemplates/{certificateTemplatesId}:testIamPermissions',
        http_method='POST',
        method_id='privateca.projects.locations.certificateTemplates.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='PrivatecaProjectsLocationsCertificateTemplatesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(PrivatecaV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (PrivatecaProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='privateca.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='PrivatecaProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (PrivatecaProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='privateca.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (PrivatecaProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='privateca.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (PrivatecaProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='privateca.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(PrivatecaV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (PrivatecaProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='privateca.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (PrivatecaProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='privateca.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='PrivatecaProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(PrivatecaV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
