"""Generated client library for securitycenter version v1beta2."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.securitycenter.v1beta2 import securitycenter_v1beta2_messages as messages


class SecuritycenterV1beta2(base_api.BaseApiClient):
  """Generated client library for service securitycenter version v1beta2."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://securitycenter.googleapis.com/'
  MTLS_BASE_URL = 'https://securitycenter.mtls.googleapis.com/'

  _PACKAGE = 'securitycenter'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1beta2'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'SecuritycenterV1beta2'
  _URL_VERSION = 'v1beta2'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new securitycenter handle."""
    url = url or self.BASE_URL
    super(SecuritycenterV1beta2, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.folders_containerThreatDetectionSettings = self.FoldersContainerThreatDetectionSettingsService(self)
    self.folders_eventThreatDetectionSettings = self.FoldersEventThreatDetectionSettingsService(self)
    self.folders_rapidVulnerabilityDetectionSettings = self.FoldersRapidVulnerabilityDetectionSettingsService(self)
    self.folders_securityHealthAnalyticsSettings = self.FoldersSecurityHealthAnalyticsSettingsService(self)
    self.folders_virtualMachineThreatDetectionSettings = self.FoldersVirtualMachineThreatDetectionSettingsService(self)
    self.folders_webSecurityScannerSettings = self.FoldersWebSecurityScannerSettingsService(self)
    self.folders = self.FoldersService(self)
    self.organizations_containerThreatDetectionSettings = self.OrganizationsContainerThreatDetectionSettingsService(self)
    self.organizations_eventThreatDetectionSettings = self.OrganizationsEventThreatDetectionSettingsService(self)
    self.organizations_rapidVulnerabilityDetectionSettings = self.OrganizationsRapidVulnerabilityDetectionSettingsService(self)
    self.organizations_securityHealthAnalyticsSettings = self.OrganizationsSecurityHealthAnalyticsSettingsService(self)
    self.organizations_virtualMachineThreatDetectionSettings = self.OrganizationsVirtualMachineThreatDetectionSettingsService(self)
    self.organizations_webSecurityScannerSettings = self.OrganizationsWebSecurityScannerSettingsService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_containerThreatDetectionSettings = self.ProjectsContainerThreatDetectionSettingsService(self)
    self.projects_eventThreatDetectionSettings = self.ProjectsEventThreatDetectionSettingsService(self)
    self.projects_locations_clusters_containerThreatDetectionSettings = self.ProjectsLocationsClustersContainerThreatDetectionSettingsService(self)
    self.projects_locations_clusters = self.ProjectsLocationsClustersService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects_rapidVulnerabilityDetectionSettings = self.ProjectsRapidVulnerabilityDetectionSettingsService(self)
    self.projects_securityHealthAnalyticsSettings = self.ProjectsSecurityHealthAnalyticsSettingsService(self)
    self.projects_virtualMachineThreatDetectionSettings = self.ProjectsVirtualMachineThreatDetectionSettingsService(self)
    self.projects_webSecurityScannerSettings = self.ProjectsWebSecurityScannerSettingsService(self)
    self.projects = self.ProjectsService(self)

  class FoldersContainerThreatDetectionSettingsService(base_api.BaseApiService):
    """Service class for the folders_containerThreatDetectionSettings resource."""

    _NAME = 'folders_containerThreatDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.FoldersContainerThreatDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective ContainerThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterFoldersContainerThreatDetectionSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ContainerThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/containerThreatDetectionSettings:calculate',
        http_method='GET',
        method_id='securitycenter.folders.containerThreatDetectionSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterFoldersContainerThreatDetectionSettingsCalculateRequest',
        response_type_name='ContainerThreatDetectionSettings',
        supports_download=False,
    )

  class FoldersEventThreatDetectionSettingsService(base_api.BaseApiService):
    """Service class for the folders_eventThreatDetectionSettings resource."""

    _NAME = 'folders_eventThreatDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.FoldersEventThreatDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective EventThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterFoldersEventThreatDetectionSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/eventThreatDetectionSettings:calculate',
        http_method='GET',
        method_id='securitycenter.folders.eventThreatDetectionSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterFoldersEventThreatDetectionSettingsCalculateRequest',
        response_type_name='EventThreatDetectionSettings',
        supports_download=False,
    )

  class FoldersRapidVulnerabilityDetectionSettingsService(base_api.BaseApiService):
    """Service class for the folders_rapidVulnerabilityDetectionSettings resource."""

    _NAME = 'folders_rapidVulnerabilityDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.FoldersRapidVulnerabilityDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective RapidVulnerabilityDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterFoldersRapidVulnerabilityDetectionSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RapidVulnerabilityDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/rapidVulnerabilityDetectionSettings:calculate',
        http_method='GET',
        method_id='securitycenter.folders.rapidVulnerabilityDetectionSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterFoldersRapidVulnerabilityDetectionSettingsCalculateRequest',
        response_type_name='RapidVulnerabilityDetectionSettings',
        supports_download=False,
    )

  class FoldersSecurityHealthAnalyticsSettingsService(base_api.BaseApiService):
    """Service class for the folders_securityHealthAnalyticsSettings resource."""

    _NAME = 'folders_securityHealthAnalyticsSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.FoldersSecurityHealthAnalyticsSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective SecurityHealthAnalyticsSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterFoldersSecurityHealthAnalyticsSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/securityHealthAnalyticsSettings:calculate',
        http_method='GET',
        method_id='securitycenter.folders.securityHealthAnalyticsSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterFoldersSecurityHealthAnalyticsSettingsCalculateRequest',
        response_type_name='SecurityHealthAnalyticsSettings',
        supports_download=False,
    )

  class FoldersVirtualMachineThreatDetectionSettingsService(base_api.BaseApiService):
    """Service class for the folders_virtualMachineThreatDetectionSettings resource."""

    _NAME = 'folders_virtualMachineThreatDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.FoldersVirtualMachineThreatDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective VirtualMachineThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterFoldersVirtualMachineThreatDetectionSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VirtualMachineThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/virtualMachineThreatDetectionSettings:calculate',
        http_method='GET',
        method_id='securitycenter.folders.virtualMachineThreatDetectionSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterFoldersVirtualMachineThreatDetectionSettingsCalculateRequest',
        response_type_name='VirtualMachineThreatDetectionSettings',
        supports_download=False,
    )

  class FoldersWebSecurityScannerSettingsService(base_api.BaseApiService):
    """Service class for the folders_webSecurityScannerSettings resource."""

    _NAME = 'folders_webSecurityScannerSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.FoldersWebSecurityScannerSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective WebSecurityScannerSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterFoldersWebSecurityScannerSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WebSecurityScannerSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/webSecurityScannerSettings:calculate',
        http_method='GET',
        method_id='securitycenter.folders.webSecurityScannerSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterFoldersWebSecurityScannerSettingsCalculateRequest',
        response_type_name='WebSecurityScannerSettings',
        supports_download=False,
    )

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(SecuritycenterV1beta2.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

    def GetContainerThreatDetectionSettings(self, request, global_params=None):
      r"""Get the ContainerThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetContainerThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateContainerThreatDetectionSettings for this purpose.

      Args:
        request: (SecuritycenterFoldersGetContainerThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ContainerThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('GetContainerThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetContainerThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/containerThreatDetectionSettings',
        http_method='GET',
        method_id='securitycenter.folders.getContainerThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersGetContainerThreatDetectionSettingsRequest',
        response_type_name='ContainerThreatDetectionSettings',
        supports_download=False,
    )

    def GetEventThreatDetectionSettings(self, request, global_params=None):
      r"""Get the EventThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetEventThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateEventThreatDetectionSettings for this purpose.

      Args:
        request: (SecuritycenterFoldersGetEventThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('GetEventThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetEventThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/eventThreatDetectionSettings',
        http_method='GET',
        method_id='securitycenter.folders.getEventThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersGetEventThreatDetectionSettingsRequest',
        response_type_name='EventThreatDetectionSettings',
        supports_download=False,
    )

    def GetRapidVulnerabilityDetectionSettings(self, request, global_params=None):
      r"""Get the RapidVulnerabilityDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetRapidVulnerabilityDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateRapidVulnerabilityDetectionSettings for this purpose.

      Args:
        request: (SecuritycenterFoldersGetRapidVulnerabilityDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RapidVulnerabilityDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('GetRapidVulnerabilityDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetRapidVulnerabilityDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/rapidVulnerabilityDetectionSettings',
        http_method='GET',
        method_id='securitycenter.folders.getRapidVulnerabilityDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersGetRapidVulnerabilityDetectionSettingsRequest',
        response_type_name='RapidVulnerabilityDetectionSettings',
        supports_download=False,
    )

    def GetSecurityCenterSettings(self, request, global_params=None):
      r"""Get the SecurityCenterSettings resource.

      Args:
        request: (SecuritycenterFoldersGetSecurityCenterSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityCenterSettings) The response message.
      """
      config = self.GetMethodConfig('GetSecurityCenterSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSecurityCenterSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/securityCenterSettings',
        http_method='GET',
        method_id='securitycenter.folders.getSecurityCenterSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersGetSecurityCenterSettingsRequest',
        response_type_name='SecurityCenterSettings',
        supports_download=False,
    )

    def GetSecurityHealthAnalyticsSettings(self, request, global_params=None):
      r"""Get the SecurityHealthAnalyticsSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetSecurityHealthAnalyticsSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateSecurityHealthAnalyticsSettings for this purpose.

      Args:
        request: (SecuritycenterFoldersGetSecurityHealthAnalyticsSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsSettings) The response message.
      """
      config = self.GetMethodConfig('GetSecurityHealthAnalyticsSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSecurityHealthAnalyticsSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/securityHealthAnalyticsSettings',
        http_method='GET',
        method_id='securitycenter.folders.getSecurityHealthAnalyticsSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersGetSecurityHealthAnalyticsSettingsRequest',
        response_type_name='SecurityHealthAnalyticsSettings',
        supports_download=False,
    )

    def GetVirtualMachineThreatDetectionSettings(self, request, global_params=None):
      r"""Get the VirtualMachineThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetVirtualMachineThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateVirtualMachineThreatDetectionSettings for this purpose.

      Args:
        request: (SecuritycenterFoldersGetVirtualMachineThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VirtualMachineThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('GetVirtualMachineThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetVirtualMachineThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/virtualMachineThreatDetectionSettings',
        http_method='GET',
        method_id='securitycenter.folders.getVirtualMachineThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersGetVirtualMachineThreatDetectionSettingsRequest',
        response_type_name='VirtualMachineThreatDetectionSettings',
        supports_download=False,
    )

    def GetWebSecurityScannerSettings(self, request, global_params=None):
      r"""Get the WebSecurityScannerSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetWebSecurityScannerSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateWebSecurityScannerSettings for this purpose.

      Args:
        request: (SecuritycenterFoldersGetWebSecurityScannerSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WebSecurityScannerSettings) The response message.
      """
      config = self.GetMethodConfig('GetWebSecurityScannerSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetWebSecurityScannerSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/webSecurityScannerSettings',
        http_method='GET',
        method_id='securitycenter.folders.getWebSecurityScannerSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersGetWebSecurityScannerSettingsRequest',
        response_type_name='WebSecurityScannerSettings',
        supports_download=False,
    )

    def UpdateContainerThreatDetectionSettings(self, request, global_params=None):
      r"""Update the ContainerThreatDetectionSettings resource.

      Args:
        request: (SecuritycenterFoldersUpdateContainerThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ContainerThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateContainerThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateContainerThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/containerThreatDetectionSettings',
        http_method='PATCH',
        method_id='securitycenter.folders.updateContainerThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='containerThreatDetectionSettings',
        request_type_name='SecuritycenterFoldersUpdateContainerThreatDetectionSettingsRequest',
        response_type_name='ContainerThreatDetectionSettings',
        supports_download=False,
    )

    def UpdateEventThreatDetectionSettings(self, request, global_params=None):
      r"""Update the EventThreatDetectionSettings resource.

      Args:
        request: (SecuritycenterFoldersUpdateEventThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateEventThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateEventThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/eventThreatDetectionSettings',
        http_method='PATCH',
        method_id='securitycenter.folders.updateEventThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='eventThreatDetectionSettings',
        request_type_name='SecuritycenterFoldersUpdateEventThreatDetectionSettingsRequest',
        response_type_name='EventThreatDetectionSettings',
        supports_download=False,
    )

    def UpdateRapidVulnerabilityDetectionSettings(self, request, global_params=None):
      r"""Update the RapidVulnerabilityDetectionSettings resource.

      Args:
        request: (SecuritycenterFoldersUpdateRapidVulnerabilityDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RapidVulnerabilityDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateRapidVulnerabilityDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateRapidVulnerabilityDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/rapidVulnerabilityDetectionSettings',
        http_method='PATCH',
        method_id='securitycenter.folders.updateRapidVulnerabilityDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='rapidVulnerabilityDetectionSettings',
        request_type_name='SecuritycenterFoldersUpdateRapidVulnerabilityDetectionSettingsRequest',
        response_type_name='RapidVulnerabilityDetectionSettings',
        supports_download=False,
    )

    def UpdateSecurityHealthAnalyticsSettings(self, request, global_params=None):
      r"""Update the SecurityHealthAnalyticsSettings resource.

      Args:
        request: (SecuritycenterFoldersUpdateSecurityHealthAnalyticsSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityHealthAnalyticsSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityHealthAnalyticsSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/securityHealthAnalyticsSettings',
        http_method='PATCH',
        method_id='securitycenter.folders.updateSecurityHealthAnalyticsSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='securityHealthAnalyticsSettings',
        request_type_name='SecuritycenterFoldersUpdateSecurityHealthAnalyticsSettingsRequest',
        response_type_name='SecurityHealthAnalyticsSettings',
        supports_download=False,
    )

    def UpdateVirtualMachineThreatDetectionSettings(self, request, global_params=None):
      r"""Update the VirtualMachineThreatDetectionSettings resource.

      Args:
        request: (SecuritycenterFoldersUpdateVirtualMachineThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VirtualMachineThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateVirtualMachineThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateVirtualMachineThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/virtualMachineThreatDetectionSettings',
        http_method='PATCH',
        method_id='securitycenter.folders.updateVirtualMachineThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='virtualMachineThreatDetectionSettings',
        request_type_name='SecuritycenterFoldersUpdateVirtualMachineThreatDetectionSettingsRequest',
        response_type_name='VirtualMachineThreatDetectionSettings',
        supports_download=False,
    )

    def UpdateWebSecurityScannerSettings(self, request, global_params=None):
      r"""Update the WebSecurityScannerSettings resource.

      Args:
        request: (SecuritycenterFoldersUpdateWebSecurityScannerSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WebSecurityScannerSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateWebSecurityScannerSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateWebSecurityScannerSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/folders/{foldersId}/webSecurityScannerSettings',
        http_method='PATCH',
        method_id='securitycenter.folders.updateWebSecurityScannerSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='webSecurityScannerSettings',
        request_type_name='SecuritycenterFoldersUpdateWebSecurityScannerSettingsRequest',
        response_type_name='WebSecurityScannerSettings',
        supports_download=False,
    )

  class OrganizationsContainerThreatDetectionSettingsService(base_api.BaseApiService):
    """Service class for the organizations_containerThreatDetectionSettings resource."""

    _NAME = 'organizations_containerThreatDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.OrganizationsContainerThreatDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective ContainerThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterOrganizationsContainerThreatDetectionSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ContainerThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/containerThreatDetectionSettings:calculate',
        http_method='GET',
        method_id='securitycenter.organizations.containerThreatDetectionSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterOrganizationsContainerThreatDetectionSettingsCalculateRequest',
        response_type_name='ContainerThreatDetectionSettings',
        supports_download=False,
    )

  class OrganizationsEventThreatDetectionSettingsService(base_api.BaseApiService):
    """Service class for the organizations_eventThreatDetectionSettings resource."""

    _NAME = 'organizations_eventThreatDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.OrganizationsEventThreatDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective EventThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterOrganizationsEventThreatDetectionSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/eventThreatDetectionSettings:calculate',
        http_method='GET',
        method_id='securitycenter.organizations.eventThreatDetectionSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterOrganizationsEventThreatDetectionSettingsCalculateRequest',
        response_type_name='EventThreatDetectionSettings',
        supports_download=False,
    )

  class OrganizationsRapidVulnerabilityDetectionSettingsService(base_api.BaseApiService):
    """Service class for the organizations_rapidVulnerabilityDetectionSettings resource."""

    _NAME = 'organizations_rapidVulnerabilityDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.OrganizationsRapidVulnerabilityDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective RapidVulnerabilityDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterOrganizationsRapidVulnerabilityDetectionSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RapidVulnerabilityDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/rapidVulnerabilityDetectionSettings:calculate',
        http_method='GET',
        method_id='securitycenter.organizations.rapidVulnerabilityDetectionSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterOrganizationsRapidVulnerabilityDetectionSettingsCalculateRequest',
        response_type_name='RapidVulnerabilityDetectionSettings',
        supports_download=False,
    )

  class OrganizationsSecurityHealthAnalyticsSettingsService(base_api.BaseApiService):
    """Service class for the organizations_securityHealthAnalyticsSettings resource."""

    _NAME = 'organizations_securityHealthAnalyticsSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.OrganizationsSecurityHealthAnalyticsSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective SecurityHealthAnalyticsSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/securityHealthAnalyticsSettings:calculate',
        http_method='GET',
        method_id='securitycenter.organizations.securityHealthAnalyticsSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCalculateRequest',
        response_type_name='SecurityHealthAnalyticsSettings',
        supports_download=False,
    )

  class OrganizationsVirtualMachineThreatDetectionSettingsService(base_api.BaseApiService):
    """Service class for the organizations_virtualMachineThreatDetectionSettings resource."""

    _NAME = 'organizations_virtualMachineThreatDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.OrganizationsVirtualMachineThreatDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective VirtualMachineThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterOrganizationsVirtualMachineThreatDetectionSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VirtualMachineThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/virtualMachineThreatDetectionSettings:calculate',
        http_method='GET',
        method_id='securitycenter.organizations.virtualMachineThreatDetectionSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterOrganizationsVirtualMachineThreatDetectionSettingsCalculateRequest',
        response_type_name='VirtualMachineThreatDetectionSettings',
        supports_download=False,
    )

  class OrganizationsWebSecurityScannerSettingsService(base_api.BaseApiService):
    """Service class for the organizations_webSecurityScannerSettings resource."""

    _NAME = 'organizations_webSecurityScannerSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.OrganizationsWebSecurityScannerSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective WebSecurityScannerSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterOrganizationsWebSecurityScannerSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WebSecurityScannerSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/webSecurityScannerSettings:calculate',
        http_method='GET',
        method_id='securitycenter.organizations.webSecurityScannerSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterOrganizationsWebSecurityScannerSettingsCalculateRequest',
        response_type_name='WebSecurityScannerSettings',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(SecuritycenterV1beta2.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetContainerThreatDetectionSettings(self, request, global_params=None):
      r"""Get the ContainerThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetContainerThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateContainerThreatDetectionSettings for this purpose.

      Args:
        request: (SecuritycenterOrganizationsGetContainerThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ContainerThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('GetContainerThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetContainerThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/containerThreatDetectionSettings',
        http_method='GET',
        method_id='securitycenter.organizations.getContainerThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsGetContainerThreatDetectionSettingsRequest',
        response_type_name='ContainerThreatDetectionSettings',
        supports_download=False,
    )

    def GetEventThreatDetectionSettings(self, request, global_params=None):
      r"""Get the EventThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetEventThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateEventThreatDetectionSettings for this purpose.

      Args:
        request: (SecuritycenterOrganizationsGetEventThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('GetEventThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetEventThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/eventThreatDetectionSettings',
        http_method='GET',
        method_id='securitycenter.organizations.getEventThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsGetEventThreatDetectionSettingsRequest',
        response_type_name='EventThreatDetectionSettings',
        supports_download=False,
    )

    def GetRapidVulnerabilityDetectionSettings(self, request, global_params=None):
      r"""Get the RapidVulnerabilityDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetRapidVulnerabilityDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateRapidVulnerabilityDetectionSettings for this purpose.

      Args:
        request: (SecuritycenterOrganizationsGetRapidVulnerabilityDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RapidVulnerabilityDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('GetRapidVulnerabilityDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetRapidVulnerabilityDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/rapidVulnerabilityDetectionSettings',
        http_method='GET',
        method_id='securitycenter.organizations.getRapidVulnerabilityDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsGetRapidVulnerabilityDetectionSettingsRequest',
        response_type_name='RapidVulnerabilityDetectionSettings',
        supports_download=False,
    )

    def GetSecurityCenterSettings(self, request, global_params=None):
      r"""Get the SecurityCenterSettings resource.

      Args:
        request: (SecuritycenterOrganizationsGetSecurityCenterSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityCenterSettings) The response message.
      """
      config = self.GetMethodConfig('GetSecurityCenterSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSecurityCenterSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/securityCenterSettings',
        http_method='GET',
        method_id='securitycenter.organizations.getSecurityCenterSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsGetSecurityCenterSettingsRequest',
        response_type_name='SecurityCenterSettings',
        supports_download=False,
    )

    def GetSecurityHealthAnalyticsSettings(self, request, global_params=None):
      r"""Get the SecurityHealthAnalyticsSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetSecurityHealthAnalyticsSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateSecurityHealthAnalyticsSettings for this purpose.

      Args:
        request: (SecuritycenterOrganizationsGetSecurityHealthAnalyticsSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsSettings) The response message.
      """
      config = self.GetMethodConfig('GetSecurityHealthAnalyticsSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSecurityHealthAnalyticsSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/securityHealthAnalyticsSettings',
        http_method='GET',
        method_id='securitycenter.organizations.getSecurityHealthAnalyticsSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsGetSecurityHealthAnalyticsSettingsRequest',
        response_type_name='SecurityHealthAnalyticsSettings',
        supports_download=False,
    )

    def GetSubscription(self, request, global_params=None):
      r"""Get the Subscription resource.

      Args:
        request: (SecuritycenterOrganizationsGetSubscriptionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Subscription) The response message.
      """
      config = self.GetMethodConfig('GetSubscription')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSubscription.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/subscription',
        http_method='GET',
        method_id='securitycenter.organizations.getSubscription',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsGetSubscriptionRequest',
        response_type_name='Subscription',
        supports_download=False,
    )

    def GetVirtualMachineThreatDetectionSettings(self, request, global_params=None):
      r"""Get the VirtualMachineThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetVirtualMachineThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateVirtualMachineThreatDetectionSettings for this purpose.

      Args:
        request: (SecuritycenterOrganizationsGetVirtualMachineThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VirtualMachineThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('GetVirtualMachineThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetVirtualMachineThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/virtualMachineThreatDetectionSettings',
        http_method='GET',
        method_id='securitycenter.organizations.getVirtualMachineThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsGetVirtualMachineThreatDetectionSettingsRequest',
        response_type_name='VirtualMachineThreatDetectionSettings',
        supports_download=False,
    )

    def GetWebSecurityScannerSettings(self, request, global_params=None):
      r"""Get the WebSecurityScannerSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetWebSecurityScannerSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateWebSecurityScannerSettings for this purpose.

      Args:
        request: (SecuritycenterOrganizationsGetWebSecurityScannerSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WebSecurityScannerSettings) The response message.
      """
      config = self.GetMethodConfig('GetWebSecurityScannerSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetWebSecurityScannerSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/webSecurityScannerSettings',
        http_method='GET',
        method_id='securitycenter.organizations.getWebSecurityScannerSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsGetWebSecurityScannerSettingsRequest',
        response_type_name='WebSecurityScannerSettings',
        supports_download=False,
    )

    def UpdateContainerThreatDetectionSettings(self, request, global_params=None):
      r"""Update the ContainerThreatDetectionSettings resource.

      Args:
        request: (SecuritycenterOrganizationsUpdateContainerThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ContainerThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateContainerThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateContainerThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/containerThreatDetectionSettings',
        http_method='PATCH',
        method_id='securitycenter.organizations.updateContainerThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='containerThreatDetectionSettings',
        request_type_name='SecuritycenterOrganizationsUpdateContainerThreatDetectionSettingsRequest',
        response_type_name='ContainerThreatDetectionSettings',
        supports_download=False,
    )

    def UpdateEventThreatDetectionSettings(self, request, global_params=None):
      r"""Update the EventThreatDetectionSettings resource.

      Args:
        request: (SecuritycenterOrganizationsUpdateEventThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateEventThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateEventThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/eventThreatDetectionSettings',
        http_method='PATCH',
        method_id='securitycenter.organizations.updateEventThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='eventThreatDetectionSettings',
        request_type_name='SecuritycenterOrganizationsUpdateEventThreatDetectionSettingsRequest',
        response_type_name='EventThreatDetectionSettings',
        supports_download=False,
    )

    def UpdateRapidVulnerabilityDetectionSettings(self, request, global_params=None):
      r"""Update the RapidVulnerabilityDetectionSettings resource.

      Args:
        request: (SecuritycenterOrganizationsUpdateRapidVulnerabilityDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RapidVulnerabilityDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateRapidVulnerabilityDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateRapidVulnerabilityDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/rapidVulnerabilityDetectionSettings',
        http_method='PATCH',
        method_id='securitycenter.organizations.updateRapidVulnerabilityDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='rapidVulnerabilityDetectionSettings',
        request_type_name='SecuritycenterOrganizationsUpdateRapidVulnerabilityDetectionSettingsRequest',
        response_type_name='RapidVulnerabilityDetectionSettings',
        supports_download=False,
    )

    def UpdateSecurityHealthAnalyticsSettings(self, request, global_params=None):
      r"""Update the SecurityHealthAnalyticsSettings resource.

      Args:
        request: (SecuritycenterOrganizationsUpdateSecurityHealthAnalyticsSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityHealthAnalyticsSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityHealthAnalyticsSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/securityHealthAnalyticsSettings',
        http_method='PATCH',
        method_id='securitycenter.organizations.updateSecurityHealthAnalyticsSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='securityHealthAnalyticsSettings',
        request_type_name='SecuritycenterOrganizationsUpdateSecurityHealthAnalyticsSettingsRequest',
        response_type_name='SecurityHealthAnalyticsSettings',
        supports_download=False,
    )

    def UpdateVirtualMachineThreatDetectionSettings(self, request, global_params=None):
      r"""Update the VirtualMachineThreatDetectionSettings resource.

      Args:
        request: (SecuritycenterOrganizationsUpdateVirtualMachineThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VirtualMachineThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateVirtualMachineThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateVirtualMachineThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/virtualMachineThreatDetectionSettings',
        http_method='PATCH',
        method_id='securitycenter.organizations.updateVirtualMachineThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='virtualMachineThreatDetectionSettings',
        request_type_name='SecuritycenterOrganizationsUpdateVirtualMachineThreatDetectionSettingsRequest',
        response_type_name='VirtualMachineThreatDetectionSettings',
        supports_download=False,
    )

    def UpdateWebSecurityScannerSettings(self, request, global_params=None):
      r"""Update the WebSecurityScannerSettings resource.

      Args:
        request: (SecuritycenterOrganizationsUpdateWebSecurityScannerSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WebSecurityScannerSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateWebSecurityScannerSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateWebSecurityScannerSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/organizations/{organizationsId}/webSecurityScannerSettings',
        http_method='PATCH',
        method_id='securitycenter.organizations.updateWebSecurityScannerSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='webSecurityScannerSettings',
        request_type_name='SecuritycenterOrganizationsUpdateWebSecurityScannerSettingsRequest',
        response_type_name='WebSecurityScannerSettings',
        supports_download=False,
    )

  class ProjectsContainerThreatDetectionSettingsService(base_api.BaseApiService):
    """Service class for the projects_containerThreatDetectionSettings resource."""

    _NAME = 'projects_containerThreatDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.ProjectsContainerThreatDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective ContainerThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterProjectsContainerThreatDetectionSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ContainerThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/containerThreatDetectionSettings:calculate',
        http_method='GET',
        method_id='securitycenter.projects.containerThreatDetectionSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterProjectsContainerThreatDetectionSettingsCalculateRequest',
        response_type_name='ContainerThreatDetectionSettings',
        supports_download=False,
    )

  class ProjectsEventThreatDetectionSettingsService(base_api.BaseApiService):
    """Service class for the projects_eventThreatDetectionSettings resource."""

    _NAME = 'projects_eventThreatDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.ProjectsEventThreatDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective EventThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterProjectsEventThreatDetectionSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/eventThreatDetectionSettings:calculate',
        http_method='GET',
        method_id='securitycenter.projects.eventThreatDetectionSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterProjectsEventThreatDetectionSettingsCalculateRequest',
        response_type_name='EventThreatDetectionSettings',
        supports_download=False,
    )

  class ProjectsLocationsClustersContainerThreatDetectionSettingsService(base_api.BaseApiService):
    """Service class for the projects_locations_clusters_containerThreatDetectionSettings resource."""

    _NAME = 'projects_locations_clusters_containerThreatDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.ProjectsLocationsClustersContainerThreatDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective ContainerThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterProjectsLocationsClustersContainerThreatDetectionSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ContainerThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/containerThreatDetectionSettings:calculate',
        http_method='GET',
        method_id='securitycenter.projects.locations.clusters.containerThreatDetectionSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterProjectsLocationsClustersContainerThreatDetectionSettingsCalculateRequest',
        response_type_name='ContainerThreatDetectionSettings',
        supports_download=False,
    )

  class ProjectsLocationsClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_clusters resource."""

    _NAME = 'projects_locations_clusters'

    def __init__(self, client):
      super(SecuritycenterV1beta2.ProjectsLocationsClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def GetContainerThreatDetectionSettings(self, request, global_params=None):
      r"""Get the ContainerThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetContainerThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateContainerThreatDetectionSettings for this purpose.

      Args:
        request: (SecuritycenterProjectsLocationsClustersGetContainerThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ContainerThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('GetContainerThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetContainerThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/containerThreatDetectionSettings',
        http_method='GET',
        method_id='securitycenter.projects.locations.clusters.getContainerThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsLocationsClustersGetContainerThreatDetectionSettingsRequest',
        response_type_name='ContainerThreatDetectionSettings',
        supports_download=False,
    )

    def UpdateContainerThreatDetectionSettings(self, request, global_params=None):
      r"""Update the ContainerThreatDetectionSettings resource.

      Args:
        request: (SecuritycenterProjectsLocationsClustersUpdateContainerThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ContainerThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateContainerThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateContainerThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/containerThreatDetectionSettings',
        http_method='PATCH',
        method_id='securitycenter.projects.locations.clusters.updateContainerThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='containerThreatDetectionSettings',
        request_type_name='SecuritycenterProjectsLocationsClustersUpdateContainerThreatDetectionSettingsRequest',
        response_type_name='ContainerThreatDetectionSettings',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(SecuritycenterV1beta2.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsRapidVulnerabilityDetectionSettingsService(base_api.BaseApiService):
    """Service class for the projects_rapidVulnerabilityDetectionSettings resource."""

    _NAME = 'projects_rapidVulnerabilityDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.ProjectsRapidVulnerabilityDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective RapidVulnerabilityDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterProjectsRapidVulnerabilityDetectionSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RapidVulnerabilityDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/rapidVulnerabilityDetectionSettings:calculate',
        http_method='GET',
        method_id='securitycenter.projects.rapidVulnerabilityDetectionSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterProjectsRapidVulnerabilityDetectionSettingsCalculateRequest',
        response_type_name='RapidVulnerabilityDetectionSettings',
        supports_download=False,
    )

  class ProjectsSecurityHealthAnalyticsSettingsService(base_api.BaseApiService):
    """Service class for the projects_securityHealthAnalyticsSettings resource."""

    _NAME = 'projects_securityHealthAnalyticsSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.ProjectsSecurityHealthAnalyticsSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective SecurityHealthAnalyticsSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterProjectsSecurityHealthAnalyticsSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/securityHealthAnalyticsSettings:calculate',
        http_method='GET',
        method_id='securitycenter.projects.securityHealthAnalyticsSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterProjectsSecurityHealthAnalyticsSettingsCalculateRequest',
        response_type_name='SecurityHealthAnalyticsSettings',
        supports_download=False,
    )

  class ProjectsVirtualMachineThreatDetectionSettingsService(base_api.BaseApiService):
    """Service class for the projects_virtualMachineThreatDetectionSettings resource."""

    _NAME = 'projects_virtualMachineThreatDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.ProjectsVirtualMachineThreatDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective VirtualMachineThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterProjectsVirtualMachineThreatDetectionSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VirtualMachineThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/virtualMachineThreatDetectionSettings:calculate',
        http_method='GET',
        method_id='securitycenter.projects.virtualMachineThreatDetectionSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterProjectsVirtualMachineThreatDetectionSettingsCalculateRequest',
        response_type_name='VirtualMachineThreatDetectionSettings',
        supports_download=False,
    )

  class ProjectsWebSecurityScannerSettingsService(base_api.BaseApiService):
    """Service class for the projects_webSecurityScannerSettings resource."""

    _NAME = 'projects_webSecurityScannerSettings'

    def __init__(self, client):
      super(SecuritycenterV1beta2.ProjectsWebSecurityScannerSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Calculate(self, request, global_params=None):
      r"""Calculates the effective WebSecurityScannerSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.

      Args:
        request: (SecuritycenterProjectsWebSecurityScannerSettingsCalculateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WebSecurityScannerSettings) The response message.
      """
      config = self.GetMethodConfig('Calculate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Calculate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/webSecurityScannerSettings:calculate',
        http_method='GET',
        method_id='securitycenter.projects.webSecurityScannerSettings.calculate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1beta2/{+name}:calculate',
        request_field='',
        request_type_name='SecuritycenterProjectsWebSecurityScannerSettingsCalculateRequest',
        response_type_name='WebSecurityScannerSettings',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(SecuritycenterV1beta2.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetContainerThreatDetectionSettings(self, request, global_params=None):
      r"""Get the ContainerThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetContainerThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateContainerThreatDetectionSettings for this purpose.

      Args:
        request: (SecuritycenterProjectsGetContainerThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ContainerThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('GetContainerThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetContainerThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/containerThreatDetectionSettings',
        http_method='GET',
        method_id='securitycenter.projects.getContainerThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsGetContainerThreatDetectionSettingsRequest',
        response_type_name='ContainerThreatDetectionSettings',
        supports_download=False,
    )

    def GetEventThreatDetectionSettings(self, request, global_params=None):
      r"""Get the EventThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetEventThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateEventThreatDetectionSettings for this purpose.

      Args:
        request: (SecuritycenterProjectsGetEventThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('GetEventThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetEventThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/eventThreatDetectionSettings',
        http_method='GET',
        method_id='securitycenter.projects.getEventThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsGetEventThreatDetectionSettingsRequest',
        response_type_name='EventThreatDetectionSettings',
        supports_download=False,
    )

    def GetRapidVulnerabilityDetectionSettings(self, request, global_params=None):
      r"""Get the RapidVulnerabilityDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetRapidVulnerabilityDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateRapidVulnerabilityDetectionSettings for this purpose.

      Args:
        request: (SecuritycenterProjectsGetRapidVulnerabilityDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RapidVulnerabilityDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('GetRapidVulnerabilityDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetRapidVulnerabilityDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/rapidVulnerabilityDetectionSettings',
        http_method='GET',
        method_id='securitycenter.projects.getRapidVulnerabilityDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsGetRapidVulnerabilityDetectionSettingsRequest',
        response_type_name='RapidVulnerabilityDetectionSettings',
        supports_download=False,
    )

    def GetSecurityCenterSettings(self, request, global_params=None):
      r"""Get the SecurityCenterSettings resource.

      Args:
        request: (SecuritycenterProjectsGetSecurityCenterSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityCenterSettings) The response message.
      """
      config = self.GetMethodConfig('GetSecurityCenterSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSecurityCenterSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/securityCenterSettings',
        http_method='GET',
        method_id='securitycenter.projects.getSecurityCenterSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsGetSecurityCenterSettingsRequest',
        response_type_name='SecurityCenterSettings',
        supports_download=False,
    )

    def GetSecurityHealthAnalyticsSettings(self, request, global_params=None):
      r"""Get the SecurityHealthAnalyticsSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetSecurityHealthAnalyticsSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateSecurityHealthAnalyticsSettings for this purpose.

      Args:
        request: (SecuritycenterProjectsGetSecurityHealthAnalyticsSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsSettings) The response message.
      """
      config = self.GetMethodConfig('GetSecurityHealthAnalyticsSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSecurityHealthAnalyticsSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/securityHealthAnalyticsSettings',
        http_method='GET',
        method_id='securitycenter.projects.getSecurityHealthAnalyticsSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsGetSecurityHealthAnalyticsSettingsRequest',
        response_type_name='SecurityHealthAnalyticsSettings',
        supports_download=False,
    )

    def GetVirtualMachineThreatDetectionSettings(self, request, global_params=None):
      r"""Get the VirtualMachineThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetVirtualMachineThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateVirtualMachineThreatDetectionSettings for this purpose.

      Args:
        request: (SecuritycenterProjectsGetVirtualMachineThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VirtualMachineThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('GetVirtualMachineThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetVirtualMachineThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/virtualMachineThreatDetectionSettings',
        http_method='GET',
        method_id='securitycenter.projects.getVirtualMachineThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsGetVirtualMachineThreatDetectionSettingsRequest',
        response_type_name='VirtualMachineThreatDetectionSettings',
        supports_download=False,
    )

    def GetWebSecurityScannerSettings(self, request, global_params=None):
      r"""Get the WebSecurityScannerSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetWebSecurityScannerSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateWebSecurityScannerSettings for this purpose.

      Args:
        request: (SecuritycenterProjectsGetWebSecurityScannerSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WebSecurityScannerSettings) The response message.
      """
      config = self.GetMethodConfig('GetWebSecurityScannerSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetWebSecurityScannerSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/webSecurityScannerSettings',
        http_method='GET',
        method_id='securitycenter.projects.getWebSecurityScannerSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsGetWebSecurityScannerSettingsRequest',
        response_type_name='WebSecurityScannerSettings',
        supports_download=False,
    )

    def UpdateContainerThreatDetectionSettings(self, request, global_params=None):
      r"""Update the ContainerThreatDetectionSettings resource.

      Args:
        request: (SecuritycenterProjectsUpdateContainerThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ContainerThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateContainerThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateContainerThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/containerThreatDetectionSettings',
        http_method='PATCH',
        method_id='securitycenter.projects.updateContainerThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='containerThreatDetectionSettings',
        request_type_name='SecuritycenterProjectsUpdateContainerThreatDetectionSettingsRequest',
        response_type_name='ContainerThreatDetectionSettings',
        supports_download=False,
    )

    def UpdateEventThreatDetectionSettings(self, request, global_params=None):
      r"""Update the EventThreatDetectionSettings resource.

      Args:
        request: (SecuritycenterProjectsUpdateEventThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateEventThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateEventThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/eventThreatDetectionSettings',
        http_method='PATCH',
        method_id='securitycenter.projects.updateEventThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='eventThreatDetectionSettings',
        request_type_name='SecuritycenterProjectsUpdateEventThreatDetectionSettingsRequest',
        response_type_name='EventThreatDetectionSettings',
        supports_download=False,
    )

    def UpdateRapidVulnerabilityDetectionSettings(self, request, global_params=None):
      r"""Update the RapidVulnerabilityDetectionSettings resource.

      Args:
        request: (SecuritycenterProjectsUpdateRapidVulnerabilityDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RapidVulnerabilityDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateRapidVulnerabilityDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateRapidVulnerabilityDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/rapidVulnerabilityDetectionSettings',
        http_method='PATCH',
        method_id='securitycenter.projects.updateRapidVulnerabilityDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='rapidVulnerabilityDetectionSettings',
        request_type_name='SecuritycenterProjectsUpdateRapidVulnerabilityDetectionSettingsRequest',
        response_type_name='RapidVulnerabilityDetectionSettings',
        supports_download=False,
    )

    def UpdateSecurityHealthAnalyticsSettings(self, request, global_params=None):
      r"""Update the SecurityHealthAnalyticsSettings resource.

      Args:
        request: (SecuritycenterProjectsUpdateSecurityHealthAnalyticsSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityHealthAnalyticsSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityHealthAnalyticsSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/securityHealthAnalyticsSettings',
        http_method='PATCH',
        method_id='securitycenter.projects.updateSecurityHealthAnalyticsSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='securityHealthAnalyticsSettings',
        request_type_name='SecuritycenterProjectsUpdateSecurityHealthAnalyticsSettingsRequest',
        response_type_name='SecurityHealthAnalyticsSettings',
        supports_download=False,
    )

    def UpdateVirtualMachineThreatDetectionSettings(self, request, global_params=None):
      r"""Update the VirtualMachineThreatDetectionSettings resource.

      Args:
        request: (SecuritycenterProjectsUpdateVirtualMachineThreatDetectionSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VirtualMachineThreatDetectionSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateVirtualMachineThreatDetectionSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateVirtualMachineThreatDetectionSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/virtualMachineThreatDetectionSettings',
        http_method='PATCH',
        method_id='securitycenter.projects.updateVirtualMachineThreatDetectionSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='virtualMachineThreatDetectionSettings',
        request_type_name='SecuritycenterProjectsUpdateVirtualMachineThreatDetectionSettingsRequest',
        response_type_name='VirtualMachineThreatDetectionSettings',
        supports_download=False,
    )

    def UpdateWebSecurityScannerSettings(self, request, global_params=None):
      r"""Update the WebSecurityScannerSettings resource.

      Args:
        request: (SecuritycenterProjectsUpdateWebSecurityScannerSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WebSecurityScannerSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateWebSecurityScannerSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateWebSecurityScannerSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta2/projects/{projectsId}/webSecurityScannerSettings',
        http_method='PATCH',
        method_id='securitycenter.projects.updateWebSecurityScannerSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta2/{+name}',
        request_field='webSecurityScannerSettings',
        request_type_name='SecuritycenterProjectsUpdateWebSecurityScannerSettingsRequest',
        response_type_name='WebSecurityScannerSettings',
        supports_download=False,
    )
