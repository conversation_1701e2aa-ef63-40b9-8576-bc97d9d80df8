"""Generated client library for securitycenter version v2."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.securitycenter.v2 import securitycenter_v2_messages as messages


class SecuritycenterV2(base_api.BaseApiClient):
  """Generated client library for service securitycenter version v2."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://securitycenter.googleapis.com/'
  MTLS_BASE_URL = 'https://securitycenter.mtls.googleapis.com/'

  _PACKAGE = 'securitycenter'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v2'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'SecuritycenterV2'
  _URL_VERSION = 'v2'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new securitycenter handle."""
    url = url or self.BASE_URL
    super(SecuritycenterV2, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.folders_assets = self.FoldersAssetsService(self)
    self.folders_findings = self.FoldersFindingsService(self)
    self.folders_locations_bigQueryExports = self.FoldersLocationsBigQueryExportsService(self)
    self.folders_locations_findings = self.FoldersLocationsFindingsService(self)
    self.folders_locations_muteConfigs = self.FoldersLocationsMuteConfigsService(self)
    self.folders_locations_notificationConfigs = self.FoldersLocationsNotificationConfigsService(self)
    self.folders_locations = self.FoldersLocationsService(self)
    self.folders_muteConfigs = self.FoldersMuteConfigsService(self)
    self.folders_sources_findings_externalSystems = self.FoldersSourcesFindingsExternalSystemsService(self)
    self.folders_sources_findings = self.FoldersSourcesFindingsService(self)
    self.folders_sources_locations_findings_externalSystems = self.FoldersSourcesLocationsFindingsExternalSystemsService(self)
    self.folders_sources_locations_findings = self.FoldersSourcesLocationsFindingsService(self)
    self.folders_sources_locations = self.FoldersSourcesLocationsService(self)
    self.folders_sources = self.FoldersSourcesService(self)
    self.folders = self.FoldersService(self)
    self.organizations_assets = self.OrganizationsAssetsService(self)
    self.organizations_attackPaths = self.OrganizationsAttackPathsService(self)
    self.organizations_findings = self.OrganizationsFindingsService(self)
    self.organizations_locations_bigQueryExports = self.OrganizationsLocationsBigQueryExportsService(self)
    self.organizations_locations_findings = self.OrganizationsLocationsFindingsService(self)
    self.organizations_locations_muteConfigs = self.OrganizationsLocationsMuteConfigsService(self)
    self.organizations_locations_notificationConfigs = self.OrganizationsLocationsNotificationConfigsService(self)
    self.organizations_locations_resourceValueConfigs = self.OrganizationsLocationsResourceValueConfigsService(self)
    self.organizations_locations_simulations_attackExposureResults_attackPaths = self.OrganizationsLocationsSimulationsAttackExposureResultsAttackPathsService(self)
    self.organizations_locations_simulations_attackExposureResults = self.OrganizationsLocationsSimulationsAttackExposureResultsService(self)
    self.organizations_locations_simulations_valuedResources_attackPaths = self.OrganizationsLocationsSimulationsValuedResourcesAttackPathsService(self)
    self.organizations_locations_simulations_valuedResources = self.OrganizationsLocationsSimulationsValuedResourcesService(self)
    self.organizations_locations_simulations = self.OrganizationsLocationsSimulationsService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations_muteConfigs = self.OrganizationsMuteConfigsService(self)
    self.organizations_operations = self.OrganizationsOperationsService(self)
    self.organizations_resourceValueConfigs = self.OrganizationsResourceValueConfigsService(self)
    self.organizations_simulations_attackExposureResults_attackPaths = self.OrganizationsSimulationsAttackExposureResultsAttackPathsService(self)
    self.organizations_simulations_attackExposureResults_valuedResources = self.OrganizationsSimulationsAttackExposureResultsValuedResourcesService(self)
    self.organizations_simulations_attackExposureResults = self.OrganizationsSimulationsAttackExposureResultsService(self)
    self.organizations_simulations_attackPaths = self.OrganizationsSimulationsAttackPathsService(self)
    self.organizations_simulations_valuedResources_attackPaths = self.OrganizationsSimulationsValuedResourcesAttackPathsService(self)
    self.organizations_simulations_valuedResources = self.OrganizationsSimulationsValuedResourcesService(self)
    self.organizations_simulations = self.OrganizationsSimulationsService(self)
    self.organizations_sources_findings_externalSystems = self.OrganizationsSourcesFindingsExternalSystemsService(self)
    self.organizations_sources_findings = self.OrganizationsSourcesFindingsService(self)
    self.organizations_sources_locations_findings_externalSystems = self.OrganizationsSourcesLocationsFindingsExternalSystemsService(self)
    self.organizations_sources_locations_findings = self.OrganizationsSourcesLocationsFindingsService(self)
    self.organizations_sources_locations = self.OrganizationsSourcesLocationsService(self)
    self.organizations_sources = self.OrganizationsSourcesService(self)
    self.organizations_valuedResources = self.OrganizationsValuedResourcesService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_assets = self.ProjectsAssetsService(self)
    self.projects_findings = self.ProjectsFindingsService(self)
    self.projects_locations_bigQueryExports = self.ProjectsLocationsBigQueryExportsService(self)
    self.projects_locations_findings = self.ProjectsLocationsFindingsService(self)
    self.projects_locations_muteConfigs = self.ProjectsLocationsMuteConfigsService(self)
    self.projects_locations_notificationConfigs = self.ProjectsLocationsNotificationConfigsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects_muteConfigs = self.ProjectsMuteConfigsService(self)
    self.projects_sources_findings_externalSystems = self.ProjectsSourcesFindingsExternalSystemsService(self)
    self.projects_sources_findings = self.ProjectsSourcesFindingsService(self)
    self.projects_sources_locations_findings_externalSystems = self.ProjectsSourcesLocationsFindingsExternalSystemsService(self)
    self.projects_sources_locations_findings = self.ProjectsSourcesLocationsFindingsService(self)
    self.projects_sources_locations = self.ProjectsSourcesLocationsService(self)
    self.projects_sources = self.ProjectsSourcesService(self)
    self.projects = self.ProjectsService(self)

  class FoldersAssetsService(base_api.BaseApiService):
    """Service class for the folders_assets resource."""

    _NAME = 'folders_assets'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersAssetsService, self).__init__(client)
      self._upload_configs = {
          }

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks. For Finding Security marks, if no location is specified, finding is assumed to be in global. Assets Security Marks can only be accessed through global endpoint.

      Args:
        request: (SecuritycenterFoldersAssetsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/assets/{assetsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.folders.assets.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2SecurityMarks',
        request_type_name='SecuritycenterFoldersAssetsUpdateSecurityMarksRequest',
        response_type_name='GoogleCloudSecuritycenterV2SecurityMarks',
        supports_download=False,
    )

  class FoldersFindingsService(base_api.BaseApiService):
    """Service class for the folders_findings resource."""

    _NAME = 'folders_findings'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def BulkMute(self, request, global_params=None):
      r"""Kicks off an LRO to bulk mute findings for a parent based on a filter. If no location is specified, findings are muted in global. The parent can be either an organization, folder, or project. The findings matched by the filter will be muted after the LRO is done.

      Args:
        request: (SecuritycenterFoldersFindingsBulkMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BulkMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    BulkMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/findings:bulkMute',
        http_method='POST',
        method_id='securitycenter.folders.findings.bulkMute',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:bulkMute',
        request_field='bulkMuteFindingsRequest',
        request_type_name='SecuritycenterFoldersFindingsBulkMuteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class FoldersLocationsBigQueryExportsService(base_api.BaseApiService):
    """Service class for the folders_locations_bigQueryExports resource."""

    _NAME = 'folders_locations_bigQueryExports'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersLocationsBigQueryExportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a BigQuery export.

      Args:
        request: (SecuritycenterFoldersLocationsBigQueryExportsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/bigQueryExports',
        http_method='POST',
        method_id='securitycenter.folders.locations.bigQueryExports.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bigQueryExportId'],
        relative_path='v2/{+parent}/bigQueryExports',
        request_field='googleCloudSecuritycenterV2BigQueryExport',
        request_type_name='SecuritycenterFoldersLocationsBigQueryExportsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV2BigQueryExport',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing BigQuery export.

      Args:
        request: (SecuritycenterFoldersLocationsBigQueryExportsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='DELETE',
        method_id='securitycenter.folders.locations.bigQueryExports.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersLocationsBigQueryExportsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a BigQuery export.

      Args:
        request: (SecuritycenterFoldersLocationsBigQueryExportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='GET',
        method_id='securitycenter.folders.locations.bigQueryExports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersLocationsBigQueryExportsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV2BigQueryExport',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BigQuery exports. Note that when requesting BigQuery exports at a given level all exports under that level are also returned e.g. if requesting BigQuery exports under a folder, then all BigQuery exports immediately under the folder plus the ones created under the projects within the folder are returned.

      Args:
        request: (SecuritycenterFoldersLocationsBigQueryExportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBigQueryExportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/bigQueryExports',
        http_method='GET',
        method_id='securitycenter.folders.locations.bigQueryExports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/bigQueryExports',
        request_field='',
        request_type_name='SecuritycenterFoldersLocationsBigQueryExportsListRequest',
        response_type_name='ListBigQueryExportsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a BigQuery export.

      Args:
        request: (SecuritycenterFoldersLocationsBigQueryExportsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='PATCH',
        method_id='securitycenter.folders.locations.bigQueryExports.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2BigQueryExport',
        request_type_name='SecuritycenterFoldersLocationsBigQueryExportsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2BigQueryExport',
        supports_download=False,
    )

  class FoldersLocationsFindingsService(base_api.BaseApiService):
    """Service class for the folders_locations_findings resource."""

    _NAME = 'folders_locations_findings'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersLocationsFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def BulkMute(self, request, global_params=None):
      r"""Kicks off an LRO to bulk mute findings for a parent based on a filter. If no location is specified, findings are muted in global. The parent can be either an organization, folder, or project. The findings matched by the filter will be muted after the LRO is done.

      Args:
        request: (SecuritycenterFoldersLocationsFindingsBulkMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BulkMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    BulkMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/findings:bulkMute',
        http_method='POST',
        method_id='securitycenter.folders.locations.findings.bulkMute',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:bulkMute',
        request_field='bulkMuteFindingsRequest',
        request_type_name='SecuritycenterFoldersLocationsFindingsBulkMuteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class FoldersLocationsMuteConfigsService(base_api.BaseApiService):
    """Service class for the folders_locations_muteConfigs resource."""

    _NAME = 'folders_locations_muteConfigs'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersLocationsMuteConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a mute config.

      Args:
        request: (SecuritycenterFoldersLocationsMuteConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/muteConfigs',
        http_method='POST',
        method_id='securitycenter.folders.locations.muteConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['muteConfigId'],
        relative_path='v2/{+parent}/muteConfigs',
        request_field='googleCloudSecuritycenterV2MuteConfig',
        request_type_name='SecuritycenterFoldersLocationsMuteConfigsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterFoldersLocationsMuteConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/muteConfigs/{muteConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.folders.locations.muteConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersLocationsMuteConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterFoldersLocationsMuteConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/muteConfigs/{muteConfigsId}',
        http_method='GET',
        method_id='securitycenter.folders.locations.muteConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersLocationsMuteConfigsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists mute configs. If no location is specified, default is global.

      Args:
        request: (SecuritycenterFoldersLocationsMuteConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMuteConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/muteConfigs',
        http_method='GET',
        method_id='securitycenter.folders.locations.muteConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/muteConfigs',
        request_field='',
        request_type_name='SecuritycenterFoldersLocationsMuteConfigsListRequest',
        response_type_name='ListMuteConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterFoldersLocationsMuteConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/muteConfigs/{muteConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.folders.locations.muteConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2MuteConfig',
        request_type_name='SecuritycenterFoldersLocationsMuteConfigsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

  class FoldersLocationsNotificationConfigsService(base_api.BaseApiService):
    """Service class for the folders_locations_notificationConfigs resource."""

    _NAME = 'folders_locations_notificationConfigs'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersLocationsNotificationConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a notification config.

      Args:
        request: (SecuritycenterFoldersLocationsNotificationConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/notificationConfigs',
        http_method='POST',
        method_id='securitycenter.folders.locations.notificationConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['configId'],
        relative_path='v2/{+parent}/notificationConfigs',
        request_field='notificationConfig',
        request_type_name='SecuritycenterFoldersLocationsNotificationConfigsCreateRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a notification config.

      Args:
        request: (SecuritycenterFoldersLocationsNotificationConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/notificationConfigs/{notificationConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.folders.locations.notificationConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersLocationsNotificationConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a notification config.

      Args:
        request: (SecuritycenterFoldersLocationsNotificationConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/notificationConfigs/{notificationConfigsId}',
        http_method='GET',
        method_id='securitycenter.folders.locations.notificationConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersLocationsNotificationConfigsGetRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists notification configs.

      Args:
        request: (SecuritycenterFoldersLocationsNotificationConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNotificationConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/notificationConfigs',
        http_method='GET',
        method_id='securitycenter.folders.locations.notificationConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/notificationConfigs',
        request_field='',
        request_type_name='SecuritycenterFoldersLocationsNotificationConfigsListRequest',
        response_type_name='ListNotificationConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a notification config. The following update fields are allowed: description, pubsub_topic, streaming_config.filter.

      Args:
        request: (SecuritycenterFoldersLocationsNotificationConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/locations/{locationsId}/notificationConfigs/{notificationConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.folders.locations.notificationConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='notificationConfig',
        request_type_name='SecuritycenterFoldersLocationsNotificationConfigsPatchRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

  class FoldersLocationsService(base_api.BaseApiService):
    """Service class for the folders_locations resource."""

    _NAME = 'folders_locations'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersMuteConfigsService(base_api.BaseApiService):
    """Service class for the folders_muteConfigs resource."""

    _NAME = 'folders_muteConfigs'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersMuteConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a mute config.

      Args:
        request: (SecuritycenterFoldersMuteConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/muteConfigs',
        http_method='POST',
        method_id='securitycenter.folders.muteConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['muteConfigId'],
        relative_path='v2/{+parent}/muteConfigs',
        request_field='googleCloudSecuritycenterV2MuteConfig',
        request_type_name='SecuritycenterFoldersMuteConfigsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterFoldersMuteConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/muteConfigs/{muteConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.folders.muteConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersMuteConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterFoldersMuteConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/muteConfigs/{muteConfigsId}',
        http_method='GET',
        method_id='securitycenter.folders.muteConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersMuteConfigsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists mute configs. If no location is specified, default is global.

      Args:
        request: (SecuritycenterFoldersMuteConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMuteConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/muteConfigs',
        http_method='GET',
        method_id='securitycenter.folders.muteConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/muteConfigs',
        request_field='',
        request_type_name='SecuritycenterFoldersMuteConfigsListRequest',
        response_type_name='ListMuteConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterFoldersMuteConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/muteConfigs/{muteConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.folders.muteConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2MuteConfig',
        request_type_name='SecuritycenterFoldersMuteConfigsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

  class FoldersSourcesFindingsExternalSystemsService(base_api.BaseApiService):
    """Service class for the folders_sources_findings_externalSystems resource."""

    _NAME = 'folders_sources_findings_externalSystems'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersSourcesFindingsExternalSystemsService, self).__init__(client)
      self._upload_configs = {
          }

    def Patch(self, request, global_params=None):
      r"""Updates external system. This is for a given finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterFoldersSourcesFindingsExternalSystemsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2ExternalSystem) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/findings/{findingsId}/externalSystems/{externalSystemsId}',
        http_method='PATCH',
        method_id='securitycenter.folders.sources.findings.externalSystems.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2ExternalSystem',
        request_type_name='SecuritycenterFoldersSourcesFindingsExternalSystemsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2ExternalSystem',
        supports_download=False,
    )

  class FoldersSourcesFindingsService(base_api.BaseApiService):
    """Service class for the folders_sources_findings resource."""

    _NAME = 'folders_sources_findings'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersSourcesFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Group(self, request, global_params=None):
      r"""Filters an organization or source's findings and groups them by their specified properties in a location. If no location is specified, findings are assumed to be in global To group across all sources provide a `-` as the source id. The following list shows some examples: + `/v2/organizations/{organization_id}/sources/-/findings` + `/v2/organizations/{organization_id}/sources/-/locations/{location_id}/findings` + `/v2/folders/{folder_id}/sources/-/findings` + `/v2/folders/{folder_id}/sources/-/locations/{location_id}/findings` + `/v2/projects/{project_id}/sources/-/findings` + `/v2/projects/{project_id}/sources/-/locations/{location_id}/findings`.

      Args:
        request: (SecuritycenterFoldersSourcesFindingsGroupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GroupFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('Group')
      return self._RunMethod(
          config, request, global_params=global_params)

    Group.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/findings:group',
        http_method='POST',
        method_id='securitycenter.folders.sources.findings.group',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:group',
        request_field='groupFindingsRequest',
        request_type_name='SecuritycenterFoldersSourcesFindingsGroupRequest',
        response_type_name='GroupFindingsResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists an organization or source's findings. To list across all sources for a given location provide a `-` as the source id. If no location is specified, finding are assumed to be in global. The following list shows some examples: + `/v2/organizations/{organization_id}/sources/-/findings` + `/v2/organizations/{organization_id}/sources/-/locations/{location_id}/findings`.

      Args:
        request: (SecuritycenterFoldersSourcesFindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/findings',
        http_method='GET',
        method_id='securitycenter.folders.sources.findings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['fieldMask', 'filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/findings',
        request_field='',
        request_type_name='SecuritycenterFoldersSourcesFindingsListRequest',
        response_type_name='ListFindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Creates or updates a finding. If no location is specified, finding is assumed to be in global. The corresponding source must exist for a finding creation to succeed.

      Args:
        request: (SecuritycenterFoldersSourcesFindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/findings/{findingsId}',
        http_method='PATCH',
        method_id='securitycenter.folders.sources.findings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2Finding',
        request_type_name='SecuritycenterFoldersSourcesFindingsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def SetMute(self, request, global_params=None):
      r"""Updates the mute state of a finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterFoldersSourcesFindingsSetMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('SetMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/findings/{findingsId}:setMute',
        http_method='POST',
        method_id='securitycenter.folders.sources.findings.setMute',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:setMute',
        request_field='setMuteRequest',
        request_type_name='SecuritycenterFoldersSourcesFindingsSetMuteRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def SetState(self, request, global_params=None):
      r"""Updates the state of a finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterFoldersSourcesFindingsSetStateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('SetState')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetState.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/findings/{findingsId}:setState',
        http_method='POST',
        method_id='securitycenter.folders.sources.findings.setState',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:setState',
        request_field='setFindingStateRequest',
        request_type_name='SecuritycenterFoldersSourcesFindingsSetStateRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks. For Finding Security marks, if no location is specified, finding is assumed to be in global. Assets Security Marks can only be accessed through global endpoint.

      Args:
        request: (SecuritycenterFoldersSourcesFindingsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/findings/{findingsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.folders.sources.findings.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2SecurityMarks',
        request_type_name='SecuritycenterFoldersSourcesFindingsUpdateSecurityMarksRequest',
        response_type_name='GoogleCloudSecuritycenterV2SecurityMarks',
        supports_download=False,
    )

  class FoldersSourcesLocationsFindingsExternalSystemsService(base_api.BaseApiService):
    """Service class for the folders_sources_locations_findings_externalSystems resource."""

    _NAME = 'folders_sources_locations_findings_externalSystems'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersSourcesLocationsFindingsExternalSystemsService, self).__init__(client)
      self._upload_configs = {
          }

    def Patch(self, request, global_params=None):
      r"""Updates external system. This is for a given finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterFoldersSourcesLocationsFindingsExternalSystemsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2ExternalSystem) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}/externalSystems/{externalSystemsId}',
        http_method='PATCH',
        method_id='securitycenter.folders.sources.locations.findings.externalSystems.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2ExternalSystem',
        request_type_name='SecuritycenterFoldersSourcesLocationsFindingsExternalSystemsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2ExternalSystem',
        supports_download=False,
    )

  class FoldersSourcesLocationsFindingsService(base_api.BaseApiService):
    """Service class for the folders_sources_locations_findings resource."""

    _NAME = 'folders_sources_locations_findings'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersSourcesLocationsFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Export(self, request, global_params=None):
      r"""Kicks off an LRO to export findings for an organization to the customer's BigQuery dataset.

      Args:
        request: (SecuritycenterFoldersSourcesLocationsFindingsExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/locations/{locationsId}/findings:export',
        http_method='POST',
        method_id='securitycenter.folders.sources.locations.findings.export',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:export',
        request_field='exportFindingsRequest',
        request_type_name='SecuritycenterFoldersSourcesLocationsFindingsExportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Group(self, request, global_params=None):
      r"""Filters an organization or source's findings and groups them by their specified properties in a location. If no location is specified, findings are assumed to be in global To group across all sources provide a `-` as the source id. The following list shows some examples: + `/v2/organizations/{organization_id}/sources/-/findings` + `/v2/organizations/{organization_id}/sources/-/locations/{location_id}/findings` + `/v2/folders/{folder_id}/sources/-/findings` + `/v2/folders/{folder_id}/sources/-/locations/{location_id}/findings` + `/v2/projects/{project_id}/sources/-/findings` + `/v2/projects/{project_id}/sources/-/locations/{location_id}/findings`.

      Args:
        request: (SecuritycenterFoldersSourcesLocationsFindingsGroupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GroupFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('Group')
      return self._RunMethod(
          config, request, global_params=global_params)

    Group.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/locations/{locationsId}/findings:group',
        http_method='POST',
        method_id='securitycenter.folders.sources.locations.findings.group',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:group',
        request_field='groupFindingsRequest',
        request_type_name='SecuritycenterFoldersSourcesLocationsFindingsGroupRequest',
        response_type_name='GroupFindingsResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists an organization or source's findings. To list across all sources for a given location provide a `-` as the source id. If no location is specified, finding are assumed to be in global. The following list shows some examples: + `/v2/organizations/{organization_id}/sources/-/findings` + `/v2/organizations/{organization_id}/sources/-/locations/{location_id}/findings`.

      Args:
        request: (SecuritycenterFoldersSourcesLocationsFindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/locations/{locationsId}/findings',
        http_method='GET',
        method_id='securitycenter.folders.sources.locations.findings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['fieldMask', 'filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/findings',
        request_field='',
        request_type_name='SecuritycenterFoldersSourcesLocationsFindingsListRequest',
        response_type_name='ListFindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Creates or updates a finding. If no location is specified, finding is assumed to be in global. The corresponding source must exist for a finding creation to succeed.

      Args:
        request: (SecuritycenterFoldersSourcesLocationsFindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}',
        http_method='PATCH',
        method_id='securitycenter.folders.sources.locations.findings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2Finding',
        request_type_name='SecuritycenterFoldersSourcesLocationsFindingsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def SetMute(self, request, global_params=None):
      r"""Updates the mute state of a finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterFoldersSourcesLocationsFindingsSetMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('SetMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}:setMute',
        http_method='POST',
        method_id='securitycenter.folders.sources.locations.findings.setMute',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:setMute',
        request_field='setMuteRequest',
        request_type_name='SecuritycenterFoldersSourcesLocationsFindingsSetMuteRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def SetState(self, request, global_params=None):
      r"""Updates the state of a finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterFoldersSourcesLocationsFindingsSetStateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('SetState')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetState.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}:setState',
        http_method='POST',
        method_id='securitycenter.folders.sources.locations.findings.setState',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:setState',
        request_field='setFindingStateRequest',
        request_type_name='SecuritycenterFoldersSourcesLocationsFindingsSetStateRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks. For Finding Security marks, if no location is specified, finding is assumed to be in global. Assets Security Marks can only be accessed through global endpoint.

      Args:
        request: (SecuritycenterFoldersSourcesLocationsFindingsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.folders.sources.locations.findings.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2SecurityMarks',
        request_type_name='SecuritycenterFoldersSourcesLocationsFindingsUpdateSecurityMarksRequest',
        response_type_name='GoogleCloudSecuritycenterV2SecurityMarks',
        supports_download=False,
    )

  class FoldersSourcesLocationsService(base_api.BaseApiService):
    """Service class for the folders_sources_locations resource."""

    _NAME = 'folders_sources_locations'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersSourcesLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersSourcesService(base_api.BaseApiService):
    """Service class for the folders_sources resource."""

    _NAME = 'folders_sources'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersSourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all sources belonging to an organization.

      Args:
        request: (SecuritycenterFoldersSourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/folders/{foldersId}/sources',
        http_method='GET',
        method_id='securitycenter.folders.sources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/sources',
        request_field='',
        request_type_name='SecuritycenterFoldersSourcesListRequest',
        response_type_name='ListSourcesResponse',
        supports_download=False,
    )

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(SecuritycenterV2.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsAssetsService(base_api.BaseApiService):
    """Service class for the organizations_assets resource."""

    _NAME = 'organizations_assets'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsAssetsService, self).__init__(client)
      self._upload_configs = {
          }

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks. For Finding Security marks, if no location is specified, finding is assumed to be in global. Assets Security Marks can only be accessed through global endpoint.

      Args:
        request: (SecuritycenterOrganizationsAssetsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/assets/{assetsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.organizations.assets.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2SecurityMarks',
        request_type_name='SecuritycenterOrganizationsAssetsUpdateSecurityMarksRequest',
        response_type_name='GoogleCloudSecuritycenterV2SecurityMarks',
        supports_download=False,
    )

  class OrganizationsAttackPathsService(base_api.BaseApiService):
    """Service class for the organizations_attackPaths resource."""

    _NAME = 'organizations_attackPaths'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsAttackPathsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the attack paths for a set of simulation results or valued resources and filter.

      Args:
        request: (SecuritycenterOrganizationsAttackPathsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttackPathsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/attackPaths',
        http_method='GET',
        method_id='securitycenter.organizations.attackPaths.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/attackPaths',
        request_field='',
        request_type_name='SecuritycenterOrganizationsAttackPathsListRequest',
        response_type_name='ListAttackPathsResponse',
        supports_download=False,
    )

  class OrganizationsFindingsService(base_api.BaseApiService):
    """Service class for the organizations_findings resource."""

    _NAME = 'organizations_findings'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def BulkMute(self, request, global_params=None):
      r"""Kicks off an LRO to bulk mute findings for a parent based on a filter. If no location is specified, findings are muted in global. The parent can be either an organization, folder, or project. The findings matched by the filter will be muted after the LRO is done.

      Args:
        request: (SecuritycenterOrganizationsFindingsBulkMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BulkMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    BulkMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/findings:bulkMute',
        http_method='POST',
        method_id='securitycenter.organizations.findings.bulkMute',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:bulkMute',
        request_field='bulkMuteFindingsRequest',
        request_type_name='SecuritycenterOrganizationsFindingsBulkMuteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsLocationsBigQueryExportsService(base_api.BaseApiService):
    """Service class for the organizations_locations_bigQueryExports resource."""

    _NAME = 'organizations_locations_bigQueryExports'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsLocationsBigQueryExportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a BigQuery export.

      Args:
        request: (SecuritycenterOrganizationsLocationsBigQueryExportsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/bigQueryExports',
        http_method='POST',
        method_id='securitycenter.organizations.locations.bigQueryExports.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bigQueryExportId'],
        relative_path='v2/{+parent}/bigQueryExports',
        request_field='googleCloudSecuritycenterV2BigQueryExport',
        request_type_name='SecuritycenterOrganizationsLocationsBigQueryExportsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV2BigQueryExport',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing BigQuery export.

      Args:
        request: (SecuritycenterOrganizationsLocationsBigQueryExportsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='DELETE',
        method_id='securitycenter.organizations.locations.bigQueryExports.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsBigQueryExportsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a BigQuery export.

      Args:
        request: (SecuritycenterOrganizationsLocationsBigQueryExportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='GET',
        method_id='securitycenter.organizations.locations.bigQueryExports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsBigQueryExportsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV2BigQueryExport',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BigQuery exports. Note that when requesting BigQuery exports at a given level all exports under that level are also returned e.g. if requesting BigQuery exports under a folder, then all BigQuery exports immediately under the folder plus the ones created under the projects within the folder are returned.

      Args:
        request: (SecuritycenterOrganizationsLocationsBigQueryExportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBigQueryExportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/bigQueryExports',
        http_method='GET',
        method_id='securitycenter.organizations.locations.bigQueryExports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/bigQueryExports',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsBigQueryExportsListRequest',
        response_type_name='ListBigQueryExportsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a BigQuery export.

      Args:
        request: (SecuritycenterOrganizationsLocationsBigQueryExportsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.locations.bigQueryExports.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2BigQueryExport',
        request_type_name='SecuritycenterOrganizationsLocationsBigQueryExportsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2BigQueryExport',
        supports_download=False,
    )

  class OrganizationsLocationsFindingsService(base_api.BaseApiService):
    """Service class for the organizations_locations_findings resource."""

    _NAME = 'organizations_locations_findings'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsLocationsFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def BulkMute(self, request, global_params=None):
      r"""Kicks off an LRO to bulk mute findings for a parent based on a filter. If no location is specified, findings are muted in global. The parent can be either an organization, folder, or project. The findings matched by the filter will be muted after the LRO is done.

      Args:
        request: (SecuritycenterOrganizationsLocationsFindingsBulkMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BulkMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    BulkMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/findings:bulkMute',
        http_method='POST',
        method_id='securitycenter.organizations.locations.findings.bulkMute',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:bulkMute',
        request_field='bulkMuteFindingsRequest',
        request_type_name='SecuritycenterOrganizationsLocationsFindingsBulkMuteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsLocationsMuteConfigsService(base_api.BaseApiService):
    """Service class for the organizations_locations_muteConfigs resource."""

    _NAME = 'organizations_locations_muteConfigs'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsLocationsMuteConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a mute config.

      Args:
        request: (SecuritycenterOrganizationsLocationsMuteConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/muteConfigs',
        http_method='POST',
        method_id='securitycenter.organizations.locations.muteConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['muteConfigId'],
        relative_path='v2/{+parent}/muteConfigs',
        request_field='googleCloudSecuritycenterV2MuteConfig',
        request_type_name='SecuritycenterOrganizationsLocationsMuteConfigsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterOrganizationsLocationsMuteConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/muteConfigs/{muteConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.organizations.locations.muteConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsMuteConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterOrganizationsLocationsMuteConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/muteConfigs/{muteConfigsId}',
        http_method='GET',
        method_id='securitycenter.organizations.locations.muteConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsMuteConfigsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists mute configs. If no location is specified, default is global.

      Args:
        request: (SecuritycenterOrganizationsLocationsMuteConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMuteConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/muteConfigs',
        http_method='GET',
        method_id='securitycenter.organizations.locations.muteConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/muteConfigs',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsMuteConfigsListRequest',
        response_type_name='ListMuteConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterOrganizationsLocationsMuteConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/muteConfigs/{muteConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.locations.muteConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2MuteConfig',
        request_type_name='SecuritycenterOrganizationsLocationsMuteConfigsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

  class OrganizationsLocationsNotificationConfigsService(base_api.BaseApiService):
    """Service class for the organizations_locations_notificationConfigs resource."""

    _NAME = 'organizations_locations_notificationConfigs'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsLocationsNotificationConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a notification config.

      Args:
        request: (SecuritycenterOrganizationsLocationsNotificationConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/notificationConfigs',
        http_method='POST',
        method_id='securitycenter.organizations.locations.notificationConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['configId'],
        relative_path='v2/{+parent}/notificationConfigs',
        request_field='notificationConfig',
        request_type_name='SecuritycenterOrganizationsLocationsNotificationConfigsCreateRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a notification config.

      Args:
        request: (SecuritycenterOrganizationsLocationsNotificationConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/notificationConfigs/{notificationConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.organizations.locations.notificationConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsNotificationConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a notification config.

      Args:
        request: (SecuritycenterOrganizationsLocationsNotificationConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/notificationConfigs/{notificationConfigsId}',
        http_method='GET',
        method_id='securitycenter.organizations.locations.notificationConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsNotificationConfigsGetRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists notification configs.

      Args:
        request: (SecuritycenterOrganizationsLocationsNotificationConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNotificationConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/notificationConfigs',
        http_method='GET',
        method_id='securitycenter.organizations.locations.notificationConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/notificationConfigs',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsNotificationConfigsListRequest',
        response_type_name='ListNotificationConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a notification config. The following update fields are allowed: description, pubsub_topic, streaming_config.filter.

      Args:
        request: (SecuritycenterOrganizationsLocationsNotificationConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/notificationConfigs/{notificationConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.locations.notificationConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='notificationConfig',
        request_type_name='SecuritycenterOrganizationsLocationsNotificationConfigsPatchRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

  class OrganizationsLocationsResourceValueConfigsService(base_api.BaseApiService):
    """Service class for the organizations_locations_resourceValueConfigs resource."""

    _NAME = 'organizations_locations_resourceValueConfigs'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsLocationsResourceValueConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Creates a ResourceValueConfig for an organization. Maps user's tags to difference resource values for use by the attack path simulation.

      Args:
        request: (SecuritycenterOrganizationsLocationsResourceValueConfigsBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BatchCreateResourceValueConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/resourceValueConfigs:batchCreate',
        http_method='POST',
        method_id='securitycenter.organizations.locations.resourceValueConfigs.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/resourceValueConfigs:batchCreate',
        request_field='batchCreateResourceValueConfigsRequest',
        request_type_name='SecuritycenterOrganizationsLocationsResourceValueConfigsBatchCreateRequest',
        response_type_name='BatchCreateResourceValueConfigsResponse',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a ResourceValueConfig.

      Args:
        request: (SecuritycenterOrganizationsLocationsResourceValueConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/resourceValueConfigs/{resourceValueConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.organizations.locations.resourceValueConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsResourceValueConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a ResourceValueConfig.

      Args:
        request: (SecuritycenterOrganizationsLocationsResourceValueConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2ResourceValueConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/resourceValueConfigs/{resourceValueConfigsId}',
        http_method='GET',
        method_id='securitycenter.organizations.locations.resourceValueConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsResourceValueConfigsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV2ResourceValueConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all ResourceValueConfigs.

      Args:
        request: (SecuritycenterOrganizationsLocationsResourceValueConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListResourceValueConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/resourceValueConfigs',
        http_method='GET',
        method_id='securitycenter.organizations.locations.resourceValueConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/resourceValueConfigs',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsResourceValueConfigsListRequest',
        response_type_name='ListResourceValueConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing ResourceValueConfigs with new rules.

      Args:
        request: (SecuritycenterOrganizationsLocationsResourceValueConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2ResourceValueConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/resourceValueConfigs/{resourceValueConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.locations.resourceValueConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2ResourceValueConfig',
        request_type_name='SecuritycenterOrganizationsLocationsResourceValueConfigsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2ResourceValueConfig',
        supports_download=False,
    )

  class OrganizationsLocationsSimulationsAttackExposureResultsAttackPathsService(base_api.BaseApiService):
    """Service class for the organizations_locations_simulations_attackExposureResults_attackPaths resource."""

    _NAME = 'organizations_locations_simulations_attackExposureResults_attackPaths'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsLocationsSimulationsAttackExposureResultsAttackPathsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the attack paths for a set of simulation results or valued resources and filter.

      Args:
        request: (SecuritycenterOrganizationsLocationsSimulationsAttackExposureResultsAttackPathsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttackPathsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/simulations/{simulationsId}/attackExposureResults/{attackExposureResultsId}/attackPaths',
        http_method='GET',
        method_id='securitycenter.organizations.locations.simulations.attackExposureResults.attackPaths.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/attackPaths',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsSimulationsAttackExposureResultsAttackPathsListRequest',
        response_type_name='ListAttackPathsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsSimulationsAttackExposureResultsService(base_api.BaseApiService):
    """Service class for the organizations_locations_simulations_attackExposureResults resource."""

    _NAME = 'organizations_locations_simulations_attackExposureResults'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsLocationsSimulationsAttackExposureResultsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsLocationsSimulationsValuedResourcesAttackPathsService(base_api.BaseApiService):
    """Service class for the organizations_locations_simulations_valuedResources_attackPaths resource."""

    _NAME = 'organizations_locations_simulations_valuedResources_attackPaths'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsLocationsSimulationsValuedResourcesAttackPathsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the attack paths for a set of simulation results or valued resources and filter.

      Args:
        request: (SecuritycenterOrganizationsLocationsSimulationsValuedResourcesAttackPathsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttackPathsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/simulations/{simulationsId}/valuedResources/{valuedResourcesId}/attackPaths',
        http_method='GET',
        method_id='securitycenter.organizations.locations.simulations.valuedResources.attackPaths.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/attackPaths',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsSimulationsValuedResourcesAttackPathsListRequest',
        response_type_name='ListAttackPathsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsSimulationsValuedResourcesService(base_api.BaseApiService):
    """Service class for the organizations_locations_simulations_valuedResources resource."""

    _NAME = 'organizations_locations_simulations_valuedResources'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsLocationsSimulationsValuedResourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get the valued resource by name.

      Args:
        request: (SecuritycenterOrganizationsLocationsSimulationsValuedResourcesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ValuedResource) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/simulations/{simulationsId}/valuedResources/{valuedResourcesId}',
        http_method='GET',
        method_id='securitycenter.organizations.locations.simulations.valuedResources.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsSimulationsValuedResourcesGetRequest',
        response_type_name='ValuedResource',
        supports_download=False,
    )

  class OrganizationsLocationsSimulationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_simulations resource."""

    _NAME = 'organizations_locations_simulations'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsLocationsSimulationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get the simulation by name or the latest simulation for the given organization.

      Args:
        request: (SecuritycenterOrganizationsLocationsSimulationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Simulation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/simulations/{simulationsId}',
        http_method='GET',
        method_id='securitycenter.organizations.locations.simulations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsLocationsSimulationsGetRequest',
        response_type_name='Simulation',
        supports_download=False,
    )

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsMuteConfigsService(base_api.BaseApiService):
    """Service class for the organizations_muteConfigs resource."""

    _NAME = 'organizations_muteConfigs'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsMuteConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a mute config.

      Args:
        request: (SecuritycenterOrganizationsMuteConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/muteConfigs',
        http_method='POST',
        method_id='securitycenter.organizations.muteConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['muteConfigId'],
        relative_path='v2/{+parent}/muteConfigs',
        request_field='googleCloudSecuritycenterV2MuteConfig',
        request_type_name='SecuritycenterOrganizationsMuteConfigsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterOrganizationsMuteConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/muteConfigs/{muteConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.organizations.muteConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsMuteConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterOrganizationsMuteConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/muteConfigs/{muteConfigsId}',
        http_method='GET',
        method_id='securitycenter.organizations.muteConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsMuteConfigsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists mute configs. If no location is specified, default is global.

      Args:
        request: (SecuritycenterOrganizationsMuteConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMuteConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/muteConfigs',
        http_method='GET',
        method_id='securitycenter.organizations.muteConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/muteConfigs',
        request_field='',
        request_type_name='SecuritycenterOrganizationsMuteConfigsListRequest',
        response_type_name='ListMuteConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterOrganizationsMuteConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/muteConfigs/{muteConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.muteConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2MuteConfig',
        request_type_name='SecuritycenterOrganizationsMuteConfigsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

  class OrganizationsOperationsService(base_api.BaseApiService):
    """Service class for the organizations_operations resource."""

    _NAME = 'organizations_operations'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (SecuritycenterOrganizationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='securitycenter.organizations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='',
        request_type_name='SecuritycenterOrganizationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (SecuritycenterOrganizationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='securitycenter.organizations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (SecuritycenterOrganizationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='securitycenter.organizations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (SecuritycenterOrganizationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/operations',
        http_method='GET',
        method_id='securitycenter.organizations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class OrganizationsResourceValueConfigsService(base_api.BaseApiService):
    """Service class for the organizations_resourceValueConfigs resource."""

    _NAME = 'organizations_resourceValueConfigs'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsResourceValueConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Creates a ResourceValueConfig for an organization. Maps user's tags to difference resource values for use by the attack path simulation.

      Args:
        request: (SecuritycenterOrganizationsResourceValueConfigsBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BatchCreateResourceValueConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/resourceValueConfigs:batchCreate',
        http_method='POST',
        method_id='securitycenter.organizations.resourceValueConfigs.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/resourceValueConfigs:batchCreate',
        request_field='batchCreateResourceValueConfigsRequest',
        request_type_name='SecuritycenterOrganizationsResourceValueConfigsBatchCreateRequest',
        response_type_name='BatchCreateResourceValueConfigsResponse',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a ResourceValueConfig.

      Args:
        request: (SecuritycenterOrganizationsResourceValueConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/resourceValueConfigs/{resourceValueConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.organizations.resourceValueConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsResourceValueConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a ResourceValueConfig.

      Args:
        request: (SecuritycenterOrganizationsResourceValueConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2ResourceValueConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/resourceValueConfigs/{resourceValueConfigsId}',
        http_method='GET',
        method_id='securitycenter.organizations.resourceValueConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsResourceValueConfigsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV2ResourceValueConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all ResourceValueConfigs.

      Args:
        request: (SecuritycenterOrganizationsResourceValueConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListResourceValueConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/resourceValueConfigs',
        http_method='GET',
        method_id='securitycenter.organizations.resourceValueConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/resourceValueConfigs',
        request_field='',
        request_type_name='SecuritycenterOrganizationsResourceValueConfigsListRequest',
        response_type_name='ListResourceValueConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing ResourceValueConfigs with new rules.

      Args:
        request: (SecuritycenterOrganizationsResourceValueConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2ResourceValueConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/resourceValueConfigs/{resourceValueConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.resourceValueConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2ResourceValueConfig',
        request_type_name='SecuritycenterOrganizationsResourceValueConfigsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2ResourceValueConfig',
        supports_download=False,
    )

  class OrganizationsSimulationsAttackExposureResultsAttackPathsService(base_api.BaseApiService):
    """Service class for the organizations_simulations_attackExposureResults_attackPaths resource."""

    _NAME = 'organizations_simulations_attackExposureResults_attackPaths'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsSimulationsAttackExposureResultsAttackPathsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the attack paths for a set of simulation results or valued resources and filter.

      Args:
        request: (SecuritycenterOrganizationsSimulationsAttackExposureResultsAttackPathsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttackPathsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/simulations/{simulationsId}/attackExposureResults/{attackExposureResultsId}/attackPaths',
        http_method='GET',
        method_id='securitycenter.organizations.simulations.attackExposureResults.attackPaths.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/attackPaths',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSimulationsAttackExposureResultsAttackPathsListRequest',
        response_type_name='ListAttackPathsResponse',
        supports_download=False,
    )

  class OrganizationsSimulationsAttackExposureResultsValuedResourcesService(base_api.BaseApiService):
    """Service class for the organizations_simulations_attackExposureResults_valuedResources resource."""

    _NAME = 'organizations_simulations_attackExposureResults_valuedResources'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsSimulationsAttackExposureResultsValuedResourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the valued resources for a set of simulation results and filter.

      Args:
        request: (SecuritycenterOrganizationsSimulationsAttackExposureResultsValuedResourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListValuedResourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/simulations/{simulationsId}/attackExposureResults/{attackExposureResultsId}/valuedResources',
        http_method='GET',
        method_id='securitycenter.organizations.simulations.attackExposureResults.valuedResources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/valuedResources',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSimulationsAttackExposureResultsValuedResourcesListRequest',
        response_type_name='ListValuedResourcesResponse',
        supports_download=False,
    )

  class OrganizationsSimulationsAttackExposureResultsService(base_api.BaseApiService):
    """Service class for the organizations_simulations_attackExposureResults resource."""

    _NAME = 'organizations_simulations_attackExposureResults'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsSimulationsAttackExposureResultsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsSimulationsAttackPathsService(base_api.BaseApiService):
    """Service class for the organizations_simulations_attackPaths resource."""

    _NAME = 'organizations_simulations_attackPaths'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsSimulationsAttackPathsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the attack paths for a set of simulation results or valued resources and filter.

      Args:
        request: (SecuritycenterOrganizationsSimulationsAttackPathsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttackPathsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/simulations/{simulationsId}/attackPaths',
        http_method='GET',
        method_id='securitycenter.organizations.simulations.attackPaths.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/attackPaths',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSimulationsAttackPathsListRequest',
        response_type_name='ListAttackPathsResponse',
        supports_download=False,
    )

  class OrganizationsSimulationsValuedResourcesAttackPathsService(base_api.BaseApiService):
    """Service class for the organizations_simulations_valuedResources_attackPaths resource."""

    _NAME = 'organizations_simulations_valuedResources_attackPaths'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsSimulationsValuedResourcesAttackPathsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the attack paths for a set of simulation results or valued resources and filter.

      Args:
        request: (SecuritycenterOrganizationsSimulationsValuedResourcesAttackPathsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttackPathsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/simulations/{simulationsId}/valuedResources/{valuedResourcesId}/attackPaths',
        http_method='GET',
        method_id='securitycenter.organizations.simulations.valuedResources.attackPaths.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/attackPaths',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSimulationsValuedResourcesAttackPathsListRequest',
        response_type_name='ListAttackPathsResponse',
        supports_download=False,
    )

  class OrganizationsSimulationsValuedResourcesService(base_api.BaseApiService):
    """Service class for the organizations_simulations_valuedResources resource."""

    _NAME = 'organizations_simulations_valuedResources'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsSimulationsValuedResourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get the valued resource by name.

      Args:
        request: (SecuritycenterOrganizationsSimulationsValuedResourcesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ValuedResource) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/simulations/{simulationsId}/valuedResources/{valuedResourcesId}',
        http_method='GET',
        method_id='securitycenter.organizations.simulations.valuedResources.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSimulationsValuedResourcesGetRequest',
        response_type_name='ValuedResource',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the valued resources for a set of simulation results and filter.

      Args:
        request: (SecuritycenterOrganizationsSimulationsValuedResourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListValuedResourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/simulations/{simulationsId}/valuedResources',
        http_method='GET',
        method_id='securitycenter.organizations.simulations.valuedResources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/valuedResources',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSimulationsValuedResourcesListRequest',
        response_type_name='ListValuedResourcesResponse',
        supports_download=False,
    )

  class OrganizationsSimulationsService(base_api.BaseApiService):
    """Service class for the organizations_simulations resource."""

    _NAME = 'organizations_simulations'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsSimulationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get the simulation by name or the latest simulation for the given organization.

      Args:
        request: (SecuritycenterOrganizationsSimulationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Simulation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/simulations/{simulationsId}',
        http_method='GET',
        method_id='securitycenter.organizations.simulations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSimulationsGetRequest',
        response_type_name='Simulation',
        supports_download=False,
    )

  class OrganizationsSourcesFindingsExternalSystemsService(base_api.BaseApiService):
    """Service class for the organizations_sources_findings_externalSystems resource."""

    _NAME = 'organizations_sources_findings_externalSystems'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsSourcesFindingsExternalSystemsService, self).__init__(client)
      self._upload_configs = {
          }

    def Patch(self, request, global_params=None):
      r"""Updates external system. This is for a given finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsExternalSystemsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2ExternalSystem) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/findings/{findingsId}/externalSystems/{externalSystemsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.sources.findings.externalSystems.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2ExternalSystem',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsExternalSystemsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2ExternalSystem',
        supports_download=False,
    )

  class OrganizationsSourcesFindingsService(base_api.BaseApiService):
    """Service class for the organizations_sources_findings resource."""

    _NAME = 'organizations_sources_findings'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsSourcesFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a finding in a location. The corresponding source must exist for finding creation to succeed.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/findings',
        http_method='POST',
        method_id='securitycenter.organizations.sources.findings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['findingId'],
        relative_path='v2/{+parent}/findings',
        request_field='googleCloudSecuritycenterV2Finding',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def Group(self, request, global_params=None):
      r"""Filters an organization or source's findings and groups them by their specified properties in a location. If no location is specified, findings are assumed to be in global To group across all sources provide a `-` as the source id. The following list shows some examples: + `/v2/organizations/{organization_id}/sources/-/findings` + `/v2/organizations/{organization_id}/sources/-/locations/{location_id}/findings` + `/v2/folders/{folder_id}/sources/-/findings` + `/v2/folders/{folder_id}/sources/-/locations/{location_id}/findings` + `/v2/projects/{project_id}/sources/-/findings` + `/v2/projects/{project_id}/sources/-/locations/{location_id}/findings`.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsGroupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GroupFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('Group')
      return self._RunMethod(
          config, request, global_params=global_params)

    Group.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/findings:group',
        http_method='POST',
        method_id='securitycenter.organizations.sources.findings.group',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:group',
        request_field='groupFindingsRequest',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsGroupRequest',
        response_type_name='GroupFindingsResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists an organization or source's findings. To list across all sources for a given location provide a `-` as the source id. If no location is specified, finding are assumed to be in global. The following list shows some examples: + `/v2/organizations/{organization_id}/sources/-/findings` + `/v2/organizations/{organization_id}/sources/-/locations/{location_id}/findings`.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/findings',
        http_method='GET',
        method_id='securitycenter.organizations.sources.findings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['fieldMask', 'filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/findings',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsListRequest',
        response_type_name='ListFindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Creates or updates a finding. If no location is specified, finding is assumed to be in global. The corresponding source must exist for a finding creation to succeed.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/findings/{findingsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.sources.findings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2Finding',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def SetMute(self, request, global_params=None):
      r"""Updates the mute state of a finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsSetMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('SetMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/findings/{findingsId}:setMute',
        http_method='POST',
        method_id='securitycenter.organizations.sources.findings.setMute',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:setMute',
        request_field='setMuteRequest',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsSetMuteRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def SetState(self, request, global_params=None):
      r"""Updates the state of a finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsSetStateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('SetState')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetState.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/findings/{findingsId}:setState',
        http_method='POST',
        method_id='securitycenter.organizations.sources.findings.setState',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:setState',
        request_field='setFindingStateRequest',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsSetStateRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks. For Finding Security marks, if no location is specified, finding is assumed to be in global. Assets Security Marks can only be accessed through global endpoint.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/findings/{findingsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.organizations.sources.findings.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2SecurityMarks',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsUpdateSecurityMarksRequest',
        response_type_name='GoogleCloudSecuritycenterV2SecurityMarks',
        supports_download=False,
    )

  class OrganizationsSourcesLocationsFindingsExternalSystemsService(base_api.BaseApiService):
    """Service class for the organizations_sources_locations_findings_externalSystems resource."""

    _NAME = 'organizations_sources_locations_findings_externalSystems'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsSourcesLocationsFindingsExternalSystemsService, self).__init__(client)
      self._upload_configs = {
          }

    def Patch(self, request, global_params=None):
      r"""Updates external system. This is for a given finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterOrganizationsSourcesLocationsFindingsExternalSystemsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2ExternalSystem) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}/externalSystems/{externalSystemsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.sources.locations.findings.externalSystems.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2ExternalSystem',
        request_type_name='SecuritycenterOrganizationsSourcesLocationsFindingsExternalSystemsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2ExternalSystem',
        supports_download=False,
    )

  class OrganizationsSourcesLocationsFindingsService(base_api.BaseApiService):
    """Service class for the organizations_sources_locations_findings resource."""

    _NAME = 'organizations_sources_locations_findings'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsSourcesLocationsFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a finding in a location. The corresponding source must exist for finding creation to succeed.

      Args:
        request: (SecuritycenterOrganizationsSourcesLocationsFindingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/locations/{locationsId}/findings',
        http_method='POST',
        method_id='securitycenter.organizations.sources.locations.findings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['findingId'],
        relative_path='v2/{+parent}/findings',
        request_field='googleCloudSecuritycenterV2Finding',
        request_type_name='SecuritycenterOrganizationsSourcesLocationsFindingsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Kicks off an LRO to export findings for an organization to the customer's BigQuery dataset.

      Args:
        request: (SecuritycenterOrganizationsSourcesLocationsFindingsExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/locations/{locationsId}/findings:export',
        http_method='POST',
        method_id='securitycenter.organizations.sources.locations.findings.export',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:export',
        request_field='exportFindingsRequest',
        request_type_name='SecuritycenterOrganizationsSourcesLocationsFindingsExportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Group(self, request, global_params=None):
      r"""Filters an organization or source's findings and groups them by their specified properties in a location. If no location is specified, findings are assumed to be in global To group across all sources provide a `-` as the source id. The following list shows some examples: + `/v2/organizations/{organization_id}/sources/-/findings` + `/v2/organizations/{organization_id}/sources/-/locations/{location_id}/findings` + `/v2/folders/{folder_id}/sources/-/findings` + `/v2/folders/{folder_id}/sources/-/locations/{location_id}/findings` + `/v2/projects/{project_id}/sources/-/findings` + `/v2/projects/{project_id}/sources/-/locations/{location_id}/findings`.

      Args:
        request: (SecuritycenterOrganizationsSourcesLocationsFindingsGroupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GroupFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('Group')
      return self._RunMethod(
          config, request, global_params=global_params)

    Group.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/locations/{locationsId}/findings:group',
        http_method='POST',
        method_id='securitycenter.organizations.sources.locations.findings.group',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:group',
        request_field='groupFindingsRequest',
        request_type_name='SecuritycenterOrganizationsSourcesLocationsFindingsGroupRequest',
        response_type_name='GroupFindingsResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists an organization or source's findings. To list across all sources for a given location provide a `-` as the source id. If no location is specified, finding are assumed to be in global. The following list shows some examples: + `/v2/organizations/{organization_id}/sources/-/findings` + `/v2/organizations/{organization_id}/sources/-/locations/{location_id}/findings`.

      Args:
        request: (SecuritycenterOrganizationsSourcesLocationsFindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/locations/{locationsId}/findings',
        http_method='GET',
        method_id='securitycenter.organizations.sources.locations.findings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['fieldMask', 'filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/findings',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSourcesLocationsFindingsListRequest',
        response_type_name='ListFindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Creates or updates a finding. If no location is specified, finding is assumed to be in global. The corresponding source must exist for a finding creation to succeed.

      Args:
        request: (SecuritycenterOrganizationsSourcesLocationsFindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.sources.locations.findings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2Finding',
        request_type_name='SecuritycenterOrganizationsSourcesLocationsFindingsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def SetMute(self, request, global_params=None):
      r"""Updates the mute state of a finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterOrganizationsSourcesLocationsFindingsSetMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('SetMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}:setMute',
        http_method='POST',
        method_id='securitycenter.organizations.sources.locations.findings.setMute',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:setMute',
        request_field='setMuteRequest',
        request_type_name='SecuritycenterOrganizationsSourcesLocationsFindingsSetMuteRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def SetState(self, request, global_params=None):
      r"""Updates the state of a finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterOrganizationsSourcesLocationsFindingsSetStateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('SetState')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetState.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}:setState',
        http_method='POST',
        method_id='securitycenter.organizations.sources.locations.findings.setState',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:setState',
        request_field='setFindingStateRequest',
        request_type_name='SecuritycenterOrganizationsSourcesLocationsFindingsSetStateRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks. For Finding Security marks, if no location is specified, finding is assumed to be in global. Assets Security Marks can only be accessed through global endpoint.

      Args:
        request: (SecuritycenterOrganizationsSourcesLocationsFindingsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.organizations.sources.locations.findings.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2SecurityMarks',
        request_type_name='SecuritycenterOrganizationsSourcesLocationsFindingsUpdateSecurityMarksRequest',
        response_type_name='GoogleCloudSecuritycenterV2SecurityMarks',
        supports_download=False,
    )

  class OrganizationsSourcesLocationsService(base_api.BaseApiService):
    """Service class for the organizations_sources_locations resource."""

    _NAME = 'organizations_sources_locations'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsSourcesLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsSourcesService(base_api.BaseApiService):
    """Service class for the organizations_sources resource."""

    _NAME = 'organizations_sources'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsSourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a source.

      Args:
        request: (SecuritycenterOrganizationsSourcesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Source) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources',
        http_method='POST',
        method_id='securitycenter.organizations.sources.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/sources',
        request_field='source',
        request_type_name='SecuritycenterOrganizationsSourcesCreateRequest',
        response_type_name='Source',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a source.

      Args:
        request: (SecuritycenterOrganizationsSourcesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Source) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}',
        http_method='GET',
        method_id='securitycenter.organizations.sources.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSourcesGetRequest',
        response_type_name='Source',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy on the specified Source.

      Args:
        request: (SecuritycenterOrganizationsSourcesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}:getIamPolicy',
        http_method='POST',
        method_id='securitycenter.organizations.sources.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='SecuritycenterOrganizationsSourcesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all sources belonging to an organization.

      Args:
        request: (SecuritycenterOrganizationsSourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources',
        http_method='GET',
        method_id='securitycenter.organizations.sources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/sources',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSourcesListRequest',
        response_type_name='ListSourcesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a source.

      Args:
        request: (SecuritycenterOrganizationsSourcesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Source) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.sources.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='source',
        request_type_name='SecuritycenterOrganizationsSourcesPatchRequest',
        response_type_name='Source',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified Source.

      Args:
        request: (SecuritycenterOrganizationsSourcesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}:setIamPolicy',
        http_method='POST',
        method_id='securitycenter.organizations.sources.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='SecuritycenterOrganizationsSourcesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns the permissions that a caller has on the specified source.

      Args:
        request: (SecuritycenterOrganizationsSourcesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/sources/{sourcesId}:testIamPermissions',
        http_method='POST',
        method_id='securitycenter.organizations.sources.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='SecuritycenterOrganizationsSourcesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class OrganizationsValuedResourcesService(base_api.BaseApiService):
    """Service class for the organizations_valuedResources resource."""

    _NAME = 'organizations_valuedResources'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsValuedResourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the valued resources for a set of simulation results and filter.

      Args:
        request: (SecuritycenterOrganizationsValuedResourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListValuedResourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/valuedResources',
        http_method='GET',
        method_id='securitycenter.organizations.valuedResources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/valuedResources',
        request_field='',
        request_type_name='SecuritycenterOrganizationsValuedResourcesListRequest',
        response_type_name='ListValuedResourcesResponse',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(SecuritycenterV2.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsAssetsService(base_api.BaseApiService):
    """Service class for the projects_assets resource."""

    _NAME = 'projects_assets'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsAssetsService, self).__init__(client)
      self._upload_configs = {
          }

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks. For Finding Security marks, if no location is specified, finding is assumed to be in global. Assets Security Marks can only be accessed through global endpoint.

      Args:
        request: (SecuritycenterProjectsAssetsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/assets/{assetsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.projects.assets.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2SecurityMarks',
        request_type_name='SecuritycenterProjectsAssetsUpdateSecurityMarksRequest',
        response_type_name='GoogleCloudSecuritycenterV2SecurityMarks',
        supports_download=False,
    )

  class ProjectsFindingsService(base_api.BaseApiService):
    """Service class for the projects_findings resource."""

    _NAME = 'projects_findings'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def BulkMute(self, request, global_params=None):
      r"""Kicks off an LRO to bulk mute findings for a parent based on a filter. If no location is specified, findings are muted in global. The parent can be either an organization, folder, or project. The findings matched by the filter will be muted after the LRO is done.

      Args:
        request: (SecuritycenterProjectsFindingsBulkMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BulkMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    BulkMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/findings:bulkMute',
        http_method='POST',
        method_id='securitycenter.projects.findings.bulkMute',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:bulkMute',
        request_field='bulkMuteFindingsRequest',
        request_type_name='SecuritycenterProjectsFindingsBulkMuteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsBigQueryExportsService(base_api.BaseApiService):
    """Service class for the projects_locations_bigQueryExports resource."""

    _NAME = 'projects_locations_bigQueryExports'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsLocationsBigQueryExportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a BigQuery export.

      Args:
        request: (SecuritycenterProjectsLocationsBigQueryExportsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/bigQueryExports',
        http_method='POST',
        method_id='securitycenter.projects.locations.bigQueryExports.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bigQueryExportId'],
        relative_path='v2/{+parent}/bigQueryExports',
        request_field='googleCloudSecuritycenterV2BigQueryExport',
        request_type_name='SecuritycenterProjectsLocationsBigQueryExportsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV2BigQueryExport',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing BigQuery export.

      Args:
        request: (SecuritycenterProjectsLocationsBigQueryExportsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='DELETE',
        method_id='securitycenter.projects.locations.bigQueryExports.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsLocationsBigQueryExportsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a BigQuery export.

      Args:
        request: (SecuritycenterProjectsLocationsBigQueryExportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='GET',
        method_id='securitycenter.projects.locations.bigQueryExports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsLocationsBigQueryExportsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV2BigQueryExport',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BigQuery exports. Note that when requesting BigQuery exports at a given level all exports under that level are also returned e.g. if requesting BigQuery exports under a folder, then all BigQuery exports immediately under the folder plus the ones created under the projects within the folder are returned.

      Args:
        request: (SecuritycenterProjectsLocationsBigQueryExportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBigQueryExportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/bigQueryExports',
        http_method='GET',
        method_id='securitycenter.projects.locations.bigQueryExports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/bigQueryExports',
        request_field='',
        request_type_name='SecuritycenterProjectsLocationsBigQueryExportsListRequest',
        response_type_name='ListBigQueryExportsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a BigQuery export.

      Args:
        request: (SecuritycenterProjectsLocationsBigQueryExportsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='PATCH',
        method_id='securitycenter.projects.locations.bigQueryExports.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2BigQueryExport',
        request_type_name='SecuritycenterProjectsLocationsBigQueryExportsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2BigQueryExport',
        supports_download=False,
    )

  class ProjectsLocationsFindingsService(base_api.BaseApiService):
    """Service class for the projects_locations_findings resource."""

    _NAME = 'projects_locations_findings'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsLocationsFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def BulkMute(self, request, global_params=None):
      r"""Kicks off an LRO to bulk mute findings for a parent based on a filter. If no location is specified, findings are muted in global. The parent can be either an organization, folder, or project. The findings matched by the filter will be muted after the LRO is done.

      Args:
        request: (SecuritycenterProjectsLocationsFindingsBulkMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BulkMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    BulkMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/findings:bulkMute',
        http_method='POST',
        method_id='securitycenter.projects.locations.findings.bulkMute',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:bulkMute',
        request_field='bulkMuteFindingsRequest',
        request_type_name='SecuritycenterProjectsLocationsFindingsBulkMuteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMuteConfigsService(base_api.BaseApiService):
    """Service class for the projects_locations_muteConfigs resource."""

    _NAME = 'projects_locations_muteConfigs'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsLocationsMuteConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a mute config.

      Args:
        request: (SecuritycenterProjectsLocationsMuteConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/muteConfigs',
        http_method='POST',
        method_id='securitycenter.projects.locations.muteConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['muteConfigId'],
        relative_path='v2/{+parent}/muteConfigs',
        request_field='googleCloudSecuritycenterV2MuteConfig',
        request_type_name='SecuritycenterProjectsLocationsMuteConfigsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterProjectsLocationsMuteConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/muteConfigs/{muteConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.projects.locations.muteConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsLocationsMuteConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterProjectsLocationsMuteConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/muteConfigs/{muteConfigsId}',
        http_method='GET',
        method_id='securitycenter.projects.locations.muteConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsLocationsMuteConfigsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists mute configs. If no location is specified, default is global.

      Args:
        request: (SecuritycenterProjectsLocationsMuteConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMuteConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/muteConfigs',
        http_method='GET',
        method_id='securitycenter.projects.locations.muteConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/muteConfigs',
        request_field='',
        request_type_name='SecuritycenterProjectsLocationsMuteConfigsListRequest',
        response_type_name='ListMuteConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterProjectsLocationsMuteConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/muteConfigs/{muteConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.projects.locations.muteConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2MuteConfig',
        request_type_name='SecuritycenterProjectsLocationsMuteConfigsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

  class ProjectsLocationsNotificationConfigsService(base_api.BaseApiService):
    """Service class for the projects_locations_notificationConfigs resource."""

    _NAME = 'projects_locations_notificationConfigs'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsLocationsNotificationConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a notification config.

      Args:
        request: (SecuritycenterProjectsLocationsNotificationConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/notificationConfigs',
        http_method='POST',
        method_id='securitycenter.projects.locations.notificationConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['configId'],
        relative_path='v2/{+parent}/notificationConfigs',
        request_field='notificationConfig',
        request_type_name='SecuritycenterProjectsLocationsNotificationConfigsCreateRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a notification config.

      Args:
        request: (SecuritycenterProjectsLocationsNotificationConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/notificationConfigs/{notificationConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.projects.locations.notificationConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsLocationsNotificationConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a notification config.

      Args:
        request: (SecuritycenterProjectsLocationsNotificationConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/notificationConfigs/{notificationConfigsId}',
        http_method='GET',
        method_id='securitycenter.projects.locations.notificationConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsLocationsNotificationConfigsGetRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists notification configs.

      Args:
        request: (SecuritycenterProjectsLocationsNotificationConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNotificationConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/notificationConfigs',
        http_method='GET',
        method_id='securitycenter.projects.locations.notificationConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/notificationConfigs',
        request_field='',
        request_type_name='SecuritycenterProjectsLocationsNotificationConfigsListRequest',
        response_type_name='ListNotificationConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a notification config. The following update fields are allowed: description, pubsub_topic, streaming_config.filter.

      Args:
        request: (SecuritycenterProjectsLocationsNotificationConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/notificationConfigs/{notificationConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.projects.locations.notificationConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='notificationConfig',
        request_type_name='SecuritycenterProjectsLocationsNotificationConfigsPatchRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsMuteConfigsService(base_api.BaseApiService):
    """Service class for the projects_muteConfigs resource."""

    _NAME = 'projects_muteConfigs'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsMuteConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a mute config.

      Args:
        request: (SecuritycenterProjectsMuteConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/muteConfigs',
        http_method='POST',
        method_id='securitycenter.projects.muteConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['muteConfigId'],
        relative_path='v2/{+parent}/muteConfigs',
        request_field='googleCloudSecuritycenterV2MuteConfig',
        request_type_name='SecuritycenterProjectsMuteConfigsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterProjectsMuteConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/muteConfigs/{muteConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.projects.muteConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsMuteConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterProjectsMuteConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/muteConfigs/{muteConfigsId}',
        http_method='GET',
        method_id='securitycenter.projects.muteConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsMuteConfigsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists mute configs. If no location is specified, default is global.

      Args:
        request: (SecuritycenterProjectsMuteConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMuteConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/muteConfigs',
        http_method='GET',
        method_id='securitycenter.projects.muteConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/muteConfigs',
        request_field='',
        request_type_name='SecuritycenterProjectsMuteConfigsListRequest',
        response_type_name='ListMuteConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a mute config. If no location is specified, default is global.

      Args:
        request: (SecuritycenterProjectsMuteConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/muteConfigs/{muteConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.projects.muteConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2MuteConfig',
        request_type_name='SecuritycenterProjectsMuteConfigsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2MuteConfig',
        supports_download=False,
    )

  class ProjectsSourcesFindingsExternalSystemsService(base_api.BaseApiService):
    """Service class for the projects_sources_findings_externalSystems resource."""

    _NAME = 'projects_sources_findings_externalSystems'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsSourcesFindingsExternalSystemsService, self).__init__(client)
      self._upload_configs = {
          }

    def Patch(self, request, global_params=None):
      r"""Updates external system. This is for a given finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterProjectsSourcesFindingsExternalSystemsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2ExternalSystem) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/findings/{findingsId}/externalSystems/{externalSystemsId}',
        http_method='PATCH',
        method_id='securitycenter.projects.sources.findings.externalSystems.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2ExternalSystem',
        request_type_name='SecuritycenterProjectsSourcesFindingsExternalSystemsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2ExternalSystem',
        supports_download=False,
    )

  class ProjectsSourcesFindingsService(base_api.BaseApiService):
    """Service class for the projects_sources_findings resource."""

    _NAME = 'projects_sources_findings'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsSourcesFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Group(self, request, global_params=None):
      r"""Filters an organization or source's findings and groups them by their specified properties in a location. If no location is specified, findings are assumed to be in global To group across all sources provide a `-` as the source id. The following list shows some examples: + `/v2/organizations/{organization_id}/sources/-/findings` + `/v2/organizations/{organization_id}/sources/-/locations/{location_id}/findings` + `/v2/folders/{folder_id}/sources/-/findings` + `/v2/folders/{folder_id}/sources/-/locations/{location_id}/findings` + `/v2/projects/{project_id}/sources/-/findings` + `/v2/projects/{project_id}/sources/-/locations/{location_id}/findings`.

      Args:
        request: (SecuritycenterProjectsSourcesFindingsGroupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GroupFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('Group')
      return self._RunMethod(
          config, request, global_params=global_params)

    Group.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/findings:group',
        http_method='POST',
        method_id='securitycenter.projects.sources.findings.group',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:group',
        request_field='groupFindingsRequest',
        request_type_name='SecuritycenterProjectsSourcesFindingsGroupRequest',
        response_type_name='GroupFindingsResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists an organization or source's findings. To list across all sources for a given location provide a `-` as the source id. If no location is specified, finding are assumed to be in global. The following list shows some examples: + `/v2/organizations/{organization_id}/sources/-/findings` + `/v2/organizations/{organization_id}/sources/-/locations/{location_id}/findings`.

      Args:
        request: (SecuritycenterProjectsSourcesFindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/findings',
        http_method='GET',
        method_id='securitycenter.projects.sources.findings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['fieldMask', 'filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/findings',
        request_field='',
        request_type_name='SecuritycenterProjectsSourcesFindingsListRequest',
        response_type_name='ListFindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Creates or updates a finding. If no location is specified, finding is assumed to be in global. The corresponding source must exist for a finding creation to succeed.

      Args:
        request: (SecuritycenterProjectsSourcesFindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/findings/{findingsId}',
        http_method='PATCH',
        method_id='securitycenter.projects.sources.findings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2Finding',
        request_type_name='SecuritycenterProjectsSourcesFindingsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def SetMute(self, request, global_params=None):
      r"""Updates the mute state of a finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterProjectsSourcesFindingsSetMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('SetMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/findings/{findingsId}:setMute',
        http_method='POST',
        method_id='securitycenter.projects.sources.findings.setMute',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:setMute',
        request_field='setMuteRequest',
        request_type_name='SecuritycenterProjectsSourcesFindingsSetMuteRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def SetState(self, request, global_params=None):
      r"""Updates the state of a finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterProjectsSourcesFindingsSetStateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('SetState')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetState.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/findings/{findingsId}:setState',
        http_method='POST',
        method_id='securitycenter.projects.sources.findings.setState',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:setState',
        request_field='setFindingStateRequest',
        request_type_name='SecuritycenterProjectsSourcesFindingsSetStateRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks. For Finding Security marks, if no location is specified, finding is assumed to be in global. Assets Security Marks can only be accessed through global endpoint.

      Args:
        request: (SecuritycenterProjectsSourcesFindingsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/findings/{findingsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.projects.sources.findings.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2SecurityMarks',
        request_type_name='SecuritycenterProjectsSourcesFindingsUpdateSecurityMarksRequest',
        response_type_name='GoogleCloudSecuritycenterV2SecurityMarks',
        supports_download=False,
    )

  class ProjectsSourcesLocationsFindingsExternalSystemsService(base_api.BaseApiService):
    """Service class for the projects_sources_locations_findings_externalSystems resource."""

    _NAME = 'projects_sources_locations_findings_externalSystems'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsSourcesLocationsFindingsExternalSystemsService, self).__init__(client)
      self._upload_configs = {
          }

    def Patch(self, request, global_params=None):
      r"""Updates external system. This is for a given finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterProjectsSourcesLocationsFindingsExternalSystemsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2ExternalSystem) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}/externalSystems/{externalSystemsId}',
        http_method='PATCH',
        method_id='securitycenter.projects.sources.locations.findings.externalSystems.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2ExternalSystem',
        request_type_name='SecuritycenterProjectsSourcesLocationsFindingsExternalSystemsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2ExternalSystem',
        supports_download=False,
    )

  class ProjectsSourcesLocationsFindingsService(base_api.BaseApiService):
    """Service class for the projects_sources_locations_findings resource."""

    _NAME = 'projects_sources_locations_findings'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsSourcesLocationsFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Export(self, request, global_params=None):
      r"""Kicks off an LRO to export findings for an organization to the customer's BigQuery dataset.

      Args:
        request: (SecuritycenterProjectsSourcesLocationsFindingsExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/locations/{locationsId}/findings:export',
        http_method='POST',
        method_id='securitycenter.projects.sources.locations.findings.export',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:export',
        request_field='exportFindingsRequest',
        request_type_name='SecuritycenterProjectsSourcesLocationsFindingsExportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Group(self, request, global_params=None):
      r"""Filters an organization or source's findings and groups them by their specified properties in a location. If no location is specified, findings are assumed to be in global To group across all sources provide a `-` as the source id. The following list shows some examples: + `/v2/organizations/{organization_id}/sources/-/findings` + `/v2/organizations/{organization_id}/sources/-/locations/{location_id}/findings` + `/v2/folders/{folder_id}/sources/-/findings` + `/v2/folders/{folder_id}/sources/-/locations/{location_id}/findings` + `/v2/projects/{project_id}/sources/-/findings` + `/v2/projects/{project_id}/sources/-/locations/{location_id}/findings`.

      Args:
        request: (SecuritycenterProjectsSourcesLocationsFindingsGroupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GroupFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('Group')
      return self._RunMethod(
          config, request, global_params=global_params)

    Group.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/locations/{locationsId}/findings:group',
        http_method='POST',
        method_id='securitycenter.projects.sources.locations.findings.group',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/findings:group',
        request_field='groupFindingsRequest',
        request_type_name='SecuritycenterProjectsSourcesLocationsFindingsGroupRequest',
        response_type_name='GroupFindingsResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists an organization or source's findings. To list across all sources for a given location provide a `-` as the source id. If no location is specified, finding are assumed to be in global. The following list shows some examples: + `/v2/organizations/{organization_id}/sources/-/findings` + `/v2/organizations/{organization_id}/sources/-/locations/{location_id}/findings`.

      Args:
        request: (SecuritycenterProjectsSourcesLocationsFindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/locations/{locationsId}/findings',
        http_method='GET',
        method_id='securitycenter.projects.sources.locations.findings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['fieldMask', 'filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/findings',
        request_field='',
        request_type_name='SecuritycenterProjectsSourcesLocationsFindingsListRequest',
        response_type_name='ListFindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Creates or updates a finding. If no location is specified, finding is assumed to be in global. The corresponding source must exist for a finding creation to succeed.

      Args:
        request: (SecuritycenterProjectsSourcesLocationsFindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}',
        http_method='PATCH',
        method_id='securitycenter.projects.sources.locations.findings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2Finding',
        request_type_name='SecuritycenterProjectsSourcesLocationsFindingsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def SetMute(self, request, global_params=None):
      r"""Updates the mute state of a finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterProjectsSourcesLocationsFindingsSetMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('SetMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}:setMute',
        http_method='POST',
        method_id='securitycenter.projects.sources.locations.findings.setMute',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:setMute',
        request_field='setMuteRequest',
        request_type_name='SecuritycenterProjectsSourcesLocationsFindingsSetMuteRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def SetState(self, request, global_params=None):
      r"""Updates the state of a finding. If no location is specified, finding is assumed to be in global.

      Args:
        request: (SecuritycenterProjectsSourcesLocationsFindingsSetStateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2Finding) The response message.
      """
      config = self.GetMethodConfig('SetState')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetState.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}:setState',
        http_method='POST',
        method_id='securitycenter.projects.sources.locations.findings.setState',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:setState',
        request_field='setFindingStateRequest',
        request_type_name='SecuritycenterProjectsSourcesLocationsFindingsSetStateRequest',
        response_type_name='GoogleCloudSecuritycenterV2Finding',
        supports_download=False,
    )

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks. For Finding Security marks, if no location is specified, finding is assumed to be in global. Assets Security Marks can only be accessed through global endpoint.

      Args:
        request: (SecuritycenterProjectsSourcesLocationsFindingsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV2SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources/{sourcesId}/locations/{locationsId}/findings/{findingsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.projects.sources.locations.findings.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudSecuritycenterV2SecurityMarks',
        request_type_name='SecuritycenterProjectsSourcesLocationsFindingsUpdateSecurityMarksRequest',
        response_type_name='GoogleCloudSecuritycenterV2SecurityMarks',
        supports_download=False,
    )

  class ProjectsSourcesLocationsService(base_api.BaseApiService):
    """Service class for the projects_sources_locations resource."""

    _NAME = 'projects_sources_locations'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsSourcesLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsSourcesService(base_api.BaseApiService):
    """Service class for the projects_sources resource."""

    _NAME = 'projects_sources'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsSourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all sources belonging to an organization.

      Args:
        request: (SecuritycenterProjectsSourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/sources',
        http_method='GET',
        method_id='securitycenter.projects.sources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/sources',
        request_field='',
        request_type_name='SecuritycenterProjectsSourcesListRequest',
        response_type_name='ListSourcesResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(SecuritycenterV2.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
