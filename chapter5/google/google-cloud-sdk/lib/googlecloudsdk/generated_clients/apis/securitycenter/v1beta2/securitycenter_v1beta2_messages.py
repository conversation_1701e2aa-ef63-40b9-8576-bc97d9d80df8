"""Generated message classes for securitycenter version v1beta2.

Security Command Center API provides access to temporal views of assets and
findings within an organization.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'securitycenter'


class Access(_messages.Message):
  r"""Represents an access event.

  Fields:
    callerIp: Caller's IP address, such as "*******".
    callerIpGeo: The caller IP's geolocation, which identifies where the call
      came from.
    methodName: The method that the service account called, e.g.
      "SetIamPolicy".
    principalEmail: Associated email, such as "<EMAIL>". The email
      address of the authenticated user or a service account acting on behalf
      of a third party principal making the request. For third party identity
      callers, the `principal_subject` field is populated instead of this
      field. For privacy reasons, the principal email address is sometimes
      redacted. For more information, see [Caller identities in audit
      logs](https://cloud.google.com/logging/docs/audit#user-id).
    principalSubject: A string that represents the principal_subject that is
      associated with the identity. Unlike `principal_email`,
      `principal_subject` supports principals that aren't associated with
      email addresses, such as third party principals. For most identities,
      the format is `principal://iam.googleapis.com/{identity pool
      name}/subject/{subject}`. Some GKE identities, such as GKE_WORKLOAD,
      FREEFORM, and GKE_HUB_WORKLOAD, still use the legacy format
      `serviceAccount:{identity pool name}[{subject}]`.
    serviceAccountDelegationInfo: The identity delegation history of an
      authenticated service account that made the request. The
      `serviceAccountDelegationInfo[]` object contains information about the
      real authorities that try to access Google Cloud resources by delegating
      on a service account. When multiple authorities are present, they are
      guaranteed to be sorted based on the original ordering of the identity
      delegation events.
    serviceAccountKeyName: The name of the service account key that was used
      to create or exchange credentials when authenticating the service
      account that made the request. This is a scheme-less URI full resource
      name. For example: "//iam.googleapis.com/projects/{PROJECT_ID}/serviceAc
      counts/{ACCOUNT}/keys/{key}".
    serviceName: This is the API service that the service account made a call
      to, e.g. "iam.googleapis.com"
    userAgent: The caller's user agent string associated with the finding.
    userAgentFamily: Type of user agent associated with the finding. For
      example, an operating system shell or an embedded or standalone
      application.
    userName: A string that represents a username. The username provided
      depends on the type of the finding and is likely not an IAM principal.
      For example, this can be a system username if the finding is related to
      a virtual machine, or it can be an application login username.
  """

  callerIp = _messages.StringField(1)
  callerIpGeo = _messages.MessageField('Geolocation', 2)
  methodName = _messages.StringField(3)
  principalEmail = _messages.StringField(4)
  principalSubject = _messages.StringField(5)
  serviceAccountDelegationInfo = _messages.MessageField('ServiceAccountDelegationInfo', 6, repeated=True)
  serviceAccountKeyName = _messages.StringField(7)
  serviceName = _messages.StringField(8)
  userAgent = _messages.StringField(9)
  userAgentFamily = _messages.StringField(10)
  userName = _messages.StringField(11)


class AccessReview(_messages.Message):
  r"""Conveys information about a Kubernetes access review (such as one
  returned by a [`kubectl auth
  can-i`](https://kubernetes.io/docs/reference/access-authn-
  authz/authorization/#checking-api-access) command) that was involved in a
  finding.

  Fields:
    group: The API group of the resource. "*" means all.
    name: The name of the resource being requested. Empty means all.
    ns: Namespace of the action being requested. Currently, there is no
      distinction between no namespace and all namespaces. Both are
      represented by "" (empty).
    resource: The optional resource type requested. "*" means all.
    subresource: The optional subresource type.
    verb: A Kubernetes resource API verb, like get, list, watch, create,
      update, delete, proxy. "*" means all.
    version: The API version of the resource. "*" means all.
  """

  group = _messages.StringField(1)
  name = _messages.StringField(2)
  ns = _messages.StringField(3)
  resource = _messages.StringField(4)
  subresource = _messages.StringField(5)
  verb = _messages.StringField(6)
  version = _messages.StringField(7)


class AdaptiveProtection(_messages.Message):
  r"""Information about [Google Cloud Armor Adaptive
  Protection](https://cloud.google.com/armor/docs/cloud-armor-overview#google-
  cloud-armor-adaptive-protection).

  Fields:
    confidence: A score of 0 means that there is low confidence that the
      detected event is an actual attack. A score of 1 means that there is
      high confidence that the detected event is an attack. See the [Adaptive
      Protection documentation](https://cloud.google.com/armor/docs/adaptive-
      protection-overview#configure-alert-tuning) for further explanation.
  """

  confidence = _messages.FloatField(1)


class AffectedResources(_messages.Message):
  r"""Details about resources affected by this finding.

  Fields:
    count: The count of resources affected by the finding.
  """

  count = _messages.IntegerField(1)


class Allowed(_messages.Message):
  r"""Allowed IP rule.

  Fields:
    ipRules: Optional. Optional list of allowed IP rules.
  """

  ipRules = _messages.MessageField('IpRule', 1, repeated=True)


class Application(_messages.Message):
  r"""Represents an application associated with a finding.

  Fields:
    baseUri: The base URI that identifies the network location of the
      application in which the vulnerability was detected. For example,
      `http://example.com`.
    fullUri: The full URI with payload that can be used to reproduce the
      vulnerability. For example, `http://example.com?p=aMmYgI6H`.
  """

  baseUri = _messages.StringField(1)
  fullUri = _messages.StringField(2)


class Attack(_messages.Message):
  r"""Information about DDoS attack volume and classification.

  Fields:
    classification: Type of attack, for example, 'SYN-flood', 'NTP-udp', or
      'CHARGEN-udp'.
    volumeBps: Total BPS (bytes per second) volume of attack. Deprecated -
      refer to volume_bps_long instead.
    volumeBpsLong: Total BPS (bytes per second) volume of attack.
    volumePps: Total PPS (packets per second) volume of attack. Deprecated -
      refer to volume_pps_long instead.
    volumePpsLong: Total PPS (packets per second) volume of attack.
  """

  classification = _messages.StringField(1)
  volumeBps = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  volumeBpsLong = _messages.IntegerField(3)
  volumePps = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  volumePpsLong = _messages.IntegerField(5)


class AttackExposure(_messages.Message):
  r"""An attack exposure contains the results of an attack path simulation
  run.

  Enums:
    StateValueValuesEnum: What state this AttackExposure is in. This captures
      whether or not an attack exposure has been calculated or not.

  Fields:
    attackExposureResult: The resource name of the attack path simulation
      result that contains the details regarding this attack exposure score.
      Example: `organizations/123/simulations/456/attackExposureResults/789`
    exposedHighValueResourcesCount: The number of high value resources that
      are exposed as a result of this finding.
    exposedLowValueResourcesCount: The number of high value resources that are
      exposed as a result of this finding.
    exposedMediumValueResourcesCount: The number of medium value resources
      that are exposed as a result of this finding.
    latestCalculationTime: The most recent time the attack exposure was
      updated on this finding.
    score: A number between 0 (inclusive) and infinity that represents how
      important this finding is to remediate. The higher the score, the more
      important it is to remediate.
    state: What state this AttackExposure is in. This captures whether or not
      an attack exposure has been calculated or not.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""What state this AttackExposure is in. This captures whether or not an
    attack exposure has been calculated or not.

    Values:
      STATE_UNSPECIFIED: The state is not specified.
      CALCULATED: The attack exposure has been calculated.
      NOT_CALCULATED: The attack exposure has not been calculated.
    """
    STATE_UNSPECIFIED = 0
    CALCULATED = 1
    NOT_CALCULATED = 2

  attackExposureResult = _messages.StringField(1)
  exposedHighValueResourcesCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  exposedLowValueResourcesCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  exposedMediumValueResourcesCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  latestCalculationTime = _messages.StringField(5)
  score = _messages.FloatField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)


class AwsAccount(_messages.Message):
  r"""An AWS account that is a member of an organization.

  Fields:
    id: The unique identifier (ID) of the account, containing exactly 12
      digits.
    name: The friendly name of this account.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2)


class AwsMetadata(_messages.Message):
  r"""AWS metadata associated with the resource, only applicable if the
  finding's cloud provider is Amazon Web Services.

  Fields:
    account: The AWS account associated with the resource.
    organization: The AWS organization associated with the resource.
    organizationalUnits: A list of AWS organizational units associated with
      the resource, ordered from lowest level (closest to the account) to
      highest level.
  """

  account = _messages.MessageField('AwsAccount', 1)
  organization = _messages.MessageField('AwsOrganization', 2)
  organizationalUnits = _messages.MessageField('AwsOrganizationalUnit', 3, repeated=True)


class AwsOrganization(_messages.Message):
  r"""An organization is a collection of accounts that are centrally managed
  together using consolidated billing, organized hierarchically with
  organizational units (OUs), and controlled with policies.

  Fields:
    id: The unique identifier (ID) for the organization. The regex pattern for
      an organization ID string requires "o-" followed by from 10 to 32
      lowercase letters or digits.
  """

  id = _messages.StringField(1)


class AwsOrganizationalUnit(_messages.Message):
  r"""An Organizational Unit (OU) is a container of AWS accounts within a root
  of an organization. Policies that are attached to an OU apply to all
  accounts contained in that OU and in any child OUs.

  Fields:
    id: The unique identifier (ID) associated with this OU. The regex pattern
      for an organizational unit ID string requires "ou-" followed by from 4
      to 32 lowercase letters or digits (the ID of the root that contains the
      OU). This string is followed by a second "-" dash and from 8 to 32
      additional lowercase letters or digits. For example, "ou-ab12-cd34ef56".
    name: The friendly name of the OU.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2)


class AzureManagementGroup(_messages.Message):
  r"""Represents an Azure management group.

  Fields:
    displayName: The display name of the Azure management group.
    id: The UUID of the Azure management group, for example,
      `********-0001-0000-0000-************`.
  """

  displayName = _messages.StringField(1)
  id = _messages.StringField(2)


class AzureMetadata(_messages.Message):
  r"""Azure metadata associated with the resource, only applicable if the
  finding's cloud provider is Microsoft Azure.

  Fields:
    managementGroups: A list of Azure management groups associated with the
      resource, ordered from lowest level (closest to the subscription) to
      highest level.
    resourceGroup: The Azure resource group associated with the resource.
    subscription: The Azure subscription associated with the resource.
    tenant: The Azure Entra tenant associated with the resource.
  """

  managementGroups = _messages.MessageField('AzureManagementGroup', 1, repeated=True)
  resourceGroup = _messages.MessageField('AzureResourceGroup', 2)
  subscription = _messages.MessageField('AzureSubscription', 3)
  tenant = _messages.MessageField('AzureTenant', 4)


class AzureResourceGroup(_messages.Message):
  r"""Represents an Azure resource group.

  Fields:
    id: The ID of the Azure resource group.
    name: The name of the Azure resource group. This is not a UUID.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2)


class AzureSubscription(_messages.Message):
  r"""Represents an Azure subscription.

  Fields:
    displayName: The display name of the Azure subscription.
    id: The UUID of the Azure subscription, for example,
      `291bba3f-e0a5-47bc-a099-3bdcb2a50a05`.
  """

  displayName = _messages.StringField(1)
  id = _messages.StringField(2)


class AzureTenant(_messages.Message):
  r"""Represents a Microsoft Entra tenant.

  Fields:
    displayName: The display name of the Azure tenant.
    id: The ID of the Microsoft Entra tenant, for example,
      "a11aaa11-aa11-1aa1-11aa-1aaa11a".
  """

  displayName = _messages.StringField(1)
  id = _messages.StringField(2)


class BackupDisasterRecovery(_messages.Message):
  r"""Information related to Google Cloud Backup and DR Service findings.

  Fields:
    appliance: The name of the Backup and DR appliance that captures, moves,
      and manages the lifecycle of backup data. For example, `backup-
      server-57137`.
    applications: The names of Backup and DR applications. An application is a
      VM, database, or file system on a managed host monitored by a backup and
      recovery appliance. For example, `centos7-01-vol00`, `centos7-01-vol01`,
      `centos7-01-vol02`.
    backupCreateTime: The timestamp at which the Backup and DR backup was
      created.
    backupTemplate: The name of a Backup and DR template which comprises one
      or more backup policies. See the [Backup and DR
      documentation](https://cloud.google.com/backup-disaster-
      recovery/docs/concepts/backup-plan#temp) for more information. For
      example, `snap-ov`.
    backupType: The backup type of the Backup and DR image. For example,
      `Snapshot`, `Remote Snapshot`, `OnVault`.
    host: The name of a Backup and DR host, which is managed by the backup and
      recovery appliance and known to the management console. The host can be
      of type Generic (for example, Compute Engine, SQL Server, Oracle DB, SMB
      file system, etc.), vCenter, or an ESX server. See the [Backup and DR
      documentation on hosts](https://cloud.google.com/backup-disaster-
      recovery/docs/configuration/manage-hosts-and-their-applications) for
      more information. For example, `centos7-01`.
    policies: The names of Backup and DR policies that are associated with a
      template and that define when to run a backup, how frequently to run a
      backup, and how long to retain the backup image. For example,
      `onvaults`.
    policyOptions: The names of Backup and DR advanced policy options of a
      policy applying to an application. See the [Backup and DR documentation
      on policy options](https://cloud.google.com/backup-disaster-
      recovery/docs/create-plan/policy-settings). For example,
      `skipofflineappsincongrp, nounmap`.
    profile: The name of the Backup and DR resource profile that specifies the
      storage media for backups of application and VM data. See the [Backup
      and DR documentation on profiles](https://cloud.google.com/backup-
      disaster-recovery/docs/concepts/backup-plan#profile). For example,
      `GCP`.
    storagePool: The name of the Backup and DR storage pool that the backup
      and recovery appliance is storing data in. The storage pool could be of
      type Cloud, Primary, Snapshot, or OnVault. See the [Backup and DR
      documentation on storage pools](https://cloud.google.com/backup-
      disaster-recovery/docs/concepts/storage-pools). For example,
      `DiskPoolOne`.
  """

  appliance = _messages.StringField(1)
  applications = _messages.StringField(2, repeated=True)
  backupCreateTime = _messages.StringField(3)
  backupTemplate = _messages.StringField(4)
  backupType = _messages.StringField(5)
  host = _messages.StringField(6)
  policies = _messages.StringField(7, repeated=True)
  policyOptions = _messages.StringField(8, repeated=True)
  profile = _messages.StringField(9)
  storagePool = _messages.StringField(10)


class Chokepoint(_messages.Message):
  r"""Contains details about a chokepoint, which is a resource or resource
  group where high-risk attack paths converge, based on [attack path
  simulations] (https://cloud.google.com/security-command-center/docs/attack-
  exposure-learn#attack_path_simulations).

  Fields:
    relatedFindings: List of resource names of findings associated with this
      chokepoint. For example, organizations/123/sources/456/findings/789.
      This list will have at most 100 findings.
  """

  relatedFindings = _messages.StringField(1, repeated=True)


class CloudArmor(_messages.Message):
  r"""Fields related to Google Cloud Armor findings.

  Fields:
    adaptiveProtection: Information about potential Layer 7 DDoS attacks
      identified by [Google Cloud Armor Adaptive
      Protection](https://cloud.google.com/armor/docs/adaptive-protection-
      overview).
    attack: Information about DDoS attack volume and classification.
    duration: Duration of attack from the start until the current moment
      (updated every 5 minutes).
    requests: Information about incoming requests evaluated by [Google Cloud
      Armor security policies](https://cloud.google.com/armor/docs/security-
      policy-overview).
    securityPolicy: Information about the [Google Cloud Armor security
      policy](https://cloud.google.com/armor/docs/security-policy-overview)
      relevant to the finding.
    threatVector: Distinguish between volumetric & protocol DDoS attack and
      application layer attacks. For example, "L3_4" for Layer 3 and Layer 4
      DDoS attacks, or "L_7" for Layer 7 DDoS attacks.
  """

  adaptiveProtection = _messages.MessageField('AdaptiveProtection', 1)
  attack = _messages.MessageField('Attack', 2)
  duration = _messages.StringField(3)
  requests = _messages.MessageField('Requests', 4)
  securityPolicy = _messages.MessageField('SecurityPolicy', 5)
  threatVector = _messages.StringField(6)


class CloudDlpDataProfile(_messages.Message):
  r"""The [data profile](https://cloud.google.com/dlp/docs/data-profiles)
  associated with the finding.

  Enums:
    ParentTypeValueValuesEnum: The resource hierarchy level at which the data
      profile was generated.

  Fields:
    dataProfile: Name of the data profile, for example,
      `projects/123/locations/europe/tableProfiles/8383929`.
    parentType: The resource hierarchy level at which the data profile was
      generated.
  """

  class ParentTypeValueValuesEnum(_messages.Enum):
    r"""The resource hierarchy level at which the data profile was generated.

    Values:
      PARENT_TYPE_UNSPECIFIED: Unspecified parent type.
      ORGANIZATION: Organization-level configurations.
      PROJECT: Project-level configurations.
    """
    PARENT_TYPE_UNSPECIFIED = 0
    ORGANIZATION = 1
    PROJECT = 2

  dataProfile = _messages.StringField(1)
  parentType = _messages.EnumField('ParentTypeValueValuesEnum', 2)


class CloudDlpInspection(_messages.Message):
  r"""Details about the Cloud Data Loss Prevention (Cloud DLP) [inspection
  job](https://cloud.google.com/dlp/docs/concepts-job-triggers) that produced
  the finding.

  Fields:
    fullScan: Whether Cloud DLP scanned the complete resource or a sampled
      subset.
    infoType: The type of information (or
      *[infoType](https://cloud.google.com/dlp/docs/infotypes-reference)*)
      found, for example, `EMAIL_ADDRESS` or `STREET_ADDRESS`.
    infoTypeCount: The number of times Cloud DLP found this infoType within
      this job and resource.
    inspectJob: Name of the inspection job, for example,
      `projects/123/locations/europe/dlpJobs/i-8383929`.
  """

  fullScan = _messages.BooleanField(1)
  infoType = _messages.StringField(2)
  infoTypeCount = _messages.IntegerField(3)
  inspectJob = _messages.StringField(4)


class CloudLoggingEntry(_messages.Message):
  r"""Metadata taken from a [Cloud Logging LogEntry](https://cloud.google.com/
  logging/docs/reference/v2/rest/v2/LogEntry)

  Fields:
    insertId: A unique identifier for the log entry.
    logId: The type of the log (part of `log_name`. `log_name` is the resource
      name of the log to which this log entry belongs). For example:
      `cloudresourcemanager.googleapis.com/activity`. Note that this field is
      not URL-encoded, unlike the `LOG_ID` field in `LogEntry`.
    resourceContainer: The organization, folder, or project of the monitored
      resource that produced this log entry.
    timestamp: The time the event described by the log entry occurred.
  """

  insertId = _messages.StringField(1)
  logId = _messages.StringField(2)
  resourceContainer = _messages.StringField(3)
  timestamp = _messages.StringField(4)


class Compliance(_messages.Message):
  r"""Contains compliance information about a security standard indicating
  unmet recommendations.

  Fields:
    ids: Policies within the standard or benchmark, for example, A.12.4.1
    standard: Industry-wide compliance standards or benchmarks, such as CIS,
      PCI, and OWASP.
    version: Version of the standard or benchmark, for example, 1.1
  """

  ids = _messages.StringField(1, repeated=True)
  standard = _messages.StringField(2)
  version = _messages.StringField(3)


class Config(_messages.Message):
  r"""Configuration of a module.

  Enums:
    ModuleEnablementStateValueValuesEnum: The state of enablement for the
      module at its level of the resource hierarchy.

  Messages:
    ValueValue: The configuration value for the module. The absence of this
      field implies its inheritance from the parent.

  Fields:
    moduleEnablementState: The state of enablement for the module at its level
      of the resource hierarchy.
    value: The configuration value for the module. The absence of this field
      implies its inheritance from the parent.
  """

  class ModuleEnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the module at its level of the resource
    hierarchy.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ValueValue(_messages.Message):
    r"""The configuration value for the module. The absence of this field
    implies its inheritance from the parent.

    Messages:
      AdditionalProperty: An additional property for a ValueValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ValueValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  moduleEnablementState = _messages.EnumField('ModuleEnablementStateValueValuesEnum', 1)
  value = _messages.MessageField('ValueValue', 2)


class Connection(_messages.Message):
  r"""Contains information about the IP connection associated with the
  finding.

  Enums:
    ProtocolValueValuesEnum: IANA Internet Protocol Number such as TCP(6) and
      UDP(17).

  Fields:
    destinationIp: Destination IP address. Not present for sockets that are
      listening and not connected.
    destinationPort: Destination port. Not present for sockets that are
      listening and not connected.
    protocol: IANA Internet Protocol Number such as TCP(6) and UDP(17).
    sourceIp: Source IP address.
    sourcePort: Source port.
  """

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""IANA Internet Protocol Number such as TCP(6) and UDP(17).

    Values:
      PROTOCOL_UNSPECIFIED: Unspecified protocol (not HOPOPT).
      ICMP: Internet Control Message Protocol.
      TCP: Transmission Control Protocol.
      UDP: User Datagram Protocol.
      GRE: Generic Routing Encapsulation.
      ESP: Encap Security Payload.
    """
    PROTOCOL_UNSPECIFIED = 0
    ICMP = 1
    TCP = 2
    UDP = 3
    GRE = 4
    ESP = 5

  destinationIp = _messages.StringField(1)
  destinationPort = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 3)
  sourceIp = _messages.StringField(4)
  sourcePort = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class Contact(_messages.Message):
  r"""The email address of a contact.

  Fields:
    email: An email address. For example, "`<EMAIL>`".
  """

  email = _messages.StringField(1)


class ContactDetails(_messages.Message):
  r"""Details about specific contacts

  Fields:
    contacts: A list of contacts
  """

  contacts = _messages.MessageField('Contact', 1, repeated=True)


class Container(_messages.Message):
  r"""Container associated with the finding.

  Fields:
    createTime: The time that the container was created.
    imageId: Optional container image ID, if provided by the container
      runtime. Uniquely identifies the container image launched using a
      container image digest.
    labels: Container labels, as provided by the container runtime.
    name: Name of the container.
    uri: Container image URI provided when configuring a pod or container.
      This string can identify a container image version using mutable tags.
  """

  createTime = _messages.StringField(1)
  imageId = _messages.StringField(2)
  labels = _messages.MessageField('Label', 3, repeated=True)
  name = _messages.StringField(4)
  uri = _messages.StringField(5)


class ContainerThreatDetectionSettings(_messages.Message):
  r"""Resource capturing the settings for the Container Threat Detection
  service.

  Enums:
    ServiceEnablementStateValueValuesEnum: The state of enablement for the
      service at its level of the resource hierarchy. A DISABLED state will
      override all module enablement_states to DISABLED.

  Messages:
    ModulesValue: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's configuration.

  Fields:
    modules: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's configuration.
    name: Identifier. The resource name of the
      ContainerThreatDetectionSettings. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
    serviceAccount: Output only. The service account used by Container Threat
      Detection for scanning. Service accounts are scoped at the project level
      meaning this field will be empty at any level above a project.
    serviceEnablementState: The state of enablement for the service at its
      level of the resource hierarchy. A DISABLED state will override all
      module enablement_states to DISABLED.
    updateTime: Output only. The time the settings were last updated.
  """

  class ServiceEnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the service at its level of the resource
    hierarchy. A DISABLED state will override all module enablement_states to
    DISABLED.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModulesValue(_messages.Message):
    r"""The configurations including the state of enablement for the service's
    different modules. The absence of a module in the map implies its
    configuration is inherited from its parent's configuration.

    Messages:
      AdditionalProperty: An additional property for a ModulesValue object.

    Fields:
      additionalProperties: Additional properties of type ModulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModulesValue object.

      Fields:
        key: Name of the additional property.
        value: A Config attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Config', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  modules = _messages.MessageField('ModulesValue', 1)
  name = _messages.StringField(2)
  serviceAccount = _messages.StringField(3)
  serviceEnablementState = _messages.EnumField('ServiceEnablementStateValueValuesEnum', 4)
  updateTime = _messages.StringField(5)


class Cve(_messages.Message):
  r"""CVE stands for Common Vulnerabilities and Exposures. Information from
  the [CVE record](https://www.cve.org/ResourcesSupport/Glossary) that
  describes this vulnerability.

  Enums:
    ExploitationActivityValueValuesEnum: The exploitation activity of the
      vulnerability in the wild.
    ImpactValueValuesEnum: The potential impact of the vulnerability if it was
      to be exploited.

  Fields:
    cvssv3: Describe Common Vulnerability Scoring System specified at
      https://www.first.org/cvss/v3.1/specification-document
    exploitReleaseDate: Date the first publicly available exploit or PoC was
      released.
    exploitationActivity: The exploitation activity of the vulnerability in
      the wild.
    firstExploitationDate: Date of the earliest known exploitation.
    id: The unique identifier for the vulnerability. e.g. CVE-2021-34527
    impact: The potential impact of the vulnerability if it was to be
      exploited.
    observedInTheWild: Whether or not the vulnerability has been observed in
      the wild.
    references: Additional information about the CVE. e.g.
      https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-34527
    upstreamFixAvailable: Whether upstream fix is available for the CVE.
    zeroDay: Whether or not the vulnerability was zero day when the finding
      was published.
  """

  class ExploitationActivityValueValuesEnum(_messages.Enum):
    r"""The exploitation activity of the vulnerability in the wild.

    Values:
      EXPLOITATION_ACTIVITY_UNSPECIFIED: Invalid or empty value.
      WIDE: Exploitation has been reported or confirmed to widely occur.
      CONFIRMED: Limited reported or confirmed exploitation activities.
      AVAILABLE: Exploit is publicly available.
      ANTICIPATED: No known exploitation activity, but has a high potential
        for exploitation.
      NO_KNOWN: No known exploitation activity.
    """
    EXPLOITATION_ACTIVITY_UNSPECIFIED = 0
    WIDE = 1
    CONFIRMED = 2
    AVAILABLE = 3
    ANTICIPATED = 4
    NO_KNOWN = 5

  class ImpactValueValuesEnum(_messages.Enum):
    r"""The potential impact of the vulnerability if it was to be exploited.

    Values:
      RISK_RATING_UNSPECIFIED: Invalid or empty value.
      LOW: Exploitation would have little to no security impact.
      MEDIUM: Exploitation would enable attackers to perform activities, or
        could allow attackers to have a direct impact, but would require
        additional steps.
      HIGH: Exploitation would enable attackers to have a notable direct
        impact without needing to overcome any major mitigating factors.
      CRITICAL: Exploitation would fundamentally undermine the security of
        affected systems, enable actors to perform significant attacks with
        minimal effort, with little to no mitigating factors to overcome.
    """
    RISK_RATING_UNSPECIFIED = 0
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

  cvssv3 = _messages.MessageField('Cvssv3', 1)
  exploitReleaseDate = _messages.StringField(2)
  exploitationActivity = _messages.EnumField('ExploitationActivityValueValuesEnum', 3)
  firstExploitationDate = _messages.StringField(4)
  id = _messages.StringField(5)
  impact = _messages.EnumField('ImpactValueValuesEnum', 6)
  observedInTheWild = _messages.BooleanField(7)
  references = _messages.MessageField('Reference', 8, repeated=True)
  upstreamFixAvailable = _messages.BooleanField(9)
  zeroDay = _messages.BooleanField(10)


class Cvssv3(_messages.Message):
  r"""Common Vulnerability Scoring System version 3.

  Enums:
    AttackComplexityValueValuesEnum: This metric describes the conditions
      beyond the attacker's control that must exist in order to exploit the
      vulnerability.
    AttackVectorValueValuesEnum: Base Metrics Represents the intrinsic
      characteristics of a vulnerability that are constant over time and
      across user environments. This metric reflects the context by which
      vulnerability exploitation is possible.
    AvailabilityImpactValueValuesEnum: This metric measures the impact to the
      availability of the impacted component resulting from a successfully
      exploited vulnerability.
    ConfidentialityImpactValueValuesEnum: This metric measures the impact to
      the confidentiality of the information resources managed by a software
      component due to a successfully exploited vulnerability.
    IntegrityImpactValueValuesEnum: This metric measures the impact to
      integrity of a successfully exploited vulnerability.
    PrivilegesRequiredValueValuesEnum: This metric describes the level of
      privileges an attacker must possess before successfully exploiting the
      vulnerability.
    ScopeValueValuesEnum: The Scope metric captures whether a vulnerability in
      one vulnerable component impacts resources in components beyond its
      security scope.
    UserInteractionValueValuesEnum: This metric captures the requirement for a
      human user, other than the attacker, to participate in the successful
      compromise of the vulnerable component.

  Fields:
    attackComplexity: This metric describes the conditions beyond the
      attacker's control that must exist in order to exploit the
      vulnerability.
    attackVector: Base Metrics Represents the intrinsic characteristics of a
      vulnerability that are constant over time and across user environments.
      This metric reflects the context by which vulnerability exploitation is
      possible.
    availabilityImpact: This metric measures the impact to the availability of
      the impacted component resulting from a successfully exploited
      vulnerability.
    baseScore: The base score is a function of the base metric scores.
    confidentialityImpact: This metric measures the impact to the
      confidentiality of the information resources managed by a software
      component due to a successfully exploited vulnerability.
    integrityImpact: This metric measures the impact to integrity of a
      successfully exploited vulnerability.
    privilegesRequired: This metric describes the level of privileges an
      attacker must possess before successfully exploiting the vulnerability.
    scope: The Scope metric captures whether a vulnerability in one vulnerable
      component impacts resources in components beyond its security scope.
    userInteraction: This metric captures the requirement for a human user,
      other than the attacker, to participate in the successful compromise of
      the vulnerable component.
  """

  class AttackComplexityValueValuesEnum(_messages.Enum):
    r"""This metric describes the conditions beyond the attacker's control
    that must exist in order to exploit the vulnerability.

    Values:
      ATTACK_COMPLEXITY_UNSPECIFIED: Invalid value.
      ATTACK_COMPLEXITY_LOW: Specialized access conditions or extenuating
        circumstances do not exist. An attacker can expect repeatable success
        when attacking the vulnerable component.
      ATTACK_COMPLEXITY_HIGH: A successful attack depends on conditions beyond
        the attacker's control. That is, a successful attack cannot be
        accomplished at will, but requires the attacker to invest in some
        measurable amount of effort in preparation or execution against the
        vulnerable component before a successful attack can be expected.
    """
    ATTACK_COMPLEXITY_UNSPECIFIED = 0
    ATTACK_COMPLEXITY_LOW = 1
    ATTACK_COMPLEXITY_HIGH = 2

  class AttackVectorValueValuesEnum(_messages.Enum):
    r"""Base Metrics Represents the intrinsic characteristics of a
    vulnerability that are constant over time and across user environments.
    This metric reflects the context by which vulnerability exploitation is
    possible.

    Values:
      ATTACK_VECTOR_UNSPECIFIED: Invalid value.
      ATTACK_VECTOR_NETWORK: The vulnerable component is bound to the network
        stack and the set of possible attackers extends beyond the other
        options listed below, up to and including the entire Internet.
      ATTACK_VECTOR_ADJACENT: The vulnerable component is bound to the network
        stack, but the attack is limited at the protocol level to a logically
        adjacent topology.
      ATTACK_VECTOR_LOCAL: The vulnerable component is not bound to the
        network stack and the attacker's path is via read/write/execute
        capabilities.
      ATTACK_VECTOR_PHYSICAL: The attack requires the attacker to physically
        touch or manipulate the vulnerable component.
    """
    ATTACK_VECTOR_UNSPECIFIED = 0
    ATTACK_VECTOR_NETWORK = 1
    ATTACK_VECTOR_ADJACENT = 2
    ATTACK_VECTOR_LOCAL = 3
    ATTACK_VECTOR_PHYSICAL = 4

  class AvailabilityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to the availability of the impacted
    component resulting from a successfully exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class ConfidentialityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to the confidentiality of the
    information resources managed by a software component due to a
    successfully exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class IntegrityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to integrity of a successfully
    exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class PrivilegesRequiredValueValuesEnum(_messages.Enum):
    r"""This metric describes the level of privileges an attacker must possess
    before successfully exploiting the vulnerability.

    Values:
      PRIVILEGES_REQUIRED_UNSPECIFIED: Invalid value.
      PRIVILEGES_REQUIRED_NONE: The attacker is unauthorized prior to attack,
        and therefore does not require any access to settings or files of the
        vulnerable system to carry out an attack.
      PRIVILEGES_REQUIRED_LOW: The attacker requires privileges that provide
        basic user capabilities that could normally affect only settings and
        files owned by a user. Alternatively, an attacker with Low privileges
        has the ability to access only non-sensitive resources.
      PRIVILEGES_REQUIRED_HIGH: The attacker requires privileges that provide
        significant (e.g., administrative) control over the vulnerable
        component allowing access to component-wide settings and files.
    """
    PRIVILEGES_REQUIRED_UNSPECIFIED = 0
    PRIVILEGES_REQUIRED_NONE = 1
    PRIVILEGES_REQUIRED_LOW = 2
    PRIVILEGES_REQUIRED_HIGH = 3

  class ScopeValueValuesEnum(_messages.Enum):
    r"""The Scope metric captures whether a vulnerability in one vulnerable
    component impacts resources in components beyond its security scope.

    Values:
      SCOPE_UNSPECIFIED: Invalid value.
      SCOPE_UNCHANGED: An exploited vulnerability can only affect resources
        managed by the same security authority.
      SCOPE_CHANGED: An exploited vulnerability can affect resources beyond
        the security scope managed by the security authority of the vulnerable
        component.
    """
    SCOPE_UNSPECIFIED = 0
    SCOPE_UNCHANGED = 1
    SCOPE_CHANGED = 2

  class UserInteractionValueValuesEnum(_messages.Enum):
    r"""This metric captures the requirement for a human user, other than the
    attacker, to participate in the successful compromise of the vulnerable
    component.

    Values:
      USER_INTERACTION_UNSPECIFIED: Invalid value.
      USER_INTERACTION_NONE: The vulnerable system can be exploited without
        interaction from any user.
      USER_INTERACTION_REQUIRED: Successful exploitation of this vulnerability
        requires a user to take some action before the vulnerability can be
        exploited.
    """
    USER_INTERACTION_UNSPECIFIED = 0
    USER_INTERACTION_NONE = 1
    USER_INTERACTION_REQUIRED = 2

  attackComplexity = _messages.EnumField('AttackComplexityValueValuesEnum', 1)
  attackVector = _messages.EnumField('AttackVectorValueValuesEnum', 2)
  availabilityImpact = _messages.EnumField('AvailabilityImpactValueValuesEnum', 3)
  baseScore = _messages.FloatField(4)
  confidentialityImpact = _messages.EnumField('ConfidentialityImpactValueValuesEnum', 5)
  integrityImpact = _messages.EnumField('IntegrityImpactValueValuesEnum', 6)
  privilegesRequired = _messages.EnumField('PrivilegesRequiredValueValuesEnum', 7)
  scope = _messages.EnumField('ScopeValueValuesEnum', 8)
  userInteraction = _messages.EnumField('UserInteractionValueValuesEnum', 9)


class Cwe(_messages.Message):
  r"""CWE stands for Common Weakness Enumeration. Information about this
  weakness, as described by [CWE](https://cwe.mitre.org/).

  Fields:
    id: The CWE identifier, e.g. CWE-94
    references: Any reference to the details on the CWE, for example,
      https://cwe.mitre.org/data/definitions/94.html
  """

  id = _messages.StringField(1)
  references = _messages.MessageField('Reference', 2, repeated=True)


class DataAccessEvent(_messages.Message):
  r"""Details about a data access attempt made by a principal not authorized
  under applicable data security policy.

  Enums:
    OperationValueValuesEnum: The operation performed by the principal to
      access the data.

  Fields:
    eventId: Unique identifier for data access event.
    eventTime: Timestamp of data access event.
    operation: The operation performed by the principal to access the data.
    principalEmail: The email address of the principal that accessed the data.
      The principal could be a user account, service account, Google group, or
      other.
  """

  class OperationValueValuesEnum(_messages.Enum):
    r"""The operation performed by the principal to access the data.

    Values:
      OPERATION_UNSPECIFIED: The operation is unspecified.
      READ: Represents a read operation.
      MOVE: Represents a move operation.
      COPY: Represents a copy operation.
    """
    OPERATION_UNSPECIFIED = 0
    READ = 1
    MOVE = 2
    COPY = 3

  eventId = _messages.StringField(1)
  eventTime = _messages.StringField(2)
  operation = _messages.EnumField('OperationValueValuesEnum', 3)
  principalEmail = _messages.StringField(4)


class DataFlowEvent(_messages.Message):
  r"""Details about a data flow event, in which either the data is moved to or
  is accessed from a non-compliant geo-location, as defined in the applicable
  data security policy.

  Enums:
    OperationValueValuesEnum: The operation performed by the principal for the
      data flow event.

  Fields:
    eventId: Unique identifier for data flow event.
    eventTime: Timestamp of data flow event.
    operation: The operation performed by the principal for the data flow
      event.
    principalEmail: The email address of the principal that initiated the data
      flow event. The principal could be a user account, service account,
      Google group, or other.
    violatedLocation: Non-compliant location of the principal or the data
      destination.
  """

  class OperationValueValuesEnum(_messages.Enum):
    r"""The operation performed by the principal for the data flow event.

    Values:
      OPERATION_UNSPECIFIED: The operation is unspecified.
      READ: Represents a read operation.
      MOVE: Represents a move operation.
      COPY: Represents a copy operation.
    """
    OPERATION_UNSPECIFIED = 0
    READ = 1
    MOVE = 2
    COPY = 3

  eventId = _messages.StringField(1)
  eventTime = _messages.StringField(2)
  operation = _messages.EnumField('OperationValueValuesEnum', 3)
  principalEmail = _messages.StringField(4)
  violatedLocation = _messages.StringField(5)


class DataRetentionDeletionEvent(_messages.Message):
  r"""Details about data retention deletion violations, in which the data is
  non-compliant based on their retention or deletion time, as defined in the
  applicable data security policy. The Data Retention Deletion (DRD) control
  is a control of the DSPM (Data Security Posture Management) suite that
  enables organizations to manage data retention and deletion policies in
  compliance with regulations, such as GDPR and CRPA. DRD supports two primary
  policy types: maximum storage length (max TTL) and minimum storage length
  (min TTL). Both are aimed at helping organizations meet regulatory and data
  management commitments.

  Enums:
    EventTypeValueValuesEnum: Type of the DRD event.

  Fields:
    dataObjectCount: Number of objects that violated the policy for this
      resource. If the number is less than 1,000, then the value of this field
      is the exact number. If the number of objects that violated the policy
      is greater than or equal to 1,000, then the value of this field is 1000.
    eventDetectionTime: Timestamp indicating when the event was detected.
    eventType: Type of the DRD event.
    maxRetentionAllowed: Maximum duration of retention allowed from the DRD
      control. This comes from the DRD control where users set a max TTL for
      their data. For example, suppose that a user sets the max TTL for a
      Cloud Storage bucket to 90 days. However, an object in that bucket is
      100 days old. In this case, a DataRetentionDeletionEvent will be
      generated for that Cloud Storage bucket, and the max_retention_allowed
      is 90 days.
  """

  class EventTypeValueValuesEnum(_messages.Enum):
    r"""Type of the DRD event.

    Values:
      EVENT_TYPE_UNSPECIFIED: Unspecified event type.
      EVENT_TYPE_MAX_TTL_EXCEEDED: The maximum retention time has been
        exceeded.
    """
    EVENT_TYPE_UNSPECIFIED = 0
    EVENT_TYPE_MAX_TTL_EXCEEDED = 1

  dataObjectCount = _messages.IntegerField(1)
  eventDetectionTime = _messages.StringField(2)
  eventType = _messages.EnumField('EventTypeValueValuesEnum', 3)
  maxRetentionAllowed = _messages.StringField(4)


class Database(_messages.Message):
  r"""Represents database access information, such as queries. A database may
  be a sub-resource of an instance (as in the case of Cloud SQL instances or
  Cloud Spanner instances), or the database instance itself. Some database
  resources might not have the [full resource
  name](https://google.aip.dev/122#full-resource-names) populated because
  these resource types, such as Cloud SQL databases, are not yet supported by
  Cloud Asset Inventory. In these cases only the display name is provided.

  Fields:
    displayName: The human-readable name of the database that the user
      connected to.
    grantees: The target usernames, roles, or groups of an SQL privilege
      grant, which is not an IAM policy change.
    name: Some database resources may not have the [full resource
      name](https://google.aip.dev/122#full-resource-names) populated because
      these resource types are not yet supported by Cloud Asset Inventory
      (e.g. Cloud SQL databases). In these cases only the display name will be
      provided. The [full resource name](https://google.aip.dev/122#full-
      resource-names) of the database that the user connected to, if it is
      supported by Cloud Asset Inventory.
    query: The SQL statement that is associated with the database access.
    userName: The username used to connect to the database. The username might
      not be an IAM principal and does not have a set format.
    version: The version of the database, for example, POSTGRES_14. See [the
      complete list](https://cloud.google.com/sql/docs/mysql/admin-
      api/rest/v1/SqlDatabaseVersion).
  """

  displayName = _messages.StringField(1)
  grantees = _messages.StringField(2, repeated=True)
  name = _messages.StringField(3)
  query = _messages.StringField(4)
  userName = _messages.StringField(5)
  version = _messages.StringField(6)


class Denied(_messages.Message):
  r"""Denied IP rule.

  Fields:
    ipRules: Optional. Optional list of denied IP rules.
  """

  ipRules = _messages.MessageField('IpRule', 1, repeated=True)


class Details(_messages.Message):
  r"""Details of a subscription.

  Enums:
    TypeValueValuesEnum: The type of subscription

  Fields:
    endTime: The time the subscription has or will end.
    startTime: The time the subscription has or will start.
    type: The type of subscription
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of subscription

    Values:
      TYPE_UNSPECIFIED: Default value. This value is unused.
      STANDARD: The standard subscription.
      TRIAL: The trial subscription.
      ALPHA: The alpha subscription.
      DEMO: The demo subscription for channel partners.
      PAY_AS_YOU_GO: Pay-as-you-go subscription.
    """
    TYPE_UNSPECIFIED = 0
    STANDARD = 1
    TRIAL = 2
    ALPHA = 3
    DEMO = 4
    PAY_AS_YOU_GO = 5

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class Detection(_messages.Message):
  r"""Memory hash detection contributing to the binary family match.

  Fields:
    binary: The name of the binary associated with the memory hash signature
      detection.
    percentPagesMatched: The percentage of memory page hashes in the signature
      that were matched.
  """

  binary = _messages.StringField(1)
  percentPagesMatched = _messages.FloatField(2)


class Disk(_messages.Message):
  r"""Contains information about the disk associated with the finding.

  Fields:
    name: The name of the disk, for example,
      "https://www.googleapis.com/compute/v1/projects/{project-
      id}/zones/{zone-id}/disks/{disk-id}".
  """

  name = _messages.StringField(1)


class DiskPath(_messages.Message):
  r"""Path of the file in terms of underlying disk/partition identifiers.

  Fields:
    partitionUuid: UUID of the partition (format
      https://wiki.archlinux.org/title/persistent_block_device_naming#by-uuid)
    relativePath: Relative path of the file in the partition as a JSON encoded
      string. Example: /home/<USER>/executable_file.sh
  """

  partitionUuid = _messages.StringField(1)
  relativePath = _messages.StringField(2)


class DynamicMuteRecord(_messages.Message):
  r"""The record of a dynamic mute rule that matches the finding.

  Fields:
    matchTime: When the dynamic mute rule first matched the finding.
    muteConfig: The relative resource name of the mute rule, represented by a
      mute config, that created this record, for example
      `organizations/123/muteConfigs/mymuteconfig` or
      `organizations/123/locations/global/muteConfigs/mymuteconfig`.
  """

  matchTime = _messages.StringField(1)
  muteConfig = _messages.StringField(2)


class EnvironmentVariable(_messages.Message):
  r"""A name-value pair representing an environment variable used in an
  operating system process.

  Fields:
    name: Environment variable name as a JSON encoded string.
    val: Environment variable value as a JSON encoded string.
  """

  name = _messages.StringField(1)
  val = _messages.StringField(2)


class EventThreatDetectionSettings(_messages.Message):
  r"""Resource capturing the settings for the Event Threat Detection service.

  Enums:
    ServiceEnablementStateValueValuesEnum: The state of enablement for the
      service at its level of the resource hierarchy. A DISABLED state will
      override all module enablement_states to DISABLED.

  Messages:
    ModulesValue: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's configuration.

  Fields:
    modules: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's configuration.
    name: Identifier. The resource name of the EventThreatDetectionSettings.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
    serviceEnablementState: The state of enablement for the service at its
      level of the resource hierarchy. A DISABLED state will override all
      module enablement_states to DISABLED.
    updateTime: Output only. The time the settings were last updated.
  """

  class ServiceEnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the service at its level of the resource
    hierarchy. A DISABLED state will override all module enablement_states to
    DISABLED.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModulesValue(_messages.Message):
    r"""The configurations including the state of enablement for the service's
    different modules. The absence of a module in the map implies its
    configuration is inherited from its parent's configuration.

    Messages:
      AdditionalProperty: An additional property for a ModulesValue object.

    Fields:
      additionalProperties: Additional properties of type ModulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModulesValue object.

      Fields:
        key: Name of the additional property.
        value: A Config attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Config', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  modules = _messages.MessageField('ModulesValue', 1)
  name = _messages.StringField(2)
  serviceEnablementState = _messages.EnumField('ServiceEnablementStateValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class ExfilResource(_messages.Message):
  r"""Resource where data was exfiltrated from or exfiltrated to.

  Fields:
    components: Subcomponents of the asset that was exfiltrated, like URIs
      used during exfiltration, table names, databases, and filenames. For
      example, multiple tables might have been exfiltrated from the same Cloud
      SQL instance, or multiple files might have been exfiltrated from the
      same Cloud Storage bucket.
    name: The resource's [full resource name](https://cloud.google.com/apis/de
      sign/resource_names#full_resource_name).
  """

  components = _messages.StringField(1, repeated=True)
  name = _messages.StringField(2)


class Exfiltration(_messages.Message):
  r"""Exfiltration represents a data exfiltration attempt from one or more
  sources to one or more targets. The `sources` attribute lists the sources of
  the exfiltrated data. The `targets` attribute lists the destinations the
  data was copied to.

  Fields:
    sources: If there are multiple sources, then the data is considered
      "joined" between them. For instance, BigQuery can join multiple tables,
      and each table would be considered a source.
    targets: If there are multiple targets, each target would get a complete
      copy of the "joined" source data.
    totalExfiltratedBytes: Total exfiltrated bytes processed for the entire
      job.
  """

  sources = _messages.MessageField('ExfilResource', 1, repeated=True)
  targets = _messages.MessageField('ExfilResource', 2, repeated=True)
  totalExfiltratedBytes = _messages.IntegerField(3)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class File(_messages.Message):
  r"""File information about the related binary/library used by an executable,
  or the script used by a script interpreter

  Fields:
    contents: Prefix of the file contents as a JSON-encoded string.
    diskPath: Path of the file in terms of underlying disk/partition
      identifiers.
    hashedSize: The length in bytes of the file prefix that was hashed. If
      hashed_size == size, any hashes reported represent the entire file.
    operations: Operation(s) performed on a file.
    partiallyHashed: True when the hash covers only a prefix of the file.
    path: Absolute path of the file as a JSON encoded string.
    sha256: SHA256 hash of the first hashed_size bytes of the file encoded as
      a hex string. If hashed_size == size, sha256 represents the SHA256 hash
      of the entire file.
    size: Size of the file in bytes.
  """

  contents = _messages.StringField(1)
  diskPath = _messages.MessageField('DiskPath', 2)
  hashedSize = _messages.IntegerField(3)
  operations = _messages.MessageField('FileOperation', 4, repeated=True)
  partiallyHashed = _messages.BooleanField(5)
  path = _messages.StringField(6)
  sha256 = _messages.StringField(7)
  size = _messages.IntegerField(8)


class FileOperation(_messages.Message):
  r"""Operation(s) performed on a file.

  Enums:
    TypeValueValuesEnum: The type of the operation

  Fields:
    type: The type of the operation
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the operation

    Values:
      OPERATION_TYPE_UNSPECIFIED: The operation is unspecified.
      OPEN: Represents an open operation.
      READ: Represents a read operation.
      RENAME: Represents a rename operation.
      WRITE: Represents a write operation.
      EXECUTE: Represents an execute operation.
    """
    OPERATION_TYPE_UNSPECIFIED = 0
    OPEN = 1
    READ = 2
    RENAME = 3
    WRITE = 4
    EXECUTE = 5

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class Finding(_messages.Message):
  r"""Security Command Center finding. A finding is a record of assessment
  data like security, risk, health, or privacy, that is ingested into Security
  Command Center for presentation, notification, analysis, policy testing, and
  enforcement. For example, a cross-site scripting (XSS) vulnerability in an
  App Engine application is a finding.

  Enums:
    FindingClassValueValuesEnum: The class of the finding.
    MuteValueValuesEnum: Indicates the mute state of a finding (either muted,
      unmuted or undefined). Unlike other attributes of a finding, a finding
      provider shouldn't set the value of mute.
    SeverityValueValuesEnum: The severity of the finding. This field is
      managed by the source that writes the finding.
    StateValueValuesEnum: The state of the finding.

  Messages:
    ContactsValue: Output only. Map containing the points of contact for the
      given finding. The key represents the type of contact, while the value
      contains a list of all the contacts that pertain. Please refer to:
      https://cloud.google.com/resource-manager/docs/managing-notification-
      contacts#notification-categories { "security": { "contacts": [ {
      "email": "<EMAIL>" }, { "email": "<EMAIL>" } ] }
      }
    ExternalSystemsValue: Output only. Third party SIEM/SOAR fields within
      SCC, contains external system information and external system finding
      fields.
    SourcePropertiesValue: Source specific properties. These properties are
      managed by the source that writes the finding. The key names in the
      source_properties map must be between 1 and 255 characters, and must
      start with a letter and contain alphanumeric characters or underscores
      only.

  Fields:
    access: Access details associated with the finding, such as more
      information on the caller, which method was accessed, and from where.
    affectedResources: AffectedResources associated with the finding.
    application: Represents an application associated with the finding.
    attackExposure: The results of an attack path simulation relevant to this
      finding.
    backupDisasterRecovery: Fields related to Backup and DR findings.
    canonicalName: The canonical name of the finding. It's either "organizatio
      ns/{organization_id}/sources/{source_id}/findings/{finding_id}",
      "folders/{folder_id}/sources/{source_id}/findings/{finding_id}" or
      "projects/{project_number}/sources/{source_id}/findings/{finding_id}",
      depending on the closest CRM ancestor of the resource associated with
      the finding.
    category: The additional taxonomy group within findings from a given
      source. This field is immutable after creation time. Example:
      "XSS_FLASH_INJECTION"
    chokepoint: Contains details about a chokepoint, which is a resource or
      resource group where high-risk attack paths converge, based on [attack
      path simulations] (https://cloud.google.com/security-command-
      center/docs/attack-exposure-learn#attack_path_simulations). This field
      cannot be updated. Its value is ignored in all update requests.
    cloudArmor: Fields related to Cloud Armor findings.
    cloudDlpDataProfile: Cloud DLP data profile that is associated with the
      finding.
    cloudDlpInspection: Cloud Data Loss Prevention (Cloud DLP) inspection
      results that are associated with the finding.
    compliances: Contains compliance information for security standards
      associated to the finding.
    connections: Contains information about the IP connection associated with
      the finding.
    contacts: Output only. Map containing the points of contact for the given
      finding. The key represents the type of contact, while the value
      contains a list of all the contacts that pertain. Please refer to:
      https://cloud.google.com/resource-manager/docs/managing-notification-
      contacts#notification-categories { "security": { "contacts": [ {
      "email": "<EMAIL>" }, { "email": "<EMAIL>" } ] }
      }
    containers: Containers associated with the finding. This field provides
      information for both Kubernetes and non-Kubernetes containers.
    createTime: The time at which the finding was created in Security Command
      Center.
    dataAccessEvents: Data access events associated with the finding.
    dataFlowEvents: Data flow events associated with the finding.
    dataRetentionDeletionEvents: Data retention deletion events associated
      with the finding.
    database: Database associated with the finding.
    description: Contains more details about the finding.
    disk: Disk associated with the finding.
    eventTime: The time the finding was first detected. If an existing finding
      is updated, then this is the time the update occurred. For example, if
      the finding represents an open firewall, this property captures the time
      the detector believes the firewall became open. The accuracy is
      determined by the detector. If the finding is later resolved, then this
      time reflects when the finding was resolved. This must not be set to a
      value greater than the current timestamp.
    exfiltration: Represents exfiltrations associated with the finding.
    externalSystems: Output only. Third party SIEM/SOAR fields within SCC,
      contains external system information and external system finding fields.
    externalUri: The URI that, if available, points to a web page outside of
      Security Command Center where additional information about the finding
      can be found. This field is guaranteed to be either empty or a well
      formed URL.
    files: File associated with the finding.
    findingClass: The class of the finding.
    groupMemberships: Contains details about groups of which this finding is a
      member. A group is a collection of findings that are related in some
      way. This field cannot be updated. Its value is ignored in all update
      requests.
    iamBindings: Represents IAM bindings associated with the finding.
    indicator: Represents what's commonly known as an *indicator of
      compromise* (IoC) in computer forensics. This is an artifact observed on
      a network or in an operating system that, with high confidence,
      indicates a computer intrusion. For more information, see [Indicator of
      compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise).
    ipRules: IP rules associated with the finding.
    job: Job associated with the finding.
    kernelRootkit: Signature of the kernel rootkit.
    kubernetes: Kubernetes resources associated with the finding.
    loadBalancers: The load balancers associated with the finding.
    logEntries: Log entries that are relevant to the finding.
    mitreAttack: MITRE ATT&CK tactics and techniques related to this finding.
      See: https://attack.mitre.org
    moduleName: Unique identifier of the module which generated the finding.
      Example: folders/598186756061/securityHealthAnalyticsSettings/customModu
      les/56799441161885
    mute: Indicates the mute state of a finding (either muted, unmuted or
      undefined). Unlike other attributes of a finding, a finding provider
      shouldn't set the value of mute.
    muteInfo: Output only. The mute information regarding this finding.
    muteInitiator: Records additional information about the mute operation,
      for example, the [mute configuration](/security-command-center/docs/how-
      to-mute-findings) that muted the finding and the user who muted the
      finding.
    muteUpdateTime: Output only. The most recent time this finding was muted
      or unmuted.
    name: The [relative resource name](https://cloud.google.com/apis/design/re
      source_names#relative_resource_name) of the finding. Example: "organizat
      ions/{organization_id}/sources/{source_id}/findings/{finding_id}",
      "folders/{folder_id}/sources/{source_id}/findings/{finding_id}",
      "projects/{project_id}/sources/{source_id}/findings/{finding_id}".
    networks: Represents the VPC networks that the resource is attached to.
    nextSteps: Steps to address the finding.
    notebook: Notebook associated with the finding.
    orgPolicies: Contains information about the org policies associated with
      the finding.
    parent: The relative resource name of the source the finding belongs to.
      See: https://cloud.google.com/apis/design/resource_names#relative_resour
      ce_name This field is immutable after creation time. For example:
      "organizations/{organization_id}/sources/{source_id}"
    parentDisplayName: Output only. The human readable display name of the
      finding source such as "Event Threat Detection" or "Security Health
      Analytics".
    processes: Represents operating system processes associated with the
      Finding.
    resourceName: For findings on Google Cloud resources, the full resource
      name of the Google Cloud resource this finding is for. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
      When the finding is for a non-Google Cloud resource, the resourceName
      can be a customer or partner defined string. This field is immutable
      after creation time.
    securityMarks: Output only. User specified security marks. These marks are
      entirely managed by the user and come from the SecurityMarks resource
      that belongs to the finding.
    securityPosture: The security posture associated with the finding.
    severity: The severity of the finding. This field is managed by the source
      that writes the finding.
    sourceProperties: Source specific properties. These properties are managed
      by the source that writes the finding. The key names in the
      source_properties map must be between 1 and 255 characters, and must
      start with a letter and contain alphanumeric characters or underscores
      only.
    state: The state of the finding.
    toxicCombination: Contains details about a group of security issues that,
      when the issues occur together, represent a greater risk than when the
      issues occur independently. A group of such issues is referred to as a
      toxic combination. This field cannot be updated. Its value is ignored in
      all update requests.
    vulnerability: Represents vulnerability-specific fields like CVE and CVSS
      scores. CVE stands for Common Vulnerabilities and Exposures
      (https://cve.mitre.org/about/)
  """

  class FindingClassValueValuesEnum(_messages.Enum):
    r"""The class of the finding.

    Values:
      FINDING_CLASS_UNSPECIFIED: Unspecified finding class.
      THREAT: Describes unwanted or malicious activity.
      VULNERABILITY: Describes a potential weakness in software that increases
        risk to Confidentiality & Integrity & Availability.
      MISCONFIGURATION: Describes a potential weakness in cloud resource/asset
        configuration that increases risk.
      OBSERVATION: Describes a security observation that is for informational
        purposes.
      SCC_ERROR: Describes an error that prevents some SCC functionality.
      POSTURE_VIOLATION: Describes a potential security risk due to a change
        in the security posture.
      TOXIC_COMBINATION: Describes a group of security issues that, when the
        issues occur together, represent a greater risk than when the issues
        occur independently. A group of such issues is referred to as a toxic
        combination.
      SENSITIVE_DATA_RISK: Describes a potential security risk to data assets
        that contain sensitive data.
      CHOKEPOINT: Describes a resource or resource group where high risk
        attack paths converge, based on attack path simulations (APS).
    """
    FINDING_CLASS_UNSPECIFIED = 0
    THREAT = 1
    VULNERABILITY = 2
    MISCONFIGURATION = 3
    OBSERVATION = 4
    SCC_ERROR = 5
    POSTURE_VIOLATION = 6
    TOXIC_COMBINATION = 7
    SENSITIVE_DATA_RISK = 8
    CHOKEPOINT = 9

  class MuteValueValuesEnum(_messages.Enum):
    r"""Indicates the mute state of a finding (either muted, unmuted or
    undefined). Unlike other attributes of a finding, a finding provider
    shouldn't set the value of mute.

    Values:
      MUTE_UNSPECIFIED: Unspecified.
      MUTED: Finding has been muted.
      UNMUTED: Finding has been unmuted.
      UNDEFINED: Finding has never been muted/unmuted.
    """
    MUTE_UNSPECIFIED = 0
    MUTED = 1
    UNMUTED = 2
    UNDEFINED = 3

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the finding. This field is managed by the source that
    writes the finding.

    Values:
      SEVERITY_UNSPECIFIED: This value is used for findings when a source
        doesn't write a severity value.
      CRITICAL: Vulnerability: A critical vulnerability is easily discoverable
        by an external actor, exploitable, and results in the direct ability
        to execute arbitrary code, exfiltrate data, and otherwise gain
        additional access and privileges to cloud resources and workloads.
        Examples include publicly accessible unprotected user data and public
        SSH access with weak or no passwords. Threat: Indicates a threat that
        is able to access, modify, or delete data or execute unauthorized code
        within existing resources.
      HIGH: Vulnerability: A high risk vulnerability can be easily discovered
        and exploited in combination with other vulnerabilities in order to
        gain direct access and the ability to execute arbitrary code,
        exfiltrate data, and otherwise gain additional access and privileges
        to cloud resources and workloads. An example is a database with weak
        or no passwords that is only accessible internally. This database
        could easily be compromised by an actor that had access to the
        internal network. Threat: Indicates a threat that is able to create
        new computational resources in an environment but not able to access
        data or execute code in existing resources.
      MEDIUM: Vulnerability: A medium risk vulnerability could be used by an
        actor to gain access to resources or privileges that enable them to
        eventually (through multiple steps or a complex exploit) gain access
        and the ability to execute arbitrary code or exfiltrate data. An
        example is a service account with access to more projects than it
        should have. If an actor gains access to the service account, they
        could potentially use that access to manipulate a project the service
        account was not intended to. Threat: Indicates a threat that is able
        to cause operational impact but may not access data or execute
        unauthorized code.
      LOW: Vulnerability: A low risk vulnerability hampers a security
        organization's ability to detect vulnerabilities or active threats in
        their deployment, or prevents the root cause investigation of security
        issues. An example is monitoring and logs being disabled for resource
        configurations and access. Threat: Indicates a threat that has
        obtained minimal access to an environment but is not able to access
        data, execute code, or create resources.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the finding.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      ACTIVE: The finding requires attention and has not been addressed yet.
      INACTIVE: The finding has been fixed, triaged as a non-issue or
        otherwise addressed and is no longer active.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ContactsValue(_messages.Message):
    r"""Output only. Map containing the points of contact for the given
    finding. The key represents the type of contact, while the value contains
    a list of all the contacts that pertain. Please refer to:
    https://cloud.google.com/resource-manager/docs/managing-notification-
    contacts#notification-categories { "security": { "contacts": [ { "email":
    "<EMAIL>" }, { "email": "<EMAIL>" } ] } }

    Messages:
      AdditionalProperty: An additional property for a ContactsValue object.

    Fields:
      additionalProperties: Additional properties of type ContactsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ContactsValue object.

      Fields:
        key: Name of the additional property.
        value: A ContactDetails attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ContactDetails', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExternalSystemsValue(_messages.Message):
    r"""Output only. Third party SIEM/SOAR fields within SCC, contains
    external system information and external system finding fields.

    Messages:
      AdditionalProperty: An additional property for a ExternalSystemsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ExternalSystemsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExternalSystemsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudSecuritycenterV1ExternalSystem attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudSecuritycenterV1ExternalSystem', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SourcePropertiesValue(_messages.Message):
    r"""Source specific properties. These properties are managed by the source
    that writes the finding. The key names in the source_properties map must
    be between 1 and 255 characters, and must start with a letter and contain
    alphanumeric characters or underscores only.

    Messages:
      AdditionalProperty: An additional property for a SourcePropertiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        SourcePropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SourcePropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  access = _messages.MessageField('Access', 1)
  affectedResources = _messages.MessageField('AffectedResources', 2)
  application = _messages.MessageField('Application', 3)
  attackExposure = _messages.MessageField('AttackExposure', 4)
  backupDisasterRecovery = _messages.MessageField('BackupDisasterRecovery', 5)
  canonicalName = _messages.StringField(6)
  category = _messages.StringField(7)
  chokepoint = _messages.MessageField('Chokepoint', 8)
  cloudArmor = _messages.MessageField('CloudArmor', 9)
  cloudDlpDataProfile = _messages.MessageField('CloudDlpDataProfile', 10)
  cloudDlpInspection = _messages.MessageField('CloudDlpInspection', 11)
  compliances = _messages.MessageField('Compliance', 12, repeated=True)
  connections = _messages.MessageField('Connection', 13, repeated=True)
  contacts = _messages.MessageField('ContactsValue', 14)
  containers = _messages.MessageField('Container', 15, repeated=True)
  createTime = _messages.StringField(16)
  dataAccessEvents = _messages.MessageField('DataAccessEvent', 17, repeated=True)
  dataFlowEvents = _messages.MessageField('DataFlowEvent', 18, repeated=True)
  dataRetentionDeletionEvents = _messages.MessageField('DataRetentionDeletionEvent', 19, repeated=True)
  database = _messages.MessageField('Database', 20)
  description = _messages.StringField(21)
  disk = _messages.MessageField('Disk', 22)
  eventTime = _messages.StringField(23)
  exfiltration = _messages.MessageField('Exfiltration', 24)
  externalSystems = _messages.MessageField('ExternalSystemsValue', 25)
  externalUri = _messages.StringField(26)
  files = _messages.MessageField('File', 27, repeated=True)
  findingClass = _messages.EnumField('FindingClassValueValuesEnum', 28)
  groupMemberships = _messages.MessageField('GroupMembership', 29, repeated=True)
  iamBindings = _messages.MessageField('IamBinding', 30, repeated=True)
  indicator = _messages.MessageField('Indicator', 31)
  ipRules = _messages.MessageField('IpRules', 32)
  job = _messages.MessageField('Job', 33)
  kernelRootkit = _messages.MessageField('KernelRootkit', 34)
  kubernetes = _messages.MessageField('Kubernetes', 35)
  loadBalancers = _messages.MessageField('LoadBalancer', 36, repeated=True)
  logEntries = _messages.MessageField('LogEntry', 37, repeated=True)
  mitreAttack = _messages.MessageField('MitreAttack', 38)
  moduleName = _messages.StringField(39)
  mute = _messages.EnumField('MuteValueValuesEnum', 40)
  muteInfo = _messages.MessageField('MuteInfo', 41)
  muteInitiator = _messages.StringField(42)
  muteUpdateTime = _messages.StringField(43)
  name = _messages.StringField(44)
  networks = _messages.MessageField('Network', 45, repeated=True)
  nextSteps = _messages.StringField(46)
  notebook = _messages.MessageField('Notebook', 47)
  orgPolicies = _messages.MessageField('OrgPolicy', 48, repeated=True)
  parent = _messages.StringField(49)
  parentDisplayName = _messages.StringField(50)
  processes = _messages.MessageField('Process', 51, repeated=True)
  resourceName = _messages.StringField(52)
  securityMarks = _messages.MessageField('SecurityMarks', 53)
  securityPosture = _messages.MessageField('SecurityPosture', 54)
  severity = _messages.EnumField('SeverityValueValuesEnum', 55)
  sourceProperties = _messages.MessageField('SourcePropertiesValue', 56)
  state = _messages.EnumField('StateValueValuesEnum', 57)
  toxicCombination = _messages.MessageField('ToxicCombination', 58)
  vulnerability = _messages.MessageField('Vulnerability', 59)


class Folder(_messages.Message):
  r"""Message that contains the resource name and display name of a folder
  resource.

  Fields:
    resourceFolder: Full resource name of this folder. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    resourceFolderDisplayName: The user defined display name for this folder.
  """

  resourceFolder = _messages.StringField(1)
  resourceFolderDisplayName = _messages.StringField(2)


class GcpMetadata(_messages.Message):
  r"""GCP metadata associated with the resource, only applicable if the
  finding's cloud provider is Google Cloud Platform.

  Fields:
    folders: Output only. Contains a Folder message for each folder in the
      assets ancestry. The first folder is the deepest nested folder, and the
      last folder is the folder directly under the Organization.
    organization: The name of the organization that the resource belongs to.
    parent: The full resource name of resource's parent.
    parentDisplayName: The human readable name of resource's parent.
    project: The full resource name of project that the resource belongs to.
    projectDisplayName: The project ID that the resource belongs to.
  """

  folders = _messages.MessageField('GoogleCloudSecuritycenterV2Folder', 1, repeated=True)
  organization = _messages.StringField(2)
  parent = _messages.StringField(3)
  parentDisplayName = _messages.StringField(4)
  project = _messages.StringField(5)
  projectDisplayName = _messages.StringField(6)


class Geolocation(_messages.Message):
  r"""Represents a geographical location for a given access.

  Fields:
    regionCode: A CLDR.
  """

  regionCode = _messages.StringField(1)


class GoogleCloudSecuritycenterV1BigQueryExport(_messages.Message):
  r"""Configures how to deliver Findings to BigQuery Instance.

  Fields:
    createTime: Output only. The time at which the BigQuery export was
      created. This field is set by the server and will be ignored if provided
      on export on creation.
    dataset: The dataset to write findings' updates to. Its format is
      "projects/[project_id]/datasets/[bigquery_dataset_id]". BigQuery Dataset
      unique ID must contain only letters (a-z, A-Z), numbers (0-9), or
      underscores (_).
    description: The description of the export (max of 1024 characters).
    filter: Expression that defines the filter to apply across create/update
      events of findings. The expression is a list of zero or more
      restrictions combined via logical operators `AND` and `OR`. Parentheses
      are supported, and `OR` has higher precedence than `AND`. Restrictions
      have the form ` ` and may have a `-` character in front of them to
      indicate negation. The fields map to those defined in the corresponding
      resource. The supported operators are: * `=` for all value types. * `>`,
      `<`, `>=`, `<=` for integer values. * `:`, meaning substring matching,
      for strings. The supported value types are: * string literals in quotes.
      * integer literals without quotes. * boolean literals `true` and `false`
      without quotes.
    mostRecentEditor: Output only. Email address of the user who last edited
      the BigQuery export. This field is set by the server and will be ignored
      if provided on export creation or update.
    name: The relative resource name of this export. See: https://cloud.google
      .com/apis/design/resource_names#relative_resource_name. Example format:
      "organizations/{organization_id}/bigQueryExports/{export_id}" Example
      format: "folders/{folder_id}/bigQueryExports/{export_id}" Example
      format: "projects/{project_id}/bigQueryExports/{export_id}" This field
      is provided in responses, and is ignored when provided in create
      requests.
    principal: Output only. The service account that needs permission to
      create table and upload data to the BigQuery dataset.
    updateTime: Output only. The most recent time at which the BigQuery export
      was updated. This field is set by the server and will be ignored if
      provided on export creation or update.
  """

  createTime = _messages.StringField(1)
  dataset = _messages.StringField(2)
  description = _messages.StringField(3)
  filter = _messages.StringField(4)
  mostRecentEditor = _messages.StringField(5)
  name = _messages.StringField(6)
  principal = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class GoogleCloudSecuritycenterV1Binding(_messages.Message):
  r"""Represents a Kubernetes RoleBinding or ClusterRoleBinding.

  Fields:
    name: Name for the binding.
    ns: Namespace for the binding.
    role: The Role or ClusterRole referenced by the binding.
    subjects: Represents one or more subjects that are bound to the role. Not
      always available for PATCH requests.
  """

  name = _messages.StringField(1)
  ns = _messages.StringField(2)
  role = _messages.MessageField('Role', 3)
  subjects = _messages.MessageField('Subject', 4, repeated=True)


class GoogleCloudSecuritycenterV1BulkMuteFindingsResponse(_messages.Message):
  r"""The response to a BulkMute request. Contains the LRO information."""


class GoogleCloudSecuritycenterV1CustomConfig(_messages.Message):
  r"""Defines the properties in a custom module configuration for Security
  Health Analytics. Use the custom module configuration to create custom
  detectors that generate custom findings for resources that you specify.

  Enums:
    SeverityValueValuesEnum: The severity to assign to findings generated by
      the module.

  Fields:
    customOutput: Custom output properties.
    description: Text that describes the vulnerability or misconfiguration
      that the custom module detects. This explanation is returned with each
      finding instance to help investigators understand the detected issue.
      The text must be enclosed in quotation marks.
    predicate: The CEL expression to evaluate to produce findings. When the
      expression evaluates to true against a resource, a finding is generated.
    recommendation: An explanation of the recommended steps that security
      teams can take to resolve the detected issue. This explanation is
      returned with each finding generated by this module in the `nextSteps`
      property of the finding JSON.
    resourceSelector: The resource types that the custom module operates on.
      Each custom module can specify up to 5 resource types.
    severity: The severity to assign to findings generated by the module.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity to assign to findings generated by the module.

    Values:
      SEVERITY_UNSPECIFIED: Unspecified severity.
      CRITICAL: Critical severity.
      HIGH: High severity.
      MEDIUM: Medium severity.
      LOW: Low severity.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  customOutput = _messages.MessageField('GoogleCloudSecuritycenterV1CustomOutputSpec', 1)
  description = _messages.StringField(2)
  predicate = _messages.MessageField('Expr', 3)
  recommendation = _messages.StringField(4)
  resourceSelector = _messages.MessageField('GoogleCloudSecuritycenterV1ResourceSelector', 5)
  severity = _messages.EnumField('SeverityValueValuesEnum', 6)


class GoogleCloudSecuritycenterV1CustomOutputSpec(_messages.Message):
  r"""A set of optional name-value pairs that define custom source properties
  to return with each finding that is generated by the custom module. The
  custom source properties that are defined here are included in the finding
  JSON under `sourceProperties`.

  Fields:
    properties: A list of custom output properties to add to the finding.
  """

  properties = _messages.MessageField('GoogleCloudSecuritycenterV1Property', 1, repeated=True)


class GoogleCloudSecuritycenterV1EffectiveSecurityHealthAnalyticsCustomModule(_messages.Message):
  r"""An EffectiveSecurityHealthAnalyticsCustomModule is the representation of
  a Security Health Analytics custom module at a specified level of the
  resource hierarchy: organization, folder, or project. If a custom module is
  inherited from a parent organization or folder, the value of the
  `enablementState` property in EffectiveSecurityHealthAnalyticsCustomModule
  is set to the value that is effective in the parent, instead of `INHERITED`.
  For example, if the module is enabled in a parent organization or folder,
  the effective enablement_state for the module in all child folders or
  projects is also `enabled`. EffectiveSecurityHealthAnalyticsCustomModule is
  read-only.

  Enums:
    CloudProviderValueValuesEnum: The cloud provider of the custom module.
    EnablementStateValueValuesEnum: Output only. The effective state of
      enablement for the module at the given level of the hierarchy.

  Fields:
    cloudProvider: The cloud provider of the custom module.
    customConfig: Output only. The user-specified configuration for the
      module.
    displayName: Output only. The display name for the custom module. The name
      must be between 1 and 128 characters, start with a lowercase letter, and
      contain alphanumeric characters or underscores only.
    enablementState: Output only. The effective state of enablement for the
      module at the given level of the hierarchy.
    name: Output only. The resource name of the custom module. Its format is "
      organizations/{organization}/securityHealthAnalyticsSettings/effectiveCu
      stomModules/{customModule}", or "folders/{folder}/securityHealthAnalytic
      sSettings/effectiveCustomModules/{customModule}", or "projects/{project}
      /securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}"
  """

  class CloudProviderValueValuesEnum(_messages.Enum):
    r"""The cloud provider of the custom module.

    Values:
      CLOUD_PROVIDER_UNSPECIFIED: Unspecified cloud provider.
      GOOGLE_CLOUD_PLATFORM: Google Cloud Platform.
      AMAZON_WEB_SERVICES: Amazon Web Services.
      MICROSOFT_AZURE: Microsoft Azure.
    """
    CLOUD_PROVIDER_UNSPECIFIED = 0
    GOOGLE_CLOUD_PLATFORM = 1
    AMAZON_WEB_SERVICES = 2
    MICROSOFT_AZURE = 3

  class EnablementStateValueValuesEnum(_messages.Enum):
    r"""Output only. The effective state of enablement for the module at the
    given level of the hierarchy.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Unspecified enablement state.
      ENABLED: The module is enabled at the given level.
      DISABLED: The module is disabled at the given level.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  cloudProvider = _messages.EnumField('CloudProviderValueValuesEnum', 1)
  customConfig = _messages.MessageField('GoogleCloudSecuritycenterV1CustomConfig', 2)
  displayName = _messages.StringField(3)
  enablementState = _messages.EnumField('EnablementStateValueValuesEnum', 4)
  name = _messages.StringField(5)


class GoogleCloudSecuritycenterV1ExternalSystem(_messages.Message):
  r"""Representation of third party SIEM/SOAR fields within SCC.

  Fields:
    assignees: References primary/secondary etc assignees in the external
      system.
    caseCloseTime: The time when the case was closed, as reported by the
      external system.
    caseCreateTime: The time when the case was created, as reported by the
      external system.
    casePriority: The priority of the finding's corresponding case in the
      external system.
    caseSla: The SLA of the finding's corresponding case in the external
      system.
    caseUri: The link to the finding's corresponding case in the external
      system.
    externalSystemUpdateTime: The time when the case was last updated, as
      reported by the external system.
    externalUid: The identifier that's used to track the finding's
      corresponding case in the external system.
    name: Full resource name of the external system, for example:
      "organizations/1234/sources/5678/findings/123456/externalSystems/jira",
      "folders/1234/sources/5678/findings/123456/externalSystems/jira",
      "projects/1234/sources/5678/findings/123456/externalSystems/jira"
    status: The most recent status of the finding's corresponding case, as
      reported by the external system.
    ticketInfo: Information about the ticket, if any, that is being used to
      track the resolution of the issue that is identified by this finding.
  """

  assignees = _messages.StringField(1, repeated=True)
  caseCloseTime = _messages.StringField(2)
  caseCreateTime = _messages.StringField(3)
  casePriority = _messages.StringField(4)
  caseSla = _messages.StringField(5)
  caseUri = _messages.StringField(6)
  externalSystemUpdateTime = _messages.StringField(7)
  externalUid = _messages.StringField(8)
  name = _messages.StringField(9)
  status = _messages.StringField(10)
  ticketInfo = _messages.MessageField('TicketInfo', 11)


class GoogleCloudSecuritycenterV1MuteConfig(_messages.Message):
  r"""A mute config is a Cloud SCC resource that contains the configuration to
  mute create/update events of findings.

  Enums:
    TypeValueValuesEnum: Optional. The type of the mute config, which
      determines what type of mute state the config affects. The static mute
      state takes precedence over the dynamic mute state. Immutable after
      creation. STATIC by default if not set during creation.

  Fields:
    createTime: Output only. The time at which the mute config was created.
      This field is set by the server and will be ignored if provided on
      config creation.
    description: A description of the mute config.
    displayName: The human readable name to be displayed for the mute config.
    expiryTime: Optional. The expiry of the mute config. Only applicable for
      dynamic configs. If the expiry is set, when the config expires, it is
      removed from all findings.
    filter: Required. An expression that defines the filter to apply across
      create/update events of findings. While creating a filter string, be
      mindful of the scope in which the mute configuration is being created.
      E.g., If a filter contains project = X but is created under the project
      = Y scope, it might not match any findings. The following field and
      operator combinations are supported: * severity: `=`, `:` * category:
      `=`, `:` * resource.name: `=`, `:` * resource.project_name: `=`, `:` *
      resource.project_display_name: `=`, `:` *
      resource.folders.resource_folder: `=`, `:` * resource.parent_name: `=`,
      `:` * resource.parent_display_name: `=`, `:` * resource.type: `=`, `:` *
      finding_class: `=`, `:` * indicator.ip_addresses: `=`, `:` *
      indicator.domains: `=`, `:`
    mostRecentEditor: Output only. Email address of the user who last edited
      the mute config. This field is set by the server and will be ignored if
      provided on config creation or update.
    name: This field will be ignored if provided on config creation. Format
      `organizations/{organization}/muteConfigs/{mute_config}`
      `folders/{folder}/muteConfigs/{mute_config}`
      `projects/{project}/muteConfigs/{mute_config}` `organizations/{organizat
      ion}/locations/global/muteConfigs/{mute_config}`
      `folders/{folder}/locations/global/muteConfigs/{mute_config}`
      `projects/{project}/locations/global/muteConfigs/{mute_config}`
    type: Optional. The type of the mute config, which determines what type of
      mute state the config affects. The static mute state takes precedence
      over the dynamic mute state. Immutable after creation. STATIC by default
      if not set during creation.
    updateTime: Output only. The most recent time at which the mute config was
      updated. This field is set by the server and will be ignored if provided
      on config creation or update.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of the mute config, which determines what type of
    mute state the config affects. The static mute state takes precedence over
    the dynamic mute state. Immutable after creation. STATIC by default if not
    set during creation.

    Values:
      MUTE_CONFIG_TYPE_UNSPECIFIED: Unused.
      STATIC: A static mute config, which sets the static mute state of future
        matching findings to muted. Once the static mute state has been set,
        finding or config modifications will not affect the state.
      DYNAMIC: A dynamic mute config, which is applied to existing and future
        matching findings, setting their dynamic mute state to "muted". If the
        config is updated or deleted, or a matching finding is updated, such
        that the finding doesn't match the config, the config will be removed
        from the finding, and the finding's dynamic mute state may become
        "unmuted" (unless other configs still match).
    """
    MUTE_CONFIG_TYPE_UNSPECIFIED = 0
    STATIC = 1
    DYNAMIC = 2

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  expiryTime = _messages.StringField(4)
  filter = _messages.StringField(5)
  mostRecentEditor = _messages.StringField(6)
  name = _messages.StringField(7)
  type = _messages.EnumField('TypeValueValuesEnum', 8)
  updateTime = _messages.StringField(9)


class GoogleCloudSecuritycenterV1NotificationMessage(_messages.Message):
  r"""Cloud SCC's Notification

  Fields:
    finding: If it's a Finding based notification config, this field will be
      populated.
    notificationConfigName: Name of the notification config that generated
      current notification.
    resource: The Cloud resource tied to this notification's Finding.
  """

  finding = _messages.MessageField('Finding', 1)
  notificationConfigName = _messages.StringField(2)
  resource = _messages.MessageField('GoogleCloudSecuritycenterV1Resource', 3)


class GoogleCloudSecuritycenterV1Property(_messages.Message):
  r"""An individual name-value pair that defines a custom source property.

  Fields:
    name: Name of the property for the custom output.
    valueExpression: The CEL expression for the custom output. A resource
      property can be specified to return the value of the property or a text
      string enclosed in quotation marks.
  """

  name = _messages.StringField(1)
  valueExpression = _messages.MessageField('Expr', 2)


class GoogleCloudSecuritycenterV1Resource(_messages.Message):
  r"""Information related to the Google Cloud resource.

  Enums:
    CloudProviderValueValuesEnum: Indicates which cloud provider the resource
      resides in.

  Fields:
    awsMetadata: The AWS metadata associated with the finding.
    azureMetadata: The Azure metadata associated with the finding.
    cloudProvider: Indicates which cloud provider the resource resides in.
    displayName: The human readable name of the resource.
    folders: Output only. Contains a Folder message for each folder in the
      assets ancestry. The first folder is the deepest nested folder, and the
      last folder is the folder directly under the Organization.
    location: The region or location of the service (if applicable).
    name: The full resource name of the resource. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    organization: Indicates which organization or tenant in the cloud provider
      the finding applies to.
    parent: The full resource name of resource's parent.
    parentDisplayName: The human readable name of resource's parent.
    project: The full resource name of project that the resource belongs to.
    projectDisplayName: The project ID that the resource belongs to.
    resourcePath: Provides the path to the resource within the resource
      hierarchy.
    resourcePathString: A string representation of the resource path. For
      Google Cloud, it has the format of `organizations/{organization_id}/fold
      ers/{folder_id}/folders/{folder_id}/projects/{project_id}` where there
      can be any number of folders. For AWS, it has the format of `org/{organi
      zation_id}/ou/{organizational_unit_id}/ou/{organizational_unit_id}/accou
      nt/{account_id}` where there can be any number of organizational units.
      For Azure, it has the format of `mg/{management_group_id}/mg/{management
      _group_id}/subscription/{subscription_id}/rg/{resource_group_name}`
      where there can be any number of management groups.
    service: The parent service or product from which the resource is
      provided, for example, GKE or SNS.
    type: The full resource type of the resource.
  """

  class CloudProviderValueValuesEnum(_messages.Enum):
    r"""Indicates which cloud provider the resource resides in.

    Values:
      CLOUD_PROVIDER_UNSPECIFIED: The cloud provider is unspecified.
      GOOGLE_CLOUD_PLATFORM: The cloud provider is Google Cloud Platform.
      AMAZON_WEB_SERVICES: The cloud provider is Amazon Web Services.
      MICROSOFT_AZURE: The cloud provider is Microsoft Azure.
    """
    CLOUD_PROVIDER_UNSPECIFIED = 0
    GOOGLE_CLOUD_PLATFORM = 1
    AMAZON_WEB_SERVICES = 2
    MICROSOFT_AZURE = 3

  awsMetadata = _messages.MessageField('AwsMetadata', 1)
  azureMetadata = _messages.MessageField('AzureMetadata', 2)
  cloudProvider = _messages.EnumField('CloudProviderValueValuesEnum', 3)
  displayName = _messages.StringField(4)
  folders = _messages.MessageField('Folder', 5, repeated=True)
  location = _messages.StringField(6)
  name = _messages.StringField(7)
  organization = _messages.StringField(8)
  parent = _messages.StringField(9)
  parentDisplayName = _messages.StringField(10)
  project = _messages.StringField(11)
  projectDisplayName = _messages.StringField(12)
  resourcePath = _messages.MessageField('ResourcePath', 13)
  resourcePathString = _messages.StringField(14)
  service = _messages.StringField(15)
  type = _messages.StringField(16)


class GoogleCloudSecuritycenterV1ResourceSelector(_messages.Message):
  r"""Resource for selecting resource type.

  Fields:
    resourceTypes: The resource types to run the detector on.
  """

  resourceTypes = _messages.StringField(1, repeated=True)


class GoogleCloudSecuritycenterV1ResourceValueConfig(_messages.Message):
  r"""A resource value configuration (RVC) is a mapping configuration of
  user's resources to resource values. Used in Attack path simulations.

  Enums:
    CloudProviderValueValuesEnum: Cloud provider this configuration applies to
    ResourceValueValueValuesEnum: Required. Resource value level this
      expression represents

  Messages:
    ResourceLabelsSelectorValue: List of resource labels to search for,
      evaluated with `AND`. For example, `"resource_labels_selector": {"key":
      "value", "env": "prod"}` will match resources with labels "key": "value"
      `AND` "env": "prod" https://cloud.google.com/resource-
      manager/docs/creating-managing-labels

  Fields:
    cloudProvider: Cloud provider this configuration applies to
    createTime: Output only. Timestamp this resource value configuration was
      created.
    description: Description of the resource value configuration.
    name: Name for the resource value configuration
    resourceLabelsSelector: List of resource labels to search for, evaluated
      with `AND`. For example, `"resource_labels_selector": {"key": "value",
      "env": "prod"}` will match resources with labels "key": "value" `AND`
      "env": "prod" https://cloud.google.com/resource-manager/docs/creating-
      managing-labels
    resourceType: Apply resource_value only to resources that match
      resource_type. resource_type will be checked with `AND` of other
      resources. For example, "storage.googleapis.com/Bucket" with
      resource_value "HIGH" will apply "HIGH" value only to
      "storage.googleapis.com/Bucket" resources.
    resourceValue: Required. Resource value level this expression represents
    scope: Project or folder to scope this configuration to. For example,
      "project/456" would apply this configuration only to resources in
      "project/456" scope will be checked with `AND` of other resources.
    sensitiveDataProtectionMapping: A mapping of the sensitivity on Sensitive
      Data Protection finding to resource values. This mapping can only be
      used in combination with a resource_type that is related to BigQuery,
      e.g. "bigquery.googleapis.com/Dataset".
    tagValues: Required. Tag values combined with `AND` to check against. For
      Google Cloud resources, they are tag value IDs in the form of
      "tagValues/123". Example: `[ "tagValues/123", "tagValues/456",
      "tagValues/789" ]` https://cloud.google.com/resource-
      manager/docs/tags/tags-creating-and-managing
    updateTime: Output only. Timestamp this resource value configuration was
      last updated.
  """

  class CloudProviderValueValuesEnum(_messages.Enum):
    r"""Cloud provider this configuration applies to

    Values:
      CLOUD_PROVIDER_UNSPECIFIED: The cloud provider is unspecified.
      GOOGLE_CLOUD_PLATFORM: The cloud provider is Google Cloud Platform.
      AMAZON_WEB_SERVICES: The cloud provider is Amazon Web Services.
      MICROSOFT_AZURE: The cloud provider is Microsoft Azure.
    """
    CLOUD_PROVIDER_UNSPECIFIED = 0
    GOOGLE_CLOUD_PLATFORM = 1
    AMAZON_WEB_SERVICES = 2
    MICROSOFT_AZURE = 3

  class ResourceValueValueValuesEnum(_messages.Enum):
    r"""Required. Resource value level this expression represents

    Values:
      RESOURCE_VALUE_UNSPECIFIED: Unspecific value
      HIGH: High resource value
      MEDIUM: Medium resource value
      LOW: Low resource value
      NONE: No resource value, e.g. ignore these resources
    """
    RESOURCE_VALUE_UNSPECIFIED = 0
    HIGH = 1
    MEDIUM = 2
    LOW = 3
    NONE = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceLabelsSelectorValue(_messages.Message):
    r"""List of resource labels to search for, evaluated with `AND`. For
    example, `"resource_labels_selector": {"key": "value", "env": "prod"}`
    will match resources with labels "key": "value" `AND` "env": "prod"
    https://cloud.google.com/resource-manager/docs/creating-managing-labels

    Messages:
      AdditionalProperty: An additional property for a
        ResourceLabelsSelectorValue object.

    Fields:
      additionalProperties: Additional properties of type
        ResourceLabelsSelectorValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceLabelsSelectorValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cloudProvider = _messages.EnumField('CloudProviderValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  name = _messages.StringField(4)
  resourceLabelsSelector = _messages.MessageField('ResourceLabelsSelectorValue', 5)
  resourceType = _messages.StringField(6)
  resourceValue = _messages.EnumField('ResourceValueValueValuesEnum', 7)
  scope = _messages.StringField(8)
  sensitiveDataProtectionMapping = _messages.MessageField('GoogleCloudSecuritycenterV1SensitiveDataProtectionMapping', 9)
  tagValues = _messages.StringField(10, repeated=True)
  updateTime = _messages.StringField(11)


class GoogleCloudSecuritycenterV1RunAssetDiscoveryResponse(_messages.Message):
  r"""Response of asset discovery run

  Enums:
    StateValueValuesEnum: The state of an asset discovery run.

  Fields:
    duration: The duration between asset discovery run start and end
    state: The state of an asset discovery run.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of an asset discovery run.

    Values:
      STATE_UNSPECIFIED: Asset discovery run state was unspecified.
      COMPLETED: Asset discovery run completed successfully.
      SUPERSEDED: Asset discovery run was cancelled with tasks still pending,
        as another run for the same organization was started with a higher
        priority.
      TERMINATED: Asset discovery run was killed and terminated.
    """
    STATE_UNSPECIFIED = 0
    COMPLETED = 1
    SUPERSEDED = 2
    TERMINATED = 3

  duration = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule(_messages.Message):
  r"""Represents an instance of a Security Health Analytics custom module,
  including its full module name, display name, enablement state, and last
  updated time. You can create a custom module at the organization, folder, or
  project level. Custom modules that you create at the organization or folder
  level are inherited by the child folders and projects.

  Enums:
    CloudProviderValueValuesEnum: The cloud provider of the custom module.
    EnablementStateValueValuesEnum: The enablement state of the custom module.

  Fields:
    ancestorModule: Output only. If empty, indicates that the custom module
      was created in the organization, folder, or project in which you are
      viewing the custom module. Otherwise, `ancestor_module` specifies the
      organization or folder from which the custom module is inherited.
    cloudProvider: The cloud provider of the custom module.
    customConfig: The user specified custom configuration for the module.
    displayName: The display name of the Security Health Analytics custom
      module. This display name becomes the finding category for all findings
      that are returned by this custom module. The display name must be
      between 1 and 128 characters, start with a lowercase letter, and contain
      alphanumeric characters or underscores only.
    enablementState: The enablement state of the custom module.
    lastEditor: Output only. The editor that last updated the custom module.
    name: Immutable. The resource name of the custom module. Its format is "or
      ganizations/{organization}/securityHealthAnalyticsSettings/customModules
      /{customModule}", or "folders/{folder}/securityHealthAnalyticsSettings/c
      ustomModules/{customModule}", or "projects/{project}/securityHealthAnaly
      ticsSettings/customModules/{customModule}" The id {customModule} is
      server-generated and is not user settable. It will be a numeric id
      containing 1-20 digits.
    updateTime: Output only. The time at which the custom module was last
      updated.
  """

  class CloudProviderValueValuesEnum(_messages.Enum):
    r"""The cloud provider of the custom module.

    Values:
      CLOUD_PROVIDER_UNSPECIFIED: Unspecified cloud provider.
      GOOGLE_CLOUD_PLATFORM: Google Cloud.
      AMAZON_WEB_SERVICES: Amazon Web Services (AWS).
      MICROSOFT_AZURE: Microsoft Azure.
    """
    CLOUD_PROVIDER_UNSPECIFIED = 0
    GOOGLE_CLOUD_PLATFORM = 1
    AMAZON_WEB_SERVICES = 2
    MICROSOFT_AZURE = 3

  class EnablementStateValueValuesEnum(_messages.Enum):
    r"""The enablement state of the custom module.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Unspecified enablement state.
      ENABLED: The module is enabled at the given CRM resource.
      DISABLED: The module is disabled at the given CRM resource.
      INHERITED: State is inherited from an ancestor module. The module will
        either be effectively ENABLED or DISABLED based on its closest non-
        inherited ancestor module in the CRM hierarchy.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2
    INHERITED = 3

  ancestorModule = _messages.StringField(1)
  cloudProvider = _messages.EnumField('CloudProviderValueValuesEnum', 2)
  customConfig = _messages.MessageField('GoogleCloudSecuritycenterV1CustomConfig', 3)
  displayName = _messages.StringField(4)
  enablementState = _messages.EnumField('EnablementStateValueValuesEnum', 5)
  lastEditor = _messages.StringField(6)
  name = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class GoogleCloudSecuritycenterV1SensitiveDataProtectionMapping(_messages.Message):
  r"""Resource value mapping for Sensitive Data Protection findings. If any of
  these mappings have a resource value that is not unspecified, the
  resource_value field will be ignored when reading this configuration.

  Enums:
    HighSensitivityMappingValueValuesEnum: Resource value mapping for high-
      sensitivity Sensitive Data Protection findings
    MediumSensitivityMappingValueValuesEnum: Resource value mapping for
      medium-sensitivity Sensitive Data Protection findings

  Fields:
    highSensitivityMapping: Resource value mapping for high-sensitivity
      Sensitive Data Protection findings
    mediumSensitivityMapping: Resource value mapping for medium-sensitivity
      Sensitive Data Protection findings
  """

  class HighSensitivityMappingValueValuesEnum(_messages.Enum):
    r"""Resource value mapping for high-sensitivity Sensitive Data Protection
    findings

    Values:
      RESOURCE_VALUE_UNSPECIFIED: Unspecific value
      HIGH: High resource value
      MEDIUM: Medium resource value
      LOW: Low resource value
      NONE: No resource value, e.g. ignore these resources
    """
    RESOURCE_VALUE_UNSPECIFIED = 0
    HIGH = 1
    MEDIUM = 2
    LOW = 3
    NONE = 4

  class MediumSensitivityMappingValueValuesEnum(_messages.Enum):
    r"""Resource value mapping for medium-sensitivity Sensitive Data
    Protection findings

    Values:
      RESOURCE_VALUE_UNSPECIFIED: Unspecific value
      HIGH: High resource value
      MEDIUM: Medium resource value
      LOW: Low resource value
      NONE: No resource value, e.g. ignore these resources
    """
    RESOURCE_VALUE_UNSPECIFIED = 0
    HIGH = 1
    MEDIUM = 2
    LOW = 3
    NONE = 4

  highSensitivityMapping = _messages.EnumField('HighSensitivityMappingValueValuesEnum', 1)
  mediumSensitivityMapping = _messages.EnumField('MediumSensitivityMappingValueValuesEnum', 2)


class GoogleCloudSecuritycenterV1beta1RunAssetDiscoveryResponse(_messages.Message):
  r"""Response of asset discovery run

  Enums:
    StateValueValuesEnum: The state of an asset discovery run.

  Fields:
    duration: The duration between asset discovery run start and end
    state: The state of an asset discovery run.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of an asset discovery run.

    Values:
      STATE_UNSPECIFIED: Asset discovery run state was unspecified.
      COMPLETED: Asset discovery run completed successfully.
      SUPERSEDED: Asset discovery run was cancelled with tasks still pending,
        as another run for the same organization was started with a higher
        priority.
      TERMINATED: Asset discovery run was killed and terminated.
    """
    STATE_UNSPECIFIED = 0
    COMPLETED = 1
    SUPERSEDED = 2
    TERMINATED = 3

  duration = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudSecuritycenterV1p1beta1Finding(_messages.Message):
  r"""Security Command Center finding. A finding is a record of assessment
  data (security, risk, health or privacy) ingested into Security Command
  Center for presentation, notification, analysis, policy testing, and
  enforcement. For example, an XSS vulnerability in an App Engine application
  is a finding.

  Enums:
    SeverityValueValuesEnum: The severity of the finding. This field is
      managed by the source that writes the finding.
    StateValueValuesEnum: The state of the finding.

  Messages:
    SourcePropertiesValue: Source specific properties. These properties are
      managed by the source that writes the finding. The key names in the
      source_properties map must be between 1 and 255 characters, and must
      start with a letter and contain alphanumeric characters or underscores
      only.

  Fields:
    canonicalName: The canonical name of the finding. It's either "organizatio
      ns/{organization_id}/sources/{source_id}/findings/{finding_id}",
      "folders/{folder_id}/sources/{source_id}/findings/{finding_id}" or
      "projects/{project_number}/sources/{source_id}/findings/{finding_id}",
      depending on the closest CRM ancestor of the resource associated with
      the finding.
    category: The additional taxonomy group within findings from a given
      source. This field is immutable after creation time. Example:
      "XSS_FLASH_INJECTION"
    createTime: The time at which the finding was created in Security Command
      Center.
    eventTime: The time at which the event took place, or when an update to
      the finding occurred. For example, if the finding represents an open
      firewall it would capture the time the detector believes the firewall
      became open. The accuracy is determined by the detector. If the finding
      were to be resolved afterward, this time would reflect when the finding
      was resolved. Must not be set to a value greater than the current
      timestamp.
    externalUri: The URI that, if available, points to a web page outside of
      Security Command Center where additional information about the finding
      can be found. This field is guaranteed to be either empty or a well
      formed URL.
    name: The relative resource name of this finding. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Example: "organizations/{organization_id}/sources/{source_id}/finding
      s/{finding_id}"
    parent: The relative resource name of the source the finding belongs to.
      See: https://cloud.google.com/apis/design/resource_names#relative_resour
      ce_name This field is immutable after creation time. For example:
      "organizations/{organization_id}/sources/{source_id}"
    resourceName: For findings on Google Cloud resources, the full resource
      name of the Google Cloud resource this finding is for. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
      When the finding is for a non-Google Cloud resource, the resourceName
      can be a customer or partner defined string. This field is immutable
      after creation time.
    securityMarks: Output only. User specified security marks. These marks are
      entirely managed by the user and come from the SecurityMarks resource
      that belongs to the finding.
    severity: The severity of the finding. This field is managed by the source
      that writes the finding.
    sourceProperties: Source specific properties. These properties are managed
      by the source that writes the finding. The key names in the
      source_properties map must be between 1 and 255 characters, and must
      start with a letter and contain alphanumeric characters or underscores
      only.
    state: The state of the finding.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the finding. This field is managed by the source that
    writes the finding.

    Values:
      SEVERITY_UNSPECIFIED: No severity specified. The default value.
      CRITICAL: Critical severity.
      HIGH: High severity.
      MEDIUM: Medium severity.
      LOW: Low severity.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the finding.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      ACTIVE: The finding requires attention and has not been addressed yet.
      INACTIVE: The finding has been fixed, triaged as a non-issue or
        otherwise addressed and is no longer active.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SourcePropertiesValue(_messages.Message):
    r"""Source specific properties. These properties are managed by the source
    that writes the finding. The key names in the source_properties map must
    be between 1 and 255 characters, and must start with a letter and contain
    alphanumeric characters or underscores only.

    Messages:
      AdditionalProperty: An additional property for a SourcePropertiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        SourcePropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SourcePropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  canonicalName = _messages.StringField(1)
  category = _messages.StringField(2)
  createTime = _messages.StringField(3)
  eventTime = _messages.StringField(4)
  externalUri = _messages.StringField(5)
  name = _messages.StringField(6)
  parent = _messages.StringField(7)
  resourceName = _messages.StringField(8)
  securityMarks = _messages.MessageField('GoogleCloudSecuritycenterV1p1beta1SecurityMarks', 9)
  severity = _messages.EnumField('SeverityValueValuesEnum', 10)
  sourceProperties = _messages.MessageField('SourcePropertiesValue', 11)
  state = _messages.EnumField('StateValueValuesEnum', 12)


class GoogleCloudSecuritycenterV1p1beta1Folder(_messages.Message):
  r"""Message that contains the resource name and display name of a folder
  resource.

  Fields:
    resourceFolder: Full resource name of this folder. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    resourceFolderDisplayName: The user defined display name for this folder.
  """

  resourceFolder = _messages.StringField(1)
  resourceFolderDisplayName = _messages.StringField(2)


class GoogleCloudSecuritycenterV1p1beta1NotificationMessage(_messages.Message):
  r"""Security Command Center's Notification

  Fields:
    finding: If it's a Finding based notification config, this field will be
      populated.
    notificationConfigName: Name of the notification config that generated
      current notification.
    resource: The Cloud resource tied to the notification.
  """

  finding = _messages.MessageField('GoogleCloudSecuritycenterV1p1beta1Finding', 1)
  notificationConfigName = _messages.StringField(2)
  resource = _messages.MessageField('GoogleCloudSecuritycenterV1p1beta1Resource', 3)


class GoogleCloudSecuritycenterV1p1beta1Resource(_messages.Message):
  r"""Information related to the Google Cloud resource.

  Fields:
    folders: Output only. Contains a Folder message for each folder in the
      assets ancestry. The first folder is the deepest nested folder, and the
      last folder is the folder directly under the Organization.
    name: The full resource name of the resource. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    parent: The full resource name of resource's parent.
    parentDisplayName: The human readable name of resource's parent.
    project: The full resource name of project that the resource belongs to.
    projectDisplayName: The project id that the resource belongs to.
  """

  folders = _messages.MessageField('GoogleCloudSecuritycenterV1p1beta1Folder', 1, repeated=True)
  name = _messages.StringField(2)
  parent = _messages.StringField(3)
  parentDisplayName = _messages.StringField(4)
  project = _messages.StringField(5)
  projectDisplayName = _messages.StringField(6)


class GoogleCloudSecuritycenterV1p1beta1RunAssetDiscoveryResponse(_messages.Message):
  r"""Response of asset discovery run

  Enums:
    StateValueValuesEnum: The state of an asset discovery run.

  Fields:
    duration: The duration between asset discovery run start and end
    state: The state of an asset discovery run.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of an asset discovery run.

    Values:
      STATE_UNSPECIFIED: Asset discovery run state was unspecified.
      COMPLETED: Asset discovery run completed successfully.
      SUPERSEDED: Asset discovery run was cancelled with tasks still pending,
        as another run for the same organization was started with a higher
        priority.
      TERMINATED: Asset discovery run was killed and terminated.
    """
    STATE_UNSPECIFIED = 0
    COMPLETED = 1
    SUPERSEDED = 2
    TERMINATED = 3

  duration = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudSecuritycenterV1p1beta1SecurityMarks(_messages.Message):
  r"""User specified security marks that are attached to the parent Security
  Command Center resource. Security marks are scoped within a Security Command
  Center organization -- they can be modified and viewed by all users who have
  proper permissions on the organization.

  Messages:
    MarksValue: Mutable user specified security marks belonging to the parent
      resource. Constraints are as follows: * Keys and values are treated as
      case insensitive * Keys must be between 1 - 256 characters (inclusive) *
      Keys must be letters, numbers, underscores, or dashes * Values have
      leading and trailing whitespace trimmed, remaining characters must be
      between 1 - 4096 characters (inclusive)

  Fields:
    canonicalName: The canonical name of the marks. Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks"
      "folders/{folder_id}/assets/{asset_id}/securityMarks"
      "projects/{project_number}/assets/{asset_id}/securityMarks" "organizatio
      ns/{organization_id}/sources/{source_id}/findings/{finding_id}/securityM
      arks" "folders/{folder_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks" "projects/{project_number}/sources/{source_id}/findings/{fin
      ding_id}/securityMarks"
    marks: Mutable user specified security marks belonging to the parent
      resource. Constraints are as follows: * Keys and values are treated as
      case insensitive * Keys must be between 1 - 256 characters (inclusive) *
      Keys must be letters, numbers, underscores, or dashes * Values have
      leading and trailing whitespace trimmed, remaining characters must be
      between 1 - 4096 characters (inclusive)
    name: The relative resource name of the SecurityMarks. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks" "organ
      izations/{organization_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks".
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MarksValue(_messages.Message):
    r"""Mutable user specified security marks belonging to the parent
    resource. Constraints are as follows: * Keys and values are treated as
    case insensitive * Keys must be between 1 - 256 characters (inclusive) *
    Keys must be letters, numbers, underscores, or dashes * Values have
    leading and trailing whitespace trimmed, remaining characters must be
    between 1 - 4096 characters (inclusive)

    Messages:
      AdditionalProperty: An additional property for a MarksValue object.

    Fields:
      additionalProperties: Additional properties of type MarksValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MarksValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  canonicalName = _messages.StringField(1)
  marks = _messages.MessageField('MarksValue', 2)
  name = _messages.StringField(3)


class GoogleCloudSecuritycenterV2Access(_messages.Message):
  r"""Represents an access event.

  Fields:
    callerIp: Caller's IP address, such as "*******".
    callerIpGeo: The caller IP's geolocation, which identifies where the call
      came from.
    methodName: The method that the service account called, e.g.
      "SetIamPolicy".
    principalEmail: Associated email, such as "<EMAIL>". The email
      address of the authenticated user or a service account acting on behalf
      of a third party principal making the request. For third party identity
      callers, the `principal_subject` field is populated instead of this
      field. For privacy reasons, the principal email address is sometimes
      redacted. For more information, see [Caller identities in audit
      logs](https://cloud.google.com/logging/docs/audit#user-id).
    principalSubject: A string that represents the principal_subject that is
      associated with the identity. Unlike `principal_email`,
      `principal_subject` supports principals that aren't associated with
      email addresses, such as third party principals. For most identities,
      the format is `principal://iam.googleapis.com/{identity pool
      name}/subject/{subject}`. Some GKE identities, such as GKE_WORKLOAD,
      FREEFORM, and GKE_HUB_WORKLOAD, still use the legacy format
      `serviceAccount:{identity pool name}[{subject}]`.
    serviceAccountDelegationInfo: The identity delegation history of an
      authenticated service account that made the request. The
      `serviceAccountDelegationInfo[]` object contains information about the
      real authorities that try to access Google Cloud resources by delegating
      on a service account. When multiple authorities are present, they are
      guaranteed to be sorted based on the original ordering of the identity
      delegation events.
    serviceAccountKeyName: The name of the service account key that was used
      to create or exchange credentials when authenticating the service
      account that made the request. This is a scheme-less URI full resource
      name. For example: "//iam.googleapis.com/projects/{PROJECT_ID}/serviceAc
      counts/{ACCOUNT}/keys/{key}".
    serviceName: This is the API service that the service account made a call
      to, e.g. "iam.googleapis.com"
    userAgent: The caller's user agent string associated with the finding.
    userAgentFamily: Type of user agent associated with the finding. For
      example, an operating system shell or an embedded or standalone
      application.
    userName: A string that represents a username. The username provided
      depends on the type of the finding and is likely not an IAM principal.
      For example, this can be a system username if the finding is related to
      a virtual machine, or it can be an application login username.
  """

  callerIp = _messages.StringField(1)
  callerIpGeo = _messages.MessageField('GoogleCloudSecuritycenterV2Geolocation', 2)
  methodName = _messages.StringField(3)
  principalEmail = _messages.StringField(4)
  principalSubject = _messages.StringField(5)
  serviceAccountDelegationInfo = _messages.MessageField('GoogleCloudSecuritycenterV2ServiceAccountDelegationInfo', 6, repeated=True)
  serviceAccountKeyName = _messages.StringField(7)
  serviceName = _messages.StringField(8)
  userAgent = _messages.StringField(9)
  userAgentFamily = _messages.StringField(10)
  userName = _messages.StringField(11)


class GoogleCloudSecuritycenterV2AccessReview(_messages.Message):
  r"""Conveys information about a Kubernetes access review (such as one
  returned by a [`kubectl auth
  can-i`](https://kubernetes.io/docs/reference/access-authn-
  authz/authorization/#checking-api-access) command) that was involved in a
  finding.

  Fields:
    group: The API group of the resource. "*" means all.
    name: The name of the resource being requested. Empty means all.
    ns: Namespace of the action being requested. Currently, there is no
      distinction between no namespace and all namespaces. Both are
      represented by "" (empty).
    resource: The optional resource type requested. "*" means all.
    subresource: The optional subresource type.
    verb: A Kubernetes resource API verb, like get, list, watch, create,
      update, delete, proxy. "*" means all.
    version: The API version of the resource. "*" means all.
  """

  group = _messages.StringField(1)
  name = _messages.StringField(2)
  ns = _messages.StringField(3)
  resource = _messages.StringField(4)
  subresource = _messages.StringField(5)
  verb = _messages.StringField(6)
  version = _messages.StringField(7)


class GoogleCloudSecuritycenterV2AdaptiveProtection(_messages.Message):
  r"""Information about [Google Cloud Armor Adaptive
  Protection](https://cloud.google.com/armor/docs/cloud-armor-overview#google-
  cloud-armor-adaptive-protection).

  Fields:
    confidence: A score of 0 means that there is low confidence that the
      detected event is an actual attack. A score of 1 means that there is
      high confidence that the detected event is an attack. See the [Adaptive
      Protection documentation](https://cloud.google.com/armor/docs/adaptive-
      protection-overview#configure-alert-tuning) for further explanation.
  """

  confidence = _messages.FloatField(1)


class GoogleCloudSecuritycenterV2AffectedResources(_messages.Message):
  r"""Details about resources affected by this finding.

  Fields:
    count: The count of resources affected by the finding.
  """

  count = _messages.IntegerField(1)


class GoogleCloudSecuritycenterV2Allowed(_messages.Message):
  r"""Allowed IP rule.

  Fields:
    ipRules: Optional. Optional list of allowed IP rules.
  """

  ipRules = _messages.MessageField('GoogleCloudSecuritycenterV2IpRule', 1, repeated=True)


class GoogleCloudSecuritycenterV2Application(_messages.Message):
  r"""Represents an application associated with a finding.

  Fields:
    baseUri: The base URI that identifies the network location of the
      application in which the vulnerability was detected. For example,
      `http://example.com`.
    fullUri: The full URI with payload that could be used to reproduce the
      vulnerability. For example, `http://example.com?p=aMmYgI6H`.
  """

  baseUri = _messages.StringField(1)
  fullUri = _messages.StringField(2)


class GoogleCloudSecuritycenterV2Attack(_messages.Message):
  r"""Information about DDoS attack volume and classification.

  Fields:
    classification: Type of attack, for example, 'SYN-flood', 'NTP-udp', or
      'CHARGEN-udp'.
    volumeBps: Total BPS (bytes per second) volume of attack. Deprecated -
      refer to volume_bps_long instead.
    volumeBpsLong: Total BPS (bytes per second) volume of attack.
    volumePps: Total PPS (packets per second) volume of attack. Deprecated -
      refer to volume_pps_long instead.
    volumePpsLong: Total PPS (packets per second) volume of attack.
  """

  classification = _messages.StringField(1)
  volumeBps = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  volumeBpsLong = _messages.IntegerField(3)
  volumePps = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  volumePpsLong = _messages.IntegerField(5)


class GoogleCloudSecuritycenterV2AttackExposure(_messages.Message):
  r"""An attack exposure contains the results of an attack path simulation
  run.

  Enums:
    StateValueValuesEnum: Output only. What state this AttackExposure is in.
      This captures whether or not an attack exposure has been calculated or
      not.

  Fields:
    attackExposureResult: The resource name of the attack path simulation
      result that contains the details regarding this attack exposure score.
      Example: `organizations/123/simulations/456/attackExposureResults/789`
    exposedHighValueResourcesCount: The number of high value resources that
      are exposed as a result of this finding.
    exposedLowValueResourcesCount: The number of high value resources that are
      exposed as a result of this finding.
    exposedMediumValueResourcesCount: The number of medium value resources
      that are exposed as a result of this finding.
    latestCalculationTime: The most recent time the attack exposure was
      updated on this finding.
    score: A number between 0 (inclusive) and infinity that represents how
      important this finding is to remediate. The higher the score, the more
      important it is to remediate.
    state: Output only. What state this AttackExposure is in. This captures
      whether or not an attack exposure has been calculated or not.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. What state this AttackExposure is in. This captures
    whether or not an attack exposure has been calculated or not.

    Values:
      STATE_UNSPECIFIED: The state is not specified.
      CALCULATED: The attack exposure has been calculated.
      NOT_CALCULATED: The attack exposure has not been calculated.
    """
    STATE_UNSPECIFIED = 0
    CALCULATED = 1
    NOT_CALCULATED = 2

  attackExposureResult = _messages.StringField(1)
  exposedHighValueResourcesCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  exposedLowValueResourcesCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  exposedMediumValueResourcesCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  latestCalculationTime = _messages.StringField(5)
  score = _messages.FloatField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)


class GoogleCloudSecuritycenterV2AwsAccount(_messages.Message):
  r"""An AWS account that is a member of an organization.

  Fields:
    id: The unique identifier (ID) of the account, containing exactly 12
      digits.
    name: The friendly name of this account.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2)


class GoogleCloudSecuritycenterV2AwsMetadata(_messages.Message):
  r"""AWS metadata associated with the resource, only applicable if the
  finding's cloud provider is Amazon Web Services.

  Fields:
    account: The AWS account associated with the resource.
    organization: The AWS organization associated with the resource.
    organizationalUnits: A list of AWS organizational units associated with
      the resource, ordered from lowest level (closest to the account) to
      highest level.
  """

  account = _messages.MessageField('GoogleCloudSecuritycenterV2AwsAccount', 1)
  organization = _messages.MessageField('GoogleCloudSecuritycenterV2AwsOrganization', 2)
  organizationalUnits = _messages.MessageField('GoogleCloudSecuritycenterV2AwsOrganizationalUnit', 3, repeated=True)


class GoogleCloudSecuritycenterV2AwsOrganization(_messages.Message):
  r"""An organization is a collection of accounts that are centrally managed
  together using consolidated billing, organized hierarchically with
  organizational units (OUs), and controlled with policies.

  Fields:
    id: The unique identifier (ID) for the organization. The regex pattern for
      an organization ID string requires "o-" followed by from 10 to 32
      lowercase letters or digits.
  """

  id = _messages.StringField(1)


class GoogleCloudSecuritycenterV2AwsOrganizationalUnit(_messages.Message):
  r"""An Organizational Unit (OU) is a container of AWS accounts within a root
  of an organization. Policies that are attached to an OU apply to all
  accounts contained in that OU and in any child OUs.

  Fields:
    id: The unique identifier (ID) associated with this OU. The regex pattern
      for an organizational unit ID string requires "ou-" followed by from 4
      to 32 lowercase letters or digits (the ID of the root that contains the
      OU). This string is followed by a second "-" dash and from 8 to 32
      additional lowercase letters or digits. For example, "ou-ab12-cd34ef56".
    name: The friendly name of the OU.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2)


class GoogleCloudSecuritycenterV2AzureManagementGroup(_messages.Message):
  r"""Represents an Azure management group.

  Fields:
    displayName: The display name of the Azure management group.
    id: The UUID of the Azure management group, for example,
      `********-0001-0000-0000-************`.
  """

  displayName = _messages.StringField(1)
  id = _messages.StringField(2)


class GoogleCloudSecuritycenterV2AzureMetadata(_messages.Message):
  r"""Azure metadata associated with the resource, only applicable if the
  finding's cloud provider is Microsoft Azure.

  Fields:
    managementGroups: A list of Azure management groups associated with the
      resource, ordered from lowest level (closest to the subscription) to
      highest level.
    resourceGroup: The Azure resource group associated with the resource.
    subscription: The Azure subscription associated with the resource.
    tenant: The Azure Entra tenant associated with the resource.
  """

  managementGroups = _messages.MessageField('GoogleCloudSecuritycenterV2AzureManagementGroup', 1, repeated=True)
  resourceGroup = _messages.MessageField('GoogleCloudSecuritycenterV2AzureResourceGroup', 2)
  subscription = _messages.MessageField('GoogleCloudSecuritycenterV2AzureSubscription', 3)
  tenant = _messages.MessageField('GoogleCloudSecuritycenterV2AzureTenant', 4)


class GoogleCloudSecuritycenterV2AzureResourceGroup(_messages.Message):
  r"""Represents an Azure resource group.

  Fields:
    id: The ID of the Azure resource group.
    name: The name of the Azure resource group. This is not a UUID.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2)


class GoogleCloudSecuritycenterV2AzureSubscription(_messages.Message):
  r"""Represents an Azure subscription.

  Fields:
    displayName: The display name of the Azure subscription.
    id: The UUID of the Azure subscription, for example,
      `291bba3f-e0a5-47bc-a099-3bdcb2a50a05`.
  """

  displayName = _messages.StringField(1)
  id = _messages.StringField(2)


class GoogleCloudSecuritycenterV2AzureTenant(_messages.Message):
  r"""Represents a Microsoft Entra tenant.

  Fields:
    displayName: The display name of the Azure tenant.
    id: The ID of the Microsoft Entra tenant, for example,
      "a11aaa11-aa11-1aa1-11aa-1aaa11a".
  """

  displayName = _messages.StringField(1)
  id = _messages.StringField(2)


class GoogleCloudSecuritycenterV2BackupDisasterRecovery(_messages.Message):
  r"""Information related to Google Cloud Backup and DR Service findings.

  Fields:
    appliance: The name of the Backup and DR appliance that captures, moves,
      and manages the lifecycle of backup data. For example, `backup-
      server-57137`.
    applications: The names of Backup and DR applications. An application is a
      VM, database, or file system on a managed host monitored by a backup and
      recovery appliance. For example, `centos7-01-vol00`, `centos7-01-vol01`,
      `centos7-01-vol02`.
    backupCreateTime: The timestamp at which the Backup and DR backup was
      created.
    backupTemplate: The name of a Backup and DR template which comprises one
      or more backup policies. See the [Backup and DR
      documentation](https://cloud.google.com/backup-disaster-
      recovery/docs/concepts/backup-plan#temp) for more information. For
      example, `snap-ov`.
    backupType: The backup type of the Backup and DR image. For example,
      `Snapshot`, `Remote Snapshot`, `OnVault`.
    host: The name of a Backup and DR host, which is managed by the backup and
      recovery appliance and known to the management console. The host can be
      of type Generic (for example, Compute Engine, SQL Server, Oracle DB, SMB
      file system, etc.), vCenter, or an ESX server. See the [Backup and DR
      documentation on hosts](https://cloud.google.com/backup-disaster-
      recovery/docs/configuration/manage-hosts-and-their-applications) for
      more information. For example, `centos7-01`.
    policies: The names of Backup and DR policies that are associated with a
      template and that define when to run a backup, how frequently to run a
      backup, and how long to retain the backup image. For example,
      `onvaults`.
    policyOptions: The names of Backup and DR advanced policy options of a
      policy applying to an application. See the [Backup and DR documentation
      on policy options](https://cloud.google.com/backup-disaster-
      recovery/docs/create-plan/policy-settings). For example,
      `skipofflineappsincongrp, nounmap`.
    profile: The name of the Backup and DR resource profile that specifies the
      storage media for backups of application and VM data. See the [Backup
      and DR documentation on profiles](https://cloud.google.com/backup-
      disaster-recovery/docs/concepts/backup-plan#profile). For example,
      `GCP`.
    storagePool: The name of the Backup and DR storage pool that the backup
      and recovery appliance is storing data in. The storage pool could be of
      type Cloud, Primary, Snapshot, or OnVault. See the [Backup and DR
      documentation on storage pools](https://cloud.google.com/backup-
      disaster-recovery/docs/concepts/storage-pools). For example,
      `DiskPoolOne`.
  """

  appliance = _messages.StringField(1)
  applications = _messages.StringField(2, repeated=True)
  backupCreateTime = _messages.StringField(3)
  backupTemplate = _messages.StringField(4)
  backupType = _messages.StringField(5)
  host = _messages.StringField(6)
  policies = _messages.StringField(7, repeated=True)
  policyOptions = _messages.StringField(8, repeated=True)
  profile = _messages.StringField(9)
  storagePool = _messages.StringField(10)


class GoogleCloudSecuritycenterV2BigQueryExport(_messages.Message):
  r"""Configures how to deliver Findings to BigQuery Instance.

  Fields:
    createTime: Output only. The time at which the BigQuery export was
      created. This field is set by the server and will be ignored if provided
      on export on creation.
    dataset: The dataset to write findings' updates to. Its format is
      "projects/[project_id]/datasets/[bigquery_dataset_id]". BigQuery dataset
      unique ID must contain only letters (a-z, A-Z), numbers (0-9), or
      underscores (_).
    description: The description of the export (max of 1024 characters).
    filter: Expression that defines the filter to apply across create/update
      events of findings. The expression is a list of zero or more
      restrictions combined via logical operators `AND` and `OR`. Parentheses
      are supported, and `OR` has higher precedence than `AND`. Restrictions
      have the form ` ` and may have a `-` character in front of them to
      indicate negation. The fields map to those defined in the corresponding
      resource. The supported operators are: * `=` for all value types. * `>`,
      `<`, `>=`, `<=` for integer values. * `:`, meaning substring matching,
      for strings. The supported value types are: * string literals in quotes.
      * integer literals without quotes. * boolean literals `true` and `false`
      without quotes.
    mostRecentEditor: Output only. Email address of the user who last edited
      the BigQuery export. This field is set by the server and will be ignored
      if provided on export creation or update.
    name: Identifier. The relative resource name of this export. See: https://
      cloud.google.com/apis/design/resource_names#relative_resource_name. The
      following list shows some examples: + `organizations/{organization_id}/l
      ocations/{location_id}/bigQueryExports/{export_id}` + `folders/{folder_i
      d}/locations/{location_id}/bigQueryExports/{export_id}` + `projects/{pro
      ject_id}/locations/{location_id}/bigQueryExports/{export_id}` This field
      is provided in responses, and is ignored when provided in create
      requests.
    principal: Output only. The service account that needs permission to
      create table and upload data to the BigQuery dataset.
    updateTime: Output only. The most recent time at which the BigQuery export
      was updated. This field is set by the server and will be ignored if
      provided on export creation or update.
  """

  createTime = _messages.StringField(1)
  dataset = _messages.StringField(2)
  description = _messages.StringField(3)
  filter = _messages.StringField(4)
  mostRecentEditor = _messages.StringField(5)
  name = _messages.StringField(6)
  principal = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class GoogleCloudSecuritycenterV2Binding(_messages.Message):
  r"""Represents a Kubernetes RoleBinding or ClusterRoleBinding.

  Fields:
    name: Name for the binding.
    ns: Namespace for the binding.
    role: The Role or ClusterRole referenced by the binding.
    subjects: Represents one or more subjects that are bound to the role. Not
      always available for PATCH requests.
  """

  name = _messages.StringField(1)
  ns = _messages.StringField(2)
  role = _messages.MessageField('GoogleCloudSecuritycenterV2Role', 3)
  subjects = _messages.MessageField('GoogleCloudSecuritycenterV2Subject', 4, repeated=True)


class GoogleCloudSecuritycenterV2BulkMuteFindingsResponse(_messages.Message):
  r"""The response to a BulkMute request. Contains the LRO information."""


class GoogleCloudSecuritycenterV2Chokepoint(_messages.Message):
  r"""Contains details about a chokepoint, which is a resource or resource
  group where high-risk attack paths converge, based on [attack path
  simulations] (https://cloud.google.com/security-command-center/docs/attack-
  exposure-learn#attack_path_simulations).

  Fields:
    relatedFindings: List of resource names of findings associated with this
      chokepoint. For example, organizations/123/sources/456/findings/789.
      This list will have at most 100 findings.
  """

  relatedFindings = _messages.StringField(1, repeated=True)


class GoogleCloudSecuritycenterV2CloudArmor(_messages.Message):
  r"""Fields related to Google Cloud Armor findings.

  Fields:
    adaptiveProtection: Information about potential Layer 7 DDoS attacks
      identified by [Google Cloud Armor Adaptive
      Protection](https://cloud.google.com/armor/docs/adaptive-protection-
      overview).
    attack: Information about DDoS attack volume and classification.
    duration: Duration of attack from the start until the current moment
      (updated every 5 minutes).
    requests: Information about incoming requests evaluated by [Google Cloud
      Armor security policies](https://cloud.google.com/armor/docs/security-
      policy-overview).
    securityPolicy: Information about the [Google Cloud Armor security
      policy](https://cloud.google.com/armor/docs/security-policy-overview)
      relevant to the finding.
    threatVector: Distinguish between volumetric & protocol DDoS attack and
      application layer attacks. For example, "L3_4" for Layer 3 and Layer 4
      DDoS attacks, or "L_7" for Layer 7 DDoS attacks.
  """

  adaptiveProtection = _messages.MessageField('GoogleCloudSecuritycenterV2AdaptiveProtection', 1)
  attack = _messages.MessageField('GoogleCloudSecuritycenterV2Attack', 2)
  duration = _messages.StringField(3)
  requests = _messages.MessageField('GoogleCloudSecuritycenterV2Requests', 4)
  securityPolicy = _messages.MessageField('GoogleCloudSecuritycenterV2SecurityPolicy', 5)
  threatVector = _messages.StringField(6)


class GoogleCloudSecuritycenterV2CloudDlpDataProfile(_messages.Message):
  r"""The [data profile](https://cloud.google.com/dlp/docs/data-profiles)
  associated with the finding.

  Enums:
    ParentTypeValueValuesEnum: The resource hierarchy level at which the data
      profile was generated.

  Fields:
    dataProfile: Name of the data profile, for example,
      `projects/123/locations/europe/tableProfiles/8383929`.
    parentType: The resource hierarchy level at which the data profile was
      generated.
  """

  class ParentTypeValueValuesEnum(_messages.Enum):
    r"""The resource hierarchy level at which the data profile was generated.

    Values:
      PARENT_TYPE_UNSPECIFIED: Unspecified parent type.
      ORGANIZATION: Organization-level configurations.
      PROJECT: Project-level configurations.
    """
    PARENT_TYPE_UNSPECIFIED = 0
    ORGANIZATION = 1
    PROJECT = 2

  dataProfile = _messages.StringField(1)
  parentType = _messages.EnumField('ParentTypeValueValuesEnum', 2)


class GoogleCloudSecuritycenterV2CloudDlpInspection(_messages.Message):
  r"""Details about the Cloud Data Loss Prevention (Cloud DLP) [inspection
  job](https://cloud.google.com/dlp/docs/concepts-job-triggers) that produced
  the finding.

  Fields:
    fullScan: Whether Cloud DLP scanned the complete resource or a sampled
      subset.
    infoType: The type of information (or
      *[infoType](https://cloud.google.com/dlp/docs/infotypes-reference)*)
      found, for example, `EMAIL_ADDRESS` or `STREET_ADDRESS`.
    infoTypeCount: The number of times Cloud DLP found this infoType within
      this job and resource.
    inspectJob: Name of the inspection job, for example,
      `projects/123/locations/europe/dlpJobs/i-8383929`.
  """

  fullScan = _messages.BooleanField(1)
  infoType = _messages.StringField(2)
  infoTypeCount = _messages.IntegerField(3)
  inspectJob = _messages.StringField(4)


class GoogleCloudSecuritycenterV2CloudLoggingEntry(_messages.Message):
  r"""Metadata taken from a [Cloud Logging LogEntry](https://cloud.google.com/
  logging/docs/reference/v2/rest/v2/LogEntry)

  Fields:
    insertId: A unique identifier for the log entry.
    logId: The type of the log (part of `log_name`. `log_name` is the resource
      name of the log to which this log entry belongs). For example:
      `cloudresourcemanager.googleapis.com/activity` Note that this field is
      not URL-encoded, unlike in `LogEntry`.
    resourceContainer: The organization, folder, or project of the monitored
      resource that produced this log entry.
    timestamp: The time the event described by the log entry occurred.
  """

  insertId = _messages.StringField(1)
  logId = _messages.StringField(2)
  resourceContainer = _messages.StringField(3)
  timestamp = _messages.StringField(4)


class GoogleCloudSecuritycenterV2Compliance(_messages.Message):
  r"""Contains compliance information about a security standard indicating
  unmet recommendations.

  Fields:
    ids: Policies within the standard or benchmark, for example, A.12.4.1
    standard: Industry-wide compliance standards or benchmarks, such as CIS,
      PCI, and OWASP.
    version: Version of the standard or benchmark, for example, 1.1
  """

  ids = _messages.StringField(1, repeated=True)
  standard = _messages.StringField(2)
  version = _messages.StringField(3)


class GoogleCloudSecuritycenterV2Connection(_messages.Message):
  r"""Contains information about the IP connection associated with the
  finding.

  Enums:
    ProtocolValueValuesEnum: IANA Internet Protocol Number such as TCP(6) and
      UDP(17).

  Fields:
    destinationIp: Destination IP address. Not present for sockets that are
      listening and not connected.
    destinationPort: Destination port. Not present for sockets that are
      listening and not connected.
    protocol: IANA Internet Protocol Number such as TCP(6) and UDP(17).
    sourceIp: Source IP address.
    sourcePort: Source port.
  """

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""IANA Internet Protocol Number such as TCP(6) and UDP(17).

    Values:
      PROTOCOL_UNSPECIFIED: Unspecified protocol (not HOPOPT).
      ICMP: Internet Control Message Protocol.
      TCP: Transmission Control Protocol.
      UDP: User Datagram Protocol.
      GRE: Generic Routing Encapsulation.
      ESP: Encap Security Payload.
    """
    PROTOCOL_UNSPECIFIED = 0
    ICMP = 1
    TCP = 2
    UDP = 3
    GRE = 4
    ESP = 5

  destinationIp = _messages.StringField(1)
  destinationPort = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 3)
  sourceIp = _messages.StringField(4)
  sourcePort = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class GoogleCloudSecuritycenterV2Contact(_messages.Message):
  r"""The email address of a contact.

  Fields:
    email: An email address. For example, "`<EMAIL>`".
  """

  email = _messages.StringField(1)


class GoogleCloudSecuritycenterV2ContactDetails(_messages.Message):
  r"""Details about specific contacts

  Fields:
    contacts: A list of contacts
  """

  contacts = _messages.MessageField('GoogleCloudSecuritycenterV2Contact', 1, repeated=True)


class GoogleCloudSecuritycenterV2Container(_messages.Message):
  r"""Container associated with the finding.

  Fields:
    createTime: The time that the container was created.
    imageId: Optional container image ID, if provided by the container
      runtime. Uniquely identifies the container image launched using a
      container image digest.
    labels: Container labels, as provided by the container runtime.
    name: Name of the container.
    uri: Container image URI provided when configuring a pod or container.
      This string can identify a container image version using mutable tags.
  """

  createTime = _messages.StringField(1)
  imageId = _messages.StringField(2)
  labels = _messages.MessageField('GoogleCloudSecuritycenterV2Label', 3, repeated=True)
  name = _messages.StringField(4)
  uri = _messages.StringField(5)


class GoogleCloudSecuritycenterV2Cve(_messages.Message):
  r"""CVE stands for Common Vulnerabilities and Exposures. Information from
  the [CVE record](https://www.cve.org/ResourcesSupport/Glossary) that
  describes this vulnerability.

  Enums:
    ExploitationActivityValueValuesEnum: The exploitation activity of the
      vulnerability in the wild.
    ImpactValueValuesEnum: The potential impact of the vulnerability if it was
      to be exploited.

  Fields:
    cvssv3: Describe Common Vulnerability Scoring System specified at
      https://www.first.org/cvss/v3.1/specification-document
    exploitReleaseDate: Date the first publicly available exploit or PoC was
      released.
    exploitationActivity: The exploitation activity of the vulnerability in
      the wild.
    firstExploitationDate: Date of the earliest known exploitation.
    id: The unique identifier for the vulnerability. e.g. CVE-2021-34527
    impact: The potential impact of the vulnerability if it was to be
      exploited.
    observedInTheWild: Whether or not the vulnerability has been observed in
      the wild.
    references: Additional information about the CVE. e.g.
      https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-34527
    upstreamFixAvailable: Whether upstream fix is available for the CVE.
    zeroDay: Whether or not the vulnerability was zero day when the finding
      was published.
  """

  class ExploitationActivityValueValuesEnum(_messages.Enum):
    r"""The exploitation activity of the vulnerability in the wild.

    Values:
      EXPLOITATION_ACTIVITY_UNSPECIFIED: Invalid or empty value.
      WIDE: Exploitation has been reported or confirmed to widely occur.
      CONFIRMED: Limited reported or confirmed exploitation activities.
      AVAILABLE: Exploit is publicly available.
      ANTICIPATED: No known exploitation activity, but has a high potential
        for exploitation.
      NO_KNOWN: No known exploitation activity.
    """
    EXPLOITATION_ACTIVITY_UNSPECIFIED = 0
    WIDE = 1
    CONFIRMED = 2
    AVAILABLE = 3
    ANTICIPATED = 4
    NO_KNOWN = 5

  class ImpactValueValuesEnum(_messages.Enum):
    r"""The potential impact of the vulnerability if it was to be exploited.

    Values:
      RISK_RATING_UNSPECIFIED: Invalid or empty value.
      LOW: Exploitation would have little to no security impact.
      MEDIUM: Exploitation would enable attackers to perform activities, or
        could allow attackers to have a direct impact, but would require
        additional steps.
      HIGH: Exploitation would enable attackers to have a notable direct
        impact without needing to overcome any major mitigating factors.
      CRITICAL: Exploitation would fundamentally undermine the security of
        affected systems, enable actors to perform significant attacks with
        minimal effort, with little to no mitigating factors to overcome.
    """
    RISK_RATING_UNSPECIFIED = 0
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

  cvssv3 = _messages.MessageField('GoogleCloudSecuritycenterV2Cvssv3', 1)
  exploitReleaseDate = _messages.StringField(2)
  exploitationActivity = _messages.EnumField('ExploitationActivityValueValuesEnum', 3)
  firstExploitationDate = _messages.StringField(4)
  id = _messages.StringField(5)
  impact = _messages.EnumField('ImpactValueValuesEnum', 6)
  observedInTheWild = _messages.BooleanField(7)
  references = _messages.MessageField('GoogleCloudSecuritycenterV2Reference', 8, repeated=True)
  upstreamFixAvailable = _messages.BooleanField(9)
  zeroDay = _messages.BooleanField(10)


class GoogleCloudSecuritycenterV2Cvssv3(_messages.Message):
  r"""Common Vulnerability Scoring System version 3.

  Enums:
    AttackComplexityValueValuesEnum: This metric describes the conditions
      beyond the attacker's control that must exist in order to exploit the
      vulnerability.
    AttackVectorValueValuesEnum: Base Metrics Represents the intrinsic
      characteristics of a vulnerability that are constant over time and
      across user environments. This metric reflects the context by which
      vulnerability exploitation is possible.
    AvailabilityImpactValueValuesEnum: This metric measures the impact to the
      availability of the impacted component resulting from a successfully
      exploited vulnerability.
    ConfidentialityImpactValueValuesEnum: This metric measures the impact to
      the confidentiality of the information resources managed by a software
      component due to a successfully exploited vulnerability.
    IntegrityImpactValueValuesEnum: This metric measures the impact to
      integrity of a successfully exploited vulnerability.
    PrivilegesRequiredValueValuesEnum: This metric describes the level of
      privileges an attacker must possess before successfully exploiting the
      vulnerability.
    ScopeValueValuesEnum: The Scope metric captures whether a vulnerability in
      one vulnerable component impacts resources in components beyond its
      security scope.
    UserInteractionValueValuesEnum: This metric captures the requirement for a
      human user, other than the attacker, to participate in the successful
      compromise of the vulnerable component.

  Fields:
    attackComplexity: This metric describes the conditions beyond the
      attacker's control that must exist in order to exploit the
      vulnerability.
    attackVector: Base Metrics Represents the intrinsic characteristics of a
      vulnerability that are constant over time and across user environments.
      This metric reflects the context by which vulnerability exploitation is
      possible.
    availabilityImpact: This metric measures the impact to the availability of
      the impacted component resulting from a successfully exploited
      vulnerability.
    baseScore: The base score is a function of the base metric scores.
    confidentialityImpact: This metric measures the impact to the
      confidentiality of the information resources managed by a software
      component due to a successfully exploited vulnerability.
    integrityImpact: This metric measures the impact to integrity of a
      successfully exploited vulnerability.
    privilegesRequired: This metric describes the level of privileges an
      attacker must possess before successfully exploiting the vulnerability.
    scope: The Scope metric captures whether a vulnerability in one vulnerable
      component impacts resources in components beyond its security scope.
    userInteraction: This metric captures the requirement for a human user,
      other than the attacker, to participate in the successful compromise of
      the vulnerable component.
  """

  class AttackComplexityValueValuesEnum(_messages.Enum):
    r"""This metric describes the conditions beyond the attacker's control
    that must exist in order to exploit the vulnerability.

    Values:
      ATTACK_COMPLEXITY_UNSPECIFIED: Invalid value.
      ATTACK_COMPLEXITY_LOW: Specialized access conditions or extenuating
        circumstances do not exist. An attacker can expect repeatable success
        when attacking the vulnerable component.
      ATTACK_COMPLEXITY_HIGH: A successful attack depends on conditions beyond
        the attacker's control. That is, a successful attack cannot be
        accomplished at will, but requires the attacker to invest in some
        measurable amount of effort in preparation or execution against the
        vulnerable component before a successful attack can be expected.
    """
    ATTACK_COMPLEXITY_UNSPECIFIED = 0
    ATTACK_COMPLEXITY_LOW = 1
    ATTACK_COMPLEXITY_HIGH = 2

  class AttackVectorValueValuesEnum(_messages.Enum):
    r"""Base Metrics Represents the intrinsic characteristics of a
    vulnerability that are constant over time and across user environments.
    This metric reflects the context by which vulnerability exploitation is
    possible.

    Values:
      ATTACK_VECTOR_UNSPECIFIED: Invalid value.
      ATTACK_VECTOR_NETWORK: The vulnerable component is bound to the network
        stack and the set of possible attackers extends beyond the other
        options listed below, up to and including the entire Internet.
      ATTACK_VECTOR_ADJACENT: The vulnerable component is bound to the network
        stack, but the attack is limited at the protocol level to a logically
        adjacent topology.
      ATTACK_VECTOR_LOCAL: The vulnerable component is not bound to the
        network stack and the attacker's path is via read/write/execute
        capabilities.
      ATTACK_VECTOR_PHYSICAL: The attack requires the attacker to physically
        touch or manipulate the vulnerable component.
    """
    ATTACK_VECTOR_UNSPECIFIED = 0
    ATTACK_VECTOR_NETWORK = 1
    ATTACK_VECTOR_ADJACENT = 2
    ATTACK_VECTOR_LOCAL = 3
    ATTACK_VECTOR_PHYSICAL = 4

  class AvailabilityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to the availability of the impacted
    component resulting from a successfully exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class ConfidentialityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to the confidentiality of the
    information resources managed by a software component due to a
    successfully exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class IntegrityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to integrity of a successfully
    exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class PrivilegesRequiredValueValuesEnum(_messages.Enum):
    r"""This metric describes the level of privileges an attacker must possess
    before successfully exploiting the vulnerability.

    Values:
      PRIVILEGES_REQUIRED_UNSPECIFIED: Invalid value.
      PRIVILEGES_REQUIRED_NONE: The attacker is unauthorized prior to attack,
        and therefore does not require any access to settings or files of the
        vulnerable system to carry out an attack.
      PRIVILEGES_REQUIRED_LOW: The attacker requires privileges that provide
        basic user capabilities that could normally affect only settings and
        files owned by a user. Alternatively, an attacker with Low privileges
        has the ability to access only non-sensitive resources.
      PRIVILEGES_REQUIRED_HIGH: The attacker requires privileges that provide
        significant (e.g., administrative) control over the vulnerable
        component allowing access to component-wide settings and files.
    """
    PRIVILEGES_REQUIRED_UNSPECIFIED = 0
    PRIVILEGES_REQUIRED_NONE = 1
    PRIVILEGES_REQUIRED_LOW = 2
    PRIVILEGES_REQUIRED_HIGH = 3

  class ScopeValueValuesEnum(_messages.Enum):
    r"""The Scope metric captures whether a vulnerability in one vulnerable
    component impacts resources in components beyond its security scope.

    Values:
      SCOPE_UNSPECIFIED: Invalid value.
      SCOPE_UNCHANGED: An exploited vulnerability can only affect resources
        managed by the same security authority.
      SCOPE_CHANGED: An exploited vulnerability can affect resources beyond
        the security scope managed by the security authority of the vulnerable
        component.
    """
    SCOPE_UNSPECIFIED = 0
    SCOPE_UNCHANGED = 1
    SCOPE_CHANGED = 2

  class UserInteractionValueValuesEnum(_messages.Enum):
    r"""This metric captures the requirement for a human user, other than the
    attacker, to participate in the successful compromise of the vulnerable
    component.

    Values:
      USER_INTERACTION_UNSPECIFIED: Invalid value.
      USER_INTERACTION_NONE: The vulnerable system can be exploited without
        interaction from any user.
      USER_INTERACTION_REQUIRED: Successful exploitation of this vulnerability
        requires a user to take some action before the vulnerability can be
        exploited.
    """
    USER_INTERACTION_UNSPECIFIED = 0
    USER_INTERACTION_NONE = 1
    USER_INTERACTION_REQUIRED = 2

  attackComplexity = _messages.EnumField('AttackComplexityValueValuesEnum', 1)
  attackVector = _messages.EnumField('AttackVectorValueValuesEnum', 2)
  availabilityImpact = _messages.EnumField('AvailabilityImpactValueValuesEnum', 3)
  baseScore = _messages.FloatField(4)
  confidentialityImpact = _messages.EnumField('ConfidentialityImpactValueValuesEnum', 5)
  integrityImpact = _messages.EnumField('IntegrityImpactValueValuesEnum', 6)
  privilegesRequired = _messages.EnumField('PrivilegesRequiredValueValuesEnum', 7)
  scope = _messages.EnumField('ScopeValueValuesEnum', 8)
  userInteraction = _messages.EnumField('UserInteractionValueValuesEnum', 9)


class GoogleCloudSecuritycenterV2Cwe(_messages.Message):
  r"""CWE stands for Common Weakness Enumeration. Information about this
  weakness, as described by [CWE](https://cwe.mitre.org/).

  Fields:
    id: The CWE identifier, e.g. CWE-94
    references: Any reference to the details on the CWE, for example,
      https://cwe.mitre.org/data/definitions/94.html
  """

  id = _messages.StringField(1)
  references = _messages.MessageField('GoogleCloudSecuritycenterV2Reference', 2, repeated=True)


class GoogleCloudSecuritycenterV2DataAccessEvent(_messages.Message):
  r"""Details about a data access attempt made by a principal not authorized
  under applicable data security policy.

  Enums:
    OperationValueValuesEnum: The operation performed by the principal to
      access the data.

  Fields:
    eventId: Unique identifier for data access event.
    eventTime: Timestamp of data access event.
    operation: The operation performed by the principal to access the data.
    principalEmail: The email address of the principal that accessed the data.
      The principal could be a user account, service account, Google group, or
      other.
  """

  class OperationValueValuesEnum(_messages.Enum):
    r"""The operation performed by the principal to access the data.

    Values:
      OPERATION_UNSPECIFIED: The operation is unspecified.
      READ: Represents a read operation.
      MOVE: Represents a move operation.
      COPY: Represents a copy operation.
    """
    OPERATION_UNSPECIFIED = 0
    READ = 1
    MOVE = 2
    COPY = 3

  eventId = _messages.StringField(1)
  eventTime = _messages.StringField(2)
  operation = _messages.EnumField('OperationValueValuesEnum', 3)
  principalEmail = _messages.StringField(4)


class GoogleCloudSecuritycenterV2DataFlowEvent(_messages.Message):
  r"""Details about a data flow event, in which either the data is moved to or
  is accessed from a non-compliant geo-location, as defined in the applicable
  data security policy.

  Enums:
    OperationValueValuesEnum: The operation performed by the principal for the
      data flow event.

  Fields:
    eventId: Unique identifier for data flow event.
    eventTime: Timestamp of data flow event.
    operation: The operation performed by the principal for the data flow
      event.
    principalEmail: The email address of the principal that initiated the data
      flow event. The principal could be a user account, service account,
      Google group, or other.
    violatedLocation: Non-compliant location of the principal or the data
      destination.
  """

  class OperationValueValuesEnum(_messages.Enum):
    r"""The operation performed by the principal for the data flow event.

    Values:
      OPERATION_UNSPECIFIED: The operation is unspecified.
      READ: Represents a read operation.
      MOVE: Represents a move operation.
      COPY: Represents a copy operation.
    """
    OPERATION_UNSPECIFIED = 0
    READ = 1
    MOVE = 2
    COPY = 3

  eventId = _messages.StringField(1)
  eventTime = _messages.StringField(2)
  operation = _messages.EnumField('OperationValueValuesEnum', 3)
  principalEmail = _messages.StringField(4)
  violatedLocation = _messages.StringField(5)


class GoogleCloudSecuritycenterV2DataRetentionDeletionEvent(_messages.Message):
  r"""Details about data retention deletion violations, in which the data is
  non-compliant based on their retention or deletion time, as defined in the
  applicable data security policy. The Data Retention Deletion (DRD) control
  is a control of the DSPM (Data Security Posture Management) suite that
  enables organizations to manage data retention and deletion policies in
  compliance with regulations, such as GDPR and CRPA. DRD supports two primary
  policy types: maximum storage length (max TTL) and minimum storage length
  (min TTL). Both are aimed at helping organizations meet regulatory and data
  management commitments.

  Enums:
    EventTypeValueValuesEnum: Type of the DRD event.

  Fields:
    dataObjectCount: Number of objects that violated the policy for this
      resource. If the number is less than 1,000, then the value of this field
      is the exact number. If the number of objects that violated the policy
      is greater than or equal to 1,000, then the value of this field is 1000.
    eventDetectionTime: Timestamp indicating when the event was detected.
    eventType: Type of the DRD event.
    maxRetentionAllowed: Maximum duration of retention allowed from the DRD
      control. This comes from the DRD control where users set a max TTL for
      their data. For example, suppose that a user sets the max TTL for a
      Cloud Storage bucket to 90 days. However, an object in that bucket is
      100 days old. In this case, a DataRetentionDeletionEvent will be
      generated for that Cloud Storage bucket, and the max_retention_allowed
      is 90 days.
  """

  class EventTypeValueValuesEnum(_messages.Enum):
    r"""Type of the DRD event.

    Values:
      EVENT_TYPE_UNSPECIFIED: Unspecified event type.
      EVENT_TYPE_MAX_TTL_EXCEEDED: The maximum retention time has been
        exceeded.
    """
    EVENT_TYPE_UNSPECIFIED = 0
    EVENT_TYPE_MAX_TTL_EXCEEDED = 1

  dataObjectCount = _messages.IntegerField(1)
  eventDetectionTime = _messages.StringField(2)
  eventType = _messages.EnumField('EventTypeValueValuesEnum', 3)
  maxRetentionAllowed = _messages.StringField(4)


class GoogleCloudSecuritycenterV2Database(_messages.Message):
  r"""Represents database access information, such as queries. A database may
  be a sub-resource of an instance (as in the case of Cloud SQL instances or
  Cloud Spanner instances), or the database instance itself. Some database
  resources might not have the [full resource
  name](https://google.aip.dev/122#full-resource-names) populated because
  these resource types, such as Cloud SQL databases, are not yet supported by
  Cloud Asset Inventory. In these cases only the display name is provided.

  Fields:
    displayName: The human-readable name of the database that the user
      connected to.
    grantees: The target usernames, roles, or groups of an SQL privilege
      grant, which is not an IAM policy change.
    name: Some database resources may not have the [full resource
      name](https://google.aip.dev/122#full-resource-names) populated because
      these resource types are not yet supported by Cloud Asset Inventory
      (e.g. Cloud SQL databases). In these cases only the display name will be
      provided. The [full resource name](https://google.aip.dev/122#full-
      resource-names) of the database that the user connected to, if it is
      supported by Cloud Asset Inventory.
    query: The SQL statement that is associated with the database access.
    userName: The username used to connect to the database. The username might
      not be an IAM principal and does not have a set format.
    version: The version of the database, for example, POSTGRES_14. See [the
      complete list](https://cloud.google.com/sql/docs/mysql/admin-
      api/rest/v1/SqlDatabaseVersion).
  """

  displayName = _messages.StringField(1)
  grantees = _messages.StringField(2, repeated=True)
  name = _messages.StringField(3)
  query = _messages.StringField(4)
  userName = _messages.StringField(5)
  version = _messages.StringField(6)


class GoogleCloudSecuritycenterV2Denied(_messages.Message):
  r"""Denied IP rule.

  Fields:
    ipRules: Optional. Optional list of denied IP rules.
  """

  ipRules = _messages.MessageField('GoogleCloudSecuritycenterV2IpRule', 1, repeated=True)


class GoogleCloudSecuritycenterV2Detection(_messages.Message):
  r"""Memory hash detection contributing to the binary family match.

  Fields:
    binary: The name of the binary associated with the memory hash signature
      detection.
    percentPagesMatched: The percentage of memory page hashes in the signature
      that were matched.
  """

  binary = _messages.StringField(1)
  percentPagesMatched = _messages.FloatField(2)


class GoogleCloudSecuritycenterV2Disk(_messages.Message):
  r"""Contains information about the disk associated with the finding.

  Fields:
    name: The name of the disk, for example,
      "https://www.googleapis.com/compute/v1/projects/{project-
      id}/zones/{zone-id}/disks/{disk-id}".
  """

  name = _messages.StringField(1)


class GoogleCloudSecuritycenterV2DiskPath(_messages.Message):
  r"""Path of the file in terms of underlying disk/partition identifiers.

  Fields:
    partitionUuid: UUID of the partition (format
      https://wiki.archlinux.org/title/persistent_block_device_naming#by-uuid)
    relativePath: Relative path of the file in the partition as a JSON encoded
      string. Example: /home/<USER>/executable_file.sh
  """

  partitionUuid = _messages.StringField(1)
  relativePath = _messages.StringField(2)


class GoogleCloudSecuritycenterV2DynamicMuteRecord(_messages.Message):
  r"""The record of a dynamic mute rule that matches the finding.

  Fields:
    matchTime: When the dynamic mute rule first matched the finding.
    muteConfig: The relative resource name of the mute rule, represented by a
      mute config, that created this record, for example
      `organizations/123/muteConfigs/mymuteconfig` or
      `organizations/123/locations/global/muteConfigs/mymuteconfig`.
  """

  matchTime = _messages.StringField(1)
  muteConfig = _messages.StringField(2)


class GoogleCloudSecuritycenterV2EnvironmentVariable(_messages.Message):
  r"""A name-value pair representing an environment variable used in an
  operating system process.

  Fields:
    name: Environment variable name as a JSON encoded string.
    val: Environment variable value as a JSON encoded string.
  """

  name = _messages.StringField(1)
  val = _messages.StringField(2)


class GoogleCloudSecuritycenterV2ExfilResource(_messages.Message):
  r"""Resource where data was exfiltrated from or exfiltrated to.

  Fields:
    components: Subcomponents of the asset that was exfiltrated, like URIs
      used during exfiltration, table names, databases, and filenames. For
      example, multiple tables might have been exfiltrated from the same Cloud
      SQL instance, or multiple files might have been exfiltrated from the
      same Cloud Storage bucket.
    name: The resource's [full resource name](https://cloud.google.com/apis/de
      sign/resource_names#full_resource_name).
  """

  components = _messages.StringField(1, repeated=True)
  name = _messages.StringField(2)


class GoogleCloudSecuritycenterV2Exfiltration(_messages.Message):
  r"""Exfiltration represents a data exfiltration attempt from one or more
  sources to one or more targets. The `sources` attribute lists the sources of
  the exfiltrated data. The `targets` attribute lists the destinations the
  data was copied to.

  Fields:
    sources: If there are multiple sources, then the data is considered
      "joined" between them. For instance, BigQuery can join multiple tables,
      and each table would be considered a source.
    targets: If there are multiple targets, each target would get a complete
      copy of the "joined" source data.
    totalExfiltratedBytes: Total exfiltrated bytes processed for the entire
      job.
  """

  sources = _messages.MessageField('GoogleCloudSecuritycenterV2ExfilResource', 1, repeated=True)
  targets = _messages.MessageField('GoogleCloudSecuritycenterV2ExfilResource', 2, repeated=True)
  totalExfiltratedBytes = _messages.IntegerField(3)


class GoogleCloudSecuritycenterV2ExternalSystem(_messages.Message):
  r"""Representation of third party SIEM/SOAR fields within SCC.

  Fields:
    assignees: References primary/secondary etc assignees in the external
      system.
    caseCloseTime: The time when the case was closed, as reported by the
      external system.
    caseCreateTime: The time when the case was created, as reported by the
      external system.
    casePriority: The priority of the finding's corresponding case in the
      external system.
    caseSla: The SLA of the finding's corresponding case in the external
      system.
    caseUri: The link to the finding's corresponding case in the external
      system.
    externalSystemUpdateTime: The time when the case was last updated, as
      reported by the external system.
    externalUid: The identifier that's used to track the finding's
      corresponding case in the external system.
    name: Full resource name of the external system. The following list shows
      some examples: +
      `organizations/1234/sources/5678/findings/123456/externalSystems/jira` +
      `organizations/1234/sources/5678/locations/us/findings/123456/externalSy
      stems/jira` +
      `folders/1234/sources/5678/findings/123456/externalSystems/jira` + `fold
      ers/1234/sources/5678/locations/us/findings/123456/externalSystems/jira`
      + `projects/1234/sources/5678/findings/123456/externalSystems/jira` + `p
      rojects/1234/sources/5678/locations/us/findings/123456/externalSystems/j
      ira`
    status: The most recent status of the finding's corresponding case, as
      reported by the external system.
    ticketInfo: Information about the ticket, if any, that is being used to
      track the resolution of the issue that is identified by this finding.
  """

  assignees = _messages.StringField(1, repeated=True)
  caseCloseTime = _messages.StringField(2)
  caseCreateTime = _messages.StringField(3)
  casePriority = _messages.StringField(4)
  caseSla = _messages.StringField(5)
  caseUri = _messages.StringField(6)
  externalSystemUpdateTime = _messages.StringField(7)
  externalUid = _messages.StringField(8)
  name = _messages.StringField(9)
  status = _messages.StringField(10)
  ticketInfo = _messages.MessageField('GoogleCloudSecuritycenterV2TicketInfo', 11)


class GoogleCloudSecuritycenterV2File(_messages.Message):
  r"""File information about the related binary/library used by an executable,
  or the script used by a script interpreter

  Fields:
    contents: Prefix of the file contents as a JSON-encoded string.
    diskPath: Path of the file in terms of underlying disk/partition
      identifiers.
    hashedSize: The length in bytes of the file prefix that was hashed. If
      hashed_size == size, any hashes reported represent the entire file.
    operations: Operation(s) performed on a file.
    partiallyHashed: True when the hash covers only a prefix of the file.
    path: Absolute path of the file as a JSON encoded string.
    sha256: SHA256 hash of the first hashed_size bytes of the file encoded as
      a hex string. If hashed_size == size, sha256 represents the SHA256 hash
      of the entire file.
    size: Size of the file in bytes.
  """

  contents = _messages.StringField(1)
  diskPath = _messages.MessageField('GoogleCloudSecuritycenterV2DiskPath', 2)
  hashedSize = _messages.IntegerField(3)
  operations = _messages.MessageField('GoogleCloudSecuritycenterV2FileOperation', 4, repeated=True)
  partiallyHashed = _messages.BooleanField(5)
  path = _messages.StringField(6)
  sha256 = _messages.StringField(7)
  size = _messages.IntegerField(8)


class GoogleCloudSecuritycenterV2FileOperation(_messages.Message):
  r"""Operation(s) performed on a file.

  Enums:
    TypeValueValuesEnum: The type of the operation

  Fields:
    type: The type of the operation
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the operation

    Values:
      OPERATION_TYPE_UNSPECIFIED: The operation is unspecified.
      OPEN: Represents an open operation.
      READ: Represents a read operation.
      RENAME: Represents a rename operation.
      WRITE: Represents a write operation.
      EXECUTE: Represents an execute operation.
    """
    OPERATION_TYPE_UNSPECIFIED = 0
    OPEN = 1
    READ = 2
    RENAME = 3
    WRITE = 4
    EXECUTE = 5

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class GoogleCloudSecuritycenterV2Finding(_messages.Message):
  r"""Security Command Center finding. A finding is a record of assessment
  data like security, risk, health, or privacy, that is ingested into Security
  Command Center for presentation, notification, analysis, policy testing, and
  enforcement. For example, a cross-site scripting (XSS) vulnerability in an
  App Engine application is a finding.

  Enums:
    FindingClassValueValuesEnum: The class of the finding.
    MuteValueValuesEnum: Indicates the mute state of a finding (either muted,
      unmuted or undefined). Unlike other attributes of a finding, a finding
      provider shouldn't set the value of mute.
    SeverityValueValuesEnum: The severity of the finding. This field is
      managed by the source that writes the finding.
    StateValueValuesEnum: Output only. The state of the finding.

  Messages:
    ContactsValue: Output only. Map containing the points of contact for the
      given finding. The key represents the type of contact, while the value
      contains a list of all the contacts that pertain. Please refer to:
      https://cloud.google.com/resource-manager/docs/managing-notification-
      contacts#notification-categories { "security": { "contacts": [ {
      "email": "<EMAIL>" }, { "email": "<EMAIL>" } ] }
      }
    ExternalSystemsValue: Output only. Third party SIEM/SOAR fields within
      SCC, contains external system information and external system finding
      fields.
    SourcePropertiesValue: Source specific properties. These properties are
      managed by the source that writes the finding. The key names in the
      source_properties map must be between 1 and 255 characters, and must
      start with a letter and contain alphanumeric characters or underscores
      only.

  Fields:
    access: Access details associated with the finding, such as more
      information on the caller, which method was accessed, and from where.
    affectedResources: AffectedResources associated with the finding.
    application: Represents an application associated with the finding.
    attackExposure: The results of an attack path simulation relevant to this
      finding.
    backupDisasterRecovery: Fields related to Backup and DR findings.
    canonicalName: Output only. The canonical name of the finding. The
      following list shows some examples: + `organizations/{organization_id}/s
      ources/{source_id}/findings/{finding_id}` + `organizations/{organization
      _id}/sources/{source_id}/locations/{location_id}/findings/{finding_id}`
      + `folders/{folder_id}/sources/{source_id}/findings/{finding_id}` + `fol
      ders/{folder_id}/sources/{source_id}/locations/{location_id}/findings/{f
      inding_id}` +
      `projects/{project_id}/sources/{source_id}/findings/{finding_id}` + `pro
      jects/{project_id}/sources/{source_id}/locations/{location_id}/findings/
      {finding_id}` The prefix is the closest CRM ancestor of the resource
      associated with the finding.
    category: Immutable. The additional taxonomy group within findings from a
      given source. Example: "XSS_FLASH_INJECTION"
    chokepoint: Contains details about a chokepoint, which is a resource or
      resource group where high-risk attack paths converge, based on [attack
      path simulations] (https://cloud.google.com/security-command-
      center/docs/attack-exposure-learn#attack_path_simulations). This field
      cannot be updated. Its value is ignored in all update requests.
    cloudArmor: Fields related to Cloud Armor findings.
    cloudDlpDataProfile: Cloud DLP data profile that is associated with the
      finding.
    cloudDlpInspection: Cloud Data Loss Prevention (Cloud DLP) inspection
      results that are associated with the finding.
    compliances: Contains compliance information for security standards
      associated to the finding.
    connections: Contains information about the IP connection associated with
      the finding.
    contacts: Output only. Map containing the points of contact for the given
      finding. The key represents the type of contact, while the value
      contains a list of all the contacts that pertain. Please refer to:
      https://cloud.google.com/resource-manager/docs/managing-notification-
      contacts#notification-categories { "security": { "contacts": [ {
      "email": "<EMAIL>" }, { "email": "<EMAIL>" } ] }
      }
    containers: Containers associated with the finding. This field provides
      information for both Kubernetes and non-Kubernetes containers.
    createTime: Output only. The time at which the finding was created in
      Security Command Center.
    dataAccessEvents: Data access events associated with the finding.
    dataFlowEvents: Data flow events associated with the finding.
    dataRetentionDeletionEvents: Data retention deletion events associated
      with the finding.
    database: Database associated with the finding.
    description: Contains more details about the finding.
    disk: Disk associated with the finding.
    eventTime: The time the finding was first detected. If an existing finding
      is updated, then this is the time the update occurred. For example, if
      the finding represents an open firewall, this property captures the time
      the detector believes the firewall became open. The accuracy is
      determined by the detector. If the finding is later resolved, then this
      time reflects when the finding was resolved. This must not be set to a
      value greater than the current timestamp.
    exfiltration: Represents exfiltrations associated with the finding.
    externalSystems: Output only. Third party SIEM/SOAR fields within SCC,
      contains external system information and external system finding fields.
    externalUri: The URI that, if available, points to a web page outside of
      Security Command Center where additional information about the finding
      can be found. This field is guaranteed to be either empty or a well
      formed URL.
    files: File associated with the finding.
    findingClass: The class of the finding.
    groupMemberships: Contains details about groups of which this finding is a
      member. A group is a collection of findings that are related in some
      way. This field cannot be updated. Its value is ignored in all update
      requests.
    iamBindings: Represents IAM bindings associated with the finding.
    indicator: Represents what's commonly known as an *indicator of
      compromise* (IoC) in computer forensics. This is an artifact observed on
      a network or in an operating system that, with high confidence,
      indicates a computer intrusion. For more information, see [Indicator of
      compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise).
    ipRules: IP rules associated with the finding.
    job: Job associated with the finding.
    kernelRootkit: Signature of the kernel rootkit.
    kubernetes: Kubernetes resources associated with the finding.
    loadBalancers: The load balancers associated with the finding.
    logEntries: Log entries that are relevant to the finding.
    mitreAttack: MITRE ATT&CK tactics and techniques related to this finding.
      See: https://attack.mitre.org
    moduleName: Unique identifier of the module which generated the finding.
      Example: folders/598186756061/securityHealthAnalyticsSettings/customModu
      les/56799441161885
    mute: Indicates the mute state of a finding (either muted, unmuted or
      undefined). Unlike other attributes of a finding, a finding provider
      shouldn't set the value of mute.
    muteInfo: Output only. The mute information regarding this finding.
    muteInitiator: Records additional information about the mute operation,
      for example, the [mute configuration](https://cloud.google.com/security-
      command-center/docs/how-to-mute-findings) that muted the finding and the
      user who muted the finding.
    muteUpdateTime: Output only. The most recent time this finding was muted
      or unmuted.
    name: The [relative resource name](https://cloud.google.com/apis/design/re
      source_names#relative_resource_name) of the finding. The following list
      shows some examples: + `organizations/{organization_id}/sources/{source_
      id}/findings/{finding_id}` + `organizations/{organization_id}/sources/{s
      ource_id}/locations/{location_id}/findings/{finding_id}` +
      `folders/{folder_id}/sources/{source_id}/findings/{finding_id}` + `folde
      rs/{folder_id}/sources/{source_id}/locations/{location_id}/findings/{fin
      ding_id}` +
      `projects/{project_id}/sources/{source_id}/findings/{finding_id}` + `pro
      jects/{project_id}/sources/{source_id}/locations/{location_id}/findings/
      {finding_id}`
    networks: Represents the VPC networks that the resource is attached to.
    nextSteps: Steps to address the finding.
    notebook: Notebook associated with the finding.
    orgPolicies: Contains information about the org policies associated with
      the finding.
    parent: The relative resource name of the source and location the finding
      belongs to. See: https://cloud.google.com/apis/design/resource_names#rel
      ative_resource_name This field is immutable after creation time. The
      following list shows some examples: +
      `organizations/{organization_id}/sources/{source_id}` +
      `folders/{folders_id}/sources/{source_id}` +
      `projects/{projects_id}/sources/{source_id}` + `organizations/{organizat
      ion_id}/sources/{source_id}/locations/{location_id}` +
      `folders/{folders_id}/sources/{source_id}/locations/{location_id}` +
      `projects/{projects_id}/sources/{source_id}/locations/{location_id}`
    parentDisplayName: Output only. The human readable display name of the
      finding source such as "Event Threat Detection" or "Security Health
      Analytics".
    processes: Represents operating system processes associated with the
      Finding.
    resourceName: Immutable. For findings on Google Cloud resources, the full
      resource name of the Google Cloud resource this finding is for. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
      When the finding is for a non-Google Cloud resource, the resourceName
      can be a customer or partner defined string.
    securityMarks: Output only. User specified security marks. These marks are
      entirely managed by the user and come from the SecurityMarks resource
      that belongs to the finding.
    securityPosture: The security posture associated with the finding.
    severity: The severity of the finding. This field is managed by the source
      that writes the finding.
    sourceProperties: Source specific properties. These properties are managed
      by the source that writes the finding. The key names in the
      source_properties map must be between 1 and 255 characters, and must
      start with a letter and contain alphanumeric characters or underscores
      only.
    state: Output only. The state of the finding.
    toxicCombination: Contains details about a group of security issues that,
      when the issues occur together, represent a greater risk than when the
      issues occur independently. A group of such issues is referred to as a
      toxic combination. This field cannot be updated. Its value is ignored in
      all update requests.
    vulnerability: Represents vulnerability-specific fields like CVE and CVSS
      scores. CVE stands for Common Vulnerabilities and Exposures
      (https://cve.mitre.org/about/)
  """

  class FindingClassValueValuesEnum(_messages.Enum):
    r"""The class of the finding.

    Values:
      FINDING_CLASS_UNSPECIFIED: Unspecified finding class.
      THREAT: Describes unwanted or malicious activity.
      VULNERABILITY: Describes a potential weakness in software that increases
        risk to Confidentiality & Integrity & Availability.
      MISCONFIGURATION: Describes a potential weakness in cloud resource/asset
        configuration that increases risk.
      OBSERVATION: Describes a security observation that is for informational
        purposes.
      SCC_ERROR: Describes an error that prevents some SCC functionality.
      POSTURE_VIOLATION: Describes a potential security risk due to a change
        in the security posture.
      TOXIC_COMBINATION: Describes a combination of security issues that
        represent a more severe security problem when taken together.
      SENSITIVE_DATA_RISK: Describes a potential security risk to data assets
        that contain sensitive data.
      CHOKEPOINT: Describes a resource or resource group where high risk
        attack paths converge, based on attack path simulations (APS).
    """
    FINDING_CLASS_UNSPECIFIED = 0
    THREAT = 1
    VULNERABILITY = 2
    MISCONFIGURATION = 3
    OBSERVATION = 4
    SCC_ERROR = 5
    POSTURE_VIOLATION = 6
    TOXIC_COMBINATION = 7
    SENSITIVE_DATA_RISK = 8
    CHOKEPOINT = 9

  class MuteValueValuesEnum(_messages.Enum):
    r"""Indicates the mute state of a finding (either muted, unmuted or
    undefined). Unlike other attributes of a finding, a finding provider
    shouldn't set the value of mute.

    Values:
      MUTE_UNSPECIFIED: Unspecified.
      MUTED: Finding has been muted.
      UNMUTED: Finding has been unmuted.
      UNDEFINED: Finding has never been muted/unmuted.
    """
    MUTE_UNSPECIFIED = 0
    MUTED = 1
    UNMUTED = 2
    UNDEFINED = 3

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the finding. This field is managed by the source that
    writes the finding.

    Values:
      SEVERITY_UNSPECIFIED: This value is used for findings when a source
        doesn't write a severity value.
      CRITICAL: Vulnerability: A critical vulnerability is easily discoverable
        by an external actor, exploitable, and results in the direct ability
        to execute arbitrary code, exfiltrate data, and otherwise gain
        additional access and privileges to cloud resources and workloads.
        Examples include publicly accessible unprotected user data and public
        SSH access with weak or no passwords. Threat: Indicates a threat that
        is able to access, modify, or delete data or execute unauthorized code
        within existing resources.
      HIGH: Vulnerability: A high risk vulnerability can be easily discovered
        and exploited in combination with other vulnerabilities in order to
        gain direct access and the ability to execute arbitrary code,
        exfiltrate data, and otherwise gain additional access and privileges
        to cloud resources and workloads. An example is a database with weak
        or no passwords that is only accessible internally. This database
        could easily be compromised by an actor that had access to the
        internal network. Threat: Indicates a threat that is able to create
        new computational resources in an environment but not able to access
        data or execute code in existing resources.
      MEDIUM: Vulnerability: A medium risk vulnerability could be used by an
        actor to gain access to resources or privileges that enable them to
        eventually (through multiple steps or a complex exploit) gain access
        and the ability to execute arbitrary code or exfiltrate data. An
        example is a service account with access to more projects than it
        should have. If an actor gains access to the service account, they
        could potentially use that access to manipulate a project the service
        account was not intended to. Threat: Indicates a threat that is able
        to cause operational impact but may not access data or execute
        unauthorized code.
      LOW: Vulnerability: A low risk vulnerability hampers a security
        organization's ability to detect vulnerabilities or active threats in
        their deployment, or prevents the root cause investigation of security
        issues. An example is monitoring and logs being disabled for resource
        configurations and access. Threat: Indicates a threat that has
        obtained minimal access to an environment but is not able to access
        data, execute code, or create resources.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the finding.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      ACTIVE: The finding requires attention and has not been addressed yet.
      INACTIVE: The finding has been fixed, triaged as a non-issue or
        otherwise addressed and is no longer active.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ContactsValue(_messages.Message):
    r"""Output only. Map containing the points of contact for the given
    finding. The key represents the type of contact, while the value contains
    a list of all the contacts that pertain. Please refer to:
    https://cloud.google.com/resource-manager/docs/managing-notification-
    contacts#notification-categories { "security": { "contacts": [ { "email":
    "<EMAIL>" }, { "email": "<EMAIL>" } ] } }

    Messages:
      AdditionalProperty: An additional property for a ContactsValue object.

    Fields:
      additionalProperties: Additional properties of type ContactsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ContactsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudSecuritycenterV2ContactDetails attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudSecuritycenterV2ContactDetails', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExternalSystemsValue(_messages.Message):
    r"""Output only. Third party SIEM/SOAR fields within SCC, contains
    external system information and external system finding fields.

    Messages:
      AdditionalProperty: An additional property for a ExternalSystemsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ExternalSystemsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExternalSystemsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudSecuritycenterV2ExternalSystem attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudSecuritycenterV2ExternalSystem', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SourcePropertiesValue(_messages.Message):
    r"""Source specific properties. These properties are managed by the source
    that writes the finding. The key names in the source_properties map must
    be between 1 and 255 characters, and must start with a letter and contain
    alphanumeric characters or underscores only.

    Messages:
      AdditionalProperty: An additional property for a SourcePropertiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        SourcePropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SourcePropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  access = _messages.MessageField('GoogleCloudSecuritycenterV2Access', 1)
  affectedResources = _messages.MessageField('GoogleCloudSecuritycenterV2AffectedResources', 2)
  application = _messages.MessageField('GoogleCloudSecuritycenterV2Application', 3)
  attackExposure = _messages.MessageField('GoogleCloudSecuritycenterV2AttackExposure', 4)
  backupDisasterRecovery = _messages.MessageField('GoogleCloudSecuritycenterV2BackupDisasterRecovery', 5)
  canonicalName = _messages.StringField(6)
  category = _messages.StringField(7)
  chokepoint = _messages.MessageField('GoogleCloudSecuritycenterV2Chokepoint', 8)
  cloudArmor = _messages.MessageField('GoogleCloudSecuritycenterV2CloudArmor', 9)
  cloudDlpDataProfile = _messages.MessageField('GoogleCloudSecuritycenterV2CloudDlpDataProfile', 10)
  cloudDlpInspection = _messages.MessageField('GoogleCloudSecuritycenterV2CloudDlpInspection', 11)
  compliances = _messages.MessageField('GoogleCloudSecuritycenterV2Compliance', 12, repeated=True)
  connections = _messages.MessageField('GoogleCloudSecuritycenterV2Connection', 13, repeated=True)
  contacts = _messages.MessageField('ContactsValue', 14)
  containers = _messages.MessageField('GoogleCloudSecuritycenterV2Container', 15, repeated=True)
  createTime = _messages.StringField(16)
  dataAccessEvents = _messages.MessageField('GoogleCloudSecuritycenterV2DataAccessEvent', 17, repeated=True)
  dataFlowEvents = _messages.MessageField('GoogleCloudSecuritycenterV2DataFlowEvent', 18, repeated=True)
  dataRetentionDeletionEvents = _messages.MessageField('GoogleCloudSecuritycenterV2DataRetentionDeletionEvent', 19, repeated=True)
  database = _messages.MessageField('GoogleCloudSecuritycenterV2Database', 20)
  description = _messages.StringField(21)
  disk = _messages.MessageField('GoogleCloudSecuritycenterV2Disk', 22)
  eventTime = _messages.StringField(23)
  exfiltration = _messages.MessageField('GoogleCloudSecuritycenterV2Exfiltration', 24)
  externalSystems = _messages.MessageField('ExternalSystemsValue', 25)
  externalUri = _messages.StringField(26)
  files = _messages.MessageField('GoogleCloudSecuritycenterV2File', 27, repeated=True)
  findingClass = _messages.EnumField('FindingClassValueValuesEnum', 28)
  groupMemberships = _messages.MessageField('GoogleCloudSecuritycenterV2GroupMembership', 29, repeated=True)
  iamBindings = _messages.MessageField('GoogleCloudSecuritycenterV2IamBinding', 30, repeated=True)
  indicator = _messages.MessageField('GoogleCloudSecuritycenterV2Indicator', 31)
  ipRules = _messages.MessageField('GoogleCloudSecuritycenterV2IpRules', 32)
  job = _messages.MessageField('GoogleCloudSecuritycenterV2Job', 33)
  kernelRootkit = _messages.MessageField('GoogleCloudSecuritycenterV2KernelRootkit', 34)
  kubernetes = _messages.MessageField('GoogleCloudSecuritycenterV2Kubernetes', 35)
  loadBalancers = _messages.MessageField('GoogleCloudSecuritycenterV2LoadBalancer', 36, repeated=True)
  logEntries = _messages.MessageField('GoogleCloudSecuritycenterV2LogEntry', 37, repeated=True)
  mitreAttack = _messages.MessageField('GoogleCloudSecuritycenterV2MitreAttack', 38)
  moduleName = _messages.StringField(39)
  mute = _messages.EnumField('MuteValueValuesEnum', 40)
  muteInfo = _messages.MessageField('GoogleCloudSecuritycenterV2MuteInfo', 41)
  muteInitiator = _messages.StringField(42)
  muteUpdateTime = _messages.StringField(43)
  name = _messages.StringField(44)
  networks = _messages.MessageField('GoogleCloudSecuritycenterV2Network', 45, repeated=True)
  nextSteps = _messages.StringField(46)
  notebook = _messages.MessageField('GoogleCloudSecuritycenterV2Notebook', 47)
  orgPolicies = _messages.MessageField('GoogleCloudSecuritycenterV2OrgPolicy', 48, repeated=True)
  parent = _messages.StringField(49)
  parentDisplayName = _messages.StringField(50)
  processes = _messages.MessageField('GoogleCloudSecuritycenterV2Process', 51, repeated=True)
  resourceName = _messages.StringField(52)
  securityMarks = _messages.MessageField('GoogleCloudSecuritycenterV2SecurityMarks', 53)
  securityPosture = _messages.MessageField('GoogleCloudSecuritycenterV2SecurityPosture', 54)
  severity = _messages.EnumField('SeverityValueValuesEnum', 55)
  sourceProperties = _messages.MessageField('SourcePropertiesValue', 56)
  state = _messages.EnumField('StateValueValuesEnum', 57)
  toxicCombination = _messages.MessageField('GoogleCloudSecuritycenterV2ToxicCombination', 58)
  vulnerability = _messages.MessageField('GoogleCloudSecuritycenterV2Vulnerability', 59)


class GoogleCloudSecuritycenterV2Folder(_messages.Message):
  r"""Message that contains the resource name and display name of a folder
  resource.

  Fields:
    resourceFolder: Full resource name of this folder. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    resourceFolderDisplayName: The user defined display name for this folder.
  """

  resourceFolder = _messages.StringField(1)
  resourceFolderDisplayName = _messages.StringField(2)


class GoogleCloudSecuritycenterV2Geolocation(_messages.Message):
  r"""Represents a geographical location for a given access.

  Fields:
    regionCode: A CLDR.
  """

  regionCode = _messages.StringField(1)


class GoogleCloudSecuritycenterV2GroupMembership(_messages.Message):
  r"""Contains details about groups of which this finding is a member. A group
  is a collection of findings that are related in some way.

  Enums:
    GroupTypeValueValuesEnum: Type of group.

  Fields:
    groupId: ID of the group.
    groupType: Type of group.
  """

  class GroupTypeValueValuesEnum(_messages.Enum):
    r"""Type of group.

    Values:
      GROUP_TYPE_UNSPECIFIED: Default value.
      GROUP_TYPE_TOXIC_COMBINATION: Group represents a toxic combination.
      GROUP_TYPE_CHOKEPOINT: Group represents a chokepoint.
    """
    GROUP_TYPE_UNSPECIFIED = 0
    GROUP_TYPE_TOXIC_COMBINATION = 1
    GROUP_TYPE_CHOKEPOINT = 2

  groupId = _messages.StringField(1)
  groupType = _messages.EnumField('GroupTypeValueValuesEnum', 2)


class GoogleCloudSecuritycenterV2IamBinding(_messages.Message):
  r"""Represents a particular IAM binding, which captures a member's role
  addition, removal, or state.

  Enums:
    ActionValueValuesEnum: The action that was performed on a Binding.

  Fields:
    action: The action that was performed on a Binding.
    member: A single identity requesting access for a Cloud Platform resource,
      for example, "<EMAIL>".
    role: Role that is assigned to "members". For example, "roles/viewer",
      "roles/editor", or "roles/owner".
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""The action that was performed on a Binding.

    Values:
      ACTION_UNSPECIFIED: Unspecified.
      ADD: Addition of a Binding.
      REMOVE: Removal of a Binding.
    """
    ACTION_UNSPECIFIED = 0
    ADD = 1
    REMOVE = 2

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  member = _messages.StringField(2)
  role = _messages.StringField(3)


class GoogleCloudSecuritycenterV2Indicator(_messages.Message):
  r"""Represents what's commonly known as an _indicator of compromise_ (IoC)
  in computer forensics. This is an artifact observed on a network or in an
  operating system that, with high confidence, indicates a computer intrusion.
  For more information, see [Indicator of
  compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise).

  Fields:
    domains: List of domains associated to the Finding.
    ipAddresses: The list of IP addresses that are associated with the
      finding.
    signatures: The list of matched signatures indicating that the given
      process is present in the environment.
    uris: The list of URIs associated to the Findings.
  """

  domains = _messages.StringField(1, repeated=True)
  ipAddresses = _messages.StringField(2, repeated=True)
  signatures = _messages.MessageField('GoogleCloudSecuritycenterV2ProcessSignature', 3, repeated=True)
  uris = _messages.StringField(4, repeated=True)


class GoogleCloudSecuritycenterV2IpRule(_messages.Message):
  r"""IP rule information.

  Fields:
    portRanges: Optional. An optional list of ports to which this rule
      applies. This field is only applicable for the UDP or (S)TCP protocols.
      Each entry must be either an integer or a range including a min and max
      port number.
    protocol: The IP protocol this rule applies to. This value can either be
      one of the following well known protocol strings (TCP, UDP, ICMP, ESP,
      AH, IPIP, SCTP) or a string representation of the integer value.
  """

  portRanges = _messages.MessageField('GoogleCloudSecuritycenterV2PortRange', 1, repeated=True)
  protocol = _messages.StringField(2)


class GoogleCloudSecuritycenterV2IpRules(_messages.Message):
  r"""IP rules associated with the finding.

  Enums:
    DirectionValueValuesEnum: The direction that the rule is applicable to,
      one of ingress or egress.

  Fields:
    allowed: Tuple with allowed rules.
    denied: Tuple with denied rules.
    destinationIpRanges: If destination IP ranges are specified, the firewall
      rule applies only to traffic that has a destination IP address in these
      ranges. These ranges must be expressed in CIDR format. Only supports
      IPv4.
    direction: The direction that the rule is applicable to, one of ingress or
      egress.
    exposedServices: Name of the network protocol service, such as FTP, that
      is exposed by the open port. Follows the naming convention available at:
      https://www.iana.org/assignments/service-names-port-numbers/service-
      names-port-numbers.xhtml.
    sourceIpRanges: If source IP ranges are specified, the firewall rule
      applies only to traffic that has a source IP address in these ranges.
      These ranges must be expressed in CIDR format. Only supports IPv4.
  """

  class DirectionValueValuesEnum(_messages.Enum):
    r"""The direction that the rule is applicable to, one of ingress or
    egress.

    Values:
      DIRECTION_UNSPECIFIED: Unspecified direction value.
      INGRESS: Ingress direction value.
      EGRESS: Egress direction value.
    """
    DIRECTION_UNSPECIFIED = 0
    INGRESS = 1
    EGRESS = 2

  allowed = _messages.MessageField('GoogleCloudSecuritycenterV2Allowed', 1)
  denied = _messages.MessageField('GoogleCloudSecuritycenterV2Denied', 2)
  destinationIpRanges = _messages.StringField(3, repeated=True)
  direction = _messages.EnumField('DirectionValueValuesEnum', 4)
  exposedServices = _messages.StringField(5, repeated=True)
  sourceIpRanges = _messages.StringField(6, repeated=True)


class GoogleCloudSecuritycenterV2Issue(_messages.Message):
  r"""Security Command Center Issue.

  Enums:
    IssueTypeValueValuesEnum: The type of the issue.
    SeverityValueValuesEnum: The severity of the issue.
    StateValueValuesEnum: Output only. The state of the issue.

  Fields:
    createTime: Output only. The time the issue was created.
    description: The description of the issue in Markdown format.
    detection: The finding category or rule name that generated the issue.
    domains: The domains of the issue.
    exposureScore: The exposure score of the issue.
    issueType: The type of the issue.
    lastObservationTime: The time the issue was last observed.
    mute: The mute information of the issue.
    name: Identifier. The name of the issue. Format:
      organizations/{organization}/locations/{location}/issues/{issue}
    primaryResource: The primary resource associated with the issue.
    relatedFindings: The findings related to the issue.
    remediations: Approaches to remediate the issue in Markdown format.
    secondaryResources: Additional resources associated with the issue.
    securityContexts: The security context of the issue.
    severity: The severity of the issue.
    state: Output only. The state of the issue.
    updateTime: Output only. The time the issue was last updated.
  """

  class IssueTypeValueValuesEnum(_messages.Enum):
    r"""The type of the issue.

    Values:
      ISSUE_TYPE_UNSPECIFIED: Unspecified issue type.
      CHOKEPOINT: Chokepoint issue type.
      TOXIC_COMBINATION: Toxic combination issue type.
      INSIGHT: Insight issue type.
    """
    ISSUE_TYPE_UNSPECIFIED = 0
    CHOKEPOINT = 1
    TOXIC_COMBINATION = 2
    INSIGHT = 3

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the issue.

    Values:
      SEVERITY_UNSPECIFIED: Unspecified severity.
      CRITICAL: Critical severity.
      HIGH: High severity.
      MEDIUM: Medium severity.
      LOW: Low severity.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the issue.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      ACTIVE: Active state.
      INACTIVE: Inactive state.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  detection = _messages.StringField(3)
  domains = _messages.MessageField('GoogleCloudSecuritycenterV2IssueDomain', 4, repeated=True)
  exposureScore = _messages.FloatField(5)
  issueType = _messages.EnumField('IssueTypeValueValuesEnum', 6)
  lastObservationTime = _messages.StringField(7)
  mute = _messages.MessageField('GoogleCloudSecuritycenterV2IssueMute', 8)
  name = _messages.StringField(9)
  primaryResource = _messages.MessageField('GoogleCloudSecuritycenterV2IssueResource', 10)
  relatedFindings = _messages.MessageField('GoogleCloudSecuritycenterV2IssueFinding', 11, repeated=True)
  remediations = _messages.StringField(12, repeated=True)
  secondaryResources = _messages.MessageField('GoogleCloudSecuritycenterV2IssueResource', 13, repeated=True)
  securityContexts = _messages.MessageField('GoogleCloudSecuritycenterV2IssueSecurityContext', 14, repeated=True)
  severity = _messages.EnumField('SeverityValueValuesEnum', 15)
  state = _messages.EnumField('StateValueValuesEnum', 16)
  updateTime = _messages.StringField(17)


class GoogleCloudSecuritycenterV2IssueDomain(_messages.Message):
  r"""The domains of an issue.

  Enums:
    DomainCategoryValueValuesEnum: The domain category of the issue.

  Fields:
    domainCategory: The domain category of the issue.
  """

  class DomainCategoryValueValuesEnum(_messages.Enum):
    r"""The domain category of the issue.

    Values:
      DOMAIN_CATEGORY_UNSPECIFIED: Unspecified domain category.
      AI: Issues in the AI domain.
      CODE: Issues in the code domain.
      CONTAINER: Issues in the container domain.
      DATA: Issues in the data domain.
      IDENTITY_AND_ACCESS: Issues in the identity and access domain.
      VULNERABILITY: Issues in the vulnerability domain.
      THREAT: Issues in the threat domain.
    """
    DOMAIN_CATEGORY_UNSPECIFIED = 0
    AI = 1
    CODE = 2
    CONTAINER = 3
    DATA = 4
    IDENTITY_AND_ACCESS = 5
    VULNERABILITY = 6
    THREAT = 7

  domainCategory = _messages.EnumField('DomainCategoryValueValuesEnum', 1)


class GoogleCloudSecuritycenterV2IssueFinding(_messages.Message):
  r"""Finding related to an issue.

  Fields:
    cve: The CVE of the finding.
    name: The name of the finding.
    securityBulletin: The security bulletin of the finding.
  """

  cve = _messages.MessageField('GoogleCloudSecuritycenterV2IssueFindingCve', 1)
  name = _messages.StringField(2)
  securityBulletin = _messages.MessageField('GoogleCloudSecuritycenterV2IssueFindingSecurityBulletin', 3)


class GoogleCloudSecuritycenterV2IssueFindingCve(_messages.Message):
  r"""The CVE of the finding.

  Fields:
    name: The CVE name.
  """

  name = _messages.StringField(1)


class GoogleCloudSecuritycenterV2IssueFindingSecurityBulletin(_messages.Message):
  r"""The security bulletin of the finding.

  Fields:
    name: The security bulletin name.
  """

  name = _messages.StringField(1)


class GoogleCloudSecuritycenterV2IssueMute(_messages.Message):
  r"""The mute information of the issue.

  Enums:
    MuteStateValueValuesEnum: Output only. The mute state of the issue.

  Fields:
    muteInitiator: The email address of the user who last changed the mute
      state of the issue.
    muteReason: The user-provided reason for muting the issue.
    muteState: Output only. The mute state of the issue.
    muteUpdateTime: The time the issue was muted.
  """

  class MuteStateValueValuesEnum(_messages.Enum):
    r"""Output only. The mute state of the issue.

    Values:
      MUTE_STATE_UNSPECIFIED: Unspecified mute state.
      NOT_MUTED: Not muted.
      MUTED: Muted.
    """
    MUTE_STATE_UNSPECIFIED = 0
    NOT_MUTED = 1
    MUTED = 2

  muteInitiator = _messages.StringField(1)
  muteReason = _messages.StringField(2)
  muteState = _messages.EnumField('MuteStateValueValuesEnum', 3)
  muteUpdateTime = _messages.StringField(4)


class GoogleCloudSecuritycenterV2IssueResource(_messages.Message):
  r"""A resource associated with the an issue.

  Enums:
    CloudProviderValueValuesEnum: The cloud provider of the resource
      associated with the issue.

  Fields:
    awsMetadata: The AWS metadata of the resource associated with the issue.
      Only populated for AWS resources.
    azureMetadata: The Azure metadata of the resource associated with the
      issue. Only populated for Azure resources.
    cloudProvider: The cloud provider of the resource associated with the
      issue.
    displayName: The resource-type specific display name of the resource
      associated with the issue.
    googleCloudMetadata: The Google Cloud metadata of the resource associated
      with the issue. Only populated for Google Cloud resources.
    name: The full resource name of the resource associated with the issue.
    type: The type of the resource associated with the issue.
  """

  class CloudProviderValueValuesEnum(_messages.Enum):
    r"""The cloud provider of the resource associated with the issue.

    Values:
      CLOUD_PROVIDER_UNSPECIFIED: Unspecified cloud provider.
      GOOGLE_CLOUD: Google Cloud.
      AMAZON_WEB_SERVICES: Amazon Web Services.
      MICROSOFT_AZURE: Microsoft Azure.
    """
    CLOUD_PROVIDER_UNSPECIFIED = 0
    GOOGLE_CLOUD = 1
    AMAZON_WEB_SERVICES = 2
    MICROSOFT_AZURE = 3

  awsMetadata = _messages.MessageField('GoogleCloudSecuritycenterV2IssueResourceAwsMetadata', 1)
  azureMetadata = _messages.MessageField('GoogleCloudSecuritycenterV2IssueResourceAzureMetadata', 2)
  cloudProvider = _messages.EnumField('CloudProviderValueValuesEnum', 3)
  displayName = _messages.StringField(4)
  googleCloudMetadata = _messages.MessageField('GoogleCloudSecuritycenterV2IssueResourceGoogleCloudMetadata', 5)
  name = _messages.StringField(6)
  type = _messages.StringField(7)


class GoogleCloudSecuritycenterV2IssueResourceAwsMetadata(_messages.Message):
  r"""The AWS metadata of a resource associated with an issue.

  Fields:
    account: The AWS account of the resource associated with the issue.
  """

  account = _messages.MessageField('GoogleCloudSecuritycenterV2IssueResourceAwsMetadataAwsAccount', 1)


class GoogleCloudSecuritycenterV2IssueResourceAwsMetadataAwsAccount(_messages.Message):
  r"""The AWS account of the resource associated with the issue.

  Fields:
    id: The AWS account ID of the resource associated with the issue.
    name: The AWS account name of the resource associated with the issue.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2)


class GoogleCloudSecuritycenterV2IssueResourceAzureMetadata(_messages.Message):
  r"""The Azure metadata of a resource associated with an issue.

  Fields:
    subscription: The Azure subscription of the resource associated with the
      issue.
  """

  subscription = _messages.MessageField('GoogleCloudSecuritycenterV2IssueResourceAzureMetadataAzureSubscription', 1)


class GoogleCloudSecuritycenterV2IssueResourceAzureMetadataAzureSubscription(_messages.Message):
  r"""The Azure subscription of the resource associated with the issue.

  Fields:
    displayName: The Azure subscription display name of the resource
      associated with the issue.
    id: The Azure subscription ID of the resource associated with the issue.
  """

  displayName = _messages.StringField(1)
  id = _messages.StringField(2)


class GoogleCloudSecuritycenterV2IssueResourceGoogleCloudMetadata(_messages.Message):
  r"""Google Cloud metadata of a resource associated with an issue.

  Fields:
    projectId: The project ID that the resource associated with the issue
      belongs to.
  """

  projectId = _messages.StringField(1)


class GoogleCloudSecuritycenterV2IssueSecurityContext(_messages.Message):
  r"""Security context associated with an issue.

  Fields:
    aggregatedCount: The aggregated count of the security context.
    context: The context of the security context.
  """

  aggregatedCount = _messages.MessageField('GoogleCloudSecuritycenterV2IssueSecurityContextAggregatedCount', 1)
  context = _messages.MessageField('GoogleCloudSecuritycenterV2IssueSecurityContextContext', 2)


class GoogleCloudSecuritycenterV2IssueSecurityContextAggregatedCount(_messages.Message):
  r"""Aggregated count of a security context.

  Fields:
    key: Aggregation key.
    value: Aggregation value.
  """

  key = _messages.StringField(1)
  value = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudSecuritycenterV2IssueSecurityContextContext(_messages.Message):
  r"""Context of a security context.

  Fields:
    type: Context type.
    values: Context values.
  """

  type = _messages.StringField(1)
  values = _messages.StringField(2, repeated=True)


class GoogleCloudSecuritycenterV2Job(_messages.Message):
  r"""Describes a job

  Enums:
    StateValueValuesEnum: Output only. State of the job, such as `RUNNING` or
      `PENDING`.

  Fields:
    errorCode: Optional. If the job did not complete successfully, this field
      describes why.
    location: Optional. Gives the location where the job ran, such as `US` or
      `europe-west1`
    name: The fully-qualified name for a job. e.g. `projects//jobs/`
    state: Output only. State of the job, such as `RUNNING` or `PENDING`.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the job, such as `RUNNING` or `PENDING`.

    Values:
      JOB_STATE_UNSPECIFIED: Unspecified represents an unknown state and
        should not be used.
      PENDING: Job is scheduled and pending for run
      RUNNING: Job in progress
      SUCCEEDED: Job has completed with success
      FAILED: Job has completed but with failure
    """
    JOB_STATE_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4

  errorCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  location = _messages.StringField(2)
  name = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class GoogleCloudSecuritycenterV2KernelRootkit(_messages.Message):
  r"""Kernel mode rootkit signatures.

  Fields:
    name: Rootkit name, when available.
    unexpectedCodeModification: True if unexpected modifications of kernel
      code memory are present.
    unexpectedFtraceHandler: True if `ftrace` points are present with
      callbacks pointing to regions that are not in the expected kernel or
      module code range.
    unexpectedInterruptHandler: True if interrupt handlers that are are not in
      the expected kernel or module code regions are present.
    unexpectedKernelCodePages: True if kernel code pages that are not in the
      expected kernel or module code regions are present.
    unexpectedKprobeHandler: True if `kprobe` points are present with
      callbacks pointing to regions that are not in the expected kernel or
      module code range.
    unexpectedProcessesInRunqueue: True if unexpected processes in the
      scheduler run queue are present. Such processes are in the run queue,
      but not in the process task list.
    unexpectedReadOnlyDataModification: True if unexpected modifications of
      kernel read-only data memory are present.
    unexpectedSystemCallHandler: True if system call handlers that are are not
      in the expected kernel or module code regions are present.
  """

  name = _messages.StringField(1)
  unexpectedCodeModification = _messages.BooleanField(2)
  unexpectedFtraceHandler = _messages.BooleanField(3)
  unexpectedInterruptHandler = _messages.BooleanField(4)
  unexpectedKernelCodePages = _messages.BooleanField(5)
  unexpectedKprobeHandler = _messages.BooleanField(6)
  unexpectedProcessesInRunqueue = _messages.BooleanField(7)
  unexpectedReadOnlyDataModification = _messages.BooleanField(8)
  unexpectedSystemCallHandler = _messages.BooleanField(9)


class GoogleCloudSecuritycenterV2Kubernetes(_messages.Message):
  r"""Kubernetes-related attributes.

  Fields:
    accessReviews: Provides information on any Kubernetes access reviews
      (privilege checks) relevant to the finding.
    bindings: Provides Kubernetes role binding information for findings that
      involve [RoleBindings or
      ClusterRoleBindings](https://cloud.google.com/kubernetes-
      engine/docs/how-to/role-based-access-control).
    nodePools: GKE [node pools](https://cloud.google.com/kubernetes-
      engine/docs/concepts/node-pools) associated with the finding. This field
      contains node pool information for each node, when it is available.
    nodes: Provides Kubernetes [node](https://cloud.google.com/kubernetes-
      engine/docs/concepts/cluster-architecture#nodes) information.
    objects: Kubernetes objects related to the finding.
    pods: Kubernetes [Pods](https://cloud.google.com/kubernetes-
      engine/docs/concepts/pod) associated with the finding. This field
      contains Pod records for each container that is owned by a Pod.
    roles: Provides Kubernetes role information for findings that involve
      [Roles or ClusterRoles](https://cloud.google.com/kubernetes-
      engine/docs/how-to/role-based-access-control).
  """

  accessReviews = _messages.MessageField('GoogleCloudSecuritycenterV2AccessReview', 1, repeated=True)
  bindings = _messages.MessageField('GoogleCloudSecuritycenterV2Binding', 2, repeated=True)
  nodePools = _messages.MessageField('GoogleCloudSecuritycenterV2NodePool', 3, repeated=True)
  nodes = _messages.MessageField('GoogleCloudSecuritycenterV2Node', 4, repeated=True)
  objects = _messages.MessageField('GoogleCloudSecuritycenterV2Object', 5, repeated=True)
  pods = _messages.MessageField('GoogleCloudSecuritycenterV2Pod', 6, repeated=True)
  roles = _messages.MessageField('GoogleCloudSecuritycenterV2Role', 7, repeated=True)


class GoogleCloudSecuritycenterV2Label(_messages.Message):
  r"""Represents a generic name-value label. A label has separate name and
  value fields to support filtering with the `contains()` function. For more
  information, see [Filtering on array-type
  fields](https://cloud.google.com/security-command-center/docs/how-to-api-
  list-findings#array-contains-filtering).

  Fields:
    name: Name of the label.
    value: Value that corresponds to the label's name.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleCloudSecuritycenterV2LoadBalancer(_messages.Message):
  r"""Contains information related to the load balancer associated with the
  finding.

  Fields:
    name: The name of the load balancer associated with the finding.
  """

  name = _messages.StringField(1)


class GoogleCloudSecuritycenterV2LogEntry(_messages.Message):
  r"""An individual entry in a log.

  Fields:
    cloudLoggingEntry: An individual entry in a log stored in Cloud Logging.
  """

  cloudLoggingEntry = _messages.MessageField('GoogleCloudSecuritycenterV2CloudLoggingEntry', 1)


class GoogleCloudSecuritycenterV2MemoryHashSignature(_messages.Message):
  r"""A signature corresponding to memory page hashes.

  Fields:
    binaryFamily: The binary family.
    detections: The list of memory hash detections contributing to the binary
      family match.
  """

  binaryFamily = _messages.StringField(1)
  detections = _messages.MessageField('GoogleCloudSecuritycenterV2Detection', 2, repeated=True)


class GoogleCloudSecuritycenterV2MitreAttack(_messages.Message):
  r"""MITRE ATT&CK tactics and techniques related to this finding. See:
  https://attack.mitre.org

  Enums:
    AdditionalTacticsValueListEntryValuesEnum:
    AdditionalTechniquesValueListEntryValuesEnum:
    PrimaryTacticValueValuesEnum: The MITRE ATT&CK tactic most closely
      represented by this finding, if any.
    PrimaryTechniquesValueListEntryValuesEnum:

  Fields:
    additionalTactics: Additional MITRE ATT&CK tactics related to this
      finding, if any.
    additionalTechniques: Additional MITRE ATT&CK techniques related to this
      finding, if any, along with any of their respective parent techniques.
    primaryTactic: The MITRE ATT&CK tactic most closely represented by this
      finding, if any.
    primaryTechniques: The MITRE ATT&CK technique most closely represented by
      this finding, if any. primary_techniques is a repeated field because
      there are multiple levels of MITRE ATT&CK techniques. If the technique
      most closely represented by this finding is a sub-technique (e.g.
      `SCANNING_IP_BLOCKS`), both the sub-technique and its parent
      technique(s) will be listed (e.g. `SCANNING_IP_BLOCKS`,
      `ACTIVE_SCANNING`).
    version: The MITRE ATT&CK version referenced by the above fields. E.g.
      "8".
  """

  class AdditionalTacticsValueListEntryValuesEnum(_messages.Enum):
    r"""AdditionalTacticsValueListEntryValuesEnum enum type.

    Values:
      TACTIC_UNSPECIFIED: Unspecified value.
      RECONNAISSANCE: TA0043
      RESOURCE_DEVELOPMENT: TA0042
      INITIAL_ACCESS: TA0001
      EXECUTION: TA0002
      PERSISTENCE: TA0003
      PRIVILEGE_ESCALATION: TA0004
      DEFENSE_EVASION: TA0005
      CREDENTIAL_ACCESS: TA0006
      DISCOVERY: TA0007
      LATERAL_MOVEMENT: TA0008
      COLLECTION: TA0009
      COMMAND_AND_CONTROL: TA0011
      EXFILTRATION: TA0010
      IMPACT: TA0040
    """
    TACTIC_UNSPECIFIED = 0
    RECONNAISSANCE = 1
    RESOURCE_DEVELOPMENT = 2
    INITIAL_ACCESS = 3
    EXECUTION = 4
    PERSISTENCE = 5
    PRIVILEGE_ESCALATION = 6
    DEFENSE_EVASION = 7
    CREDENTIAL_ACCESS = 8
    DISCOVERY = 9
    LATERAL_MOVEMENT = 10
    COLLECTION = 11
    COMMAND_AND_CONTROL = 12
    EXFILTRATION = 13
    IMPACT = 14

  class AdditionalTechniquesValueListEntryValuesEnum(_messages.Enum):
    r"""AdditionalTechniquesValueListEntryValuesEnum enum type.

    Values:
      TECHNIQUE_UNSPECIFIED: Unspecified value.
      DATA_OBFUSCATION: T1001
      DATA_OBFUSCATION_STEGANOGRAPHY: T1001.002
      OS_CREDENTIAL_DUMPING: T1003
      OS_CREDENTIAL_DUMPING_PROC_FILESYSTEM: T1003.007
      OS_CREDENTIAL_DUMPING_ETC_PASSWORD_AND_ETC_SHADOW: T1003.008
      DATA_FROM_LOCAL_SYSTEM: T1005
      AUTOMATED_EXFILTRATION: T1020
      OBFUSCATED_FILES_OR_INFO: T1027
      STEGANOGRAPHY: T1027.003
      COMPILE_AFTER_DELIVERY: T1027.004
      COMMAND_OBFUSCATION: T1027.010
      SCHEDULED_TRANSFER: T1029
      SYSTEM_OWNER_USER_DISCOVERY: T1033
      MASQUERADING: T1036
      MATCH_LEGITIMATE_NAME_OR_LOCATION: T1036.005
      BOOT_OR_LOGON_INITIALIZATION_SCRIPTS: T1037
      STARTUP_ITEMS: T1037.005
      NETWORK_SERVICE_DISCOVERY: T1046
      SCHEDULED_TASK_JOB: T1053
      SCHEDULED_TASK_JOB_CRON: T1053.003
      CONTAINER_ORCHESTRATION_JOB: T1053.007
      PROCESS_INJECTION: T1055
      INPUT_CAPTURE: T1056
      INPUT_CAPTURE_KEYLOGGING: T1056.001
      PROCESS_DISCOVERY: T1057
      COMMAND_AND_SCRIPTING_INTERPRETER: T1059
      UNIX_SHELL: T1059.004
      PYTHON: T1059.006
      EXPLOITATION_FOR_PRIVILEGE_ESCALATION: T1068
      PERMISSION_GROUPS_DISCOVERY: T1069
      CLOUD_GROUPS: T1069.003
      INDICATOR_REMOVAL: T1070
      INDICATOR_REMOVAL_CLEAR_LINUX_OR_MAC_SYSTEM_LOGS: T1070.002
      INDICATOR_REMOVAL_CLEAR_COMMAND_HISTORY: T1070.003
      INDICATOR_REMOVAL_FILE_DELETION: T1070.004
      INDICATOR_REMOVAL_TIMESTOMP: T1070.006
      INDICATOR_REMOVAL_CLEAR_MAILBOX_DATA: T1070.008
      APPLICATION_LAYER_PROTOCOL: T1071
      DNS: T1071.004
      SOFTWARE_DEPLOYMENT_TOOLS: T1072
      VALID_ACCOUNTS: T1078
      DEFAULT_ACCOUNTS: T1078.001
      LOCAL_ACCOUNTS: T1078.003
      CLOUD_ACCOUNTS: T1078.004
      FILE_AND_DIRECTORY_DISCOVERY: T1083
      ACCOUNT_DISCOVERY_LOCAL_ACCOUNT: T1087.001
      PROXY: T1090
      EXTERNAL_PROXY: T1090.002
      MULTI_HOP_PROXY: T1090.003
      ACCOUNT_MANIPULATION: T1098
      ADDITIONAL_CLOUD_CREDENTIALS: T1098.001
      ADDITIONAL_CLOUD_ROLES: T1098.003
      SSH_AUTHORIZED_KEYS: T1098.004
      ADDITIONAL_CONTAINER_CLUSTER_ROLES: T1098.006
      MULTI_STAGE_CHANNELS: T1104
      INGRESS_TOOL_TRANSFER: T1105
      NATIVE_API: T1106
      BRUTE_FORCE: T1110
      AUTOMATED_COLLECTION: T1119
      SHARED_MODULES: T1129
      DATA_ENCODING: T1132
      STANDARD_ENCODING: T1132.001
      ACCESS_TOKEN_MANIPULATION: T1134
      TOKEN_IMPERSONATION_OR_THEFT: T1134.001
      CREATE_ACCOUNT: T1136
      LOCAL_ACCOUNT: T1136.001
      DEOBFUSCATE_DECODE_FILES_OR_INFO: T1140
      EXPLOIT_PUBLIC_FACING_APPLICATION: T1190
      SUPPLY_CHAIN_COMPROMISE: T1195
      COMPROMISE_SOFTWARE_DEPENDENCIES_AND_DEVELOPMENT_TOOLS: T1195.001
      USER_EXECUTION: T1204
      DOMAIN_POLICY_MODIFICATION: T1484
      DATA_DESTRUCTION: T1485
      DATA_ENCRYPTED_FOR_IMPACT: T1486
      SERVICE_STOP: T1489
      INHIBIT_SYSTEM_RECOVERY: T1490
      FIRMWARE_CORRUPTION: T1495
      RESOURCE_HIJACKING: T1496
      NETWORK_DENIAL_OF_SERVICE: T1498
      CLOUD_SERVICE_DISCOVERY: T1526
      STEAL_APPLICATION_ACCESS_TOKEN: T1528
      ACCOUNT_ACCESS_REMOVAL: T1531
      TRANSFER_DATA_TO_CLOUD_ACCOUNT: T1537
      STEAL_WEB_SESSION_COOKIE: T1539
      CREATE_OR_MODIFY_SYSTEM_PROCESS: T1543
      EVENT_TRIGGERED_EXECUTION: T1546
      BOOT_OR_LOGON_AUTOSTART_EXECUTION: T1547
      KERNEL_MODULES_AND_EXTENSIONS: T1547.006
      SHORTCUT_MODIFICATION: T1547.009
      ABUSE_ELEVATION_CONTROL_MECHANISM: T1548
      ABUSE_ELEVATION_CONTROL_MECHANISM_SUDO_AND_SUDO_CACHING: T1548.003
      UNSECURED_CREDENTIALS: T1552
      CREDENTIALS_IN_FILES: T1552.001
      BASH_HISTORY: T1552.003
      PRIVATE_KEYS: T1552.004
      SUBVERT_TRUST_CONTROL: T1553
      INSTALL_ROOT_CERTIFICATE: T1553.004
      COMPROMISE_HOST_SOFTWARE_BINARY: T1554
      CREDENTIALS_FROM_PASSWORD_STORES: T1555
      MODIFY_AUTHENTICATION_PROCESS: T1556
      PLUGGABLE_AUTHENTICATION_MODULES: T1556.003
      IMPAIR_DEFENSES: T1562
      DISABLE_OR_MODIFY_TOOLS: T1562.001
      INDICATOR_BLOCKING: T1562.006
      DISABLE_OR_MODIFY_LINUX_AUDIT_SYSTEM: T1562.012
      HIDE_ARTIFACTS: T1564
      HIDDEN_FILES_AND_DIRECTORIES: T1564.001
      HIDDEN_USERS: T1564.002
      EXFILTRATION_OVER_WEB_SERVICE: T1567
      EXFILTRATION_TO_CLOUD_STORAGE: T1567.002
      DYNAMIC_RESOLUTION: T1568
      LATERAL_TOOL_TRANSFER: T1570
      HIJACK_EXECUTION_FLOW: T1574
      HIJACK_EXECUTION_FLOW_DYNAMIC_LINKER_HIJACKING: T1574.006
      MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE: T1578
      CREATE_SNAPSHOT: T1578.001
      CLOUD_INFRASTRUCTURE_DISCOVERY: T1580
      DEVELOP_CAPABILITIES: T1587
      DEVELOP_CAPABILITIES_MALWARE: T1587.001
      OBTAIN_CAPABILITIES: T1588
      OBTAIN_CAPABILITIES_MALWARE: T1588.001
      OBTAIN_CAPABILITIES_VULNERABILITIES: T1588.006
      ACTIVE_SCANNING: T1595
      SCANNING_IP_BLOCKS: T1595.001
      STAGE_CAPABILITIES: T1608
      UPLOAD_MALWARE: T1608.001
      CONTAINER_ADMINISTRATION_COMMAND: T1609
      DEPLOY_CONTAINER: T1610
      ESCAPE_TO_HOST: T1611
      CONTAINER_AND_RESOURCE_DISCOVERY: T1613
      REFLECTIVE_CODE_LOADING: T1620
      STEAL_OR_FORGE_AUTHENTICATION_CERTIFICATES: T1649
      FINANCIAL_THEFT: T1657
    """
    TECHNIQUE_UNSPECIFIED = 0
    DATA_OBFUSCATION = 1
    DATA_OBFUSCATION_STEGANOGRAPHY = 2
    OS_CREDENTIAL_DUMPING = 3
    OS_CREDENTIAL_DUMPING_PROC_FILESYSTEM = 4
    OS_CREDENTIAL_DUMPING_ETC_PASSWORD_AND_ETC_SHADOW = 5
    DATA_FROM_LOCAL_SYSTEM = 6
    AUTOMATED_EXFILTRATION = 7
    OBFUSCATED_FILES_OR_INFO = 8
    STEGANOGRAPHY = 9
    COMPILE_AFTER_DELIVERY = 10
    COMMAND_OBFUSCATION = 11
    SCHEDULED_TRANSFER = 12
    SYSTEM_OWNER_USER_DISCOVERY = 13
    MASQUERADING = 14
    MATCH_LEGITIMATE_NAME_OR_LOCATION = 15
    BOOT_OR_LOGON_INITIALIZATION_SCRIPTS = 16
    STARTUP_ITEMS = 17
    NETWORK_SERVICE_DISCOVERY = 18
    SCHEDULED_TASK_JOB = 19
    SCHEDULED_TASK_JOB_CRON = 20
    CONTAINER_ORCHESTRATION_JOB = 21
    PROCESS_INJECTION = 22
    INPUT_CAPTURE = 23
    INPUT_CAPTURE_KEYLOGGING = 24
    PROCESS_DISCOVERY = 25
    COMMAND_AND_SCRIPTING_INTERPRETER = 26
    UNIX_SHELL = 27
    PYTHON = 28
    EXPLOITATION_FOR_PRIVILEGE_ESCALATION = 29
    PERMISSION_GROUPS_DISCOVERY = 30
    CLOUD_GROUPS = 31
    INDICATOR_REMOVAL = 32
    INDICATOR_REMOVAL_CLEAR_LINUX_OR_MAC_SYSTEM_LOGS = 33
    INDICATOR_REMOVAL_CLEAR_COMMAND_HISTORY = 34
    INDICATOR_REMOVAL_FILE_DELETION = 35
    INDICATOR_REMOVAL_TIMESTOMP = 36
    INDICATOR_REMOVAL_CLEAR_MAILBOX_DATA = 37
    APPLICATION_LAYER_PROTOCOL = 38
    DNS = 39
    SOFTWARE_DEPLOYMENT_TOOLS = 40
    VALID_ACCOUNTS = 41
    DEFAULT_ACCOUNTS = 42
    LOCAL_ACCOUNTS = 43
    CLOUD_ACCOUNTS = 44
    FILE_AND_DIRECTORY_DISCOVERY = 45
    ACCOUNT_DISCOVERY_LOCAL_ACCOUNT = 46
    PROXY = 47
    EXTERNAL_PROXY = 48
    MULTI_HOP_PROXY = 49
    ACCOUNT_MANIPULATION = 50
    ADDITIONAL_CLOUD_CREDENTIALS = 51
    ADDITIONAL_CLOUD_ROLES = 52
    SSH_AUTHORIZED_KEYS = 53
    ADDITIONAL_CONTAINER_CLUSTER_ROLES = 54
    MULTI_STAGE_CHANNELS = 55
    INGRESS_TOOL_TRANSFER = 56
    NATIVE_API = 57
    BRUTE_FORCE = 58
    AUTOMATED_COLLECTION = 59
    SHARED_MODULES = 60
    DATA_ENCODING = 61
    STANDARD_ENCODING = 62
    ACCESS_TOKEN_MANIPULATION = 63
    TOKEN_IMPERSONATION_OR_THEFT = 64
    CREATE_ACCOUNT = 65
    LOCAL_ACCOUNT = 66
    DEOBFUSCATE_DECODE_FILES_OR_INFO = 67
    EXPLOIT_PUBLIC_FACING_APPLICATION = 68
    SUPPLY_CHAIN_COMPROMISE = 69
    COMPROMISE_SOFTWARE_DEPENDENCIES_AND_DEVELOPMENT_TOOLS = 70
    USER_EXECUTION = 71
    DOMAIN_POLICY_MODIFICATION = 72
    DATA_DESTRUCTION = 73
    DATA_ENCRYPTED_FOR_IMPACT = 74
    SERVICE_STOP = 75
    INHIBIT_SYSTEM_RECOVERY = 76
    FIRMWARE_CORRUPTION = 77
    RESOURCE_HIJACKING = 78
    NETWORK_DENIAL_OF_SERVICE = 79
    CLOUD_SERVICE_DISCOVERY = 80
    STEAL_APPLICATION_ACCESS_TOKEN = 81
    ACCOUNT_ACCESS_REMOVAL = 82
    TRANSFER_DATA_TO_CLOUD_ACCOUNT = 83
    STEAL_WEB_SESSION_COOKIE = 84
    CREATE_OR_MODIFY_SYSTEM_PROCESS = 85
    EVENT_TRIGGERED_EXECUTION = 86
    BOOT_OR_LOGON_AUTOSTART_EXECUTION = 87
    KERNEL_MODULES_AND_EXTENSIONS = 88
    SHORTCUT_MODIFICATION = 89
    ABUSE_ELEVATION_CONTROL_MECHANISM = 90
    ABUSE_ELEVATION_CONTROL_MECHANISM_SUDO_AND_SUDO_CACHING = 91
    UNSECURED_CREDENTIALS = 92
    CREDENTIALS_IN_FILES = 93
    BASH_HISTORY = 94
    PRIVATE_KEYS = 95
    SUBVERT_TRUST_CONTROL = 96
    INSTALL_ROOT_CERTIFICATE = 97
    COMPROMISE_HOST_SOFTWARE_BINARY = 98
    CREDENTIALS_FROM_PASSWORD_STORES = 99
    MODIFY_AUTHENTICATION_PROCESS = 100
    PLUGGABLE_AUTHENTICATION_MODULES = 101
    IMPAIR_DEFENSES = 102
    DISABLE_OR_MODIFY_TOOLS = 103
    INDICATOR_BLOCKING = 104
    DISABLE_OR_MODIFY_LINUX_AUDIT_SYSTEM = 105
    HIDE_ARTIFACTS = 106
    HIDDEN_FILES_AND_DIRECTORIES = 107
    HIDDEN_USERS = 108
    EXFILTRATION_OVER_WEB_SERVICE = 109
    EXFILTRATION_TO_CLOUD_STORAGE = 110
    DYNAMIC_RESOLUTION = 111
    LATERAL_TOOL_TRANSFER = 112
    HIJACK_EXECUTION_FLOW = 113
    HIJACK_EXECUTION_FLOW_DYNAMIC_LINKER_HIJACKING = 114
    MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE = 115
    CREATE_SNAPSHOT = 116
    CLOUD_INFRASTRUCTURE_DISCOVERY = 117
    DEVELOP_CAPABILITIES = 118
    DEVELOP_CAPABILITIES_MALWARE = 119
    OBTAIN_CAPABILITIES = 120
    OBTAIN_CAPABILITIES_MALWARE = 121
    OBTAIN_CAPABILITIES_VULNERABILITIES = 122
    ACTIVE_SCANNING = 123
    SCANNING_IP_BLOCKS = 124
    STAGE_CAPABILITIES = 125
    UPLOAD_MALWARE = 126
    CONTAINER_ADMINISTRATION_COMMAND = 127
    DEPLOY_CONTAINER = 128
    ESCAPE_TO_HOST = 129
    CONTAINER_AND_RESOURCE_DISCOVERY = 130
    REFLECTIVE_CODE_LOADING = 131
    STEAL_OR_FORGE_AUTHENTICATION_CERTIFICATES = 132
    FINANCIAL_THEFT = 133

  class PrimaryTacticValueValuesEnum(_messages.Enum):
    r"""The MITRE ATT&CK tactic most closely represented by this finding, if
    any.

    Values:
      TACTIC_UNSPECIFIED: Unspecified value.
      RECONNAISSANCE: TA0043
      RESOURCE_DEVELOPMENT: TA0042
      INITIAL_ACCESS: TA0001
      EXECUTION: TA0002
      PERSISTENCE: TA0003
      PRIVILEGE_ESCALATION: TA0004
      DEFENSE_EVASION: TA0005
      CREDENTIAL_ACCESS: TA0006
      DISCOVERY: TA0007
      LATERAL_MOVEMENT: TA0008
      COLLECTION: TA0009
      COMMAND_AND_CONTROL: TA0011
      EXFILTRATION: TA0010
      IMPACT: TA0040
    """
    TACTIC_UNSPECIFIED = 0
    RECONNAISSANCE = 1
    RESOURCE_DEVELOPMENT = 2
    INITIAL_ACCESS = 3
    EXECUTION = 4
    PERSISTENCE = 5
    PRIVILEGE_ESCALATION = 6
    DEFENSE_EVASION = 7
    CREDENTIAL_ACCESS = 8
    DISCOVERY = 9
    LATERAL_MOVEMENT = 10
    COLLECTION = 11
    COMMAND_AND_CONTROL = 12
    EXFILTRATION = 13
    IMPACT = 14

  class PrimaryTechniquesValueListEntryValuesEnum(_messages.Enum):
    r"""PrimaryTechniquesValueListEntryValuesEnum enum type.

    Values:
      TECHNIQUE_UNSPECIFIED: Unspecified value.
      DATA_OBFUSCATION: T1001
      DATA_OBFUSCATION_STEGANOGRAPHY: T1001.002
      OS_CREDENTIAL_DUMPING: T1003
      OS_CREDENTIAL_DUMPING_PROC_FILESYSTEM: T1003.007
      OS_CREDENTIAL_DUMPING_ETC_PASSWORD_AND_ETC_SHADOW: T1003.008
      DATA_FROM_LOCAL_SYSTEM: T1005
      AUTOMATED_EXFILTRATION: T1020
      OBFUSCATED_FILES_OR_INFO: T1027
      STEGANOGRAPHY: T1027.003
      COMPILE_AFTER_DELIVERY: T1027.004
      COMMAND_OBFUSCATION: T1027.010
      SCHEDULED_TRANSFER: T1029
      SYSTEM_OWNER_USER_DISCOVERY: T1033
      MASQUERADING: T1036
      MATCH_LEGITIMATE_NAME_OR_LOCATION: T1036.005
      BOOT_OR_LOGON_INITIALIZATION_SCRIPTS: T1037
      STARTUP_ITEMS: T1037.005
      NETWORK_SERVICE_DISCOVERY: T1046
      SCHEDULED_TASK_JOB: T1053
      SCHEDULED_TASK_JOB_CRON: T1053.003
      CONTAINER_ORCHESTRATION_JOB: T1053.007
      PROCESS_INJECTION: T1055
      INPUT_CAPTURE: T1056
      INPUT_CAPTURE_KEYLOGGING: T1056.001
      PROCESS_DISCOVERY: T1057
      COMMAND_AND_SCRIPTING_INTERPRETER: T1059
      UNIX_SHELL: T1059.004
      PYTHON: T1059.006
      EXPLOITATION_FOR_PRIVILEGE_ESCALATION: T1068
      PERMISSION_GROUPS_DISCOVERY: T1069
      CLOUD_GROUPS: T1069.003
      INDICATOR_REMOVAL: T1070
      INDICATOR_REMOVAL_CLEAR_LINUX_OR_MAC_SYSTEM_LOGS: T1070.002
      INDICATOR_REMOVAL_CLEAR_COMMAND_HISTORY: T1070.003
      INDICATOR_REMOVAL_FILE_DELETION: T1070.004
      INDICATOR_REMOVAL_TIMESTOMP: T1070.006
      INDICATOR_REMOVAL_CLEAR_MAILBOX_DATA: T1070.008
      APPLICATION_LAYER_PROTOCOL: T1071
      DNS: T1071.004
      SOFTWARE_DEPLOYMENT_TOOLS: T1072
      VALID_ACCOUNTS: T1078
      DEFAULT_ACCOUNTS: T1078.001
      LOCAL_ACCOUNTS: T1078.003
      CLOUD_ACCOUNTS: T1078.004
      FILE_AND_DIRECTORY_DISCOVERY: T1083
      ACCOUNT_DISCOVERY_LOCAL_ACCOUNT: T1087.001
      PROXY: T1090
      EXTERNAL_PROXY: T1090.002
      MULTI_HOP_PROXY: T1090.003
      ACCOUNT_MANIPULATION: T1098
      ADDITIONAL_CLOUD_CREDENTIALS: T1098.001
      ADDITIONAL_CLOUD_ROLES: T1098.003
      SSH_AUTHORIZED_KEYS: T1098.004
      ADDITIONAL_CONTAINER_CLUSTER_ROLES: T1098.006
      MULTI_STAGE_CHANNELS: T1104
      INGRESS_TOOL_TRANSFER: T1105
      NATIVE_API: T1106
      BRUTE_FORCE: T1110
      AUTOMATED_COLLECTION: T1119
      SHARED_MODULES: T1129
      DATA_ENCODING: T1132
      STANDARD_ENCODING: T1132.001
      ACCESS_TOKEN_MANIPULATION: T1134
      TOKEN_IMPERSONATION_OR_THEFT: T1134.001
      CREATE_ACCOUNT: T1136
      LOCAL_ACCOUNT: T1136.001
      DEOBFUSCATE_DECODE_FILES_OR_INFO: T1140
      EXPLOIT_PUBLIC_FACING_APPLICATION: T1190
      SUPPLY_CHAIN_COMPROMISE: T1195
      COMPROMISE_SOFTWARE_DEPENDENCIES_AND_DEVELOPMENT_TOOLS: T1195.001
      USER_EXECUTION: T1204
      DOMAIN_POLICY_MODIFICATION: T1484
      DATA_DESTRUCTION: T1485
      DATA_ENCRYPTED_FOR_IMPACT: T1486
      SERVICE_STOP: T1489
      INHIBIT_SYSTEM_RECOVERY: T1490
      FIRMWARE_CORRUPTION: T1495
      RESOURCE_HIJACKING: T1496
      NETWORK_DENIAL_OF_SERVICE: T1498
      CLOUD_SERVICE_DISCOVERY: T1526
      STEAL_APPLICATION_ACCESS_TOKEN: T1528
      ACCOUNT_ACCESS_REMOVAL: T1531
      TRANSFER_DATA_TO_CLOUD_ACCOUNT: T1537
      STEAL_WEB_SESSION_COOKIE: T1539
      CREATE_OR_MODIFY_SYSTEM_PROCESS: T1543
      EVENT_TRIGGERED_EXECUTION: T1546
      BOOT_OR_LOGON_AUTOSTART_EXECUTION: T1547
      KERNEL_MODULES_AND_EXTENSIONS: T1547.006
      SHORTCUT_MODIFICATION: T1547.009
      ABUSE_ELEVATION_CONTROL_MECHANISM: T1548
      ABUSE_ELEVATION_CONTROL_MECHANISM_SUDO_AND_SUDO_CACHING: T1548.003
      UNSECURED_CREDENTIALS: T1552
      CREDENTIALS_IN_FILES: T1552.001
      BASH_HISTORY: T1552.003
      PRIVATE_KEYS: T1552.004
      SUBVERT_TRUST_CONTROL: T1553
      INSTALL_ROOT_CERTIFICATE: T1553.004
      COMPROMISE_HOST_SOFTWARE_BINARY: T1554
      CREDENTIALS_FROM_PASSWORD_STORES: T1555
      MODIFY_AUTHENTICATION_PROCESS: T1556
      PLUGGABLE_AUTHENTICATION_MODULES: T1556.003
      IMPAIR_DEFENSES: T1562
      DISABLE_OR_MODIFY_TOOLS: T1562.001
      INDICATOR_BLOCKING: T1562.006
      DISABLE_OR_MODIFY_LINUX_AUDIT_SYSTEM: T1562.012
      HIDE_ARTIFACTS: T1564
      HIDDEN_FILES_AND_DIRECTORIES: T1564.001
      HIDDEN_USERS: T1564.002
      EXFILTRATION_OVER_WEB_SERVICE: T1567
      EXFILTRATION_TO_CLOUD_STORAGE: T1567.002
      DYNAMIC_RESOLUTION: T1568
      LATERAL_TOOL_TRANSFER: T1570
      HIJACK_EXECUTION_FLOW: T1574
      HIJACK_EXECUTION_FLOW_DYNAMIC_LINKER_HIJACKING: T1574.006
      MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE: T1578
      CREATE_SNAPSHOT: T1578.001
      CLOUD_INFRASTRUCTURE_DISCOVERY: T1580
      DEVELOP_CAPABILITIES: T1587
      DEVELOP_CAPABILITIES_MALWARE: T1587.001
      OBTAIN_CAPABILITIES: T1588
      OBTAIN_CAPABILITIES_MALWARE: T1588.001
      OBTAIN_CAPABILITIES_VULNERABILITIES: T1588.006
      ACTIVE_SCANNING: T1595
      SCANNING_IP_BLOCKS: T1595.001
      STAGE_CAPABILITIES: T1608
      UPLOAD_MALWARE: T1608.001
      CONTAINER_ADMINISTRATION_COMMAND: T1609
      DEPLOY_CONTAINER: T1610
      ESCAPE_TO_HOST: T1611
      CONTAINER_AND_RESOURCE_DISCOVERY: T1613
      REFLECTIVE_CODE_LOADING: T1620
      STEAL_OR_FORGE_AUTHENTICATION_CERTIFICATES: T1649
      FINANCIAL_THEFT: T1657
    """
    TECHNIQUE_UNSPECIFIED = 0
    DATA_OBFUSCATION = 1
    DATA_OBFUSCATION_STEGANOGRAPHY = 2
    OS_CREDENTIAL_DUMPING = 3
    OS_CREDENTIAL_DUMPING_PROC_FILESYSTEM = 4
    OS_CREDENTIAL_DUMPING_ETC_PASSWORD_AND_ETC_SHADOW = 5
    DATA_FROM_LOCAL_SYSTEM = 6
    AUTOMATED_EXFILTRATION = 7
    OBFUSCATED_FILES_OR_INFO = 8
    STEGANOGRAPHY = 9
    COMPILE_AFTER_DELIVERY = 10
    COMMAND_OBFUSCATION = 11
    SCHEDULED_TRANSFER = 12
    SYSTEM_OWNER_USER_DISCOVERY = 13
    MASQUERADING = 14
    MATCH_LEGITIMATE_NAME_OR_LOCATION = 15
    BOOT_OR_LOGON_INITIALIZATION_SCRIPTS = 16
    STARTUP_ITEMS = 17
    NETWORK_SERVICE_DISCOVERY = 18
    SCHEDULED_TASK_JOB = 19
    SCHEDULED_TASK_JOB_CRON = 20
    CONTAINER_ORCHESTRATION_JOB = 21
    PROCESS_INJECTION = 22
    INPUT_CAPTURE = 23
    INPUT_CAPTURE_KEYLOGGING = 24
    PROCESS_DISCOVERY = 25
    COMMAND_AND_SCRIPTING_INTERPRETER = 26
    UNIX_SHELL = 27
    PYTHON = 28
    EXPLOITATION_FOR_PRIVILEGE_ESCALATION = 29
    PERMISSION_GROUPS_DISCOVERY = 30
    CLOUD_GROUPS = 31
    INDICATOR_REMOVAL = 32
    INDICATOR_REMOVAL_CLEAR_LINUX_OR_MAC_SYSTEM_LOGS = 33
    INDICATOR_REMOVAL_CLEAR_COMMAND_HISTORY = 34
    INDICATOR_REMOVAL_FILE_DELETION = 35
    INDICATOR_REMOVAL_TIMESTOMP = 36
    INDICATOR_REMOVAL_CLEAR_MAILBOX_DATA = 37
    APPLICATION_LAYER_PROTOCOL = 38
    DNS = 39
    SOFTWARE_DEPLOYMENT_TOOLS = 40
    VALID_ACCOUNTS = 41
    DEFAULT_ACCOUNTS = 42
    LOCAL_ACCOUNTS = 43
    CLOUD_ACCOUNTS = 44
    FILE_AND_DIRECTORY_DISCOVERY = 45
    ACCOUNT_DISCOVERY_LOCAL_ACCOUNT = 46
    PROXY = 47
    EXTERNAL_PROXY = 48
    MULTI_HOP_PROXY = 49
    ACCOUNT_MANIPULATION = 50
    ADDITIONAL_CLOUD_CREDENTIALS = 51
    ADDITIONAL_CLOUD_ROLES = 52
    SSH_AUTHORIZED_KEYS = 53
    ADDITIONAL_CONTAINER_CLUSTER_ROLES = 54
    MULTI_STAGE_CHANNELS = 55
    INGRESS_TOOL_TRANSFER = 56
    NATIVE_API = 57
    BRUTE_FORCE = 58
    AUTOMATED_COLLECTION = 59
    SHARED_MODULES = 60
    DATA_ENCODING = 61
    STANDARD_ENCODING = 62
    ACCESS_TOKEN_MANIPULATION = 63
    TOKEN_IMPERSONATION_OR_THEFT = 64
    CREATE_ACCOUNT = 65
    LOCAL_ACCOUNT = 66
    DEOBFUSCATE_DECODE_FILES_OR_INFO = 67
    EXPLOIT_PUBLIC_FACING_APPLICATION = 68
    SUPPLY_CHAIN_COMPROMISE = 69
    COMPROMISE_SOFTWARE_DEPENDENCIES_AND_DEVELOPMENT_TOOLS = 70
    USER_EXECUTION = 71
    DOMAIN_POLICY_MODIFICATION = 72
    DATA_DESTRUCTION = 73
    DATA_ENCRYPTED_FOR_IMPACT = 74
    SERVICE_STOP = 75
    INHIBIT_SYSTEM_RECOVERY = 76
    FIRMWARE_CORRUPTION = 77
    RESOURCE_HIJACKING = 78
    NETWORK_DENIAL_OF_SERVICE = 79
    CLOUD_SERVICE_DISCOVERY = 80
    STEAL_APPLICATION_ACCESS_TOKEN = 81
    ACCOUNT_ACCESS_REMOVAL = 82
    TRANSFER_DATA_TO_CLOUD_ACCOUNT = 83
    STEAL_WEB_SESSION_COOKIE = 84
    CREATE_OR_MODIFY_SYSTEM_PROCESS = 85
    EVENT_TRIGGERED_EXECUTION = 86
    BOOT_OR_LOGON_AUTOSTART_EXECUTION = 87
    KERNEL_MODULES_AND_EXTENSIONS = 88
    SHORTCUT_MODIFICATION = 89
    ABUSE_ELEVATION_CONTROL_MECHANISM = 90
    ABUSE_ELEVATION_CONTROL_MECHANISM_SUDO_AND_SUDO_CACHING = 91
    UNSECURED_CREDENTIALS = 92
    CREDENTIALS_IN_FILES = 93
    BASH_HISTORY = 94
    PRIVATE_KEYS = 95
    SUBVERT_TRUST_CONTROL = 96
    INSTALL_ROOT_CERTIFICATE = 97
    COMPROMISE_HOST_SOFTWARE_BINARY = 98
    CREDENTIALS_FROM_PASSWORD_STORES = 99
    MODIFY_AUTHENTICATION_PROCESS = 100
    PLUGGABLE_AUTHENTICATION_MODULES = 101
    IMPAIR_DEFENSES = 102
    DISABLE_OR_MODIFY_TOOLS = 103
    INDICATOR_BLOCKING = 104
    DISABLE_OR_MODIFY_LINUX_AUDIT_SYSTEM = 105
    HIDE_ARTIFACTS = 106
    HIDDEN_FILES_AND_DIRECTORIES = 107
    HIDDEN_USERS = 108
    EXFILTRATION_OVER_WEB_SERVICE = 109
    EXFILTRATION_TO_CLOUD_STORAGE = 110
    DYNAMIC_RESOLUTION = 111
    LATERAL_TOOL_TRANSFER = 112
    HIJACK_EXECUTION_FLOW = 113
    HIJACK_EXECUTION_FLOW_DYNAMIC_LINKER_HIJACKING = 114
    MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE = 115
    CREATE_SNAPSHOT = 116
    CLOUD_INFRASTRUCTURE_DISCOVERY = 117
    DEVELOP_CAPABILITIES = 118
    DEVELOP_CAPABILITIES_MALWARE = 119
    OBTAIN_CAPABILITIES = 120
    OBTAIN_CAPABILITIES_MALWARE = 121
    OBTAIN_CAPABILITIES_VULNERABILITIES = 122
    ACTIVE_SCANNING = 123
    SCANNING_IP_BLOCKS = 124
    STAGE_CAPABILITIES = 125
    UPLOAD_MALWARE = 126
    CONTAINER_ADMINISTRATION_COMMAND = 127
    DEPLOY_CONTAINER = 128
    ESCAPE_TO_HOST = 129
    CONTAINER_AND_RESOURCE_DISCOVERY = 130
    REFLECTIVE_CODE_LOADING = 131
    STEAL_OR_FORGE_AUTHENTICATION_CERTIFICATES = 132
    FINANCIAL_THEFT = 133

  additionalTactics = _messages.EnumField('AdditionalTacticsValueListEntryValuesEnum', 1, repeated=True)
  additionalTechniques = _messages.EnumField('AdditionalTechniquesValueListEntryValuesEnum', 2, repeated=True)
  primaryTactic = _messages.EnumField('PrimaryTacticValueValuesEnum', 3)
  primaryTechniques = _messages.EnumField('PrimaryTechniquesValueListEntryValuesEnum', 4, repeated=True)
  version = _messages.StringField(5)


class GoogleCloudSecuritycenterV2MuteConfig(_messages.Message):
  r"""A mute config is a Cloud SCC resource that contains the configuration to
  mute create/update events of findings.

  Enums:
    TypeValueValuesEnum: Required. The type of the mute config, which
      determines what type of mute state the config affects. Immutable after
      creation.

  Fields:
    createTime: Output only. The time at which the mute config was created.
      This field is set by the server and will be ignored if provided on
      config creation.
    description: A description of the mute config.
    expiryTime: Optional. The expiry of the mute config. Only applicable for
      dynamic configs. If the expiry is set, when the config expires, it is
      removed from all findings.
    filter: Required. An expression that defines the filter to apply across
      create/update events of findings. While creating a filter string, be
      mindful of the scope in which the mute configuration is being created.
      E.g., If a filter contains project = X but is created under the project
      = Y scope, it might not match any findings. The following field and
      operator combinations are supported: * severity: `=`, `:` * category:
      `=`, `:` * resource.name: `=`, `:` * resource.project_name: `=`, `:` *
      resource.project_display_name: `=`, `:` *
      resource.folders.resource_folder: `=`, `:` * resource.parent_name: `=`,
      `:` * resource.parent_display_name: `=`, `:` * resource.type: `=`, `:` *
      finding_class: `=`, `:` * indicator.ip_addresses: `=`, `:` *
      indicator.domains: `=`, `:`
    mostRecentEditor: Output only. Email address of the user who last edited
      the mute config. This field is set by the server and will be ignored if
      provided on config creation or update.
    name: Identifier. This field will be ignored if provided on config
      creation. The following list shows some examples of the format: +
      `organizations/{organization}/muteConfigs/{mute_config}` + `organization
      s/{organization}locations/{location}//muteConfigs/{mute_config}` +
      `folders/{folder}/muteConfigs/{mute_config}` +
      `folders/{folder}/locations/{location}/muteConfigs/{mute_config}` +
      `projects/{project}/muteConfigs/{mute_config}` +
      `projects/{project}/locations/{location}/muteConfigs/{mute_config}`
    type: Required. The type of the mute config, which determines what type of
      mute state the config affects. Immutable after creation.
    updateTime: Output only. The most recent time at which the mute config was
      updated. This field is set by the server and will be ignored if provided
      on config creation or update.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of the mute config, which determines what type of
    mute state the config affects. Immutable after creation.

    Values:
      MUTE_CONFIG_TYPE_UNSPECIFIED: Unused.
      STATIC: A static mute config, which sets the static mute state of future
        matching findings to muted. Once the static mute state has been set,
        finding or config modifications will not affect the state.
      DYNAMIC: A dynamic mute config, which is applied to existing and future
        matching findings, setting their dynamic mute state to "muted". If the
        config is updated or deleted, or a matching finding is updated, such
        that the finding doesn't match the config, the config will be removed
        from the finding, and the finding's dynamic mute state may become
        "unmuted" (unless other configs still match).
    """
    MUTE_CONFIG_TYPE_UNSPECIFIED = 0
    STATIC = 1
    DYNAMIC = 2

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  expiryTime = _messages.StringField(3)
  filter = _messages.StringField(4)
  mostRecentEditor = _messages.StringField(5)
  name = _messages.StringField(6)
  type = _messages.EnumField('TypeValueValuesEnum', 7)
  updateTime = _messages.StringField(8)


class GoogleCloudSecuritycenterV2MuteInfo(_messages.Message):
  r"""Mute information about the finding, including whether the finding has a
  static mute or any matching dynamic mute rules.

  Fields:
    dynamicMuteRecords: The list of dynamic mute rules that currently match
      the finding.
    staticMute: If set, the static mute applied to this finding. Static mutes
      override dynamic mutes. If unset, there is no static mute.
  """

  dynamicMuteRecords = _messages.MessageField('GoogleCloudSecuritycenterV2DynamicMuteRecord', 1, repeated=True)
  staticMute = _messages.MessageField('GoogleCloudSecuritycenterV2StaticMute', 2)


class GoogleCloudSecuritycenterV2Network(_messages.Message):
  r"""Contains information about a VPC network associated with the finding.

  Fields:
    name: The name of the VPC network resource, for example,
      `//compute.googleapis.com/projects/my-project/global/networks/my-
      network`.
  """

  name = _messages.StringField(1)


class GoogleCloudSecuritycenterV2Node(_messages.Message):
  r"""Kubernetes nodes associated with the finding.

  Fields:
    name: [Full resource name](https://google.aip.dev/122#full-resource-names)
      of the Compute Engine VM running the cluster node.
  """

  name = _messages.StringField(1)


class GoogleCloudSecuritycenterV2NodePool(_messages.Message):
  r"""Provides GKE node pool information.

  Fields:
    name: Kubernetes node pool name.
    nodes: Nodes associated with the finding.
  """

  name = _messages.StringField(1)
  nodes = _messages.MessageField('GoogleCloudSecuritycenterV2Node', 2, repeated=True)


class GoogleCloudSecuritycenterV2Notebook(_messages.Message):
  r"""Represents a Jupyter notebook IPYNB file, such as a [Colab Enterprise
  notebook](https://cloud.google.com/colab/docs/introduction) file, that is
  associated with a finding.

  Fields:
    lastAuthor: The user ID of the latest author to modify the notebook.
    name: The name of the notebook.
    notebookUpdateTime: The most recent time the notebook was updated.
    service: The source notebook service, for example, "Colab Enterprise".
  """

  lastAuthor = _messages.StringField(1)
  name = _messages.StringField(2)
  notebookUpdateTime = _messages.StringField(3)
  service = _messages.StringField(4)


class GoogleCloudSecuritycenterV2NotificationMessage(_messages.Message):
  r"""Cloud SCC's Notification

  Fields:
    finding: If it's a Finding based notification config, this field will be
      populated.
    notificationConfigName: Name of the notification config that generated
      current notification.
    resource: The Cloud resource tied to this notification's Finding.
  """

  finding = _messages.MessageField('GoogleCloudSecuritycenterV2Finding', 1)
  notificationConfigName = _messages.StringField(2)
  resource = _messages.MessageField('GoogleCloudSecuritycenterV2Resource', 3)


class GoogleCloudSecuritycenterV2Object(_messages.Message):
  r"""Kubernetes object related to the finding, uniquely identified by GKNN.
  Used if the object Kind is not one of Pod, Node, NodePool, Binding, or
  AccessReview.

  Fields:
    containers: Pod containers associated with this finding, if any.
    group: Kubernetes object group, such as "policy.k8s.io/v1".
    kind: Kubernetes object kind, such as "Namespace".
    name: Kubernetes object name. For details see
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/names/.
    ns: Kubernetes object namespace. Must be a valid DNS label. Named "ns" to
      avoid collision with C++ namespace keyword. For details see
      https://kubernetes.io/docs/tasks/administer-cluster/namespaces/.
  """

  containers = _messages.MessageField('GoogleCloudSecuritycenterV2Container', 1, repeated=True)
  group = _messages.StringField(2)
  kind = _messages.StringField(3)
  name = _messages.StringField(4)
  ns = _messages.StringField(5)


class GoogleCloudSecuritycenterV2OrgPolicy(_messages.Message):
  r"""Contains information about the org policies associated with the finding.

  Fields:
    name: Identifier. The resource name of the org policy. Example:
      "organizations/{organization_id}/policies/{constraint_name}"
  """

  name = _messages.StringField(1)


class GoogleCloudSecuritycenterV2Package(_messages.Message):
  r"""Package is a generic definition of a package.

  Fields:
    cpeUri: The CPE URI where the vulnerability was detected.
    packageName: The name of the package where the vulnerability was detected.
    packageType: Type of package, for example, os, maven, or go.
    packageVersion: The version of the package.
  """

  cpeUri = _messages.StringField(1)
  packageName = _messages.StringField(2)
  packageType = _messages.StringField(3)
  packageVersion = _messages.StringField(4)


class GoogleCloudSecuritycenterV2Pod(_messages.Message):
  r"""A Kubernetes Pod.

  Fields:
    containers: Pod containers associated with this finding, if any.
    labels: Pod labels. For Kubernetes containers, these are applied to the
      container.
    name: Kubernetes Pod name.
    ns: Kubernetes Pod namespace.
  """

  containers = _messages.MessageField('GoogleCloudSecuritycenterV2Container', 1, repeated=True)
  labels = _messages.MessageField('GoogleCloudSecuritycenterV2Label', 2, repeated=True)
  name = _messages.StringField(3)
  ns = _messages.StringField(4)


class GoogleCloudSecuritycenterV2PolicyDriftDetails(_messages.Message):
  r"""The policy field that violates the deployed posture and its expected and
  detected values.

  Fields:
    detectedValue: The detected value that violates the deployed posture, for
      example, `false` or `allowed_values={"projects/22831892"}`.
    expectedValue: The value of this field that was configured in a posture,
      for example, `true` or `allowed_values={"projects/29831892"}`.
    field: The name of the updated field, for example
      constraint.implementation.policy_rules[0].enforce
  """

  detectedValue = _messages.StringField(1)
  expectedValue = _messages.StringField(2)
  field = _messages.StringField(3)


class GoogleCloudSecuritycenterV2PortRange(_messages.Message):
  r"""A port range which is inclusive of the min and max values. Values are
  between 0 and 2^16-1. The max can be equal / must be not smaller than the
  min value. If min and max are equal this indicates that it is a single port.

  Fields:
    max: Maximum port value.
    min: Minimum port value.
  """

  max = _messages.IntegerField(1)
  min = _messages.IntegerField(2)


class GoogleCloudSecuritycenterV2Process(_messages.Message):
  r"""Represents an operating system process.

  Fields:
    args: Process arguments as JSON encoded strings.
    argumentsTruncated: True if `args` is incomplete.
    binary: File information for the process executable.
    envVariables: Process environment variables.
    envVariablesTruncated: True if `env_variables` is incomplete.
    libraries: File information for libraries loaded by the process.
    name: The process name, as displayed in utilities like `top` and `ps`.
      This name can be accessed through `/proc/[pid]/comm` and changed with
      `prctl(PR_SET_NAME)`.
    parentPid: The parent process ID.
    pid: The process ID.
    script: When the process represents the invocation of a script, `binary`
      provides information about the interpreter, while `script` provides
      information about the script file provided to the interpreter.
  """

  args = _messages.StringField(1, repeated=True)
  argumentsTruncated = _messages.BooleanField(2)
  binary = _messages.MessageField('GoogleCloudSecuritycenterV2File', 3)
  envVariables = _messages.MessageField('GoogleCloudSecuritycenterV2EnvironmentVariable', 4, repeated=True)
  envVariablesTruncated = _messages.BooleanField(5)
  libraries = _messages.MessageField('GoogleCloudSecuritycenterV2File', 6, repeated=True)
  name = _messages.StringField(7)
  parentPid = _messages.IntegerField(8)
  pid = _messages.IntegerField(9)
  script = _messages.MessageField('GoogleCloudSecuritycenterV2File', 10)


class GoogleCloudSecuritycenterV2ProcessSignature(_messages.Message):
  r"""Indicates what signature matched this process.

  Enums:
    SignatureTypeValueValuesEnum: Describes the type of resource associated
      with the signature.

  Fields:
    memoryHashSignature: Signature indicating that a binary family was
      matched.
    signatureType: Describes the type of resource associated with the
      signature.
    yaraRuleSignature: Signature indicating that a YARA rule was matched.
  """

  class SignatureTypeValueValuesEnum(_messages.Enum):
    r"""Describes the type of resource associated with the signature.

    Values:
      SIGNATURE_TYPE_UNSPECIFIED: The default signature type.
      SIGNATURE_TYPE_PROCESS: Used for signatures concerning processes.
      SIGNATURE_TYPE_FILE: Used for signatures concerning disks.
    """
    SIGNATURE_TYPE_UNSPECIFIED = 0
    SIGNATURE_TYPE_PROCESS = 1
    SIGNATURE_TYPE_FILE = 2

  memoryHashSignature = _messages.MessageField('GoogleCloudSecuritycenterV2MemoryHashSignature', 1)
  signatureType = _messages.EnumField('SignatureTypeValueValuesEnum', 2)
  yaraRuleSignature = _messages.MessageField('GoogleCloudSecuritycenterV2YaraRuleSignature', 3)


class GoogleCloudSecuritycenterV2Reference(_messages.Message):
  r"""Additional Links

  Fields:
    source: Source of the reference e.g. NVD
    uri: Uri for the mentioned source e.g. https://cve.mitre.org/cgi-
      bin/cvename.cgi?name=CVE-2021-34527.
  """

  source = _messages.StringField(1)
  uri = _messages.StringField(2)


class GoogleCloudSecuritycenterV2Requests(_messages.Message):
  r"""Information about the requests relevant to the finding.

  Fields:
    longTermAllowed: Allowed RPS (requests per second) over the long term.
    longTermDenied: Denied RPS (requests per second) over the long term.
    ratio: For 'Increasing deny ratio', the ratio is the denied traffic
      divided by the allowed traffic. For 'Allowed traffic spike', the ratio
      is the allowed traffic in the short term divided by allowed traffic in
      the long term.
    shortTermAllowed: Allowed RPS (requests per second) in the short term.
  """

  longTermAllowed = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  longTermDenied = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  ratio = _messages.FloatField(3)
  shortTermAllowed = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudSecuritycenterV2Resource(_messages.Message):
  r"""Information related to the Google Cloud resource.

  Enums:
    CloudProviderValueValuesEnum: Indicates which cloud provider the finding
      is from.

  Fields:
    awsMetadata: The AWS metadata associated with the finding.
    azureMetadata: The Azure metadata associated with the finding.
    cloudProvider: Indicates which cloud provider the finding is from.
    displayName: The human readable name of the resource.
    gcpMetadata: The GCP metadata associated with the finding.
    location: The region or location of the service (if applicable).
    name: The full resource name of the resource. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    resourcePath: Provides the path to the resource within the resource
      hierarchy.
    resourcePathString: A string representation of the resource path. For
      Google Cloud, it has the format of `organizations/{organization_id}/fold
      ers/{folder_id}/folders/{folder_id}/projects/{project_id}` where there
      can be any number of folders. For AWS, it has the format of `org/{organi
      zation_id}/ou/{organizational_unit_id}/ou/{organizational_unit_id}/accou
      nt/{account_id}` where there can be any number of organizational units.
      For Azure, it has the format of `mg/{management_group_id}/mg/{management
      _group_id}/subscription/{subscription_id}/rg/{resource_group_name}`
      where there can be any number of management groups.
    service: The service or resource provider associated with the resource.
    type: The full resource type of the resource.
  """

  class CloudProviderValueValuesEnum(_messages.Enum):
    r"""Indicates which cloud provider the finding is from.

    Values:
      CLOUD_PROVIDER_UNSPECIFIED: The cloud provider is unspecified.
      GOOGLE_CLOUD_PLATFORM: The cloud provider is Google Cloud Platform.
      AMAZON_WEB_SERVICES: The cloud provider is Amazon Web Services.
      MICROSOFT_AZURE: The cloud provider is Microsoft Azure.
    """
    CLOUD_PROVIDER_UNSPECIFIED = 0
    GOOGLE_CLOUD_PLATFORM = 1
    AMAZON_WEB_SERVICES = 2
    MICROSOFT_AZURE = 3

  awsMetadata = _messages.MessageField('GoogleCloudSecuritycenterV2AwsMetadata', 1)
  azureMetadata = _messages.MessageField('GoogleCloudSecuritycenterV2AzureMetadata', 2)
  cloudProvider = _messages.EnumField('CloudProviderValueValuesEnum', 3)
  displayName = _messages.StringField(4)
  gcpMetadata = _messages.MessageField('GcpMetadata', 5)
  location = _messages.StringField(6)
  name = _messages.StringField(7)
  resourcePath = _messages.MessageField('GoogleCloudSecuritycenterV2ResourcePath', 8)
  resourcePathString = _messages.StringField(9)
  service = _messages.StringField(10)
  type = _messages.StringField(11)


class GoogleCloudSecuritycenterV2ResourcePath(_messages.Message):
  r"""Represents the path of resources leading up to the resource this finding
  is about.

  Fields:
    nodes: The list of nodes that make the up resource path, ordered from
      lowest level to highest level.
  """

  nodes = _messages.MessageField('GoogleCloudSecuritycenterV2ResourcePathNode', 1, repeated=True)


class GoogleCloudSecuritycenterV2ResourcePathNode(_messages.Message):
  r"""A node within the resource path. Each node represents a resource within
  the resource hierarchy.

  Enums:
    NodeTypeValueValuesEnum: The type of resource this node represents.

  Fields:
    displayName: The display name of the resource this node represents.
    id: The ID of the resource this node represents.
    nodeType: The type of resource this node represents.
  """

  class NodeTypeValueValuesEnum(_messages.Enum):
    r"""The type of resource this node represents.

    Values:
      RESOURCE_PATH_NODE_TYPE_UNSPECIFIED: Node type is unspecified.
      GCP_ORGANIZATION: The node represents a Google Cloud organization.
      GCP_FOLDER: The node represents a Google Cloud folder.
      GCP_PROJECT: The node represents a Google Cloud project.
      AWS_ORGANIZATION: The node represents an AWS organization.
      AWS_ORGANIZATIONAL_UNIT: The node represents an AWS organizational unit.
      AWS_ACCOUNT: The node represents an AWS account.
      AZURE_MANAGEMENT_GROUP: The node represents an Azure management group.
      AZURE_SUBSCRIPTION: The node represents an Azure subscription.
      AZURE_RESOURCE_GROUP: The node represents an Azure resource group.
    """
    RESOURCE_PATH_NODE_TYPE_UNSPECIFIED = 0
    GCP_ORGANIZATION = 1
    GCP_FOLDER = 2
    GCP_PROJECT = 3
    AWS_ORGANIZATION = 4
    AWS_ORGANIZATIONAL_UNIT = 5
    AWS_ACCOUNT = 6
    AZURE_MANAGEMENT_GROUP = 7
    AZURE_SUBSCRIPTION = 8
    AZURE_RESOURCE_GROUP = 9

  displayName = _messages.StringField(1)
  id = _messages.StringField(2)
  nodeType = _messages.EnumField('NodeTypeValueValuesEnum', 3)


class GoogleCloudSecuritycenterV2ResourceValueConfig(_messages.Message):
  r"""A resource value configuration (RVC) is a mapping configuration of
  user's resources to resource values. Used in Attack path simulations.

  Enums:
    CloudProviderValueValuesEnum: Cloud provider this configuration applies to
    ResourceValueValueValuesEnum: Resource value level this expression
      represents Only required when there is no Sensitive Data Protection
      mapping in the request

  Messages:
    ResourceLabelsSelectorValue: List of resource labels to search for,
      evaluated with `AND`. For example, "resource_labels_selector": {"key":
      "value", "env": "prod"} will match resources with labels "key": "value"
      `AND` "env": "prod" https://cloud.google.com/resource-
      manager/docs/creating-managing-labels

  Fields:
    cloudProvider: Cloud provider this configuration applies to
    createTime: Output only. Timestamp this resource value configuration was
      created.
    description: Description of the resource value configuration.
    name: Identifier. Name for the resource value configuration
    resourceLabelsSelector: List of resource labels to search for, evaluated
      with `AND`. For example, "resource_labels_selector": {"key": "value",
      "env": "prod"} will match resources with labels "key": "value" `AND`
      "env": "prod" https://cloud.google.com/resource-manager/docs/creating-
      managing-labels
    resourceType: Apply resource_value only to resources that match
      resource_type. resource_type will be checked with `AND` of other
      resources. For example, "storage.googleapis.com/Bucket" with
      resource_value "HIGH" will apply "HIGH" value only to
      "storage.googleapis.com/Bucket" resources.
    resourceValue: Resource value level this expression represents Only
      required when there is no Sensitive Data Protection mapping in the
      request
    scope: Project or folder to scope this configuration to. For example,
      "project/456" would apply this configuration only to resources in
      "project/456" scope and will be checked with `AND` of other resources.
    sensitiveDataProtectionMapping: A mapping of the sensitivity on Sensitive
      Data Protection finding to resource values. This mapping can only be
      used in combination with a resource_type that is related to BigQuery,
      e.g. "bigquery.googleapis.com/Dataset".
    tagValues: Tag values combined with `AND` to check against. For Google
      Cloud resources, they are tag value IDs in the form of "tagValues/123".
      Example: `[ "tagValues/123", "tagValues/456", "tagValues/789" ]`
      https://cloud.google.com/resource-manager/docs/tags/tags-creating-and-
      managing
    updateTime: Output only. Timestamp this resource value configuration was
      last updated.
  """

  class CloudProviderValueValuesEnum(_messages.Enum):
    r"""Cloud provider this configuration applies to

    Values:
      CLOUD_PROVIDER_UNSPECIFIED: The cloud provider is unspecified.
      GOOGLE_CLOUD_PLATFORM: The cloud provider is Google Cloud Platform.
      AMAZON_WEB_SERVICES: The cloud provider is Amazon Web Services.
      MICROSOFT_AZURE: The cloud provider is Microsoft Azure.
    """
    CLOUD_PROVIDER_UNSPECIFIED = 0
    GOOGLE_CLOUD_PLATFORM = 1
    AMAZON_WEB_SERVICES = 2
    MICROSOFT_AZURE = 3

  class ResourceValueValueValuesEnum(_messages.Enum):
    r"""Resource value level this expression represents Only required when
    there is no Sensitive Data Protection mapping in the request

    Values:
      RESOURCE_VALUE_UNSPECIFIED: Unspecific value
      HIGH: High resource value
      MEDIUM: Medium resource value
      LOW: Low resource value
      NONE: No resource value, e.g. ignore these resources
    """
    RESOURCE_VALUE_UNSPECIFIED = 0
    HIGH = 1
    MEDIUM = 2
    LOW = 3
    NONE = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceLabelsSelectorValue(_messages.Message):
    r"""List of resource labels to search for, evaluated with `AND`. For
    example, "resource_labels_selector": {"key": "value", "env": "prod"} will
    match resources with labels "key": "value" `AND` "env": "prod"
    https://cloud.google.com/resource-manager/docs/creating-managing-labels

    Messages:
      AdditionalProperty: An additional property for a
        ResourceLabelsSelectorValue object.

    Fields:
      additionalProperties: Additional properties of type
        ResourceLabelsSelectorValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceLabelsSelectorValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cloudProvider = _messages.EnumField('CloudProviderValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  name = _messages.StringField(4)
  resourceLabelsSelector = _messages.MessageField('ResourceLabelsSelectorValue', 5)
  resourceType = _messages.StringField(6)
  resourceValue = _messages.EnumField('ResourceValueValueValuesEnum', 7)
  scope = _messages.StringField(8)
  sensitiveDataProtectionMapping = _messages.MessageField('GoogleCloudSecuritycenterV2SensitiveDataProtectionMapping', 9)
  tagValues = _messages.StringField(10, repeated=True)
  updateTime = _messages.StringField(11)


class GoogleCloudSecuritycenterV2Role(_messages.Message):
  r"""Kubernetes Role or ClusterRole.

  Enums:
    KindValueValuesEnum: Role type.

  Fields:
    kind: Role type.
    name: Role name.
    ns: Role namespace.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Role type.

    Values:
      KIND_UNSPECIFIED: Role type is not specified.
      ROLE: Kubernetes Role.
      CLUSTER_ROLE: Kubernetes ClusterRole.
    """
    KIND_UNSPECIFIED = 0
    ROLE = 1
    CLUSTER_ROLE = 2

  kind = _messages.EnumField('KindValueValuesEnum', 1)
  name = _messages.StringField(2)
  ns = _messages.StringField(3)


class GoogleCloudSecuritycenterV2SecurityBulletin(_messages.Message):
  r"""SecurityBulletin are notifications of vulnerabilities of Google
  products.

  Fields:
    bulletinId: ID of the bulletin corresponding to the vulnerability.
    submissionTime: Submission time of this Security Bulletin.
    suggestedUpgradeVersion: This represents a version that the cluster
      receiving this notification should be upgraded to, based on its current
      version. For example, 1.15.0
  """

  bulletinId = _messages.StringField(1)
  submissionTime = _messages.StringField(2)
  suggestedUpgradeVersion = _messages.StringField(3)


class GoogleCloudSecuritycenterV2SecurityMarks(_messages.Message):
  r"""User specified security marks that are attached to the parent Security
  Command Center resource. Security marks are scoped within a Security Command
  Center organization -- they can be modified and viewed by all users who have
  proper permissions on the organization.

  Messages:
    MarksValue: Mutable user specified security marks belonging to the parent
      resource. Constraints are as follows: * Keys and values are treated as
      case insensitive * Keys must be between 1 - 256 characters (inclusive) *
      Keys must be letters, numbers, underscores, or dashes * Values have
      leading and trailing whitespace trimmed, remaining characters must be
      between 1 - 4096 characters (inclusive)

  Fields:
    canonicalName: The canonical name of the marks. The following list shows
      some examples: +
      `organizations/{organization_id}/assets/{asset_id}/securityMarks` + `org
      anizations/{organization_id}/sources/{source_id}/findings/{finding_id}/s
      ecurityMarks` + `organizations/{organization_id}/sources/{source_id}/loc
      ations/{location}/findings/{finding_id}/securityMarks` +
      `folders/{folder_id}/assets/{asset_id}/securityMarks` + `folders/{folder
      _id}/sources/{source_id}/findings/{finding_id}/securityMarks` + `folders
      /{folder_id}/sources/{source_id}/locations/{location}/findings/{finding_
      id}/securityMarks` +
      `projects/{project_number}/assets/{asset_id}/securityMarks` + `projects/
      {project_number}/sources/{source_id}/findings/{finding_id}/securityMarks
      ` + `projects/{project_number}/sources/{source_id}/locations/{location}/
      findings/{finding_id}/securityMarks`
    marks: Mutable user specified security marks belonging to the parent
      resource. Constraints are as follows: * Keys and values are treated as
      case insensitive * Keys must be between 1 - 256 characters (inclusive) *
      Keys must be letters, numbers, underscores, or dashes * Values have
      leading and trailing whitespace trimmed, remaining characters must be
      between 1 - 4096 characters (inclusive)
    name: The relative resource name of the SecurityMarks. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me The following list shows some examples: +
      `organizations/{organization_id}/assets/{asset_id}/securityMarks` + `org
      anizations/{organization_id}/sources/{source_id}/findings/{finding_id}/s
      ecurityMarks` + `organizations/{organization_id}/sources/{source_id}/loc
      ations/{location}/findings/{finding_id}/securityMarks`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MarksValue(_messages.Message):
    r"""Mutable user specified security marks belonging to the parent
    resource. Constraints are as follows: * Keys and values are treated as
    case insensitive * Keys must be between 1 - 256 characters (inclusive) *
    Keys must be letters, numbers, underscores, or dashes * Values have
    leading and trailing whitespace trimmed, remaining characters must be
    between 1 - 4096 characters (inclusive)

    Messages:
      AdditionalProperty: An additional property for a MarksValue object.

    Fields:
      additionalProperties: Additional properties of type MarksValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MarksValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  canonicalName = _messages.StringField(1)
  marks = _messages.MessageField('MarksValue', 2)
  name = _messages.StringField(3)


class GoogleCloudSecuritycenterV2SecurityPolicy(_messages.Message):
  r"""Information about the [Google Cloud Armor security
  policy](https://cloud.google.com/armor/docs/security-policy-overview)
  relevant to the finding.

  Fields:
    name: The name of the Google Cloud Armor security policy, for example,
      "my-security-policy".
    preview: Whether or not the associated rule or policy is in preview mode.
    type: The type of Google Cloud Armor security policy for example, 'backend
      security policy', 'edge security policy', 'network edge security
      policy', or 'always-on DDoS protection'.
  """

  name = _messages.StringField(1)
  preview = _messages.BooleanField(2)
  type = _messages.StringField(3)


class GoogleCloudSecuritycenterV2SecurityPosture(_messages.Message):
  r"""Represents a posture that is deployed on Google Cloud by the Security
  Command Center Posture Management service. A posture contains one or more
  policy sets. A policy set is a group of policies that enforce a set of
  security rules on Google Cloud.

  Fields:
    changedPolicy: The name of the updated policy, for example,
      `projects/{project_id}/policies/{constraint_name}`.
    name: Name of the posture, for example, `CIS-Posture`.
    policy: The ID of the updated policy, for example, `compute-policy-1`.
    policyDriftDetails: The details about a change in an updated policy that
      violates the deployed posture.
    policySet: The name of the updated policy set, for example, `cis-
      policyset`.
    postureDeployment: The name of the posture deployment, for example,
      `organizations/{org_id}/posturedeployments/{posture_deployment_id}`.
    postureDeploymentResource: The project, folder, or organization on which
      the posture is deployed, for example, `projects/{project_number}`.
    revisionId: The version of the posture, for example, `c7cfa2a8`.
  """

  changedPolicy = _messages.StringField(1)
  name = _messages.StringField(2)
  policy = _messages.StringField(3)
  policyDriftDetails = _messages.MessageField('GoogleCloudSecuritycenterV2PolicyDriftDetails', 4, repeated=True)
  policySet = _messages.StringField(5)
  postureDeployment = _messages.StringField(6)
  postureDeploymentResource = _messages.StringField(7)
  revisionId = _messages.StringField(8)


class GoogleCloudSecuritycenterV2SensitiveDataProtectionMapping(_messages.Message):
  r"""Resource value mapping for Sensitive Data Protection findings If any of
  these mappings have a resource value that is not unspecified, the
  resource_value field will be ignored when reading this configuration.

  Enums:
    HighSensitivityMappingValueValuesEnum: Resource value mapping for high-
      sensitivity Sensitive Data Protection findings
    MediumSensitivityMappingValueValuesEnum: Resource value mapping for
      medium-sensitivity Sensitive Data Protection findings

  Fields:
    highSensitivityMapping: Resource value mapping for high-sensitivity
      Sensitive Data Protection findings
    mediumSensitivityMapping: Resource value mapping for medium-sensitivity
      Sensitive Data Protection findings
  """

  class HighSensitivityMappingValueValuesEnum(_messages.Enum):
    r"""Resource value mapping for high-sensitivity Sensitive Data Protection
    findings

    Values:
      RESOURCE_VALUE_UNSPECIFIED: Unspecific value
      HIGH: High resource value
      MEDIUM: Medium resource value
      LOW: Low resource value
      NONE: No resource value, e.g. ignore these resources
    """
    RESOURCE_VALUE_UNSPECIFIED = 0
    HIGH = 1
    MEDIUM = 2
    LOW = 3
    NONE = 4

  class MediumSensitivityMappingValueValuesEnum(_messages.Enum):
    r"""Resource value mapping for medium-sensitivity Sensitive Data
    Protection findings

    Values:
      RESOURCE_VALUE_UNSPECIFIED: Unspecific value
      HIGH: High resource value
      MEDIUM: Medium resource value
      LOW: Low resource value
      NONE: No resource value, e.g. ignore these resources
    """
    RESOURCE_VALUE_UNSPECIFIED = 0
    HIGH = 1
    MEDIUM = 2
    LOW = 3
    NONE = 4

  highSensitivityMapping = _messages.EnumField('HighSensitivityMappingValueValuesEnum', 1)
  mediumSensitivityMapping = _messages.EnumField('MediumSensitivityMappingValueValuesEnum', 2)


class GoogleCloudSecuritycenterV2ServiceAccountDelegationInfo(_messages.Message):
  r"""Identity delegation history of an authenticated service account.

  Fields:
    principalEmail: The email address of a Google account.
    principalSubject: A string representing the principal_subject associated
      with the identity. As compared to `principal_email`, supports principals
      that aren't associated with email addresses, such as third party
      principals. For most identities, the format will be
      `principal://iam.googleapis.com/{identity pool name}/subjects/{subject}`
      except for some GKE identities (GKE_WORKLOAD, FREEFORM,
      GKE_HUB_WORKLOAD) that are still in the legacy format
      `serviceAccount:{identity pool name}[{subject}]`
  """

  principalEmail = _messages.StringField(1)
  principalSubject = _messages.StringField(2)


class GoogleCloudSecuritycenterV2StaticMute(_messages.Message):
  r"""Information about the static mute state. A static mute state overrides
  any dynamic mute rules that apply to this finding. The static mute state can
  be set by a static mute rule or by muting the finding directly.

  Enums:
    StateValueValuesEnum: The static mute state. If the value is `MUTED` or
      `UNMUTED`, then the finding's overall mute state will have the same
      value.

  Fields:
    applyTime: When the static mute was applied.
    state: The static mute state. If the value is `MUTED` or `UNMUTED`, then
      the finding's overall mute state will have the same value.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The static mute state. If the value is `MUTED` or `UNMUTED`, then the
    finding's overall mute state will have the same value.

    Values:
      MUTE_UNSPECIFIED: Unspecified.
      MUTED: Finding has been muted.
      UNMUTED: Finding has been unmuted.
      UNDEFINED: Finding has never been muted/unmuted.
    """
    MUTE_UNSPECIFIED = 0
    MUTED = 1
    UNMUTED = 2
    UNDEFINED = 3

  applyTime = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudSecuritycenterV2Subject(_messages.Message):
  r"""Represents a Kubernetes subject.

  Enums:
    KindValueValuesEnum: Authentication type for the subject.

  Fields:
    kind: Authentication type for the subject.
    name: Name for the subject.
    ns: Namespace for the subject.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Authentication type for the subject.

    Values:
      AUTH_TYPE_UNSPECIFIED: Authentication is not specified.
      USER: User with valid certificate.
      SERVICEACCOUNT: Users managed by Kubernetes API with credentials stored
        as secrets.
      GROUP: Collection of users.
    """
    AUTH_TYPE_UNSPECIFIED = 0
    USER = 1
    SERVICEACCOUNT = 2
    GROUP = 3

  kind = _messages.EnumField('KindValueValuesEnum', 1)
  name = _messages.StringField(2)
  ns = _messages.StringField(3)


class GoogleCloudSecuritycenterV2TicketInfo(_messages.Message):
  r"""Information about the ticket, if any, that is being used to track the
  resolution of the issue that is identified by this finding.

  Fields:
    assignee: The assignee of the ticket in the ticket system.
    description: The description of the ticket in the ticket system.
    id: The identifier of the ticket in the ticket system.
    status: The latest status of the ticket, as reported by the ticket system.
    updateTime: The time when the ticket was last updated, as reported by the
      ticket system.
    uri: The link to the ticket in the ticket system.
  """

  assignee = _messages.StringField(1)
  description = _messages.StringField(2)
  id = _messages.StringField(3)
  status = _messages.StringField(4)
  updateTime = _messages.StringField(5)
  uri = _messages.StringField(6)


class GoogleCloudSecuritycenterV2ToxicCombination(_messages.Message):
  r"""Contains details about a group of security issues that, when the issues
  occur together, represent a greater risk than when the issues occur
  independently. A group of such issues is referred to as a toxic combination.

  Fields:
    attackExposureScore: The [Attack exposure
      score](https://cloud.google.com/security-command-center/docs/attack-
      exposure-learn#attack_exposure_scores) of this toxic combination. The
      score is a measure of how much this toxic combination exposes one or
      more high-value resources to potential attack.
    relatedFindings: List of resource names of findings associated with this
      toxic combination. For example,
      `organizations/123/sources/456/findings/789`.
  """

  attackExposureScore = _messages.FloatField(1)
  relatedFindings = _messages.StringField(2, repeated=True)


class GoogleCloudSecuritycenterV2Vulnerability(_messages.Message):
  r"""Refers to common vulnerability fields e.g. cve, cvss, cwe etc.

  Fields:
    cve: CVE stands for Common Vulnerabilities and Exposures
      (https://cve.mitre.org/about/)
    cwes: Represents one or more Common Weakness Enumeration (CWE) information
      on this vulnerability.
    fixedPackage: The fixed package is relevant to the finding.
    offendingPackage: The offending package is relevant to the finding.
    providerRiskScore: Provider provided risk_score based on multiple factors.
      The higher the risk score, the more risky the vulnerability is.
    reachable: Represents whether the vulnerability is reachable (detected via
      static analysis)
    securityBulletin: The security bulletin is relevant to this finding.
  """

  cve = _messages.MessageField('GoogleCloudSecuritycenterV2Cve', 1)
  cwes = _messages.MessageField('GoogleCloudSecuritycenterV2Cwe', 2, repeated=True)
  fixedPackage = _messages.MessageField('GoogleCloudSecuritycenterV2Package', 3)
  offendingPackage = _messages.MessageField('GoogleCloudSecuritycenterV2Package', 4)
  providerRiskScore = _messages.IntegerField(5)
  reachable = _messages.BooleanField(6)
  securityBulletin = _messages.MessageField('GoogleCloudSecuritycenterV2SecurityBulletin', 7)


class GoogleCloudSecuritycenterV2YaraRuleSignature(_messages.Message):
  r"""A signature corresponding to a YARA rule.

  Fields:
    yaraRule: The name of the YARA rule.
  """

  yaraRule = _messages.StringField(1)


class GroupMembership(_messages.Message):
  r"""Contains details about groups of which this finding is a member. A group
  is a collection of findings that are related in some way.

  Enums:
    GroupTypeValueValuesEnum: Type of group.

  Fields:
    groupId: ID of the group.
    groupType: Type of group.
  """

  class GroupTypeValueValuesEnum(_messages.Enum):
    r"""Type of group.

    Values:
      GROUP_TYPE_UNSPECIFIED: Default value.
      GROUP_TYPE_TOXIC_COMBINATION: Group represents a toxic combination.
      GROUP_TYPE_CHOKEPOINT: Group represents a chokepoint.
    """
    GROUP_TYPE_UNSPECIFIED = 0
    GROUP_TYPE_TOXIC_COMBINATION = 1
    GROUP_TYPE_CHOKEPOINT = 2

  groupId = _messages.StringField(1)
  groupType = _messages.EnumField('GroupTypeValueValuesEnum', 2)


class IamBinding(_messages.Message):
  r"""Represents a particular IAM binding, which captures a member's role
  addition, removal, or state.

  Enums:
    ActionValueValuesEnum: The action that was performed on a Binding.

  Fields:
    action: The action that was performed on a Binding.
    member: A single identity requesting access for a Cloud Platform resource,
      for example, "<EMAIL>".
    role: Role that is assigned to "members". For example, "roles/viewer",
      "roles/editor", or "roles/owner".
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""The action that was performed on a Binding.

    Values:
      ACTION_UNSPECIFIED: Unspecified.
      ADD: Addition of a Binding.
      REMOVE: Removal of a Binding.
    """
    ACTION_UNSPECIFIED = 0
    ADD = 1
    REMOVE = 2

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  member = _messages.StringField(2)
  role = _messages.StringField(3)


class Indicator(_messages.Message):
  r"""Represents what's commonly known as an _indicator of compromise_ (IoC)
  in computer forensics. This is an artifact observed on a network or in an
  operating system that, with high confidence, indicates a computer intrusion.
  For more information, see [Indicator of
  compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise).

  Fields:
    domains: List of domains associated to the Finding.
    ipAddresses: The list of IP addresses that are associated with the
      finding.
    signatures: The list of matched signatures indicating that the given
      process is present in the environment.
    uris: The list of URIs associated to the Findings.
  """

  domains = _messages.StringField(1, repeated=True)
  ipAddresses = _messages.StringField(2, repeated=True)
  signatures = _messages.MessageField('ProcessSignature', 3, repeated=True)
  uris = _messages.StringField(4, repeated=True)


class IpRule(_messages.Message):
  r"""IP rule information.

  Fields:
    portRanges: Optional. An optional list of ports to which this rule
      applies. This field is only applicable for the UDP or (S)TCP protocols.
      Each entry must be either an integer or a range including a min and max
      port number.
    protocol: The IP protocol this rule applies to. This value can either be
      one of the following well known protocol strings (TCP, UDP, ICMP, ESP,
      AH, IPIP, SCTP) or a string representation of the integer value.
  """

  portRanges = _messages.MessageField('PortRange', 1, repeated=True)
  protocol = _messages.StringField(2)


class IpRules(_messages.Message):
  r"""IP rules associated with the finding.

  Enums:
    DirectionValueValuesEnum: The direction that the rule is applicable to,
      one of ingress or egress.

  Fields:
    allowed: Tuple with allowed rules.
    denied: Tuple with denied rules.
    destinationIpRanges: If destination IP ranges are specified, the firewall
      rule applies only to traffic that has a destination IP address in these
      ranges. These ranges must be expressed in CIDR format. Only supports
      IPv4.
    direction: The direction that the rule is applicable to, one of ingress or
      egress.
    exposedServices: Name of the network protocol service, such as FTP, that
      is exposed by the open port. Follows the naming convention available at:
      https://www.iana.org/assignments/service-names-port-numbers/service-
      names-port-numbers.xhtml.
    sourceIpRanges: If source IP ranges are specified, the firewall rule
      applies only to traffic that has a source IP address in these ranges.
      These ranges must be expressed in CIDR format. Only supports IPv4.
  """

  class DirectionValueValuesEnum(_messages.Enum):
    r"""The direction that the rule is applicable to, one of ingress or
    egress.

    Values:
      DIRECTION_UNSPECIFIED: Unspecified direction value.
      INGRESS: Ingress direction value.
      EGRESS: Egress direction value.
    """
    DIRECTION_UNSPECIFIED = 0
    INGRESS = 1
    EGRESS = 2

  allowed = _messages.MessageField('Allowed', 1)
  denied = _messages.MessageField('Denied', 2)
  destinationIpRanges = _messages.StringField(3, repeated=True)
  direction = _messages.EnumField('DirectionValueValuesEnum', 4)
  exposedServices = _messages.StringField(5, repeated=True)
  sourceIpRanges = _messages.StringField(6, repeated=True)


class Job(_messages.Message):
  r"""Describes a job

  Enums:
    StateValueValuesEnum: Output only. State of the job, such as `RUNNING` or
      `PENDING`.

  Fields:
    errorCode: Optional. If the job did not complete successfully, this field
      describes why.
    location: Optional. Gives the location where the job ran, such as `US` or
      `europe-west1`
    name: The fully-qualified name for a job. e.g. `projects//jobs/`
    state: Output only. State of the job, such as `RUNNING` or `PENDING`.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the job, such as `RUNNING` or `PENDING`.

    Values:
      JOB_STATE_UNSPECIFIED: Unspecified represents an unknown state and
        should not be used.
      PENDING: Job is scheduled and pending for run
      RUNNING: Job in progress
      SUCCEEDED: Job has completed with success
      FAILED: Job has completed but with failure
    """
    JOB_STATE_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4

  errorCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  location = _messages.StringField(2)
  name = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class KernelRootkit(_messages.Message):
  r"""Kernel mode rootkit signatures.

  Fields:
    name: Rootkit name, when available.
    unexpectedCodeModification: True if unexpected modifications of kernel
      code memory are present.
    unexpectedFtraceHandler: True if `ftrace` points are present with
      callbacks pointing to regions that are not in the expected kernel or
      module code range.
    unexpectedInterruptHandler: True if interrupt handlers that are are not in
      the expected kernel or module code regions are present.
    unexpectedKernelCodePages: True if kernel code pages that are not in the
      expected kernel or module code regions are present.
    unexpectedKprobeHandler: True if `kprobe` points are present with
      callbacks pointing to regions that are not in the expected kernel or
      module code range.
    unexpectedProcessesInRunqueue: True if unexpected processes in the
      scheduler run queue are present. Such processes are in the run queue,
      but not in the process task list.
    unexpectedReadOnlyDataModification: True if unexpected modifications of
      kernel read-only data memory are present.
    unexpectedSystemCallHandler: True if system call handlers that are are not
      in the expected kernel or module code regions are present.
  """

  name = _messages.StringField(1)
  unexpectedCodeModification = _messages.BooleanField(2)
  unexpectedFtraceHandler = _messages.BooleanField(3)
  unexpectedInterruptHandler = _messages.BooleanField(4)
  unexpectedKernelCodePages = _messages.BooleanField(5)
  unexpectedKprobeHandler = _messages.BooleanField(6)
  unexpectedProcessesInRunqueue = _messages.BooleanField(7)
  unexpectedReadOnlyDataModification = _messages.BooleanField(8)
  unexpectedSystemCallHandler = _messages.BooleanField(9)


class Kubernetes(_messages.Message):
  r"""Kubernetes-related attributes.

  Fields:
    accessReviews: Provides information on any Kubernetes access reviews
      (privilege checks) relevant to the finding.
    bindings: Provides Kubernetes role binding information for findings that
      involve [RoleBindings or
      ClusterRoleBindings](https://cloud.google.com/kubernetes-
      engine/docs/how-to/role-based-access-control).
    nodePools: GKE [node pools](https://cloud.google.com/kubernetes-
      engine/docs/concepts/node-pools) associated with the finding. This field
      contains node pool information for each node, when it is available.
    nodes: Provides Kubernetes [node](https://cloud.google.com/kubernetes-
      engine/docs/concepts/cluster-architecture#nodes) information.
    objects: Kubernetes objects related to the finding.
    pods: Kubernetes [Pods](https://cloud.google.com/kubernetes-
      engine/docs/concepts/pod) associated with the finding. This field
      contains Pod records for each container that is owned by a Pod.
    roles: Provides Kubernetes role information for findings that involve
      [Roles or ClusterRoles](https://cloud.google.com/kubernetes-
      engine/docs/how-to/role-based-access-control).
  """

  accessReviews = _messages.MessageField('AccessReview', 1, repeated=True)
  bindings = _messages.MessageField('GoogleCloudSecuritycenterV1Binding', 2, repeated=True)
  nodePools = _messages.MessageField('NodePool', 3, repeated=True)
  nodes = _messages.MessageField('Node', 4, repeated=True)
  objects = _messages.MessageField('Object', 5, repeated=True)
  pods = _messages.MessageField('Pod', 6, repeated=True)
  roles = _messages.MessageField('Role', 7, repeated=True)


class Label(_messages.Message):
  r"""Represents a generic name-value label. A label has separate name and
  value fields to support filtering with the `contains()` function. For more
  information, see [Filtering on array-type
  fields](https://cloud.google.com/security-command-center/docs/how-to-api-
  list-findings#array-contains-filtering).

  Fields:
    name: Name of the label.
    value: Value that corresponds to the label's name.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class LoadBalancer(_messages.Message):
  r"""Contains information related to the load balancer associated with the
  finding.

  Fields:
    name: The name of the load balancer associated with the finding.
  """

  name = _messages.StringField(1)


class LogEntry(_messages.Message):
  r"""An individual entry in a log.

  Fields:
    cloudLoggingEntry: An individual entry in a log stored in Cloud Logging.
  """

  cloudLoggingEntry = _messages.MessageField('CloudLoggingEntry', 1)


class MemoryHashSignature(_messages.Message):
  r"""A signature corresponding to memory page hashes.

  Fields:
    binaryFamily: The binary family.
    detections: The list of memory hash detections contributing to the binary
      family match.
  """

  binaryFamily = _messages.StringField(1)
  detections = _messages.MessageField('Detection', 2, repeated=True)


class MitreAttack(_messages.Message):
  r"""MITRE ATT&CK tactics and techniques related to this finding. See:
  https://attack.mitre.org

  Enums:
    AdditionalTacticsValueListEntryValuesEnum:
    AdditionalTechniquesValueListEntryValuesEnum:
    PrimaryTacticValueValuesEnum: The MITRE ATT&CK tactic most closely
      represented by this finding, if any.
    PrimaryTechniquesValueListEntryValuesEnum:

  Fields:
    additionalTactics: Additional MITRE ATT&CK tactics related to this
      finding, if any.
    additionalTechniques: Additional MITRE ATT&CK techniques related to this
      finding, if any, along with any of their respective parent techniques.
    primaryTactic: The MITRE ATT&CK tactic most closely represented by this
      finding, if any.
    primaryTechniques: The MITRE ATT&CK technique most closely represented by
      this finding, if any. primary_techniques is a repeated field because
      there are multiple levels of MITRE ATT&CK techniques. If the technique
      most closely represented by this finding is a sub-technique (e.g.
      `SCANNING_IP_BLOCKS`), both the sub-technique and its parent
      technique(s) will be listed (e.g. `SCANNING_IP_BLOCKS`,
      `ACTIVE_SCANNING`).
    version: The MITRE ATT&CK version referenced by the above fields. E.g.
      "8".
  """

  class AdditionalTacticsValueListEntryValuesEnum(_messages.Enum):
    r"""AdditionalTacticsValueListEntryValuesEnum enum type.

    Values:
      TACTIC_UNSPECIFIED: Unspecified value.
      RECONNAISSANCE: TA0043
      RESOURCE_DEVELOPMENT: TA0042
      INITIAL_ACCESS: TA0001
      EXECUTION: TA0002
      PERSISTENCE: TA0003
      PRIVILEGE_ESCALATION: TA0004
      DEFENSE_EVASION: TA0005
      CREDENTIAL_ACCESS: TA0006
      DISCOVERY: TA0007
      LATERAL_MOVEMENT: TA0008
      COLLECTION: TA0009
      COMMAND_AND_CONTROL: TA0011
      EXFILTRATION: TA0010
      IMPACT: TA0040
    """
    TACTIC_UNSPECIFIED = 0
    RECONNAISSANCE = 1
    RESOURCE_DEVELOPMENT = 2
    INITIAL_ACCESS = 3
    EXECUTION = 4
    PERSISTENCE = 5
    PRIVILEGE_ESCALATION = 6
    DEFENSE_EVASION = 7
    CREDENTIAL_ACCESS = 8
    DISCOVERY = 9
    LATERAL_MOVEMENT = 10
    COLLECTION = 11
    COMMAND_AND_CONTROL = 12
    EXFILTRATION = 13
    IMPACT = 14

  class AdditionalTechniquesValueListEntryValuesEnum(_messages.Enum):
    r"""AdditionalTechniquesValueListEntryValuesEnum enum type.

    Values:
      TECHNIQUE_UNSPECIFIED: Unspecified value.
      DATA_OBFUSCATION: T1001
      DATA_OBFUSCATION_STEGANOGRAPHY: T1001.002
      OS_CREDENTIAL_DUMPING: T1003
      OS_CREDENTIAL_DUMPING_PROC_FILESYSTEM: T1003.007
      OS_CREDENTIAL_DUMPING_ETC_PASSWORD_AND_ETC_SHADOW: T1003.008
      DATA_FROM_LOCAL_SYSTEM: T1005
      AUTOMATED_EXFILTRATION: T1020
      OBFUSCATED_FILES_OR_INFO: T1027
      STEGANOGRAPHY: T1027.003
      COMPILE_AFTER_DELIVERY: T1027.004
      COMMAND_OBFUSCATION: T1027.010
      SCHEDULED_TRANSFER: T1029
      SYSTEM_OWNER_USER_DISCOVERY: T1033
      MASQUERADING: T1036
      MATCH_LEGITIMATE_NAME_OR_LOCATION: T1036.005
      BOOT_OR_LOGON_INITIALIZATION_SCRIPTS: T1037
      STARTUP_ITEMS: T1037.005
      NETWORK_SERVICE_DISCOVERY: T1046
      SCHEDULED_TASK_JOB: T1053
      SCHEDULED_TASK_JOB_CRON: T1053.003
      CONTAINER_ORCHESTRATION_JOB: T1053.007
      PROCESS_INJECTION: T1055
      INPUT_CAPTURE: T1056
      INPUT_CAPTURE_KEYLOGGING: T1056.001
      PROCESS_DISCOVERY: T1057
      COMMAND_AND_SCRIPTING_INTERPRETER: T1059
      UNIX_SHELL: T1059.004
      PYTHON: T1059.006
      EXPLOITATION_FOR_PRIVILEGE_ESCALATION: T1068
      PERMISSION_GROUPS_DISCOVERY: T1069
      CLOUD_GROUPS: T1069.003
      INDICATOR_REMOVAL: T1070
      INDICATOR_REMOVAL_CLEAR_LINUX_OR_MAC_SYSTEM_LOGS: T1070.002
      INDICATOR_REMOVAL_CLEAR_COMMAND_HISTORY: T1070.003
      INDICATOR_REMOVAL_FILE_DELETION: T1070.004
      INDICATOR_REMOVAL_TIMESTOMP: T1070.006
      INDICATOR_REMOVAL_CLEAR_MAILBOX_DATA: T1070.008
      APPLICATION_LAYER_PROTOCOL: T1071
      DNS: T1071.004
      SOFTWARE_DEPLOYMENT_TOOLS: T1072
      VALID_ACCOUNTS: T1078
      DEFAULT_ACCOUNTS: T1078.001
      LOCAL_ACCOUNTS: T1078.003
      CLOUD_ACCOUNTS: T1078.004
      FILE_AND_DIRECTORY_DISCOVERY: T1083
      ACCOUNT_DISCOVERY_LOCAL_ACCOUNT: T1087.001
      PROXY: T1090
      EXTERNAL_PROXY: T1090.002
      MULTI_HOP_PROXY: T1090.003
      ACCOUNT_MANIPULATION: T1098
      ADDITIONAL_CLOUD_CREDENTIALS: T1098.001
      ADDITIONAL_CLOUD_ROLES: T1098.003
      SSH_AUTHORIZED_KEYS: T1098.004
      ADDITIONAL_CONTAINER_CLUSTER_ROLES: T1098.006
      MULTI_STAGE_CHANNELS: T1104
      INGRESS_TOOL_TRANSFER: T1105
      NATIVE_API: T1106
      BRUTE_FORCE: T1110
      AUTOMATED_COLLECTION: T1119
      SHARED_MODULES: T1129
      DATA_ENCODING: T1132
      STANDARD_ENCODING: T1132.001
      ACCESS_TOKEN_MANIPULATION: T1134
      TOKEN_IMPERSONATION_OR_THEFT: T1134.001
      CREATE_ACCOUNT: T1136
      LOCAL_ACCOUNT: T1136.001
      DEOBFUSCATE_DECODE_FILES_OR_INFO: T1140
      EXPLOIT_PUBLIC_FACING_APPLICATION: T1190
      SUPPLY_CHAIN_COMPROMISE: T1195
      COMPROMISE_SOFTWARE_DEPENDENCIES_AND_DEVELOPMENT_TOOLS: T1195.001
      USER_EXECUTION: T1204
      DOMAIN_POLICY_MODIFICATION: T1484
      DATA_DESTRUCTION: T1485
      DATA_ENCRYPTED_FOR_IMPACT: T1486
      SERVICE_STOP: T1489
      INHIBIT_SYSTEM_RECOVERY: T1490
      FIRMWARE_CORRUPTION: T1495
      RESOURCE_HIJACKING: T1496
      NETWORK_DENIAL_OF_SERVICE: T1498
      CLOUD_SERVICE_DISCOVERY: T1526
      STEAL_APPLICATION_ACCESS_TOKEN: T1528
      ACCOUNT_ACCESS_REMOVAL: T1531
      TRANSFER_DATA_TO_CLOUD_ACCOUNT: T1537
      STEAL_WEB_SESSION_COOKIE: T1539
      CREATE_OR_MODIFY_SYSTEM_PROCESS: T1543
      EVENT_TRIGGERED_EXECUTION: T1546
      BOOT_OR_LOGON_AUTOSTART_EXECUTION: T1547
      KERNEL_MODULES_AND_EXTENSIONS: T1547.006
      SHORTCUT_MODIFICATION: T1547.009
      ABUSE_ELEVATION_CONTROL_MECHANISM: T1548
      ABUSE_ELEVATION_CONTROL_MECHANISM_SUDO_AND_SUDO_CACHING: T1548.003
      UNSECURED_CREDENTIALS: T1552
      CREDENTIALS_IN_FILES: T1552.001
      BASH_HISTORY: T1552.003
      PRIVATE_KEYS: T1552.004
      SUBVERT_TRUST_CONTROL: T1553
      INSTALL_ROOT_CERTIFICATE: T1553.004
      COMPROMISE_HOST_SOFTWARE_BINARY: T1554
      CREDENTIALS_FROM_PASSWORD_STORES: T1555
      MODIFY_AUTHENTICATION_PROCESS: T1556
      PLUGGABLE_AUTHENTICATION_MODULES: T1556.003
      IMPAIR_DEFENSES: T1562
      DISABLE_OR_MODIFY_TOOLS: T1562.001
      INDICATOR_BLOCKING: T1562.006
      DISABLE_OR_MODIFY_LINUX_AUDIT_SYSTEM: T1562.012
      HIDE_ARTIFACTS: T1564
      HIDDEN_FILES_AND_DIRECTORIES: T1564.001
      HIDDEN_USERS: T1564.002
      EXFILTRATION_OVER_WEB_SERVICE: T1567
      EXFILTRATION_TO_CLOUD_STORAGE: T1567.002
      DYNAMIC_RESOLUTION: T1568
      LATERAL_TOOL_TRANSFER: T1570
      HIJACK_EXECUTION_FLOW: T1574
      HIJACK_EXECUTION_FLOW_DYNAMIC_LINKER_HIJACKING: T1574.006
      MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE: T1578
      CREATE_SNAPSHOT: T1578.001
      CLOUD_INFRASTRUCTURE_DISCOVERY: T1580
      DEVELOP_CAPABILITIES: T1587
      DEVELOP_CAPABILITIES_MALWARE: T1587.001
      OBTAIN_CAPABILITIES: T1588
      OBTAIN_CAPABILITIES_MALWARE: T1588.001
      OBTAIN_CAPABILITIES_VULNERABILITIES: T1588.006
      ACTIVE_SCANNING: T1595
      SCANNING_IP_BLOCKS: T1595.001
      STAGE_CAPABILITIES: T1608
      UPLOAD_MALWARE: T1608.001
      CONTAINER_ADMINISTRATION_COMMAND: T1609
      DEPLOY_CONTAINER: T1610
      ESCAPE_TO_HOST: T1611
      CONTAINER_AND_RESOURCE_DISCOVERY: T1613
      REFLECTIVE_CODE_LOADING: T1620
      STEAL_OR_FORGE_AUTHENTICATION_CERTIFICATES: T1649
      FINANCIAL_THEFT: T1657
    """
    TECHNIQUE_UNSPECIFIED = 0
    DATA_OBFUSCATION = 1
    DATA_OBFUSCATION_STEGANOGRAPHY = 2
    OS_CREDENTIAL_DUMPING = 3
    OS_CREDENTIAL_DUMPING_PROC_FILESYSTEM = 4
    OS_CREDENTIAL_DUMPING_ETC_PASSWORD_AND_ETC_SHADOW = 5
    DATA_FROM_LOCAL_SYSTEM = 6
    AUTOMATED_EXFILTRATION = 7
    OBFUSCATED_FILES_OR_INFO = 8
    STEGANOGRAPHY = 9
    COMPILE_AFTER_DELIVERY = 10
    COMMAND_OBFUSCATION = 11
    SCHEDULED_TRANSFER = 12
    SYSTEM_OWNER_USER_DISCOVERY = 13
    MASQUERADING = 14
    MATCH_LEGITIMATE_NAME_OR_LOCATION = 15
    BOOT_OR_LOGON_INITIALIZATION_SCRIPTS = 16
    STARTUP_ITEMS = 17
    NETWORK_SERVICE_DISCOVERY = 18
    SCHEDULED_TASK_JOB = 19
    SCHEDULED_TASK_JOB_CRON = 20
    CONTAINER_ORCHESTRATION_JOB = 21
    PROCESS_INJECTION = 22
    INPUT_CAPTURE = 23
    INPUT_CAPTURE_KEYLOGGING = 24
    PROCESS_DISCOVERY = 25
    COMMAND_AND_SCRIPTING_INTERPRETER = 26
    UNIX_SHELL = 27
    PYTHON = 28
    EXPLOITATION_FOR_PRIVILEGE_ESCALATION = 29
    PERMISSION_GROUPS_DISCOVERY = 30
    CLOUD_GROUPS = 31
    INDICATOR_REMOVAL = 32
    INDICATOR_REMOVAL_CLEAR_LINUX_OR_MAC_SYSTEM_LOGS = 33
    INDICATOR_REMOVAL_CLEAR_COMMAND_HISTORY = 34
    INDICATOR_REMOVAL_FILE_DELETION = 35
    INDICATOR_REMOVAL_TIMESTOMP = 36
    INDICATOR_REMOVAL_CLEAR_MAILBOX_DATA = 37
    APPLICATION_LAYER_PROTOCOL = 38
    DNS = 39
    SOFTWARE_DEPLOYMENT_TOOLS = 40
    VALID_ACCOUNTS = 41
    DEFAULT_ACCOUNTS = 42
    LOCAL_ACCOUNTS = 43
    CLOUD_ACCOUNTS = 44
    FILE_AND_DIRECTORY_DISCOVERY = 45
    ACCOUNT_DISCOVERY_LOCAL_ACCOUNT = 46
    PROXY = 47
    EXTERNAL_PROXY = 48
    MULTI_HOP_PROXY = 49
    ACCOUNT_MANIPULATION = 50
    ADDITIONAL_CLOUD_CREDENTIALS = 51
    ADDITIONAL_CLOUD_ROLES = 52
    SSH_AUTHORIZED_KEYS = 53
    ADDITIONAL_CONTAINER_CLUSTER_ROLES = 54
    MULTI_STAGE_CHANNELS = 55
    INGRESS_TOOL_TRANSFER = 56
    NATIVE_API = 57
    BRUTE_FORCE = 58
    AUTOMATED_COLLECTION = 59
    SHARED_MODULES = 60
    DATA_ENCODING = 61
    STANDARD_ENCODING = 62
    ACCESS_TOKEN_MANIPULATION = 63
    TOKEN_IMPERSONATION_OR_THEFT = 64
    CREATE_ACCOUNT = 65
    LOCAL_ACCOUNT = 66
    DEOBFUSCATE_DECODE_FILES_OR_INFO = 67
    EXPLOIT_PUBLIC_FACING_APPLICATION = 68
    SUPPLY_CHAIN_COMPROMISE = 69
    COMPROMISE_SOFTWARE_DEPENDENCIES_AND_DEVELOPMENT_TOOLS = 70
    USER_EXECUTION = 71
    DOMAIN_POLICY_MODIFICATION = 72
    DATA_DESTRUCTION = 73
    DATA_ENCRYPTED_FOR_IMPACT = 74
    SERVICE_STOP = 75
    INHIBIT_SYSTEM_RECOVERY = 76
    FIRMWARE_CORRUPTION = 77
    RESOURCE_HIJACKING = 78
    NETWORK_DENIAL_OF_SERVICE = 79
    CLOUD_SERVICE_DISCOVERY = 80
    STEAL_APPLICATION_ACCESS_TOKEN = 81
    ACCOUNT_ACCESS_REMOVAL = 82
    TRANSFER_DATA_TO_CLOUD_ACCOUNT = 83
    STEAL_WEB_SESSION_COOKIE = 84
    CREATE_OR_MODIFY_SYSTEM_PROCESS = 85
    EVENT_TRIGGERED_EXECUTION = 86
    BOOT_OR_LOGON_AUTOSTART_EXECUTION = 87
    KERNEL_MODULES_AND_EXTENSIONS = 88
    SHORTCUT_MODIFICATION = 89
    ABUSE_ELEVATION_CONTROL_MECHANISM = 90
    ABUSE_ELEVATION_CONTROL_MECHANISM_SUDO_AND_SUDO_CACHING = 91
    UNSECURED_CREDENTIALS = 92
    CREDENTIALS_IN_FILES = 93
    BASH_HISTORY = 94
    PRIVATE_KEYS = 95
    SUBVERT_TRUST_CONTROL = 96
    INSTALL_ROOT_CERTIFICATE = 97
    COMPROMISE_HOST_SOFTWARE_BINARY = 98
    CREDENTIALS_FROM_PASSWORD_STORES = 99
    MODIFY_AUTHENTICATION_PROCESS = 100
    PLUGGABLE_AUTHENTICATION_MODULES = 101
    IMPAIR_DEFENSES = 102
    DISABLE_OR_MODIFY_TOOLS = 103
    INDICATOR_BLOCKING = 104
    DISABLE_OR_MODIFY_LINUX_AUDIT_SYSTEM = 105
    HIDE_ARTIFACTS = 106
    HIDDEN_FILES_AND_DIRECTORIES = 107
    HIDDEN_USERS = 108
    EXFILTRATION_OVER_WEB_SERVICE = 109
    EXFILTRATION_TO_CLOUD_STORAGE = 110
    DYNAMIC_RESOLUTION = 111
    LATERAL_TOOL_TRANSFER = 112
    HIJACK_EXECUTION_FLOW = 113
    HIJACK_EXECUTION_FLOW_DYNAMIC_LINKER_HIJACKING = 114
    MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE = 115
    CREATE_SNAPSHOT = 116
    CLOUD_INFRASTRUCTURE_DISCOVERY = 117
    DEVELOP_CAPABILITIES = 118
    DEVELOP_CAPABILITIES_MALWARE = 119
    OBTAIN_CAPABILITIES = 120
    OBTAIN_CAPABILITIES_MALWARE = 121
    OBTAIN_CAPABILITIES_VULNERABILITIES = 122
    ACTIVE_SCANNING = 123
    SCANNING_IP_BLOCKS = 124
    STAGE_CAPABILITIES = 125
    UPLOAD_MALWARE = 126
    CONTAINER_ADMINISTRATION_COMMAND = 127
    DEPLOY_CONTAINER = 128
    ESCAPE_TO_HOST = 129
    CONTAINER_AND_RESOURCE_DISCOVERY = 130
    REFLECTIVE_CODE_LOADING = 131
    STEAL_OR_FORGE_AUTHENTICATION_CERTIFICATES = 132
    FINANCIAL_THEFT = 133

  class PrimaryTacticValueValuesEnum(_messages.Enum):
    r"""The MITRE ATT&CK tactic most closely represented by this finding, if
    any.

    Values:
      TACTIC_UNSPECIFIED: Unspecified value.
      RECONNAISSANCE: TA0043
      RESOURCE_DEVELOPMENT: TA0042
      INITIAL_ACCESS: TA0001
      EXECUTION: TA0002
      PERSISTENCE: TA0003
      PRIVILEGE_ESCALATION: TA0004
      DEFENSE_EVASION: TA0005
      CREDENTIAL_ACCESS: TA0006
      DISCOVERY: TA0007
      LATERAL_MOVEMENT: TA0008
      COLLECTION: TA0009
      COMMAND_AND_CONTROL: TA0011
      EXFILTRATION: TA0010
      IMPACT: TA0040
    """
    TACTIC_UNSPECIFIED = 0
    RECONNAISSANCE = 1
    RESOURCE_DEVELOPMENT = 2
    INITIAL_ACCESS = 3
    EXECUTION = 4
    PERSISTENCE = 5
    PRIVILEGE_ESCALATION = 6
    DEFENSE_EVASION = 7
    CREDENTIAL_ACCESS = 8
    DISCOVERY = 9
    LATERAL_MOVEMENT = 10
    COLLECTION = 11
    COMMAND_AND_CONTROL = 12
    EXFILTRATION = 13
    IMPACT = 14

  class PrimaryTechniquesValueListEntryValuesEnum(_messages.Enum):
    r"""PrimaryTechniquesValueListEntryValuesEnum enum type.

    Values:
      TECHNIQUE_UNSPECIFIED: Unspecified value.
      DATA_OBFUSCATION: T1001
      DATA_OBFUSCATION_STEGANOGRAPHY: T1001.002
      OS_CREDENTIAL_DUMPING: T1003
      OS_CREDENTIAL_DUMPING_PROC_FILESYSTEM: T1003.007
      OS_CREDENTIAL_DUMPING_ETC_PASSWORD_AND_ETC_SHADOW: T1003.008
      DATA_FROM_LOCAL_SYSTEM: T1005
      AUTOMATED_EXFILTRATION: T1020
      OBFUSCATED_FILES_OR_INFO: T1027
      STEGANOGRAPHY: T1027.003
      COMPILE_AFTER_DELIVERY: T1027.004
      COMMAND_OBFUSCATION: T1027.010
      SCHEDULED_TRANSFER: T1029
      SYSTEM_OWNER_USER_DISCOVERY: T1033
      MASQUERADING: T1036
      MATCH_LEGITIMATE_NAME_OR_LOCATION: T1036.005
      BOOT_OR_LOGON_INITIALIZATION_SCRIPTS: T1037
      STARTUP_ITEMS: T1037.005
      NETWORK_SERVICE_DISCOVERY: T1046
      SCHEDULED_TASK_JOB: T1053
      SCHEDULED_TASK_JOB_CRON: T1053.003
      CONTAINER_ORCHESTRATION_JOB: T1053.007
      PROCESS_INJECTION: T1055
      INPUT_CAPTURE: T1056
      INPUT_CAPTURE_KEYLOGGING: T1056.001
      PROCESS_DISCOVERY: T1057
      COMMAND_AND_SCRIPTING_INTERPRETER: T1059
      UNIX_SHELL: T1059.004
      PYTHON: T1059.006
      EXPLOITATION_FOR_PRIVILEGE_ESCALATION: T1068
      PERMISSION_GROUPS_DISCOVERY: T1069
      CLOUD_GROUPS: T1069.003
      INDICATOR_REMOVAL: T1070
      INDICATOR_REMOVAL_CLEAR_LINUX_OR_MAC_SYSTEM_LOGS: T1070.002
      INDICATOR_REMOVAL_CLEAR_COMMAND_HISTORY: T1070.003
      INDICATOR_REMOVAL_FILE_DELETION: T1070.004
      INDICATOR_REMOVAL_TIMESTOMP: T1070.006
      INDICATOR_REMOVAL_CLEAR_MAILBOX_DATA: T1070.008
      APPLICATION_LAYER_PROTOCOL: T1071
      DNS: T1071.004
      SOFTWARE_DEPLOYMENT_TOOLS: T1072
      VALID_ACCOUNTS: T1078
      DEFAULT_ACCOUNTS: T1078.001
      LOCAL_ACCOUNTS: T1078.003
      CLOUD_ACCOUNTS: T1078.004
      FILE_AND_DIRECTORY_DISCOVERY: T1083
      ACCOUNT_DISCOVERY_LOCAL_ACCOUNT: T1087.001
      PROXY: T1090
      EXTERNAL_PROXY: T1090.002
      MULTI_HOP_PROXY: T1090.003
      ACCOUNT_MANIPULATION: T1098
      ADDITIONAL_CLOUD_CREDENTIALS: T1098.001
      ADDITIONAL_CLOUD_ROLES: T1098.003
      SSH_AUTHORIZED_KEYS: T1098.004
      ADDITIONAL_CONTAINER_CLUSTER_ROLES: T1098.006
      MULTI_STAGE_CHANNELS: T1104
      INGRESS_TOOL_TRANSFER: T1105
      NATIVE_API: T1106
      BRUTE_FORCE: T1110
      AUTOMATED_COLLECTION: T1119
      SHARED_MODULES: T1129
      DATA_ENCODING: T1132
      STANDARD_ENCODING: T1132.001
      ACCESS_TOKEN_MANIPULATION: T1134
      TOKEN_IMPERSONATION_OR_THEFT: T1134.001
      CREATE_ACCOUNT: T1136
      LOCAL_ACCOUNT: T1136.001
      DEOBFUSCATE_DECODE_FILES_OR_INFO: T1140
      EXPLOIT_PUBLIC_FACING_APPLICATION: T1190
      SUPPLY_CHAIN_COMPROMISE: T1195
      COMPROMISE_SOFTWARE_DEPENDENCIES_AND_DEVELOPMENT_TOOLS: T1195.001
      USER_EXECUTION: T1204
      DOMAIN_POLICY_MODIFICATION: T1484
      DATA_DESTRUCTION: T1485
      DATA_ENCRYPTED_FOR_IMPACT: T1486
      SERVICE_STOP: T1489
      INHIBIT_SYSTEM_RECOVERY: T1490
      FIRMWARE_CORRUPTION: T1495
      RESOURCE_HIJACKING: T1496
      NETWORK_DENIAL_OF_SERVICE: T1498
      CLOUD_SERVICE_DISCOVERY: T1526
      STEAL_APPLICATION_ACCESS_TOKEN: T1528
      ACCOUNT_ACCESS_REMOVAL: T1531
      TRANSFER_DATA_TO_CLOUD_ACCOUNT: T1537
      STEAL_WEB_SESSION_COOKIE: T1539
      CREATE_OR_MODIFY_SYSTEM_PROCESS: T1543
      EVENT_TRIGGERED_EXECUTION: T1546
      BOOT_OR_LOGON_AUTOSTART_EXECUTION: T1547
      KERNEL_MODULES_AND_EXTENSIONS: T1547.006
      SHORTCUT_MODIFICATION: T1547.009
      ABUSE_ELEVATION_CONTROL_MECHANISM: T1548
      ABUSE_ELEVATION_CONTROL_MECHANISM_SUDO_AND_SUDO_CACHING: T1548.003
      UNSECURED_CREDENTIALS: T1552
      CREDENTIALS_IN_FILES: T1552.001
      BASH_HISTORY: T1552.003
      PRIVATE_KEYS: T1552.004
      SUBVERT_TRUST_CONTROL: T1553
      INSTALL_ROOT_CERTIFICATE: T1553.004
      COMPROMISE_HOST_SOFTWARE_BINARY: T1554
      CREDENTIALS_FROM_PASSWORD_STORES: T1555
      MODIFY_AUTHENTICATION_PROCESS: T1556
      PLUGGABLE_AUTHENTICATION_MODULES: T1556.003
      IMPAIR_DEFENSES: T1562
      DISABLE_OR_MODIFY_TOOLS: T1562.001
      INDICATOR_BLOCKING: T1562.006
      DISABLE_OR_MODIFY_LINUX_AUDIT_SYSTEM: T1562.012
      HIDE_ARTIFACTS: T1564
      HIDDEN_FILES_AND_DIRECTORIES: T1564.001
      HIDDEN_USERS: T1564.002
      EXFILTRATION_OVER_WEB_SERVICE: T1567
      EXFILTRATION_TO_CLOUD_STORAGE: T1567.002
      DYNAMIC_RESOLUTION: T1568
      LATERAL_TOOL_TRANSFER: T1570
      HIJACK_EXECUTION_FLOW: T1574
      HIJACK_EXECUTION_FLOW_DYNAMIC_LINKER_HIJACKING: T1574.006
      MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE: T1578
      CREATE_SNAPSHOT: T1578.001
      CLOUD_INFRASTRUCTURE_DISCOVERY: T1580
      DEVELOP_CAPABILITIES: T1587
      DEVELOP_CAPABILITIES_MALWARE: T1587.001
      OBTAIN_CAPABILITIES: T1588
      OBTAIN_CAPABILITIES_MALWARE: T1588.001
      OBTAIN_CAPABILITIES_VULNERABILITIES: T1588.006
      ACTIVE_SCANNING: T1595
      SCANNING_IP_BLOCKS: T1595.001
      STAGE_CAPABILITIES: T1608
      UPLOAD_MALWARE: T1608.001
      CONTAINER_ADMINISTRATION_COMMAND: T1609
      DEPLOY_CONTAINER: T1610
      ESCAPE_TO_HOST: T1611
      CONTAINER_AND_RESOURCE_DISCOVERY: T1613
      REFLECTIVE_CODE_LOADING: T1620
      STEAL_OR_FORGE_AUTHENTICATION_CERTIFICATES: T1649
      FINANCIAL_THEFT: T1657
    """
    TECHNIQUE_UNSPECIFIED = 0
    DATA_OBFUSCATION = 1
    DATA_OBFUSCATION_STEGANOGRAPHY = 2
    OS_CREDENTIAL_DUMPING = 3
    OS_CREDENTIAL_DUMPING_PROC_FILESYSTEM = 4
    OS_CREDENTIAL_DUMPING_ETC_PASSWORD_AND_ETC_SHADOW = 5
    DATA_FROM_LOCAL_SYSTEM = 6
    AUTOMATED_EXFILTRATION = 7
    OBFUSCATED_FILES_OR_INFO = 8
    STEGANOGRAPHY = 9
    COMPILE_AFTER_DELIVERY = 10
    COMMAND_OBFUSCATION = 11
    SCHEDULED_TRANSFER = 12
    SYSTEM_OWNER_USER_DISCOVERY = 13
    MASQUERADING = 14
    MATCH_LEGITIMATE_NAME_OR_LOCATION = 15
    BOOT_OR_LOGON_INITIALIZATION_SCRIPTS = 16
    STARTUP_ITEMS = 17
    NETWORK_SERVICE_DISCOVERY = 18
    SCHEDULED_TASK_JOB = 19
    SCHEDULED_TASK_JOB_CRON = 20
    CONTAINER_ORCHESTRATION_JOB = 21
    PROCESS_INJECTION = 22
    INPUT_CAPTURE = 23
    INPUT_CAPTURE_KEYLOGGING = 24
    PROCESS_DISCOVERY = 25
    COMMAND_AND_SCRIPTING_INTERPRETER = 26
    UNIX_SHELL = 27
    PYTHON = 28
    EXPLOITATION_FOR_PRIVILEGE_ESCALATION = 29
    PERMISSION_GROUPS_DISCOVERY = 30
    CLOUD_GROUPS = 31
    INDICATOR_REMOVAL = 32
    INDICATOR_REMOVAL_CLEAR_LINUX_OR_MAC_SYSTEM_LOGS = 33
    INDICATOR_REMOVAL_CLEAR_COMMAND_HISTORY = 34
    INDICATOR_REMOVAL_FILE_DELETION = 35
    INDICATOR_REMOVAL_TIMESTOMP = 36
    INDICATOR_REMOVAL_CLEAR_MAILBOX_DATA = 37
    APPLICATION_LAYER_PROTOCOL = 38
    DNS = 39
    SOFTWARE_DEPLOYMENT_TOOLS = 40
    VALID_ACCOUNTS = 41
    DEFAULT_ACCOUNTS = 42
    LOCAL_ACCOUNTS = 43
    CLOUD_ACCOUNTS = 44
    FILE_AND_DIRECTORY_DISCOVERY = 45
    ACCOUNT_DISCOVERY_LOCAL_ACCOUNT = 46
    PROXY = 47
    EXTERNAL_PROXY = 48
    MULTI_HOP_PROXY = 49
    ACCOUNT_MANIPULATION = 50
    ADDITIONAL_CLOUD_CREDENTIALS = 51
    ADDITIONAL_CLOUD_ROLES = 52
    SSH_AUTHORIZED_KEYS = 53
    ADDITIONAL_CONTAINER_CLUSTER_ROLES = 54
    MULTI_STAGE_CHANNELS = 55
    INGRESS_TOOL_TRANSFER = 56
    NATIVE_API = 57
    BRUTE_FORCE = 58
    AUTOMATED_COLLECTION = 59
    SHARED_MODULES = 60
    DATA_ENCODING = 61
    STANDARD_ENCODING = 62
    ACCESS_TOKEN_MANIPULATION = 63
    TOKEN_IMPERSONATION_OR_THEFT = 64
    CREATE_ACCOUNT = 65
    LOCAL_ACCOUNT = 66
    DEOBFUSCATE_DECODE_FILES_OR_INFO = 67
    EXPLOIT_PUBLIC_FACING_APPLICATION = 68
    SUPPLY_CHAIN_COMPROMISE = 69
    COMPROMISE_SOFTWARE_DEPENDENCIES_AND_DEVELOPMENT_TOOLS = 70
    USER_EXECUTION = 71
    DOMAIN_POLICY_MODIFICATION = 72
    DATA_DESTRUCTION = 73
    DATA_ENCRYPTED_FOR_IMPACT = 74
    SERVICE_STOP = 75
    INHIBIT_SYSTEM_RECOVERY = 76
    FIRMWARE_CORRUPTION = 77
    RESOURCE_HIJACKING = 78
    NETWORK_DENIAL_OF_SERVICE = 79
    CLOUD_SERVICE_DISCOVERY = 80
    STEAL_APPLICATION_ACCESS_TOKEN = 81
    ACCOUNT_ACCESS_REMOVAL = 82
    TRANSFER_DATA_TO_CLOUD_ACCOUNT = 83
    STEAL_WEB_SESSION_COOKIE = 84
    CREATE_OR_MODIFY_SYSTEM_PROCESS = 85
    EVENT_TRIGGERED_EXECUTION = 86
    BOOT_OR_LOGON_AUTOSTART_EXECUTION = 87
    KERNEL_MODULES_AND_EXTENSIONS = 88
    SHORTCUT_MODIFICATION = 89
    ABUSE_ELEVATION_CONTROL_MECHANISM = 90
    ABUSE_ELEVATION_CONTROL_MECHANISM_SUDO_AND_SUDO_CACHING = 91
    UNSECURED_CREDENTIALS = 92
    CREDENTIALS_IN_FILES = 93
    BASH_HISTORY = 94
    PRIVATE_KEYS = 95
    SUBVERT_TRUST_CONTROL = 96
    INSTALL_ROOT_CERTIFICATE = 97
    COMPROMISE_HOST_SOFTWARE_BINARY = 98
    CREDENTIALS_FROM_PASSWORD_STORES = 99
    MODIFY_AUTHENTICATION_PROCESS = 100
    PLUGGABLE_AUTHENTICATION_MODULES = 101
    IMPAIR_DEFENSES = 102
    DISABLE_OR_MODIFY_TOOLS = 103
    INDICATOR_BLOCKING = 104
    DISABLE_OR_MODIFY_LINUX_AUDIT_SYSTEM = 105
    HIDE_ARTIFACTS = 106
    HIDDEN_FILES_AND_DIRECTORIES = 107
    HIDDEN_USERS = 108
    EXFILTRATION_OVER_WEB_SERVICE = 109
    EXFILTRATION_TO_CLOUD_STORAGE = 110
    DYNAMIC_RESOLUTION = 111
    LATERAL_TOOL_TRANSFER = 112
    HIJACK_EXECUTION_FLOW = 113
    HIJACK_EXECUTION_FLOW_DYNAMIC_LINKER_HIJACKING = 114
    MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE = 115
    CREATE_SNAPSHOT = 116
    CLOUD_INFRASTRUCTURE_DISCOVERY = 117
    DEVELOP_CAPABILITIES = 118
    DEVELOP_CAPABILITIES_MALWARE = 119
    OBTAIN_CAPABILITIES = 120
    OBTAIN_CAPABILITIES_MALWARE = 121
    OBTAIN_CAPABILITIES_VULNERABILITIES = 122
    ACTIVE_SCANNING = 123
    SCANNING_IP_BLOCKS = 124
    STAGE_CAPABILITIES = 125
    UPLOAD_MALWARE = 126
    CONTAINER_ADMINISTRATION_COMMAND = 127
    DEPLOY_CONTAINER = 128
    ESCAPE_TO_HOST = 129
    CONTAINER_AND_RESOURCE_DISCOVERY = 130
    REFLECTIVE_CODE_LOADING = 131
    STEAL_OR_FORGE_AUTHENTICATION_CERTIFICATES = 132
    FINANCIAL_THEFT = 133

  additionalTactics = _messages.EnumField('AdditionalTacticsValueListEntryValuesEnum', 1, repeated=True)
  additionalTechniques = _messages.EnumField('AdditionalTechniquesValueListEntryValuesEnum', 2, repeated=True)
  primaryTactic = _messages.EnumField('PrimaryTacticValueValuesEnum', 3)
  primaryTechniques = _messages.EnumField('PrimaryTechniquesValueListEntryValuesEnum', 4, repeated=True)
  version = _messages.StringField(5)


class MuteInfo(_messages.Message):
  r"""Mute information about the finding, including whether the finding has a
  static mute or any matching dynamic mute rules.

  Fields:
    dynamicMuteRecords: The list of dynamic mute rules that currently match
      the finding.
    staticMute: If set, the static mute applied to this finding. Static mutes
      override dynamic mutes. If unset, there is no static mute.
  """

  dynamicMuteRecords = _messages.MessageField('DynamicMuteRecord', 1, repeated=True)
  staticMute = _messages.MessageField('StaticMute', 2)


class Network(_messages.Message):
  r"""Contains information about a VPC network associated with the finding.

  Fields:
    name: The name of the VPC network resource, for example,
      `//compute.googleapis.com/projects/my-project/global/networks/my-
      network`.
  """

  name = _messages.StringField(1)


class Node(_messages.Message):
  r"""Kubernetes nodes associated with the finding.

  Fields:
    name: [Full resource name](https://google.aip.dev/122#full-resource-names)
      of the Compute Engine VM running the cluster node.
  """

  name = _messages.StringField(1)


class NodePool(_messages.Message):
  r"""Provides GKE node pool information.

  Fields:
    name: Kubernetes node pool name.
    nodes: Nodes associated with the finding.
  """

  name = _messages.StringField(1)
  nodes = _messages.MessageField('Node', 2, repeated=True)


class Notebook(_messages.Message):
  r"""Represents a Jupyter notebook IPYNB file, such as a [Colab Enterprise
  notebook](https://cloud.google.com/colab/docs/introduction) file, that is
  associated with a finding.

  Fields:
    lastAuthor: The user ID of the latest author to modify the notebook.
    name: The name of the notebook.
    notebookUpdateTime: The most recent time the notebook was updated.
    service: The source notebook service, for example, "Colab Enterprise".
  """

  lastAuthor = _messages.StringField(1)
  name = _messages.StringField(2)
  notebookUpdateTime = _messages.StringField(3)
  service = _messages.StringField(4)


class Object(_messages.Message):
  r"""Kubernetes object related to the finding, uniquely identified by GKNN.
  Used if the object Kind is not one of Pod, Node, NodePool, Binding, or
  AccessReview.

  Fields:
    containers: Pod containers associated with this finding, if any.
    group: Kubernetes object group, such as "policy.k8s.io/v1".
    kind: Kubernetes object kind, such as "Namespace".
    name: Kubernetes object name. For details see
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/names/.
    ns: Kubernetes object namespace. Must be a valid DNS label. Named "ns" to
      avoid collision with C++ namespace keyword. For details see
      https://kubernetes.io/docs/tasks/administer-cluster/namespaces/.
  """

  containers = _messages.MessageField('Container', 1, repeated=True)
  group = _messages.StringField(2)
  kind = _messages.StringField(3)
  name = _messages.StringField(4)
  ns = _messages.StringField(5)


class OrgPolicy(_messages.Message):
  r"""Contains information about the org policies associated with the finding.

  Fields:
    name: The resource name of the org policy. Example:
      "organizations/{organization_id}/policies/{constraint_name}"
  """

  name = _messages.StringField(1)


class Package(_messages.Message):
  r"""Package is a generic definition of a package.

  Fields:
    cpeUri: The CPE URI where the vulnerability was detected.
    packageName: The name of the package where the vulnerability was detected.
    packageType: Type of package, for example, os, maven, or go.
    packageVersion: The version of the package.
  """

  cpeUri = _messages.StringField(1)
  packageName = _messages.StringField(2)
  packageType = _messages.StringField(3)
  packageVersion = _messages.StringField(4)


class Pod(_messages.Message):
  r"""A Kubernetes Pod.

  Fields:
    containers: Pod containers associated with this finding, if any.
    labels: Pod labels. For Kubernetes containers, these are applied to the
      container.
    name: Kubernetes Pod name.
    ns: Kubernetes Pod namespace.
  """

  containers = _messages.MessageField('Container', 1, repeated=True)
  labels = _messages.MessageField('Label', 2, repeated=True)
  name = _messages.StringField(3)
  ns = _messages.StringField(4)


class PolicyDriftDetails(_messages.Message):
  r"""The policy field that violates the deployed posture and its expected and
  detected values.

  Fields:
    detectedValue: The detected value that violates the deployed posture, for
      example, `false` or `allowed_values={"projects/22831892"}`.
    expectedValue: The value of this field that was configured in a posture,
      for example, `true` or `allowed_values={"projects/29831892"}`.
    field: The name of the updated field, for example
      constraint.implementation.policy_rules[0].enforce
  """

  detectedValue = _messages.StringField(1)
  expectedValue = _messages.StringField(2)
  field = _messages.StringField(3)


class PortRange(_messages.Message):
  r"""A port range which is inclusive of the min and max values. Values are
  between 0 and 2^16-1. The max can be equal / must be not smaller than the
  min value. If min and max are equal this indicates that it is a single port.

  Fields:
    max: Maximum port value.
    min: Minimum port value.
  """

  max = _messages.IntegerField(1)
  min = _messages.IntegerField(2)


class Process(_messages.Message):
  r"""Represents an operating system process.

  Fields:
    args: Process arguments as JSON encoded strings.
    argumentsTruncated: True if `args` is incomplete.
    binary: File information for the process executable.
    envVariables: Process environment variables.
    envVariablesTruncated: True if `env_variables` is incomplete.
    libraries: File information for libraries loaded by the process.
    name: The process name, as displayed in utilities like `top` and `ps`.
      This name can be accessed through `/proc/[pid]/comm` and changed with
      `prctl(PR_SET_NAME)`.
    parentPid: The parent process ID.
    pid: The process ID.
    script: When the process represents the invocation of a script, `binary`
      provides information about the interpreter, while `script` provides
      information about the script file provided to the interpreter.
  """

  args = _messages.StringField(1, repeated=True)
  argumentsTruncated = _messages.BooleanField(2)
  binary = _messages.MessageField('File', 3)
  envVariables = _messages.MessageField('EnvironmentVariable', 4, repeated=True)
  envVariablesTruncated = _messages.BooleanField(5)
  libraries = _messages.MessageField('File', 6, repeated=True)
  name = _messages.StringField(7)
  parentPid = _messages.IntegerField(8)
  pid = _messages.IntegerField(9)
  script = _messages.MessageField('File', 10)


class ProcessSignature(_messages.Message):
  r"""Indicates what signature matched this process.

  Enums:
    SignatureTypeValueValuesEnum: Describes the type of resource associated
      with the signature.

  Fields:
    memoryHashSignature: Signature indicating that a binary family was
      matched.
    signatureType: Describes the type of resource associated with the
      signature.
    yaraRuleSignature: Signature indicating that a YARA rule was matched.
  """

  class SignatureTypeValueValuesEnum(_messages.Enum):
    r"""Describes the type of resource associated with the signature.

    Values:
      SIGNATURE_TYPE_UNSPECIFIED: The default signature type.
      SIGNATURE_TYPE_PROCESS: Used for signatures concerning processes.
      SIGNATURE_TYPE_FILE: Used for signatures concerning disks.
    """
    SIGNATURE_TYPE_UNSPECIFIED = 0
    SIGNATURE_TYPE_PROCESS = 1
    SIGNATURE_TYPE_FILE = 2

  memoryHashSignature = _messages.MessageField('MemoryHashSignature', 1)
  signatureType = _messages.EnumField('SignatureTypeValueValuesEnum', 2)
  yaraRuleSignature = _messages.MessageField('YaraRuleSignature', 3)


class RapidVulnerabilityDetectionSettings(_messages.Message):
  r"""Resource capturing the settings for the Rapid Vulnerability Detection
  service.

  Enums:
    ServiceEnablementStateValueValuesEnum: The state of enablement for the
      service at its level of the resource hierarchy. A DISABLED state will
      override all module enablement_states to DISABLED.

  Messages:
    ModulesValue: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's.

  Fields:
    modules: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's.
    name: The resource name of the RapidVulnerabilityDetectionSettings.
      Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
    serviceEnablementState: The state of enablement for the service at its
      level of the resource hierarchy. A DISABLED state will override all
      module enablement_states to DISABLED.
    updateTime: Output only. The time the settings were last updated.
  """

  class ServiceEnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the service at its level of the resource
    hierarchy. A DISABLED state will override all module enablement_states to
    DISABLED.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModulesValue(_messages.Message):
    r"""The configurations including the state of enablement for the service's
    different modules. The absence of a module in the map implies its
    configuration is inherited from its parent's.

    Messages:
      AdditionalProperty: An additional property for a ModulesValue object.

    Fields:
      additionalProperties: Additional properties of type ModulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModulesValue object.

      Fields:
        key: Name of the additional property.
        value: A Config attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Config', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  modules = _messages.MessageField('ModulesValue', 1)
  name = _messages.StringField(2)
  serviceEnablementState = _messages.EnumField('ServiceEnablementStateValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class Reference(_messages.Message):
  r"""Additional Links

  Fields:
    source: Source of the reference e.g. NVD
    uri: Uri for the mentioned source e.g. https://cve.mitre.org/cgi-
      bin/cvename.cgi?name=CVE-2021-34527.
  """

  source = _messages.StringField(1)
  uri = _messages.StringField(2)


class Requests(_messages.Message):
  r"""Information about the requests relevant to the finding.

  Fields:
    longTermAllowed: Allowed RPS (requests per second) over the long term.
    longTermDenied: Denied RPS (requests per second) over the long term.
    ratio: For 'Increasing deny ratio', the ratio is the denied traffic
      divided by the allowed traffic. For 'Allowed traffic spike', the ratio
      is the allowed traffic in the short term divided by allowed traffic in
      the long term.
    shortTermAllowed: Allowed RPS (requests per second) in the short term.
  """

  longTermAllowed = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  longTermDenied = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  ratio = _messages.FloatField(3)
  shortTermAllowed = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ResourcePath(_messages.Message):
  r"""Represents the path of resources leading up to the resource this finding
  is about.

  Fields:
    nodes: The list of nodes that make the up resource path, ordered from
      lowest level to highest level.
  """

  nodes = _messages.MessageField('ResourcePathNode', 1, repeated=True)


class ResourcePathNode(_messages.Message):
  r"""A node within the resource path. Each node represents a resource within
  the resource hierarchy.

  Enums:
    NodeTypeValueValuesEnum: The type of resource this node represents.

  Fields:
    displayName: The display name of the resource this node represents.
    id: The ID of the resource this node represents.
    nodeType: The type of resource this node represents.
  """

  class NodeTypeValueValuesEnum(_messages.Enum):
    r"""The type of resource this node represents.

    Values:
      RESOURCE_PATH_NODE_TYPE_UNSPECIFIED: Node type is unspecified.
      GCP_ORGANIZATION: The node represents a Google Cloud organization.
      GCP_FOLDER: The node represents a Google Cloud folder.
      GCP_PROJECT: The node represents a Google Cloud project.
      AWS_ORGANIZATION: The node represents an AWS organization.
      AWS_ORGANIZATIONAL_UNIT: The node represents an AWS organizational unit.
      AWS_ACCOUNT: The node represents an AWS account.
      AZURE_MANAGEMENT_GROUP: The node represents an Azure management group.
      AZURE_SUBSCRIPTION: The node represents an Azure subscription.
      AZURE_RESOURCE_GROUP: The node represents an Azure resource group.
    """
    RESOURCE_PATH_NODE_TYPE_UNSPECIFIED = 0
    GCP_ORGANIZATION = 1
    GCP_FOLDER = 2
    GCP_PROJECT = 3
    AWS_ORGANIZATION = 4
    AWS_ORGANIZATIONAL_UNIT = 5
    AWS_ACCOUNT = 6
    AZURE_MANAGEMENT_GROUP = 7
    AZURE_SUBSCRIPTION = 8
    AZURE_RESOURCE_GROUP = 9

  displayName = _messages.StringField(1)
  id = _messages.StringField(2)
  nodeType = _messages.EnumField('NodeTypeValueValuesEnum', 3)


class Role(_messages.Message):
  r"""Kubernetes Role or ClusterRole.

  Enums:
    KindValueValuesEnum: Role type.

  Fields:
    kind: Role type.
    name: Role name.
    ns: Role namespace.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Role type.

    Values:
      KIND_UNSPECIFIED: Role type is not specified.
      ROLE: Kubernetes Role.
      CLUSTER_ROLE: Kubernetes ClusterRole.
    """
    KIND_UNSPECIFIED = 0
    ROLE = 1
    CLUSTER_ROLE = 2

  kind = _messages.EnumField('KindValueValuesEnum', 1)
  name = _messages.StringField(2)
  ns = _messages.StringField(3)


class SecurityBulletin(_messages.Message):
  r"""SecurityBulletin are notifications of vulnerabilities of Google
  products.

  Fields:
    bulletinId: ID of the bulletin corresponding to the vulnerability.
    submissionTime: Submission time of this Security Bulletin.
    suggestedUpgradeVersion: This represents a version that the cluster
      receiving this notification should be upgraded to, based on its current
      version. For example, 1.15.0
  """

  bulletinId = _messages.StringField(1)
  submissionTime = _messages.StringField(2)
  suggestedUpgradeVersion = _messages.StringField(3)


class SecurityCenterSettings(_messages.Message):
  r"""Resource capturing the settings for Security Center.

  Fields:
    logSinkProject: The resource name of the project to send logs to. This
      project must be part of the organization this resource resides in. The
      format is `projects/{project_id}`. An empty value disables logging. This
      value is only referenced by services that support log sink. Please refer
      to the documentation for an updated list of compatible services. This
      may only be specified for organization level onboarding.
    name: The resource name of the SecurityCenterSettings. Format:
      organizations/{organization}/securityCenterSettings Format:
      folders/{folder}/securityCenterSettings Format:
      projects/{project}/securityCenterSettings
    onboardingTime: Output only. Timestamp of when the customer organization
      was onboarded to SCC.
    orgServiceAccount: Output only. The organization level service account to
      be used for security center components.
  """

  logSinkProject = _messages.StringField(1)
  name = _messages.StringField(2)
  onboardingTime = _messages.StringField(3)
  orgServiceAccount = _messages.StringField(4)


class SecurityHealthAnalyticsSettings(_messages.Message):
  r"""Resource capturing the settings for the Security Health Analytics
  service.

  Enums:
    ServiceEnablementStateValueValuesEnum: The state of enablement for the
      service at its level of the resource hierarchy. A DISABLED state will
      override all module enablement_states to DISABLED.

  Messages:
    ModulesValue: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's configuration.

  Fields:
    modules: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's configuration.
    name: Identifier. The resource name of the
      SecurityHealthAnalyticsSettings. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
    serviceAccount: Output only. The service account used by Security Health
      Analytics detectors.
    serviceEnablementState: The state of enablement for the service at its
      level of the resource hierarchy. A DISABLED state will override all
      module enablement_states to DISABLED.
    updateTime: Output only. The time the settings were last updated.
  """

  class ServiceEnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the service at its level of the resource
    hierarchy. A DISABLED state will override all module enablement_states to
    DISABLED.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModulesValue(_messages.Message):
    r"""The configurations including the state of enablement for the service's
    different modules. The absence of a module in the map implies its
    configuration is inherited from its parent's configuration.

    Messages:
      AdditionalProperty: An additional property for a ModulesValue object.

    Fields:
      additionalProperties: Additional properties of type ModulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModulesValue object.

      Fields:
        key: Name of the additional property.
        value: A Config attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Config', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  modules = _messages.MessageField('ModulesValue', 1)
  name = _messages.StringField(2)
  serviceAccount = _messages.StringField(3)
  serviceEnablementState = _messages.EnumField('ServiceEnablementStateValueValuesEnum', 4)
  updateTime = _messages.StringField(5)


class SecurityMarks(_messages.Message):
  r"""User specified security marks that are attached to the parent Security
  Command Center resource. Security marks are scoped within a Security Command
  Center organization -- they can be modified and viewed by all users who have
  proper permissions on the organization.

  Messages:
    MarksValue: Mutable user specified security marks belonging to the parent
      resource. Constraints are as follows: * Keys and values are treated as
      case insensitive * Keys must be between 1 - 256 characters (inclusive) *
      Keys must be letters, numbers, underscores, or dashes * Values have
      leading and trailing whitespace trimmed, remaining characters must be
      between 1 - 4096 characters (inclusive)

  Fields:
    canonicalName: The canonical name of the marks. Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks"
      "folders/{folder_id}/assets/{asset_id}/securityMarks"
      "projects/{project_number}/assets/{asset_id}/securityMarks" "organizatio
      ns/{organization_id}/sources/{source_id}/findings/{finding_id}/securityM
      arks" "folders/{folder_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks" "projects/{project_number}/sources/{source_id}/findings/{fin
      ding_id}/securityMarks"
    marks: Mutable user specified security marks belonging to the parent
      resource. Constraints are as follows: * Keys and values are treated as
      case insensitive * Keys must be between 1 - 256 characters (inclusive) *
      Keys must be letters, numbers, underscores, or dashes * Values have
      leading and trailing whitespace trimmed, remaining characters must be
      between 1 - 4096 characters (inclusive)
    name: The relative resource name of the SecurityMarks. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks" "organ
      izations/{organization_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks".
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MarksValue(_messages.Message):
    r"""Mutable user specified security marks belonging to the parent
    resource. Constraints are as follows: * Keys and values are treated as
    case insensitive * Keys must be between 1 - 256 characters (inclusive) *
    Keys must be letters, numbers, underscores, or dashes * Values have
    leading and trailing whitespace trimmed, remaining characters must be
    between 1 - 4096 characters (inclusive)

    Messages:
      AdditionalProperty: An additional property for a MarksValue object.

    Fields:
      additionalProperties: Additional properties of type MarksValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MarksValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  canonicalName = _messages.StringField(1)
  marks = _messages.MessageField('MarksValue', 2)
  name = _messages.StringField(3)


class SecurityPolicy(_messages.Message):
  r"""Information about the [Google Cloud Armor security
  policy](https://cloud.google.com/armor/docs/security-policy-overview)
  relevant to the finding.

  Fields:
    name: The name of the Google Cloud Armor security policy, for example,
      "my-security-policy".
    preview: Whether or not the associated rule or policy is in preview mode.
    type: The type of Google Cloud Armor security policy for example, 'backend
      security policy', 'edge security policy', 'network edge security
      policy', or 'always-on DDoS protection'.
  """

  name = _messages.StringField(1)
  preview = _messages.BooleanField(2)
  type = _messages.StringField(3)


class SecurityPosture(_messages.Message):
  r"""Represents a posture that is deployed on Google Cloud by the Security
  Command Center Posture Management service. A posture contains one or more
  policy sets. A policy set is a group of policies that enforce a set of
  security rules on Google Cloud.

  Fields:
    changedPolicy: The name of the updated policy, for example,
      `projects/{project_id}/policies/{constraint_name}`.
    name: Name of the posture, for example, `CIS-Posture`.
    policy: The ID of the updated policy, for example, `compute-policy-1`.
    policyDriftDetails: The details about a change in an updated policy that
      violates the deployed posture.
    policySet: The name of the updated policyset, for example, `cis-
      policyset`.
    postureDeployment: The name of the posture deployment, for example,
      `organizations/{org_id}/posturedeployments/{posture_deployment_id}`.
    postureDeploymentResource: The project, folder, or organization on which
      the posture is deployed, for example, `projects/{project_number}`.
    revisionId: The version of the posture, for example, `c7cfa2a8`.
  """

  changedPolicy = _messages.StringField(1)
  name = _messages.StringField(2)
  policy = _messages.StringField(3)
  policyDriftDetails = _messages.MessageField('PolicyDriftDetails', 4, repeated=True)
  policySet = _messages.StringField(5)
  postureDeployment = _messages.StringField(6)
  postureDeploymentResource = _messages.StringField(7)
  revisionId = _messages.StringField(8)


class SecuritycenterFoldersContainerThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterFoldersContainerThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterFoldersEventThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterFoldersEventThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the EventThreatDetectionSettings to calculate.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterFoldersGetContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetContainerThreatDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersGetEventThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetEventThreatDetectionSettingsRequest object.

  Fields:
    name: Required. The name of the EventThreatDetectionSettings to retrieve.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersGetRapidVulnerabilityDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetRapidVulnerabilityDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the RapidVulnerabilityDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersGetSecurityCenterSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetSecurityCenterSettingsRequest object.

  Fields:
    name: Required. The name of the SecurityCenterSettings to retrieve.
      Format: organizations/{organization}/securityCenterSettings Format:
      folders/{folder}/securityCenterSettings Format:
      projects/{project}/securityCenterSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersGetSecurityHealthAnalyticsSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetSecurityHealthAnalyticsSettingsRequest object.

  Fields:
    name: Required. The name of the SecurityHealthAnalyticsSettings to
      retrieve. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersGetVirtualMachineThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetVirtualMachineThreatDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the VirtualMachineThreatDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersGetWebSecurityScannerSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetWebSecurityScannerSettingsRequest object.

  Fields:
    name: Required. The name of the WebSecurityScannerSettings to retrieve.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersRapidVulnerabilityDetectionSettingsCalculateRequest(_messages.Message):
  r"""A
  SecuritycenterFoldersRapidVulnerabilityDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the RapidVulnerabilityDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersSecurityHealthAnalyticsSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterFoldersSecurityHealthAnalyticsSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the SecurityHealthAnalyticsSettings to
      calculate. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterFoldersUpdateContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersUpdateContainerThreatDetectionSettingsRequest
  object.

  Fields:
    containerThreatDetectionSettings: A ContainerThreatDetectionSettings
      resource to be passed as the request body.
    name: Identifier. The resource name of the
      ContainerThreatDetectionSettings. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
    updateMask: The list of fields to be updated.
  """

  containerThreatDetectionSettings = _messages.MessageField('ContainerThreatDetectionSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterFoldersUpdateEventThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersUpdateEventThreatDetectionSettingsRequest object.

  Fields:
    eventThreatDetectionSettings: A EventThreatDetectionSettings resource to
      be passed as the request body.
    name: Identifier. The resource name of the EventThreatDetectionSettings.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
    updateMask: The list of fields to be updated.
  """

  eventThreatDetectionSettings = _messages.MessageField('EventThreatDetectionSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterFoldersUpdateRapidVulnerabilityDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersUpdateRapidVulnerabilityDetectionSettingsRequest
  object.

  Fields:
    name: The resource name of the RapidVulnerabilityDetectionSettings.
      Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
    rapidVulnerabilityDetectionSettings: A RapidVulnerabilityDetectionSettings
      resource to be passed as the request body.
    updateMask: The list of fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  rapidVulnerabilityDetectionSettings = _messages.MessageField('RapidVulnerabilityDetectionSettings', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterFoldersUpdateSecurityHealthAnalyticsSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersUpdateSecurityHealthAnalyticsSettingsRequest
  object.

  Fields:
    name: Identifier. The resource name of the
      SecurityHealthAnalyticsSettings. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
    securityHealthAnalyticsSettings: A SecurityHealthAnalyticsSettings
      resource to be passed as the request body.
    updateMask: The list of fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  securityHealthAnalyticsSettings = _messages.MessageField('SecurityHealthAnalyticsSettings', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterFoldersUpdateVirtualMachineThreatDetectionSettingsRequest(_messages.Message):
  r"""A
  SecuritycenterFoldersUpdateVirtualMachineThreatDetectionSettingsRequest
  object.

  Fields:
    name: Identifier. The resource name of the
      VirtualMachineThreatDetectionSettings. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
    updateMask: The list of fields to be updated.
    virtualMachineThreatDetectionSettings: A
      VirtualMachineThreatDetectionSettings resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  virtualMachineThreatDetectionSettings = _messages.MessageField('VirtualMachineThreatDetectionSettings', 3)


class SecuritycenterFoldersUpdateWebSecurityScannerSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersUpdateWebSecurityScannerSettingsRequest object.

  Fields:
    name: Identifier. The resource name of the WebSecurityScannerSettings.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
    updateMask: The list of fields to be updated.
    webSecurityScannerSettings: A WebSecurityScannerSettings resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  webSecurityScannerSettings = _messages.MessageField('WebSecurityScannerSettings', 3)


class SecuritycenterFoldersVirtualMachineThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A
  SecuritycenterFoldersVirtualMachineThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the VirtualMachineThreatDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterFoldersWebSecurityScannerSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterFoldersWebSecurityScannerSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the WebSecurityScannerSettings to calculate.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterOrganizationsContainerThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsContainerThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterOrganizationsEventThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsEventThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the EventThreatDetectionSettings to calculate.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterOrganizationsGetContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsGetContainerThreatDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetEventThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsGetEventThreatDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the EventThreatDetectionSettings to retrieve.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetRapidVulnerabilityDetectionSettingsRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsGetRapidVulnerabilityDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the RapidVulnerabilityDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetSecurityCenterSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsGetSecurityCenterSettingsRequest object.

  Fields:
    name: Required. The name of the SecurityCenterSettings to retrieve.
      Format: organizations/{organization}/securityCenterSettings Format:
      folders/{folder}/securityCenterSettings Format:
      projects/{project}/securityCenterSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetSecurityHealthAnalyticsSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsGetSecurityHealthAnalyticsSettingsRequest
  object.

  Fields:
    name: Required. The name of the SecurityHealthAnalyticsSettings to
      retrieve. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetSubscriptionRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsGetSubscriptionRequest object.

  Fields:
    name: Required. The name of the subscription to retrieve. Format:
      organizations/{organization}/subscription
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetVirtualMachineThreatDetectionSettingsRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsGetVirtualMachineThreatDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the VirtualMachineThreatDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetWebSecurityScannerSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsGetWebSecurityScannerSettingsRequest
  object.

  Fields:
    name: Required. The name of the WebSecurityScannerSettings to retrieve.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsRapidVulnerabilityDetectionSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsRapidVulnerabilityDetectionSettingsCalculat
  eRequest object.

  Fields:
    name: Required. The name of the RapidVulnerabilityDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCalculateRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the SecurityHealthAnalyticsSettings to
      calculate. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterOrganizationsUpdateContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsUpdateContainerThreatDetectionSettingsRequest
  object.

  Fields:
    containerThreatDetectionSettings: A ContainerThreatDetectionSettings
      resource to be passed as the request body.
    name: Identifier. The resource name of the
      ContainerThreatDetectionSettings. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
    updateMask: The list of fields to be updated.
  """

  containerThreatDetectionSettings = _messages.MessageField('ContainerThreatDetectionSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsUpdateEventThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsUpdateEventThreatDetectionSettingsRequest
  object.

  Fields:
    eventThreatDetectionSettings: A EventThreatDetectionSettings resource to
      be passed as the request body.
    name: Identifier. The resource name of the EventThreatDetectionSettings.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
    updateMask: The list of fields to be updated.
  """

  eventThreatDetectionSettings = _messages.MessageField('EventThreatDetectionSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsUpdateRapidVulnerabilityDetectionSettingsRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsUpdateRapidVulnerabilityDetectionSettingsRequest
  object.

  Fields:
    name: The resource name of the RapidVulnerabilityDetectionSettings.
      Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
    rapidVulnerabilityDetectionSettings: A RapidVulnerabilityDetectionSettings
      resource to be passed as the request body.
    updateMask: The list of fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  rapidVulnerabilityDetectionSettings = _messages.MessageField('RapidVulnerabilityDetectionSettings', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsUpdateSecurityHealthAnalyticsSettingsRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsUpdateSecurityHealthAnalyticsSettingsRequest
  object.

  Fields:
    name: Identifier. The resource name of the
      SecurityHealthAnalyticsSettings. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
    securityHealthAnalyticsSettings: A SecurityHealthAnalyticsSettings
      resource to be passed as the request body.
    updateMask: The list of fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  securityHealthAnalyticsSettings = _messages.MessageField('SecurityHealthAnalyticsSettings', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsUpdateVirtualMachineThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsUpdateVirtualMachineThreatDetectionSettings
  Request object.

  Fields:
    name: Identifier. The resource name of the
      VirtualMachineThreatDetectionSettings. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
    updateMask: The list of fields to be updated.
    virtualMachineThreatDetectionSettings: A
      VirtualMachineThreatDetectionSettings resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  virtualMachineThreatDetectionSettings = _messages.MessageField('VirtualMachineThreatDetectionSettings', 3)


class SecuritycenterOrganizationsUpdateWebSecurityScannerSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsUpdateWebSecurityScannerSettingsRequest
  object.

  Fields:
    name: Identifier. The resource name of the WebSecurityScannerSettings.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
    updateMask: The list of fields to be updated.
    webSecurityScannerSettings: A WebSecurityScannerSettings resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  webSecurityScannerSettings = _messages.MessageField('WebSecurityScannerSettings', 3)


class SecuritycenterOrganizationsVirtualMachineThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsVirtualMachineThreatDetectionSettingsCalcul
  ateRequest object.

  Fields:
    name: Required. The name of the VirtualMachineThreatDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterOrganizationsWebSecurityScannerSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsWebSecurityScannerSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the WebSecurityScannerSettings to calculate.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterProjectsContainerThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterProjectsContainerThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterProjectsEventThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterProjectsEventThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the EventThreatDetectionSettings to calculate.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterProjectsGetContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetContainerThreatDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsGetEventThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetEventThreatDetectionSettingsRequest object.

  Fields:
    name: Required. The name of the EventThreatDetectionSettings to retrieve.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsGetRapidVulnerabilityDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetRapidVulnerabilityDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the RapidVulnerabilityDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsGetSecurityCenterSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetSecurityCenterSettingsRequest object.

  Fields:
    name: Required. The name of the SecurityCenterSettings to retrieve.
      Format: organizations/{organization}/securityCenterSettings Format:
      folders/{folder}/securityCenterSettings Format:
      projects/{project}/securityCenterSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsGetSecurityHealthAnalyticsSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetSecurityHealthAnalyticsSettingsRequest
  object.

  Fields:
    name: Required. The name of the SecurityHealthAnalyticsSettings to
      retrieve. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsGetVirtualMachineThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetVirtualMachineThreatDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the VirtualMachineThreatDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsGetWebSecurityScannerSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetWebSecurityScannerSettingsRequest object.

  Fields:
    name: Required. The name of the WebSecurityScannerSettings to retrieve.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsLocationsClustersContainerThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterProjectsLocationsClustersContainerThreatDetectionSetting
  sCalculateRequest object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterProjectsLocationsClustersGetContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsLocationsClustersGetContainerThreatDetectionSett
  ingsRequest object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsLocationsClustersUpdateContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsLocationsClustersUpdateContainerThreatDetectionS
  ettingsRequest object.

  Fields:
    containerThreatDetectionSettings: A ContainerThreatDetectionSettings
      resource to be passed as the request body.
    name: Identifier. The resource name of the
      ContainerThreatDetectionSettings. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
    updateMask: The list of fields to be updated.
  """

  containerThreatDetectionSettings = _messages.MessageField('ContainerThreatDetectionSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsRapidVulnerabilityDetectionSettingsCalculateRequest(_messages.Message):
  r"""A
  SecuritycenterProjectsRapidVulnerabilityDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the RapidVulnerabilityDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsSecurityHealthAnalyticsSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterProjectsSecurityHealthAnalyticsSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the SecurityHealthAnalyticsSettings to
      calculate. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterProjectsUpdateContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsUpdateContainerThreatDetectionSettingsRequest
  object.

  Fields:
    containerThreatDetectionSettings: A ContainerThreatDetectionSettings
      resource to be passed as the request body.
    name: Identifier. The resource name of the
      ContainerThreatDetectionSettings. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
    updateMask: The list of fields to be updated.
  """

  containerThreatDetectionSettings = _messages.MessageField('ContainerThreatDetectionSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsUpdateEventThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsUpdateEventThreatDetectionSettingsRequest
  object.

  Fields:
    eventThreatDetectionSettings: A EventThreatDetectionSettings resource to
      be passed as the request body.
    name: Identifier. The resource name of the EventThreatDetectionSettings.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
    updateMask: The list of fields to be updated.
  """

  eventThreatDetectionSettings = _messages.MessageField('EventThreatDetectionSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsUpdateRapidVulnerabilityDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsUpdateRapidVulnerabilityDetectionSettingsRequest
  object.

  Fields:
    name: The resource name of the RapidVulnerabilityDetectionSettings.
      Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
    rapidVulnerabilityDetectionSettings: A RapidVulnerabilityDetectionSettings
      resource to be passed as the request body.
    updateMask: The list of fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  rapidVulnerabilityDetectionSettings = _messages.MessageField('RapidVulnerabilityDetectionSettings', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsUpdateSecurityHealthAnalyticsSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsUpdateSecurityHealthAnalyticsSettingsRequest
  object.

  Fields:
    name: Identifier. The resource name of the
      SecurityHealthAnalyticsSettings. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
    securityHealthAnalyticsSettings: A SecurityHealthAnalyticsSettings
      resource to be passed as the request body.
    updateMask: The list of fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  securityHealthAnalyticsSettings = _messages.MessageField('SecurityHealthAnalyticsSettings', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsUpdateVirtualMachineThreatDetectionSettingsRequest(_messages.Message):
  r"""A
  SecuritycenterProjectsUpdateVirtualMachineThreatDetectionSettingsRequest
  object.

  Fields:
    name: Identifier. The resource name of the
      VirtualMachineThreatDetectionSettings. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
    updateMask: The list of fields to be updated.
    virtualMachineThreatDetectionSettings: A
      VirtualMachineThreatDetectionSettings resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  virtualMachineThreatDetectionSettings = _messages.MessageField('VirtualMachineThreatDetectionSettings', 3)


class SecuritycenterProjectsUpdateWebSecurityScannerSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsUpdateWebSecurityScannerSettingsRequest object.

  Fields:
    name: Identifier. The resource name of the WebSecurityScannerSettings.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
    updateMask: The list of fields to be updated.
    webSecurityScannerSettings: A WebSecurityScannerSettings resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  webSecurityScannerSettings = _messages.MessageField('WebSecurityScannerSettings', 3)


class SecuritycenterProjectsVirtualMachineThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A
  SecuritycenterProjectsVirtualMachineThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the VirtualMachineThreatDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycenterProjectsWebSecurityScannerSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterProjectsWebSecurityScannerSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the WebSecurityScannerSettings to calculate.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
    showEligibleModulesOnly: Optional. When set, will only retrieve the
      modules that are in scope. By default, all modules will be shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class ServiceAccountDelegationInfo(_messages.Message):
  r"""Identity delegation history of an authenticated service account.

  Fields:
    principalEmail: The email address of a Google account.
    principalSubject: A string representing the principal_subject associated
      with the identity. As compared to `principal_email`, supports principals
      that aren't associated with email addresses, such as third party
      principals. For most identities, the format will be
      `principal://iam.googleapis.com/{identity pool name}/subjects/{subject}`
      except for some GKE identities (GKE_WORKLOAD, FREEFORM,
      GKE_HUB_WORKLOAD) that are still in the legacy format
      `serviceAccount:{identity pool name}[{subject}]`
  """

  principalEmail = _messages.StringField(1)
  principalSubject = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StaticMute(_messages.Message):
  r"""Information about the static mute state. A static mute state overrides
  any dynamic mute rules that apply to this finding. The static mute state can
  be set by a static mute rule or by muting the finding directly.

  Enums:
    StateValueValuesEnum: The static mute state. If the value is `MUTED` or
      `UNMUTED`, then the finding's overall mute state will have the same
      value.

  Fields:
    applyTime: When the static mute was applied.
    state: The static mute state. If the value is `MUTED` or `UNMUTED`, then
      the finding's overall mute state will have the same value.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The static mute state. If the value is `MUTED` or `UNMUTED`, then the
    finding's overall mute state will have the same value.

    Values:
      MUTE_UNSPECIFIED: Unspecified.
      MUTED: Finding has been muted.
      UNMUTED: Finding has been unmuted.
      UNDEFINED: Finding has never been muted/unmuted.
    """
    MUTE_UNSPECIFIED = 0
    MUTED = 1
    UNMUTED = 2
    UNDEFINED = 3

  applyTime = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class Subject(_messages.Message):
  r"""Represents a Kubernetes subject.

  Enums:
    KindValueValuesEnum: Authentication type for the subject.

  Fields:
    kind: Authentication type for the subject.
    name: Name for the subject.
    ns: Namespace for the subject.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Authentication type for the subject.

    Values:
      AUTH_TYPE_UNSPECIFIED: Authentication is not specified.
      USER: User with valid certificate.
      SERVICEACCOUNT: Users managed by Kubernetes API with credentials stored
        as secrets.
      GROUP: Collection of users.
    """
    AUTH_TYPE_UNSPECIFIED = 0
    USER = 1
    SERVICEACCOUNT = 2
    GROUP = 3

  kind = _messages.EnumField('KindValueValuesEnum', 1)
  name = _messages.StringField(2)
  ns = _messages.StringField(3)


class Subscription(_messages.Message):
  r"""Resource capturing the state of an organization's subscription.

  Enums:
    TierValueValuesEnum: The tier of SCC features this organization currently
      has access to.

  Fields:
    details: The details of the most recent active subscription. If there has
      never been a subscription this will be empty.
    name: The resource name of the subscription. Format:
      organizations/{organization}/subscription
    tier: The tier of SCC features this organization currently has access to.
  """

  class TierValueValuesEnum(_messages.Enum):
    r"""The tier of SCC features this organization currently has access to.

    Values:
      TIER_UNSPECIFIED: Default value. This value is unused.
      STANDARD: The standard tier.
      PREMIUM: The premium tier.
      ENTERPRISE: The enterprise tier.
      ENTERPRISE_MC: The enterprise MC tier.
    """
    TIER_UNSPECIFIED = 0
    STANDARD = 1
    PREMIUM = 2
    ENTERPRISE = 3
    ENTERPRISE_MC = 4

  details = _messages.MessageField('Details', 1)
  name = _messages.StringField(2)
  tier = _messages.EnumField('TierValueValuesEnum', 3)


class TicketInfo(_messages.Message):
  r"""Information about the ticket, if any, that is being used to track the
  resolution of the issue that is identified by this finding.

  Fields:
    assignee: The assignee of the ticket in the ticket system.
    description: The description of the ticket in the ticket system.
    id: The identifier of the ticket in the ticket system.
    status: The latest status of the ticket, as reported by the ticket system.
    updateTime: The time when the ticket was last updated, as reported by the
      ticket system.
    uri: The link to the ticket in the ticket system.
  """

  assignee = _messages.StringField(1)
  description = _messages.StringField(2)
  id = _messages.StringField(3)
  status = _messages.StringField(4)
  updateTime = _messages.StringField(5)
  uri = _messages.StringField(6)


class ToxicCombination(_messages.Message):
  r"""Contains details about a group of security issues that, when the issues
  occur together, represent a greater risk than when the issues occur
  independently. A group of such issues is referred to as a toxic combination.

  Fields:
    attackExposureScore: The [Attack exposure
      score](https://cloud.google.com/security-command-center/docs/attack-
      exposure-learn#attack_exposure_scores) of this toxic combination. The
      score is a measure of how much this toxic combination exposes one or
      more high-value resources to potential attack.
    relatedFindings: List of resource names of findings associated with this
      toxic combination. For example,
      `organizations/123/sources/456/findings/789`.
  """

  attackExposureScore = _messages.FloatField(1)
  relatedFindings = _messages.StringField(2, repeated=True)


class VirtualMachineThreatDetectionSettings(_messages.Message):
  r"""Resource capturing the settings for the Virtual Machine Threat Detection
  service.

  Enums:
    ServiceEnablementStateValueValuesEnum: The state of enablement for the
      service at its level of the resource hierarchy. A DISABLED state will
      override all module enablement_states to DISABLED.

  Messages:
    ModulesValue: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's configuration.

  Fields:
    modules: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's configuration.
    name: Identifier. The resource name of the
      VirtualMachineThreatDetectionSettings. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
    serviceAccount: Output only. The service account used by Virtual Machine
      Threat Detection detectors.
    serviceEnablementState: The state of enablement for the service at its
      level of the resource hierarchy. A DISABLED state will override all
      module enablement_states to DISABLED.
    updateTime: Output only. The time the settings were last updated.
  """

  class ServiceEnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the service at its level of the resource
    hierarchy. A DISABLED state will override all module enablement_states to
    DISABLED.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModulesValue(_messages.Message):
    r"""The configurations including the state of enablement for the service's
    different modules. The absence of a module in the map implies its
    configuration is inherited from its parent's configuration.

    Messages:
      AdditionalProperty: An additional property for a ModulesValue object.

    Fields:
      additionalProperties: Additional properties of type ModulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModulesValue object.

      Fields:
        key: Name of the additional property.
        value: A Config attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Config', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  modules = _messages.MessageField('ModulesValue', 1)
  name = _messages.StringField(2)
  serviceAccount = _messages.StringField(3)
  serviceEnablementState = _messages.EnumField('ServiceEnablementStateValueValuesEnum', 4)
  updateTime = _messages.StringField(5)


class Vulnerability(_messages.Message):
  r"""Refers to common vulnerability fields e.g. cve, cvss, cwe etc.

  Fields:
    cve: CVE stands for Common Vulnerabilities and Exposures
      (https://cve.mitre.org/about/)
    cwes: Represents one or more Common Weakness Enumeration (CWE) information
      on this vulnerability.
    fixedPackage: The fixed package is relevant to the finding.
    offendingPackage: The offending package is relevant to the finding.
    providerRiskScore: Provider provided risk_score based on multiple factors.
      The higher the risk score, the more risky the vulnerability is.
    reachable: Represents whether the vulnerability is reachable (detected via
      static analysis)
    securityBulletin: The security bulletin is relevant to this finding.
  """

  cve = _messages.MessageField('Cve', 1)
  cwes = _messages.MessageField('Cwe', 2, repeated=True)
  fixedPackage = _messages.MessageField('Package', 3)
  offendingPackage = _messages.MessageField('Package', 4)
  providerRiskScore = _messages.IntegerField(5)
  reachable = _messages.BooleanField(6)
  securityBulletin = _messages.MessageField('SecurityBulletin', 7)


class VulnerabilityCountBySeverity(_messages.Message):
  r"""Vulnerability count by severity.

  Messages:
    SeverityToFindingCountValue: Key is the Severity enum.

  Fields:
    severityToFindingCount: Key is the Severity enum.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SeverityToFindingCountValue(_messages.Message):
    r"""Key is the Severity enum.

    Messages:
      AdditionalProperty: An additional property for a
        SeverityToFindingCountValue object.

    Fields:
      additionalProperties: Additional properties of type
        SeverityToFindingCountValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SeverityToFindingCountValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  severityToFindingCount = _messages.MessageField('SeverityToFindingCountValue', 1)


class VulnerabilitySnapshot(_messages.Message):
  r"""Result containing the properties and count of a VulnerabilitySnapshot
  request.

  Enums:
    CloudProviderValueValuesEnum: The cloud provider for the vulnerability
      snapshot.

  Fields:
    cloudProvider: The cloud provider for the vulnerability snapshot.
    findingCount: The vulnerability count by severity.
    name: Identifier. The vulnerability snapshot name. Format:
      //locations//vulnerabilitySnapshots/
    snapshotTime: The time that the snapshot was taken.
  """

  class CloudProviderValueValuesEnum(_messages.Enum):
    r"""The cloud provider for the vulnerability snapshot.

    Values:
      CLOUD_PROVIDER_UNSPECIFIED: The cloud provider is unspecified.
      GOOGLE_CLOUD_PLATFORM: The cloud provider is Google Cloud Platform.
      AMAZON_WEB_SERVICES: The cloud provider is Amazon Web Services.
      MICROSOFT_AZURE: The cloud provider is Microsoft Azure.
    """
    CLOUD_PROVIDER_UNSPECIFIED = 0
    GOOGLE_CLOUD_PLATFORM = 1
    AMAZON_WEB_SERVICES = 2
    MICROSOFT_AZURE = 3

  cloudProvider = _messages.EnumField('CloudProviderValueValuesEnum', 1)
  findingCount = _messages.MessageField('VulnerabilityCountBySeverity', 2)
  name = _messages.StringField(3)
  snapshotTime = _messages.StringField(4)


class WebSecurityScannerSettings(_messages.Message):
  r"""Resource capturing the settings for the Web Security Scanner service.

  Enums:
    ServiceEnablementStateValueValuesEnum: The state of enablement for the
      service at its level of the resource hierarchy. A DISABLED state will
      override all module enablement_states to DISABLED.

  Messages:
    ModulesValue: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's configuration.

  Fields:
    modules: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's configuration.
    name: Identifier. The resource name of the WebSecurityScannerSettings.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
    serviceEnablementState: The state of enablement for the service at its
      level of the resource hierarchy. A DISABLED state will override all
      module enablement_states to DISABLED.
    updateTime: Output only. The time the settings were last updated.
  """

  class ServiceEnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the service at its level of the resource
    hierarchy. A DISABLED state will override all module enablement_states to
    DISABLED.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModulesValue(_messages.Message):
    r"""The configurations including the state of enablement for the service's
    different modules. The absence of a module in the map implies its
    configuration is inherited from its parent's configuration.

    Messages:
      AdditionalProperty: An additional property for a ModulesValue object.

    Fields:
      additionalProperties: Additional properties of type ModulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModulesValue object.

      Fields:
        key: Name of the additional property.
        value: A Config attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Config', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  modules = _messages.MessageField('ModulesValue', 1)
  name = _messages.StringField(2)
  serviceEnablementState = _messages.EnumField('ServiceEnablementStateValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class YaraRuleSignature(_messages.Message):
  r"""A signature corresponding to a YARA rule.

  Fields:
    yaraRule: The name of the YARA rule.
  """

  yaraRule = _messages.StringField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
