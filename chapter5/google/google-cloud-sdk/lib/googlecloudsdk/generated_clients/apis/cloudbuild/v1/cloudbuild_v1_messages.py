"""Generated message classes for cloudbuild version v1.

Creates and manages builds on Google Cloud Platform.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'cloudbuild'


class AnthosWorkerPool(_messages.Message):
  r"""Anthos CICD cluster option.

  Fields:
    membership: Membership of the GKE Hub registered cluster this build should
      execute on. Example:
      /projects/{project}/locations/{location}/memberships/{cluster_name} The
      cluster's project number must be the same project ID that is running the
      build.
  """

  membership = _messages.StringField(1)


class ApprovalConfig(_messages.Message):
  r"""ApprovalConfig describes configuration for manual approval of a build.

  Fields:
    approvalRequired: Whether or not approval is needed. If this is set on a
      build, it will become pending when created, and will need to be
      explicitly approved to start.
  """

  approvalRequired = _messages.BooleanField(1)


class ApprovalResult(_messages.Message):
  r"""ApprovalResult describes the decision and associated metadata of a
  manual approval of a build.

  Enums:
    DecisionValueValuesEnum: Required. The decision of this manual approval.

  Fields:
    approvalTime: Output only. The time when the approval decision was made.
    approverAccount: Output only. Email of the user that called the
      ApproveBuild API to approve or reject a build at the time that the API
      was called.
    comment: Optional. An optional comment for this manual approval result.
    decision: Required. The decision of this manual approval.
    url: Optional. An optional URL tied to this manual approval result. This
      field is essentially the same as comment, except that it will be
      rendered by the UI differently. An example use case is a link to an
      external job that approved this Build.
  """

  class DecisionValueValuesEnum(_messages.Enum):
    r"""Required. The decision of this manual approval.

    Values:
      DECISION_UNSPECIFIED: Default enum type. This should not be used.
      APPROVED: Build is approved.
      REJECTED: Build is rejected.
    """
    DECISION_UNSPECIFIED = 0
    APPROVED = 1
    REJECTED = 2

  approvalTime = _messages.StringField(1)
  approverAccount = _messages.StringField(2)
  comment = _messages.StringField(3)
  decision = _messages.EnumField('DecisionValueValuesEnum', 4)
  url = _messages.StringField(5)


class ApproveBuildRequest(_messages.Message):
  r"""Request to approve or reject a pending build.

  Fields:
    approvalResult: Approval decision and metadata.
  """

  approvalResult = _messages.MessageField('ApprovalResult', 1)


class ArtifactObjects(_messages.Message):
  r"""Files in the workspace to upload to Cloud Storage upon successful
  completion of all build steps.

  Fields:
    location: Cloud Storage bucket and optional object path, in the form
      "gs://bucket/path/to/somewhere/". (see [Bucket Name
      Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)). Files in the workspace matching any path pattern
      will be uploaded to Cloud Storage with this location as a prefix.
    paths: Path globs used to match files in the build's workspace.
    timing: Output only. Stores timing information for pushing all artifact
      objects.
  """

  location = _messages.StringField(1)
  paths = _messages.StringField(2, repeated=True)
  timing = _messages.MessageField('TimeSpan', 3)


class ArtifactResult(_messages.Message):
  r"""An artifact that was uploaded during a build. This is a single record in
  the artifact manifest JSON file.

  Fields:
    fileHash: The file hash of the artifact.
    location: The path of an artifact in a Cloud Storage bucket, with the
      generation number. For example,
      `gs://mybucket/path/to/output.jar#generation`.
  """

  fileHash = _messages.MessageField('FileHashes', 1, repeated=True)
  location = _messages.StringField(2)


class Artifacts(_messages.Message):
  r"""Artifacts produced by a build that should be uploaded upon successful
  completion of all build steps.

  Fields:
    goModules: Optional. A list of Go modules to be uploaded to Artifact
      Registry upon successful completion of all build steps. If any objects
      fail to be pushed, the build is marked FAILURE.
    images: A list of images to be pushed upon the successful completion of
      all build steps. The images will be pushed using the builder service
      account's credentials. The digests of the pushed images will be stored
      in the Build resource's results field. If any of the images fail to be
      pushed, the build is marked FAILURE.
    mavenArtifacts: A list of Maven artifacts to be uploaded to Artifact
      Registry upon successful completion of all build steps. Artifacts in the
      workspace matching specified paths globs will be uploaded to the
      specified Artifact Registry repository using the builder service
      account's credentials. If any artifacts fail to be pushed, the build is
      marked FAILURE.
    npmPackages: A list of npm packages to be uploaded to Artifact Registry
      upon successful completion of all build steps. Npm packages in the
      specified paths will be uploaded to the specified Artifact Registry
      repository using the builder service account's credentials. If any
      packages fail to be pushed, the build is marked FAILURE.
    objects: A list of objects to be uploaded to Cloud Storage upon successful
      completion of all build steps. Files in the workspace matching specified
      paths globs will be uploaded to the specified Cloud Storage location
      using the builder service account's credentials. The location and
      generation of the uploaded objects will be stored in the Build
      resource's results field. If any objects fail to be pushed, the build is
      marked FAILURE.
    pythonPackages: A list of Python packages to be uploaded to Artifact
      Registry upon successful completion of all build steps. The build
      service account credentials will be used to perform the upload. If any
      objects fail to be pushed, the build is marked FAILURE.
  """

  goModules = _messages.MessageField('GoModule', 1, repeated=True)
  images = _messages.StringField(2, repeated=True)
  mavenArtifacts = _messages.MessageField('MavenArtifact', 3, repeated=True)
  npmPackages = _messages.MessageField('NpmPackage', 4, repeated=True)
  objects = _messages.MessageField('ArtifactObjects', 5)
  pythonPackages = _messages.MessageField('PythonPackage', 6, repeated=True)


class BatchCreateBitbucketServerConnectedRepositoriesRequest(_messages.Message):
  r"""RPC request object accepted by
  BatchCreateBitbucketServerConnectedRepositories RPC method.

  Fields:
    requests: Required. Requests to connect Bitbucket Server repositories.
  """

  requests = _messages.MessageField('CreateBitbucketServerConnectedRepositoryRequest', 1, repeated=True)


class BatchCreateBitbucketServerConnectedRepositoriesResponse(_messages.Message):
  r"""Response of BatchCreateBitbucketServerConnectedRepositories RPC method
  including all successfully connected Bitbucket Server repositories.

  Fields:
    bitbucketServerConnectedRepositories: The connected Bitbucket Server
      repositories.
  """

  bitbucketServerConnectedRepositories = _messages.MessageField('BitbucketServerConnectedRepository', 1, repeated=True)


class BatchCreateBitbucketServerConnectedRepositoriesResponseMetadata(_messages.Message):
  r"""Metadata for `BatchCreateBitbucketServerConnectedRepositories`
  operation.

  Fields:
    completeTime: Time the operation was completed.
    config: The name of the `BitbucketServerConfig` that added connected
      repositories. Format: `projects/{project}/locations/{location}/bitbucket
      ServerConfigs/{config}`
    createTime: Time the operation was created.
  """

  completeTime = _messages.StringField(1)
  config = _messages.StringField(2)
  createTime = _messages.StringField(3)


class BatchCreateGitLabConnectedRepositoriesRequest(_messages.Message):
  r"""RPC request object accepted by BatchCreateGitLabConnectedRepositories
  RPC method.

  Fields:
    requests: Required. Requests to connect GitLab repositories.
  """

  requests = _messages.MessageField('CreateGitLabConnectedRepositoryRequest', 1, repeated=True)


class BatchCreateGitLabConnectedRepositoriesResponse(_messages.Message):
  r"""Response of BatchCreateGitLabConnectedRepositories RPC method.

  Fields:
    gitlabConnectedRepositories: The GitLab connected repository requests'
      responses.
  """

  gitlabConnectedRepositories = _messages.MessageField('GitLabConnectedRepository', 1, repeated=True)


class BatchCreateGitLabConnectedRepositoriesResponseMetadata(_messages.Message):
  r"""Metadata for `BatchCreateGitLabConnectedRepositories` operation.

  Fields:
    completeTime: Time the operation was completed.
    config: The name of the `GitLabConfig` that added connected repositories.
      Format: `projects/{project}/locations/{location}/gitLabConfigs/{config}`
    createTime: Time the operation was created.
  """

  completeTime = _messages.StringField(1)
  config = _messages.StringField(2)
  createTime = _messages.StringField(3)


class BitbucketServerConfig(_messages.Message):
  r"""BitbucketServerConfig represents the configuration for a Bitbucket
  Server.

  Fields:
    apiKey: Required. Immutable. API Key that will be attached to webhook.
      Once this field has been set, it cannot be changed. If you need to
      change it, please create another BitbucketServerConfig.
    connectedRepositories: Output only. Connected Bitbucket Server
      repositories for this config.
    createTime: Time when the config was created.
    hostUri: Required. Immutable. The URI of the Bitbucket Server host. Once
      this field has been set, it cannot be changed. If you need to change it,
      please create another BitbucketServerConfig.
    name: The resource name for the config.
    peeredNetwork: Optional. The network to be used when reaching out to the
      Bitbucket Server instance. The VPC network must be enabled for private
      service connection. This should be set if the Bitbucket Server instance
      is hosted on-premises and not reachable by public internet. If this
      field is left empty, no network peering will occur and calls to the
      Bitbucket Server instance will be made over the public internet. Must be
      in the format `projects/{project}/global/networks/{network}`, where
      {project} is a project number or id and {network} is the name of a VPC
      network in the project.
    peeredNetworkIpRange: Immutable. IP range within the peered network. This
      is specified in CIDR notation with a slash and the subnet prefix size.
      You can optionally specify an IP address before the subnet prefix value.
      e.g. `***********/29` would specify an IP range starting at ***********
      with a 29 bit prefix size. `/16` would specify a prefix size of 16 bits,
      with an automatically determined IP within the peered VPC. If
      unspecified, a value of `/24` will be used. The field only has an effect
      if peered_network is set.
    secrets: Required. Secret Manager secrets needed by the config.
    sslCa: Optional. SSL certificate to use for requests to Bitbucket Server.
      The format should be PEM format but the extension can be one of .pem,
      .cer, or .crt.
    username: Username of the account Cloud Build will use on Bitbucket
      Server.
    webhookKey: Output only. UUID included in webhook requests. The UUID is
      used to look up the corresponding config.
  """

  apiKey = _messages.StringField(1)
  connectedRepositories = _messages.MessageField('BitbucketServerRepositoryId', 2, repeated=True)
  createTime = _messages.StringField(3)
  hostUri = _messages.StringField(4)
  name = _messages.StringField(5)
  peeredNetwork = _messages.StringField(6)
  peeredNetworkIpRange = _messages.StringField(7)
  secrets = _messages.MessageField('BitbucketServerSecrets', 8)
  sslCa = _messages.StringField(9)
  username = _messages.StringField(10)
  webhookKey = _messages.StringField(11)


class BitbucketServerConnectedRepository(_messages.Message):
  r"""/ BitbucketServerConnectedRepository represents a connected Bitbucket
  Server / repository.

  Fields:
    parent: The name of the `BitbucketServerConfig` that added connected
      repository. Format: `projects/{project}/locations/{location}/bitbucketSe
      rverConfigs/{config}`
    repo: The Bitbucket Server repositories to connect.
    status: Output only. The status of the repo connection request.
  """

  parent = _messages.StringField(1)
  repo = _messages.MessageField('BitbucketServerRepositoryId', 2)
  status = _messages.MessageField('Status', 3)


class BitbucketServerRepository(_messages.Message):
  r"""BitbucketServerRepository represents a repository hosted on a Bitbucket
  Server.

  Fields:
    browseUri: Link to the browse repo page on the Bitbucket Server instance.
    description: Description of the repository.
    displayName: Display name of the repository.
    name: The resource name of the repository.
    repoId: Identifier for a repository hosted on a Bitbucket Server.
  """

  browseUri = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  name = _messages.StringField(4)
  repoId = _messages.MessageField('BitbucketServerRepositoryId', 5)


class BitbucketServerRepositoryId(_messages.Message):
  r"""BitbucketServerRepositoryId identifies a specific repository hosted on a
  Bitbucket Server.

  Fields:
    projectKey: Required. Identifier for the project storing the repository.
    repoSlug: Required. Identifier for the repository.
    webhookId: Output only. The ID of the webhook that was created for
      receiving events from this repo. We only create and manage a single
      webhook for each repo.
  """

  projectKey = _messages.StringField(1)
  repoSlug = _messages.StringField(2)
  webhookId = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class BitbucketServerSecrets(_messages.Message):
  r"""BitbucketServerSecrets represents the secrets in Secret Manager for a
  Bitbucket Server.

  Fields:
    adminAccessTokenVersionName: Required. The resource name for the admin
      access token's secret version.
    readAccessTokenVersionName: Required. The resource name for the read
      access token's secret version.
    webhookSecretVersionName: Required. Immutable. The resource name for the
      webhook secret's secret version. Once this field has been set, it cannot
      be changed. If you need to change it, please create another
      BitbucketServerConfig.
  """

  adminAccessTokenVersionName = _messages.StringField(1)
  readAccessTokenVersionName = _messages.StringField(2)
  webhookSecretVersionName = _messages.StringField(3)


class BitbucketServerTriggerConfig(_messages.Message):
  r"""BitbucketServerTriggerConfig describes the configuration of a trigger
  that creates a build whenever a Bitbucket Server event is received.

  Fields:
    bitbucketServerConfig: Output only. The BitbucketServerConfig specified in
      the bitbucket_server_config_resource field.
    bitbucketServerConfigResource: Required. The Bitbucket server config
      resource that this trigger config maps to.
    projectKey: Required. Key of the project that the repo is in. For example:
      The key for https://mybitbucket.server/projects/TEST/repos/test-repo is
      "TEST".
    pullRequest: Filter to match changes in pull requests.
    push: Filter to match changes in refs like branches, tags.
    repoSlug: Required. Slug of the repository. A repository slug is a URL-
      friendly version of a repository name, automatically generated by
      Bitbucket for use in the URL. For example, if the repository name is
      'test repo', in the URL it would become 'test-repo' as in
      https://mybitbucket.server/projects/TEST/repos/test-repo.
  """

  bitbucketServerConfig = _messages.MessageField('BitbucketServerConfig', 1)
  bitbucketServerConfigResource = _messages.StringField(2)
  projectKey = _messages.StringField(3)
  pullRequest = _messages.MessageField('PullRequestFilter', 4)
  push = _messages.MessageField('PushFilter', 5)
  repoSlug = _messages.StringField(6)


class Build(_messages.Message):
  r"""A build resource in the Cloud Build API. At a high level, a `Build`
  describes where to find source code, how to build it (for example, the
  builder image to run on the source), and where to store the built artifacts.
  Fields can include the following variables, which will be expanded when the
  build is created: - $PROJECT_ID: the project ID of the build. -
  $PROJECT_NUMBER: the project number of the build. - $LOCATION: the
  location/region of the build. - $BUILD_ID: the autogenerated ID of the
  build. - $REPO_NAME: the source repository name specified by RepoSource. -
  $BRANCH_NAME: the branch name specified by RepoSource. - $TAG_NAME: the tag
  name specified by RepoSource. - $REVISION_ID or $COMMIT_SHA: the commit SHA
  specified by RepoSource or resolved from the specified branch or tag. -
  $SHORT_SHA: first 7 characters of $REVISION_ID or $COMMIT_SHA.

  Enums:
    StatusValueValuesEnum: Output only. Status of the build.

  Messages:
    SubstitutionsValue: Substitutions data for `Build` resource.
    TimingValue: Output only. Stores timing information for phases of the
      build. Valid keys are: * BUILD: time to execute all build steps. * PUSH:
      time to push all artifacts including docker images and non docker
      artifacts. * FETCHSOURCE: time to fetch source. * SETUPBUILD: time to
      set up build. If the build does not specify source or images, these keys
      will not be included.

  Fields:
    approval: Output only. Describes this build's approval configuration,
      status, and result.
    artifacts: Artifacts produced by the build that should be uploaded upon
      successful completion of all build steps.
    availableSecrets: Secrets and secret environment variables.
    buildTriggerId: Output only. The ID of the `BuildTrigger` that triggered
      this build, if it was triggered automatically.
    createTime: Output only. Time at which the request to create the build was
      received.
    dependencies: Optional. Dependencies that the Cloud Build worker will
      fetch before executing user steps.
    failureInfo: Output only. Contains information about the build when
      status=FAILURE.
    finishTime: Output only. Time at which execution of the build was
      finished. The difference between finish_time and start_time is the
      duration of the build's execution.
    gitConfig: Optional. Configuration for git operations.
    id: Output only. Unique identifier of the build.
    images: A list of images to be pushed upon the successful completion of
      all build steps. The images are pushed using the builder service
      account's credentials. The digests of the pushed images will be stored
      in the `Build` resource's results field. If any of the images fail to be
      pushed, the build status is marked `FAILURE`.
    logUrl: Output only. URL to logs for this build in Google Cloud Console.
    logsBucket: Cloud Storage bucket where logs should be written (see [Bucket
      Name Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)). Logs file names will be of the format
      `${logs_bucket}/log-${build_id}.txt`.
    name: Output only. The 'Build' name with format:
      `projects/{project}/locations/{location}/builds/{build}`, where {build}
      is a unique identifier generated by the service.
    options: Special options for this build.
    projectId: Output only. ID of the project.
    queueTtl: TTL in queue for this build. If provided and the build is
      enqueued longer than this value, the build will expire and the build
      status will be `EXPIRED`. The TTL starts ticking from create_time.
    results: Output only. Results of the build.
    secrets: Secrets to decrypt using Cloud Key Management Service. Note:
      Secret Manager is the recommended technique for managing sensitive data
      with Cloud Build. Use `available_secrets` to configure builds to access
      secrets from Secret Manager. For instructions, see:
      https://cloud.google.com/cloud-build/docs/securing-builds/use-secrets
    serviceAccount: IAM service account whose credentials will be used at
      build runtime. Must be of the format
      `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`. ACCOUNT can be email
      address or uniqueId of the service account.
    source: Optional. The location of the source files to build.
    sourceProvenance: Output only. A permanent fixed identifier for source.
    startTime: Output only. Time at which execution of the build was started.
    status: Output only. Status of the build.
    statusDetail: Output only. Customer-readable message about the current
      status.
    steps: Required. The operations to be performed on the workspace.
    substitutions: Substitutions data for `Build` resource.
    tags: Tags for annotation of a `Build`. These are not docker tags.
    timeout: Amount of time that this build should be allowed to run, to
      second granularity. If this amount of time elapses, work on the build
      will cease and the build status will be `TIMEOUT`. `timeout` starts
      ticking from `startTime`. Default time is 60 minutes.
    timing: Output only. Stores timing information for phases of the build.
      Valid keys are: * BUILD: time to execute all build steps. * PUSH: time
      to push all artifacts including docker images and non docker artifacts.
      * FETCHSOURCE: time to fetch source. * SETUPBUILD: time to set up build.
      If the build does not specify source or images, these keys will not be
      included.
    warnings: Output only. Non-fatal problems encountered during the execution
      of the build.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the build.

    Values:
      STATUS_UNKNOWN: Status of the build is unknown.
      PENDING: Build has been created and is pending execution and queuing. It
        has not been queued.
      QUEUED: Build or step is queued; work has not yet begun.
      WORKING: Build or step is being executed.
      SUCCESS: Build or step finished successfully.
      FAILURE: Build or step failed to complete successfully.
      INTERNAL_ERROR: Build or step failed due to an internal cause.
      TIMEOUT: Build or step took longer than was allowed.
      CANCELLED: Build or step was canceled by a user.
      EXPIRED: Build was enqueued for longer than the value of `queue_ttl`.
    """
    STATUS_UNKNOWN = 0
    PENDING = 1
    QUEUED = 2
    WORKING = 3
    SUCCESS = 4
    FAILURE = 5
    INTERNAL_ERROR = 6
    TIMEOUT = 7
    CANCELLED = 8
    EXPIRED = 9

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SubstitutionsValue(_messages.Message):
    r"""Substitutions data for `Build` resource.

    Messages:
      AdditionalProperty: An additional property for a SubstitutionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type SubstitutionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SubstitutionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TimingValue(_messages.Message):
    r"""Output only. Stores timing information for phases of the build. Valid
    keys are: * BUILD: time to execute all build steps. * PUSH: time to push
    all artifacts including docker images and non docker artifacts. *
    FETCHSOURCE: time to fetch source. * SETUPBUILD: time to set up build. If
    the build does not specify source or images, these keys will not be
    included.

    Messages:
      AdditionalProperty: An additional property for a TimingValue object.

    Fields:
      additionalProperties: Additional properties of type TimingValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TimingValue object.

      Fields:
        key: Name of the additional property.
        value: A TimeSpan attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('TimeSpan', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  approval = _messages.MessageField('BuildApproval', 1)
  artifacts = _messages.MessageField('Artifacts', 2)
  availableSecrets = _messages.MessageField('Secrets', 3)
  buildTriggerId = _messages.StringField(4)
  createTime = _messages.StringField(5)
  dependencies = _messages.MessageField('Dependency', 6, repeated=True)
  failureInfo = _messages.MessageField('FailureInfo', 7)
  finishTime = _messages.StringField(8)
  gitConfig = _messages.MessageField('GitConfig', 9)
  id = _messages.StringField(10)
  images = _messages.StringField(11, repeated=True)
  logUrl = _messages.StringField(12)
  logsBucket = _messages.StringField(13)
  name = _messages.StringField(14)
  options = _messages.MessageField('BuildOptions', 15)
  projectId = _messages.StringField(16)
  queueTtl = _messages.StringField(17)
  results = _messages.MessageField('Results', 18)
  secrets = _messages.MessageField('Secret', 19, repeated=True)
  serviceAccount = _messages.StringField(20)
  source = _messages.MessageField('Source', 21)
  sourceProvenance = _messages.MessageField('SourceProvenance', 22)
  startTime = _messages.StringField(23)
  status = _messages.EnumField('StatusValueValuesEnum', 24)
  statusDetail = _messages.StringField(25)
  steps = _messages.MessageField('BuildStep', 26, repeated=True)
  substitutions = _messages.MessageField('SubstitutionsValue', 27)
  tags = _messages.StringField(28, repeated=True)
  timeout = _messages.StringField(29)
  timing = _messages.MessageField('TimingValue', 30)
  warnings = _messages.MessageField('Warning', 31, repeated=True)


class BuildApproval(_messages.Message):
  r"""BuildApproval describes a build's approval configuration, state, and
  result.

  Enums:
    StateValueValuesEnum: Output only. The state of this build's approval.

  Fields:
    config: Output only. Configuration for manual approval of this build.
    result: Output only. Result of manual approval for this Build.
    state: Output only. The state of this build's approval.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of this build's approval.

    Values:
      STATE_UNSPECIFIED: Default enum type. This should not be used.
      PENDING: Build approval is pending.
      APPROVED: Build approval has been approved.
      REJECTED: Build approval has been rejected.
      CANCELLED: Build was cancelled while it was still pending approval.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    APPROVED = 2
    REJECTED = 3
    CANCELLED = 4

  config = _messages.MessageField('ApprovalConfig', 1)
  result = _messages.MessageField('ApprovalResult', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class BuildOperationMetadata(_messages.Message):
  r"""Metadata for build operations.

  Fields:
    build: The build that the operation is tracking.
  """

  build = _messages.MessageField('Build', 1)


class BuildOptions(_messages.Message):
  r"""Optional arguments to enable specific features of builds.

  Enums:
    DefaultLogsBucketBehaviorValueValuesEnum: Optional. Option to specify how
      default logs buckets are setup.
    DockerDaemonValueValuesEnum: Optional. Option to specify how (or if) a
      Docker daemon is provided for the build.
    LogStreamingOptionValueValuesEnum: Option to define build log streaming
      behavior to Cloud Storage.
    LoggingValueValuesEnum: Option to specify the logging mode, which
      determines if and where build logs are stored.
    MachineTypeValueValuesEnum: Compute Engine machine type on which to run
      the build.
    RequestedVerifyOptionValueValuesEnum: Requested verifiability options.
    SourceProvenanceHashValueListEntryValuesEnum:
    SubstitutionOptionValueValuesEnum: Option to specify behavior when there
      is an error in the substitution checks. NOTE: this is always set to
      ALLOW_LOOSE for triggered builds and cannot be overridden in the build
      configuration file.

  Fields:
    anthosCluster: Details about how this build should be executed on a Anthos
      cluster.
    automapSubstitutions: Option to include built-in and custom substitutions
      as env variables for all build steps.
    cluster: Details about how this build should be executed on a GKE cluster.
    defaultLogsBucketBehavior: Optional. Option to specify how default logs
      buckets are setup.
    diskSizeGb: Requested disk size for the VM that runs the build. Note that
      this is *NOT* "disk free"; some of the space will be used by the
      operating system and build utilities. Also note that this is the minimum
      disk size that will be allocated for the build -- the build may run with
      a larger disk than requested. At present, the maximum disk size is
      4000GB; builds that request more than the maximum are rejected with an
      error.
    dockerDaemon: Optional. Option to specify how (or if) a Docker daemon is
      provided for the build.
    dynamicSubstitutions: Option to specify whether or not to apply bash style
      string operations to the substitutions. NOTE: this is always enabled for
      triggered builds and cannot be overridden in the build configuration
      file.
    enableStructuredLogging: Optional. Option to specify whether structured
      logging is enabled. If true, JSON-formatted logs are parsed as
      structured logs.
    env: A list of global environment variable definitions that will exist for
      all build steps in this build. If a variable is defined in both globally
      and in a build step, the variable will use the build step value. The
      elements are of the form "KEY=VALUE" for the environment variable "KEY"
      being given the value "VALUE".
    logStreamingOption: Option to define build log streaming behavior to Cloud
      Storage.
    logging: Option to specify the logging mode, which determines if and where
      build logs are stored.
    machineType: Compute Engine machine type on which to run the build.
    pool: Optional. Specification for execution on a `WorkerPool`. See
      [running builds in a private
      pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-
      private-pool) for more information.
    pubsubTopic: Optional. Option to specify the Pub/Sub topic to receive
      build status updates.
    requestedVerifyOption: Requested verifiability options.
    secretEnv: A list of global environment variables, which are encrypted
      using a Cloud Key Management Service crypto key. These values must be
      specified in the build's `Secret`. These variables will be available to
      all build steps in this build.
    sourceProvenanceHash: Requested hash for SourceProvenance.
    substitutionOption: Option to specify behavior when there is an error in
      the substitution checks. NOTE: this is always set to ALLOW_LOOSE for
      triggered builds and cannot be overridden in the build configuration
      file.
    volumes: Global list of volumes to mount for ALL build steps Each volume
      is created as an empty volume prior to starting the build process. Upon
      completion of the build, volumes and their contents are discarded.
      Global volume names and paths cannot conflict with the volumes defined a
      build step. Using a global volume in a build with only one step is not
      valid as it is indicative of a build request with an incorrect
      configuration.
    workerPool: This field deprecated; please use `pool.name` instead.
  """

  class DefaultLogsBucketBehaviorValueValuesEnum(_messages.Enum):
    r"""Optional. Option to specify how default logs buckets are setup.

    Values:
      DEFAULT_LOGS_BUCKET_BEHAVIOR_UNSPECIFIED: Unspecified.
      REGIONAL_USER_OWNED_BUCKET: Bucket is located in user-owned project in
        the same region as the build. The builder service account must have
        access to create and write to Cloud Storage buckets in the build
        project.
      LEGACY_BUCKET: Bucket is located in a Google-owned project and is not
        regionalized.
    """
    DEFAULT_LOGS_BUCKET_BEHAVIOR_UNSPECIFIED = 0
    REGIONAL_USER_OWNED_BUCKET = 1
    LEGACY_BUCKET = 2

  class DockerDaemonValueValuesEnum(_messages.Enum):
    r"""Optional. Option to specify how (or if) a Docker daemon is provided
    for the build.

    Values:
      DOCKER_DAEMON_UNSPECIFIED: If the option is unspecified, a default will
        be set based on the environment.
      NO_DOCKER: No Docker daemon or functionality will be provided to the
        build.
      NON_PRIVILEGED: A Docker daemon is available during the build that is
        running without privileged mode.
      PRIVILEGED: A Docker daemon will be available that is running in
        privileged mode. This is potentially a security vulnerability and
        should only be used if the user is fully aware of the associated
        risks.
    """
    DOCKER_DAEMON_UNSPECIFIED = 0
    NO_DOCKER = 1
    NON_PRIVILEGED = 2
    PRIVILEGED = 3

  class LogStreamingOptionValueValuesEnum(_messages.Enum):
    r"""Option to define build log streaming behavior to Cloud Storage.

    Values:
      STREAM_DEFAULT: Service may automatically determine build log streaming
        behavior.
      STREAM_ON: Build logs should be streamed to Cloud Storage.
      STREAM_OFF: Build logs should not be streamed to Cloud Storage; they
        will be written when the build is completed.
    """
    STREAM_DEFAULT = 0
    STREAM_ON = 1
    STREAM_OFF = 2

  class LoggingValueValuesEnum(_messages.Enum):
    r"""Option to specify the logging mode, which determines if and where
    build logs are stored.

    Values:
      LOGGING_UNSPECIFIED: The service determines the logging mode. The
        default is `LEGACY`. Do not rely on the default logging behavior as it
        may change in the future.
      LEGACY: Build logs are stored in Cloud Logging and Cloud Storage.
      GCS_ONLY: Build logs are stored in Cloud Storage.
      STACKDRIVER_ONLY: This option is the same as CLOUD_LOGGING_ONLY.
      CLOUD_LOGGING_ONLY: Build logs are stored in Cloud Logging. Selecting
        this option will not allow [logs
        streaming](https://cloud.google.com/sdk/gcloud/reference/builds/log).
      NONE: Turn off all logging. No build logs will be captured.
    """
    LOGGING_UNSPECIFIED = 0
    LEGACY = 1
    GCS_ONLY = 2
    STACKDRIVER_ONLY = 3
    CLOUD_LOGGING_ONLY = 4
    NONE = 5

  class MachineTypeValueValuesEnum(_messages.Enum):
    r"""Compute Engine machine type on which to run the build.

    Values:
      UNSPECIFIED: Standard machine type.
      N1_HIGHCPU_8: Highcpu machine with 8 CPUs.
      N1_HIGHCPU_32: Highcpu machine with 32 CPUs.
      E2_HIGHCPU_8: Highcpu e2 machine with 8 CPUs.
      E2_HIGHCPU_32: Highcpu e2 machine with 32 CPUs.
      E2_MEDIUM: E2 machine with 1 CPU.
    """
    UNSPECIFIED = 0
    N1_HIGHCPU_8 = 1
    N1_HIGHCPU_32 = 2
    E2_HIGHCPU_8 = 3
    E2_HIGHCPU_32 = 4
    E2_MEDIUM = 5

  class RequestedVerifyOptionValueValuesEnum(_messages.Enum):
    r"""Requested verifiability options.

    Values:
      NOT_VERIFIED: Not a verifiable build (the default).
      VERIFIED: Build must be verified.
    """
    NOT_VERIFIED = 0
    VERIFIED = 1

  class SourceProvenanceHashValueListEntryValuesEnum(_messages.Enum):
    r"""SourceProvenanceHashValueListEntryValuesEnum enum type.

    Values:
      NONE: No hash requested.
      SHA256: Use a sha256 hash.
      MD5: Use a md5 hash.
      GO_MODULE_H1: Dirhash of a Go module's source code which is then hex-
        encoded.
      SHA512: Use a sha512 hash.
    """
    NONE = 0
    SHA256 = 1
    MD5 = 2
    GO_MODULE_H1 = 3
    SHA512 = 4

  class SubstitutionOptionValueValuesEnum(_messages.Enum):
    r"""Option to specify behavior when there is an error in the substitution
    checks. NOTE: this is always set to ALLOW_LOOSE for triggered builds and
    cannot be overridden in the build configuration file.

    Values:
      MUST_MATCH: Fails the build if error in substitutions checks, like
        missing a substitution in the template or in the map.
      ALLOW_LOOSE: Do not fail the build if error in substitutions checks.
    """
    MUST_MATCH = 0
    ALLOW_LOOSE = 1

  anthosCluster = _messages.MessageField('AnthosWorkerPool', 1)
  automapSubstitutions = _messages.BooleanField(2)
  cluster = _messages.MessageField('ClusterOptions', 3)
  defaultLogsBucketBehavior = _messages.EnumField('DefaultLogsBucketBehaviorValueValuesEnum', 4)
  diskSizeGb = _messages.IntegerField(5)
  dockerDaemon = _messages.EnumField('DockerDaemonValueValuesEnum', 6)
  dynamicSubstitutions = _messages.BooleanField(7)
  enableStructuredLogging = _messages.BooleanField(8)
  env = _messages.StringField(9, repeated=True)
  logStreamingOption = _messages.EnumField('LogStreamingOptionValueValuesEnum', 10)
  logging = _messages.EnumField('LoggingValueValuesEnum', 11)
  machineType = _messages.EnumField('MachineTypeValueValuesEnum', 12)
  pool = _messages.MessageField('PoolOption', 13)
  pubsubTopic = _messages.StringField(14)
  requestedVerifyOption = _messages.EnumField('RequestedVerifyOptionValueValuesEnum', 15)
  secretEnv = _messages.StringField(16, repeated=True)
  sourceProvenanceHash = _messages.EnumField('SourceProvenanceHashValueListEntryValuesEnum', 17, repeated=True)
  substitutionOption = _messages.EnumField('SubstitutionOptionValueValuesEnum', 18)
  volumes = _messages.MessageField('Volume', 19, repeated=True)
  workerPool = _messages.StringField(20)


class BuildStep(_messages.Message):
  r"""A step in the build pipeline.

  Enums:
    StatusValueValuesEnum: Output only. Status of the build step. At this
      time, build step status is only updated on build completion; step status
      is not updated in real-time as the build progresses.

  Fields:
    allowExitCodes: Allow this build step to fail without failing the entire
      build if and only if the exit code is one of the specified codes. If
      allow_failure is also specified, this field will take precedence.
    allowFailure: Allow this build step to fail without failing the entire
      build. If false, the entire build will fail if this step fails.
      Otherwise, the build will succeed, but this step will still have a
      failure status. Error information will be reported in the failure_detail
      field.
    args: A list of arguments that will be presented to the step when it is
      started. If the image used to run the step's container has an
      entrypoint, the `args` are used as arguments to that entrypoint. If the
      image does not define an entrypoint, the first element in args is used
      as the entrypoint, and the remainder will be used as arguments.
    automapSubstitutions: Option to include built-in and custom substitutions
      as env variables for this build step. This option will override the
      global option in BuildOption.
    dir: Working directory to use when running this step's container. If this
      value is a relative path, it is relative to the build's working
      directory. If this value is absolute, it may be outside the build's
      working directory, in which case the contents of the path may not be
      persisted across build step executions, unless a `volume` for that path
      is specified. If the build specifies a `RepoSource` with `dir` and a
      step with a `dir`, which specifies an absolute path, the `RepoSource`
      `dir` is ignored for the step's execution.
    entrypoint: Entrypoint to be used instead of the build step image's
      default entrypoint. If unset, the image's default entrypoint is used.
    env: A list of environment variable definitions to be used when running a
      step. The elements are of the form "KEY=VALUE" for the environment
      variable "KEY" being given the value "VALUE".
    exitCode: Output only. Return code from running the step.
    id: Unique identifier for this build step, used in `wait_for` to reference
      this build step as a dependency.
    name: Required. The name of the container image that will run this
      particular build step. If the image is available in the host's Docker
      daemon's cache, it will be run directly. If not, the host will attempt
      to pull the image first, using the builder service account's credentials
      if necessary. The Docker daemon's cache will already have the latest
      versions of all of the officially supported build steps
      ([https://github.com/GoogleCloudPlatform/cloud-
      builders](https://github.com/GoogleCloudPlatform/cloud-builders)). The
      Docker daemon will also have cached many of the layers for some popular
      images, like "ubuntu", "debian", but they will be refreshed at the time
      you attempt to use them. If you built an image in a previous build step,
      it will be stored in the host's Docker daemon's cache and is available
      to use as the name for a later build step.
    pullTiming: Output only. Stores timing information for pulling this build
      step's builder image only.
    results: Declaration of results for this build step.
    script: A shell script to be executed in the step. When script is
      provided, the user cannot specify the entrypoint or args.
    secretEnv: A list of environment variables which are encrypted using a
      Cloud Key Management Service crypto key. These values must be specified
      in the build's `Secret`.
    status: Output only. Status of the build step. At this time, build step
      status is only updated on build completion; step status is not updated
      in real-time as the build progresses.
    timeout: Time limit for executing this build step. If not defined, the
      step has no time limit and will be allowed to continue to run until
      either it completes or the build itself times out.
    timing: Output only. Stores timing information for executing this build
      step.
    volumes: List of volumes to mount into the build step. Each volume is
      created as an empty volume prior to execution of the build step. Upon
      completion of the build, volumes and their contents are discarded. Using
      a named volume in only one step is not valid as it is indicative of a
      build request with an incorrect configuration.
    waitFor: The ID(s) of the step(s) that this build step depends on. This
      build step will not start until all the build steps in `wait_for` have
      completed successfully. If `wait_for` is empty, this build step will
      start when all previous build steps in the `Build.Steps` list have
      completed successfully.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the build step. At this time, build step status
    is only updated on build completion; step status is not updated in real-
    time as the build progresses.

    Values:
      STATUS_UNKNOWN: Status of the build is unknown.
      PENDING: Build has been created and is pending execution and queuing. It
        has not been queued.
      QUEUED: Build or step is queued; work has not yet begun.
      WORKING: Build or step is being executed.
      SUCCESS: Build or step finished successfully.
      FAILURE: Build or step failed to complete successfully.
      INTERNAL_ERROR: Build or step failed due to an internal cause.
      TIMEOUT: Build or step took longer than was allowed.
      CANCELLED: Build or step was canceled by a user.
      EXPIRED: Build was enqueued for longer than the value of `queue_ttl`.
    """
    STATUS_UNKNOWN = 0
    PENDING = 1
    QUEUED = 2
    WORKING = 3
    SUCCESS = 4
    FAILURE = 5
    INTERNAL_ERROR = 6
    TIMEOUT = 7
    CANCELLED = 8
    EXPIRED = 9

  allowExitCodes = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  allowFailure = _messages.BooleanField(2)
  args = _messages.StringField(3, repeated=True)
  automapSubstitutions = _messages.BooleanField(4)
  dir = _messages.StringField(5)
  entrypoint = _messages.StringField(6)
  env = _messages.StringField(7, repeated=True)
  exitCode = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  id = _messages.StringField(9)
  name = _messages.StringField(10)
  pullTiming = _messages.MessageField('TimeSpan', 11)
  results = _messages.MessageField('StepResult', 12, repeated=True)
  script = _messages.StringField(13)
  secretEnv = _messages.StringField(14, repeated=True)
  status = _messages.EnumField('StatusValueValuesEnum', 15)
  timeout = _messages.StringField(16)
  timing = _messages.MessageField('TimeSpan', 17)
  volumes = _messages.MessageField('Volume', 18, repeated=True)
  waitFor = _messages.StringField(19, repeated=True)


class BuildTrigger(_messages.Message):
  r"""Configuration for an automated build in response to source repository
  changes.

  Enums:
    EventTypeValueValuesEnum: EventType allows the user to explicitly set the
      type of event to which this BuildTrigger should respond. This field will
      be validated against the rest of the configuration if it is set.
    IncludeBuildLogsValueValuesEnum: If set to INCLUDE_BUILD_LOGS_WITH_STATUS,
      log url will be shown on GitHub page when build status is final. Setting
      this field to INCLUDE_BUILD_LOGS_WITH_STATUS for non GitHub triggers
      results in INVALID_ARGUMENT error.

  Messages:
    SubstitutionsValue: Substitutions for Build resource. The keys must match
      the following regular expression: `^_[A-Z0-9_]+$`.

  Fields:
    approvalConfig: Configuration for manual approval to start a build
      invocation of this BuildTrigger.
    autodetect: Autodetect build configuration. The following precedence is
      used (case insensitive): 1. cloudbuild.yaml 2. cloudbuild.yml 3.
      cloudbuild.json 4. Dockerfile Currently only available for GitHub App
      Triggers.
    bitbucketServerTriggerConfig: BitbucketServerTriggerConfig describes the
      configuration of a trigger that creates a build whenever a Bitbucket
      Server event is received.
    build: Contents of the build template.
    createTime: Output only. Time when the trigger was created.
    cron: CronConfig describes the configuration of a trigger that creates a
      build whenever a Cloud Scheduler event is received.
    description: Human-readable description of this trigger.
    developerConnectEventConfig: Optional. The configuration of a trigger that
      creates a build whenever an event from the DeveloperConnect API is
      received.
    disabled: If true, the trigger will never automatically execute a build.
    eventType: EventType allows the user to explicitly set the type of event
      to which this BuildTrigger should respond. This field will be validated
      against the rest of the configuration if it is set.
    filename: Path, from the source root, to the build configuration file
      (i.e. cloudbuild.yaml).
    filter: A Common Expression Language string.
    gitFileSource: The file source describing the local or remote Build
      template.
    github: GitHubEventsConfig describes the configuration of a trigger that
      creates a build whenever a GitHub event is received. Mutually exclusive
      with `trigger_template`.
    gitlabEnterpriseEventsConfig: GitLabEnterpriseEventsConfig describes the
      configuration of a trigger that creates a build whenever a GitLab
      Enterprise event is received.
    id: Output only. Unique identifier of the trigger.
    ignoredFiles: ignored_files and included_files are file glob matches using
      https://golang.org/pkg/path/filepath/#Match extended with support for
      "**". If ignored_files and changed files are both empty, then they are
      not used to determine whether or not to trigger a build. If
      ignored_files is not empty, then we ignore any files that match any of
      the ignored_file globs. If the change has no files that are outside of
      the ignored_files globs, then we do not trigger a build.
    includeBuildLogs: If set to INCLUDE_BUILD_LOGS_WITH_STATUS, log url will
      be shown on GitHub page when build status is final. Setting this field
      to INCLUDE_BUILD_LOGS_WITH_STATUS for non GitHub triggers results in
      INVALID_ARGUMENT error.
    includedFiles: If any of the files altered in the commit pass the
      ignored_files filter and included_files is empty, then as far as this
      filter is concerned, we should trigger the build. If any of the files
      altered in the commit pass the ignored_files filter and included_files
      is not empty, then we make sure that at least one of those files matches
      a included_files glob. If not, then we do not trigger a build.
    name: User-assigned name of the trigger. Must be unique within the
      project. Trigger names must meet the following requirements: + They must
      contain only alphanumeric characters and dashes. + They can be 1-64
      characters long. + They must begin and end with an alphanumeric
      character.
    pubsubConfig: PubsubConfig describes the configuration of a trigger that
      creates a build whenever a Pub/Sub message is published.
    repositoryEventConfig: The configuration of a trigger that creates a build
      whenever an event from Repo API is received.
    resourceName: The `Trigger` name with format:
      `projects/{project}/locations/{location}/triggers/{trigger}`, where
      {trigger} is a unique identifier generated by the service.
    serviceAccount: The service account used for all user-controlled
      operations including UpdateBuildTrigger, RunBuildTrigger, CreateBuild,
      and CancelBuild. If no service account is set and the legacy Cloud Build
      service account ([PROJECT_NUM]@cloudbuild.gserviceaccount.com) is the
      default for the project then it will be used instead. Format:
      `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT_ID_OR_EMAIL}`
    sourceToBuild: The repo and ref of the repository from which to build.
      This field is used only for those triggers that do not respond to SCM
      events. Triggers that respond to such events build source at whatever
      commit caused the event. This field is currently only used by Webhook,
      Pub/Sub, Manual, and Cron triggers.
    substitutions: Substitutions for Build resource. The keys must match the
      following regular expression: `^_[A-Z0-9_]+$`.
    tags: Tags for annotation of a `BuildTrigger`
    triggerTemplate: Template describing the types of source changes to
      trigger a build. Branch and tag names in trigger templates are
      interpreted as regular expressions. Any branch or tag change that
      matches that regular expression will trigger a build. Mutually exclusive
      with `github`.
    webhookConfig: WebhookConfig describes the configuration of a trigger that
      creates a build whenever a webhook is sent to a trigger's webhook URL.
  """

  class EventTypeValueValuesEnum(_messages.Enum):
    r"""EventType allows the user to explicitly set the type of event to which
    this BuildTrigger should respond. This field will be validated against the
    rest of the configuration if it is set.

    Values:
      EVENT_TYPE_UNSPECIFIED: EVENT_TYPE_UNSPECIFIED event_types are ignored.
      REPO: REPO corresponds to the supported VCS integrations.
      WEBHOOK: WEBHOOK corresponds to webhook triggers.
      PUBSUB: PUBSUB corresponds to pubsub triggers.
      MANUAL: MANUAL corresponds to manual-only invoked triggers.
    """
    EVENT_TYPE_UNSPECIFIED = 0
    REPO = 1
    WEBHOOK = 2
    PUBSUB = 3
    MANUAL = 4

  class IncludeBuildLogsValueValuesEnum(_messages.Enum):
    r"""If set to INCLUDE_BUILD_LOGS_WITH_STATUS, log url will be shown on
    GitHub page when build status is final. Setting this field to
    INCLUDE_BUILD_LOGS_WITH_STATUS for non GitHub triggers results in
    INVALID_ARGUMENT error.

    Values:
      INCLUDE_BUILD_LOGS_UNSPECIFIED: Build logs will not be shown on GitHub.
      INCLUDE_BUILD_LOGS_WITH_STATUS: Build logs will be shown on GitHub.
    """
    INCLUDE_BUILD_LOGS_UNSPECIFIED = 0
    INCLUDE_BUILD_LOGS_WITH_STATUS = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SubstitutionsValue(_messages.Message):
    r"""Substitutions for Build resource. The keys must match the following
    regular expression: `^_[A-Z0-9_]+$`.

    Messages:
      AdditionalProperty: An additional property for a SubstitutionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type SubstitutionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SubstitutionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  approvalConfig = _messages.MessageField('ApprovalConfig', 1)
  autodetect = _messages.BooleanField(2)
  bitbucketServerTriggerConfig = _messages.MessageField('BitbucketServerTriggerConfig', 3)
  build = _messages.MessageField('Build', 4)
  createTime = _messages.StringField(5)
  cron = _messages.MessageField('CronConfig', 6)
  description = _messages.StringField(7)
  developerConnectEventConfig = _messages.MessageField('DeveloperConnectEventConfig', 8)
  disabled = _messages.BooleanField(9)
  eventType = _messages.EnumField('EventTypeValueValuesEnum', 10)
  filename = _messages.StringField(11)
  filter = _messages.StringField(12)
  gitFileSource = _messages.MessageField('GitFileSource', 13)
  github = _messages.MessageField('GitHubEventsConfig', 14)
  gitlabEnterpriseEventsConfig = _messages.MessageField('GitLabEventsConfig', 15)
  id = _messages.StringField(16)
  ignoredFiles = _messages.StringField(17, repeated=True)
  includeBuildLogs = _messages.EnumField('IncludeBuildLogsValueValuesEnum', 18)
  includedFiles = _messages.StringField(19, repeated=True)
  name = _messages.StringField(20)
  pubsubConfig = _messages.MessageField('PubsubConfig', 21)
  repositoryEventConfig = _messages.MessageField('RepositoryEventConfig', 22)
  resourceName = _messages.StringField(23)
  serviceAccount = _messages.StringField(24)
  sourceToBuild = _messages.MessageField('GitRepoSource', 25)
  substitutions = _messages.MessageField('SubstitutionsValue', 26)
  tags = _messages.StringField(27, repeated=True)
  triggerTemplate = _messages.MessageField('RepoSource', 28)
  webhookConfig = _messages.MessageField('WebhookConfig', 29)


class BuiltImage(_messages.Message):
  r"""An image built by the pipeline.

  Fields:
    digest: Docker Registry 2.0 digest.
    name: Name used to push the container image to Google Container Registry,
      as presented to `docker push`.
    pushTiming: Output only. Stores timing information for pushing the
      specified image.
  """

  digest = _messages.StringField(1)
  name = _messages.StringField(2)
  pushTiming = _messages.MessageField('TimeSpan', 3)


class CancelBuildRequest(_messages.Message):
  r"""Request to cancel an ongoing build.

  Fields:
    id: Required. ID of the build.
    name: The name of the `Build` to cancel. Format:
      `projects/{project}/locations/{location}/builds/{build}`
    projectId: Required. ID of the project.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2)
  projectId = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CloudbuildGithubDotComWebhookReceiveRequest(_messages.Message):
  r"""A CloudbuildGithubDotComWebhookReceiveRequest object.

  Fields:
    httpBody: A HttpBody resource to be passed as the request body.
    webhookKey: For GitHub Enterprise webhooks, this key is used to associate
      the webhook request with the GitHubEnterpriseConfig to use for
      validation.
  """

  httpBody = _messages.MessageField('HttpBody', 1)
  webhookKey = _messages.StringField(2)


class CloudbuildGithubInstallationsInstallationsListRequest(_messages.Message):
  r"""A CloudbuildGithubInstallationsInstallationsListRequest object.

  Fields:
    installationId: Installation ID
  """

  installationId = _messages.IntegerField(1, required=True)


class CloudbuildGithubInstallationsProjectsListRequest(_messages.Message):
  r"""A CloudbuildGithubInstallationsProjectsListRequest object.

  Fields:
    installationId: Installation ID
  """

  installationId = _messages.IntegerField(1, required=True)


class CloudbuildInstallationsInstallationsListRequest(_messages.Message):
  r"""A CloudbuildInstallationsInstallationsListRequest object.

  Fields:
    installationId: Installation ID
  """

  installationId = _messages.IntegerField(1, required=True)


class CloudbuildLocationsRegionalWebhookRequest(_messages.Message):
  r"""A CloudbuildLocationsRegionalWebhookRequest object.

  Fields:
    httpBody: A HttpBody resource to be passed as the request body.
    location: Required. The location where the webhook should be sent.
    webhookKey: For GitHub Enterprise webhooks, this key is used to associate
      the webhook request with the GitHubEnterpriseConfig to use for
      validation.
  """

  httpBody = _messages.MessageField('HttpBody', 1)
  location = _messages.StringField(2, required=True)
  webhookKey = _messages.StringField(3)


class CloudbuildOauthGetRegistrationRequest(_messages.Message):
  r"""A CloudbuildOauthGetRegistrationRequest object.

  Enums:
    NamespaceValueValuesEnum: Required. The namespace that the credential
      belongs to.

  Fields:
    authUser: For users who are logged in using multiple accounts, specify the
      auth user parameter so that the registration url redirects back to the
      cloud console using the proper account.
    csesidx: Optional. For users who use byoid, specify the csesidx parameter
      so that the registration url redirects back to the cloud console using
      the proper account.
    githubEnterpriseConfig: Optional. The full resource name of the github
      enterprise resource if applicable.
    hostUrl: Required. The host url that the oauth credentials are associated
      with. For GitHub, this would be "https://github.com". For
      GitHubEnterprise, this would be the host name of their github enterprise
      instance.
    namespace: Required. The namespace that the credential belongs to.
  """

  class NamespaceValueValuesEnum(_messages.Enum):
    r"""Required. The namespace that the credential belongs to.

    Values:
      NAMESPACE_UNSPECIFIED: The default namespace.
      GITHUB_ENTERPRISE: A credential to be used with GitHub enterprise.
    """
    NAMESPACE_UNSPECIFIED = 0
    GITHUB_ENTERPRISE = 1

  authUser = _messages.StringField(1)
  csesidx = _messages.StringField(2)
  githubEnterpriseConfig = _messages.StringField(3)
  hostUrl = _messages.StringField(4)
  namespace = _messages.EnumField('NamespaceValueValuesEnum', 5)


class CloudbuildOauthProcessOAuthCallbackRequest(_messages.Message):
  r"""A CloudbuildOauthProcessOAuthCallbackRequest object.

  Fields:
    code: GitHub generated temproary authorization code.
    githubEnterpriseConfig: For github enterprise, the full resource name of
      the github enterprise resource.
    hostUrl: The host url of the site that the OAuth token is issued for.
    namespace: The namespace that the oauth callback credential should be
      processed for. This should map to the string name of the enum defined in
      the GetOAuthRegistrationURLRequest.
    state: The XSRF token that was sent as part of the initial request to
      start the OAuth flow.
  """

  code = _messages.StringField(1)
  githubEnterpriseConfig = _messages.StringField(2)
  hostUrl = _messages.StringField(3)
  namespace = _messages.StringField(4)
  state = _messages.StringField(5)


class CloudbuildOperationsCancelRequest(_messages.Message):
  r"""A CloudbuildOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudbuildOperationsGetRequest(_messages.Message):
  r"""A CloudbuildOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsBuildsApproveRequest(_messages.Message):
  r"""A CloudbuildProjectsBuildsApproveRequest object.

  Fields:
    approveBuildRequest: A ApproveBuildRequest resource to be passed as the
      request body.
    name: Required. Name of the target build. For example:
      "projects/{$project_id}/builds/{$build_id}"
  """

  approveBuildRequest = _messages.MessageField('ApproveBuildRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudbuildProjectsBuildsCancelRequest(_messages.Message):
  r"""A CloudbuildProjectsBuildsCancelRequest object.

  Fields:
    cancelBuildRequest: A CancelBuildRequest resource to be passed as the
      request body.
    id: Required. ID of the build.
    projectId: Required. ID of the project.
  """

  cancelBuildRequest = _messages.MessageField('CancelBuildRequest', 1)
  id = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)


class CloudbuildProjectsBuildsCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsBuildsCreateRequest object.

  Fields:
    build: A Build resource to be passed as the request body.
    parent: The parent resource where this build will be created. Format:
      `projects/{project}/locations/{location}`
    projectId: Required. ID of the project.
  """

  build = _messages.MessageField('Build', 1)
  parent = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)


class CloudbuildProjectsBuildsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsBuildsGetRequest object.

  Fields:
    id: Required. ID of the build.
    name: The name of the `Build` to retrieve. Format:
      `projects/{project}/locations/{location}/builds/{build}`
    projectId: Required. ID of the project.
  """

  id = _messages.StringField(1, required=True)
  name = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)


class CloudbuildProjectsBuildsListRequest(_messages.Message):
  r"""A CloudbuildProjectsBuildsListRequest object.

  Fields:
    filter: The raw filter text to constrain the results.
    pageSize: Number of results to return in the list.
    pageToken: The page token for the next page of Builds. If unspecified, the
      first page of results is returned. If the token is rejected for any
      reason, INVALID_ARGUMENT will be thrown. In this case, the token should
      be discarded, and pagination should be restarted from the first page of
      results. See https://google.aip.dev/158 for more.
    parent: The parent of the collection of `Builds`. Format:
      `projects/{project}/locations/{location}`
    projectId: Required. ID of the project.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4)
  projectId = _messages.StringField(5, required=True)


class CloudbuildProjectsGithubEnterpriseConfigsCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsGithubEnterpriseConfigsCreateRequest object.

  Fields:
    gheConfigId: Optional. The ID to use for the GithubEnterpriseConfig, which
      will become the final component of the GithubEnterpriseConfig's resource
      name. ghe_config_id must meet the following requirements: + They must
      contain only alphanumeric characters and dashes. + They can be 1-64
      characters long. + They must begin and end with an alphanumeric
      character
    gitHubEnterpriseConfig: A GitHubEnterpriseConfig resource to be passed as
      the request body.
    parent: Name of the parent project. For example:
      projects/{$project_number} or projects/{$project_id}
    projectId: ID of the project.
  """

  gheConfigId = _messages.StringField(1)
  gitHubEnterpriseConfig = _messages.MessageField('GitHubEnterpriseConfig', 2)
  parent = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4)


class CloudbuildProjectsGithubEnterpriseConfigsDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsGithubEnterpriseConfigsDeleteRequest object.

  Fields:
    configId: Unique identifier of the `GitHubEnterpriseConfig`
    name: This field should contain the name of the enterprise config
      resource. For example: "projects/{$project_id}/locations/{$location_id}/
      githubEnterpriseConfigs/{$config_id}"
    projectId: ID of the project
  """

  configId = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3)


class CloudbuildProjectsGithubEnterpriseConfigsGetAppRequest(_messages.Message):
  r"""A CloudbuildProjectsGithubEnterpriseConfigsGetAppRequest object.

  Fields:
    enterpriseConfigResource: Required. The name of the enterprise config
      resource associated with the GitHub App. For example: "projects/{$projec
      t_id}/locations/{location_id}/githubEnterpriseConfigs/{$config_id}"
  """

  enterpriseConfigResource = _messages.StringField(1, required=True)


class CloudbuildProjectsGithubEnterpriseConfigsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsGithubEnterpriseConfigsGetRequest object.

  Fields:
    configId: Unique identifier of the `GitHubEnterpriseConfig`
    name: This field should contain the name of the enterprise config
      resource. For example: "projects/{$project_id}/locations/{$location_id}/
      githubEnterpriseConfigs/{$config_id}"
    projectId: ID of the project
  """

  configId = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3)


class CloudbuildProjectsGithubEnterpriseConfigsListRequest(_messages.Message):
  r"""A CloudbuildProjectsGithubEnterpriseConfigsListRequest object.

  Fields:
    parent: Name of the parent project. For example:
      projects/{$project_number} or projects/{$project_id}
    projectId: ID of the project
  """

  parent = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2)


class CloudbuildProjectsGithubEnterpriseConfigsPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsGithubEnterpriseConfigsPatchRequest object.

  Fields:
    gitHubEnterpriseConfig: A GitHubEnterpriseConfig resource to be passed as
      the request body.
    name: The full resource name for the GitHubEnterpriseConfig For example: "
      projects/{$project_id}/locations/{$location_id}/githubEnterpriseConfigs/
      {$config_id}"
    updateMask: Update mask for the resource. If this is set, the server will
      only update the fields specified in the field mask. Otherwise, a full
      update of the mutable resource fields will be performed.
  """

  gitHubEnterpriseConfig = _messages.MessageField('GitHubEnterpriseConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudbuildProjectsGithubInstallationsCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsGithubInstallationsCreateRequest object.

  Fields:
    installation: A Installation resource to be passed as the request body.
    parent: The parent resource where this github installation will be
      created. Format: `projects/{project}/locations/{location}`
    projectId: The project ID of the GCP project the installation is
      associated with.
    projectId1: ID of the project.
    userOauthCode: GitHub user code. If a GitHub credential is already
      associated with the user this can be omitted, else the code is used to
      exchange and store an OAuth token.
  """

  installation = _messages.MessageField('Installation', 1)
  parent = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)
  projectId1 = _messages.StringField(4)
  userOauthCode = _messages.StringField(5)


class CloudbuildProjectsGithubInstallationsDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsGithubInstallationsDeleteRequest object.

  Fields:
    installationId: GitHub app installation ID.
    name: The name of the `GitHubInstallation` to delete. Format:
      `projects/{project}/locations/{location}/installations/{installation}`
    projectId: Cloud Project ID.
  """

  installationId = _messages.IntegerField(1, required=True)
  name = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)


class CloudbuildProjectsGithubInstallationsListRequest(_messages.Message):
  r"""A CloudbuildProjectsGithubInstallationsListRequest object.

  Fields:
    parent: The parent resource where github installations for project will be
      listed. Format: `projects/{project}/locations/{location}`
    projectId: Project id
  """

  parent = _messages.StringField(1)
  projectId = _messages.StringField(2, required=True)


class CloudbuildProjectsGithubInstallationsPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsGithubInstallationsPatchRequest object.

  Fields:
    id: GitHub installation ID, created by GitHub.
    installation: A Installation resource to be passed as the request body.
    installationId: Unique identifier of the GitHub installation. Deprecated.
      Should set installation.id
    name: The name of the `GitHubInstallation` to update. Format:
      `projects/{project}/locations/{location}/installations/{installation}`
    projectId: The project ID of the GCP project the installation is
      associated with.
    projectId1: ID of the project.
    updateMask: Update mask for the Installation resource. If this is set, the
      server will only update the fields specified in the field mask.
      Otherwise, a full update of the resource will be performed.
  """

  id = _messages.IntegerField(1, required=True)
  installation = _messages.MessageField('Installation', 2)
  installationId = _messages.IntegerField(3)
  name = _messages.StringField(4)
  projectId = _messages.StringField(5, required=True)
  projectId1 = _messages.StringField(6)
  updateMask = _messages.StringField(7)


class CloudbuildProjectsInstallationsCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsInstallationsCreateRequest object.

  Fields:
    installation: A Installation resource to be passed as the request body.
    parent: The parent resource where this github installation will be
      created. Format: `projects/{project}/locations/{location}`
    projectId: ID of the project.
    userOauthCode: GitHub user code. If a GitHub credential is already
      associated with the user this can be omitted, else the code is used to
      exchange and store an OAuth token.
  """

  installation = _messages.MessageField('Installation', 1)
  parent = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)
  userOauthCode = _messages.StringField(4)


class CloudbuildProjectsInstallationsDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsInstallationsDeleteRequest object.

  Fields:
    installationId: GitHub app installation ID.
    name: The name of the `GitHubInstallation` to delete. Format:
      `projects/{project}/locations/{location}/installations/{installation}`
    projectId: Cloud Project ID.
  """

  installationId = _messages.IntegerField(1, required=True)
  name = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)


class CloudbuildProjectsInstallationsListRequest(_messages.Message):
  r"""A CloudbuildProjectsInstallationsListRequest object.

  Fields:
    parent: The parent resource where github installations for project will be
      listed. Format: `projects/{project}/locations/{location}`
    projectId: Project id
  """

  parent = _messages.StringField(1)
  projectId = _messages.StringField(2, required=True)


class CloudbuildProjectsInstallationsPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsInstallationsPatchRequest object.

  Fields:
    id: GitHub installation ID, created by GitHub.
    installation: A Installation resource to be passed as the request body.
    installationId: Unique identifier of the GitHub installation. Deprecated.
      Should set installation.id
    name: The name of the `GitHubInstallation` to update. Format:
      `projects/{project}/locations/{location}/installations/{installation}`
    projectId: ID of the project.
    projectNum: Numerical ID of the project.
    updateMask: Update mask for the Installation resource. If this is set, the
      server will only update the fields specified in the field mask.
      Otherwise, a full update of the resource will be performed.
  """

  id = _messages.IntegerField(1, required=True)
  installation = _messages.MessageField('Installation', 2)
  installationId = _messages.IntegerField(3)
  name = _messages.StringField(4)
  projectId = _messages.StringField(5)
  projectNum = _messages.IntegerField(6, required=True)
  updateMask = _messages.StringField(7)


class CloudbuildProjectsLocationsBitbucketServerConfigsConnectedRepositoriesBatchCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsBitbucketServerConfigsConnectedRepositories
  BatchCreateRequest object.

  Fields:
    batchCreateBitbucketServerConnectedRepositoriesRequest: A
      BatchCreateBitbucketServerConnectedRepositoriesRequest resource to be
      passed as the request body.
    parent: The name of the `BitbucketServerConfig` that added connected
      repository. Format: `projects/{project}/locations/{location}/bitbucketSe
      rverConfigs/{config}`
  """

  batchCreateBitbucketServerConnectedRepositoriesRequest = _messages.MessageField('BatchCreateBitbucketServerConnectedRepositoriesRequest', 1)
  parent = _messages.StringField(2, required=True)


class CloudbuildProjectsLocationsBitbucketServerConfigsCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsBitbucketServerConfigsCreateRequest object.

  Fields:
    bitbucketServerConfig: A BitbucketServerConfig resource to be passed as
      the request body.
    bitbucketServerConfigId: Optional. The ID to use for the
      BitbucketServerConfig, which will become the final component of the
      BitbucketServerConfig's resource name. bitbucket_server_config_id must
      meet the following requirements: + They must contain only alphanumeric
      characters and dashes. + They can be 1-64 characters long. + They must
      begin and end with an alphanumeric character.
    parent: Required. Name of the parent resource.
  """

  bitbucketServerConfig = _messages.MessageField('BitbucketServerConfig', 1)
  bitbucketServerConfigId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudbuildProjectsLocationsBitbucketServerConfigsDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsBitbucketServerConfigsDeleteRequest object.

  Fields:
    name: Required. The config resource name.
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsBitbucketServerConfigsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsBitbucketServerConfigsGetRequest object.

  Fields:
    name: Required. The config resource name.
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsBitbucketServerConfigsListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsBitbucketServerConfigsListRequest object.

  Fields:
    pageSize: The maximum number of configs to return. The service may return
      fewer than this value. If unspecified, at most 50 configs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous
      `ListBitbucketServerConfigsRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListBitbucketServerConfigsRequest` must match the call that provided
      the page token.
    parent: Required. Name of the parent resource.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudbuildProjectsLocationsBitbucketServerConfigsPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsBitbucketServerConfigsPatchRequest object.

  Fields:
    bitbucketServerConfig: A BitbucketServerConfig resource to be passed as
      the request body.
    name: The resource name for the config.
    updateMask: Update mask for the resource. If this is set, the server will
      only update the fields specified in the field mask. Otherwise, a full
      update of the mutable resource fields will be performed.
  """

  bitbucketServerConfig = _messages.MessageField('BitbucketServerConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudbuildProjectsLocationsBitbucketServerConfigsRemoveBitbucketServerConnectedRepositoryRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsBitbucketServerConfigsRemoveBitbucketServer
  ConnectedRepositoryRequest object.

  Fields:
    config: Required. The name of the `BitbucketServerConfig` to remove a
      connected repository. Format: `projects/{project}/locations/{location}/b
      itbucketServerConfigs/{config}`
    removeBitbucketServerConnectedRepositoryRequest: A
      RemoveBitbucketServerConnectedRepositoryRequest resource to be passed as
      the request body.
  """

  config = _messages.StringField(1, required=True)
  removeBitbucketServerConnectedRepositoryRequest = _messages.MessageField('RemoveBitbucketServerConnectedRepositoryRequest', 2)


class CloudbuildProjectsLocationsBitbucketServerConfigsReposListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsBitbucketServerConfigsReposListRequest
  object.

  Fields:
    pageSize: The maximum number of configs to return. The service may return
      fewer than this value. The maximum value is 1000; values above 1000 will
      be coerced to 1000.
    pageToken: A page token, received from a previous
      `ListBitbucketServerRepositoriesRequest` call. Provide this to retrieve
      the subsequent page. When paginating, all other parameters provided to
      `ListBitbucketServerConfigsRequest` must match the call that provided
      the page token.
    parent: Required. Name of the parent resource.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudbuildProjectsLocationsBuildsApproveRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsBuildsApproveRequest object.

  Fields:
    approveBuildRequest: A ApproveBuildRequest resource to be passed as the
      request body.
    name: Required. Name of the target build. For example:
      "projects/{$project_id}/builds/{$build_id}"
  """

  approveBuildRequest = _messages.MessageField('ApproveBuildRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudbuildProjectsLocationsBuildsCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsBuildsCreateRequest object.

  Fields:
    build: A Build resource to be passed as the request body.
    parent: The parent resource where this build will be created. Format:
      `projects/{project}/locations/{location}`
    projectId: Required. ID of the project.
  """

  build = _messages.MessageField('Build', 1)
  parent = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3)


class CloudbuildProjectsLocationsBuildsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsBuildsGetRequest object.

  Fields:
    id: Required. ID of the build.
    name: The name of the `Build` to retrieve. Format:
      `projects/{project}/locations/{location}/builds/{build}`
    projectId: Required. ID of the project.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3)


class CloudbuildProjectsLocationsBuildsListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsBuildsListRequest object.

  Fields:
    filter: The raw filter text to constrain the results.
    pageSize: Number of results to return in the list.
    pageToken: The page token for the next page of Builds. If unspecified, the
      first page of results is returned. If the token is rejected for any
      reason, INVALID_ARGUMENT will be thrown. In this case, the token should
      be discarded, and pagination should be restarted from the first page of
      results. See https://google.aip.dev/158 for more.
    parent: The parent of the collection of `Builds`. Format:
      `projects/{project}/locations/{location}`
    projectId: Required. ID of the project.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  projectId = _messages.StringField(5)


class CloudbuildProjectsLocationsGetDefaultServiceAccountRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGetDefaultServiceAccountRequest object.

  Fields:
    name: Required. The name of the `DefaultServiceAccount` to retrieve.
      Format: `projects/{project}/locations/{location}/defaultServiceAccount`
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsGitLabConfigsConnectedRepositoriesBatchCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGitLabConfigsConnectedRepositoriesBatchCrea
  teRequest object.

  Fields:
    batchCreateGitLabConnectedRepositoriesRequest: A
      BatchCreateGitLabConnectedRepositoriesRequest resource to be passed as
      the request body.
    parent: The name of the `GitLabConfig` that adds connected repositories.
      Format: `projects/{project}/locations/{location}/gitLabConfigs/{config}`
  """

  batchCreateGitLabConnectedRepositoriesRequest = _messages.MessageField('BatchCreateGitLabConnectedRepositoriesRequest', 1)
  parent = _messages.StringField(2, required=True)


class CloudbuildProjectsLocationsGitLabConfigsCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGitLabConfigsCreateRequest object.

  Fields:
    gitLabConfig: A GitLabConfig resource to be passed as the request body.
    gitlabConfigId: Optional. The ID to use for the GitLabConfig, which will
      become the final component of the GitLabConfig's resource name.
      gitlab_config_id must meet the following requirements: + They must
      contain only alphanumeric characters and dashes. + They can be 1-64
      characters long. + They must begin and end with an alphanumeric
      character
    parent: Required. Name of the parent resource.
  """

  gitLabConfig = _messages.MessageField('GitLabConfig', 1)
  gitlabConfigId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudbuildProjectsLocationsGitLabConfigsDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGitLabConfigsDeleteRequest object.

  Fields:
    name: Required. The config resource name.
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsGitLabConfigsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGitLabConfigsGetRequest object.

  Fields:
    name: Required. The config resource name.
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsGitLabConfigsListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGitLabConfigsListRequest object.

  Fields:
    pageSize: The maximum number of configs to return. The service may return
      fewer than this value. If unspecified, at most 50 configs will be
      returned. The maximum value is 1000;, values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous
      'ListGitlabConfigsRequest' call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      'ListGitlabConfigsRequest' must match the call that provided the page
      token.
    parent: Required. Name of the parent resource
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudbuildProjectsLocationsGitLabConfigsPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGitLabConfigsPatchRequest object.

  Fields:
    gitLabConfig: A GitLabConfig resource to be passed as the request body.
    name: The resource name for the config.
    updateMask: Update mask for the resource. If this is set, the server will
      only update the fields specified in the field mask. Otherwise, a full
      update of the mutable resource fields will be performed.
  """

  gitLabConfig = _messages.MessageField('GitLabConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudbuildProjectsLocationsGitLabConfigsRemoveGitLabConnectedRepositoryRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGitLabConfigsRemoveGitLabConnectedRepositor
  yRequest object.

  Fields:
    config: Required. The name of the `GitLabConfig` to remove a connected
      repository. Format:
      `projects/{project}/locations/{location}/gitLabConfigs/{config}`
    removeGitLabConnectedRepositoryRequest: A
      RemoveGitLabConnectedRepositoryRequest resource to be passed as the
      request body.
  """

  config = _messages.StringField(1, required=True)
  removeGitLabConnectedRepositoryRequest = _messages.MessageField('RemoveGitLabConnectedRepositoryRequest', 2)


class CloudbuildProjectsLocationsGitLabConfigsReposListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGitLabConfigsReposListRequest object.

  Fields:
    pageSize: The maximum number of repositories to return. The service may
      return fewer than this value.
    pageToken: A page token, received from a previous
      ListGitLabRepositoriesRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListGitLabRepositoriesRequest` must match the call that provided the
      page token.
    parent: Required. Name of the parent resource.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudbuildProjectsLocationsGithubEnterpriseConfigsCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGithubEnterpriseConfigsCreateRequest
  object.

  Fields:
    gheConfigId: Optional. The ID to use for the GithubEnterpriseConfig, which
      will become the final component of the GithubEnterpriseConfig's resource
      name. ghe_config_id must meet the following requirements: + They must
      contain only alphanumeric characters and dashes. + They can be 1-64
      characters long. + They must begin and end with an alphanumeric
      character
    gitHubEnterpriseConfig: A GitHubEnterpriseConfig resource to be passed as
      the request body.
    parent: Name of the parent project. For example:
      projects/{$project_number} or projects/{$project_id}
    projectId: ID of the project.
  """

  gheConfigId = _messages.StringField(1)
  gitHubEnterpriseConfig = _messages.MessageField('GitHubEnterpriseConfig', 2)
  parent = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4)


class CloudbuildProjectsLocationsGithubEnterpriseConfigsDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGithubEnterpriseConfigsDeleteRequest
  object.

  Fields:
    configId: Unique identifier of the `GitHubEnterpriseConfig`
    name: This field should contain the name of the enterprise config
      resource. For example: "projects/{$project_id}/locations/{$location_id}/
      githubEnterpriseConfigs/{$config_id}"
    projectId: ID of the project
  """

  configId = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3)


class CloudbuildProjectsLocationsGithubEnterpriseConfigsGetAppRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGithubEnterpriseConfigsGetAppRequest
  object.

  Fields:
    enterpriseConfigResource: Required. The name of the enterprise config
      resource associated with the GitHub App. For example: "projects/{$projec
      t_id}/locations/{location_id}/githubEnterpriseConfigs/{$config_id}"
  """

  enterpriseConfigResource = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsGithubEnterpriseConfigsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGithubEnterpriseConfigsGetRequest object.

  Fields:
    configId: Unique identifier of the `GitHubEnterpriseConfig`
    name: This field should contain the name of the enterprise config
      resource. For example: "projects/{$project_id}/locations/{$location_id}/
      githubEnterpriseConfigs/{$config_id}"
    projectId: ID of the project
  """

  configId = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3)


class CloudbuildProjectsLocationsGithubEnterpriseConfigsListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGithubEnterpriseConfigsListRequest object.

  Fields:
    parent: Name of the parent project. For example:
      projects/{$project_number} or projects/{$project_id}
    projectId: ID of the project
  """

  parent = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2)


class CloudbuildProjectsLocationsGithubEnterpriseConfigsPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGithubEnterpriseConfigsPatchRequest object.

  Fields:
    gitHubEnterpriseConfig: A GitHubEnterpriseConfig resource to be passed as
      the request body.
    name: The full resource name for the GitHubEnterpriseConfig For example: "
      projects/{$project_id}/locations/{$location_id}/githubEnterpriseConfigs/
      {$config_id}"
    updateMask: Update mask for the resource. If this is set, the server will
      only update the fields specified in the field mask. Otherwise, a full
      update of the mutable resource fields will be performed.
  """

  gitHubEnterpriseConfig = _messages.MessageField('GitHubEnterpriseConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudbuildProjectsLocationsGithubInstallationsCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGithubInstallationsCreateRequest object.

  Fields:
    installation: A Installation resource to be passed as the request body.
    parent: The parent resource where this github installation will be
      created. Format: `projects/{project}/locations/{location}`
    projectId: ID of the project.
    userOauthCode: GitHub user code. If a GitHub credential is already
      associated with the user this can be omitted, else the code is used to
      exchange and store an OAuth token.
  """

  installation = _messages.MessageField('Installation', 1)
  parent = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3)
  userOauthCode = _messages.StringField(4)


class CloudbuildProjectsLocationsGithubInstallationsDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGithubInstallationsDeleteRequest object.

  Fields:
    installationId: GitHub app installation ID.
    installationsId: A string attribute.
    name: The name of the `GitHubInstallation` to delete. Format:
      `projects/{project}/locations/{location}/installations/{installation}`
    projectId: Cloud Project ID.
  """

  installationId = _messages.IntegerField(1)
  installationsId = _messages.StringField(2, required=True)
  name = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4)


class CloudbuildProjectsLocationsGithubInstallationsListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGithubInstallationsListRequest object.

  Fields:
    parent: The parent resource where github installations for project will be
      listed. Format: `projects/{project}/locations/{location}`
    projectId: Project id
  """

  parent = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2)


class CloudbuildProjectsLocationsGithubInstallationsPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsGithubInstallationsPatchRequest object.

  Fields:
    installation: A Installation resource to be passed as the request body.
    installationId: Unique identifier of the GitHub installation. Deprecated.
      Should set installation.id
    installationsId: A string attribute.
    name: The `Installation` name with format:
      `projects/{project}/locations/{location}/installations/{installation}`,
      where {installation} is GitHub installation ID created by GitHub.
    name1: The name of the `GitHubInstallation` to update. Format:
      `projects/{project}/locations/{location}/installations/{installation}`
    projectId: ID of the project.
    updateMask: Update mask for the Installation resource. If this is set, the
      server will only update the fields specified in the field mask.
      Otherwise, a full update of the resource will be performed.
  """

  installation = _messages.MessageField('Installation', 1)
  installationId = _messages.IntegerField(2)
  installationsId = _messages.StringField(3, required=True)
  name = _messages.StringField(4, required=True)
  name1 = _messages.StringField(5)
  projectId = _messages.StringField(6)
  updateMask = _messages.StringField(7)


class CloudbuildProjectsLocationsInstallationsDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsInstallationsDeleteRequest object.

  Fields:
    installationId: GitHub app installation ID.
    name: The name of the `GitHubInstallation` to delete. Format:
      `projects/{project}/locations/{location}/installations/{installation}`
    projectId: Cloud Project ID.
  """

  installationId = _messages.IntegerField(1)
  name = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3)


class CloudbuildProjectsLocationsInstallationsListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsInstallationsListRequest object.

  Fields:
    parent: The parent resource where github installations for project will be
      listed. Format: `projects/{project}/locations/{location}`
    projectId: Project id
  """

  parent = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2)


class CloudbuildProjectsLocationsInstallationsPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsInstallationsPatchRequest object.

  Fields:
    installation: A Installation resource to be passed as the request body.
    installationId: Unique identifier of the GitHub installation. Deprecated.
      Should set installation.id
    name: The `Installation` name with format:
      `projects/{project}/locations/{location}/installations/{installation}`,
      where {installation} is GitHub installation ID created by GitHub.
    name1: The name of the `GitHubInstallation` to update. Format:
      `projects/{project}/locations/{location}/installations/{installation}`
    projectId: ID of the project.
    updateMask: Update mask for the Installation resource. If this is set, the
      server will only update the fields specified in the field mask.
      Otherwise, a full update of the resource will be performed.
  """

  installation = _messages.MessageField('Installation', 1)
  installationId = _messages.IntegerField(2)
  name = _messages.StringField(3, required=True)
  name1 = _messages.StringField(4)
  projectId = _messages.StringField(5)
  updateMask = _messages.StringField(6)


class CloudbuildProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudbuildProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsTriggersCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsTriggersCreateRequest object.

  Fields:
    buildTrigger: A BuildTrigger resource to be passed as the request body.
    parent: The parent resource where this trigger will be created. Format:
      `projects/{project}/locations/{location}`
    projectId: Required. ID of the project for which to configure automatic
      builds.
  """

  buildTrigger = _messages.MessageField('BuildTrigger', 1)
  parent = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3)


class CloudbuildProjectsLocationsTriggersDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsTriggersDeleteRequest object.

  Fields:
    name: The name of the `Trigger` to delete. Format:
      `projects/{project}/locations/{location}/triggers/{trigger}`
    projectId: Required. ID of the project that owns the trigger.
    triggerId: Required. ID of the `BuildTrigger` to delete.
  """

  name = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2)
  triggerId = _messages.StringField(3)


class CloudbuildProjectsLocationsTriggersGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsTriggersGetRequest object.

  Fields:
    name: The name of the `Trigger` to retrieve. Format:
      `projects/{project}/locations/{location}/triggers/{trigger}`
    projectId: Required. ID of the project that owns the trigger.
    triggerId: Required. Identifier (`id` or `name`) of the `BuildTrigger` to
      get.
  """

  name = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2)
  triggerId = _messages.StringField(3)


class CloudbuildProjectsLocationsTriggersListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsTriggersListRequest object.

  Fields:
    pageSize: Number of results to return in the list.
    pageToken: Token to provide to skip to a particular spot in the list.
    parent: The parent of the collection of `Triggers`. Format:
      `projects/{project}/locations/{location}`
    projectId: Required. ID of the project for which to list BuildTriggers.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4)


class CloudbuildProjectsLocationsTriggersPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsTriggersPatchRequest object.

  Fields:
    buildTrigger: A BuildTrigger resource to be passed as the request body.
    projectId: Required. ID of the project that owns the trigger.
    resourceName: The `Trigger` name with format:
      `projects/{project}/locations/{location}/triggers/{trigger}`, where
      {trigger} is a unique identifier generated by the service.
    triggerId: Required. ID of the `BuildTrigger` to update.
    updateMask: Update mask for the resource. If this is set, the server will
      only update the fields specified in the field mask. Otherwise, a full
      update of the mutable resource fields will be performed.
  """

  buildTrigger = _messages.MessageField('BuildTrigger', 1)
  projectId = _messages.StringField(2)
  resourceName = _messages.StringField(3, required=True)
  triggerId = _messages.StringField(4)
  updateMask = _messages.StringField(5)


class CloudbuildProjectsLocationsTriggersRunRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsTriggersRunRequest object.

  Fields:
    name: The name of the `Trigger` to run. Format:
      `projects/{project}/locations/{location}/triggers/{trigger}`
    runBuildTriggerRequest: A RunBuildTriggerRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  runBuildTriggerRequest = _messages.MessageField('RunBuildTriggerRequest', 2)


class CloudbuildProjectsLocationsTriggersWebhookRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsTriggersWebhookRequest object.

  Fields:
    httpBody: A HttpBody resource to be passed as the request body.
    name: The name of the `ReceiveTriggerWebhook` to retrieve. Format:
      `projects/{project}/locations/{location}/triggers/{trigger}`
    projectId: Project in which the specified trigger lives
    secret: Secret token used for authorization if an OAuth token isn't
      provided.
    trigger: Name of the trigger to run the payload against
  """

  httpBody = _messages.MessageField('HttpBody', 1)
  name = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3)
  secret = _messages.StringField(4)
  trigger = _messages.StringField(5)


class CloudbuildProjectsLocationsWorkerPoolsCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkerPoolsCreateRequest object.

  Fields:
    parent: Required. The parent resource where this worker pool will be
      created. Format: `projects/{project}/locations/{location}`.
    validateOnly: If set, validate the request and preview the response, but
      do not actually post it.
    workerPool: A WorkerPool resource to be passed as the request body.
    workerPoolId: Required. Immutable. The ID to use for the `WorkerPool`,
      which will become the final component of the resource name. This value
      should be 1-63 characters, and valid characters are /a-z-/.
  """

  parent = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)
  workerPool = _messages.MessageField('WorkerPool', 3)
  workerPoolId = _messages.StringField(4)


class CloudbuildProjectsLocationsWorkerPoolsDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkerPoolsDeleteRequest object.

  Fields:
    allowMissing: If set to true, and the `WorkerPool` is not found, the
      request will succeed but no action will be taken on the server.
    etag: Optional. If provided, it must match the server's etag on the
      workerpool for the request to be processed.
    name: Required. The name of the `WorkerPool` to delete. Format:
      `projects/{project}/locations/{location}/workerPools/{workerPool}`.
    validateOnly: If set, validate the request and preview the response, but
      do not actually post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class CloudbuildProjectsLocationsWorkerPoolsGetRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkerPoolsGetRequest object.

  Fields:
    name: Required. The name of the `WorkerPool` to retrieve. Format:
      `projects/{project}/locations/{location}/workerPools/{workerPool}`.
  """

  name = _messages.StringField(1, required=True)


class CloudbuildProjectsLocationsWorkerPoolsListRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkerPoolsListRequest object.

  Fields:
    pageSize: The maximum number of `WorkerPool`s to return. The service may
      return fewer than this value. If omitted, the server will use a sensible
      default.
    pageToken: A page token, received from a previous `ListWorkerPools` call.
      Provide this to retrieve the subsequent page.
    parent: Required. The parent of the collection of `WorkerPools`. Format:
      `projects/{project}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudbuildProjectsLocationsWorkerPoolsPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsLocationsWorkerPoolsPatchRequest object.

  Fields:
    name: Output only. The resource name of the `WorkerPool`, with format
      `projects/{project}/locations/{location}/workerPools/{worker_pool}`. The
      value of `{worker_pool}` is provided by `worker_pool_id` in
      `CreateWorkerPool` request and the value of `{location}` is determined
      by the endpoint accessed.
    updateMask: Optional. A mask specifying which fields in `worker_pool` to
      update.
    validateOnly: If set, validate the request and preview the response, but
      do not actually post it.
    workerPool: A WorkerPool resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)
  workerPool = _messages.MessageField('WorkerPool', 4)


class CloudbuildProjectsTriggersCreateRequest(_messages.Message):
  r"""A CloudbuildProjectsTriggersCreateRequest object.

  Fields:
    buildTrigger: A BuildTrigger resource to be passed as the request body.
    parent: The parent resource where this trigger will be created. Format:
      `projects/{project}/locations/{location}`
    projectId: Required. ID of the project for which to configure automatic
      builds.
  """

  buildTrigger = _messages.MessageField('BuildTrigger', 1)
  parent = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)


class CloudbuildProjectsTriggersDeleteRequest(_messages.Message):
  r"""A CloudbuildProjectsTriggersDeleteRequest object.

  Fields:
    name: The name of the `Trigger` to delete. Format:
      `projects/{project}/locations/{location}/triggers/{trigger}`
    projectId: Required. ID of the project that owns the trigger.
    triggerId: Required. ID of the `BuildTrigger` to delete.
  """

  name = _messages.StringField(1)
  projectId = _messages.StringField(2, required=True)
  triggerId = _messages.StringField(3, required=True)


class CloudbuildProjectsTriggersGetRequest(_messages.Message):
  r"""A CloudbuildProjectsTriggersGetRequest object.

  Fields:
    name: The name of the `Trigger` to retrieve. Format:
      `projects/{project}/locations/{location}/triggers/{trigger}`
    projectId: Required. ID of the project that owns the trigger.
    triggerId: Required. Identifier (`id` or `name`) of the `BuildTrigger` to
      get.
  """

  name = _messages.StringField(1)
  projectId = _messages.StringField(2, required=True)
  triggerId = _messages.StringField(3, required=True)


class CloudbuildProjectsTriggersListRequest(_messages.Message):
  r"""A CloudbuildProjectsTriggersListRequest object.

  Fields:
    pageSize: Number of results to return in the list.
    pageToken: Token to provide to skip to a particular spot in the list.
    parent: The parent of the collection of `Triggers`. Format:
      `projects/{project}/locations/{location}`
    projectId: Required. ID of the project for which to list BuildTriggers.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)
  projectId = _messages.StringField(4, required=True)


class CloudbuildProjectsTriggersPatchRequest(_messages.Message):
  r"""A CloudbuildProjectsTriggersPatchRequest object.

  Fields:
    buildTrigger: A BuildTrigger resource to be passed as the request body.
    projectId: Required. ID of the project that owns the trigger.
    triggerId: Required. ID of the `BuildTrigger` to update.
    updateMask: Update mask for the resource. If this is set, the server will
      only update the fields specified in the field mask. Otherwise, a full
      update of the mutable resource fields will be performed.
  """

  buildTrigger = _messages.MessageField('BuildTrigger', 1)
  projectId = _messages.StringField(2, required=True)
  triggerId = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)


class CloudbuildProjectsTriggersRunRequest(_messages.Message):
  r"""A CloudbuildProjectsTriggersRunRequest object.

  Fields:
    name: The name of the `Trigger` to run. Format:
      `projects/{project}/locations/{location}/triggers/{trigger}`
    projectId: Required. ID of the project.
    repoSource: A RepoSource resource to be passed as the request body.
    triggerId: Required. ID of the trigger.
  """

  name = _messages.StringField(1)
  projectId = _messages.StringField(2, required=True)
  repoSource = _messages.MessageField('RepoSource', 3)
  triggerId = _messages.StringField(4, required=True)


class CloudbuildProjectsTriggersWebhookRequest(_messages.Message):
  r"""A CloudbuildProjectsTriggersWebhookRequest object.

  Fields:
    httpBody: A HttpBody resource to be passed as the request body.
    name: The name of the `ReceiveTriggerWebhook` to retrieve. Format:
      `projects/{project}/locations/{location}/triggers/{trigger}`
    projectId: Project in which the specified trigger lives
    secret: Secret token used for authorization if an OAuth token isn't
      provided.
    trigger: Name of the trigger to run the payload against
  """

  httpBody = _messages.MessageField('HttpBody', 1)
  name = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)
  secret = _messages.StringField(4)
  trigger = _messages.StringField(5, required=True)


class CloudbuildWebhookRequest(_messages.Message):
  r"""A CloudbuildWebhookRequest object.

  Fields:
    httpBody: A HttpBody resource to be passed as the request body.
    webhookKey: For GitHub Enterprise webhooks, this key is used to associate
      the webhook request with the GitHubEnterpriseConfig to use for
      validation.
  """

  httpBody = _messages.MessageField('HttpBody', 1)
  webhookKey = _messages.StringField(2)


class ClusterOptions(_messages.Message):
  r"""Details of the GKE Cluster for builds that should execute on-cluster.

  Fields:
    name: Identifier of the GKE Cluster this build should execute on. Example:
      projects/{project_id}/locations/{location}/clusters/{cluster_name} The
      cluster's project ID must be the same project ID that is running the
      build. The cluster must exist and have the CloudBuild add-on enabled.
  """

  name = _messages.StringField(1)


class ConnectedRepository(_messages.Message):
  r"""Location of the source in a 2nd-gen Google Cloud Build repository
  resource.

  Fields:
    dir: Optional. Directory, relative to the source root, in which to run the
      build.
    repository: Required. Name of the Google Cloud Build repository, formatted
      as `projects/*/locations/*/connections/*/repositories/*`.
    revision: Required. The revision to fetch from the Git repository such as
      a branch, a tag, a commit SHA, or any Git ref.
  """

  dir = _messages.StringField(1)
  repository = _messages.StringField(2)
  revision = _messages.StringField(3)


class ContainerAnalysisStorage(_messages.Message):
  r"""Configuration for provenance storage in Container Analysis.

  Fields:
    project: The GCP project that stores provenance. Format:
      `projects/{project_id}` or `projects/{project_number}`
  """

  project = _messages.StringField(1)


class CreateBitbucketServerConfigOperationMetadata(_messages.Message):
  r"""Metadata for `CreateBitbucketServerConfig` operation.

  Fields:
    bitbucketServerConfig: The resource name of the BitbucketServerConfig to
      be created. Format:
      `projects/{project}/locations/{location}/bitbucketServerConfigs/{id}`.
    completeTime: Time the operation was completed.
    createTime: Time the operation was created.
  """

  bitbucketServerConfig = _messages.StringField(1)
  completeTime = _messages.StringField(2)
  createTime = _messages.StringField(3)


class CreateBitbucketServerConnectedRepositoryRequest(_messages.Message):
  r"""Request to connect a repository from a connected Bitbucket Server host.

  Fields:
    bitbucketServerConnectedRepository: Required. The Bitbucket Server
      repository to connect.
    parent: Required. The name of the `BitbucketServerConfig` that added
      connected repository. Format: `projects/{project}/locations/{location}/b
      itbucketServerConfigs/{config}`
  """

  bitbucketServerConnectedRepository = _messages.MessageField('BitbucketServerConnectedRepository', 1)
  parent = _messages.StringField(2)


class CreateGitHubEnterpriseConfigOperationMetadata(_messages.Message):
  r"""Metadata for `CreateGithubEnterpriseConfig` operation.

  Fields:
    completeTime: Time the operation was completed.
    createTime: Time the operation was created.
    githubEnterpriseConfig: The resource name of the GitHubEnterprise to be
      created. Format:
      `projects/{project}/locations/{location}/githubEnterpriseConfigs/{id}`.
  """

  completeTime = _messages.StringField(1)
  createTime = _messages.StringField(2)
  githubEnterpriseConfig = _messages.StringField(3)


class CreateGitLabConfigOperationMetadata(_messages.Message):
  r"""Metadata for `CreateGitLabConfig` operation.

  Fields:
    completeTime: Time the operation was completed.
    createTime: Time the operation was created.
    gitlabConfig: The resource name of the GitLabConfig to be created. Format:
      `projects/{project}/locations/{location}/gitlabConfigs/{id}`.
  """

  completeTime = _messages.StringField(1)
  createTime = _messages.StringField(2)
  gitlabConfig = _messages.StringField(3)


class CreateGitLabConnectedRepositoryRequest(_messages.Message):
  r"""Request to connect a repository from a connected GitLab host.

  Fields:
    gitlabConnectedRepository: Required. The GitLab repository to connect.
    parent: Required. The name of the `GitLabConfig` that adds connected
      repository. Format:
      `projects/{project}/locations/{location}/gitLabConfigs/{config}`
  """

  gitlabConnectedRepository = _messages.MessageField('GitLabConnectedRepository', 1)
  parent = _messages.StringField(2)


class CreateWorkerPoolOperationMetadata(_messages.Message):
  r"""Metadata for the `CreateWorkerPool` operation.

  Fields:
    completeTime: Time the operation was completed.
    createTime: Time the operation was created.
    workerPool: The resource name of the `WorkerPool` to create. Format:
      `projects/{project}/locations/{location}/workerPools/{worker_pool}`.
  """

  completeTime = _messages.StringField(1)
  createTime = _messages.StringField(2)
  workerPool = _messages.StringField(3)


class CronConfig(_messages.Message):
  r"""CronConfig describes the configuration of a trigger that creates a build
  whenever a Cloud Scheduler event is received.

  Fields:
    enterpriseConfigResource: The GitHub Enterprise config resource name that
      is associated with this installation.
    schedule: Required. Describes the schedule on which the job will be
      executed. The schedule can be either of the following types: *
      [Crontab](http://en.wikipedia.org/wiki/Cron#Overview) * English-like
      [schedule](https://cloud.google.com/scheduler/docs/configuring/cron-job-
      schedules)
    timeZone: Specifies the time zone to be used in interpreting the schedule.
      The value of this field must be a time zone name from the [tz database]
      (http://en.wikipedia.org/wiki/Tz_database). Note that some time zones
      include a provision for daylight savings time. The rules for daylight
      saving time are determined by the chosen tz. For UTC use the string
      "utc". If a time zone is not specified, the default will be in UTC (also
      known as GMT).
  """

  enterpriseConfigResource = _messages.StringField(1)
  schedule = _messages.StringField(2)
  timeZone = _messages.StringField(3)


class DefaultServiceAccount(_messages.Message):
  r"""The default service account used for `Builds`.

  Fields:
    name: Identifier. Format:
      `projects/{project}/locations/{location}/defaultServiceAccount
    serviceAccountEmail: Output only. The email address of the service account
      identity that will be used for a build by default. This is returned in
      the format `projects/{project}/serviceAccounts/{service_account}` where
      `{service_account}` could be the legacy Cloud Build SA, in the format
      [PROJECT_NUMBER]@cloudbuild.gserviceaccount.com or the Compute SA, in
      the format [PROJECT_NUMBER]-<EMAIL>. If no
      service account will be used by default, this will be empty.
  """

  name = _messages.StringField(1)
  serviceAccountEmail = _messages.StringField(2)


class DeleteBitbucketServerConfigOperationMetadata(_messages.Message):
  r"""Metadata for `DeleteBitbucketServerConfig` operation.

  Fields:
    bitbucketServerConfig: The resource name of the BitbucketServerConfig to
      be deleted. Format:
      `projects/{project}/locations/{location}/bitbucketServerConfigs/{id}`.
    completeTime: Time the operation was completed.
    createTime: Time the operation was created.
  """

  bitbucketServerConfig = _messages.StringField(1)
  completeTime = _messages.StringField(2)
  createTime = _messages.StringField(3)


class DeleteGitHubEnterpriseConfigOperationMetadata(_messages.Message):
  r"""Metadata for `DeleteGitHubEnterpriseConfig` operation.

  Fields:
    completeTime: Time the operation was completed.
    createTime: Time the operation was created.
    githubEnterpriseConfig: The resource name of the GitHubEnterprise to be
      deleted. Format:
      `projects/{project}/locations/{location}/githubEnterpriseConfigs/{id}`.
  """

  completeTime = _messages.StringField(1)
  createTime = _messages.StringField(2)
  githubEnterpriseConfig = _messages.StringField(3)


class DeleteGitLabConfigOperationMetadata(_messages.Message):
  r"""Metadata for `DeleteGitLabConfig` operation.

  Fields:
    completeTime: Time the operation was completed.
    createTime: Time the operation was created.
    gitlabConfig: The resource name of the GitLabConfig to be created. Format:
      `projects/{project}/locations/{location}/gitlabConfigs/{id}`.
  """

  completeTime = _messages.StringField(1)
  createTime = _messages.StringField(2)
  gitlabConfig = _messages.StringField(3)


class DeleteWorkerPoolOperationMetadata(_messages.Message):
  r"""Metadata for the `DeleteWorkerPool` operation.

  Fields:
    completeTime: Time the operation was completed.
    createTime: Time the operation was created.
    workerPool: The resource name of the `WorkerPool` being deleted. Format:
      `projects/{project}/locations/{location}/workerPools/{worker_pool}`.
  """

  completeTime = _messages.StringField(1)
  createTime = _messages.StringField(2)
  workerPool = _messages.StringField(3)


class Dependency(_messages.Message):
  r"""A dependency that the Cloud Build worker will fetch before executing
  user steps.

  Fields:
    empty: If set to true disable all dependency fetching (ignoring the
      default source as well).
    gitSource: Represents a git repository as a build dependency.
  """

  empty = _messages.BooleanField(1)
  gitSource = _messages.MessageField('GitSourceDependency', 2)


class DeveloperConnectConfig(_messages.Message):
  r"""This config defines the location of a source through Developer Connect.

  Fields:
    dir: Required. Directory, relative to the source root, in which to run the
      build.
    gitRepositoryLink: Required. The Developer Connect Git repository link,
      formatted as `projects/*/locations/*/connections/*/gitRepositoryLink/*`.
    revision: Required. The revision to fetch from the Git repository such as
      a branch, a tag, a commit SHA, or any Git ref.
  """

  dir = _messages.StringField(1)
  gitRepositoryLink = _messages.StringField(2)
  revision = _messages.StringField(3)


class DeveloperConnectEventConfig(_messages.Message):
  r"""The configuration of a trigger that creates a build whenever an event
  from the DeveloperConnect API is received.

  Enums:
    GitRepositoryLinkTypeValueValuesEnum: Output only. The type of
      DeveloperConnect GitRepositoryLink.

  Fields:
    gitRepositoryLink: Required. The Developer Connect Git repository link,
      formatted as `projects/*/locations/*/connections/*/gitRepositoryLink/*`.
    gitRepositoryLinkType: Output only. The type of DeveloperConnect
      GitRepositoryLink.
    pullRequest: Filter to match changes in pull requests.
    push: Filter to match changes in refs like branches and tags.
  """

  class GitRepositoryLinkTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of DeveloperConnect GitRepositoryLink.

    Values:
      GIT_REPOSITORY_LINK_TYPE_UNSPECIFIED: If unspecified,
        GitRepositoryLinkType defaults to GITHUB.
      GITHUB: The SCM repo is GITHUB.
      GITHUB_ENTERPRISE: The SCM repo is GITHUB_ENTERPRISE.
      GITLAB: The SCM repo is GITLAB.
      GITLAB_ENTERPRISE: The SCM repo is GITLAB_ENTERPRISE.
      BITBUCKET_DATA_CENTER: The SCM repo is BITBUCKET_DATA_CENTER.
      BITBUCKET_CLOUD: The SCM repo is BITBUCKET_CLOUD.
    """
    GIT_REPOSITORY_LINK_TYPE_UNSPECIFIED = 0
    GITHUB = 1
    GITHUB_ENTERPRISE = 2
    GITLAB = 3
    GITLAB_ENTERPRISE = 4
    BITBUCKET_DATA_CENTER = 5
    BITBUCKET_CLOUD = 6

  gitRepositoryLink = _messages.StringField(1)
  gitRepositoryLinkType = _messages.EnumField('GitRepositoryLinkTypeValueValuesEnum', 2)
  pullRequest = _messages.MessageField('PullRequestFilter', 3)
  push = _messages.MessageField('PushFilter', 4)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class FailureInfo(_messages.Message):
  r"""A fatal problem encountered during the execution of the build.

  Enums:
    TypeValueValuesEnum: The name of the failure.

  Fields:
    detail: Explains the failure issue in more detail using hard-coded text.
    type: The name of the failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The name of the failure.

    Values:
      FAILURE_TYPE_UNSPECIFIED: Type unspecified
      PUSH_FAILED: Unable to push the image to the repository.
      PUSH_IMAGE_NOT_FOUND: Final image not found.
      PUSH_NOT_AUTHORIZED: Unauthorized push of the final image.
      LOGGING_FAILURE: Backend logging failures. Should retry.
      USER_BUILD_STEP: A build step has failed.
      FETCH_SOURCE_FAILED: The source fetching has failed.
    """
    FAILURE_TYPE_UNSPECIFIED = 0
    PUSH_FAILED = 1
    PUSH_IMAGE_NOT_FOUND = 2
    PUSH_NOT_AUTHORIZED = 3
    LOGGING_FAILURE = 4
    USER_BUILD_STEP = 5
    FETCH_SOURCE_FAILED = 6

  detail = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class FileHashes(_messages.Message):
  r"""Container message for hashes of byte content of files, used in
  SourceProvenance messages to verify integrity of source input to the build.

  Fields:
    fileHash: Collection of file hashes.
  """

  fileHash = _messages.MessageField('Hash', 1, repeated=True)


class GCSLocation(_messages.Message):
  r"""Represents a storage location in Cloud Storage

  Fields:
    bucket: Cloud Storage bucket. See
      https://cloud.google.com/storage/docs/naming#requirements
    generation: Cloud Storage generation for the object. If the generation is
      omitted, the latest generation will be used.
    object: Cloud Storage object. See
      https://cloud.google.com/storage/docs/naming#objectnames
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)


class GitConfig(_messages.Message):
  r"""GitConfig is a configuration for git operations.

  Fields:
    http: Configuration for HTTP related git operations.
  """

  http = _messages.MessageField('HttpConfig', 1)


class GitFileSource(_messages.Message):
  r"""GitFileSource describes a file within a (possibly remote) code
  repository.

  Enums:
    RepoTypeValueValuesEnum: See RepoType above.

  Fields:
    bitbucketServerConfig: The full resource name of the bitbucket server
      config. Format:
      `projects/{project}/locations/{location}/bitbucketServerConfigs/{id}`.
    githubEnterpriseConfig: The full resource name of the github enterprise
      config. Format:
      `projects/{project}/locations/{location}/githubEnterpriseConfigs/{id}`.
      `projects/{project}/githubEnterpriseConfigs/{id}`.
    path: The path of the file, with the repo root as the root of the path.
    repoType: See RepoType above.
    repository: The fully qualified resource name of the Repos API repository.
      Either URI or repository can be specified. If unspecified, the repo from
      which the trigger invocation originated is assumed to be the repo from
      which to read the specified path.
    revision: The branch, tag, arbitrary ref, or SHA version of the repo to
      use when resolving the filename (optional). This field respects the same
      syntax/resolution as described here: https://git-
      scm.com/docs/gitrevisions If unspecified, the revision from which the
      trigger invocation originated is assumed to be the revision from which
      to read the specified path.
    uri: The URI of the repo. Either uri or repository can be specified. If
      unspecified, the repo from which the trigger invocation originated is
      assumed to be the repo from which to read the specified path.
  """

  class RepoTypeValueValuesEnum(_messages.Enum):
    r"""See RepoType above.

    Values:
      UNKNOWN: The default, unknown repo type. Don't use it, instead use one
        of the other repo types.
      CLOUD_SOURCE_REPOSITORIES: A Google Cloud Source Repositories-hosted
        repo.
      GITHUB: A GitHub-hosted repo not necessarily on "github.com" (i.e.
        GitHub Enterprise).
      BITBUCKET_SERVER: A Bitbucket Server-hosted repo.
      GITLAB: A GitLab-hosted repo.
      BITBUCKET_CLOUD: A Bitbucket Cloud-hosted repo.
    """
    UNKNOWN = 0
    CLOUD_SOURCE_REPOSITORIES = 1
    GITHUB = 2
    BITBUCKET_SERVER = 3
    GITLAB = 4
    BITBUCKET_CLOUD = 5

  bitbucketServerConfig = _messages.StringField(1)
  githubEnterpriseConfig = _messages.StringField(2)
  path = _messages.StringField(3)
  repoType = _messages.EnumField('RepoTypeValueValuesEnum', 4)
  repository = _messages.StringField(5)
  revision = _messages.StringField(6)
  uri = _messages.StringField(7)


class GitHubEnterpriseApp(_messages.Message):
  r"""RPC response object returned by the GetGitHubEnterpriseApp RPC method.

  Fields:
    name: Name of the GitHub App
    slug: Slug (URL friendly name) of the GitHub App. This can be found on the
      settings page for the GitHub App (e.g.
      https://github.com/settings/apps/:app_slug) GitHub docs:
      https://docs.github.com/en/free-pro-team@latest/rest/reference/apps#get-
      an-app
  """

  name = _messages.StringField(1)
  slug = _messages.StringField(2)


class GitHubEnterpriseConfig(_messages.Message):
  r"""GitHubEnterpriseConfig represents a configuration for a GitHub
  Enterprise server.

  Fields:
    appConfigJson: Cloud Storage location of the encrypted GitHub App config
      information.
    appId: Required. The GitHub app id of the Cloud Build app on the GitHub
      Enterprise server.
    createTime: Output only. Time when the installation was associated with
      the project.
    displayName: Optional. Name to display for this config.
    hostUrl: The URL of the github enterprise host the configuration is for.
    name: The full resource name for the GitHubEnterpriseConfig For example: "
      projects/{$project_id}/locations/{$location_id}/githubEnterpriseConfigs/
      {$config_id}"
    peeredNetwork: Optional. The network to be used when reaching out to the
      GitHub Enterprise server. The VPC network must be enabled for private
      service connection. This should be set if the GitHub Enterprise server
      is hosted on-premises and not reachable by public internet. If this
      field is left empty, no network peering will occur and calls to the
      GitHub Enterprise server will be made over the public internet. Must be
      in the format `projects/{project}/global/networks/{network}`, where
      {project} is a project number or id and {network} is the name of a VPC
      network in the project.
    secrets: Optional. Names of secrets in Secret Manager.
    sslCa: Optional. SSL certificate to use for requests to GitHub Enterprise.
    webhookKey: The key that should be attached to webhook calls to the
      ReceiveWebhook endpoint.
  """

  appConfigJson = _messages.MessageField('GCSLocation', 1)
  appId = _messages.IntegerField(2)
  createTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  hostUrl = _messages.StringField(5)
  name = _messages.StringField(6)
  peeredNetwork = _messages.StringField(7)
  secrets = _messages.MessageField('GitHubEnterpriseSecrets', 8)
  sslCa = _messages.StringField(9)
  webhookKey = _messages.StringField(10)


class GitHubEnterpriseSecrets(_messages.Message):
  r"""GitHubEnterpriseSecrets represents the names of all necessary secrets in
  Secret Manager for a GitHub Enterprise server. Format is:
  projects//secrets/.

  Fields:
    oauthClientIdName: The resource name for the OAuth client ID secret in
      Secret Manager.
    oauthClientIdVersionName: The resource name for the OAuth client ID secret
      version in Secret Manager.
    oauthSecretName: The resource name for the OAuth secret in Secret Manager.
    oauthSecretVersionName: The resource name for the OAuth secret secret
      version in Secret Manager.
    privateKeyName: The resource name for the private key secret.
    privateKeyVersionName: The resource name for the private key secret
      version.
    webhookSecretName: The resource name for the webhook secret in Secret
      Manager.
    webhookSecretVersionName: The resource name for the webhook secret secret
      version in Secret Manager.
  """

  oauthClientIdName = _messages.StringField(1)
  oauthClientIdVersionName = _messages.StringField(2)
  oauthSecretName = _messages.StringField(3)
  oauthSecretVersionName = _messages.StringField(4)
  privateKeyName = _messages.StringField(5)
  privateKeyVersionName = _messages.StringField(6)
  webhookSecretName = _messages.StringField(7)
  webhookSecretVersionName = _messages.StringField(8)


class GitHubEventsConfig(_messages.Message):
  r"""GitHubEventsConfig describes the configuration of a trigger that creates
  a build whenever a GitHub event is received.

  Fields:
    enterpriseConfig: Output only. The GitHubEnterpriseConfig enterprise
      config specified in the enterprise_config_resource_name field.
    enterpriseConfigResourceName: The resource name of the github enterprise
      config that should be applied to this installation. For example: "projec
      ts/{$project_id}/locations/{$location_id}/githubEnterpriseConfigs/{$conf
      ig_id}"
    installationId: The installationID that emits the GitHub event.
    name: Name of the repository. For example: The name for
      https://github.com/googlecloudplatform/cloud-builders is "cloud-
      builders".
    owner: Owner of the repository. For example: The owner for
      https://github.com/googlecloudplatform/cloud-builders is
      "googlecloudplatform".
    pullRequest: filter to match changes in pull requests.
    push: filter to match changes in refs like branches, tags.
  """

  enterpriseConfig = _messages.MessageField('GitHubEnterpriseConfig', 1)
  enterpriseConfigResourceName = _messages.StringField(2)
  installationId = _messages.IntegerField(3)
  name = _messages.StringField(4)
  owner = _messages.StringField(5)
  pullRequest = _messages.MessageField('PullRequestFilter', 6)
  push = _messages.MessageField('PushFilter', 7)


class GitHubRepositorySetting(_messages.Message):
  r"""Represents a GitHub repository setting.

  Fields:
    name: Name of the repository.
    owner: GitHub user or organization name.
  """

  name = _messages.StringField(1)
  owner = _messages.StringField(2)


class GitHubRepositorySettingList(_messages.Message):
  r"""A wrapper message for a list of GitHubRepositorySettings.

  Fields:
    repositorySettings: A list of GitHubRepositorySettings.
  """

  repositorySettings = _messages.MessageField('GitHubRepositorySetting', 1, repeated=True)


class GitLabConfig(_messages.Message):
  r"""GitLabConfig represents the configuration for a GitLab integration.

  Fields:
    connectedRepositories: Connected GitLab.com or GitLabEnterprise
      repositories for this config.
    createTime: Output only. Time when the config was created.
    enterpriseConfig: Optional. GitLabEnterprise config.
    name: The resource name for the config.
    secrets: Required. Secret Manager secrets needed by the config.
    username: Username of the GitLab.com or GitLab Enterprise account Cloud
      Build will use.
    webhookKey: Output only. UUID included in webhook requests. The UUID is
      used to look up the corresponding config.
  """

  connectedRepositories = _messages.MessageField('GitLabRepositoryId', 1, repeated=True)
  createTime = _messages.StringField(2)
  enterpriseConfig = _messages.MessageField('GitLabEnterpriseConfig', 3)
  name = _messages.StringField(4)
  secrets = _messages.MessageField('GitLabSecrets', 5)
  username = _messages.StringField(6)
  webhookKey = _messages.StringField(7)


class GitLabConnectedRepository(_messages.Message):
  r"""GitLabConnectedRepository represents a GitLab connected repository
  request response.

  Fields:
    parent: The name of the `GitLabConfig` that added connected repository.
      Format: `projects/{project}/locations/{location}/gitLabConfigs/{config}`
    repo: The GitLab repositories to connect.
    status: Output only. The status of the repo connection request.
  """

  parent = _messages.StringField(1)
  repo = _messages.MessageField('GitLabRepositoryId', 2)
  status = _messages.MessageField('Status', 3)


class GitLabEnterpriseConfig(_messages.Message):
  r"""GitLabEnterpriseConfig represents the configuration for a
  GitLabEnterprise integration.

  Fields:
    hostUri: Immutable. The URI of the GitlabEnterprise host.
    serviceDirectoryConfig: The Service Directory configuration to be used
      when reaching out to the GitLab Enterprise instance.
    sslCa: The SSL certificate to use in requests to GitLab Enterprise
      instances.
  """

  hostUri = _messages.StringField(1)
  serviceDirectoryConfig = _messages.MessageField('ServiceDirectoryConfig', 2)
  sslCa = _messages.StringField(3)


class GitLabEventsConfig(_messages.Message):
  r"""GitLabEventsConfig describes the configuration of a trigger that creates
  a build whenever a GitLab event is received.

  Fields:
    gitlabConfig: Output only. The GitLabConfig specified in the
      gitlab_config_resource field.
    gitlabConfigResource: The GitLab config resource that this trigger config
      maps to.
    projectNamespace: Namespace of the GitLab project.
    pullRequest: Filter to match changes in pull requests.
    push: Filter to match changes in refs like branches, tags.
  """

  gitlabConfig = _messages.MessageField('GitLabConfig', 1)
  gitlabConfigResource = _messages.StringField(2)
  projectNamespace = _messages.StringField(3)
  pullRequest = _messages.MessageField('PullRequestFilter', 4)
  push = _messages.MessageField('PushFilter', 5)


class GitLabRepository(_messages.Message):
  r"""Proto Representing a GitLabRepository

  Fields:
    browseUri: Link to the browse repo page on the GitLab instance
    description: Description of the repository
    displayName: Display name of the repository
    name: The resource name of the repository
    repositoryId: Identifier for a repository
  """

  browseUri = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  name = _messages.StringField(4)
  repositoryId = _messages.MessageField('GitLabRepositoryId', 5)


class GitLabRepositoryId(_messages.Message):
  r"""GitLabRepositoryId identifies a specific repository hosted on GitLab.com
  or GitLabEnterprise

  Fields:
    id: Required. Identifier for the repository. example: "namespace/project-
      slug", namespace is usually the username or group ID
    webhookId: Output only. The ID of the webhook that was created for
      receiving events from this repo. We only create and manage a single
      webhook for each repo.
  """

  id = _messages.StringField(1)
  webhookId = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GitLabSecrets(_messages.Message):
  r"""GitLabSecrets represents the secrets in Secret Manager for a GitLab
  integration.

  Fields:
    apiAccessTokenVersion: Required. The resource name for the api access
      token's secret version
    apiKeyVersion: Required. Immutable. API Key that will be attached to
      webhook requests from GitLab to Cloud Build.
    readAccessTokenVersion: Required. The resource name for the read access
      token's secret version
    webhookSecretVersion: Required. Immutable. The resource name for the
      webhook secret's secret version. Once this field has been set, it cannot
      be changed. If you need to change it, please create another
      GitLabConfig.
  """

  apiAccessTokenVersion = _messages.StringField(1)
  apiKeyVersion = _messages.StringField(2)
  readAccessTokenVersion = _messages.StringField(3)
  webhookSecretVersion = _messages.StringField(4)


class GitRepoSource(_messages.Message):
  r"""GitRepoSource describes a repo and ref of a code repository.

  Enums:
    RepoTypeValueValuesEnum: See RepoType below.

  Fields:
    bitbucketServerConfig: The full resource name of the bitbucket server
      config. Format:
      `projects/{project}/locations/{location}/bitbucketServerConfigs/{id}`.
    githubEnterpriseConfig: The full resource name of the github enterprise
      config. Format:
      `projects/{project}/locations/{location}/githubEnterpriseConfigs/{id}`.
      `projects/{project}/githubEnterpriseConfigs/{id}`.
    ref: The branch or tag to use. Must start with "refs/" (required).
    repoType: See RepoType below.
    repository: The connected repository resource name, in the format
      `projects/*/locations/*/connections/*/repositories/*`. Either `uri` or
      `repository` can be specified and is required.
    uri: The URI of the repo (e.g. https://github.com/user/repo.git). Either
      `uri` or `repository` can be specified and is required.
  """

  class RepoTypeValueValuesEnum(_messages.Enum):
    r"""See RepoType below.

    Values:
      UNKNOWN: The default, unknown repo type. Don't use it, instead use one
        of the other repo types.
      CLOUD_SOURCE_REPOSITORIES: A Google Cloud Source Repositories-hosted
        repo.
      GITHUB: A GitHub-hosted repo not necessarily on "github.com" (i.e.
        GitHub Enterprise).
      BITBUCKET_SERVER: A Bitbucket Server-hosted repo.
      GITLAB: A GitLab-hosted repo.
      BITBUCKET_CLOUD: A Bitbucket Cloud-hosted repo.
    """
    UNKNOWN = 0
    CLOUD_SOURCE_REPOSITORIES = 1
    GITHUB = 2
    BITBUCKET_SERVER = 3
    GITLAB = 4
    BITBUCKET_CLOUD = 5

  bitbucketServerConfig = _messages.StringField(1)
  githubEnterpriseConfig = _messages.StringField(2)
  ref = _messages.StringField(3)
  repoType = _messages.EnumField('RepoTypeValueValuesEnum', 4)
  repository = _messages.StringField(5)
  uri = _messages.StringField(6)


class GitSource(_messages.Message):
  r"""Location of the source in any accessible Git repository.

  Fields:
    dir: Optional. Directory, relative to the source root, in which to run the
      build. This must be a relative path. If a step's `dir` is specified and
      is an absolute path, this value is ignored for that step's execution.
    revision: Optional. The revision to fetch from the Git repository such as
      a branch, a tag, a commit SHA, or any Git ref. Cloud Build uses `git
      fetch` to fetch the revision from the Git repository; therefore make
      sure that the string you provide for `revision` is parsable by the
      command. For information on string values accepted by `git fetch`, see
      https://git-scm.com/docs/gitrevisions#_specifying_revisions. For
      information on `git fetch`, see https://git-scm.com/docs/git-fetch.
    url: Required. Location of the Git repo to build. This will be used as a
      `git remote`, see https://git-scm.com/docs/git-remote.
  """

  dir = _messages.StringField(1)
  revision = _messages.StringField(2)
  url = _messages.StringField(3)


class GitSourceDependency(_messages.Message):
  r"""Represents a git repository as a build dependency.

  Fields:
    depth: Optional. How much history should be fetched for the build (default
      1, -1 for all history).
    destPath: Required. Where should the files be placed on the worker.
    recurseSubmodules: Optional. True if submodules should be fetched too
      (default false).
    repository: Required. The kind of repo (url or dev connect).
    revision: Required. The revision that we will fetch the repo at.
  """

  depth = _messages.IntegerField(1)
  destPath = _messages.StringField(2)
  recurseSubmodules = _messages.BooleanField(3)
  repository = _messages.MessageField('GitSourceRepository', 4)
  revision = _messages.StringField(5)


class GitSourceRepository(_messages.Message):
  r"""A repository for a git source.

  Fields:
    developerConnect: The Developer Connect Git repository link formatted as
      `projects/*/locations/*/connections/*/gitRepositoryLink/*`
    url: Location of the Git repository.
  """

  developerConnect = _messages.StringField(1)
  url = _messages.StringField(2)


class GoModule(_messages.Message):
  r"""Go module to upload to Artifact Registry upon successful completion of
  all build steps. A module refers to all dependencies in a go.mod file.

  Fields:
    modulePath: Optional. The Go module's "module path". e.g.
      example.com/foo/v2
    moduleVersion: Optional. The Go module's semantic version in the form
      vX.Y.Z. e.g. v0.1.1 Pre-release identifiers can also be added by
      appending a dash and dot separated ASCII alphanumeric characters and
      hyphens. e.g. v0.2.3-alpha.x.12m.5
    repositoryLocation: Optional. Location of the Artifact Registry
      repository. i.e. us-east1 Defaults to the build's location.
    repositoryName: Optional. Artifact Registry repository name. Specified Go
      modules will be zipped and uploaded to Artifact Registry with this
      location as a prefix. e.g. my-go-repo
    repositoryProjectId: Optional. Project ID of the Artifact Registry
      repository. Defaults to the build project.
    sourcePath: Optional. Source path of the go.mod file in the build's
      workspace. If not specified, this will default to the current directory.
      e.g. ~/code/go/mypackage
  """

  modulePath = _messages.StringField(1)
  moduleVersion = _messages.StringField(2)
  repositoryLocation = _messages.StringField(3)
  repositoryName = _messages.StringField(4)
  repositoryProjectId = _messages.StringField(5)
  sourcePath = _messages.StringField(6)


class GoogleDevtoolsCloudbuildV1BuildOptionsPoolOptionWorkerConfig(_messages.Message):
  r"""Configuration per workload for both Private Pools and Hybrid Pools.

  Fields:
    diskSizeGb: The disk size (in GB) which is requested for the build
      container. If unset, a value of 10 GB will be used.
    memoryGb: The memory (in GB) which is requested for the build container.
      If unset, a value of 4 GB will be used.
    vcpuCount: The number of vCPUs which are requested for the build
      container. If unset, a value of 1 will be used.
  """

  diskSizeGb = _messages.IntegerField(1)
  memoryGb = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  vcpuCount = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleDevtoolsCloudbuildV1NetworkConfig(_messages.Message):
  r"""Network configuration for a PrivatePool.

  Enums:
    EgressOptionValueValuesEnum: Immutable. Define whether workloads on the
      PrivatePool can talk to public IPs. If unset, the value NO_PUBLIC_EGRESS
      will be used.

  Fields:
    egressOption: Immutable. Define whether workloads on the PrivatePool can
      talk to public IPs. If unset, the value NO_PUBLIC_EGRESS will be used.
    peeredNetwork: Required. Immutable. The network definition that the
      workers are peered to. If this section is left empty, the workers will
      be peered to `WorkerPool.project_id` on the service producer network.
      Must be in the format `projects/{project}/global/networks/{network}`,
      where `{project}` is a project number, such as `12345`, and `{network}`
      is the name of a VPC network in the project. See [Understanding network
      configuration options](https://cloud.google.com/build/docs/private-
      pools/set-up-private-pool-environment)
  """

  class EgressOptionValueValuesEnum(_messages.Enum):
    r"""Immutable. Define whether workloads on the PrivatePool can talk to
    public IPs. If unset, the value NO_PUBLIC_EGRESS will be used.

    Values:
      EGRESS_OPTION_UNSPECIFIED: Unspecified policy - this is treated as
        NO_PUBLIC_EGRESS.
      NO_PUBLIC_EGRESS: Public egress is disallowed.
      PUBLIC_EGRESS: Public egress is allowed.
    """
    EGRESS_OPTION_UNSPECIFIED = 0
    NO_PUBLIC_EGRESS = 1
    PUBLIC_EGRESS = 2

  egressOption = _messages.EnumField('EgressOptionValueValuesEnum', 1)
  peeredNetwork = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1PrivatePoolConfigWorkerConfig(_messages.Message):
  r"""Defines the configuration to be used for creating workers in the pool.

  Fields:
    machineType: Machine type of the workers in the pool. This field does not
      currently support mutations.
  """

  machineType = _messages.StringField(1)


class GoogleDevtoolsCloudbuildV1ScalingConfig(_messages.Message):
  r"""Defines the scaling configuration for the pool.

  Fields:
    maxWorkersPerZone: Max number of workers in the Private Pool per zone.
      Cloud Build will run workloads in three zones per Private Pool for
      reliability.
    readyWorkers: The number of preemptible workers (pods) that will run with
      the minimum vCPU and memory to keep resources ready for customer
      workloads in the cluster. If unset, a value of 0 will be used.
  """

  maxWorkersPerZone = _messages.IntegerField(1)
  readyWorkers = _messages.IntegerField(2)


class Hash(_messages.Message):
  r"""Container message for hash values.

  Enums:
    TypeValueValuesEnum: The type of hash that was performed.

  Fields:
    type: The type of hash that was performed.
    value: The hash value.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of hash that was performed.

    Values:
      NONE: No hash requested.
      SHA256: Use a sha256 hash.
      MD5: Use a md5 hash.
      GO_MODULE_H1: Dirhash of a Go module's source code which is then hex-
        encoded.
      SHA512: Use a sha512 hash.
    """
    NONE = 0
    SHA256 = 1
    MD5 = 2
    GO_MODULE_H1 = 3
    SHA512 = 4

  type = _messages.EnumField('TypeValueValuesEnum', 1)
  value = _messages.BytesField(2)


class HttpBody(_messages.Message):
  r"""Message that represents an arbitrary HTTP body. It should only be used
  for payload formats that can't be represented as JSON, such as raw binary or
  an HTML page. This message can be used both in streaming and non-streaming
  API methods in the request as well as the response. It can be used as a top-
  level request field, which is convenient if one wants to extract parameters
  from either the URL or HTTP template into the request fields and also want
  access to the raw HTTP body. Example: message GetResourceRequest { // A
  unique request id. string request_id = 1; // The raw HTTP body is bound to
  this field. google.api.HttpBody http_body = 2; } service ResourceService {
  rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc
  UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); }
  Example with streaming methods: service CaldavService { rpc
  GetCalendar(stream google.api.HttpBody) returns (stream
  google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns
  (stream google.api.HttpBody); } Use of this type only changes how the
  request and response bodies are handled, all other features will continue to
  work unchanged.

  Messages:
    ExtensionsValueListEntry: A ExtensionsValueListEntry object.

  Fields:
    contentType: The HTTP Content-Type header value specifying the content
      type of the body.
    data: The HTTP request/response body as raw binary.
    extensions: Application specific response metadata. Must be set in the
      first response for streaming APIs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtensionsValueListEntry(_messages.Message):
    r"""A ExtensionsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        ExtensionsValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtensionsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  contentType = _messages.StringField(1)
  data = _messages.BytesField(2)
  extensions = _messages.MessageField('ExtensionsValueListEntry', 3, repeated=True)


class HttpConfig(_messages.Message):
  r"""HttpConfig is a configuration for HTTP related git operations.

  Fields:
    proxySecretVersionName: SecretVersion resource of the HTTP proxy URL. The
      Service Account used in the build (either the default Service Account or
      user-specified Service Account) should have
      `secretmanager.versions.access` permissions on this secret. The proxy
      URL should be in format `protocol://@]proxyhost[:port]`.
    proxySslCaInfo: Optional. Cloud Storage object storing the certificate to
      use with the HTTP proxy.
  """

  proxySecretVersionName = _messages.StringField(1)
  proxySslCaInfo = _messages.MessageField('GCSLocation', 2)


class HybridPoolConfig(_messages.Message):
  r"""Configuration for a Hybrid Worker Pool Next ID: 6

  Enums:
    BuilderImageCachingValueValuesEnum: Immutable. Controls how the worker
      pool caches images. If unspecified during worker pool creation, this
      field is defaulted to CACHING_DISABLED.

  Fields:
    builderImageCaching: Immutable. Controls how the worker pool caches
      images. If unspecified during worker pool creation, this field is
      defaulted to CACHING_DISABLED.
    defaultWorkerConfig: Default settings which will be applied to builds on
      this worker pool if they are not specified in the build request.
    membership: Required. Immutable. The Anthos/GKE Hub membership of the
      cluster which will run the actual build operations. Example:
      projects/{project}/locations/{location}/memberships/{cluster_name}
  """

  class BuilderImageCachingValueValuesEnum(_messages.Enum):
    r"""Immutable. Controls how the worker pool caches images. If unspecified
    during worker pool creation, this field is defaulted to CACHING_DISABLED.

    Values:
      BUILDER_IMAGE_CACHING_UNSPECIFIED: Default enum type. This should not be
        used.
      CACHING_DISABLED: DinD caching is disabled and no caching resources are
        provisioned.
      VOLUME_CACHING: A PersistentVolumeClaim is provisioned for caching.
    """
    BUILDER_IMAGE_CACHING_UNSPECIFIED = 0
    CACHING_DISABLED = 1
    VOLUME_CACHING = 2

  builderImageCaching = _messages.EnumField('BuilderImageCachingValueValuesEnum', 1)
  defaultWorkerConfig = _messages.MessageField('HybridWorkerConfig', 2)
  membership = _messages.StringField(3)


class HybridWorkerConfig(_messages.Message):
  r"""These settings can be applied to a user's build operations. Next ID: 4

  Fields:
    diskSizeGb: The disk size (in GB) which is requested for the build
      container. Defaults to 10 GB.
    memoryGb: The memory (in GB) which is requested for the build container.
      Defaults to 4 GB.
    vcpuCount: The number of vCPUs which are requested for the build
      container. Defaults to 1.
  """

  diskSizeGb = _messages.IntegerField(1)
  memoryGb = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  vcpuCount = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class InlineSecret(_messages.Message):
  r"""Pairs a set of secret environment variables mapped to encrypted values
  with the Cloud KMS key to use to decrypt the value.

  Messages:
    EnvMapValue: Map of environment variable name to its encrypted value.
      Secret environment variables must be unique across all of a build's
      secrets, and must be used by at least one build step. Values can be at
      most 64 KB in size. There can be at most 100 secret values across all of
      a build's secrets.

  Fields:
    envMap: Map of environment variable name to its encrypted value. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step. Values can be at most 64 KB
      in size. There can be at most 100 secret values across all of a build's
      secrets.
    kmsKeyName: Resource name of Cloud KMS crypto key to decrypt the encrypted
      value. In format: projects/*/locations/*/keyRings/*/cryptoKeys/*
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvMapValue(_messages.Message):
    r"""Map of environment variable name to its encrypted value. Secret
    environment variables must be unique across all of a build's secrets, and
    must be used by at least one build step. Values can be at most 64 KB in
    size. There can be at most 100 secret values across all of a build's
    secrets.

    Messages:
      AdditionalProperty: An additional property for a EnvMapValue object.

    Fields:
      additionalProperties: Additional properties of type EnvMapValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvMapValue object.

      Fields:
        key: Name of the additional property.
        value: A byte attribute.
      """

      key = _messages.StringField(1)
      value = _messages.BytesField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  envMap = _messages.MessageField('EnvMapValue', 1)
  kmsKeyName = _messages.StringField(2)


class Installation(_messages.Message):
  r"""A GitHub-app installation.

  Fields:
    createTime: Time when the installation was associated with the project.
      This field is immutable and cannot be updated.
    enterpriseConfig: Output only. The GitHubEnterpriseConfig enterprise
      config specified in the enterprise_config_resource_name field.
    enterpriseConfigResourceName: Optional: The resource name of the github
      enterprise config that should be applied to this installation. For
      example: "projects/{$project_id}/locations/{$location_id}/githubEnterpri
      seConfigs/{$config_id}"
    id: GitHub installation ID, created by GitHub.
    name: The `Installation` name with format:
      `projects/{project}/locations/{location}/installations/{installation}`,
      where {installation} is GitHub installation ID created by GitHub.
    projectId: The project ID of the GCP project the installation is
      associated with.
    projectNum: Numerical ID of the project.
    repositorySettingList: The GitHub repositories that we should respond to
      for this installation. If this is not set, we will respect the
      default_check_suite_events boolean for any repository visible for that
      installation.
  """

  createTime = _messages.StringField(1)
  enterpriseConfig = _messages.MessageField('GitHubEnterpriseConfig', 2)
  enterpriseConfigResourceName = _messages.StringField(3)
  id = _messages.IntegerField(4)
  name = _messages.StringField(5)
  projectId = _messages.StringField(6)
  projectNum = _messages.IntegerField(7)
  repositorySettingList = _messages.MessageField('GitHubRepositorySettingList', 8)


class ListBitbucketServerConfigsResponse(_messages.Message):
  r"""RPC response object returned by ListBitbucketServerConfigs RPC method.

  Fields:
    bitbucketServerConfigs: A list of BitbucketServerConfigs
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  bitbucketServerConfigs = _messages.MessageField('BitbucketServerConfig', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListBitbucketServerRepositoriesResponse(_messages.Message):
  r"""RPC response object returned by the ListBitbucketServerRepositories RPC
  method.

  Fields:
    bitbucketServerRepositories: List of Bitbucket Server repositories.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  bitbucketServerRepositories = _messages.MessageField('BitbucketServerRepository', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListBuildTriggersResponse(_messages.Message):
  r"""Response containing existing `BuildTriggers`.

  Fields:
    nextPageToken: Token to receive the next page of results.
    triggers: `BuildTriggers` for the project, sorted by `create_time`
      descending.
  """

  nextPageToken = _messages.StringField(1)
  triggers = _messages.MessageField('BuildTrigger', 2, repeated=True)


class ListBuildsResponse(_messages.Message):
  r"""Response including listed builds.

  Fields:
    builds: Builds will be sorted by `create_time`, descending.
    nextPageToken: Token to receive the next page of results. This will be
      absent if the end of the response list has been reached.
  """

  builds = _messages.MessageField('Build', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListGitHubInstallationsForProjectResponse(_messages.Message):
  r"""RPC response object returned by the ListGitHubInstallations RPC method.

  Fields:
    installations: Installations belonging to the specified project_id.
  """

  installations = _messages.MessageField('Installation', 1, repeated=True)


class ListGitHubInstallationsResponse(_messages.Message):
  r"""RPC response object accepted by the ListGitHubInstallations RPC method.

  Fields:
    installations: Installations matching the requested installation ID.
  """

  installations = _messages.MessageField('Installation', 1, repeated=True)


class ListGitLabConfigsResponse(_messages.Message):
  r"""RPC response object returned by ListGitLabConfigs RPC method.

  Fields:
    gitlabConfigs: A list of GitLabConfigs
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page If this field is omitted, there are no subsequent pages.
  """

  gitlabConfigs = _messages.MessageField('GitLabConfig', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListGitLabRepositoriesResponse(_messages.Message):
  r"""RPC response object returned by the ListGitLabRepositories RPC method.

  Fields:
    gitlabRepositories: List of GitLab repositories
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  gitlabRepositories = _messages.MessageField('GitLabRepository', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListGithubEnterpriseConfigsResponse(_messages.Message):
  r"""RPC response object returned by ListGithubEnterpriseConfigs RPC method.

  Fields:
    configs: A list of GitHubEnterpriseConfigs
  """

  configs = _messages.MessageField('GitHubEnterpriseConfig', 1, repeated=True)


class ListWorkerPoolsResponse(_messages.Message):
  r"""Response containing existing `WorkerPools`.

  Fields:
    nextPageToken: Continuation token used to page through large result sets.
      Provide this value in a subsequent ListWorkerPoolsRequest to return the
      next page of results.
    workerPools: `WorkerPools` for the specified project.
  """

  nextPageToken = _messages.StringField(1)
  workerPools = _messages.MessageField('WorkerPool', 2, repeated=True)


class MavenArtifact(_messages.Message):
  r"""A Maven artifact to upload to Artifact Registry upon successful
  completion of all build steps.

  Fields:
    artifactId: Maven `artifactId` value used when uploading the artifact to
      Artifact Registry.
    deployFolder: Optional. Path to a folder containing the files to upload to
      Artifact Registry. This can be either an absolute path, e.g.
      `/workspace/my-app/target/`, or a relative path from /workspace, e.g.
      `my-app/target/`. This field is mutually exclusive with the `path`
      field.
    groupId: Maven `groupId` value used when uploading the artifact to
      Artifact Registry.
    path: Optional. Path to an artifact in the build's workspace to be
      uploaded to Artifact Registry. This can be either an absolute path, e.g.
      /workspace/my-app/target/my-app-1.0.SNAPSHOT.jar or a relative path from
      /workspace, e.g. my-app/target/my-app-1.0.SNAPSHOT.jar.
    repository: Artifact Registry repository, in the form "https://$REGION-
      maven.pkg.dev/$PROJECT/$REPOSITORY" Artifact in the workspace specified
      by path will be uploaded to Artifact Registry with this location as a
      prefix.
    version: Maven `version` value used when uploading the artifact to
      Artifact Registry.
  """

  artifactId = _messages.StringField(1)
  deployFolder = _messages.StringField(2)
  groupId = _messages.StringField(3)
  path = _messages.StringField(4)
  repository = _messages.StringField(5)
  version = _messages.StringField(6)


class NetworkConfig(_messages.Message):
  r"""Defines the network configuration for the pool.

  Enums:
    EgressOptionValueValuesEnum: Option to configure network egress for the
      workers.

  Fields:
    egressOption: Option to configure network egress for the workers.
    peeredNetwork: Required. Immutable. The network definition that the
      workers are peered to. If this section is left empty, the workers will
      be peered to `WorkerPool.project_id` on the service producer network.
      Must be in the format `projects/{project}/global/networks/{network}`,
      where `{project}` is a project number, such as `12345`, and `{network}`
      is the name of a VPC network in the project. See [Understanding network
      configuration options](https://cloud.google.com/build/docs/private-
      pools/set-up-private-pool-environment)
    peeredNetworkIpRange: Immutable. Subnet IP range within the peered
      network. This is specified in CIDR notation with a slash and the subnet
      prefix size. You can optionally specify an IP address before the subnet
      prefix value. e.g. `***********/29` would specify an IP range starting
      at *********** with a prefix size of 29 bits. `/16` would specify a
      prefix size of 16 bits, with an automatically determined IP within the
      peered VPC. If unspecified, a value of `/24` will be used.
  """

  class EgressOptionValueValuesEnum(_messages.Enum):
    r"""Option to configure network egress for the workers.

    Values:
      EGRESS_OPTION_UNSPECIFIED: If set, defaults to PUBLIC_EGRESS.
      NO_PUBLIC_EGRESS: If set, workers are created without any public
        address, which prevents network egress to public IPs unless a network
        proxy is configured.
      PUBLIC_EGRESS: If set, workers are created with a public address which
        allows for public internet egress.
    """
    EGRESS_OPTION_UNSPECIFIED = 0
    NO_PUBLIC_EGRESS = 1
    PUBLIC_EGRESS = 2

  egressOption = _messages.EnumField('EgressOptionValueValuesEnum', 1)
  peeredNetwork = _messages.StringField(2)
  peeredNetworkIpRange = _messages.StringField(3)


class NpmPackage(_messages.Message):
  r"""Npm package to upload to Artifact Registry upon successful completion of
  all build steps.

  Fields:
    packagePath: Path to the package.json. e.g. workspace/path/to/package
    repository: Artifact Registry repository, in the form "https://$REGION-
      npm.pkg.dev/$PROJECT/$REPOSITORY" Npm package in the workspace specified
      by path will be zipped and uploaded to Artifact Registry with this
      location as a prefix.
  """

  packagePath = _messages.StringField(1)
  repository = _messages.StringField(2)


class OAuthRegistrationURI(_messages.Message):
  r"""RPC Response object returned by GetOAuthRegistrationURL

  Fields:
    registrationUri: The URL that the user should be redirected to in order to
      start the OAuth flow. When the user is redirected to this URL, they will
      be sent to the source provider specified in the request to authorize
      CloudBuild to access their oauth credentials. After the authorization is
      completed, the user will be redirected to the Cloud Build console.
  """

  registrationUri = _messages.StringField(1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have been cancelled
      successfully have google.longrunning.Operation.error value with a
      google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class PoolOption(_messages.Message):
  r"""Details about how a build should be executed on a `WorkerPool`. See
  [running builds in a private
  pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-
  private-pool) for more information.

  Fields:
    name: The `WorkerPool` resource to execute the build on. You must have
      `cloudbuild.workerpools.use` on the project hosting the WorkerPool.
      Format
      projects/{project}/locations/{location}/workerPools/{workerPoolId}
    workerConfig: Configuration per workload.
  """

  name = _messages.StringField(1)
  workerConfig = _messages.MessageField('GoogleDevtoolsCloudbuildV1BuildOptionsPoolOptionWorkerConfig', 2)


class PrivatePoolConfig(_messages.Message):
  r"""Configuration for a PrivatePool.

  Enums:
    PrivilegedModeValueValuesEnum: Immutable. Specifies the privileged mode
      for the worker pool. Once created, this setting cannot be changed on the
      worker pool, as we are unable to guarantee that the cluster has not been
      altered by misuse of privileged Docker daemon.

  Messages:
    LoggingSasValue: Output only.

  Fields:
    loggingSas: Output only.
    networkConfig: Network configuration for the pool.
    privilegedMode: Immutable. Specifies the privileged mode for the worker
      pool. Once created, this setting cannot be changed on the worker pool,
      as we are unable to guarantee that the cluster has not been altered by
      misuse of privileged Docker daemon.
    scalingConfig: Configuration options for worker pool.
    securityConfig: Security configuration for the pool.
    workerConfig: Configuration options for individual workers.
    workerPoolGroup: Output only. UUID representing worker pools with the same
      region, privilege mode and network config.
  """

  class PrivilegedModeValueValuesEnum(_messages.Enum):
    r"""Immutable. Specifies the privileged mode for the worker pool. Once
    created, this setting cannot be changed on the worker pool, as we are
    unable to guarantee that the cluster has not been altered by misuse of
    privileged Docker daemon.

    Values:
      PRIVILEGED_MODE_UNSPECIFIED: Unspecified - this is treated as
        NON_PRIVILEGED_ONLY.
      NON_PRIVILEGED_ONLY: Users can only run builds using a non-privileged
        Docker daemon. This is suitable for most cases.
      PRIVILEGED_PERMITTED: Users are allowed to run builds using a privileged
        Docker daemon. This setting should be used with caution, as using a
        privileged Docker daemon introduces a security risk. A user would want
        this if they need to run "docker-in-docker", i.e. their builds use
        docker or docker-compose.
    """
    PRIVILEGED_MODE_UNSPECIFIED = 0
    NON_PRIVILEGED_ONLY = 1
    PRIVILEGED_PERMITTED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LoggingSasValue(_messages.Message):
    r"""Output only.

    Messages:
      AdditionalProperty: An additional property for a LoggingSasValue object.

    Fields:
      additionalProperties: Additional properties of type LoggingSasValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LoggingSasValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  loggingSas = _messages.MessageField('LoggingSasValue', 1)
  networkConfig = _messages.MessageField('GoogleDevtoolsCloudbuildV1NetworkConfig', 2)
  privilegedMode = _messages.EnumField('PrivilegedModeValueValuesEnum', 3)
  scalingConfig = _messages.MessageField('GoogleDevtoolsCloudbuildV1ScalingConfig', 4)
  securityConfig = _messages.MessageField('SecurityConfig', 5)
  workerConfig = _messages.MessageField('GoogleDevtoolsCloudbuildV1PrivatePoolConfigWorkerConfig', 6)
  workerPoolGroup = _messages.StringField(7)


class PrivatePoolV1Config(_messages.Message):
  r"""Configuration for a V1 `PrivatePool`.

  Fields:
    networkConfig: Network configuration for the pool.
    privateServiceConnect: Immutable. Private Service Connect(PSC) Network
      configuration for the pool.
    workerConfig: Machine configuration for the workers in the pool.
  """

  networkConfig = _messages.MessageField('NetworkConfig', 1)
  privateServiceConnect = _messages.MessageField('PrivateServiceConnect', 2)
  workerConfig = _messages.MessageField('WorkerConfig', 3)


class PrivateServiceConnect(_messages.Message):
  r"""Defines the Private Service Connect network configuration for the pool.

  Fields:
    networkAttachment: Required. Immutable. The network attachment that the
      worker network interface is peered to. Must be in the format `projects/{
      project}/regions/{region}/networkAttachments/{networkAttachment}`. The
      region of network attachment must be the same as the worker pool. See
      [Network Attachments](https://cloud.google.com/vpc/docs/about-network-
      attachments)
    publicIpAddressDisabled: Required. Immutable. Disable public IP on the
      primary network interface. If true, workers are created without any
      public address, which prevents network egress to public IPs unless a
      network proxy is configured. If false, workers are created with a public
      address which allows for public internet egress. The public address only
      applies to traffic through the primary network interface. If
      `route_all_traffic` is set to true, all traffic will go through the non-
      primary network interface, this boolean has no effect.
    routeAllTraffic: Immutable. Route all traffic through PSC interface.
      Enable this if you want full control of traffic in the private pool.
      Configure Cloud NAT for the subnet of network attachment if you need to
      access public Internet. If false, Only route RFC 1918 (10.0.0.0/8,
      **********/12, and ***********/16) and RFC 6598 (**********/10) through
      PSC interface.
  """

  networkAttachment = _messages.StringField(1)
  publicIpAddressDisabled = _messages.BooleanField(2)
  routeAllTraffic = _messages.BooleanField(3)


class ProcessAppManifestCallbackOperationMetadata(_messages.Message):
  r"""Metadata for `ProcessAppManifestCallback` operation.

  Fields:
    completeTime: Time the operation was completed.
    createTime: Time the operation was created.
    githubEnterpriseConfig: The resource name of the GitHubEnterprise to be
      created. Format:
      `projects/{project}/locations/{location}/githubEnterpriseConfigs/{id}`.
  """

  completeTime = _messages.StringField(1)
  createTime = _messages.StringField(2)
  githubEnterpriseConfig = _messages.StringField(3)


class PubsubConfig(_messages.Message):
  r"""PubsubConfig describes the configuration of a trigger that creates a
  build whenever a Pub/Sub message is published.

  Enums:
    StateValueValuesEnum: Potential issues with the underlying Pub/Sub
      subscription configuration. Only populated on get requests.

  Fields:
    serviceAccountEmail: Service account that will make the push request.
    state: Potential issues with the underlying Pub/Sub subscription
      configuration. Only populated on get requests.
    subscription: Output only. Name of the subscription. Format is
      `projects/{project}/subscriptions/{subscription}`.
    topic: Optional. The name of the topic from which this subscription is
      receiving messages. Format is `projects/{project}/topics/{topic}`.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Potential issues with the underlying Pub/Sub subscription
    configuration. Only populated on get requests.

    Values:
      STATE_UNSPECIFIED: The subscription configuration has not been checked.
      OK: The Pub/Sub subscription is properly configured.
      SUBSCRIPTION_DELETED: The subscription has been deleted.
      TOPIC_DELETED: The topic has been deleted.
      SUBSCRIPTION_MISCONFIGURED: Some of the subscription's field are
        misconfigured.
    """
    STATE_UNSPECIFIED = 0
    OK = 1
    SUBSCRIPTION_DELETED = 2
    TOPIC_DELETED = 3
    SUBSCRIPTION_MISCONFIGURED = 4

  serviceAccountEmail = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  subscription = _messages.StringField(3)
  topic = _messages.StringField(4)


class PullRequestFilter(_messages.Message):
  r"""PullRequestFilter contains filter properties for matching GitHub Pull
  Requests.

  Enums:
    CommentControlValueValuesEnum: If CommentControl is enabled, depending on
      the setting, builds may not fire until a repository writer comments
      `/gcbrun` on a pull request or `/gcbrun` is in the pull request
      description. Only PR comments that contain `/gcbrun` will trigger
      builds. If CommentControl is set to disabled, comments with `/gcbrun`
      from a user with repository write permission or above will still trigger
      builds to run.

  Fields:
    branch: Regex of branches to match. The syntax of the regular expressions
      accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
    commentControl: If CommentControl is enabled, depending on the setting,
      builds may not fire until a repository writer comments `/gcbrun` on a
      pull request or `/gcbrun` is in the pull request description. Only PR
      comments that contain `/gcbrun` will trigger builds. If CommentControl
      is set to disabled, comments with `/gcbrun` from a user with repository
      write permission or above will still trigger builds to run.
    invertRegex: If true, branches that do NOT match the git_ref will trigger
      a build.
  """

  class CommentControlValueValuesEnum(_messages.Enum):
    r"""If CommentControl is enabled, depending on the setting, builds may not
    fire until a repository writer comments `/gcbrun` on a pull request or
    `/gcbrun` is in the pull request description. Only PR comments that
    contain `/gcbrun` will trigger builds. If CommentControl is set to
    disabled, comments with `/gcbrun` from a user with repository write
    permission or above will still trigger builds to run.

    Values:
      COMMENTS_DISABLED: Do not require `/gcbrun` comments from a user with
        repository write permission or above on pull requests before builds
        are triggered. Comments that contain `/gcbrun` will still fire builds
        so this should be thought of as comments not required.
      COMMENTS_ENABLED: Builds will only fire in response to pull requests if:
        1. The pull request author has repository write permission or above
        and `/gcbrun` is in the PR description. 2. A user with repository
        writer permissions or above comments `/gcbrun` on a pull request
        authored by any user.
      COMMENTS_ENABLED_FOR_EXTERNAL_CONTRIBUTORS_ONLY: Builds will only fire
        in response to pull requests if: 1. The pull request author is a
        repository writer or above. 2. If the author does not have write
        permissions, a user with write permissions or above must comment
        `/gcbrun` in order to fire a build.
    """
    COMMENTS_DISABLED = 0
    COMMENTS_ENABLED = 1
    COMMENTS_ENABLED_FOR_EXTERNAL_CONTRIBUTORS_ONLY = 2

  branch = _messages.StringField(1)
  commentControl = _messages.EnumField('CommentControlValueValuesEnum', 2)
  invertRegex = _messages.BooleanField(3)


class PushFilter(_messages.Message):
  r"""Push contains filter properties for matching GitHub git pushes.

  Fields:
    branch: Regexes matching branches to build. The syntax of the regular
      expressions accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
    invertRegex: When true, only trigger a build if the revision regex does
      NOT match the git_ref regex.
    tag: Regexes matching tags to build. The syntax of the regular expressions
      accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
  """

  branch = _messages.StringField(1)
  invertRegex = _messages.BooleanField(2)
  tag = _messages.StringField(3)


class PythonPackage(_messages.Message):
  r"""Python package to upload to Artifact Registry upon successful completion
  of all build steps. A package can encapsulate multiple objects to be
  uploaded to a single repository.

  Fields:
    paths: Path globs used to match files in the build's workspace. For
      Python/ Twine, this is usually `dist/*`, and sometimes additionally an
      `.asc` file.
    repository: Artifact Registry repository, in the form "https://$REGION-
      python.pkg.dev/$PROJECT/$REPOSITORY" Files in the workspace matching any
      path pattern will be uploaded to Artifact Registry with this location as
      a prefix.
  """

  paths = _messages.StringField(1, repeated=True)
  repository = _messages.StringField(2)


class ReceiveTriggerWebhookResponse(_messages.Message):
  r"""ReceiveTriggerWebhookResponse [Experimental] is the response object for
  the ReceiveTriggerWebhook method.
  """



class RemoveBitbucketServerConnectedRepositoryRequest(_messages.Message):
  r"""RPC request object accepted by RemoveBitbucketServerConnectedRepository
  RPC method.

  Fields:
    connectedRepository: The connected repository to remove.
  """

  connectedRepository = _messages.MessageField('BitbucketServerRepositoryId', 1)


class RemoveGitLabConnectedRepositoryRequest(_messages.Message):
  r"""RPC request object accepted by RemoveGitLabConnectedRepository RPC
  method.

  Fields:
    connectedRepository: The connected repository to remove.
  """

  connectedRepository = _messages.MessageField('GitLabRepositoryId', 1)


class RepoSource(_messages.Message):
  r"""Location of the source in a Google Cloud Source Repository.

  Messages:
    SubstitutionsValue: Optional. Substitutions to use in a triggered build.
      Should only be used with RunBuildTrigger

  Fields:
    branchName: Regex matching branches to build. The syntax of the regular
      expressions accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
    commitSha: Explicit commit SHA to build.
    dir: Optional. Directory, relative to the source root, in which to run the
      build. This must be a relative path. If a step's `dir` is specified and
      is an absolute path, this value is ignored for that step's execution.
    invertRegex: Optional. Only trigger a build if the revision regex does NOT
      match the revision regex.
    projectId: Optional. ID of the project that owns the Cloud Source
      Repository. If omitted, the project ID requesting the build is assumed.
    repoName: Required. Name of the Cloud Source Repository.
    substitutions: Optional. Substitutions to use in a triggered build. Should
      only be used with RunBuildTrigger
    tagName: Regex matching tags to build. The syntax of the regular
      expressions accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SubstitutionsValue(_messages.Message):
    r"""Optional. Substitutions to use in a triggered build. Should only be
    used with RunBuildTrigger

    Messages:
      AdditionalProperty: An additional property for a SubstitutionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type SubstitutionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SubstitutionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  branchName = _messages.StringField(1)
  commitSha = _messages.StringField(2)
  dir = _messages.StringField(3)
  invertRegex = _messages.BooleanField(4)
  projectId = _messages.StringField(5)
  repoName = _messages.StringField(6)
  substitutions = _messages.MessageField('SubstitutionsValue', 7)
  tagName = _messages.StringField(8)


class RepositoryEventConfig(_messages.Message):
  r"""The configuration of a trigger that creates a build whenever an event
  from Repo API is received.

  Enums:
    RepositoryTypeValueValuesEnum: Output only. The type of the SCM vendor the
      repository points to.

  Fields:
    pullRequest: Filter to match changes in pull requests.
    push: Filter to match changes in refs like branches, tags.
    repository: The resource name of the Repo API resource.
    repositoryType: Output only. The type of the SCM vendor the repository
      points to.
  """

  class RepositoryTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the SCM vendor the repository points to.

    Values:
      REPOSITORY_TYPE_UNSPECIFIED: If unspecified, RepositoryType defaults to
        GITHUB.
      GITHUB: The SCM repo is GITHUB.
      GITHUB_ENTERPRISE: The SCM repo is GITHUB Enterprise.
      GITLAB_ENTERPRISE: The SCM repo is GITLAB Enterprise.
      BITBUCKET_DATA_CENTER: The SCM repo is BITBUCKET Data Center.
      BITBUCKET_CLOUD: The SCM repo is BITBUCKET Cloud.
    """
    REPOSITORY_TYPE_UNSPECIFIED = 0
    GITHUB = 1
    GITHUB_ENTERPRISE = 2
    GITLAB_ENTERPRISE = 3
    BITBUCKET_DATA_CENTER = 4
    BITBUCKET_CLOUD = 5

  pullRequest = _messages.MessageField('PullRequestFilter', 1)
  push = _messages.MessageField('PushFilter', 2)
  repository = _messages.StringField(3)
  repositoryType = _messages.EnumField('RepositoryTypeValueValuesEnum', 4)


class Results(_messages.Message):
  r"""Artifacts created by the build pipeline.

  Fields:
    artifactManifest: Path to the artifact manifest for non-container
      artifacts uploaded to Cloud Storage. Only populated when artifacts are
      uploaded to Cloud Storage.
    artifactTiming: Time to push all non-container artifacts to Cloud Storage.
    buildStepImages: List of build step digests, in the order corresponding to
      build step indices.
    buildStepOutputs: List of build step outputs, produced by builder images,
      in the order corresponding to build step indices. [Cloud
      Builders](https://cloud.google.com/cloud-build/docs/cloud-builders) can
      produce this output by writing to `$BUILDER_OUTPUT/output`. Only the
      first 50KB of data is stored. Note that the `$BUILDER_OUTPUT` variable
      is read-only and can't be substituted.
    goModules: Optional. Go module artifacts uploaded to Artifact Registry at
      the end of the build.
    images: Container images that were built as a part of the build.
    mavenArtifacts: Maven artifacts uploaded to Artifact Registry at the end
      of the build.
    npmPackages: Npm packages uploaded to Artifact Registry at the end of the
      build.
    numArtifacts: Number of non-container artifacts uploaded to Cloud Storage.
      Only populated when artifacts are uploaded to Cloud Storage.
    pythonPackages: Python artifacts uploaded to Artifact Registry at the end
      of the build.
  """

  artifactManifest = _messages.StringField(1)
  artifactTiming = _messages.MessageField('TimeSpan', 2)
  buildStepImages = _messages.StringField(3, repeated=True)
  buildStepOutputs = _messages.BytesField(4, repeated=True)
  goModules = _messages.MessageField('UploadedGoModule', 5, repeated=True)
  images = _messages.MessageField('BuiltImage', 6, repeated=True)
  mavenArtifacts = _messages.MessageField('UploadedMavenArtifact', 7, repeated=True)
  npmPackages = _messages.MessageField('UploadedNpmPackage', 8, repeated=True)
  numArtifacts = _messages.IntegerField(9)
  pythonPackages = _messages.MessageField('UploadedPythonPackage', 10, repeated=True)


class RetryBuildRequest(_messages.Message):
  r"""Specifies a build to retry.

  Fields:
    id: Required. Build ID of the original build.
    name: The name of the `Build` to retry. Format:
      `projects/{project}/locations/{location}/builds/{build}`
    projectId: Required. ID of the project.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2)
  projectId = _messages.StringField(3)


class RunBuildTriggerRequest(_messages.Message):
  r"""Specifies a build trigger to run and the source to use.

  Fields:
    projectId: Required. ID of the project.
    source: Source to build against this trigger. Branch and tag names cannot
      consist of regular expressions.
    triggerId: Required. ID of the trigger.
  """

  projectId = _messages.StringField(1)
  source = _messages.MessageField('RepoSource', 2)
  triggerId = _messages.StringField(3)


class Secret(_messages.Message):
  r"""Pairs a set of secret environment variables containing encrypted values
  with the Cloud KMS key to use to decrypt the value. Note: Use `kmsKeyName`
  with `available_secrets` instead of using `kmsKeyName` with `secret`. For
  instructions see: https://cloud.google.com/cloud-build/docs/securing-
  builds/use-encrypted-credentials.

  Messages:
    SecretEnvValue: Map of environment variable name to its encrypted value.
      Secret environment variables must be unique across all of a build's
      secrets, and must be used by at least one build step. Values can be at
      most 64 KB in size. There can be at most 100 secret values across all of
      a build's secrets.

  Fields:
    kmsKeyName: Cloud KMS key name to use to decrypt these envs.
    secretEnv: Map of environment variable name to its encrypted value. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step. Values can be at most 64 KB
      in size. There can be at most 100 secret values across all of a build's
      secrets.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SecretEnvValue(_messages.Message):
    r"""Map of environment variable name to its encrypted value. Secret
    environment variables must be unique across all of a build's secrets, and
    must be used by at least one build step. Values can be at most 64 KB in
    size. There can be at most 100 secret values across all of a build's
    secrets.

    Messages:
      AdditionalProperty: An additional property for a SecretEnvValue object.

    Fields:
      additionalProperties: Additional properties of type SecretEnvValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SecretEnvValue object.

      Fields:
        key: Name of the additional property.
        value: A byte attribute.
      """

      key = _messages.StringField(1)
      value = _messages.BytesField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  kmsKeyName = _messages.StringField(1)
  secretEnv = _messages.MessageField('SecretEnvValue', 2)


class SecretManagerSecret(_messages.Message):
  r"""Pairs a secret environment variable with a SecretVersion in Secret
  Manager.

  Fields:
    env: Environment variable name to associate with the secret. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step.
    versionName: Resource name of the SecretVersion. In format:
      projects/*/secrets/*/versions/*
  """

  env = _messages.StringField(1)
  versionName = _messages.StringField(2)


class Secrets(_messages.Message):
  r"""Secrets and secret environment variables.

  Fields:
    inline: Secrets encrypted with KMS key and the associated secret
      environment variable.
    secretManager: Secrets in Secret Manager and associated secret environment
      variable.
  """

  inline = _messages.MessageField('InlineSecret', 1, repeated=True)
  secretManager = _messages.MessageField('SecretManagerSecret', 2, repeated=True)


class SecurityConfig(_messages.Message):
  r"""Defines the security configuration for the pool.

  Fields:
    containerAnalysisStorage: Configuration for provenance storage in
      Container Analysis.
    provenancePublicKey: Output only. The public key that will be used to
      validate provenance generated by the `Workerpool`.
  """

  containerAnalysisStorage = _messages.MessageField('ContainerAnalysisStorage', 1)
  provenancePublicKey = _messages.StringField(2)


class ServiceDirectoryConfig(_messages.Message):
  r"""ServiceDirectoryConfig represents Service Directory configuration for a
  SCM host connection.

  Fields:
    service: The Service Directory service name. Format: projects/{project}/lo
      cations/{location}/namespaces/{namespace}/services/{service}.
  """

  service = _messages.StringField(1)


class Source(_messages.Message):
  r"""Location of the source in a supported storage service.

  Fields:
    buildConfigFileName: Path, from the source root, to the build
      configuration file (i.e. cloudbuild.yaml).
    connectedRepository: Optional. If provided, get the source from this 2nd-
      gen Google Cloud Build repository resource.
    developerConnectConfig: If provided, get the source from this Developer
      Connect config.
    gitSource: If provided, get the source from this Git repository.
    repoSource: If provided, get the source from this location in a Cloud
      Source Repository.
    storageSource: If provided, get the source from this location in Cloud
      Storage.
    storageSourceManifest: If provided, get the source from this manifest in
      Cloud Storage. This feature is in Preview; see description
      [here](https://github.com/GoogleCloudPlatform/cloud-
      builders/tree/master/gcs-fetcher).
  """

  buildConfigFileName = _messages.StringField(1)
  connectedRepository = _messages.MessageField('ConnectedRepository', 2)
  developerConnectConfig = _messages.MessageField('DeveloperConnectConfig', 3)
  gitSource = _messages.MessageField('GitSource', 4)
  repoSource = _messages.MessageField('RepoSource', 5)
  storageSource = _messages.MessageField('StorageSource', 6)
  storageSourceManifest = _messages.MessageField('StorageSourceManifest', 7)


class SourceProvenance(_messages.Message):
  r"""Provenance of the source. Ways to find the original source, or verify
  that some source was used for this build.

  Messages:
    FileHashesValue: Output only. Hash(es) of the build source, which can be
      used to verify that the original source integrity was maintained in the
      build. Note that `FileHashes` will only be populated if `BuildOptions`
      has requested a `SourceProvenanceHash`. The keys to this map are file
      paths used as build source and the values contain the hash values for
      those files. If the build source came in a single package such as a
      gzipped tarfile (`.tar.gz`), the `FileHash` will be for the single path
      to that file.

  Fields:
    fileHashes: Output only. Hash(es) of the build source, which can be used
      to verify that the original source integrity was maintained in the
      build. Note that `FileHashes` will only be populated if `BuildOptions`
      has requested a `SourceProvenanceHash`. The keys to this map are file
      paths used as build source and the values contain the hash values for
      those files. If the build source came in a single package such as a
      gzipped tarfile (`.tar.gz`), the `FileHash` will be for the single path
      to that file.
    resolvedConnectedRepository: Output only. A copy of the build's
      `source.connected_repository`, if exists, with any revisions resolved.
    resolvedGitSource: Output only. A copy of the build's `source.git_source`,
      if exists, with any revisions resolved.
    resolvedRepoSource: A copy of the build's `source.repo_source`, if exists,
      with any revisions resolved.
    resolvedStorageSource: A copy of the build's `source.storage_source`, if
      exists, with any generations resolved.
    resolvedStorageSourceManifest: A copy of the build's
      `source.storage_source_manifest`, if exists, with any revisions
      resolved. This feature is in Preview.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FileHashesValue(_messages.Message):
    r"""Output only. Hash(es) of the build source, which can be used to verify
    that the original source integrity was maintained in the build. Note that
    `FileHashes` will only be populated if `BuildOptions` has requested a
    `SourceProvenanceHash`. The keys to this map are file paths used as build
    source and the values contain the hash values for those files. If the
    build source came in a single package such as a gzipped tarfile
    (`.tar.gz`), the `FileHash` will be for the single path to that file.

    Messages:
      AdditionalProperty: An additional property for a FileHashesValue object.

    Fields:
      additionalProperties: Additional properties of type FileHashesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FileHashesValue object.

      Fields:
        key: Name of the additional property.
        value: A FileHashes attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('FileHashes', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  fileHashes = _messages.MessageField('FileHashesValue', 1)
  resolvedConnectedRepository = _messages.MessageField('ConnectedRepository', 2)
  resolvedGitSource = _messages.MessageField('GitSource', 3)
  resolvedRepoSource = _messages.MessageField('RepoSource', 4)
  resolvedStorageSource = _messages.MessageField('StorageSource', 5)
  resolvedStorageSourceManifest = _messages.MessageField('StorageSourceManifest', 6)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StepResult(_messages.Message):
  r"""StepResult is the declaration of a result for a build step.

  Fields:
    attestationContent: Optional. The content of the attestation to be
      generated.
    attestationType: Optional. The type of attestation to be generated.
    name: Required. The name of the result.
  """

  attestationContent = _messages.StringField(1)
  attestationType = _messages.StringField(2)
  name = _messages.StringField(3)


class StorageSource(_messages.Message):
  r"""Location of the source in an archive file in Cloud Storage.

  Enums:
    SourceFetcherValueValuesEnum: Optional. Option to specify the tool to
      fetch the source file for the build.

  Fields:
    bucket: Cloud Storage bucket containing the source (see [Bucket Name
      Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)).
    generation: Optional. Cloud Storage generation for the object. If the
      generation is omitted, the latest generation will be used.
    object: Required. Cloud Storage object containing the source. This object
      must be a zipped (`.zip`) or gzipped archive file (`.tar.gz`) containing
      source to build.
    sourceFetcher: Optional. Option to specify the tool to fetch the source
      file for the build.
  """

  class SourceFetcherValueValuesEnum(_messages.Enum):
    r"""Optional. Option to specify the tool to fetch the source file for the
    build.

    Values:
      SOURCE_FETCHER_UNSPECIFIED: Unspecified defaults to GSUTIL.
      GSUTIL: Use the "gsutil" tool to download the source file.
      GCS_FETCHER: Use the Cloud Storage Fetcher tool to download the source
        file.
    """
    SOURCE_FETCHER_UNSPECIFIED = 0
    GSUTIL = 1
    GCS_FETCHER = 2

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)
  sourceFetcher = _messages.EnumField('SourceFetcherValueValuesEnum', 4)


class StorageSourceManifest(_messages.Message):
  r"""Location of the source manifest in Cloud Storage. This feature is in
  Preview; see description
  [here](https://github.com/GoogleCloudPlatform/cloud-
  builders/tree/master/gcs-fetcher).

  Fields:
    bucket: Required. Cloud Storage bucket containing the source manifest (see
      [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)).
    generation: Cloud Storage generation for the object. If the generation is
      omitted, the latest generation will be used.
    object: Required. Cloud Storage object containing the source manifest.
      This object must be a JSON file.
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)


class TimeSpan(_messages.Message):
  r"""Start and end times for a build execution phase.

  Fields:
    endTime: End of time span.
    startTime: Start of time span.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class UpdateBitbucketServerConfigOperationMetadata(_messages.Message):
  r"""Metadata for `UpdateBitbucketServerConfig` operation.

  Fields:
    bitbucketServerConfig: The resource name of the BitbucketServerConfig to
      be updated. Format:
      `projects/{project}/locations/{location}/bitbucketServerConfigs/{id}`.
    completeTime: Time the operation was completed.
    createTime: Time the operation was created.
  """

  bitbucketServerConfig = _messages.StringField(1)
  completeTime = _messages.StringField(2)
  createTime = _messages.StringField(3)


class UpdateGitHubEnterpriseConfigOperationMetadata(_messages.Message):
  r"""Metadata for `UpdateGitHubEnterpriseConfig` operation.

  Fields:
    completeTime: Time the operation was completed.
    createTime: Time the operation was created.
    githubEnterpriseConfig: The resource name of the GitHubEnterprise to be
      updated. Format:
      `projects/{project}/locations/{location}/githubEnterpriseConfigs/{id}`.
  """

  completeTime = _messages.StringField(1)
  createTime = _messages.StringField(2)
  githubEnterpriseConfig = _messages.StringField(3)


class UpdateGitLabConfigOperationMetadata(_messages.Message):
  r"""Metadata for `UpdateGitLabConfig` operation.

  Fields:
    completeTime: Time the operation was completed.
    createTime: Time the operation was created.
    gitlabConfig: The resource name of the GitLabConfig to be created. Format:
      `projects/{project}/locations/{location}/gitlabConfigs/{id}`.
  """

  completeTime = _messages.StringField(1)
  createTime = _messages.StringField(2)
  gitlabConfig = _messages.StringField(3)


class UpdateWorkerPoolOperationMetadata(_messages.Message):
  r"""Metadata for the `UpdateWorkerPool` operation.

  Fields:
    completeTime: Time the operation was completed.
    createTime: Time the operation was created.
    workerPool: The resource name of the `WorkerPool` being updated. Format:
      `projects/{project}/locations/{location}/workerPools/{worker_pool}`.
  """

  completeTime = _messages.StringField(1)
  createTime = _messages.StringField(2)
  workerPool = _messages.StringField(3)


class UploadedGoModule(_messages.Message):
  r"""A Go module artifact uploaded to Artifact Registry using the GoModule
  directive.

  Fields:
    fileHashes: Hash types and values of the Go Module Artifact.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded artifact.
  """

  fileHashes = _messages.MessageField('FileHashes', 1)
  pushTiming = _messages.MessageField('TimeSpan', 2)
  uri = _messages.StringField(3)


class UploadedMavenArtifact(_messages.Message):
  r"""A Maven artifact uploaded using the MavenArtifact directive.

  Fields:
    fileHashes: Hash types and values of the Maven Artifact.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded artifact.
  """

  fileHashes = _messages.MessageField('FileHashes', 1)
  pushTiming = _messages.MessageField('TimeSpan', 2)
  uri = _messages.StringField(3)


class UploadedNpmPackage(_messages.Message):
  r"""An npm package uploaded to Artifact Registry using the NpmPackage
  directive.

  Fields:
    fileHashes: Hash types and values of the npm package.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded npm package.
  """

  fileHashes = _messages.MessageField('FileHashes', 1)
  pushTiming = _messages.MessageField('TimeSpan', 2)
  uri = _messages.StringField(3)


class UploadedPythonPackage(_messages.Message):
  r"""Artifact uploaded using the PythonPackage directive.

  Fields:
    fileHashes: Hash types and values of the Python Artifact.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded artifact.
  """

  fileHashes = _messages.MessageField('FileHashes', 1)
  pushTiming = _messages.MessageField('TimeSpan', 2)
  uri = _messages.StringField(3)


class Volume(_messages.Message):
  r"""Volume describes a Docker container volume which is mounted into build
  steps in order to persist files across build step execution.

  Fields:
    name: Name of the volume to mount. Volume names must be unique per build
      step and must be valid names for Docker volumes. Each named volume must
      be used by at least two build steps.
    path: Path at which to mount the volume. Paths must be absolute and cannot
      conflict with other volume paths on the same build step or with certain
      reserved volume paths.
  """

  name = _messages.StringField(1)
  path = _messages.StringField(2)


class Warning(_messages.Message):
  r"""A non-fatal problem encountered during the execution of the build.

  Enums:
    PriorityValueValuesEnum: The priority for this warning.

  Fields:
    priority: The priority for this warning.
    text: Explanation of the warning generated.
  """

  class PriorityValueValuesEnum(_messages.Enum):
    r"""The priority for this warning.

    Values:
      PRIORITY_UNSPECIFIED: Should not be used.
      INFO: e.g. deprecation warnings and alternative feature highlights.
      WARNING: e.g. automated detection of possible issues with the build.
      ALERT: e.g. alerts that a feature used in the build is pending removal
    """
    PRIORITY_UNSPECIFIED = 0
    INFO = 1
    WARNING = 2
    ALERT = 3

  priority = _messages.EnumField('PriorityValueValuesEnum', 1)
  text = _messages.StringField(2)


class WebhookConfig(_messages.Message):
  r"""WebhookConfig describes the configuration of a trigger that creates a
  build whenever a webhook is sent to a trigger's webhook URL.

  Enums:
    StateValueValuesEnum: Potential issues with the underlying Pub/Sub
      subscription configuration. Only populated on get requests.

  Fields:
    secret: Required. Resource name for the secret required as a URL
      parameter.
    state: Potential issues with the underlying Pub/Sub subscription
      configuration. Only populated on get requests.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Potential issues with the underlying Pub/Sub subscription
    configuration. Only populated on get requests.

    Values:
      STATE_UNSPECIFIED: The webhook auth configuration not been checked.
      OK: The auth configuration is properly setup.
      SECRET_DELETED: The secret provided in auth_method has been deleted.
    """
    STATE_UNSPECIFIED = 0
    OK = 1
    SECRET_DELETED = 2

  secret = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class WorkerConfig(_messages.Message):
  r"""Defines the configuration to be used for creating workers in the pool.

  Fields:
    diskSizeGb: Size of the disk attached to the worker, in GB. See [Worker
      pool config file](https://cloud.google.com/build/docs/private-
      pools/worker-pool-config-file-schema). Specify a value of up to 4000. If
      `0` is specified, Cloud Build will use a standard disk size.
    machineType: Optional. Machine type of a worker, such as `e2-medium`. See
      [Worker pool config file](https://cloud.google.com/build/docs/private-
      pools/worker-pool-config-file-schema). If left blank, Cloud Build will
      use a sensible default.
  """

  diskSizeGb = _messages.IntegerField(1)
  machineType = _messages.StringField(2)


class WorkerPool(_messages.Message):
  r"""Configuration for a `WorkerPool`. Cloud Build owns and maintains a pool
  of workers for general use and have no access to a project's private
  network. By default, builds submitted to Cloud Build will use a worker from
  this pool. If your build needs access to resources on a private network,
  create and use a `WorkerPool` to run your builds. Private `WorkerPool`s give
  your builds access to any single VPC network that you administer, including
  any on-prem resources connected to that VPC network. For an overview of
  private pools, see [Private pools
  overview](https://cloud.google.com/build/docs/private-pools/private-pools-
  overview).

  Enums:
    StateValueValuesEnum: Output only. `WorkerPool` state.

  Messages:
    AnnotationsValue: User specified annotations. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.

  Fields:
    annotations: User specified annotations. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    createTime: Output only. Time at which the request to create the
      `WorkerPool` was received.
    deleteTime: Output only. Time at which the request to delete the
      `WorkerPool` was received.
    displayName: A user-specified, human-readable name for the `WorkerPool`.
      If provided, this value must be 1-63 characters.
    etag: Output only. Checksum computed by the server. May be sent on update
      and delete requests to ensure that the client has an up-to-date value
      before proceeding.
    hybridPoolConfig: Hybrid pool configuration
    name: Output only. The resource name of the `WorkerPool`, with format
      `projects/{project}/locations/{location}/workerPools/{worker_pool}`. The
      value of `{worker_pool}` is provided by `worker_pool_id` in
      `CreateWorkerPool` request and the value of `{location}` is determined
      by the endpoint accessed.
    privatePoolConfig: Private Pool configuration for Cloud Build 2nd gen.
      DEPRECATED due to the cancellation of Cloud Build 2nd gen.
    privatePoolV1Config: Private Pool configuration.
    state: Output only. `WorkerPool` state.
    uid: Output only. A unique identifier for the `WorkerPool`.
    updateTime: Output only. Time at which the request to update the
      `WorkerPool` was received.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. `WorkerPool` state.

    Values:
      STATE_UNSPECIFIED: State of the `WorkerPool` is unknown.
      CREATING: `WorkerPool` is being created.
      RUNNING: `WorkerPool` is running.
      DELETING: `WorkerPool` is being deleted: cancelling builds and draining
        workers.
      DELETED: `WorkerPool` is deleted.
      UPDATING: `WorkerPool` is being updated; new builds cannot be run.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    RUNNING = 2
    DELETING = 3
    DELETED = 4
    UPDATING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""User specified annotations. See https://google.aip.dev/128#annotations
    for more details such as format and size limitations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  deleteTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  hybridPoolConfig = _messages.MessageField('HybridPoolConfig', 6)
  name = _messages.StringField(7)
  privatePoolConfig = _messages.MessageField('PrivatePoolConfig', 8)
  privatePoolV1Config = _messages.MessageField('PrivatePoolV1Config', 9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
