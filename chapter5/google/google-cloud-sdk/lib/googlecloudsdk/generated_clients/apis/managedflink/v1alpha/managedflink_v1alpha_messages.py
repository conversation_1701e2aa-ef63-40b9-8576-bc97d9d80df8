"""Generated message classes for managedflink version v1alpha.

Manages your jobs and deployment resources for BigQuery Engine for Apache
Flink.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'managedflink'


class AutotuningConfig(_messages.Message):
  r"""The autotuning configuration for the Apache Flink job.

  Fields:
    fixed: Fixed policy to disable autoscaling.
    throughputBased: The throughput-based autoscaling policy that leverages
      observed throughput and true processing rate (estimated maximum
      achievable throughput) to autoscale the task parallelism per job vertex.
  """

  fixed = _messages.MessageField('Fixed', 1)
  throughputBased = _messages.MessageField('Elastic', 2)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Deployment(_messages.Message):
  r"""Describes a BigQuery Engine for Apache Flink deployment resource.

  Enums:
    StateValueValuesEnum: Output only. The state of the deployment.

  Messages:
    LabelsValue: Optional. Labels as key value pairs.

  Fields:
    createTime: Output only. The time when the deployment was created.
    deploymentSpec: Optional. Spec that describes a deployment.
    displayName: Optional. The display name of the deployment.
    labels: Optional. Labels as key value pairs.
    name: Identifier. The name of the deployment.
    state: Output only. The state of the deployment.
    updateTime: Output only. The time when the deployment was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the deployment.

    Values:
      STATE_UNSPECIFIED: State unspecified.
      INITIALIZING: Deployment is initializing.
      CREATED: Deployment has been created.
      RUNNING: Deployment is running.
      TERMINATING: Deployment is terminating.
      TERMINATED: Deployment has terminated.
      FAILED: Deployment is in failed state.
      DELETING: Deployment is being deleted.
      FAILED_TO_DELETE: Deployment failed to delete.
    """
    STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    CREATED = 2
    RUNNING = 3
    TERMINATING = 4
    TERMINATED = 5
    FAILED = 6
    DELETING = 7
    FAILED_TO_DELETE = 8

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  deploymentSpec = _messages.MessageField('DeploymentSpec', 2)
  displayName = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  updateTime = _messages.StringField(7)


class DeploymentSpec(_messages.Message):
  r"""The specification of a deployment.

  Fields:
    limits: Optional. The limit constraints for the deployment.
    networkConfig: Optional. Network configuration for the deployment.
    secretsPaths: Optional. The list of secrets paths to be shared among all
      jobs in the deployment.
    workloadIdentity: Optional. Workload identity service account for the
      deployment. If not set, the default workload identity will be used.
  """

  limits = _messages.MessageField('Limits', 1)
  networkConfig = _messages.MessageField('NetworkConfig', 2)
  secretsPaths = _messages.StringField(3, repeated=True)
  workloadIdentity = _messages.StringField(4)


class DisplayGraph(_messages.Message):
  r"""The Apache Flink display graph of the job.

  Enums:
    StateValueValuesEnum: Output only. The state of the display graph.

  Fields:
    displayGraphJson: Output only. The display graph in JSON format.
    state: Output only. The state of the display graph.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the display graph.

    Values:
      STATE_UNSPECIFIED: Display graph state unspecified.
      DISPLAY_GRAPH_POPULATED: Display graph is populated.
      DISPLAY_GRAPH_TOO_LARGE: Display graph is too large.
      DISPLAY_GRAPH_EMPTY: Display graph is empty.
    """
    STATE_UNSPECIFIED = 0
    DISPLAY_GRAPH_POPULATED = 1
    DISPLAY_GRAPH_TOO_LARGE = 2
    DISPLAY_GRAPH_EMPTY = 3

  displayGraphJson = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class Elastic(_messages.Message):
  r"""Elastic autoscaling policy to enable autoscaling with min/max limits.

  Fields:
    maxParallelism: Optional. The maximum task parallelism for the job.
    minParallelism: Optional. The minimum task parallelism for the job.
    parallelism: Optional. The initial task parallelism for the job.
  """

  maxParallelism = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minParallelism = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  parallelism = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Fixed(_messages.Message):
  r"""Fixed policy to disable autoscaling.

  Fields:
    parallelism: Optional. The fixed task parallelism for the job.
  """

  parallelism = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class Job(_messages.Message):
  r"""Describes a BigQuery Engine for Apache Flink job resource.

  Enums:
    StateValueValuesEnum: Output only. The state of the Apache Flink job.

  Messages:
    LabelsValue: Optional. User defined labels as key value pairs.

  Fields:
    createTime: Output only. The time when the job was created.
    deploymentId: Optional. The deployment that this job belongs to. If empty,
      the job is created in an ephemeral on-demand Apache Flink cluster.
    displayGraph: Output only. The display graph of the Apache Flink job. This
      is only populated on a `GetAPI` call. It is not populated on a `ListAPI`
      call.
    jobSpec: Required. The specification of the Apache Flink job.
    labels: Optional. User defined labels as key value pairs.
    name: Identifier. The name of the job.
    state: Output only. The state of the Apache Flink job.
    updateTime: Output only. The time when the job was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the Apache Flink job.

    Values:
      STATE_UNSPECIFIED: State unspecified.
      PENDING_CREATION: Job is pending creation.
      INITIALIZING: Job is initializing.
      CREATED: Job has been created.
      RUNNING: Job is running.
      FAILING: Job is failing.
      FAILED: Job has failed.
      CANCELLING: Job is getting cancelled.
      CANCELLED: Job has been cancelled.
      FINISHED: Job has finished.
      RESTARTING: Job is restarting.
      SUSPENDED: Job has been suspended.
      RECONCILING: Job is reconciling.
      DELETING: Job is getting deleted.
      DELETION_FAILED: Job failed to be deleted.
      CANCELATION_FAILED: Job failed to be cancelled.
    """
    STATE_UNSPECIFIED = 0
    PENDING_CREATION = 1
    INITIALIZING = 2
    CREATED = 3
    RUNNING = 4
    FAILING = 5
    FAILED = 6
    CANCELLING = 7
    CANCELLED = 8
    FINISHED = 9
    RESTARTING = 10
    SUSPENDED = 11
    RECONCILING = 12
    DELETING = 13
    DELETION_FAILED = 14
    CANCELATION_FAILED = 15

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User defined labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  deploymentId = _messages.StringField(2)
  displayGraph = _messages.MessageField('DisplayGraph', 3)
  jobSpec = _messages.MessageField('JobSpec', 4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  updateTime = _messages.StringField(8)


class JobSpec(_messages.Message):
  r"""The specification of the Apache Flink job.

  Fields:
    artifactUris: Required. The list of URIs for the job artifact files in
      Cloud Storage.
    autotuningConfig: Optional. Autotuning configuration for the job.
    displayName: Optional. Display name of the job. This name is explicitly
      set by the user.
    jarUris: Optional. The list of URIs for the job JARs in Cloud Storage.
    jobGraphEncoding: Optional. The encoding type of the job graph.
    jobGraphUri: Required. The job graph URI in Cloud Storage.
    jobName: Optional. Job name of the job. The client automatically derives
      this name from the job graph.
    managedKafkaConfig: Optional. The configuration for the Google Cloud
      Managed Service for Apache Kafka clusters to be used by the job.
    networkConfig: Optional. Network configuration for the job.
    originalJobGraph: Optional. The original job graph in stringified json
      format.
    pipelineOptions: Optional. The pipeline options for the job in stringified
      json format.
    secretsPaths: Optional. The list of secrets paths to be used by an on-
      demand deployment job.
    workloadIdentity: Optional. Workload identity service account for the job.
      If not set, the default workload identity will be used. This is only
      used for on-demand jobs.
  """

  artifactUris = _messages.StringField(1, repeated=True)
  autotuningConfig = _messages.MessageField('AutotuningConfig', 2)
  displayName = _messages.StringField(3)
  jarUris = _messages.StringField(4, repeated=True)
  jobGraphEncoding = _messages.StringField(5)
  jobGraphUri = _messages.StringField(6)
  jobName = _messages.StringField(7)
  managedKafkaConfig = _messages.MessageField('ManagedKafkaConfig', 8)
  networkConfig = _messages.MessageField('NetworkConfig', 9)
  originalJobGraph = _messages.StringField(10)
  pipelineOptions = _messages.StringField(11)
  secretsPaths = _messages.StringField(12, repeated=True)
  workloadIdentity = _messages.StringField(13)


class Limits(_messages.Message):
  r"""The limit constraints of the deployment. The limits are based on the
  upper bounds for the different underlying resources.

  Fields:
    maxSlots: Optional. The maximum number of slots for the deployment.
  """

  maxSlots = _messages.IntegerField(1)


class ListDeploymentsResponse(_messages.Message):
  r"""Response to the request to list deployments.

  Fields:
    deployments: The list of deployments.
    nextPageToken: A token that identifies a page of results that the server
      should return.
    unreachable: Locations that could not be reached.
  """

  deployments = _messages.MessageField('Deployment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListJobsResponse(_messages.Message):
  r"""Response to the request to list jobs.

  Fields:
    jobs: The list of jobs.
    nextPageToken: A token that identifies a page of results that the server
      should return.
    unreachable: Locations that could not be reached.
  """

  jobs = _messages.MessageField('Job', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListSessionsResponse(_messages.Message):
  r"""Message for response to listing sessions.

  Fields:
    nextPageToken: The maximum number of sessions to return. The server might
      return fewer items than requested. If unspecified, at most 50 sessions
      are returned. The maximum value is 1000; values above 1000 are coerced
      to 1000.
    sessions: The list of sessions.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  sessions = _messages.MessageField('Session', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class ManagedKafkaConfig(_messages.Message):
  r"""The configuration for the Google Cloud Managed Service for Apache Kafka
  clusters to be used by the job.

  Fields:
    managedKafkaClusters: Optional. The list of Managed Service for Apache
      Kafka clusters connected to the job, specified in the following format:
      `projects//locations//clusters/`.
  """

  managedKafkaClusters = _messages.StringField(1, repeated=True)


class ManagedflinkProjectsLocationsDeploymentsCreateRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsDeploymentsCreateRequest object.

  Fields:
    deployment: A Deployment resource to be passed as the request body.
    deploymentId: Required. ID of the requesting object. If auto-generating ID
      server-side, remove this field and deployment_id from the
      method_signature of Create RPC
    parent: Required. The parent region in which to create the deployment,
      specified in the following format:
      `projects/{project}/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this behavior for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if the original operation with
      the same request ID was received. If the request was received, the
      server ignores the second request. This behavior prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  deployment = _messages.MessageField('Deployment', 1)
  deploymentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class ManagedflinkProjectsLocationsDeploymentsDeleteRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsDeploymentsDeleteRequest object.

  Fields:
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this behavior for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if the original operation with
      the same request ID was received. If the request was received, the
      server ignores the second request. This behavior prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class ManagedflinkProjectsLocationsDeploymentsGetRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsDeploymentsGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class ManagedflinkProjectsLocationsDeploymentsListRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsDeploymentsListRequest object.

  Fields:
    filter: Optional. An expression to filter the list of returned
      deployments. The expression must use the syntax as documented in
      [AIP-160](https://google.aip.dev/160).
    orderBy: Optional. A hint for how to order the results. The hint must be
      specified as documented in
      [AIP-132](https://google.aip.dev/132#ordering).
    pageSize: Optional. Requested page size. The server might return fewer
      items than requested. If unspecified, the server picks an appropriate
      default.
    pageToken: Optional. A token that identifies a page of results that the
      server should return.
    parent: Required. Parent value for `ListDeploymentsRequest`. The value is
      the location, specified in the following format:
      `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ManagedflinkProjectsLocationsDeploymentsPatchRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsDeploymentsPatchRequest object.

  Fields:
    deployment: A Deployment resource to be passed as the request body.
    name: Identifier. The name of the deployment.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this behavior for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if the original operation with
      the same request ID was received. If the request was received, the
      server ignores the second request. This behavior prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. The field mask is used to specify the fields to be
      overwritten in the deployment resource by the update. The fields
      specified in `update_mask` are relative to the resource, not to the full
      request. A field is overwritten if it's in the mask. If you don't
      provide a mask, then all fields are overwritten.
  """

  deployment = _messages.MessageField('Deployment', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class ManagedflinkProjectsLocationsGetRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ManagedflinkProjectsLocationsJobsCreateRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsJobsCreateRequest object.

  Fields:
    job: A Job resource to be passed as the request body.
    jobId: Required. The ID to use for the job, which becomes the final
      component of the job's name.
    parent: Required. The parent region in which to create the job, specified
      in the following format: `projects/{project}/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this behavior for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if the original operation with
      the same request ID was received. If the request was received, the
      server ignores the second request. This behavior prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  job = _messages.MessageField('Job', 1)
  jobId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class ManagedflinkProjectsLocationsJobsDeleteRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsJobsDeleteRequest object.

  Fields:
    force: Optional. If set to true, then the request will force delete on-
      demand job even if the job cancelation fails.
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this behavior for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if the original operation with
      the same request ID was received. If the request was received, the
      server ignores the second request. This behavior prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class ManagedflinkProjectsLocationsJobsGetRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsJobsGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class ManagedflinkProjectsLocationsJobsListRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsJobsListRequest object.

  Fields:
    filter: Optional. An expression to filter the list of returned jobs. The
      expression must use the syntax as documented in
      [AIP-160](https://google.aip.dev/160).
    orderBy: Optional. A hint for how to order the results. The hint must be
      specified as documented in
      [AIP-132](https://google.aip.dev/132#ordering).
    pageSize: Optional. Requested page size. The server might return fewer
      items than requested. If unspecified, the server picks an appropriate
      default.
    pageToken: Optional. A token that identifies a page of results that the
      server should return.
    parent: Required. Parent value for `ListJobsRequest`. The value is the
      location, specified in the following format:
      `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ManagedflinkProjectsLocationsJobsPatchRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsJobsPatchRequest object.

  Fields:
    job: A Job resource to be passed as the request body.
    name: Identifier. The name of the job.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this behavior for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if the original operation with
      the same request ID was received. If the request was received, the
      server ignores the second request. This behavior prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. The field mask is used to specify the fields to be
      overwritten in the job resource by the update. The fields specified in
      `update_mask` are relative to the resource, not to the full request. A
      field is overwritten if it's in the mask. If you don't provide a mask,
      then all fields are overwritten.
  """

  job = _messages.MessageField('Job', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class ManagedflinkProjectsLocationsListRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class ManagedflinkProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class ManagedflinkProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class ManagedflinkProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ManagedflinkProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ManagedflinkProjectsLocationsSessionsCreateRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsSessionsCreateRequest object.

  Fields:
    parent: Required. The parent region in which to create the session,
      specified in the following format:
      `projects/{project}/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this behavior for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if the original operation with
      the same request ID was received. If the request was received, the
      server ignores the second request. This behavior prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    session: A Session resource to be passed as the request body.
    sessionId: Required. ID to use for the session.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  session = _messages.MessageField('Session', 3)
  sessionId = _messages.StringField(4)


class ManagedflinkProjectsLocationsSessionsDeleteRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsSessionsDeleteRequest object.

  Fields:
    name: Required. Name of the session, specified in the following format:
      `projects/{project}/locations/{location}/sessions/{session}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this behavior for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if the original operation with
      the same request ID was received. If the request was received, the
      server ignores the second request. This behavior prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class ManagedflinkProjectsLocationsSessionsGetRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsSessionsGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class ManagedflinkProjectsLocationsSessionsListRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsSessionsListRequest object.

  Fields:
    filter: Optional. An expression to filter the list of returned sessions.
      For example, you can filter for active sessions. The expression must use
      the syntax as documented in [AIP-160](https://google.aip.dev/160).
    orderBy: Optional. A hint for how to order the results. For example, you
      can order by time of creation or last change. The hint must be specified
      as documented in [AIP-132](https://google.aip.dev/132#ordering).
    pageSize: Optional. Requested page size. The server might return fewer
      items than requested. If unspecified, the server picks an appropriate
      default.
    pageToken: Optional. A token that identifies a page of results that the
      server should return.
    parent: Required. Parent value for `ListSessionsRequest`. The value is the
      location, specified in the following format:
      `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ManagedflinkProjectsLocationsSessionsPatchRequest(_messages.Message):
  r"""A ManagedflinkProjectsLocationsSessionsPatchRequest object.

  Fields:
    name: Identifier. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this behavior for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if the original operation with
      the same request ID was received. If the request was received, the
      server ignores the second request. This behavior prevents clients from
      accidentally creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    session: A Session resource to be passed as the request body.
    updateMask: Required. The field mask is used to specify the fields to be
      overwritten in the session resource by the update. The fields specified
      in `update_mask` are relative to the resource, not to the full request.
      A field is overwritten if it's in the mask. If you don't provide a mask,
      then all fields are overwritten.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  session = _messages.MessageField('Session', 3)
  updateMask = _messages.StringField(4)


class NetworkConfig(_messages.Message):
  r"""The VPC, region, and subnetwork that can connect to the BigQuery Engine
  for Apache Flink cluster.

  Fields:
    subnetwork: Optional. The subnetwork of the resource. The format can be
      one of the following: 1:
      `projects/{project}/regions/{region}/subnetworks/{subnetwork_id}`. 2:
      `{subnetwork_id}`. With option 1, the subnetwork should be in the same
      region as the BigQuery Engine for Apache Flink Resource. With option 2,
      the subnetwork is assumed to be in the same project as the BigQuery
      Engine for Apache Flink Resource.
    vpc: Optional. The name of the VPC Network to associate the BigQuery
      Engine for Apache Flink resources with. The format can be one of the
      following: 1: `projects/{project}/global/networks/{network_id}`. 2:
      `{network_id}`. With option 2, the network is assumed to be in the same
      project as the BigQuery Engine for Apache Flink Resource.
  """

  subnetwork = _messages.StringField(1)
  vpc = _messages.StringField(2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of a long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Session(_messages.Message):
  r"""Describes an Apache Flink session object.

  Messages:
    LabelsValue: Optional. Labels as key value pairs.

  Fields:
    createTime: Output only. [Output only] Create timestamp.
    labels: Optional. Labels as key value pairs.
    name: Identifier. Name of the resource.
    stagingLocation: Optional. The staging location for artifacts in the
      session. This location can either be provided by the user, or the system
      will generate a default staging location.
    updateTime: Output only. [Output only] Update timestamp.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  stagingLocation = _messages.MessageField('StagingLocation', 4)
  updateTime = _messages.StringField(5)


class StagingLocation(_messages.Message):
  r"""The staging location for artifacts in an Apache Flink session.

  Fields:
    buckerProvider: Optional. The type of bucket used for staging artifacts in
      the session.
    bucket: Optional. The bucket to use for staging artifacts in the session.
    path: Optional. The path within the bucket to use for staging artifacts in
      the session.
  """

  buckerProvider = _messages.StringField(1)
  bucket = _messages.StringField(2)
  path = _messages.StringField(3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
