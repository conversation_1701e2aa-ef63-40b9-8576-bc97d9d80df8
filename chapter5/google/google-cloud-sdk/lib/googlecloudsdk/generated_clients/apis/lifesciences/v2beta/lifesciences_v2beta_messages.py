"""Generated message classes for lifesciences version v2beta.

Cloud Life Sciences is a suite of services and tools for managing, processing,
and transforming life sciences data.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'lifesciences'


class Accelerator(_messages.Message):
  r"""Carries information about an accelerator that can be attached to a VM.

  Fields:
    count: How many accelerators of this type to attach.
    type: The accelerator type string (for example, "nvidia-tesla-t4"). Only
      NVIDIA GPU accelerators are currently supported. If an NVIDIA GPU is
      attached, the required runtime libraries will be made available to all
      containers under `/usr/local/nvidia`. The driver version to install must
      be specified using the NVIDIA driver version parameter on the virtual
      machine specification. Note that attaching a GPU increases the worker VM
      startup time by a few minutes.
  """

  count = _messages.IntegerField(1)
  type = _messages.StringField(2)


class Action(_messages.Message):
  r"""Specifies a single action that runs a Docker container.

  Messages:
    EnvironmentValue: The environment to pass into the container. This
      environment is merged with values specified in the
      google.cloud.lifesciences.v2beta.Pipeline message, overwriting any
      duplicate values. In addition to the values passed here, a few other
      values are automatically injected into the environment. These cannot be
      hidden or overwritten. `GOOGLE_PIPELINE_FAILED` will be set to "1" if
      the pipeline failed because an action has exited with a non-zero status
      (and did not have the `IGNORE_EXIT_STATUS` flag set). This can be used
      to determine if additional debug or logging actions should execute.
      `GOOGLE_LAST_EXIT_STATUS` will be set to the exit status of the last
      non-background action that executed. This can be used by workflow engine
      authors to determine whether an individual action has succeeded or
      failed.
    LabelsValue: Labels to associate with the action. This field is provided
      to assist workflow engine authors in identifying actions (for example,
      to indicate what sort of action they perform, such as localization or
      debugging). They are returned in the operation metadata, but are
      otherwise ignored.
    PortMappingsValue: A map of containers to host port mappings for this
      container. If the container already specifies exposed ports, use the
      `PUBLISH_EXPOSED_PORTS` flag instead. The host port number must be less
      than 65536. If it is zero, an unused random port is assigned. To
      determine the resulting port number, consult the `ContainerStartedEvent`
      in the operation metadata.

  Fields:
    alwaysRun: By default, after an action fails, no further actions are run.
      This flag indicates that this action must be run even if the pipeline
      has already failed. This is useful for actions that copy output files
      off of the VM or for debugging. Note that no actions will be run if
      image prefetching fails.
    blockExternalNetwork: Prevents the container from accessing the external
      network.
    commands: If specified, overrides the `CMD` specified in the container. If
      the container also has an `ENTRYPOINT` the values are used as entrypoint
      arguments. Otherwise, they are used as a command and arguments to run
      inside the container.
    containerName: An optional name for the container. The container hostname
      will be set to this name, making it useful for inter-container
      communication. The name must contain only upper and lowercase
      alphanumeric characters and hyphens and cannot start with a hyphen.
    credentials: If the specified image is hosted on a private registry other
      than Google Container Registry, the credentials required to pull the
      image must be specified here as an encrypted secret. The secret must
      decrypt to a JSON-encoded dictionary containing both `username` and
      `password` keys.
    disableImagePrefetch: All container images are typically downloaded before
      any actions are executed. This helps prevent typos in URIs or issues
      like lack of disk space from wasting large amounts of compute resources.
      If set, this flag prevents the worker from downloading the image until
      just before the action is executed.
    disableStandardErrorCapture: A small portion of the container's standard
      error stream is typically captured and returned inside the
      `ContainerStoppedEvent`. Setting this flag disables this functionality.
    enableFuse: Enable access to the FUSE device for this action. Filesystems
      can then be mounted into disks shared with other actions. The other
      actions do not need the `enable_fuse` flag to access the mounted
      filesystem. This has the effect of causing the container to be executed
      with `CAP_SYS_ADMIN` and exposes `/dev/fuse` to the container, so use it
      only for containers you trust.
    encryptedEnvironment: The encrypted environment to pass into the
      container. This environment is merged with values specified in the
      google.cloud.lifesciences.v2beta.Pipeline message, overwriting any
      duplicate values. The secret must decrypt to a JSON-encoded dictionary
      where key-value pairs serve as environment variable names and their
      values. The decoded environment variables can overwrite the values
      specified by the `environment` field.
    entrypoint: If specified, overrides the `ENTRYPOINT` specified in the
      container.
    environment: The environment to pass into the container. This environment
      is merged with values specified in the
      google.cloud.lifesciences.v2beta.Pipeline message, overwriting any
      duplicate values. In addition to the values passed here, a few other
      values are automatically injected into the environment. These cannot be
      hidden or overwritten. `GOOGLE_PIPELINE_FAILED` will be set to "1" if
      the pipeline failed because an action has exited with a non-zero status
      (and did not have the `IGNORE_EXIT_STATUS` flag set). This can be used
      to determine if additional debug or logging actions should execute.
      `GOOGLE_LAST_EXIT_STATUS` will be set to the exit status of the last
      non-background action that executed. This can be used by workflow engine
      authors to determine whether an individual action has succeeded or
      failed.
    ignoreExitStatus: Normally, a non-zero exit status causes the pipeline to
      fail. This flag allows execution of other actions to continue instead.
    imageUri: Required. The URI to pull the container image from. Note that
      all images referenced by actions in the pipeline are pulled before the
      first action runs. If multiple actions reference the same image, it is
      only pulled once, ensuring that the same image is used for all actions
      in a single pipeline. The image URI can be either a complete host and
      image specification (e.g., quay.io/biocontainers/samtools), a library
      and image name (e.g., google/cloud-sdk) or a bare image name ('bash') to
      pull from the default library. No schema is required in any of these
      cases. If the specified image is not public, the service account
      specified for the Virtual Machine must have access to pull the images
      from GCR, or appropriate credentials must be specified in the
      google.cloud.lifesciences.v2beta.Action.credentials field.
    labels: Labels to associate with the action. This field is provided to
      assist workflow engine authors in identifying actions (for example, to
      indicate what sort of action they perform, such as localization or
      debugging). They are returned in the operation metadata, but are
      otherwise ignored.
    mounts: A list of mounts to make available to the action. In addition to
      the values specified here, every action has a special virtual disk
      mounted under `/google` that contains log files and other operational
      components. - /google/logs All logs written during the pipeline
      execution. - /google/logs/output The combined standard output and
      standard error of all actions run as part of the pipeline execution. -
      /google/logs/action/*/stdout The complete contents of each individual
      action's standard output. - /google/logs/action/*/stderr The complete
      contents of each individual action's standard error output.
    pidNamespace: An optional identifier for a PID namespace to run the action
      inside. Multiple actions should use the same string to share a
      namespace. If unspecified, a separate isolated namespace is used.
    portMappings: A map of containers to host port mappings for this
      container. If the container already specifies exposed ports, use the
      `PUBLISH_EXPOSED_PORTS` flag instead. The host port number must be less
      than 65536. If it is zero, an unused random port is assigned. To
      determine the resulting port number, consult the `ContainerStartedEvent`
      in the operation metadata.
    publishExposedPorts: Exposes all ports specified by `EXPOSE` statements in
      the container. To discover the host side port numbers, consult the
      `ACTION_STARTED` event in the operation metadata.
    runInBackground: This flag allows an action to continue running in the
      background while executing subsequent actions. This is useful to provide
      services to other actions (or to provide debugging support tools like
      SSH servers).
    timeout: The maximum amount of time to give the action to complete. If the
      action fails to complete before the timeout, it will be terminated and
      the exit status will be non-zero. The pipeline will continue or
      terminate based on the rules defined by the `ALWAYS_RUN` and
      `IGNORE_EXIT_STATUS` flags.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentValue(_messages.Message):
    r"""The environment to pass into the container. This environment is merged
    with values specified in the google.cloud.lifesciences.v2beta.Pipeline
    message, overwriting any duplicate values. In addition to the values
    passed here, a few other values are automatically injected into the
    environment. These cannot be hidden or overwritten.
    `GOOGLE_PIPELINE_FAILED` will be set to "1" if the pipeline failed because
    an action has exited with a non-zero status (and did not have the
    `IGNORE_EXIT_STATUS` flag set). This can be used to determine if
    additional debug or logging actions should execute.
    `GOOGLE_LAST_EXIT_STATUS` will be set to the exit status of the last non-
    background action that executed. This can be used by workflow engine
    authors to determine whether an individual action has succeeded or failed.

    Messages:
      AdditionalProperty: An additional property for a EnvironmentValue
        object.

    Fields:
      additionalProperties: Additional properties of type EnvironmentValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels to associate with the action. This field is provided to assist
    workflow engine authors in identifying actions (for example, to indicate
    what sort of action they perform, such as localization or debugging). They
    are returned in the operation metadata, but are otherwise ignored.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PortMappingsValue(_messages.Message):
    r"""A map of containers to host port mappings for this container. If the
    container already specifies exposed ports, use the `PUBLISH_EXPOSED_PORTS`
    flag instead. The host port number must be less than 65536. If it is zero,
    an unused random port is assigned. To determine the resulting port number,
    consult the `ContainerStartedEvent` in the operation metadata.

    Messages:
      AdditionalProperty: An additional property for a PortMappingsValue
        object.

    Fields:
      additionalProperties: Additional properties of type PortMappingsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PortMappingsValue object.

      Fields:
        key: Name of the additional property.
        value: A integer attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2, variant=_messages.Variant.INT32)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  alwaysRun = _messages.BooleanField(1)
  blockExternalNetwork = _messages.BooleanField(2)
  commands = _messages.StringField(3, repeated=True)
  containerName = _messages.StringField(4)
  credentials = _messages.MessageField('Secret', 5)
  disableImagePrefetch = _messages.BooleanField(6)
  disableStandardErrorCapture = _messages.BooleanField(7)
  enableFuse = _messages.BooleanField(8)
  encryptedEnvironment = _messages.MessageField('Secret', 9)
  entrypoint = _messages.StringField(10)
  environment = _messages.MessageField('EnvironmentValue', 11)
  ignoreExitStatus = _messages.BooleanField(12)
  imageUri = _messages.StringField(13)
  labels = _messages.MessageField('LabelsValue', 14)
  mounts = _messages.MessageField('Mount', 15, repeated=True)
  pidNamespace = _messages.StringField(16)
  portMappings = _messages.MessageField('PortMappingsValue', 17)
  publishExposedPorts = _messages.BooleanField(18)
  runInBackground = _messages.BooleanField(19)
  timeout = _messages.StringField(20)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ContainerKilledEvent(_messages.Message):
  r"""An event generated when a container is forcibly terminated by the
  worker. Currently, this only occurs when the container outlives the timeout
  specified by the user.

  Fields:
    actionId: The numeric ID of the action that started the container.
  """

  actionId = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class ContainerStartedEvent(_messages.Message):
  r"""An event generated when a container starts.

  Messages:
    PortMappingsValue: The container-to-host port mappings installed for this
      container. This set will contain any ports exposed using the
      `PUBLISH_EXPOSED_PORTS` flag as well as any specified in the `Action`
      definition.

  Fields:
    actionId: The numeric ID of the action that started this container.
    ipAddress: The public IP address that can be used to connect to the
      container. This field is only populated when at least one port mapping
      is present. If the instance was created with a private address, this
      field will be empty even if port mappings exist.
    portMappings: The container-to-host port mappings installed for this
      container. This set will contain any ports exposed using the
      `PUBLISH_EXPOSED_PORTS` flag as well as any specified in the `Action`
      definition.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PortMappingsValue(_messages.Message):
    r"""The container-to-host port mappings installed for this container. This
    set will contain any ports exposed using the `PUBLISH_EXPOSED_PORTS` flag
    as well as any specified in the `Action` definition.

    Messages:
      AdditionalProperty: An additional property for a PortMappingsValue
        object.

    Fields:
      additionalProperties: Additional properties of type PortMappingsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PortMappingsValue object.

      Fields:
        key: Name of the additional property.
        value: A integer attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2, variant=_messages.Variant.INT32)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  actionId = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  ipAddress = _messages.StringField(2)
  portMappings = _messages.MessageField('PortMappingsValue', 3)


class ContainerStoppedEvent(_messages.Message):
  r"""An event generated when a container exits.

  Fields:
    actionId: The numeric ID of the action that started this container.
    exitStatus: The exit status of the container.
    stderr: The tail end of any content written to standard error by the
      container. If the content emits large amounts of debugging noise or
      contains sensitive information, you can prevent the content from being
      printed by setting the `DISABLE_STANDARD_ERROR_CAPTURE` flag. Note that
      only a small amount of the end of the stream is captured here. The
      entire stream is stored in the `/google/logs` directory mounted into
      each action, and can be copied off the machine as described elsewhere.
  """

  actionId = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  exitStatus = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  stderr = _messages.StringField(3)


class DelayedEvent(_messages.Message):
  r"""An event generated whenever a resource limitation or transient error
  delays execution of a pipeline that was otherwise ready to run.

  Fields:
    cause: A textual description of the cause of the delay. The string can
      change without notice because it is often generated by another service
      (such as Compute Engine).
    metrics: If the delay was caused by a resource shortage, this field lists
      the Compute Engine metrics that are preventing this operation from
      running (for example, `CPUS` or `INSTANCES`). If the particular metric
      is not known, a single `UNKNOWN` metric will be present.
  """

  cause = _messages.StringField(1)
  metrics = _messages.StringField(2, repeated=True)


class Disk(_messages.Message):
  r"""Carries information about a disk that can be attached to a VM. See
  https://cloud.google.com/compute/docs/disks/performance for more information
  about disk type, size, and performance considerations. Specify either
  `Volume` or `Disk`, but not both.

  Fields:
    name: A user-supplied name for the disk. Used when mounting the disk into
      actions. The name must contain only upper and lowercase alphanumeric
      characters and hyphens and cannot start with a hyphen.
    sizeGb: The size, in GB, of the disk to attach. If the size is not
      specified, a default is chosen to ensure reasonable I/O performance. If
      the disk type is specified as `local-ssd`, multiple local drives are
      automatically combined to provide the requested size. Note, however,
      that each physical SSD is 375GB in size, and no more than 8 drives can
      be attached to a single instance.
    sourceImage: An optional image to put on the disk before attaching it to
      the VM.
    type: The Compute Engine disk type. If unspecified, `pd-standard` is used.
  """

  name = _messages.StringField(1)
  sizeGb = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  sourceImage = _messages.StringField(3)
  type = _messages.StringField(4)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Event(_messages.Message):
  r"""Carries information about events that occur during pipeline execution.

  Fields:
    containerKilled: See
      google.cloud.lifesciences.v2beta.ContainerKilledEvent.
    containerStarted: See
      google.cloud.lifesciences.v2beta.ContainerStartedEvent.
    containerStopped: See
      google.cloud.lifesciences.v2beta.ContainerStoppedEvent.
    delayed: See google.cloud.lifesciences.v2beta.DelayedEvent.
    description: A human-readable description of the event. Note that these
      strings can change at any time without notice. Any application logic
      must use the information in the `details` field.
    failed: See google.cloud.lifesciences.v2beta.FailedEvent.
    pullStarted: See google.cloud.lifesciences.v2beta.PullStartedEvent.
    pullStopped: See google.cloud.lifesciences.v2beta.PullStoppedEvent.
    timestamp: The time at which the event occurred.
    unexpectedExitStatus: See
      google.cloud.lifesciences.v2beta.UnexpectedExitStatusEvent.
    workerAssigned: See google.cloud.lifesciences.v2beta.WorkerAssignedEvent.
    workerReleased: See google.cloud.lifesciences.v2beta.WorkerReleasedEvent.
  """

  containerKilled = _messages.MessageField('ContainerKilledEvent', 1)
  containerStarted = _messages.MessageField('ContainerStartedEvent', 2)
  containerStopped = _messages.MessageField('ContainerStoppedEvent', 3)
  delayed = _messages.MessageField('DelayedEvent', 4)
  description = _messages.StringField(5)
  failed = _messages.MessageField('FailedEvent', 6)
  pullStarted = _messages.MessageField('PullStartedEvent', 7)
  pullStopped = _messages.MessageField('PullStoppedEvent', 8)
  timestamp = _messages.StringField(9)
  unexpectedExitStatus = _messages.MessageField('UnexpectedExitStatusEvent', 10)
  workerAssigned = _messages.MessageField('WorkerAssignedEvent', 11)
  workerReleased = _messages.MessageField('WorkerReleasedEvent', 12)


class ExistingDisk(_messages.Message):
  r"""Configuration for an existing disk to be attached to the VM.

  Fields:
    disk: If `disk` contains slashes, the Cloud Life Sciences API assumes that
      it is a complete URL for the disk. If `disk` does not contain slashes,
      the Cloud Life Sciences API assumes that the disk is a zonal disk and a
      URL will be generated of the form `zones//disks/`, where `` is the zone
      in which the instance is allocated. The disk must be ext4 formatted. If
      all `Mount` references to this disk have the `read_only` flag set to
      true, the disk will be attached in `read-only` mode and can be shared
      with other instances. Otherwise, the disk will be available for writing
      but cannot be shared.
  """

  disk = _messages.StringField(1)


class FailedEvent(_messages.Message):
  r"""An event generated when the execution of a pipeline has failed. Note
  that other events can continue to occur after this event.

  Enums:
    CodeValueValuesEnum: The Google standard error code that best describes
      this failure.

  Fields:
    cause: The human-readable description of the cause of the failure.
    code: The Google standard error code that best describes this failure.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""The Google standard error code that best describes this failure.

    Values:
      OK: Not an error; returned on success. HTTP Mapping: 200 OK
      CANCELLED: The operation was cancelled, typically by the caller. HTTP
        Mapping: 499 Client Closed Request
      UNKNOWN: Unknown error. For example, this error may be returned when a
        `Status` value received from another address space belongs to an error
        space that is not known in this address space. Also errors raised by
        APIs that do not return enough error information may be converted to
        this error. HTTP Mapping: 500 Internal Server Error
      INVALID_ARGUMENT: The client specified an invalid argument. Note that
        this differs from `FAILED_PRECONDITION`. `INVALID_ARGUMENT` indicates
        arguments that are problematic regardless of the state of the system
        (e.g., a malformed file name). HTTP Mapping: 400 Bad Request
      DEADLINE_EXCEEDED: The deadline expired before the operation could
        complete. For operations that change the state of the system, this
        error may be returned even if the operation has completed
        successfully. For example, a successful response from a server could
        have been delayed long enough for the deadline to expire. HTTP
        Mapping: 504 Gateway Timeout
      NOT_FOUND: Some requested entity (e.g., file or directory) was not
        found. Note to server developers: if a request is denied for an entire
        class of users, such as gradual feature rollout or undocumented
        allowlist, `NOT_FOUND` may be used. If a request is denied for some
        users within a class of users, such as user-based access control,
        `PERMISSION_DENIED` must be used. HTTP Mapping: 404 Not Found
      ALREADY_EXISTS: The entity that a client attempted to create (e.g., file
        or directory) already exists. HTTP Mapping: 409 Conflict
      PERMISSION_DENIED: The caller does not have permission to execute the
        specified operation. `PERMISSION_DENIED` must not be used for
        rejections caused by exhausting some resource (use
        `RESOURCE_EXHAUSTED` instead for those errors). `PERMISSION_DENIED`
        must not be used if the caller can not be identified (use
        `UNAUTHENTICATED` instead for those errors). This error code does not
        imply the request is valid or the requested entity exists or satisfies
        other pre-conditions. HTTP Mapping: 403 Forbidden
      UNAUTHENTICATED: The request does not have valid authentication
        credentials for the operation. HTTP Mapping: 401 Unauthorized
      RESOURCE_EXHAUSTED: Some resource has been exhausted, perhaps a per-user
        quota, or perhaps the entire file system is out of space. HTTP
        Mapping: 429 Too Many Requests
      FAILED_PRECONDITION: The operation was rejected because the system is
        not in a state required for the operation's execution. For example,
        the directory to be deleted is non-empty, an rmdir operation is
        applied to a non-directory, etc. Service implementors can use the
        following guidelines to decide between `FAILED_PRECONDITION`,
        `ABORTED`, and `UNAVAILABLE`: (a) Use `UNAVAILABLE` if the client can
        retry just the failing call. (b) Use `ABORTED` if the client should
        retry at a higher level. For example, when a client-specified test-
        and-set fails, indicating the client should restart a read-modify-
        write sequence. (c) Use `FAILED_PRECONDITION` if the client should not
        retry until the system state has been explicitly fixed. For example,
        if an "rmdir" fails because the directory is non-empty,
        `FAILED_PRECONDITION` should be returned since the client should not
        retry unless the files are deleted from the directory. HTTP Mapping:
        400 Bad Request
      ABORTED: The operation was aborted, typically due to a concurrency issue
        such as a sequencer check failure or transaction abort. See the
        guidelines above for deciding between `FAILED_PRECONDITION`,
        `ABORTED`, and `UNAVAILABLE`. HTTP Mapping: 409 Conflict
      OUT_OF_RANGE: The operation was attempted past the valid range. E.g.,
        seeking or reading past end-of-file. Unlike `INVALID_ARGUMENT`, this
        error indicates a problem that may be fixed if the system state
        changes. For example, a 32-bit file system will generate
        `INVALID_ARGUMENT` if asked to read at an offset that is not in the
        range [0,2^32-1], but it will generate `OUT_OF_RANGE` if asked to read
        from an offset past the current file size. There is a fair bit of
        overlap between `FAILED_PRECONDITION` and `OUT_OF_RANGE`. We recommend
        using `OUT_OF_RANGE` (the more specific error) when it applies so that
        callers who are iterating through a space can easily look for an
        `OUT_OF_RANGE` error to detect when they are done. HTTP Mapping: 400
        Bad Request
      UNIMPLEMENTED: The operation is not implemented or is not
        supported/enabled in this service. HTTP Mapping: 501 Not Implemented
      INTERNAL: Internal errors. This means that some invariants expected by
        the underlying system have been broken. This error code is reserved
        for serious errors. HTTP Mapping: 500 Internal Server Error
      UNAVAILABLE: The service is currently unavailable. This is most likely a
        transient condition, which can be corrected by retrying with a
        backoff. Note that it is not always safe to retry non-idempotent
        operations. See the guidelines above for deciding between
        `FAILED_PRECONDITION`, `ABORTED`, and `UNAVAILABLE`. HTTP Mapping: 503
        Service Unavailable
      DATA_LOSS: Unrecoverable data loss or corruption. HTTP Mapping: 500
        Internal Server Error
    """
    OK = 0
    CANCELLED = 1
    UNKNOWN = 2
    INVALID_ARGUMENT = 3
    DEADLINE_EXCEEDED = 4
    NOT_FOUND = 5
    ALREADY_EXISTS = 6
    PERMISSION_DENIED = 7
    UNAUTHENTICATED = 8
    RESOURCE_EXHAUSTED = 9
    FAILED_PRECONDITION = 10
    ABORTED = 11
    OUT_OF_RANGE = 12
    UNIMPLEMENTED = 13
    INTERNAL = 14
    UNAVAILABLE = 15
    DATA_LOSS = 16

  cause = _messages.StringField(1)
  code = _messages.EnumField('CodeValueValuesEnum', 2)


class LifesciencesProjectsLocationsGetRequest(_messages.Message):
  r"""A LifesciencesProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class LifesciencesProjectsLocationsListRequest(_messages.Message):
  r"""A LifesciencesProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class LifesciencesProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A LifesciencesProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class LifesciencesProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A LifesciencesProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class LifesciencesProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A LifesciencesProjectsLocationsOperationsListRequest object.

  Fields:
    filter: A string for filtering Operations. The following filter fields are
      supported: * createTime: The time this job was created * events: The set
      of event (names) that have occurred while running the pipeline. The :
      operator can be used to determine if a particular event has occurred. *
      error: If the pipeline is running, this value is NULL. Once the pipeline
      finishes, the value is the standard Google error code. * labels.key or
      labels."key with space" where key is a label key. * done: If the
      pipeline is running, this value is false. Once the pipeline finishes,
      the value is true.
    name: The name of the operation's parent resource.
    pageSize: The maximum number of results to return. The maximum value is
      256.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class LifesciencesProjectsLocationsPipelinesRunRequest(_messages.Message):
  r"""A LifesciencesProjectsLocationsPipelinesRunRequest object.

  Fields:
    parent: The project and location that this request should be executed
      against.
    runPipelineRequest: A RunPipelineRequest resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  runPipelineRequest = _messages.MessageField('RunPipelineRequest', 2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Metadata(_messages.Message):
  r"""Carries information about the pipeline execution that is returned in the
  long running operation's metadata field.

  Messages:
    LabelsValue: The user-defined labels associated with this operation.

  Fields:
    createTime: The time at which the operation was created by the API.
    endTime: The time at which execution was completed and resources were
      cleaned up.
    events: The list of events that have happened so far during the execution
      of this operation.
    labels: The user-defined labels associated with this operation.
    pipeline: The pipeline this operation represents.
    pubSubTopic: The name of the Cloud Pub/Sub topic where notifications of
      operation status changes are sent.
    startTime: The first time at which resources were allocated to execute the
      pipeline.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The user-defined labels associated with this operation.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  endTime = _messages.StringField(2)
  events = _messages.MessageField('Event', 3, repeated=True)
  labels = _messages.MessageField('LabelsValue', 4)
  pipeline = _messages.MessageField('Pipeline', 5)
  pubSubTopic = _messages.StringField(6)
  startTime = _messages.StringField(7)


class Mount(_messages.Message):
  r"""Carries information about a particular disk mount inside a container.

  Fields:
    disk: The name of the disk to mount, as specified in the resources
      section.
    path: The path to mount the disk inside the container.
    readOnly: If true, the disk is mounted read-only inside the container.
  """

  disk = _messages.StringField(1)
  path = _messages.StringField(2)
  readOnly = _messages.BooleanField(3)


class NFSMount(_messages.Message):
  r"""Configuration for an `NFSMount` to be attached to the VM.

  Fields:
    target: A target NFS mount. The target must be specified as
      `address:/mount".
  """

  target = _messages.StringField(1)


class Network(_messages.Message):
  r"""VM networking options.

  Fields:
    network: The network name to attach the VM's network interface to. The
      value will be prefixed with `global/networks/` unless it contains a `/`,
      in which case it is assumed to be a fully specified network resource
      URL. If unspecified, the global default network is used.
    subnetwork: If the specified network is configured for custom subnet
      creation, the name of the subnetwork to attach the instance to must be
      specified here. The value is prefixed with `regions/*/subnetworks/`
      unless it contains a `/`, in which case it is assumed to be a fully
      specified subnetwork resource URL. If the `*` character appears in the
      value, it is replaced with the region that the virtual machine has been
      allocated in.
    usePrivateAddress: If set to true, do not attach a public IP address to
      the VM. Note that without a public IP address, additional configuration
      is required to allow the VM to access Google services. See
      https://cloud.google.com/vpc/docs/configure-private-google-access for
      more information.
  """

  network = _messages.StringField(1)
  subnetwork = _messages.StringField(2)
  usePrivateAddress = _messages.BooleanField(3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: An Metadata object. This will always be returned with the
      Operation.
    ResponseValue: An Empty object.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: An Metadata object. This will always be returned with the
      Operation.
    name: The server-assigned name for the operation. This may be passed to
      the other operation methods to retrieve information about the
      operation's status.
    response: An Empty object.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""An Metadata object. This will always be returned with the Operation.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""An Empty object.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class PersistentDisk(_messages.Message):
  r"""Configuration for a persistent disk to be attached to the VM. See
  https://cloud.google.com/compute/docs/disks/performance for more information
  about disk type, size, and performance considerations.

  Fields:
    sizeGb: The size, in GB, of the disk to attach. If the size is not
      specified, a default is chosen to ensure reasonable I/O performance. If
      the disk type is specified as `local-ssd`, multiple local drives are
      automatically combined to provide the requested size. Note, however,
      that each physical SSD is 375GB in size, and no more than 8 drives can
      be attached to a single instance.
    sourceImage: An image to put on the disk before attaching it to the VM.
    type: The Compute Engine disk type. If unspecified, `pd-standard` is used.
  """

  sizeGb = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  sourceImage = _messages.StringField(2)
  type = _messages.StringField(3)


class Pipeline(_messages.Message):
  r"""Specifies a series of actions to execute, expressed as Docker
  containers.

  Messages:
    EnvironmentValue: The environment to pass into every action. Each action
      can also specify additional environment variables but cannot delete an
      entry from this map (though they can overwrite it with a different
      value).

  Fields:
    actions: The list of actions to execute, in the order they are specified.
    encryptedEnvironment: The encrypted environment to pass into every action.
      Each action can also specify its own encrypted environment. The secret
      must decrypt to a JSON-encoded dictionary where key-value pairs serve as
      environment variable names and their values. The decoded environment
      variables can overwrite the values specified by the `environment` field.
    environment: The environment to pass into every action. Each action can
      also specify additional environment variables but cannot delete an entry
      from this map (though they can overwrite it with a different value).
    resources: The resources required for execution.
    timeout: The maximum amount of time to give the pipeline to complete. This
      includes the time spent waiting for a worker to be allocated. If the
      pipeline fails to complete before the timeout, it will be cancelled and
      the error code will be set to DEADLINE_EXCEEDED. If unspecified, it will
      default to 7 days.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentValue(_messages.Message):
    r"""The environment to pass into every action. Each action can also
    specify additional environment variables but cannot delete an entry from
    this map (though they can overwrite it with a different value).

    Messages:
      AdditionalProperty: An additional property for a EnvironmentValue
        object.

    Fields:
      additionalProperties: Additional properties of type EnvironmentValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  actions = _messages.MessageField('Action', 1, repeated=True)
  encryptedEnvironment = _messages.MessageField('Secret', 2)
  environment = _messages.MessageField('EnvironmentValue', 3)
  resources = _messages.MessageField('Resources', 4)
  timeout = _messages.StringField(5)


class PullStartedEvent(_messages.Message):
  r"""An event generated when the worker starts pulling an image.

  Fields:
    imageUri: The URI of the image that was pulled.
  """

  imageUri = _messages.StringField(1)


class PullStoppedEvent(_messages.Message):
  r"""An event generated when the worker stops pulling an image.

  Fields:
    imageUri: The URI of the image that was pulled.
  """

  imageUri = _messages.StringField(1)


class Resources(_messages.Message):
  r"""The system resources for the pipeline run. At least one zone or region
  must be specified or the pipeline run will fail.

  Fields:
    regions: The list of regions allowed for VM allocation. If set, the
      `zones` field must not be set.
    virtualMachine: The virtual machine specification.
    zones: The list of zones allowed for VM allocation. If set, the `regions`
      field must not be set.
  """

  regions = _messages.StringField(1, repeated=True)
  virtualMachine = _messages.MessageField('VirtualMachine', 2)
  zones = _messages.StringField(3, repeated=True)


class RunPipelineRequest(_messages.Message):
  r"""The arguments to the `RunPipeline` method. The requesting user must have
  the `iam.serviceAccounts.actAs` permission for the Cloud Life Sciences
  service account or the request will fail.

  Messages:
    LabelsValue: User-defined labels to associate with the returned operation.
      These labels are not propagated to any Google Cloud Platform resources
      used by the operation, and can be modified at any time. To associate
      labels with resources created while executing the operation, see the
      appropriate resource message (for example, `VirtualMachine`).

  Fields:
    labels: User-defined labels to associate with the returned operation.
      These labels are not propagated to any Google Cloud Platform resources
      used by the operation, and can be modified at any time. To associate
      labels with resources created while executing the operation, see the
      appropriate resource message (for example, `VirtualMachine`).
    pipeline: Required. The description of the pipeline to run.
    pubSubTopic: The name of an existing Pub/Sub topic. The server will
      publish messages to this topic whenever the status of the operation
      changes. The Life Sciences Service Agent account must have publisher
      permissions to the specified topic or notifications will not be sent.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-defined labels to associate with the returned operation. These
    labels are not propagated to any Google Cloud Platform resources used by
    the operation, and can be modified at any time. To associate labels with
    resources created while executing the operation, see the appropriate
    resource message (for example, `VirtualMachine`).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  pipeline = _messages.MessageField('Pipeline', 2)
  pubSubTopic = _messages.StringField(3)


class RunPipelineResponse(_messages.Message):
  r"""The response to the RunPipeline method, returned in the operation's
  result field on success.
  """



class Secret(_messages.Message):
  r"""Holds encrypted information that is only decrypted and stored in RAM by
  the worker VM when running the pipeline.

  Fields:
    cipherText: The value of the cipherText response from the `encrypt`
      method. This field is intentionally unaudited.
    keyName: The name of the Cloud KMS key that will be used to decrypt the
      secret value. The VM service account must have the required permissions
      and authentication scopes to invoke the `decrypt` method on the
      specified key.
  """

  cipherText = _messages.StringField(1)
  keyName = _messages.StringField(2)


class ServiceAccount(_messages.Message):
  r"""Carries information about a Google Cloud service account.

  Fields:
    email: Email address of the service account. If not specified, the default
      Compute Engine service account for the project will be used.
    scopes: List of scopes to be enabled for this service account on the VM,
      in addition to the cloud-platform API scope that will be added by
      default.
  """

  email = _messages.StringField(1)
  scopes = _messages.StringField(2, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class UnexpectedExitStatusEvent(_messages.Message):
  r"""An event generated when the execution of a container results in a non-
  zero exit status that was not otherwise ignored. Execution will continue,
  but only actions that are flagged as `ALWAYS_RUN` will be executed. Other
  actions will be skipped.

  Fields:
    actionId: The numeric ID of the action that started the container.
    exitStatus: The exit status of the container.
  """

  actionId = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  exitStatus = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class VirtualMachine(_messages.Message):
  r"""Carries information about a Compute Engine VM resource.

  Messages:
    LabelsValue: Optional set of labels to apply to the VM and any attached
      disk resources. These labels must adhere to the [name and value
      restrictions](https://cloud.google.com/compute/docs/labeling-resources)
      on VM labels imposed by Compute Engine. Labels keys with the prefix
      'google-' are reserved for use by Google. Labels applied at creation
      time to the VM. Applied on a best-effort basis to attached disk
      resources shortly after VM creation.

  Fields:
    accelerators: The list of accelerators to attach to the VM.
    bootDiskSizeGb: The size of the boot disk, in GB. The boot disk must be
      large enough to accommodate all of the Docker images from each action in
      the pipeline at the same time. If not specified, a small but reasonable
      default value is used.
    bootImage: The host operating system image to use. Currently, only
      Container-Optimized OS images can be used. The default value is
      `projects/cos-cloud/global/images/family/cos-stable`, which selects the
      latest stable release of Container-Optimized OS. This option is provided
      to allow testing against the beta release of the operating system to
      ensure that the new version does not interact negatively with production
      pipelines. To test a pipeline against the beta release of Container-
      Optimized OS, use the value `projects/cos-
      cloud/global/images/family/cos-beta`.
    cpuPlatform: The CPU platform to request. An instance based on a newer
      platform can be allocated, but never one with fewer capabilities. The
      value of this parameter must be a valid Compute Engine CPU platform name
      (such as "Intel Skylake"). This parameter is only useful for carefully
      optimized work loads where the CPU platform has a significant impact.
      For more information about the effect of this parameter, see
      https://cloud.google.com/compute/docs/instances/specify-min-cpu-
      platform.
    disks: The list of disks to create and attach to the VM. Specify either
      the `volumes[]` field or the `disks[]` field, but not both.
    dockerCacheImages: The Compute Engine Disk Images to use as a Docker
      cache. The disks will be mounted into the Docker folder in a way that
      the images present in the cache will not need to be pulled. The digests
      of the cached images must match those of the tags used or the latest
      version will still be pulled. The root directory of the ext4 image must
      contain `image` and `overlay2` directories copied from the Docker
      directory of a VM where the desired Docker images have already been
      pulled. Any images pulled that are not cached will be stored on the
      first cache disk instead of the boot disk. Only a single image is
      supported.
    enableStackdriverMonitoring: Whether Stackdriver monitoring should be
      enabled on the VM.
    labels: Optional set of labels to apply to the VM and any attached disk
      resources. These labels must adhere to the [name and value
      restrictions](https://cloud.google.com/compute/docs/labeling-resources)
      on VM labels imposed by Compute Engine. Labels keys with the prefix
      'google-' are reserved for use by Google. Labels applied at creation
      time to the VM. Applied on a best-effort basis to attached disk
      resources shortly after VM creation.
    machineType: Required. The machine type of the virtual machine to create.
      Must be the short name of a standard machine type (such as
      "n1-standard-1") or a custom machine type (such as "custom-1-4096",
      where "1" indicates the number of vCPUs and "4096" indicates the memory
      in MB). See [Creating an instance with a custom machine
      type](https://cloud.google.com/compute/docs/instances/creating-instance-
      with-custom-machine-type#create) for more specifications on creating a
      custom machine type.
    network: The VM network configuration.
    nvidiaDriverVersion: The NVIDIA driver version to use when attaching an
      NVIDIA GPU accelerator. The version specified here must be compatible
      with the GPU libraries contained in the container being executed, and
      must be one of the drivers hosted in the `nvidia-drivers-us-public`
      bucket on Google Cloud Storage.
    preemptible: If true, allocate a preemptible VM.
    reservation: If specified, the VM will only be allocated inside the
      matching reservation. It will fail if the VM parameters don't match the
      reservation.
    serviceAccount: The service account to install on the VM. This account
      does not need any permissions other than those required by the pipeline.
    volumes: The list of disks and other storage to create or attach to the
      VM. Specify either the `volumes[]` field or the `disks[]` field, but not
      both.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional set of labels to apply to the VM and any attached disk
    resources. These labels must adhere to the [name and value
    restrictions](https://cloud.google.com/compute/docs/labeling-resources) on
    VM labels imposed by Compute Engine. Labels keys with the prefix 'google-'
    are reserved for use by Google. Labels applied at creation time to the VM.
    Applied on a best-effort basis to attached disk resources shortly after VM
    creation.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accelerators = _messages.MessageField('Accelerator', 1, repeated=True)
  bootDiskSizeGb = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  bootImage = _messages.StringField(3)
  cpuPlatform = _messages.StringField(4)
  disks = _messages.MessageField('Disk', 5, repeated=True)
  dockerCacheImages = _messages.StringField(6, repeated=True)
  enableStackdriverMonitoring = _messages.BooleanField(7)
  labels = _messages.MessageField('LabelsValue', 8)
  machineType = _messages.StringField(9)
  network = _messages.MessageField('Network', 10)
  nvidiaDriverVersion = _messages.StringField(11)
  preemptible = _messages.BooleanField(12)
  reservation = _messages.StringField(13)
  serviceAccount = _messages.MessageField('ServiceAccount', 14)
  volumes = _messages.MessageField('Volume', 15, repeated=True)


class Volume(_messages.Message):
  r"""Carries information about storage that can be attached to a VM. Specify
  either `Volume` or `Disk`, but not both.

  Fields:
    existingDisk: Configuration for a existing disk.
    nfsMount: Configuration for an NFS mount.
    persistentDisk: Configuration for a persistent disk.
    volume: A user-supplied name for the volume. Used when mounting the volume
      into `Actions`. The name must contain only upper and lowercase
      alphanumeric characters and hyphens and cannot start with a hyphen.
  """

  existingDisk = _messages.MessageField('ExistingDisk', 1)
  nfsMount = _messages.MessageField('NFSMount', 2)
  persistentDisk = _messages.MessageField('PersistentDisk', 3)
  volume = _messages.StringField(4)


class WorkerAssignedEvent(_messages.Message):
  r"""An event generated after a worker VM has been assigned to run the
  pipeline.

  Fields:
    instance: The worker's instance name.
    machineType: The machine type that was assigned for the worker.
    zone: The zone the worker is running in.
  """

  instance = _messages.StringField(1)
  machineType = _messages.StringField(2)
  zone = _messages.StringField(3)


class WorkerReleasedEvent(_messages.Message):
  r"""An event generated when the worker VM that was assigned to the pipeline
  has been released (deleted).

  Fields:
    instance: The worker's instance name.
    zone: The zone the worker was running in.
  """

  instance = _messages.StringField(1)
  zone = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
