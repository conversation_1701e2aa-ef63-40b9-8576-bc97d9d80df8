"""Generated client library for eventarc version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.eventarc.v1 import eventarc_v1_messages as messages


class EventarcV1(base_api.BaseApiClient):
  """Generated client library for service eventarc version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://eventarc.googleapis.com/'
  MTLS_BASE_URL = 'https://eventarc.mtls.googleapis.com/'

  _PACKAGE = 'eventarc'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'EventarcV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new eventarc handle."""
    url = url or self.BASE_URL
    super(EventarcV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_channelConnections = self.ProjectsLocationsChannelConnectionsService(self)
    self.projects_locations_channels = self.ProjectsLocationsChannelsService(self)
    self.projects_locations_enrollments = self.ProjectsLocationsEnrollmentsService(self)
    self.projects_locations_googleApiSources = self.ProjectsLocationsGoogleApiSourcesService(self)
    self.projects_locations_kafkaSources = self.ProjectsLocationsKafkaSourcesService(self)
    self.projects_locations_messageBuses = self.ProjectsLocationsMessageBusesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_pipelines = self.ProjectsLocationsPipelinesService(self)
    self.projects_locations_providers = self.ProjectsLocationsProvidersService(self)
    self.projects_locations_triggers = self.ProjectsLocationsTriggersService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsChannelConnectionsService(base_api.BaseApiService):
    """Service class for the projects_locations_channelConnections resource."""

    _NAME = 'projects_locations_channelConnections'

    def __init__(self, client):
      super(EventarcV1.ProjectsLocationsChannelConnectionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new ChannelConnection in a particular project and location.

      Args:
        request: (EventarcProjectsLocationsChannelConnectionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channelConnections',
        http_method='POST',
        method_id='eventarc.projects.locations.channelConnections.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['channelConnectionId'],
        relative_path='v1/{+parent}/channelConnections',
        request_field='channelConnection',
        request_type_name='EventarcProjectsLocationsChannelConnectionsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single ChannelConnection.

      Args:
        request: (EventarcProjectsLocationsChannelConnectionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channelConnections/{channelConnectionsId}',
        http_method='DELETE',
        method_id='eventarc.projects.locations.channelConnections.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsChannelConnectionsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a single ChannelConnection.

      Args:
        request: (EventarcProjectsLocationsChannelConnectionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ChannelConnection) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channelConnections/{channelConnectionsId}',
        http_method='GET',
        method_id='eventarc.projects.locations.channelConnections.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsChannelConnectionsGetRequest',
        response_type_name='ChannelConnection',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (EventarcProjectsLocationsChannelConnectionsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channelConnections/{channelConnectionsId}:getIamPolicy',
        http_method='GET',
        method_id='eventarc.projects.locations.channelConnections.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='EventarcProjectsLocationsChannelConnectionsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List channel connections.

      Args:
        request: (EventarcProjectsLocationsChannelConnectionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListChannelConnectionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channelConnections',
        http_method='GET',
        method_id='eventarc.projects.locations.channelConnections.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/channelConnections',
        request_field='',
        request_type_name='EventarcProjectsLocationsChannelConnectionsListRequest',
        response_type_name='ListChannelConnectionsResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (EventarcProjectsLocationsChannelConnectionsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channelConnections/{channelConnectionsId}:setIamPolicy',
        http_method='POST',
        method_id='eventarc.projects.locations.channelConnections.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='EventarcProjectsLocationsChannelConnectionsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (EventarcProjectsLocationsChannelConnectionsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channelConnections/{channelConnectionsId}:testIamPermissions',
        http_method='POST',
        method_id='eventarc.projects.locations.channelConnections.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='EventarcProjectsLocationsChannelConnectionsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsChannelsService(base_api.BaseApiService):
    """Service class for the projects_locations_channels resource."""

    _NAME = 'projects_locations_channels'

    def __init__(self, client):
      super(EventarcV1.ProjectsLocationsChannelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new channel in a particular project and location.

      Args:
        request: (EventarcProjectsLocationsChannelsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channels',
        http_method='POST',
        method_id='eventarc.projects.locations.channels.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['channelId', 'validateOnly'],
        relative_path='v1/{+parent}/channels',
        request_field='channel',
        request_type_name='EventarcProjectsLocationsChannelsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single channel.

      Args:
        request: (EventarcProjectsLocationsChannelsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channels/{channelsId}',
        http_method='DELETE',
        method_id='eventarc.projects.locations.channels.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsChannelsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a single Channel.

      Args:
        request: (EventarcProjectsLocationsChannelsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Channel) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channels/{channelsId}',
        http_method='GET',
        method_id='eventarc.projects.locations.channels.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsChannelsGetRequest',
        response_type_name='Channel',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (EventarcProjectsLocationsChannelsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channels/{channelsId}:getIamPolicy',
        http_method='GET',
        method_id='eventarc.projects.locations.channels.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='EventarcProjectsLocationsChannelsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List channels.

      Args:
        request: (EventarcProjectsLocationsChannelsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListChannelsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channels',
        http_method='GET',
        method_id='eventarc.projects.locations.channels.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/channels',
        request_field='',
        request_type_name='EventarcProjectsLocationsChannelsListRequest',
        response_type_name='ListChannelsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single channel.

      Args:
        request: (EventarcProjectsLocationsChannelsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channels/{channelsId}',
        http_method='PATCH',
        method_id='eventarc.projects.locations.channels.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='channel',
        request_type_name='EventarcProjectsLocationsChannelsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (EventarcProjectsLocationsChannelsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channels/{channelsId}:setIamPolicy',
        http_method='POST',
        method_id='eventarc.projects.locations.channels.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='EventarcProjectsLocationsChannelsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (EventarcProjectsLocationsChannelsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/channels/{channelsId}:testIamPermissions',
        http_method='POST',
        method_id='eventarc.projects.locations.channels.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='EventarcProjectsLocationsChannelsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsEnrollmentsService(base_api.BaseApiService):
    """Service class for the projects_locations_enrollments resource."""

    _NAME = 'projects_locations_enrollments'

    def __init__(self, client):
      super(EventarcV1.ProjectsLocationsEnrollmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new Enrollment in a particular project and location.

      Args:
        request: (EventarcProjectsLocationsEnrollmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/enrollments',
        http_method='POST',
        method_id='eventarc.projects.locations.enrollments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['enrollmentId', 'validateOnly'],
        relative_path='v1/{+parent}/enrollments',
        request_field='enrollment',
        request_type_name='EventarcProjectsLocationsEnrollmentsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single Enrollment.

      Args:
        request: (EventarcProjectsLocationsEnrollmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/enrollments/{enrollmentsId}',
        http_method='DELETE',
        method_id='eventarc.projects.locations.enrollments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsEnrollmentsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a single Enrollment.

      Args:
        request: (EventarcProjectsLocationsEnrollmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Enrollment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/enrollments/{enrollmentsId}',
        http_method='GET',
        method_id='eventarc.projects.locations.enrollments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsEnrollmentsGetRequest',
        response_type_name='Enrollment',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (EventarcProjectsLocationsEnrollmentsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/enrollments/{enrollmentsId}:getIamPolicy',
        http_method='GET',
        method_id='eventarc.projects.locations.enrollments.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='EventarcProjectsLocationsEnrollmentsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List Enrollments.

      Args:
        request: (EventarcProjectsLocationsEnrollmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEnrollmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/enrollments',
        http_method='GET',
        method_id='eventarc.projects.locations.enrollments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/enrollments',
        request_field='',
        request_type_name='EventarcProjectsLocationsEnrollmentsListRequest',
        response_type_name='ListEnrollmentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single Enrollment.

      Args:
        request: (EventarcProjectsLocationsEnrollmentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/enrollments/{enrollmentsId}',
        http_method='PATCH',
        method_id='eventarc.projects.locations.enrollments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='enrollment',
        request_type_name='EventarcProjectsLocationsEnrollmentsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (EventarcProjectsLocationsEnrollmentsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/enrollments/{enrollmentsId}:setIamPolicy',
        http_method='POST',
        method_id='eventarc.projects.locations.enrollments.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='EventarcProjectsLocationsEnrollmentsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (EventarcProjectsLocationsEnrollmentsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/enrollments/{enrollmentsId}:testIamPermissions',
        http_method='POST',
        method_id='eventarc.projects.locations.enrollments.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='EventarcProjectsLocationsEnrollmentsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGoogleApiSourcesService(base_api.BaseApiService):
    """Service class for the projects_locations_googleApiSources resource."""

    _NAME = 'projects_locations_googleApiSources'

    def __init__(self, client):
      super(EventarcV1.ProjectsLocationsGoogleApiSourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new GoogleApiSource in a particular project and location.

      Args:
        request: (EventarcProjectsLocationsGoogleApiSourcesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/googleApiSources',
        http_method='POST',
        method_id='eventarc.projects.locations.googleApiSources.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['googleApiSourceId', 'validateOnly'],
        relative_path='v1/{+parent}/googleApiSources',
        request_field='googleApiSource',
        request_type_name='EventarcProjectsLocationsGoogleApiSourcesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single GoogleApiSource.

      Args:
        request: (EventarcProjectsLocationsGoogleApiSourcesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/googleApiSources/{googleApiSourcesId}',
        http_method='DELETE',
        method_id='eventarc.projects.locations.googleApiSources.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsGoogleApiSourcesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a single GoogleApiSource.

      Args:
        request: (EventarcProjectsLocationsGoogleApiSourcesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiSource) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/googleApiSources/{googleApiSourcesId}',
        http_method='GET',
        method_id='eventarc.projects.locations.googleApiSources.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsGoogleApiSourcesGetRequest',
        response_type_name='GoogleApiSource',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (EventarcProjectsLocationsGoogleApiSourcesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/googleApiSources/{googleApiSourcesId}:getIamPolicy',
        http_method='GET',
        method_id='eventarc.projects.locations.googleApiSources.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='EventarcProjectsLocationsGoogleApiSourcesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List GoogleApiSources.

      Args:
        request: (EventarcProjectsLocationsGoogleApiSourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGoogleApiSourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/googleApiSources',
        http_method='GET',
        method_id='eventarc.projects.locations.googleApiSources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/googleApiSources',
        request_field='',
        request_type_name='EventarcProjectsLocationsGoogleApiSourcesListRequest',
        response_type_name='ListGoogleApiSourcesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single GoogleApiSource.

      Args:
        request: (EventarcProjectsLocationsGoogleApiSourcesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/googleApiSources/{googleApiSourcesId}',
        http_method='PATCH',
        method_id='eventarc.projects.locations.googleApiSources.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='googleApiSource',
        request_type_name='EventarcProjectsLocationsGoogleApiSourcesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (EventarcProjectsLocationsGoogleApiSourcesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/googleApiSources/{googleApiSourcesId}:setIamPolicy',
        http_method='POST',
        method_id='eventarc.projects.locations.googleApiSources.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='EventarcProjectsLocationsGoogleApiSourcesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (EventarcProjectsLocationsGoogleApiSourcesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/googleApiSources/{googleApiSourcesId}:testIamPermissions',
        http_method='POST',
        method_id='eventarc.projects.locations.googleApiSources.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='EventarcProjectsLocationsGoogleApiSourcesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsKafkaSourcesService(base_api.BaseApiService):
    """Service class for the projects_locations_kafkaSources resource."""

    _NAME = 'projects_locations_kafkaSources'

    def __init__(self, client):
      super(EventarcV1.ProjectsLocationsKafkaSourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new KafkaSource in a particular project and location.

      Args:
        request: (EventarcProjectsLocationsKafkaSourcesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/kafkaSources',
        http_method='POST',
        method_id='eventarc.projects.locations.kafkaSources.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['kafkaSourceId', 'validateOnly'],
        relative_path='v1/{+parent}/kafkaSources',
        request_field='kafkaSource',
        request_type_name='EventarcProjectsLocationsKafkaSourcesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single KafkaSource.

      Args:
        request: (EventarcProjectsLocationsKafkaSourcesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/kafkaSources/{kafkaSourcesId}',
        http_method='DELETE',
        method_id='eventarc.projects.locations.kafkaSources.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsKafkaSourcesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a single kafkaSource.

      Args:
        request: (EventarcProjectsLocationsKafkaSourcesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (KafkaSource) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/kafkaSources/{kafkaSourcesId}',
        http_method='GET',
        method_id='eventarc.projects.locations.kafkaSources.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsKafkaSourcesGetRequest',
        response_type_name='KafkaSource',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (EventarcProjectsLocationsKafkaSourcesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/kafkaSources/{kafkaSourcesId}:getIamPolicy',
        http_method='GET',
        method_id='eventarc.projects.locations.kafkaSources.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='EventarcProjectsLocationsKafkaSourcesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List kafkaSources.

      Args:
        request: (EventarcProjectsLocationsKafkaSourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListKafkaSourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/kafkaSources',
        http_method='GET',
        method_id='eventarc.projects.locations.kafkaSources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/kafkaSources',
        request_field='',
        request_type_name='EventarcProjectsLocationsKafkaSourcesListRequest',
        response_type_name='ListKafkaSourcesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single KafkaSource.

      Args:
        request: (EventarcProjectsLocationsKafkaSourcesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/kafkaSources/{kafkaSourcesId}',
        http_method='PATCH',
        method_id='eventarc.projects.locations.kafkaSources.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='kafkaSource',
        request_type_name='EventarcProjectsLocationsKafkaSourcesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (EventarcProjectsLocationsKafkaSourcesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/kafkaSources/{kafkaSourcesId}:setIamPolicy',
        http_method='POST',
        method_id='eventarc.projects.locations.kafkaSources.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='EventarcProjectsLocationsKafkaSourcesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (EventarcProjectsLocationsKafkaSourcesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/kafkaSources/{kafkaSourcesId}:testIamPermissions',
        http_method='POST',
        method_id='eventarc.projects.locations.kafkaSources.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='EventarcProjectsLocationsKafkaSourcesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsMessageBusesService(base_api.BaseApiService):
    """Service class for the projects_locations_messageBuses resource."""

    _NAME = 'projects_locations_messageBuses'

    def __init__(self, client):
      super(EventarcV1.ProjectsLocationsMessageBusesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new MessageBus in a particular project and location.

      Args:
        request: (EventarcProjectsLocationsMessageBusesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/messageBuses',
        http_method='POST',
        method_id='eventarc.projects.locations.messageBuses.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['messageBusId', 'validateOnly'],
        relative_path='v1/{+parent}/messageBuses',
        request_field='messageBus',
        request_type_name='EventarcProjectsLocationsMessageBusesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single message bus.

      Args:
        request: (EventarcProjectsLocationsMessageBusesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}',
        http_method='DELETE',
        method_id='eventarc.projects.locations.messageBuses.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsMessageBusesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a single MessageBus.

      Args:
        request: (EventarcProjectsLocationsMessageBusesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MessageBus) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}',
        http_method='GET',
        method_id='eventarc.projects.locations.messageBuses.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsMessageBusesGetRequest',
        response_type_name='MessageBus',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (EventarcProjectsLocationsMessageBusesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}:getIamPolicy',
        http_method='GET',
        method_id='eventarc.projects.locations.messageBuses.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='EventarcProjectsLocationsMessageBusesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List message buses.

      Args:
        request: (EventarcProjectsLocationsMessageBusesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMessageBusesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/messageBuses',
        http_method='GET',
        method_id='eventarc.projects.locations.messageBuses.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/messageBuses',
        request_field='',
        request_type_name='EventarcProjectsLocationsMessageBusesListRequest',
        response_type_name='ListMessageBusesResponse',
        supports_download=False,
    )

    def ListEnrollments(self, request, global_params=None):
      r"""List message bus enrollments.

      Args:
        request: (EventarcProjectsLocationsMessageBusesListEnrollmentsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMessageBusEnrollmentsResponse) The response message.
      """
      config = self.GetMethodConfig('ListEnrollments')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListEnrollments.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}:listEnrollments',
        http_method='GET',
        method_id='eventarc.projects.locations.messageBuses.listEnrollments',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}:listEnrollments',
        request_field='',
        request_type_name='EventarcProjectsLocationsMessageBusesListEnrollmentsRequest',
        response_type_name='ListMessageBusEnrollmentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single message bus.

      Args:
        request: (EventarcProjectsLocationsMessageBusesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}',
        http_method='PATCH',
        method_id='eventarc.projects.locations.messageBuses.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='messageBus',
        request_type_name='EventarcProjectsLocationsMessageBusesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (EventarcProjectsLocationsMessageBusesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}:setIamPolicy',
        http_method='POST',
        method_id='eventarc.projects.locations.messageBuses.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='EventarcProjectsLocationsMessageBusesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (EventarcProjectsLocationsMessageBusesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}:testIamPermissions',
        http_method='POST',
        method_id='eventarc.projects.locations.messageBuses.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='EventarcProjectsLocationsMessageBusesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(EventarcV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (EventarcProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='eventarc.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleLongrunningCancelOperationRequest',
        request_type_name='EventarcProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (EventarcProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='eventarc.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (EventarcProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='eventarc.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (EventarcProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='eventarc.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='EventarcProjectsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsPipelinesService(base_api.BaseApiService):
    """Service class for the projects_locations_pipelines resource."""

    _NAME = 'projects_locations_pipelines'

    def __init__(self, client):
      super(EventarcV1.ProjectsLocationsPipelinesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new Pipeline in a particular project and location.

      Args:
        request: (EventarcProjectsLocationsPipelinesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelines',
        http_method='POST',
        method_id='eventarc.projects.locations.pipelines.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pipelineId', 'validateOnly'],
        relative_path='v1/{+parent}/pipelines',
        request_field='pipeline',
        request_type_name='EventarcProjectsLocationsPipelinesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single pipeline.

      Args:
        request: (EventarcProjectsLocationsPipelinesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelines/{pipelinesId}',
        http_method='DELETE',
        method_id='eventarc.projects.locations.pipelines.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsPipelinesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a single Pipeline.

      Args:
        request: (EventarcProjectsLocationsPipelinesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Pipeline) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelines/{pipelinesId}',
        http_method='GET',
        method_id='eventarc.projects.locations.pipelines.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsPipelinesGetRequest',
        response_type_name='Pipeline',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (EventarcProjectsLocationsPipelinesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelines/{pipelinesId}:getIamPolicy',
        http_method='GET',
        method_id='eventarc.projects.locations.pipelines.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='EventarcProjectsLocationsPipelinesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List pipelines.

      Args:
        request: (EventarcProjectsLocationsPipelinesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPipelinesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelines',
        http_method='GET',
        method_id='eventarc.projects.locations.pipelines.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/pipelines',
        request_field='',
        request_type_name='EventarcProjectsLocationsPipelinesListRequest',
        response_type_name='ListPipelinesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single pipeline.

      Args:
        request: (EventarcProjectsLocationsPipelinesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelines/{pipelinesId}',
        http_method='PATCH',
        method_id='eventarc.projects.locations.pipelines.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='pipeline',
        request_type_name='EventarcProjectsLocationsPipelinesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (EventarcProjectsLocationsPipelinesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelines/{pipelinesId}:setIamPolicy',
        http_method='POST',
        method_id='eventarc.projects.locations.pipelines.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='EventarcProjectsLocationsPipelinesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (EventarcProjectsLocationsPipelinesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelines/{pipelinesId}:testIamPermissions',
        http_method='POST',
        method_id='eventarc.projects.locations.pipelines.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='EventarcProjectsLocationsPipelinesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsProvidersService(base_api.BaseApiService):
    """Service class for the projects_locations_providers resource."""

    _NAME = 'projects_locations_providers'

    def __init__(self, client):
      super(EventarcV1.ProjectsLocationsProvidersService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get a single Provider.

      Args:
        request: (EventarcProjectsLocationsProvidersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Provider) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/providers/{providersId}',
        http_method='GET',
        method_id='eventarc.projects.locations.providers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsProvidersGetRequest',
        response_type_name='Provider',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List providers.

      Args:
        request: (EventarcProjectsLocationsProvidersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListProvidersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/providers',
        http_method='GET',
        method_id='eventarc.projects.locations.providers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/providers',
        request_field='',
        request_type_name='EventarcProjectsLocationsProvidersListRequest',
        response_type_name='ListProvidersResponse',
        supports_download=False,
    )

  class ProjectsLocationsTriggersService(base_api.BaseApiService):
    """Service class for the projects_locations_triggers resource."""

    _NAME = 'projects_locations_triggers'

    def __init__(self, client):
      super(EventarcV1.ProjectsLocationsTriggersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new trigger in a particular project and location.

      Args:
        request: (EventarcProjectsLocationsTriggersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers',
        http_method='POST',
        method_id='eventarc.projects.locations.triggers.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['triggerId', 'validateOnly'],
        relative_path='v1/{+parent}/triggers',
        request_field='trigger',
        request_type_name='EventarcProjectsLocationsTriggersCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single trigger.

      Args:
        request: (EventarcProjectsLocationsTriggersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}',
        http_method='DELETE',
        method_id='eventarc.projects.locations.triggers.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsTriggersDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a single trigger.

      Args:
        request: (EventarcProjectsLocationsTriggersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Trigger) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}',
        http_method='GET',
        method_id='eventarc.projects.locations.triggers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsTriggersGetRequest',
        response_type_name='Trigger',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (EventarcProjectsLocationsTriggersGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}:getIamPolicy',
        http_method='GET',
        method_id='eventarc.projects.locations.triggers.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='EventarcProjectsLocationsTriggersGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List triggers.

      Args:
        request: (EventarcProjectsLocationsTriggersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTriggersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers',
        http_method='GET',
        method_id='eventarc.projects.locations.triggers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/triggers',
        request_field='',
        request_type_name='EventarcProjectsLocationsTriggersListRequest',
        response_type_name='ListTriggersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single trigger.

      Args:
        request: (EventarcProjectsLocationsTriggersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}',
        http_method='PATCH',
        method_id='eventarc.projects.locations.triggers.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='trigger',
        request_type_name='EventarcProjectsLocationsTriggersPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (EventarcProjectsLocationsTriggersSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}:setIamPolicy',
        http_method='POST',
        method_id='eventarc.projects.locations.triggers.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='EventarcProjectsLocationsTriggersSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (EventarcProjectsLocationsTriggersTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}:testIamPermissions',
        http_method='POST',
        method_id='eventarc.projects.locations.triggers.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='EventarcProjectsLocationsTriggersTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(EventarcV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (EventarcProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='eventarc.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def GetGoogleChannelConfig(self, request, global_params=None):
      r"""Get a GoogleChannelConfig. The name of the GoogleChannelConfig in the response is ALWAYS coded with projectID.

      Args:
        request: (EventarcProjectsLocationsGetGoogleChannelConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleChannelConfig) The response message.
      """
      config = self.GetMethodConfig('GetGoogleChannelConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetGoogleChannelConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/googleChannelConfig',
        http_method='GET',
        method_id='eventarc.projects.locations.getGoogleChannelConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='EventarcProjectsLocationsGetGoogleChannelConfigRequest',
        response_type_name='GoogleChannelConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (EventarcProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='eventarc.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='EventarcProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

    def UpdateGoogleChannelConfig(self, request, global_params=None):
      r"""Update a single GoogleChannelConfig.

      Args:
        request: (EventarcProjectsLocationsUpdateGoogleChannelConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleChannelConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateGoogleChannelConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateGoogleChannelConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/googleChannelConfig',
        http_method='PATCH',
        method_id='eventarc.projects.locations.updateGoogleChannelConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleChannelConfig',
        request_type_name='EventarcProjectsLocationsUpdateGoogleChannelConfigRequest',
        response_type_name='GoogleChannelConfig',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(EventarcV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
