"""Generated message classes for eventarc version v1.

Build event-driven applications on Google Cloud Platform.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'eventarc'


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class AuthenticationConfig(_messages.Message):
  r"""Authentication configuration.

  Fields:
    mutualTlsAuth: mTLS authentication configuration.
    saslAuth: SASL authentication configuration.
  """

  mutualTlsAuth = _messages.MessageField('MutualTlsAuthConfig', 1)
  saslAuth = _messages.MessageField('SaslAuthConfig', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class Channel(_messages.Message):
  r"""A representation of the Channel resource. A Channel is a resource on
  which event providers publish their events. The published events are
  delivered through the transport associated with the channel. Note that a
  channel is associated with exactly one event provider.

  Enums:
    StateValueValuesEnum: Output only. The state of a Channel.

  Messages:
    LabelsValue: Optional. Resource labels.

  Fields:
    activationToken: Output only. The activation token for the channel. The
      token must be used by the provider to register the channel for
      publishing.
    createTime: Output only. The creation time.
    cryptoKeyName: Resource name of a KMS crypto key (managed by the user)
      used to encrypt/decrypt their event data. It must match the pattern
      `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
    labels: Optional. Resource labels.
    name: Required. The resource name of the channel. Must be unique within
      the location on the project and must be in
      `projects/{project}/locations/{location}/channels/{channel_id}` format.
    provider: The name of the event provider (e.g. Eventarc SaaS partner)
      associated with the channel. This provider will be granted permissions
      to publish events to the channel. Format:
      `projects/{project}/locations/{location}/providers/{provider_id}`.
    pubsubTopic: Output only. The name of the Pub/Sub topic created and
      managed by Eventarc system as a transport for the event delivery.
      Format: `projects/{project}/topics/{topic_id}`.
    satisfiesPzs: Output only. Whether or not this Channel satisfies the
      requirements of physical zone separation
    state: Output only. The state of a Channel.
    uid: Output only. Server assigned unique identifier for the channel. The
      value is a UUID4 string and guaranteed to remain unchanged until the
      resource is deleted.
    updateTime: Output only. The last-modified time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of a Channel.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      PENDING: The PENDING state indicates that a Channel has been created
        successfully and there is a new activation token available for the
        subscriber to use to convey the Channel to the provider in order to
        create a Connection.
      ACTIVE: The ACTIVE state indicates that a Channel has been successfully
        connected with the event provider. An ACTIVE Channel is ready to
        receive and route events from the event provider.
      INACTIVE: The INACTIVE state indicates that the Channel cannot receive
        events permanently. There are two possible cases this state can
        happen: 1. The SaaS provider disconnected from this Channel. 2. The
        Channel activation token has expired but the SaaS provider wasn't
        connected. To re-establish a Connection with a provider, the
        subscriber should create a new Channel and give it to the provider.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    ACTIVE = 2
    INACTIVE = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  activationToken = _messages.StringField(1)
  createTime = _messages.StringField(2)
  cryptoKeyName = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  provider = _messages.StringField(6)
  pubsubTopic = _messages.StringField(7)
  satisfiesPzs = _messages.BooleanField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class ChannelConnection(_messages.Message):
  r"""A representation of the ChannelConnection resource. A ChannelConnection
  is a resource which event providers create during the activation process to
  establish a connection between the provider and the subscriber channel.

  Messages:
    LabelsValue: Optional. Resource labels.

  Fields:
    activationToken: Input only. Activation token for the channel. The token
      will be used during the creation of ChannelConnection to bind the
      channel with the provider project. This field will not be stored in the
      provider resource.
    channel: Required. The name of the connected subscriber Channel. This is a
      weak reference to avoid cross project and cross accounts references.
      This must be in
      `projects/{project}/location/{location}/channels/{channel_id}` format.
    createTime: Output only. The creation time.
    labels: Optional. Resource labels.
    name: Required. The name of the connection.
    uid: Output only. Server assigned ID of the resource. The server
      guarantees uniqueness and immutability until deleted.
    updateTime: Output only. The last-modified time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  activationToken = _messages.StringField(1)
  channel = _messages.StringField(2)
  createTime = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  uid = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class CloudRun(_messages.Message):
  r"""Represents a Cloud Run destination.

  Fields:
    job: Required. The name of the Cloud Run job to run. See
      https://cloud.google.com/run/docs/reference/rest/v1/namespaces.jobs.
      Only jobs located in the same project as the trigger object can be
      addressed. This field cannot be used with the `service` or `path`
      fields.
    path: Optional. The relative path on the Cloud Run service the events
      should be sent to. The value must conform to the definition of a URI
      path segment (section 3.3 of RFC2396). Examples: "/route", "route",
      "route/subroute".
    region: Required. The region the Cloud Run service is deployed in.
    service: Required. The name of the Cloud Run service being addressed. See
      https://cloud.google.com/run/docs/reference/rest/v1/namespaces.services.
      Only services located in the same project as the trigger object can be
      addressed.
  """

  job = _messages.StringField(1)
  path = _messages.StringField(2)
  region = _messages.StringField(3)
  service = _messages.StringField(4)


class Destination(_messages.Message):
  r"""Represents a target of an invocation over HTTP.

  Fields:
    cloudFunction: The Cloud Function resource name. Cloud Functions V1 and V2
      are supported. Format:
      `projects/{project}/locations/{location}/functions/{function}` This is a
      read-only field. Creating Cloud Functions V1/V2 triggers is only
      supported via the Cloud Functions product. An error will be returned if
      the user sets this value.
    cloudRun: Cloud Run fully-managed resource that receives the events. The
      resource should be in the same project as the trigger.
    gke: A GKE service capable of receiving events. The service should be
      running in the same project as the trigger.
    httpEndpoint: An HTTP endpoint destination described by an URI.
    networkConfig: Optional. Network config is used to configure how Eventarc
      resolves and connect to a destination. This should only be used with
      HttpEndpoint destination type.
    workflow: The resource name of the Workflow whose Executions are triggered
      by the events. The Workflow resource should be deployed in the same
      project as the trigger. Format:
      `projects/{project}/locations/{location}/workflows/{workflow}`
  """

  cloudFunction = _messages.StringField(1)
  cloudRun = _messages.MessageField('CloudRun', 2)
  gke = _messages.MessageField('GKE', 3)
  httpEndpoint = _messages.MessageField('HttpEndpoint', 4)
  networkConfig = _messages.MessageField('NetworkConfig', 5)
  workflow = _messages.StringField(6)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Enrollment(_messages.Message):
  r"""An enrollment represents a subscription for messages on a particular
  message bus. It defines a matching criteria for messages on the bus and the
  subscriber endpoint where matched messages should be delivered.

  Messages:
    AnnotationsValue: Optional. Resource annotations.
    LabelsValue: Optional. Resource labels.

  Fields:
    annotations: Optional. Resource annotations.
    celMatch: Required. A CEL expression identifying which messages this
      enrollment applies to.
    createTime: Output only. The creation time.
    destination: Required. Destination is the Pipeline that the Enrollment is
      delivering to. It must point to the full resource name of a Pipeline.
      Format:
      "projects/{PROJECT_ID}/locations/{region}/pipelines/{PIPELINE_ID)"
    displayName: Optional. Resource display name.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and might be sent only on update and delete
      requests to ensure that the client has an up-to-date value before
      proceeding.
    labels: Optional. Resource labels.
    messageBus: Required. Immutable. Resource name of the message bus
      identifying the source of the messages. It matches the form
      projects/{project}/locations/{location}/messageBuses/{messageBus}.
    name: Identifier. Resource name of the form
      projects/{project}/locations/{location}/enrollments/{enrollment}
    uid: Output only. Server assigned unique identifier for the channel. The
      value is a UUID4 string and guaranteed to remain unchanged until the
      resource is deleted.
    updateTime: Output only. The last-modified time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Resource annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  celMatch = _messages.StringField(2)
  createTime = _messages.StringField(3)
  destination = _messages.StringField(4)
  displayName = _messages.StringField(5)
  etag = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  messageBus = _messages.StringField(8)
  name = _messages.StringField(9)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class EventFilter(_messages.Message):
  r"""Filters events based on exact matches on the CloudEvents attributes.

  Fields:
    attribute: Required. The name of a CloudEvents attribute. Currently, only
      a subset of attributes are supported for filtering. You can [retrieve a
      specific provider's supported event types](/eventarc/docs/list-
      providers#describe-provider). All triggers MUST provide a filter for the
      'type' attribute.
    operator: Optional. The operator used for matching the events with the
      value of the filter. If not specified, only events that have an exact
      key-value pair specified in the filter are matched. The allowed values
      are `path_pattern` and `match-path-pattern`. `path_pattern` is only
      allowed for GCFv1 triggers.
    value: Required. The value for the attribute.
  """

  attribute = _messages.StringField(1)
  operator = _messages.StringField(2)
  value = _messages.StringField(3)


class EventType(_messages.Message):
  r"""A representation of the event type resource.

  Fields:
    description: Output only. Human friendly description of what the event
      type is about. For example "Bucket created in Cloud Storage".
    eventSchemaUri: Output only. URI for the event schema. For example
      "https://github.com/googleapis/google-cloudevents/blob/master/proto/goog
      le/events/cloud/storage/v1/events.proto"
    filteringAttributes: Output only. Filtering attributes for the event type.
    type: Output only. The full name of the event type (for example,
      "google.cloud.storage.object.v1.finalized"). In the form of {provider-
      specific-prefix}.{resource}.{version}.{verb}. Types MUST be versioned
      and event schemas are guaranteed to remain backward compatible within
      one version. Note that event type versions and API versions do not need
      to match.
  """

  description = _messages.StringField(1)
  eventSchemaUri = _messages.StringField(2)
  filteringAttributes = _messages.MessageField('FilteringAttribute', 3, repeated=True)
  type = _messages.StringField(4)


class EventarcProjectsLocationsChannelConnectionsCreateRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelConnectionsCreateRequest object.

  Fields:
    channelConnection: A ChannelConnection resource to be passed as the
      request body.
    channelConnectionId: Required. The user-provided ID to be assigned to the
      channel connection.
    parent: Required. The parent collection in which to add this channel
      connection.
  """

  channelConnection = _messages.MessageField('ChannelConnection', 1)
  channelConnectionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class EventarcProjectsLocationsChannelConnectionsDeleteRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelConnectionsDeleteRequest object.

  Fields:
    name: Required. The name of the channel connection to delete.
  """

  name = _messages.StringField(1, required=True)


class EventarcProjectsLocationsChannelConnectionsGetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelConnectionsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class EventarcProjectsLocationsChannelConnectionsGetRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelConnectionsGetRequest object.

  Fields:
    name: Required. The name of the channel connection to get.
  """

  name = _messages.StringField(1, required=True)


class EventarcProjectsLocationsChannelConnectionsListRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelConnectionsListRequest object.

  Fields:
    pageSize: The maximum number of channel connections to return on each
      page. Note: The service may send fewer responses.
    pageToken: The page token; provide the value from the `next_page_token`
      field in a previous `ListChannelConnections` call to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListChannelConnetions` match the call that provided the page token.
    parent: Required. The parent collection from which to list channel
      connections.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class EventarcProjectsLocationsChannelConnectionsSetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelConnectionsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class EventarcProjectsLocationsChannelConnectionsTestIamPermissionsRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelConnectionsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class EventarcProjectsLocationsChannelsCreateRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelsCreateRequest object.

  Fields:
    channel: A Channel resource to be passed as the request body.
    channelId: Required. The user-provided ID to be assigned to the channel.
    parent: Required. The parent collection in which to add this channel.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  channel = _messages.MessageField('Channel', 1)
  channelId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class EventarcProjectsLocationsChannelsDeleteRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelsDeleteRequest object.

  Fields:
    name: Required. The name of the channel to be deleted.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  name = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)


class EventarcProjectsLocationsChannelsGetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class EventarcProjectsLocationsChannelsGetRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelsGetRequest object.

  Fields:
    name: Required. The name of the channel to get.
  """

  name = _messages.StringField(1, required=True)


class EventarcProjectsLocationsChannelsListRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelsListRequest object.

  Fields:
    orderBy: The sorting order of the resources returned. Value should be a
      comma-separated list of fields. The default sorting order is ascending.
      To specify descending order for a field, append a `desc` suffix; for
      example: `name desc, channel_id`.
    pageSize: The maximum number of channels to return on each page. Note: The
      service may send fewer.
    pageToken: The page token; provide the value from the `next_page_token`
      field in a previous `ListChannels` call to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListChannels` must
      match the call that provided the page token.
    parent: Required. The parent collection to list channels on.
  """

  orderBy = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class EventarcProjectsLocationsChannelsPatchRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelsPatchRequest object.

  Fields:
    channel: A Channel resource to be passed as the request body.
    name: Required. The resource name of the channel. Must be unique within
      the location on the project and must be in
      `projects/{project}/locations/{location}/channels/{channel_id}` format.
    updateMask: The fields to be updated; only fields explicitly provided are
      updated. If no field mask is provided, all provided fields in the
      request are updated. To update all fields, provide a field mask of "*".
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  channel = _messages.MessageField('Channel', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class EventarcProjectsLocationsChannelsSetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class EventarcProjectsLocationsChannelsTestIamPermissionsRequest(_messages.Message):
  r"""A EventarcProjectsLocationsChannelsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class EventarcProjectsLocationsEnrollmentsCreateRequest(_messages.Message):
  r"""A EventarcProjectsLocationsEnrollmentsCreateRequest object.

  Fields:
    enrollment: A Enrollment resource to be passed as the request body.
    enrollmentId: Required. The user-provided ID to be assigned to the
      Enrollment. It should match the format
      `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    parent: Required. The parent collection in which to add this enrollment.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  enrollment = _messages.MessageField('Enrollment', 1)
  enrollmentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class EventarcProjectsLocationsEnrollmentsDeleteRequest(_messages.Message):
  r"""A EventarcProjectsLocationsEnrollmentsDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the Enrollment is not found,
      the request will succeed but no action will be taken on the server.
    etag: Optional. If provided, the Enrollment will only be deleted if the
      etag matches the current etag on the resource.
    name: Required. The name of the Enrollment to be deleted.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class EventarcProjectsLocationsEnrollmentsGetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsEnrollmentsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class EventarcProjectsLocationsEnrollmentsGetRequest(_messages.Message):
  r"""A EventarcProjectsLocationsEnrollmentsGetRequest object.

  Fields:
    name: Required. The name of the Enrollment to get.
  """

  name = _messages.StringField(1, required=True)


class EventarcProjectsLocationsEnrollmentsListRequest(_messages.Message):
  r"""A EventarcProjectsLocationsEnrollmentsListRequest object.

  Fields:
    filter: Optional. The filter field that the list request will filter on.
      Possible filtersare described in https://google.aip.dev/160.
    orderBy: Optional. The sorting order of the resources returned. Value
      should be a comma-separated list of fields. The default sorting order is
      ascending. To specify descending order for a field, append a `desc`
      suffix; for example: `name desc, update_time`.
    pageSize: Optional. The maximum number of results to return on each page.
      Note: The service may send fewer.
    pageToken: Optional. The page token; provide the value from the
      `next_page_token` field in a previous call to retrieve the subsequent
      page. When paginating, all other parameters provided must match the
      previous call that provided the page token.
    parent: Required. The parent collection to list triggers on.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EventarcProjectsLocationsEnrollmentsPatchRequest(_messages.Message):
  r"""A EventarcProjectsLocationsEnrollmentsPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the Enrollment is not found, a
      new Enrollment will be created. In this situation, `update_mask` is
      ignored.
    enrollment: A Enrollment resource to be passed as the request body.
    name: Identifier. Resource name of the form
      projects/{project}/locations/{location}/enrollments/{enrollment}
    updateMask: Optional. The fields to be updated; only fields explicitly
      provided are updated. If no field mask is provided, all provided fields
      in the request are updated. To update all fields, provide a field mask
      of "*".
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  allowMissing = _messages.BooleanField(1)
  enrollment = _messages.MessageField('Enrollment', 2)
  name = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class EventarcProjectsLocationsEnrollmentsSetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsEnrollmentsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class EventarcProjectsLocationsEnrollmentsTestIamPermissionsRequest(_messages.Message):
  r"""A EventarcProjectsLocationsEnrollmentsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class EventarcProjectsLocationsGetGoogleChannelConfigRequest(_messages.Message):
  r"""A EventarcProjectsLocationsGetGoogleChannelConfigRequest object.

  Fields:
    name: Required. The name of the config to get.
  """

  name = _messages.StringField(1, required=True)


class EventarcProjectsLocationsGetRequest(_messages.Message):
  r"""A EventarcProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class EventarcProjectsLocationsGoogleApiSourcesCreateRequest(_messages.Message):
  r"""A EventarcProjectsLocationsGoogleApiSourcesCreateRequest object.

  Fields:
    googleApiSource: A GoogleApiSource resource to be passed as the request
      body.
    googleApiSourceId: Required. The user-provided ID to be assigned to the
      GoogleApiSource. It should match the format
      `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    parent: Required. The parent collection in which to add this google api
      source.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  googleApiSource = _messages.MessageField('GoogleApiSource', 1)
  googleApiSourceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class EventarcProjectsLocationsGoogleApiSourcesDeleteRequest(_messages.Message):
  r"""A EventarcProjectsLocationsGoogleApiSourcesDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the MessageBus is not found,
      the request will succeed but no action will be taken on the server.
    etag: Optional. If provided, the MessageBus will only be deleted if the
      etag matches the current etag on the resource.
    name: Required. The name of the GoogleApiSource to be deleted.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class EventarcProjectsLocationsGoogleApiSourcesGetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsGoogleApiSourcesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class EventarcProjectsLocationsGoogleApiSourcesGetRequest(_messages.Message):
  r"""A EventarcProjectsLocationsGoogleApiSourcesGetRequest object.

  Fields:
    name: Required. The name of the google api source to get.
  """

  name = _messages.StringField(1, required=True)


class EventarcProjectsLocationsGoogleApiSourcesListRequest(_messages.Message):
  r"""A EventarcProjectsLocationsGoogleApiSourcesListRequest object.

  Fields:
    filter: Optional. The filter field that the list request will filter on.
      Possible filtersare described in https://google.aip.dev/160.
    orderBy: Optional. The sorting order of the resources returned. Value
      should be a comma-separated list of fields. The default sorting order is
      ascending. To specify descending order for a field, append a `desc`
      suffix; for example: `name desc, update_time`.
    pageSize: Optional. The maximum number of results to return on each page.
      Note: The service may send fewer.
    pageToken: Optional. The page token; provide the value from the
      `next_page_token` field in a previous call to retrieve the subsequent
      page. When paginating, all other parameters provided must match the
      previous call that provided the page token.
    parent: Required. The parent collection to list GoogleApiSources on.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EventarcProjectsLocationsGoogleApiSourcesPatchRequest(_messages.Message):
  r"""A EventarcProjectsLocationsGoogleApiSourcesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the GoogleApiSource is not
      found, a new GoogleApiSource will be created. In this situation,
      `update_mask` is ignored.
    googleApiSource: A GoogleApiSource resource to be passed as the request
      body.
    name: Identifier. Resource name of the form projects/{project}/locations/{
      location}/googleApiSources/{google_api_source}
    updateMask: Optional. The fields to be updated; only fields explicitly
      provided are updated. If no field mask is provided, all provided fields
      in the request are updated. To update all fields, provide a field mask
      of "*".
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  allowMissing = _messages.BooleanField(1)
  googleApiSource = _messages.MessageField('GoogleApiSource', 2)
  name = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class EventarcProjectsLocationsGoogleApiSourcesSetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsGoogleApiSourcesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class EventarcProjectsLocationsGoogleApiSourcesTestIamPermissionsRequest(_messages.Message):
  r"""A EventarcProjectsLocationsGoogleApiSourcesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class EventarcProjectsLocationsKafkaSourcesCreateRequest(_messages.Message):
  r"""A EventarcProjectsLocationsKafkaSourcesCreateRequest object.

  Fields:
    kafkaSource: A KafkaSource resource to be passed as the request body.
    kafkaSourceId: Required. The user-provided ID to be assigned to the
      KafkaSource. It should match the format
      `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    parent: Required. The parent collection in which to add this kafka source.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  kafkaSource = _messages.MessageField('KafkaSource', 1)
  kafkaSourceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class EventarcProjectsLocationsKafkaSourcesDeleteRequest(_messages.Message):
  r"""A EventarcProjectsLocationsKafkaSourcesDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the KafkaSource is not found,
      the request will succeed but no action will be taken on the server.
    etag: Optional. If provided, the KafkaSource will only be deleted if the
      etag matches the current etag on the resource.
    name: Required. The name of the KafkaSource to be deleted.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class EventarcProjectsLocationsKafkaSourcesGetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsKafkaSourcesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class EventarcProjectsLocationsKafkaSourcesGetRequest(_messages.Message):
  r"""A EventarcProjectsLocationsKafkaSourcesGetRequest object.

  Fields:
    name: Required. The name of the kafka source to get.
  """

  name = _messages.StringField(1, required=True)


class EventarcProjectsLocationsKafkaSourcesListRequest(_messages.Message):
  r"""A EventarcProjectsLocationsKafkaSourcesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return on each page.
      Note: The service may send fewer.
    pageToken: Optional. The page token; provide the value from the
      `next_page_token` field in a previous call to retrieve the subsequent
      page. When paginating, all other parameters provided must match the
      previous call that provided the page token.
    parent: Required. The parent collection to list triggers on.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class EventarcProjectsLocationsKafkaSourcesPatchRequest(_messages.Message):
  r"""A EventarcProjectsLocationsKafkaSourcesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the KafkaSource is not found,
      a new KafkaSource will be created. In this situation, `update_mask` is
      ignored.
    kafkaSource: A KafkaSource resource to be passed as the request body.
    name: Identifier. Resource name of the form
      projects/{project}/locations/{location}/kafkaSources/{kafka_source}
    updateMask: Optional. The fields to be updated; only fields explicitly
      provided are updated. If no field mask is provided, all provided fields
      in the request are updated. To update all fields, provide a field mask
      of "*".
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  allowMissing = _messages.BooleanField(1)
  kafkaSource = _messages.MessageField('KafkaSource', 2)
  name = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class EventarcProjectsLocationsKafkaSourcesSetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsKafkaSourcesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class EventarcProjectsLocationsKafkaSourcesTestIamPermissionsRequest(_messages.Message):
  r"""A EventarcProjectsLocationsKafkaSourcesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class EventarcProjectsLocationsListRequest(_messages.Message):
  r"""A EventarcProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class EventarcProjectsLocationsMessageBusesCreateRequest(_messages.Message):
  r"""A EventarcProjectsLocationsMessageBusesCreateRequest object.

  Fields:
    messageBus: A MessageBus resource to be passed as the request body.
    messageBusId: Required. The user-provided ID to be assigned to the
      MessageBus. It should match the format
      `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    parent: Required. The parent collection in which to add this message bus.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  messageBus = _messages.MessageField('MessageBus', 1)
  messageBusId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class EventarcProjectsLocationsMessageBusesDeleteRequest(_messages.Message):
  r"""A EventarcProjectsLocationsMessageBusesDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the MessageBus is not found,
      the request will succeed but no action will be taken on the server.
    etag: Optional. If provided, the MessageBus will only be deleted if the
      etag matches the current etag on the resource.
    name: Required. The name of the MessageBus to be deleted.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class EventarcProjectsLocationsMessageBusesGetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsMessageBusesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class EventarcProjectsLocationsMessageBusesGetRequest(_messages.Message):
  r"""A EventarcProjectsLocationsMessageBusesGetRequest object.

  Fields:
    name: Required. The name of the message bus to get.
  """

  name = _messages.StringField(1, required=True)


class EventarcProjectsLocationsMessageBusesListEnrollmentsRequest(_messages.Message):
  r"""A EventarcProjectsLocationsMessageBusesListEnrollmentsRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return on each page.
      Note: The service may send fewer.
    pageToken: Optional. The page token; provide the value from the
      `next_page_token` field in a previous call to retrieve the subsequent
      page. When paginating, all other parameters provided must match the
      previous call that provided the page token.
    parent: Required. The parent message bus to list enrollments on.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class EventarcProjectsLocationsMessageBusesListRequest(_messages.Message):
  r"""A EventarcProjectsLocationsMessageBusesListRequest object.

  Fields:
    filter: Optional. The filter field that the list request will filter on.
      Possible filtersare described in https://google.aip.dev/160.
    orderBy: Optional. The sorting order of the resources returned. Value
      should be a comma-separated list of fields. The default sorting order is
      ascending. To specify descending order for a field, append a `desc`
      suffix; for example: `name desc, update_time`.
    pageSize: Optional. The maximum number of results to return on each page.
      Note: The service may send fewer.
    pageToken: Optional. The page token; provide the value from the
      `next_page_token` field in a previous call to retrieve the subsequent
      page. When paginating, all other parameters provided must match the
      previous call that provided the page token.
    parent: Required. The parent collection to list message buses on.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EventarcProjectsLocationsMessageBusesPatchRequest(_messages.Message):
  r"""A EventarcProjectsLocationsMessageBusesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the MessageBus is not found, a
      new MessageBus will be created. In this situation, `update_mask` is
      ignored.
    messageBus: A MessageBus resource to be passed as the request body.
    name: Identifier. Resource name of the form
      projects/{project}/locations/{location}/messageBuses/{message_bus}
    updateMask: Optional. The fields to be updated; only fields explicitly
      provided are updated. If no field mask is provided, all provided fields
      in the request are updated. To update all fields, provide a field mask
      of "*".
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  allowMissing = _messages.BooleanField(1)
  messageBus = _messages.MessageField('MessageBus', 2)
  name = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class EventarcProjectsLocationsMessageBusesSetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsMessageBusesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class EventarcProjectsLocationsMessageBusesTestIamPermissionsRequest(_messages.Message):
  r"""A EventarcProjectsLocationsMessageBusesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class EventarcProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A EventarcProjectsLocationsOperationsCancelRequest object.

  Fields:
    googleLongrunningCancelOperationRequest: A
      GoogleLongrunningCancelOperationRequest resource to be passed as the
      request body.
    name: The name of the operation resource to be cancelled.
  """

  googleLongrunningCancelOperationRequest = _messages.MessageField('GoogleLongrunningCancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class EventarcProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A EventarcProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class EventarcProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A EventarcProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class EventarcProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A EventarcProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class EventarcProjectsLocationsPipelinesCreateRequest(_messages.Message):
  r"""A EventarcProjectsLocationsPipelinesCreateRequest object.

  Fields:
    parent: Required. The parent collection in which to add this pipeline.
    pipeline: A Pipeline resource to be passed as the request body.
    pipelineId: Required. The user-provided ID to be assigned to the Pipeline.
      It should match the format `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  parent = _messages.StringField(1, required=True)
  pipeline = _messages.MessageField('Pipeline', 2)
  pipelineId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class EventarcProjectsLocationsPipelinesDeleteRequest(_messages.Message):
  r"""A EventarcProjectsLocationsPipelinesDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the Pipeline is not found, the
      request will succeed but no action will be taken on the server.
    etag: Optional. If provided, the Pipeline will only be deleted if the etag
      matches the current etag on the resource.
    name: Required. The name of the Pipeline to be deleted.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class EventarcProjectsLocationsPipelinesGetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsPipelinesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class EventarcProjectsLocationsPipelinesGetRequest(_messages.Message):
  r"""A EventarcProjectsLocationsPipelinesGetRequest object.

  Fields:
    name: Required. The name of the pipeline to get.
  """

  name = _messages.StringField(1, required=True)


class EventarcProjectsLocationsPipelinesListRequest(_messages.Message):
  r"""A EventarcProjectsLocationsPipelinesListRequest object.

  Fields:
    filter: Optional. The filter field that the list request will filter on.
      Possible filters are described in https://google.aip.dev/160.
    orderBy: Optional. The sorting order of the resources returned. Value
      should be a comma-separated list of fields. The default sorting order is
      ascending. To specify descending order for a field, append a `desc`
      suffix; for example: `name desc, update_time`.
    pageSize: Optional. The maximum number of results to return on each page.
      Note: The service may send fewer.
    pageToken: Optional. The page token; provide the value from the
      `next_page_token` field in a previous call to retrieve the subsequent
      page. When paginating, all other parameters provided must match the
      previous call that provided the page token.
    parent: Required. The parent collection to list pipelines on.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EventarcProjectsLocationsPipelinesPatchRequest(_messages.Message):
  r"""A EventarcProjectsLocationsPipelinesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the Pipeline is not found, a
      new Pipeline will be created. In this situation, `update_mask` is
      ignored.
    name: Identifier. The resource name of the Pipeline. Must be unique within
      the location of the project and must be in
      `projects/{project}/locations/{location}/pipelines/{pipeline}` format.
    pipeline: A Pipeline resource to be passed as the request body.
    updateMask: Optional. The fields to be updated; only fields explicitly
      provided are updated. If no field mask is provided, all provided fields
      in the request are updated. To update all fields, provide a field mask
      of "*".
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  pipeline = _messages.MessageField('Pipeline', 3)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class EventarcProjectsLocationsPipelinesSetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsPipelinesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class EventarcProjectsLocationsPipelinesTestIamPermissionsRequest(_messages.Message):
  r"""A EventarcProjectsLocationsPipelinesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class EventarcProjectsLocationsProvidersGetRequest(_messages.Message):
  r"""A EventarcProjectsLocationsProvidersGetRequest object.

  Fields:
    name: Required. The name of the provider to get.
  """

  name = _messages.StringField(1, required=True)


class EventarcProjectsLocationsProvidersListRequest(_messages.Message):
  r"""A EventarcProjectsLocationsProvidersListRequest object.

  Fields:
    filter: The filter field that the list request will filter on.
    orderBy: The sorting order of the resources returned. Value should be a
      comma-separated list of fields. The default sorting oder is ascending.
      To specify descending order for a field, append a `desc` suffix; for
      example: `name desc, _id`.
    pageSize: The maximum number of providers to return on each page.
    pageToken: The page token; provide the value from the `next_page_token`
      field in a previous `ListProviders` call to retrieve the subsequent
      page. When paginating, all other parameters provided to `ListProviders`
      must match the call that provided the page token.
    parent: Required. The parent of the provider to get.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EventarcProjectsLocationsTriggersCreateRequest(_messages.Message):
  r"""A EventarcProjectsLocationsTriggersCreateRequest object.

  Fields:
    parent: Required. The parent collection in which to add this trigger.
    trigger: A Trigger resource to be passed as the request body.
    triggerId: Required. The user-provided ID to be assigned to the trigger.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  parent = _messages.StringField(1, required=True)
  trigger = _messages.MessageField('Trigger', 2)
  triggerId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class EventarcProjectsLocationsTriggersDeleteRequest(_messages.Message):
  r"""A EventarcProjectsLocationsTriggersDeleteRequest object.

  Fields:
    allowMissing: If set to true, and the trigger is not found, the request
      will succeed but no action will be taken on the server.
    etag: If provided, the trigger will only be deleted if the etag matches
      the current etag on the resource.
    name: Required. The name of the trigger to be deleted.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class EventarcProjectsLocationsTriggersGetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsTriggersGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class EventarcProjectsLocationsTriggersGetRequest(_messages.Message):
  r"""A EventarcProjectsLocationsTriggersGetRequest object.

  Fields:
    name: Required. The name of the trigger to get.
  """

  name = _messages.StringField(1, required=True)


class EventarcProjectsLocationsTriggersListRequest(_messages.Message):
  r"""A EventarcProjectsLocationsTriggersListRequest object.

  Fields:
    filter: Filter field. Used to filter the Triggers to be listed. Possible
      filters are described in https://google.aip.dev/160. For example, using
      "?filter=destination:gke" would list only Triggers with a gke
      destination.
    orderBy: The sorting order of the resources returned. Value should be a
      comma-separated list of fields. The default sorting order is ascending.
      To specify descending order for a field, append a `desc` suffix; for
      example: `name desc, trigger_id`.
    pageSize: The maximum number of triggers to return on each page. Note: The
      service may send fewer.
    pageToken: The page token; provide the value from the `next_page_token`
      field in a previous `ListTriggers` call to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListTriggers` must
      match the call that provided the page token.
    parent: Required. The parent collection to list triggers on.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EventarcProjectsLocationsTriggersPatchRequest(_messages.Message):
  r"""A EventarcProjectsLocationsTriggersPatchRequest object.

  Fields:
    allowMissing: If set to true, and the trigger is not found, a new trigger
      will be created. In this situation, `update_mask` is ignored.
    name: Required. The resource name of the trigger. Must be unique within
      the location of the project and must be in
      `projects/{project}/locations/{location}/triggers/{trigger}` format.
    trigger: A Trigger resource to be passed as the request body.
    updateMask: The fields to be updated; only fields explicitly provided are
      updated. If no field mask is provided, all provided fields in the
      request are updated. To update all fields, provide a field mask of "*".
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not post it.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  trigger = _messages.MessageField('Trigger', 3)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class EventarcProjectsLocationsTriggersSetIamPolicyRequest(_messages.Message):
  r"""A EventarcProjectsLocationsTriggersSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class EventarcProjectsLocationsTriggersTestIamPermissionsRequest(_messages.Message):
  r"""A EventarcProjectsLocationsTriggersTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class EventarcProjectsLocationsUpdateGoogleChannelConfigRequest(_messages.Message):
  r"""A EventarcProjectsLocationsUpdateGoogleChannelConfigRequest object.

  Fields:
    googleChannelConfig: A GoogleChannelConfig resource to be passed as the
      request body.
    name: Required. The resource name of the config. Must be in the format of,
      `projects/{project}/locations/{location}/googleChannelConfig`. In API
      responses, the config name always includes the projectID, regardless of
      whether the projectID or projectNumber was provided.
    updateMask: The fields to be updated; only fields explicitly provided are
      updated. If no field mask is provided, all provided fields in the
      request are updated. To update all fields, provide a field mask of "*".
  """

  googleChannelConfig = _messages.MessageField('GoogleChannelConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FilteringAttribute(_messages.Message):
  r"""A representation of the FilteringAttribute resource. Filtering
  attributes are per event type.

  Fields:
    attribute: Output only. Attribute used for filtering the event type.
    description: Output only. Description of the purpose of the attribute.
    pathPatternSupported: Output only. If true, the attribute accepts matching
      expressions in the Eventarc PathPattern format.
    required: Output only. If true, the triggers for this provider should
      always specify a filter on these attributes. Trigger creation will fail
      otherwise.
  """

  attribute = _messages.StringField(1)
  description = _messages.StringField(2)
  pathPatternSupported = _messages.BooleanField(3)
  required = _messages.BooleanField(4)


class GKE(_messages.Message):
  r"""Represents a GKE destination.

  Fields:
    cluster: Required. The name of the cluster the GKE service is running in.
      The cluster must be running in the same project as the trigger being
      created.
    location: Required. The name of the Google Compute Engine in which the
      cluster resides, which can either be compute zone (for example, us-
      central1-a) for the zonal clusters or region (for example, us-central1)
      for regional clusters.
    namespace: Required. The namespace the GKE service is running in.
    path: Optional. The relative path on the GKE service the events should be
      sent to. The value must conform to the definition of a URI path segment
      (section 3.3 of RFC2396). Examples: "/route", "route", "route/subroute".
    service: Required. Name of the GKE service.
  """

  cluster = _messages.StringField(1)
  location = _messages.StringField(2)
  namespace = _messages.StringField(3)
  path = _messages.StringField(4)
  service = _messages.StringField(5)


class GoogleApiSource(_messages.Message):
  r"""A GoogleApiSource represents a subscription of 1P events from a
  MessageBus.

  Messages:
    AnnotationsValue: Optional. Resource annotations.
    LabelsValue: Optional. Resource labels.

  Fields:
    annotations: Optional. Resource annotations.
    createTime: Output only. The creation time.
    cryptoKeyName: Optional. Resource name of a KMS crypto key (managed by the
      user) used to encrypt/decrypt their event data. It must match the
      pattern `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
    destination: Required. Destination is the message bus that the
      GoogleApiSource is delivering to. It must be point to the full resource
      name of a MessageBus. Format:
      "projects/{PROJECT_ID}/locations/{region}/messagesBuses/{MESSAGE_BUS_ID)
    displayName: Optional. Resource display name.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and might be sent only on update and delete
      requests to ensure that the client has an up-to-date value before
      proceeding.
    labels: Optional. Resource labels.
    loggingConfig: Optional. Config to control Platform logging for the
      GoogleApiSource.
    name: Identifier. Resource name of the form projects/{project}/locations/{
      location}/googleApiSources/{google_api_source}
    organizationSubscription: Optional. Config to enable subscribing to events
      from all projects in the GoogleApiSource's org.
    projectSubscriptions: Optional. Config to enable subscribing to all events
      from a list of projects. All the projects must be in the same org as the
      GoogleApiSource.
    uid: Output only. Server assigned unique identifier for the channel. The
      value is a UUID4 string and guaranteed to remain unchanged until the
      resource is deleted.
    updateTime: Output only. The last-modified time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Resource annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  cryptoKeyName = _messages.StringField(3)
  destination = _messages.StringField(4)
  displayName = _messages.StringField(5)
  etag = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  loggingConfig = _messages.MessageField('LoggingConfig', 8)
  name = _messages.StringField(9)
  organizationSubscription = _messages.MessageField('OrganizationSubscription', 10)
  projectSubscriptions = _messages.MessageField('ProjectSubscriptions', 11)
  uid = _messages.StringField(12)
  updateTime = _messages.StringField(13)


class GoogleChannelConfig(_messages.Message):
  r"""A GoogleChannelConfig is a resource that stores the custom settings
  respected by Eventarc first-party triggers in the matching region. Once
  configured, first-party event data will be protected using the specified
  custom managed encryption key instead of Google-managed encryption keys.

  Messages:
    LabelsValue: Optional. Resource labels.

  Fields:
    cryptoKeyName: Optional. Resource name of a KMS crypto key (managed by the
      user) used to encrypt/decrypt their event data. It must match the
      pattern `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
    labels: Optional. Resource labels.
    name: Required. The resource name of the config. Must be in the format of,
      `projects/{project}/locations/{location}/googleChannelConfig`. In API
      responses, the config name always includes the projectID, regardless of
      whether the projectID or projectNumber was provided.
    updateTime: Output only. The last-modified time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cryptoKeyName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  updateTime = _messages.StringField(4)


class GoogleCloudEventarcV1PipelineDestination(_messages.Message):
  r"""Represents a target of an invocation over HTTP.

  Fields:
    authenticationConfig: Optional. An authentication config used to
      authenticate message requests, such that destinations can verify the
      source. For example, this can be used with private GCP destinations that
      require GCP credentials to access like Cloud Run. This field is optional
      and should be set only by users interested in authenticated push
    httpEndpoint: Optional. An HTTP endpoint destination described by an URI.
      If a DNS FQDN is provided as the endpoint, Pipeline will create a
      peering zone to the consumer VPC and forward DNS requests to the VPC
      specified by network config to resolve the service endpoint. See:
      https://cloud.google.com/dns/docs/zones/zones-overview#peering_zones
    messageBus: Optional. The resource name of the Message Bus to which events
      should be published. The Message Bus resource should exist in the same
      project as the Pipeline. Format:
      `projects/{project}/locations/{location}/messageBuses/{message_bus}`
    networkConfig: Optional. Network config is used to configure how Pipeline
      resolves and connects to a destination.
    outputPayloadFormat: Optional. The message format before it is delivered
      to the destination. If not set, the message will be delivered in the
      format it was originally delivered to the Pipeline. This field can only
      be set if Pipeline.input_payload_format is also set.
    topic: Optional. The resource name of the Pub/Sub topic to which events
      should be published. Format:
      `projects/{project}/locations/{location}/topics/{topic}`
    workflow: Optional. The resource name of the Workflow whose Executions are
      triggered by the events. The Workflow resource should be deployed in the
      same project as the Pipeline. Format:
      `projects/{project}/locations/{location}/workflows/{workflow}`
  """

  authenticationConfig = _messages.MessageField('GoogleCloudEventarcV1PipelineDestinationAuthenticationConfig', 1)
  httpEndpoint = _messages.MessageField('GoogleCloudEventarcV1PipelineDestinationHttpEndpoint', 2)
  messageBus = _messages.StringField(3)
  networkConfig = _messages.MessageField('GoogleCloudEventarcV1PipelineDestinationNetworkConfig', 4)
  outputPayloadFormat = _messages.MessageField('GoogleCloudEventarcV1PipelineMessagePayloadFormat', 5)
  topic = _messages.StringField(6)
  workflow = _messages.StringField(7)


class GoogleCloudEventarcV1PipelineDestinationAuthenticationConfig(_messages.Message):
  r"""Represents a config used to authenticate message requests.

  Fields:
    googleOidc: Optional. This authenticate method will apply Google OIDC
      tokens signed by a GCP service account to the requests.
    oauthToken: Optional. If specified, an [OAuth
      token](https://developers.google.com/identity/protocols/OAuth2) will be
      generated and attached as an `Authorization` header in the HTTP request.
      This type of authorization should generally only be used when calling
      Google APIs hosted on *.googleapis.com.
  """

  googleOidc = _messages.MessageField('GoogleCloudEventarcV1PipelineDestinationAuthenticationConfigOidcToken', 1)
  oauthToken = _messages.MessageField('GoogleCloudEventarcV1PipelineDestinationAuthenticationConfigOAuthToken', 2)


class GoogleCloudEventarcV1PipelineDestinationAuthenticationConfigOAuthToken(_messages.Message):
  r"""Contains information needed for generating an [OAuth
  token](https://developers.google.com/identity/protocols/OAuth2). This type
  of authorization should generally only be used when calling Google APIs
  hosted on *.googleapis.com.

  Fields:
    scope: Optional. OAuth scope to be used for generating OAuth access token.
      If not specified, "https://www.googleapis.com/auth/cloud-platform" will
      be used.
    serviceAccount: Required. Service account email used to generate the
      [OAuth token](https://developers.google.com/identity/protocols/OAuth2).
      The principal who calls this API must have iam.serviceAccounts.actAs
      permission in the service account. See
      https://cloud.google.com/iam/docs/understanding-service-accounts for
      more information. Eventarc service agents must have
      roles/roles/iam.serviceAccountTokenCreator role to allow Pipeline to
      create OAuth2 tokens for authenticated requests.
  """

  scope = _messages.StringField(1)
  serviceAccount = _messages.StringField(2)


class GoogleCloudEventarcV1PipelineDestinationAuthenticationConfigOidcToken(_messages.Message):
  r"""Represents a config used to authenticate with a Google OIDC token using
  a GCP service account. Use this authentication method to invoke your Cloud
  Run and Cloud Functions destinations or HTTP endpoints that support Google
  OIDC.

  Fields:
    audience: Optional. Audience to be used to generate the OIDC Token. The
      audience claim identifies the recipient that the JWT is intended for. If
      unspecified, the destination URI will be used.
    serviceAccount: Required. Service account email used to generate the OIDC
      Token. The principal who calls this API must have
      iam.serviceAccounts.actAs permission in the service account. See
      https://cloud.google.com/iam/docs/understanding-service-accounts for
      more information. Eventarc service agents must have
      roles/roles/iam.serviceAccountTokenCreator role to allow the Pipeline to
      create OpenID tokens for authenticated requests.
  """

  audience = _messages.StringField(1)
  serviceAccount = _messages.StringField(2)


class GoogleCloudEventarcV1PipelineDestinationHttpEndpoint(_messages.Message):
  r"""Represents a HTTP endpoint destination.

  Fields:
    messageBindingTemplate: Optional. The CEL expression used to modify how
      the destination-bound HTTP request is constructed. If a binding
      expression is not specified here, the message is treated as a CloudEvent
      and is mapped to the HTTP request according to the CloudEvent HTTP
      Protocol Binding Binary Content Mode (https://github.com/cloudevents/spe
      c/blob/main/cloudevents/bindings/http-protocol-binding.md#31-binary-
      content-mode). In this representation, all fields except the `data` and
      `datacontenttype` field on the message are mapped to HTTP request
      headers with a prefix of `ce-`. To construct the HTTP request payload
      and the value of the content-type HTTP header, the payload format is
      defined as follows: 1) Use the output_payload_format_type on the
      Pipeline.Destination if it is set, else: 2) Use the
      input_payload_format_type on the Pipeline if it is set, else: 3) Treat
      the payload as opaque binary data. The `data` field of the message is
      converted to the payload format or left as-is for case 3) and then
      attached as the payload of the HTTP request. The `content-type` header
      on the HTTP request is set to the payload format type or left empty for
      case 3). However, if a mediation has updated the `datacontenttype` field
      on the message so that it is not the same as the payload format type but
      it is still a prefix of the payload format type, then the `content-type`
      header on the HTTP request is set to this `datacontenttype` value. For
      example, if the `datacontenttype` is "application/json" and the payload
      format type is "application/json; charset=utf-8", then the `content-
      type` header on the HTTP request is set to "application/json;
      charset=utf-8". If a non-empty binding expression is specified then this
      expression is used to modify the default CloudEvent HTTP Protocol
      Binding Binary Content representation. The result of the CEL expression
      must be a map of key/value pairs which is used as follows: - If a map
      named `headers` exists on the result of the expression, then its
      key/value pairs are directly mapped to the HTTP request headers. The
      headers values are constructed from the corresponding value type's
      canonical representation. If the `headers` field doesn't exist then the
      resulting HTTP request will be the headers of the CloudEvent HTTP
      Binding Binary Content Mode representation of the final message. Note:
      If the specified binding expression, has updated the `datacontenttype`
      field on the message so that it is not the same as the payload format
      type but it is still a prefix of the payload format type, then the
      `content-type` header in the `headers` map is set to this
      `datacontenttype` value. - If a field named `body` exists on the result
      of the expression then its value is directly mapped to the body of the
      request. If the value of the `body` field is of type bytes or string
      then it is used for the HTTP request body as-is, with no conversion. If
      the body field is of any other type then it is converted to a JSON
      string. If the body field does not exist then the resulting payload of
      the HTTP request will be data value of the CloudEvent HTTP Binding
      Binary Content Mode representation of the final message as described
      earlier. - Any other fields in the resulting expression will be ignored.
      The CEL expression may access the incoming CloudEvent message in its
      definition, as follows: - The `data` field of the incoming CloudEvent
      message can be accessed using the `message.data` value. Subfields of
      `message.data` may also be accessed if an input_payload_format has been
      specified on the Pipeline. - Each attribute of the incoming CloudEvent
      message can be accessed using the `message.` value, where is replaced
      with the name of the attribute. - Existing headers can be accessed in
      the CEL expression using the `headers` variable. The `headers` variable
      defines a map of key/value pairs corresponding to the HTTP headers of
      the CloudEvent HTTP Binding Binary Content Mode representation of the
      final message as described earlier. For example, the following CEL
      expression can be used to construct an HTTP request by adding an
      additional header to the HTTP headers of the CloudEvent HTTP Binding
      Binary Content Mode representation of the final message and by
      overwriting the body of the request: ``` { "headers":
      headers.merge({"new-header-key": "new-header-value"}), "body": "new-
      body" } ``` - The default binding for the message payload can be
      accessed using the `body` variable. It conatins a string representation
      of the message payload in the format specified by the
      `output_payload_format` field. If the `input_payload_format` field is
      not set, the `body` variable contains the same message payload bytes
      that were published. Additionally, the following CEL extension functions
      are provided for use in this CEL expression: - toBase64Url:
      map.toBase64Url() -> string - Converts a CelValue to a base64url encoded
      string - toJsonString: map.toJsonString() -> string - Converts a
      CelValue to a JSON string - merge: map1.merge(map2) -> map3 - Merges the
      passed CEL map with the existing CEL map the function is applied to. -
      If the same key exists in both maps, if the key's value is type map both
      maps are merged else the value from the passed map is used. -
      denormalize: map.denormalize() -> map - Denormalizes a CEL map such that
      every value of type map or key in the map is expanded to return a single
      level map. - The resulting keys are "." separated indices of the map
      keys. - For example: { "a": 1, "b": { "c": 2, "d": 3 } "e": [4, 5] }
      .denormalize() -> { "a": 1, "b.c": 2, "b.d": 3, "e.0": 4, "e.1": 5 } -
      setField: map.setField(key, value) -> message - Sets the field of the
      message with the given key to the given value. - If the field is not
      present it will be added. - If the field is present it will be
      overwritten. - The key can be a dot separated path to set a field in a
      nested message. - Key must be of type string. - Value may be any valid
      type. - removeFields: map.removeFields([key1, key2, ...]) -> message -
      Removes the fields of the map with the given keys. - The keys can be a
      dot separated path to remove a field in a nested message. - If a key is
      not found it will be ignored. - Keys must be of type string. - toMap:
      [map1, map2, ...].toMap() -> map - Converts a CEL list of CEL maps to a
      single CEL map - toCloudEventJsonWithPayloadFormat:
      message.toCloudEventJsonWithPayloadFormat() -> map - Converts a message
      to the corresponding structure of JSON format for CloudEvents. - It
      converts `data` to destination payload format specified in
      `output_payload_format`. If `output_payload_format` is not set, the data
      will remain unchanged. - It also sets the corresponding datacontenttype
      of the CloudEvent, as indicated by `output_payload_format`. If no
      `output_payload_format` is set it will use the value of the
      "datacontenttype" attribute on the CloudEvent if present, else remove
      "datacontenttype" attribute. - This function expects that the content of
      the message will adhere to the standard CloudEvent format. If it doesn't
      then this function will fail. - The result is a CEL map that corresponds
      to the JSON representation of the CloudEvent. To convert that data to a
      JSON string it can be chained with the toJsonString function. The
      Pipeline expects that the message it receives adheres to the standard
      CloudEvent format. If it doesn't then the outgoing message request may
      fail with a persistent error.
    uri: Required. The URI of the HTTP endpoint. The value must be a RFC2396
      URI string. Examples: `https://svc.us-central1.p.local:8080/route`. Only
      the HTTPS protocol is supported.
  """

  messageBindingTemplate = _messages.StringField(1)
  uri = _messages.StringField(2)


class GoogleCloudEventarcV1PipelineDestinationNetworkConfig(_messages.Message):
  r"""Represents a network config to be used for destination resolution and
  connectivity.

  Fields:
    networkAttachment: Required. Name of the NetworkAttachment that allows
      access to the consumer VPC. Format: `projects/{PROJECT_ID}/regions/{REGI
      ON}/networkAttachments/{NETWORK_ATTACHMENT_NAME}`
  """

  networkAttachment = _messages.StringField(1)


class GoogleCloudEventarcV1PipelineMediation(_messages.Message):
  r"""Mediation defines different ways to modify the Pipeline.

  Fields:
    transformation: Optional. How the Pipeline is to transform messages
  """

  transformation = _messages.MessageField('GoogleCloudEventarcV1PipelineMediationTransformation', 1)


class GoogleCloudEventarcV1PipelineMediationTransformation(_messages.Message):
  r"""Transformation defines the way to transform an incoming message.

  Fields:
    transformationTemplate: Optional. The CEL expression template to apply to
      transform messages. The following CEL extension functions are provided
      for use in this CEL expression: - merge: map1.merge(map2) -> map3 -
      Merges the passed CEL map with the existing CEL map the function is
      applied to. - If the same key exists in both maps, if the key's value is
      type map both maps are merged else the value from the passed map is
      used. - denormalize: map.denormalize() -> map - Denormalizes a CEL map
      such that every value of type map or key in the map is expanded to
      return a single level map. - The resulting keys are "." separated
      indices of the map keys. - For example: { "a": 1, "b": { "c": 2, "d": 3
      } "e": [4, 5] } .denormalize() -> { "a": 1, "b.c": 2, "b.d": 3, "e.0":
      4, "e.1": 5 } - setField: map.setField(key, value) -> message - Sets the
      field of the message with the given key to the given value. - If the
      field is not present it will be added. - If the field is present it will
      be overwritten. - The key can be a dot separated path to set a field in
      a nested message. - Key must be of type string. - Value may be any valid
      type. - removeFields: map.removeFields([key1, key2, ...]) -> message -
      Removes the fields of the map with the given keys. - The keys can be a
      dot separated path to remove a field in a nested message. - If a key is
      not found it will be ignored. - Keys must be of type string. - toMap:
      [map1, map2, ...].toMap() -> map - Converts a CEL list of CEL maps to a
      single CEL map - toDestinationPayloadFormat():
      message.data.toDestinationPayloadFormat() -> string or bytes - Converts
      the message data to the destination payload format specified in
      Pipeline.Destination.output_payload_format - This function is meant to
      be applied to the message.data field. - If the destination payload
      format is not set, the function will return the message data unchanged.
      - toCloudEventJsonWithPayloadFormat:
      message.toCloudEventJsonWithPayloadFormat() -> map - Converts a message
      to the corresponding structure of JSON format for CloudEvents - This
      function applies toDestinationPayloadFormat() to the message data. It
      also sets the corresponding datacontenttype of the CloudEvent, as
      indicated by Pipeline.Destination.output_payload_format. If no
      output_payload_format is set it will use the existing datacontenttype on
      the CloudEvent if present, else leave datacontenttype absent. - This
      function expects that the content of the message will adhere to the
      standard CloudEvent format. If it doesn't then this function will fail.
      - The result is a CEL map that corresponds to the JSON representation of
      the CloudEvent. To convert that data to a JSON string it can be chained
      with the toJsonString function.
  """

  transformationTemplate = _messages.StringField(1)


class GoogleCloudEventarcV1PipelineMessagePayloadFormat(_messages.Message):
  r"""Represents the format of message data.

  Fields:
    avro: Optional. Avro format.
    json: Optional. JSON format.
    protobuf: Optional. Protobuf format.
  """

  avro = _messages.MessageField('GoogleCloudEventarcV1PipelineMessagePayloadFormatAvroFormat', 1)
  json = _messages.MessageField('GoogleCloudEventarcV1PipelineMessagePayloadFormatJsonFormat', 2)
  protobuf = _messages.MessageField('GoogleCloudEventarcV1PipelineMessagePayloadFormatProtobufFormat', 3)


class GoogleCloudEventarcV1PipelineMessagePayloadFormatAvroFormat(_messages.Message):
  r"""The format of an AVRO message payload.

  Fields:
    schemaDefinition: Optional. The entire schema definition is stored in this
      field.
  """

  schemaDefinition = _messages.StringField(1)


class GoogleCloudEventarcV1PipelineMessagePayloadFormatJsonFormat(_messages.Message):
  r"""The format of a JSON message payload."""


class GoogleCloudEventarcV1PipelineMessagePayloadFormatProtobufFormat(_messages.Message):
  r"""The format of a Protobuf message payload.

  Fields:
    schemaDefinition: Optional. The entire schema definition is stored in this
      field.
  """

  schemaDefinition = _messages.StringField(1)


class GoogleCloudEventarcV1PipelineRetryPolicy(_messages.Message):
  r"""The retry policy configuration for the Pipeline. The pipeline
  exponentially backs off in case the destination is non responsive or returns
  a retryable error code. The default semantics are as follows: The backoff
  starts with a 5 second delay and doubles the delay after each failed attempt
  (10 seconds, 20 seconds, 40 seconds, etc.). The delay is capped at 60
  seconds by default. Please note that if you set the min_retry_delay and
  max_retry_delay fields to the same value this will make the duration between
  retries constant.

  Fields:
    maxAttempts: Optional. The maximum number of delivery attempts for any
      message. The value must be between 1 and 100. The default value for this
      field is 5.
    maxRetryDelay: Optional. The maximum amount of seconds to wait between
      retry attempts. The value must be between 1 and 600. The default value
      for this field is 60.
    minRetryDelay: Optional. The minimum amount of seconds to wait between
      retry attempts. The value must be between 1 and 600. The default value
      for this field is 5.
  """

  maxAttempts = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  maxRetryDelay = _messages.StringField(2)
  minRetryDelay = _messages.StringField(3)


class GoogleLongrunningCancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class HttpEndpoint(_messages.Message):
  r"""Represents a HTTP endpoint destination.

  Fields:
    uri: Required. The URI of the HTTP endpoint. The value must be a RFC2396
      URI string. Examples: `http://**********:80/route`, `http://svc.us-
      central1.p.local:8080/`. Only HTTP and HTTPS protocols are supported.
      The host can be either a static IP addressable from the VPC specified by
      the network config, or an internal DNS hostname of the service
      resolvable via Cloud DNS.
  """

  uri = _messages.StringField(1)


class KafkaSource(_messages.Message):
  r"""KafkaSource that reads data from Kafka and delivers them to the Message
  Bus. The location of the KafkaSource must match the location of the Message
  Bus that it delivers to.

  Messages:
    AnnotationsValue: Optional. Resource annotations.
    LabelsValue: Optional. Resource labels.

  Fields:
    annotations: Optional. Resource annotations.
    authenticationConfig: Optional. Authentication configuration used to
      authenticate the Kafka client with the Kafka broker, and authorize to
      read the topic(s).
    brokerUris: Required. The Kafka broker URIs. e.g. ***********:8080
    consumerGroupId: Required. The consumer group ID used by the Kafka broker
      to track the offsets of all topic partitions being read by this Stream.
    createTime: Output only. The creation time.
    destination: Required. Destination is the message bus that the kafka
      source is delivering to. It must be point to the full resource name of a
      MessageBus. Format:
      "projects/{PROJECT_ID}/locations/{region}/messagesBuses/{MESSAGE_BUS_ID)
    displayName: Optional. Resource display name.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and might be sent only on update and delete
      requests to ensure that the client has an up-to-date value before
      proceeding.
    initialOffset: Required. The initial message offset from which to start
      streaming. Supported values: newest, oldest.
    labels: Optional. Resource labels.
    loggingConfig: Optional. Config to control Platform Logging for Kafka
      Sources.
    name: Identifier. Resource name of the form
      projects/{project}/locations/{location}/kafkaSources/{kafka_source}
    networkConfig: Optional. The network passed to the Kafka source to connect
      to the kafka broker available from a customer VPC.
    topics: Required. The Kafka topics to read from.
    uid: Output only. Server assigned unique identifier for the KafkaSource.
      The value is a UUID4 string and guaranteed to remain unchanged until the
      resource is deleted.
    updateTime: Output only. The last-modified time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Resource annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  authenticationConfig = _messages.MessageField('AuthenticationConfig', 2)
  brokerUris = _messages.StringField(3, repeated=True)
  consumerGroupId = _messages.StringField(4)
  createTime = _messages.StringField(5)
  destination = _messages.StringField(6)
  displayName = _messages.StringField(7)
  etag = _messages.StringField(8)
  initialOffset = _messages.StringField(9)
  labels = _messages.MessageField('LabelsValue', 10)
  loggingConfig = _messages.MessageField('LoggingConfig', 11)
  name = _messages.StringField(12)
  networkConfig = _messages.MessageField('NetworkConfig', 13)
  topics = _messages.StringField(14, repeated=True)
  uid = _messages.StringField(15)
  updateTime = _messages.StringField(16)


class ListChannelConnectionsResponse(_messages.Message):
  r"""The response message for the `ListChannelConnections` method.

  Fields:
    channelConnections: The requested channel connections, up to the number
      specified in `page_size`.
    nextPageToken: A page token that can be sent to `ListChannelConnections`
      to request the next page. If this is empty, then there are no more
      pages.
    unreachable: Unreachable resources, if any.
  """

  channelConnections = _messages.MessageField('ChannelConnection', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListChannelsResponse(_messages.Message):
  r"""The response message for the `ListChannels` method.

  Fields:
    channels: The requested channels, up to the number specified in
      `page_size`.
    nextPageToken: A page token that can be sent to `ListChannels` to request
      the next page. If this is empty, then there are no more pages.
    unreachable: Unreachable resources, if any.
  """

  channels = _messages.MessageField('Channel', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListEnrollmentsResponse(_messages.Message):
  r"""The response message for the `ListEnrollments` method.

  Fields:
    enrollments: The requested Enrollments, up to the number specified in
      `page_size`.
    nextPageToken: A page token that can be sent to `ListEnrollments` to
      request the next page. If this is empty, then there are no more pages.
    unreachable: Unreachable resources, if any.
  """

  enrollments = _messages.MessageField('Enrollment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGoogleApiSourcesResponse(_messages.Message):
  r"""The response message for the `ListGoogleApiSources` method.

  Fields:
    googleApiSources: The requested GoogleApiSources, up to the number
      specified in `page_size`.
    nextPageToken: A page token that can be sent to
      `ListMessageBusEnrollments` to request the next page. If this is empty,
      then there are no more pages.
    unreachable: Unreachable resources, if any.
  """

  googleApiSources = _messages.MessageField('GoogleApiSource', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListKafkaSourcesResponse(_messages.Message):
  r"""The response message for the `ListKafkaSources` method.

  Fields:
    kafkaSources: The requested KafkaSources, up to the number specified in
      `page_size`.
    nextPageToken: A page token that can be sent to `ListKafkaSources` to
      request the next page. If this is empty, then there are no more pages.
    unreachable: Unreachable resources, if any.
  """

  kafkaSources = _messages.MessageField('KafkaSource', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMessageBusEnrollmentsResponse(_messages.Message):
  r"""The response message for the `ListMessageBusEnrollments` method.`

  Fields:
    enrollments: The requested enrollments, up to the number specified in
      `page_size`.
    nextPageToken: A page token that can be sent to
      `ListMessageBusEnrollments` to request the next page. If this is empty,
      then there are no more pages.
    unreachable: Unreachable resources, if any.
  """

  enrollments = _messages.StringField(1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMessageBusesResponse(_messages.Message):
  r"""The response message for the `ListMessageBuses` method.

  Fields:
    messageBuses: The requested message buses, up to the number specified in
      `page_size`.
    nextPageToken: A page token that can be sent to `ListMessageBuses` to
      request the next page. If this is empty, then there are no more pages.
    unreachable: Unreachable resources, if any.
  """

  messageBuses = _messages.MessageField('MessageBus', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListPipelinesResponse(_messages.Message):
  r"""The response message for the ListPipelines method.

  Fields:
    nextPageToken: A page token that can be sent to `ListPipelines` to request
      the next page. If this is empty, then there are no more pages.
    pipelines: The requested pipelines, up to the number specified in
      `page_size`.
    unreachable: Unreachable resources, if any.
  """

  nextPageToken = _messages.StringField(1)
  pipelines = _messages.MessageField('Pipeline', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListProvidersResponse(_messages.Message):
  r"""The response message for the `ListProviders` method.

  Fields:
    nextPageToken: A page token that can be sent to `ListProviders` to request
      the next page. If this is empty, then there are no more pages.
    providers: The requested providers, up to the number specified in
      `page_size`.
    unreachable: Unreachable resources, if any.
  """

  nextPageToken = _messages.StringField(1)
  providers = _messages.MessageField('Provider', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListTriggersResponse(_messages.Message):
  r"""The response message for the `ListTriggers` method.

  Fields:
    nextPageToken: A page token that can be sent to `ListTriggers` to request
      the next page. If this is empty, then there are no more pages.
    triggers: The requested triggers, up to the number specified in
      `page_size`.
    unreachable: Unreachable resources, if any.
  """

  nextPageToken = _messages.StringField(1)
  triggers = _messages.MessageField('Trigger', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LoggingConfig(_messages.Message):
  r"""The configuration for Platform Telemetry logging for Eventarc Advanced
  resources.

  Enums:
    LogSeverityValueValuesEnum: Optional. The minimum severity of logs that
      will be sent to Stackdriver/Platform Telemetry. Logs at severitiy
      \\u2265 this value will be sent, unless it is NONE.

  Fields:
    logSeverity: Optional. The minimum severity of logs that will be sent to
      Stackdriver/Platform Telemetry. Logs at severitiy \u2265 this value will
      be sent, unless it is NONE.
  """

  class LogSeverityValueValuesEnum(_messages.Enum):
    r"""Optional. The minimum severity of logs that will be sent to
    Stackdriver/Platform Telemetry. Logs at severitiy \\u2265 this value will
    be sent, unless it is NONE.

    Values:
      LOG_SEVERITY_UNSPECIFIED: Log severity is not specified. This value is
        treated the same as NONE, but is used to distinguish between no update
        and update to NONE in update_masks.
      NONE: Default value at resource creation, presence of this value must be
        treated as no logging/disable logging.
      DEBUG: Debug or trace level logging.
      INFO: Routine information, such as ongoing status or performance.
      NOTICE: Normal but significant events, such as start up, shut down, or a
        configuration change.
      WARNING: Warning events might cause problems.
      ERROR: Error events are likely to cause problems.
      CRITICAL: Critical events cause more severe problems or outages.
      ALERT: A person must take action immediately.
      EMERGENCY: One or more systems are unusable.
    """
    LOG_SEVERITY_UNSPECIFIED = 0
    NONE = 1
    DEBUG = 2
    INFO = 3
    NOTICE = 4
    WARNING = 5
    ERROR = 6
    CRITICAL = 7
    ALERT = 8
    EMERGENCY = 9

  logSeverity = _messages.EnumField('LogSeverityValueValuesEnum', 1)


class MessageBus(_messages.Message):
  r"""MessageBus for the messages flowing through the system. The admin has
  visibility and control over the messages being published and consumed and
  can restrict publishers and subscribers to only a subset of data available
  in the system by defining authorization policies.

  Messages:
    AnnotationsValue: Optional. Resource annotations.
    LabelsValue: Optional. Resource labels.

  Fields:
    annotations: Optional. Resource annotations.
    createTime: Output only. The creation time.
    cryptoKeyName: Optional. Resource name of a KMS crypto key (managed by the
      user) used to encrypt/decrypt their event data. It must match the
      pattern `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
    displayName: Optional. Resource display name.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and might be sent only on update and delete
      requests to ensure that the client has an up-to-date value before
      proceeding.
    labels: Optional. Resource labels.
    loggingConfig: Optional. Config to control Platform logging for the
      Message Bus. This log configuration is applied to the Message Bus
      itself, and all the Enrollments attached to it.
    name: Identifier. Resource name of the form
      projects/{project}/locations/{location}/messageBuses/{message_bus}
    uid: Output only. Server assigned unique identifier for the channel. The
      value is a UUID4 string and guaranteed to remain unchanged until the
      resource is deleted.
    updateTime: Output only. The last-modified time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Resource annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  cryptoKeyName = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  loggingConfig = _messages.MessageField('LoggingConfig', 7)
  name = _messages.StringField(8)
  uid = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class MutualTlsAuthConfig(_messages.Message):
  r"""Mutual TLS authentication mechanism configuration.

  Fields:
    secretManagerResources: mTLS auth config loaded from Secret Manager.
  """

  secretManagerResources = _messages.MessageField('MutualTlsSecrets', 1)


class MutualTlsSecrets(_messages.Message):
  r"""Mutual TLS payloads from Secret Manager.

  Fields:
    clientCertificate: Required. The client certificate for mTLS may be loaded
      from Secret Manager. Supported Formats:
      `projects/{project}/secrets/{secret}/versions/{version}` `projects/{proj
      ect}/locations/{location}/secrets/{secret}/versions/{version}`
    clientKey: Required. The client key for mTLS may be loaded from Secret
      Manager. Supported Formats:
      `projects/{project}/secrets/{secret}/versions/{version}` `projects/{proj
      ect}/locations/{location}/secrets/{secret}/versions/{version}`
  """

  clientCertificate = _messages.StringField(1)
  clientKey = _messages.StringField(2)


class NetworkConfig(_messages.Message):
  r"""Network Configuration that can be inherited by other protos.

  Fields:
    networkAttachment: Required. Name of the NetworkAttachment that allows
      access to the customer's VPC. Format: `projects/{PROJECT_ID}/regions/{RE
      GION}/networkAttachments/{NETWORK_ATTACHMENT_NAME}`
  """

  networkAttachment = _messages.StringField(1)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class OrganizationSubscription(_messages.Message):
  r"""Config to enabled subscribing to events from other projects in the org.

  Fields:
    enabled: Required. Enable org level subscription.
  """

  enabled = _messages.BooleanField(1)


class Pipeline(_messages.Message):
  r"""A representation of the Pipeline resource.

  Messages:
    AnnotationsValue: Optional. User-defined annotations. See
      https://google.aip.dev/128#annotations.
    LabelsValue: Optional. User labels attached to the Pipeline that can be
      used to group resources. An object containing a list of "key": value
      pairs. Example: { "name": "wrench", "mass": "1.3kg", "count": "3" }.

  Fields:
    annotations: Optional. User-defined annotations. See
      https://google.aip.dev/128#annotations.
    createTime: Output only. The creation time. A timestamp in RFC3339 UTC
      "Zulu" format, with nanosecond resolution and up to nine fractional
      digits. Examples: "2014-10-02T15:01:23Z" and
      "2014-10-02T15:01:23.045123456Z".
    cryptoKeyName: Optional. Resource name of a KMS crypto key (managed by the
      user) used to encrypt/decrypt the event data. If not set, an internal
      Google-owned key will be used to encrypt messages. It must match the
      pattern "projects/{project}/locations/{location}/keyRings/{keyring}/cryp
      toKeys/{key}".
    destinations: Required. List of destinations to which messages will be
      forwarded. Currently, exactly one destination is supported per Pipeline.
    displayName: Optional. Display name of resource.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and might be sent only on create requests to
      ensure that the client has an up-to-date value before proceeding.
    inputPayloadFormat: Optional. The payload format expected for the messages
      received by the Pipeline. If input_payload_format is set then any
      messages not matching this format will be treated as persistent errors.
      If input_payload_format is not set, then the message data will be
      treated as an opaque binary and no output format can be set on the
      Pipeline through the Pipeline.Destination.output_payload_format field.
      Any Mediations on the Pipeline that involve access to the data field
      will fail as persistent errors.
    labels: Optional. User labels attached to the Pipeline that can be used to
      group resources. An object containing a list of "key": value pairs.
      Example: { "name": "wrench", "mass": "1.3kg", "count": "3" }.
    loggingConfig: Optional. Config to control Platform Logging for Pipelines.
    mediations: Optional. List of mediation operations to be performed on the
      message. Currently, only one Transformation operation is allowed in each
      Pipeline.
    name: Identifier. The resource name of the Pipeline. Must be unique within
      the location of the project and must be in
      `projects/{project}/locations/{location}/pipelines/{pipeline}` format.
    retryPolicy: Optional. The retry policy to use in the pipeline.
    satisfiesPzs: Output only. Whether or not this Pipeline satisfies the
      requirements of physical zone separation
    uid: Output only. Server-assigned unique identifier for the Pipeline. The
      value is a UUID4 string and guaranteed to remain unchanged until the
      resource is deleted.
    updateTime: Output only. The last-modified time. A timestamp in RFC3339
      UTC "Zulu" format, with nanosecond resolution and up to nine fractional
      digits. Examples: "2014-10-02T15:01:23Z" and
      "2014-10-02T15:01:23.045123456Z".
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User-defined annotations. See
    https://google.aip.dev/128#annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User labels attached to the Pipeline that can be used to
    group resources. An object containing a list of "key": value pairs.
    Example: { "name": "wrench", "mass": "1.3kg", "count": "3" }.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  cryptoKeyName = _messages.StringField(3)
  destinations = _messages.MessageField('GoogleCloudEventarcV1PipelineDestination', 4, repeated=True)
  displayName = _messages.StringField(5)
  etag = _messages.StringField(6)
  inputPayloadFormat = _messages.MessageField('GoogleCloudEventarcV1PipelineMessagePayloadFormat', 7)
  labels = _messages.MessageField('LabelsValue', 8)
  loggingConfig = _messages.MessageField('LoggingConfig', 9)
  mediations = _messages.MessageField('GoogleCloudEventarcV1PipelineMediation', 10, repeated=True)
  name = _messages.StringField(11)
  retryPolicy = _messages.MessageField('GoogleCloudEventarcV1PipelineRetryPolicy', 12)
  satisfiesPzs = _messages.BooleanField(13)
  uid = _messages.StringField(14)
  updateTime = _messages.StringField(15)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ProjectSubscriptions(_messages.Message):
  r"""Config to enable subscribing to all events from a list of projects.

  Fields:
    list: Required. A list of projects to receive events from. All the
      projects must be in the same org. The listed projects should have the
      format project/{identifier} where identifier can be either the project
      id for project number. A single list may contain both formats. At most
      100 projects can be listed.
  """

  list = _messages.StringField(1, repeated=True)


class Provider(_messages.Message):
  r"""A representation of the Provider resource.

  Fields:
    displayName: Output only. Human friendly name for the Provider. For
      example "Cloud Storage".
    eventTypes: Output only. Event types for this provider.
    name: Output only. In
      `projects/{project}/locations/{location}/providers/{provider_id}`
      format.
  """

  displayName = _messages.StringField(1)
  eventTypes = _messages.MessageField('EventType', 2, repeated=True)
  name = _messages.StringField(3)


class Pubsub(_messages.Message):
  r"""Represents a Pub/Sub transport.

  Fields:
    subscription: Output only. The name of the Pub/Sub subscription created
      and managed by Eventarc as a transport for the event delivery. Format:
      `projects/{PROJECT_ID}/subscriptions/{SUBSCRIPTION_NAME}`.
    topic: Optional. The name of the Pub/Sub topic created and managed by
      Eventarc as a transport for the event delivery. Format:
      `projects/{PROJECT_ID}/topics/{TOPIC_NAME}`. You can set an existing
      topic for triggers of the type
      `google.cloud.pubsub.topic.v1.messagePublished`. The topic you provide
      here is not deleted by Eventarc at trigger deletion.
  """

  subscription = _messages.StringField(1)
  topic = _messages.StringField(2)


class SaslAuthConfig(_messages.Message):
  r"""SASL/Plain or SASL/SCRAM mechanism configuration.

  Enums:
    MechanismValueValuesEnum: Required. The SASL authentication mechanism.

  Fields:
    mechanism: Required. The SASL authentication mechanism.
    passwordSecret: Required. The password for the authentication identity may
      be loaded from Secret Manager. Supported Format: 1-
      "projects/{project}/secrets/{secret}/versions/{version}" 2- "projects/{p
      roject}/locations/{location}/secrets/{secret}/versions/{version}"
    username: Optional. The SASL authentication identity (username).
    usernameSecret: Optional. The username for the authentication identity may
      be loaded from Secret Manager. Supported Format: 1-
      "projects/{project}/secrets/{secret}/versions/{version}" 2- "projects/{p
      roject}/locations/{location}/secrets/{secret}/versions/{version}"
  """

  class MechanismValueValuesEnum(_messages.Enum):
    r"""Required. The SASL authentication mechanism.

    Values:
      AUTH_MECHANISM_UNSPECIFIED: Default Mechanism is unspecified.
      PLAIN: PLAIN
      SHA_256: SHA_256
      SHA_512: SHA_512
    """
    AUTH_MECHANISM_UNSPECIFIED = 0
    PLAIN = 1
    SHA_256 = 2
    SHA_512 = 3

  mechanism = _messages.EnumField('MechanismValueValuesEnum', 1)
  passwordSecret = _messages.StringField(2)
  username = _messages.StringField(3)
  usernameSecret = _messages.StringField(4)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StateCondition(_messages.Message):
  r"""A condition that is part of the trigger state computation.

  Enums:
    CodeValueValuesEnum: The canonical code of the condition.

  Fields:
    code: The canonical code of the condition.
    message: Human-readable message.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""The canonical code of the condition.

    Values:
      OK: Not an error; returned on success. HTTP Mapping: 200 OK
      CANCELLED: The operation was cancelled, typically by the caller. HTTP
        Mapping: 499 Client Closed Request
      UNKNOWN: Unknown error. For example, this error may be returned when a
        `Status` value received from another address space belongs to an error
        space that is not known in this address space. Also errors raised by
        APIs that do not return enough error information may be converted to
        this error. HTTP Mapping: 500 Internal Server Error
      INVALID_ARGUMENT: The client specified an invalid argument. Note that
        this differs from `FAILED_PRECONDITION`. `INVALID_ARGUMENT` indicates
        arguments that are problematic regardless of the state of the system
        (e.g., a malformed file name). HTTP Mapping: 400 Bad Request
      DEADLINE_EXCEEDED: The deadline expired before the operation could
        complete. For operations that change the state of the system, this
        error may be returned even if the operation has completed
        successfully. For example, a successful response from a server could
        have been delayed long enough for the deadline to expire. HTTP
        Mapping: 504 Gateway Timeout
      NOT_FOUND: Some requested entity (e.g., file or directory) was not
        found. Note to server developers: if a request is denied for an entire
        class of users, such as gradual feature rollout or undocumented
        allowlist, `NOT_FOUND` may be used. If a request is denied for some
        users within a class of users, such as user-based access control,
        `PERMISSION_DENIED` must be used. HTTP Mapping: 404 Not Found
      ALREADY_EXISTS: The entity that a client attempted to create (e.g., file
        or directory) already exists. HTTP Mapping: 409 Conflict
      PERMISSION_DENIED: The caller does not have permission to execute the
        specified operation. `PERMISSION_DENIED` must not be used for
        rejections caused by exhausting some resource (use
        `RESOURCE_EXHAUSTED` instead for those errors). `PERMISSION_DENIED`
        must not be used if the caller can not be identified (use
        `UNAUTHENTICATED` instead for those errors). This error code does not
        imply the request is valid or the requested entity exists or satisfies
        other pre-conditions. HTTP Mapping: 403 Forbidden
      UNAUTHENTICATED: The request does not have valid authentication
        credentials for the operation. HTTP Mapping: 401 Unauthorized
      RESOURCE_EXHAUSTED: Some resource has been exhausted, perhaps a per-user
        quota, or perhaps the entire file system is out of space. HTTP
        Mapping: 429 Too Many Requests
      FAILED_PRECONDITION: The operation was rejected because the system is
        not in a state required for the operation's execution. For example,
        the directory to be deleted is non-empty, an rmdir operation is
        applied to a non-directory, etc. Service implementors can use the
        following guidelines to decide between `FAILED_PRECONDITION`,
        `ABORTED`, and `UNAVAILABLE`: (a) Use `UNAVAILABLE` if the client can
        retry just the failing call. (b) Use `ABORTED` if the client should
        retry at a higher level. For example, when a client-specified test-
        and-set fails, indicating the client should restart a read-modify-
        write sequence. (c) Use `FAILED_PRECONDITION` if the client should not
        retry until the system state has been explicitly fixed. For example,
        if an "rmdir" fails because the directory is non-empty,
        `FAILED_PRECONDITION` should be returned since the client should not
        retry unless the files are deleted from the directory. HTTP Mapping:
        400 Bad Request
      ABORTED: The operation was aborted, typically due to a concurrency issue
        such as a sequencer check failure or transaction abort. See the
        guidelines above for deciding between `FAILED_PRECONDITION`,
        `ABORTED`, and `UNAVAILABLE`. HTTP Mapping: 409 Conflict
      OUT_OF_RANGE: The operation was attempted past the valid range. E.g.,
        seeking or reading past end-of-file. Unlike `INVALID_ARGUMENT`, this
        error indicates a problem that may be fixed if the system state
        changes. For example, a 32-bit file system will generate
        `INVALID_ARGUMENT` if asked to read at an offset that is not in the
        range [0,2^32-1], but it will generate `OUT_OF_RANGE` if asked to read
        from an offset past the current file size. There is a fair bit of
        overlap between `FAILED_PRECONDITION` and `OUT_OF_RANGE`. We recommend
        using `OUT_OF_RANGE` (the more specific error) when it applies so that
        callers who are iterating through a space can easily look for an
        `OUT_OF_RANGE` error to detect when they are done. HTTP Mapping: 400
        Bad Request
      UNIMPLEMENTED: The operation is not implemented or is not
        supported/enabled in this service. HTTP Mapping: 501 Not Implemented
      INTERNAL: Internal errors. This means that some invariants expected by
        the underlying system have been broken. This error code is reserved
        for serious errors. HTTP Mapping: 500 Internal Server Error
      UNAVAILABLE: The service is currently unavailable. This is most likely a
        transient condition, which can be corrected by retrying with a
        backoff. Note that it is not always safe to retry non-idempotent
        operations. See the guidelines above for deciding between
        `FAILED_PRECONDITION`, `ABORTED`, and `UNAVAILABLE`. HTTP Mapping: 503
        Service Unavailable
      DATA_LOSS: Unrecoverable data loss or corruption. HTTP Mapping: 500
        Internal Server Error
    """
    OK = 0
    CANCELLED = 1
    UNKNOWN = 2
    INVALID_ARGUMENT = 3
    DEADLINE_EXCEEDED = 4
    NOT_FOUND = 5
    ALREADY_EXISTS = 6
    PERMISSION_DENIED = 7
    UNAUTHENTICATED = 8
    RESOURCE_EXHAUSTED = 9
    FAILED_PRECONDITION = 10
    ABORTED = 11
    OUT_OF_RANGE = 12
    UNIMPLEMENTED = 13
    INTERNAL = 14
    UNAVAILABLE = 15
    DATA_LOSS = 16

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  message = _messages.StringField(2)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class Transport(_messages.Message):
  r"""Represents the transport intermediaries created for the trigger to
  deliver events.

  Fields:
    pubsub: The Pub/Sub topic and subscription used by Eventarc as a transport
      intermediary.
  """

  pubsub = _messages.MessageField('Pubsub', 1)


class Trigger(_messages.Message):
  r"""A representation of the trigger resource.

  Messages:
    ConditionsValue: Output only. The reason(s) why a trigger is in FAILED
      state.
    LabelsValue: Optional. User labels attached to the triggers that can be
      used to group resources.

  Fields:
    channel: Optional. The name of the channel associated with the trigger in
      `projects/{project}/locations/{location}/channels/{channel}` format. You
      must provide a channel to receive events from Eventarc SaaS partners.
    conditions: Output only. The reason(s) why a trigger is in FAILED state.
    createTime: Output only. The creation time.
    destination: Required. Destination specifies where the events should be
      sent to.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and might be sent only on create requests to
      ensure that the client has an up-to-date value before proceeding.
    eventDataContentType: Optional. EventDataContentType specifies the type of
      payload in MIME format that is expected from the CloudEvent data field.
      This is set to `application/json` if the value is not defined.
    eventFilters: Required. Unordered list. The list of filters that applies
      to event attributes. Only events that match all the provided filters are
      sent to the destination.
    labels: Optional. User labels attached to the triggers that can be used to
      group resources.
    name: Required. The resource name of the trigger. Must be unique within
      the location of the project and must be in
      `projects/{project}/locations/{location}/triggers/{trigger}` format.
    satisfiesPzs: Output only. Whether or not this Trigger satisfies the
      requirements of physical zone separation
    serviceAccount: Optional. The IAM service account email associated with
      the trigger. The service account represents the identity of the trigger.
      The `iam.serviceAccounts.actAs` permission must be granted on the
      service account to allow a principal to impersonate the service account.
      For more information, see the [Roles and
      permissions](/eventarc/docs/all-roles-permissions) page specific to the
      trigger destination.
    transport: Optional. To deliver messages, Eventarc might use other Google
      Cloud products as a transport intermediary. This field contains a
      reference to that transport intermediary. This information can be used
      for debugging purposes.
    uid: Output only. Server-assigned unique identifier for the trigger. The
      value is a UUID4 string and guaranteed to remain unchanged until the
      resource is deleted.
    updateTime: Output only. The last-modified time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConditionsValue(_messages.Message):
    r"""Output only. The reason(s) why a trigger is in FAILED state.

    Messages:
      AdditionalProperty: An additional property for a ConditionsValue object.

    Fields:
      additionalProperties: Additional properties of type ConditionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConditionsValue object.

      Fields:
        key: Name of the additional property.
        value: A StateCondition attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('StateCondition', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User labels attached to the triggers that can be used to
    group resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  channel = _messages.StringField(1)
  conditions = _messages.MessageField('ConditionsValue', 2)
  createTime = _messages.StringField(3)
  destination = _messages.MessageField('Destination', 4)
  etag = _messages.StringField(5)
  eventDataContentType = _messages.StringField(6)
  eventFilters = _messages.MessageField('EventFilter', 7, repeated=True)
  labels = _messages.MessageField('LabelsValue', 8)
  name = _messages.StringField(9)
  satisfiesPzs = _messages.BooleanField(10)
  serviceAccount = _messages.StringField(11)
  transport = _messages.MessageField('Transport', 12)
  uid = _messages.StringField(13)
  updateTime = _messages.StringField(14)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    EventarcProjectsLocationsChannelConnectionsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    EventarcProjectsLocationsChannelsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    EventarcProjectsLocationsEnrollmentsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    EventarcProjectsLocationsGoogleApiSourcesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    EventarcProjectsLocationsKafkaSourcesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    EventarcProjectsLocationsMessageBusesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    EventarcProjectsLocationsPipelinesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    EventarcProjectsLocationsTriggersGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
