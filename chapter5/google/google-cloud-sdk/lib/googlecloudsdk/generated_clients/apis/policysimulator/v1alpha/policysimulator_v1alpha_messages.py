"""Generated message classes for policysimulator version v1alpha.

 Policy Simulator is a collection of endpoints for creating, running, and
viewing a Replay. A `Replay` is a type of simulation that lets you see how
your members' access to resources might change if you changed your IAM policy.
During a `Replay`, Policy Simulator re-evaluates, or replays, past access
attempts under both the current policy and your proposed policy, and compares
those results to determine how your members' access might change under the
proposed policy.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'policysimulator'


class GoogleCloudAuditAuthorizationLoggingOptions(_messages.Message):
  r"""Authorization-related information used by Cloud Audit Logging.

  Enums:
    PermissionTypeValueValuesEnum: The type of the permission that was
      checked.

  Fields:
    permissionType: The type of the permission that was checked.
  """

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type of the permission that was checked.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: A read of admin (meta) data.
      ADMIN_WRITE: A write of admin (meta) data.
      DATA_READ: A read of standard data.
      DATA_WRITE: A write of standard data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 1)


class GoogleCloudOrgpolicyV2AlternatePolicySpec(_messages.Message):
  r"""Similar to PolicySpec but with an extra 'launch' field for launch
  reference. The PolicySpec here is specific for dry-run.

  Fields:
    launch: Reference to the launch that will be used while audit logging and
      to control the launch. Should be set only in the alternate policy.
    spec: Specify constraint for configurations of Google Cloud resources.
  """

  launch = _messages.StringField(1)
  spec = _messages.MessageField('GoogleCloudOrgpolicyV2PolicySpec', 2)


class GoogleCloudOrgpolicyV2CustomConstraint(_messages.Message):
  r"""A custom constraint defined by customers which can *only* be applied to
  the given resource types and organization. By creating a custom constraint,
  customers can apply policies of this custom constraint. *Creating a custom
  constraint itself does NOT apply any policy enforcement*.

  Enums:
    ActionTypeValueValuesEnum: Allow or deny type.
    MethodTypesValueListEntryValuesEnum:

  Messages:
    ParametersValue: Stores the structure of `Parameters` used by the
      constraint condition. The key of `map` represents the name of the
      parameter.

  Fields:
    actionType: Allow or deny type.
    condition: A Common Expression Language (CEL) condition which is used in
      the evaluation of the constraint. For example:
      `resource.instanceName.matches("[production|test]_.*_(\d)+")` or,
      `resource.management.auto_upgrade == true` The max length of the
      condition is 1000 characters.
    description: Detailed information about this custom policy constraint. The
      max length of the description is 2000 characters.
    displayName: One line display name for the UI. The max length of the
      display_name is 200 characters.
    methodTypes: All the operations being applied for this constraint.
    name: Immutable. Name of the constraint. This is unique within the
      organization. Format of the name should be * `organizations/{organizatio
      n_id}/customConstraints/{custom_constraint_id}` Example:
      `organizations/123/customConstraints/custom.createOnlyE2TypeVms` The max
      length is 70 characters and the minimum length is 1. Note that the
      prefix `organizations/{organization_id}/customConstraints/` is not
      counted.
    parameters: Stores the structure of `Parameters` used by the constraint
      condition. The key of `map` represents the name of the parameter.
    resourceTypes: Immutable. The resource instance type on which this policy
      applies. Format will be of the form : `/` Example: *
      `compute.googleapis.com/Instance`.
    updateTime: Output only. The last time this custom constraint was updated.
      This represents the last time that the `CreateCustomConstraint` or
      `UpdateCustomConstraint` methods were called.
  """

  class ActionTypeValueValuesEnum(_messages.Enum):
    r"""Allow or deny type.

    Values:
      ACTION_TYPE_UNSPECIFIED: This is only used for distinguishing unset
        values and should never be used. Results in an error.
      ALLOW: Allowed action type.
      DENY: Deny action type.
    """
    ACTION_TYPE_UNSPECIFIED = 0
    ALLOW = 1
    DENY = 2

  class MethodTypesValueListEntryValuesEnum(_messages.Enum):
    r"""MethodTypesValueListEntryValuesEnum enum type.

    Values:
      METHOD_TYPE_UNSPECIFIED: This is only used for distinguishing unset
        values and should never be used. Results in an error.
      CREATE: Constraint applied when creating the resource.
      UPDATE: Constraint applied when updating the resource.
      DELETE: Constraint applied when deleting the resource. Not currently
        supported.
      REMOVE_GRANT: Constraint applied when removing an IAM grant.
      GOVERN_TAGS: Constraint applied when enforcing forced tagging.
    """
    METHOD_TYPE_UNSPECIFIED = 0
    CREATE = 1
    UPDATE = 2
    DELETE = 3
    REMOVE_GRANT = 4
    GOVERN_TAGS = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParametersValue(_messages.Message):
    r"""Stores the structure of `Parameters` used by the constraint condition.
    The key of `map` represents the name of the parameter.

    Messages:
      AdditionalProperty: An additional property for a ParametersValue object.

    Fields:
      additionalProperties: Additional properties of type ParametersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudOrgpolicyV2CustomConstraintParameter attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudOrgpolicyV2CustomConstraintParameter', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  actionType = _messages.EnumField('ActionTypeValueValuesEnum', 1)
  condition = _messages.StringField(2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  methodTypes = _messages.EnumField('MethodTypesValueListEntryValuesEnum', 5, repeated=True)
  name = _messages.StringField(6)
  parameters = _messages.MessageField('ParametersValue', 7)
  resourceTypes = _messages.StringField(8, repeated=True)
  updateTime = _messages.StringField(9)


class GoogleCloudOrgpolicyV2CustomConstraintParameter(_messages.Message):
  r"""Defines a parameter structure.

  Enums:
    ItemValueValuesEnum: Determines the parameter's value structure. For
      example, `LIST` can be specified by defining `type: LIST`, and `item:
      STRING`.
    TypeValueValuesEnum: Type of the parameter.

  Fields:
    defaultValue: Sets the value of the parameter in an assignment if no value
      is given.
    item: Determines the parameter's value structure. For example, `LIST` can
      be specified by defining `type: LIST`, and `item: STRING`.
    metadata: Defines properties primarily used by the UI to display user-
      friendly information.
    type: Type of the parameter.
    validValuesExpr: Provides a CEL expression to specify the acceptable
      parameter values during assignment. For example, parameterName in
      ("parameterValue1", "parameterValue2")
  """

  class ItemValueValuesEnum(_messages.Enum):
    r"""Determines the parameter's value structure. For example, `LIST` can be
    specified by defining `type: LIST`, and `item: STRING`.

    Values:
      TYPE_UNSPECIFIED: This is only used for distinguishing unset values and
        should never be used. Results in an error.
      LIST: List parameter type.
      STRING: String parameter type.
      BOOLEAN: Boolean parameter type.
    """
    TYPE_UNSPECIFIED = 0
    LIST = 1
    STRING = 2
    BOOLEAN = 3

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of the parameter.

    Values:
      TYPE_UNSPECIFIED: This is only used for distinguishing unset values and
        should never be used. Results in an error.
      LIST: List parameter type.
      STRING: String parameter type.
      BOOLEAN: Boolean parameter type.
    """
    TYPE_UNSPECIFIED = 0
    LIST = 1
    STRING = 2
    BOOLEAN = 3

  defaultValue = _messages.MessageField('extra_types.JsonValue', 1)
  item = _messages.EnumField('ItemValueValuesEnum', 2)
  metadata = _messages.MessageField('GoogleCloudOrgpolicyV2CustomConstraintParameterMetadata', 3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)
  validValuesExpr = _messages.StringField(5)


class GoogleCloudOrgpolicyV2CustomConstraintParameterMetadata(_messages.Message):
  r"""Defines metadata structure.

  Fields:
    description: Detailed description of what this parameter is and how to use
      it. Mutable.
  """

  description = _messages.StringField(1)


class GoogleCloudOrgpolicyV2Policy(_messages.Message):
  r"""Defines an organization policy which is used to specify constraints for
  configurations of Google Cloud resources.

  Fields:
    alternate: Deprecated.
    dryRunSpec: Dry-run policy. Audit-only policy, can be used to monitor how
      the policy would have impacted the existing and future resources if it's
      enforced.
    etag: Optional. An opaque tag indicating the current state of the policy,
      used for concurrency control. This 'etag' is computed by the server
      based on the value of other fields, and may be sent on update and delete
      requests to ensure the client has an up-to-date value before proceeding.
    name: Immutable. The resource name of the policy. Must be one of the
      following forms, where `constraint_name` is the name of the constraint
      which this policy configures: *
      `projects/{project_number}/policies/{constraint_name}` *
      `folders/{folder_id}/policies/{constraint_name}` *
      `organizations/{organization_id}/policies/{constraint_name}` For
      example, `projects/123/policies/compute.disableSerialPortAccess`. Note:
      `projects/{project_id}/policies/{constraint_name}` is also an acceptable
      name for API requests, but responses will return the name using the
      equivalent project number.
    spec: Basic information about the organization policy.
  """

  alternate = _messages.MessageField('GoogleCloudOrgpolicyV2AlternatePolicySpec', 1)
  dryRunSpec = _messages.MessageField('GoogleCloudOrgpolicyV2PolicySpec', 2)
  etag = _messages.StringField(3)
  name = _messages.StringField(4)
  spec = _messages.MessageField('GoogleCloudOrgpolicyV2PolicySpec', 5)


class GoogleCloudOrgpolicyV2PolicySpec(_messages.Message):
  r"""Defines a Google Cloud policy specification which is used to specify
  constraints for configurations of Google Cloud resources.

  Fields:
    etag: An opaque tag indicating the current version of the policySpec, used
      for concurrency control. This field is ignored if used in a
      `CreatePolicy` request. When the policy is returned from either a
      `GetPolicy` or a `ListPolicies` request, this `etag` indicates the
      version of the current policySpec to use when executing a read-modify-
      write loop. When the policy is returned from a `GetEffectivePolicy`
      request, the `etag` will be unset.
    inheritFromParent: Determines the inheritance behavior for this policy. If
      `inherit_from_parent` is true, policy rules set higher up in the
      hierarchy (up to the closest root) are inherited and present in the
      effective policy. If it is false, then no rules are inherited, and this
      policy becomes the new root for evaluation. This field can be set only
      for policies which configure list constraints.
    reset: Ignores policies set above this resource and restores the
      `constraint_default` enforcement behavior of the specific constraint at
      this resource. This field can be set in policies for either list or
      boolean constraints. If set, `rules` must be empty and
      `inherit_from_parent` must be set to false.
    rules: In policies for boolean constraints, the following requirements
      apply: - There must be one and only one policy rule where condition is
      unset. - Boolean policy rules with conditions must set `enforced` to the
      opposite of the policy rule without a condition. - During policy
      evaluation, policy rules with conditions that are true for a target
      resource take precedence.
    updateTime: Output only. The time stamp this was previously updated. This
      represents the last time a call to `CreatePolicy` or `UpdatePolicy` was
      made for that policy.
  """

  etag = _messages.StringField(1)
  inheritFromParent = _messages.BooleanField(2)
  reset = _messages.BooleanField(3)
  rules = _messages.MessageField('GoogleCloudOrgpolicyV2PolicySpecPolicyRule', 4, repeated=True)
  updateTime = _messages.StringField(5)


class GoogleCloudOrgpolicyV2PolicySpecPolicyRule(_messages.Message):
  r"""A rule used to express this policy.

  Messages:
    ParametersValue: Optional. Required for managed constraints if parameters
      are defined. Passes parameter values when policy enforcement is enabled.
      Ensure that parameter value types match those defined in the constraint
      definition. For example: { "allowedLocations" : ["us-east1", "us-
      west1"], "allowAll" : true }

  Fields:
    allowAll: Setting this to true means that all values are allowed. This
      field can be set only in policies for list constraints.
    condition: A condition which determines whether this rule is used in the
      evaluation of the policy. When set, the `expression` field in the `Expr'
      must include from 1 to 10 subexpressions, joined by the "||" or "&&"
      operators. Each subexpression must be of the form
      "resource.matchTag('/tag_key_short_name, 'tag_value_short_name')". or
      "resource.matchTagId('tagKeys/key_id', 'tagValues/value_id')". where
      key_name and value_name are the resource names for Label Keys and
      Values. These names are available from the Tag Manager Service. An
      example expression is: "resource.matchTag('123456789/environment,
      'prod')". or "resource.matchTagId('tagKeys/123', 'tagValues/456')".
    denyAll: Setting this to true means that all values are denied. This field
      can be set only in policies for list constraints.
    enforce: If `true`, then the policy is enforced. If `false`, then any
      configuration is acceptable. This field can be set only in policies for
      boolean constraints.
    parameters: Optional. Required for managed constraints if parameters are
      defined. Passes parameter values when policy enforcement is enabled.
      Ensure that parameter value types match those defined in the constraint
      definition. For example: { "allowedLocations" : ["us-east1", "us-
      west1"], "allowAll" : true }
    resourceTypes: Optional. The resource types policies can support, only
      used for managed constraints. Method type is `GOVERN_TAGS`.
    values: List of values to be used for this policy rule. This field can be
      set only in policies for list constraints.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParametersValue(_messages.Message):
    r"""Optional. Required for managed constraints if parameters are defined.
    Passes parameter values when policy enforcement is enabled. Ensure that
    parameter value types match those defined in the constraint definition.
    For example: { "allowedLocations" : ["us-east1", "us-west1"], "allowAll" :
    true }

    Messages:
      AdditionalProperty: An additional property for a ParametersValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowAll = _messages.BooleanField(1)
  condition = _messages.MessageField('GoogleTypeExpr', 2)
  denyAll = _messages.BooleanField(3)
  enforce = _messages.BooleanField(4)
  parameters = _messages.MessageField('ParametersValue', 5)
  resourceTypes = _messages.MessageField('GoogleCloudOrgpolicyV2PolicySpecPolicyRuleResourceTypes', 6)
  values = _messages.MessageField('GoogleCloudOrgpolicyV2PolicySpecPolicyRuleStringValues', 7)


class GoogleCloudOrgpolicyV2PolicySpecPolicyRuleResourceTypes(_messages.Message):
  r"""Set multiple resource types for one policy. For example: resourceTypes:
  included: - compute.googleapis.com/Instance - compute.googleapis.com/Disk
  Constraint definition contains an empty resource type in order to support
  multiple resource types in the policy. Only supports managed constraints.
  Method type is `GOVERN_TAGS`.

  Fields:
    included: Optional. The resource types we currently support.
      cloud/orgpolicy/customconstraintconfig/prod/resource_types.prototext
  """

  included = _messages.StringField(1, repeated=True)


class GoogleCloudOrgpolicyV2PolicySpecPolicyRuleStringValues(_messages.Message):
  r"""A message that holds specific allowed and denied values. This message
  can define specific values and subtrees of the Resource Manager resource
  hierarchy (`Organizations`, `Folders`, `Projects`) that are allowed or
  denied. This is achieved by using the `under:` and optional `is:` prefixes.
  The `under:` prefix is used to denote resource subtree values. The `is:`
  prefix is used to denote specific values, and is required only if the value
  contains a ":". Values prefixed with "is:" are treated the same as values
  with no prefix. Ancestry subtrees must be in one of the following formats: -
  `projects/` (for example, `projects/tokyo-rain-123`) - `folders/` (for
  example, `folders/1234`) - `organizations/` (for example,
  `organizations/1234`) The `supports_under` field of the associated
  `Constraint` defines whether ancestry prefixes can be used.

  Fields:
    allowedValues: List of values allowed at this resource.
    deniedValues: List of values denied at this resource.
  """

  allowedValues = _messages.StringField(1, repeated=True)
  deniedValues = _messages.StringField(2, repeated=True)


class GoogleCloudPolicysimulatorV1Replay(_messages.Message):
  r"""A resource describing a `Replay`, or simulation.

  Enums:
    StateValueValuesEnum: Output only. The current state of the `Replay`.

  Fields:
    config: Required. The configuration used for the `Replay`.
    name: Output only. The resource name of the `Replay`, which has the
      following format: `{projects|folders|organizations}/{resource-
      id}/locations/global/replays/{replay-id}`, where `{resource-id}` is the
      ID of the project, folder, or organization that owns the Replay.
      Example: `projects/my-example-
      project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
    resultsSummary: Output only. Summary statistics about the replayed log
      entries.
    state: Output only. The current state of the `Replay`.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the `Replay`.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      PENDING: The `Replay` has not started yet.
      RUNNING: The `Replay` is currently running.
      SUCCEEDED: The `Replay` has successfully completed.
      FAILED: The `Replay` has finished with an error.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4

  config = _messages.MessageField('GoogleCloudPolicysimulatorV1ReplayConfig', 1)
  name = _messages.StringField(2)
  resultsSummary = _messages.MessageField('GoogleCloudPolicysimulatorV1ReplayResultsSummary', 3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class GoogleCloudPolicysimulatorV1ReplayConfig(_messages.Message):
  r"""The configuration used for a Replay.

  Enums:
    LogSourceValueValuesEnum: The logs to use as input for the Replay.

  Messages:
    PolicyOverlayValue: A mapping of the resources that you want to simulate
      policies for and the policies that you want to simulate. Keys are the
      full resource names for the resources. For example,
      `//cloudresourcemanager.googleapis.com/projects/my-project`. For
      examples of full resource names for Google Cloud services, see
      https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
      Values are Policy objects representing the policies that you want to
      simulate. Replays automatically take into account any IAM policies
      inherited through the resource hierarchy, and any policies set on
      descendant resources. You do not need to include these policies in the
      policy overlay.

  Fields:
    logSource: The logs to use as input for the Replay.
    policyOverlay: A mapping of the resources that you want to simulate
      policies for and the policies that you want to simulate. Keys are the
      full resource names for the resources. For example,
      `//cloudresourcemanager.googleapis.com/projects/my-project`. For
      examples of full resource names for Google Cloud services, see
      https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
      Values are Policy objects representing the policies that you want to
      simulate. Replays automatically take into account any IAM policies
      inherited through the resource hierarchy, and any policies set on
      descendant resources. You do not need to include these policies in the
      policy overlay.
  """

  class LogSourceValueValuesEnum(_messages.Enum):
    r"""The logs to use as input for the Replay.

    Values:
      LOG_SOURCE_UNSPECIFIED: An unspecified log source. If the log source is
        unspecified, the Replay defaults to using `RECENT_ACCESSES`.
      RECENT_ACCESSES: All access logs from the last 90 days. These logs may
        not include logs from the most recent 7 days.
    """
    LOG_SOURCE_UNSPECIFIED = 0
    RECENT_ACCESSES = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PolicyOverlayValue(_messages.Message):
    r"""A mapping of the resources that you want to simulate policies for and
    the policies that you want to simulate. Keys are the full resource names
    for the resources. For example,
    `//cloudresourcemanager.googleapis.com/projects/my-project`. For examples
    of full resource names for Google Cloud services, see
    https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
    Values are Policy objects representing the policies that you want to
    simulate. Replays automatically take into account any IAM policies
    inherited through the resource hierarchy, and any policies set on
    descendant resources. You do not need to include these policies in the
    policy overlay.

    Messages:
      AdditionalProperty: An additional property for a PolicyOverlayValue
        object.

    Fields:
      additionalProperties: Additional properties of type PolicyOverlayValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PolicyOverlayValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleIamV1Policy attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleIamV1Policy', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  logSource = _messages.EnumField('LogSourceValueValuesEnum', 1)
  policyOverlay = _messages.MessageField('PolicyOverlayValue', 2)


class GoogleCloudPolicysimulatorV1ReplayOperationMetadata(_messages.Message):
  r"""Metadata about a Replay operation.

  Fields:
    startTime: Time when the request was received.
  """

  startTime = _messages.StringField(1)


class GoogleCloudPolicysimulatorV1ReplayResultsSummary(_messages.Message):
  r"""Summary statistics about the replayed log entries.

  Fields:
    differenceCount: The number of replayed log entries with a difference
      between baseline and simulated policies.
    errorCount: The number of log entries that could not be replayed.
    logCount: The total number of log entries replayed.
    newestDate: The date of the newest log entry replayed.
    oldestDate: The date of the oldest log entry replayed.
    unchangedCount: The number of replayed log entries with no difference
      between baseline and simulated policies.
  """

  differenceCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  errorCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  logCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  newestDate = _messages.MessageField('GoogleTypeDate', 4)
  oldestDate = _messages.MessageField('GoogleTypeDate', 5)
  unchangedCount = _messages.IntegerField(6, variant=_messages.Variant.INT32)


class GoogleCloudPolicysimulatorV1alphaAccessActivity(_messages.Message):
  r"""Describes an access activity.

  Fields:
    permissionFqdn: The FQDN of the permission needed for the access.
    principal: The principal that will lose or gain access.
    resource: The resource the principal will lose or gain access to.
  """

  permissionFqdn = _messages.StringField(1)
  principal = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaAccessActivityPrincipal', 2)
  resource = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaAccessActivityResource', 3)


class GoogleCloudPolicysimulatorV1alphaAccessActivityPrincipal(_messages.Message):
  r"""The principal of the resource.

  Fields:
    subject: TODO(user) : Add few examples. The subject of the principal.
      For 1st party users, this is the email address. For 3rd party users,
      this is the 3P subject.
    type: The type of the principal. Supported principal types are Workspace,
      Workforce Pool, Workload Pool and Service Account. Allowed string must
      be one of: - iam.googleapis.com/WorkspaceIdentity -
      iam.googleapis.com/WorkforcePoolIdentity -
      iam.googleapis.com/WorkloadPoolIdentity -
      iam.googleapis.com/ServiceAccount
  """

  subject = _messages.StringField(1)
  type = _messages.StringField(2)


class GoogleCloudPolicysimulatorV1alphaAccessActivityResource(_messages.Message):
  r"""The resource the principal will lose or gain access to.

  Fields:
    fullResourceName: The full resource name of the resource.
    type: The resource type.
  """

  fullResourceName = _messages.StringField(1)
  type = _messages.StringField(2)


class GoogleCloudPolicysimulatorV1alphaAccessChange(_messages.Message):
  r"""Change in access for a given resource.

  Enums:
    LastRecordedDecisionValueValuesEnum: The most recently recorded decision
      of the access under evaluation.
    PostChangeDecisionValueValuesEnum: The decision of the access under
      evaluation with the policy overlay (using proposed policies).
    PreChangeDecisionValueValuesEnum: The decision of the access under
      evaluation without the policy overlay (using current production
      policies).

  Fields:
    accessActivity: The access activity which was evaluated for the
      simulation.
    daysAccessedCount: The number of days this access happened in the
      observation period.
    lastAccessDate: The time when the resource was last accessed (date only,
      no time).
    lastRecordedDecision: The most recently recorded decision of the access
      under evaluation.
    postChangeDecision: The decision of the access under evaluation with the
      policy overlay (using proposed policies).
    preChangeDecision: The decision of the access under evaluation without the
      policy overlay (using current production policies).
  """

  class LastRecordedDecisionValueValuesEnum(_messages.Enum):
    r"""The most recently recorded decision of the access under evaluation.

    Values:
      DECISION_UNSPECIFIED: Default value. This value is unused.
      GRANTED: <no description>
      NOT_GRANTED: The access is not allowed.
    """
    DECISION_UNSPECIFIED = 0
    GRANTED = 1
    NOT_GRANTED = 2

  class PostChangeDecisionValueValuesEnum(_messages.Enum):
    r"""The decision of the access under evaluation with the policy overlay
    (using proposed policies).

    Values:
      DECISION_UNSPECIFIED: Default value. This value is unused.
      GRANTED: <no description>
      NOT_GRANTED: The access is not allowed.
    """
    DECISION_UNSPECIFIED = 0
    GRANTED = 1
    NOT_GRANTED = 2

  class PreChangeDecisionValueValuesEnum(_messages.Enum):
    r"""The decision of the access under evaluation without the policy overlay
    (using current production policies).

    Values:
      DECISION_UNSPECIFIED: Default value. This value is unused.
      GRANTED: <no description>
      NOT_GRANTED: The access is not allowed.
    """
    DECISION_UNSPECIFIED = 0
    GRANTED = 1
    NOT_GRANTED = 2

  accessActivity = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaAccessActivity', 1)
  daysAccessedCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  lastAccessDate = _messages.MessageField('GoogleTypeDate', 3)
  lastRecordedDecision = _messages.EnumField('LastRecordedDecisionValueValuesEnum', 4)
  postChangeDecision = _messages.EnumField('PostChangeDecisionValueValuesEnum', 5)
  preChangeDecision = _messages.EnumField('PreChangeDecisionValueValuesEnum', 6)


class GoogleCloudPolicysimulatorV1alphaAccessPolicySimulation(_messages.Message):
  r"""AccessPolicySimulation resource.

  Enums:
    StateValueValuesEnum: Output only. The state of the simulation. Output
      only.

  Fields:
    changeOverlay: Required. Immutable. The overlay to apply to the
      simulation.
    createTime: Output only. The time when the simulation was created. Output
      only.
    endTime: Output only. The time when the simulation ended. Output only.
    name: Identifier. The resource name of the simulation. Output only.
      {projects|folders|organizations}/{resource-
      id}/locations/{location}/accessPolicySimulations/{simulation_id}
    observationPeriod: Output only. The observation period for access requests
      evaluated during the simulation.
    simulationSummary: Output only. Summary of simulation results. Output
      only.
    startTime: Output only. The time when the simulation started. Output only.
    state: Output only. The state of the simulation. Output only.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the simulation. Output only.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      PENDING: The `AccessPolicySimulation` has not started yet.
      RUNNING: The `AccessPolicySimulation` is currently running.
      SUCCEEDED: The `AccessPolicySimulation` has successfully completed.
      FAILED: The `AccessPolicySimulation` has finished with an error.
      CANCELLED: The `AccessPolicySimulation` has been cancelled.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4
    CANCELLED = 5

  changeOverlay = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaChangeOverlay', 1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  name = _messages.StringField(4)
  observationPeriod = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaObservationPeriod', 5)
  simulationSummary = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaAccessPolicySimulationAccessPolicySimulationSummary', 6)
  startTime = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)


class GoogleCloudPolicysimulatorV1alphaAccessPolicySimulationAccessChangeSummary(_messages.Message):
  r"""Summary of the access changes detected during the simulation.

  Fields:
    impactedPrincipalsCount: Output only. Principals for which at least one
      access changed from allow to deny.
    impactedResourcesCount: Output only. Resources for which at least one
      access changed from allow to deny.
    newlyDeniedCount: Output only. Number of access that changed from allow to
      deny.
    totalDiffCount: Output only. Number of accesses with a changed decision
      between baseline and simulated policies.
  """

  impactedPrincipalsCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  impactedResourcesCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  newlyDeniedCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  totalDiffCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudPolicysimulatorV1alphaAccessPolicySimulationAccessPolicySimulationSummary(_messages.Message):
  r"""Summary statistics about the simulated policies.

  Fields:
    accessChangeSummary: Output only. Summary of the access changes detected
      during the simulation.
    evaluationSummary: Output only. Summary of the access requests evaluated
      during the simulation.
  """

  accessChangeSummary = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaAccessPolicySimulationAccessChangeSummary', 1)
  evaluationSummary = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaAccessPolicySimulationEvaluationSummary', 2)


class GoogleCloudPolicysimulatorV1alphaAccessPolicySimulationEvaluationSummary(_messages.Message):
  r"""Summary of the access requests evaluated during the simulation.

  Fields:
    evaluatedAccessCount: Output only. Number of accesses evaluated for the
      simulation.
    evaluatedPrincipalsCount: Output only. Number of unique principals
      evaluated for the simulation.
    evaluatedResourcesCount: Output only. Number of unique resources evaluated
      for the simulation.
  """

  evaluatedAccessCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  evaluatedPrincipalsCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  evaluatedResourcesCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleCloudPolicysimulatorV1alphaAccessPolicySimulationResult(_messages.Message):
  r"""Result for a given simulation.

  Fields:
    accessChange: resource access diff.
    name: Identifier. The resource name of the `AccessPolicySimulationResult`,
      in the following format: `{projects|folders|organizations}/{resource-
      id}/locations/global/accessPolicySimulations/{simulation-
      id}/results/{result-id}`, where `{resource-id}` is the ID of the CRM
      node. that owns the AccessPolicySimulation.
  """

  accessChange = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaAccessChange', 1)
  name = _messages.StringField(2)


class GoogleCloudPolicysimulatorV1alphaAccessStateDiff(_messages.Message):
  r"""A summary and comparison of the principal's access under the current
  (baseline) policies and the proposed (simulated) policies for a single
  access tuple.

  Enums:
    AccessChangeValueValuesEnum: How the principal's access, specified in the
      AccessState field, changed between the current (baseline) policies and
      proposed (simulated) policies.

  Fields:
    accessChange: How the principal's access, specified in the AccessState
      field, changed between the current (baseline) policies and proposed
      (simulated) policies.
    baseline: The results of evaluating the access tuple under the current
      (baseline) policies. If the AccessState couldn't be fully evaluated,
      this field explains why.
    simulated: The results of evaluating the access tuple under the proposed
      (simulated) policies. If the AccessState couldn't be fully evaluated,
      this field explains why.
  """

  class AccessChangeValueValuesEnum(_messages.Enum):
    r"""How the principal's access, specified in the AccessState field,
    changed between the current (baseline) policies and proposed (simulated)
    policies.

    Values:
      ACCESS_CHANGE_TYPE_UNSPECIFIED: Default value. This value is unused.
      NO_CHANGE: The principal's access did not change. This includes the case
        where both baseline and simulated are UNKNOWN, but the unknown
        information is equivalent.
      UNKNOWN_CHANGE: The principal's access under both the current policies
        and the proposed policies is `UNKNOWN`, but the unknown information
        differs between them.
      ACCESS_REVOKED: The principal had access under the current policies
        (`GRANTED`), but will no longer have access after the proposed changes
        (`NOT_GRANTED`).
      ACCESS_GAINED: The principal did not have access under the current
        policies (`NOT_GRANTED`), but will have access after the proposed
        changes (`GRANTED`).
      ACCESS_MAYBE_REVOKED: This result can occur for the following reasons: *
        The principal had access under the current policies (`GRANTED`), but
        their access after the proposed changes is `UNKNOWN`. * The
        principal's access under the current policies is `UNKNOWN`, but they
        will not have access after the proposed changes (`NOT_GRANTED`).
      ACCESS_MAYBE_GAINED: This result can occur for the following reasons: *
        The principal did not have access under the current policies
        (`NOT_GRANTED`), but their access after the proposed changes is
        `UNKNOWN`. * The principal's access under the current policies is
        `UNKNOWN`, but they will have access after the proposed changes
        (`GRANTED`).
    """
    ACCESS_CHANGE_TYPE_UNSPECIFIED = 0
    NO_CHANGE = 1
    UNKNOWN_CHANGE = 2
    ACCESS_REVOKED = 3
    ACCESS_GAINED = 4
    ACCESS_MAYBE_REVOKED = 5
    ACCESS_MAYBE_GAINED = 6

  accessChange = _messages.EnumField('AccessChangeValueValuesEnum', 1)
  baseline = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaExplainedAccess', 2)
  simulated = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaExplainedAccess', 3)


class GoogleCloudPolicysimulatorV1alphaAccessTuple(_messages.Message):
  r"""Information about the principal, resource, and permission to check.

  Fields:
    fullResourceName: Required. The full resource name that identifies the
      resource. For example, `//compute.googleapis.com/projects/my-
      project/zones/us-central1-a/instances/my-instance`. For examples of full
      resource names for Google Cloud services, see
      https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
    permission: Required. The IAM permission to check for the specified
      principal and resource. For a complete list of IAM permissions, see
      https://cloud.google.com/iam/help/permissions/reference. For a complete
      list of predefined IAM roles and the permissions in each role, see
      https://cloud.google.com/iam/help/roles/reference.
    principal: Required. The principal whose access you want to check, in the
      form of the email address that represents that principal. For example,
      `<EMAIL>` or `my-service-account@my-
      project.iam.gserviceaccount.com`. The principal must be a Google Account
      or a service account. Other types of principals are not supported.
  """

  fullResourceName = _messages.StringField(1)
  permission = _messages.StringField(2)
  principal = _messages.StringField(3)


class GoogleCloudPolicysimulatorV1alphaBindingExplanation(_messages.Message):
  r"""Details about how a binding in a policy affects a principal's ability to
  use a permission.

  Enums:
    AccessValueValuesEnum: Required. Indicates whether _this binding_ provides
      the specified permission to the specified principal for the specified
      resource. This field does _not_ indicate whether the principal actually
      has the permission for the resource. There might be another binding that
      overrides this binding. To determine whether the principal actually has
      the permission, use the `access` field in the
      TroubleshootIamPolicyResponse.
    RelevanceValueValuesEnum: The relevance of this binding to the overall
      determination for the entire policy.
    RolePermissionValueValuesEnum: Indicates whether the role granted by this
      binding contains the specified permission.
    RolePermissionRelevanceValueValuesEnum: The relevance of the permission's
      existence, or nonexistence, in the role to the overall determination for
      the entire policy.

  Messages:
    MembershipsValue: Indicates whether each principal in the binding includes
      the principal specified in the request, either directly or indirectly.
      Each key identifies a principal in the binding, and each value indicates
      whether the principal in the binding includes the principal in the
      request. For example, suppose that a binding includes the following
      principals: * `user:<EMAIL>` * `group:<EMAIL>`
      The principal in the replayed access tuple is `user:<EMAIL>`.
      This user is a principal of the group `group:<EMAIL>`.
      For the first principal in the binding, the key is
      `user:<EMAIL>`, and the `membership` field in the value is set
      to `MEMBERSHIP_NOT_INCLUDED`. For the second principal in the binding,
      the key is `group:<EMAIL>`, and the `membership` field
      in the value is set to `MEMBERSHIP_INCLUDED`.

  Fields:
    access: Required. Indicates whether _this binding_ provides the specified
      permission to the specified principal for the specified resource. This
      field does _not_ indicate whether the principal actually has the
      permission for the resource. There might be another binding that
      overrides this binding. To determine whether the principal actually has
      the permission, use the `access` field in the
      TroubleshootIamPolicyResponse.
    condition: A condition expression that prevents this binding from granting
      access unless the expression evaluates to `true`. To learn about IAM
      Conditions, see https://cloud.google.com/iam/docs/conditions-overview.
    memberships: Indicates whether each principal in the binding includes the
      principal specified in the request, either directly or indirectly. Each
      key identifies a principal in the binding, and each value indicates
      whether the principal in the binding includes the principal in the
      request. For example, suppose that a binding includes the following
      principals: * `user:<EMAIL>` * `group:<EMAIL>`
      The principal in the replayed access tuple is `user:<EMAIL>`.
      This user is a principal of the group `group:<EMAIL>`.
      For the first principal in the binding, the key is
      `user:<EMAIL>`, and the `membership` field in the value is set
      to `MEMBERSHIP_NOT_INCLUDED`. For the second principal in the binding,
      the key is `group:<EMAIL>`, and the `membership` field
      in the value is set to `MEMBERSHIP_INCLUDED`.
    relevance: The relevance of this binding to the overall determination for
      the entire policy.
    role: The role that this binding grants. For example,
      `roles/compute.serviceAgent`. For a complete list of predefined IAM
      roles, as well as the permissions in each role, see
      https://cloud.google.com/iam/help/roles/reference.
    rolePermission: Indicates whether the role granted by this binding
      contains the specified permission.
    rolePermissionRelevance: The relevance of the permission's existence, or
      nonexistence, in the role to the overall determination for the entire
      policy.
  """

  class AccessValueValuesEnum(_messages.Enum):
    r"""Required. Indicates whether _this binding_ provides the specified
    permission to the specified principal for the specified resource. This
    field does _not_ indicate whether the principal actually has the
    permission for the resource. There might be another binding that overrides
    this binding. To determine whether the principal actually has the
    permission, use the `access` field in the TroubleshootIamPolicyResponse.

    Values:
      ACCESS_STATE_UNSPECIFIED: Default value. This value is unused.
      GRANTED: The principal has the permission.
      NOT_GRANTED: The principal does not have the permission.
      UNKNOWN_CONDITIONAL: The principal has the permission only if a
        condition expression evaluates to `true`.
      UNKNOWN_INFO_DENIED: The user who created the Replay does not have
        access to all of the policies that Policy Simulator needs to evaluate.
    """
    ACCESS_STATE_UNSPECIFIED = 0
    GRANTED = 1
    NOT_GRANTED = 2
    UNKNOWN_CONDITIONAL = 3
    UNKNOWN_INFO_DENIED = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this binding to the overall determination for the
    entire policy.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Default value. This value is unused.
      NORMAL: The data point has a limited effect on the result. Changing the
        data point is unlikely to affect the overall determination.
      HIGH: The data point has a strong effect on the result. Changing the
        data point is likely to affect the overall determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    NORMAL = 1
    HIGH = 2

  class RolePermissionRelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the permission's existence, or nonexistence, in the
    role to the overall determination for the entire policy.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Default value. This value is unused.
      NORMAL: The data point has a limited effect on the result. Changing the
        data point is unlikely to affect the overall determination.
      HIGH: The data point has a strong effect on the result. Changing the
        data point is likely to affect the overall determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    NORMAL = 1
    HIGH = 2

  class RolePermissionValueValuesEnum(_messages.Enum):
    r"""Indicates whether the role granted by this binding contains the
    specified permission.

    Values:
      ROLE_PERMISSION_UNSPECIFIED: Default value. This value is unused.
      ROLE_PERMISSION_INCLUDED: The permission is included in the role.
      ROLE_PERMISSION_NOT_INCLUDED: The permission is not included in the
        role.
      ROLE_PERMISSION_UNKNOWN_INFO_DENIED: The user who created the Replay is
        not allowed to access the binding.
    """
    ROLE_PERMISSION_UNSPECIFIED = 0
    ROLE_PERMISSION_INCLUDED = 1
    ROLE_PERMISSION_NOT_INCLUDED = 2
    ROLE_PERMISSION_UNKNOWN_INFO_DENIED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MembershipsValue(_messages.Message):
    r"""Indicates whether each principal in the binding includes the principal
    specified in the request, either directly or indirectly. Each key
    identifies a principal in the binding, and each value indicates whether
    the principal in the binding includes the principal in the request. For
    example, suppose that a binding includes the following principals: *
    `user:<EMAIL>` * `group:<EMAIL>` The principal
    in the replayed access tuple is `user:<EMAIL>`. This user is a
    principal of the group `group:<EMAIL>`. For the first
    principal in the binding, the key is `user:<EMAIL>`, and the
    `membership` field in the value is set to `MEMBERSHIP_NOT_INCLUDED`. For
    the second principal in the binding, the key is `group:product-
    <EMAIL>`, and the `membership` field in the value is set to
    `MEMBERSHIP_INCLUDED`.

    Messages:
      AdditionalProperty: An additional property for a MembershipsValue
        object.

    Fields:
      additionalProperties: Additional properties of type MembershipsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MembershipsValue object.

      Fields:
        key: Name of the additional property.
        value: A
          GoogleCloudPolicysimulatorV1alphaBindingExplanationAnnotatedMembersh
          ip attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaBindingExplanationAnnotatedMembership', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  access = _messages.EnumField('AccessValueValuesEnum', 1)
  condition = _messages.MessageField('GoogleTypeExpr', 2)
  memberships = _messages.MessageField('MembershipsValue', 3)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 4)
  role = _messages.StringField(5)
  rolePermission = _messages.EnumField('RolePermissionValueValuesEnum', 6)
  rolePermissionRelevance = _messages.EnumField('RolePermissionRelevanceValueValuesEnum', 7)


class GoogleCloudPolicysimulatorV1alphaBindingExplanationAnnotatedMembership(_messages.Message):
  r"""Details about whether the binding includes the principal.

  Enums:
    MembershipValueValuesEnum: Indicates whether the binding includes the
      principal.
    RelevanceValueValuesEnum: The relevance of the principal's status to the
      overall determination for the binding.

  Fields:
    membership: Indicates whether the binding includes the principal.
    relevance: The relevance of the principal's status to the overall
      determination for the binding.
  """

  class MembershipValueValuesEnum(_messages.Enum):
    r"""Indicates whether the binding includes the principal.

    Values:
      MEMBERSHIP_UNSPECIFIED: Default value. This value is unused.
      MEMBERSHIP_INCLUDED: The binding includes the principal. The principal
        can be included directly or indirectly. For example: * A principal is
        included directly if that principal is listed in the binding. * A
        principal is included indirectly if that principal is in a Google
        group or Google Workspace domain that is listed in the binding.
      MEMBERSHIP_NOT_INCLUDED: The binding does not include the principal.
      MEMBERSHIP_UNKNOWN_INFO_DENIED: The user who created the Replay is not
        allowed to access the binding.
      MEMBERSHIP_UNKNOWN_UNSUPPORTED: The principal is an unsupported type.
        Only Google Accounts and service accounts are supported.
    """
    MEMBERSHIP_UNSPECIFIED = 0
    MEMBERSHIP_INCLUDED = 1
    MEMBERSHIP_NOT_INCLUDED = 2
    MEMBERSHIP_UNKNOWN_INFO_DENIED = 3
    MEMBERSHIP_UNKNOWN_UNSUPPORTED = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the principal's status to the overall determination
    for the binding.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Default value. This value is unused.
      NORMAL: The data point has a limited effect on the result. Changing the
        data point is unlikely to affect the overall determination.
      HIGH: The data point has a strong effect on the result. Changing the
        data point is likely to affect the overall determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    NORMAL = 1
    HIGH = 2

  membership = _messages.EnumField('MembershipValueValuesEnum', 1)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 2)


class GoogleCloudPolicysimulatorV1alphaChangeOverlay(_messages.Message):
  r"""Policy Overlay for the Simulator.

  Fields:
    mutations: Required. Immutable. Combination of policies which are changed
      in this overlay.
  """

  mutations = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaMutation', 1, repeated=True)


class GoogleCloudPolicysimulatorV1alphaCreateOrgPolicyViolationsPreviewOperationMetadata(_messages.Message):
  r"""CreateOrgPolicyViolationsPreviewOperationMetadata is metadata about an
  OrgPolicyViolationsPreview generations operation.

  Enums:
    StateValueValuesEnum: Output only. The current state of the operation.

  Fields:
    requestTime: Time when the request was received.
    resourcesFound: Total number of resources that need scanning. Should equal
      resource_scanned + resources_pending
    resourcesPending: Number of resources still to scan.
    resourcesScanned: Number of resources already scanned.
    startTime: Time when the request started processing, i.e., when the state
      was set to RUNNING.
    state: Output only. The current state of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the operation.

    Values:
      PREVIEW_STATE_UNSPECIFIED: The state is unspecified.
      PREVIEW_PENDING: The OrgPolicyViolationsPreview has not been created
        yet.
      PREVIEW_RUNNING: The OrgPolicyViolationsPreview is currently being
        created.
      PREVIEW_SUCCEEDED: The OrgPolicyViolationsPreview creation finished
        successfully.
      PREVIEW_FAILED: The OrgPolicyViolationsPreview creation failed with an
        error.
    """
    PREVIEW_STATE_UNSPECIFIED = 0
    PREVIEW_PENDING = 1
    PREVIEW_RUNNING = 2
    PREVIEW_SUCCEEDED = 3
    PREVIEW_FAILED = 4

  requestTime = _messages.StringField(1)
  resourcesFound = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  resourcesPending = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  resourcesScanned = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  startTime = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class GoogleCloudPolicysimulatorV1alphaDenyPolicy(_messages.Message):
  r"""Data for an IAM policy.

  Messages:
    AnnotationsValue: Immutable. A key-value map to store arbitrary metadata
      for the `Policy`. Keys can be up to 63 characters. Values can be up to
      255 characters.

  Fields:
    annotations: Immutable. A key-value map to store arbitrary metadata for
      the `Policy`. Keys can be up to 63 characters. Values can be up to 255
      characters.
    displayName: Immutable. A user-specified description of the `Policy`. This
      value can be up to 63 characters.
    etag: Immutable. An opaque tag that identifies the current version of the
      `Policy`. IAM uses this value to help manage concurrent updates, so they
      do not cause one update to be overwritten by another. If this field is
      present in a CreatePolicyRequest, the value is ignored.
    kind: Immutable. The kind of the `Policy`. Always contains the value
      `DenyPolicy`.
    managingAuthority: Immutable. Specifies that this policy is managed by an
      authority and can only be modified by that authority. Usage is
      restricted.
    policy: Immutable. The name of the original policy resource. For example,
      `policies/cloudresourcemanager.googleapis.com/{projects|folders|organiza
      tions}/{resource-id}/denypolicies/{deny-policy-id}`.
    rules: Immutable. A list of rules that specify the behavior of the
      `Policy`. All of the rules should be of the `kind` specified in the
      `Policy`.
    uid: Immutable. The globally unique ID of the `Policy`. Assigned
      automatically when the `Policy` is created.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Immutable. A key-value map to store arbitrary metadata for the
    `Policy`. Keys can be up to 63 characters. Values can be up to 255
    characters.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  displayName = _messages.StringField(2)
  etag = _messages.StringField(3)
  kind = _messages.StringField(4)
  managingAuthority = _messages.StringField(5)
  policy = _messages.StringField(6)
  rules = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaDenyPolicyRule', 7, repeated=True)
  uid = _messages.StringField(8)


class GoogleCloudPolicysimulatorV1alphaDenyPolicyOverlay(_messages.Message):
  r"""PolicyOverlay for Deny Policy.

  Fields:
    denyPolicy: Required. The Deny policy.
    force: Optional. If true, the policy will be deleted even if the policy is
      bound to targets. Only supported for DELETE action.
    parent: Required. Parent of the policy.
    policyId: Optional. The ID to use for the deny policy, which will become
      the final component of the deny policy's resource name. This value must
      start with a lowercase letter followed by up to 62 lowercase letters,
      numbers, hyphens, or dots. Pattern, /a-z{2,62}/.
    updateMask: Optional. An empty update mask will imply that only the
      present fields are being updated. Set to `*` to completely overwrite. If
      the policy doesn't currently exist (i.e. a Create), update_mask is
      ignored.
  """

  denyPolicy = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaDenyPolicy', 1)
  force = _messages.BooleanField(2)
  parent = _messages.StringField(3)
  policyId = _messages.StringField(4)
  updateMask = _messages.StringField(5)


class GoogleCloudPolicysimulatorV1alphaDenyPolicyRule(_messages.Message):
  r"""A single rule in a `Policy`.

  Fields:
    denyRule: A rule for a deny policy.
    description: Immutable. A user-specified description of the rule. This
      value can be up to 256 characters.
  """

  denyRule = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaDenyRule', 1)
  description = _messages.StringField(2)


class GoogleCloudPolicysimulatorV1alphaDenyRule(_messages.Message):
  r"""A deny rule in an IAM deny policy.

  Fields:
    denialCondition: Immutable. The condition that determines whether this
      deny rule applies to a request. If the condition expression evaluates to
      `true`, then the deny rule is applied; otherwise, the deny rule is not
      applied. Each deny rule is evaluated independently. If this deny rule
      does not apply to a request, other deny rules might still apply. The
      condition can use CEL functions that evaluate [resource
      tags](https://cloud.google.com/iam/help/conditions/resource-tags). Other
      functions and operators are not supported.
    deniedPermissions: Immutable. The permissions that are explicitly denied
      by this rule. Each permission uses the format
      `{service_fqdn}/{resource}.{verb}`, where `{service_fqdn}` is the fully
      qualified domain name for the service. For example,
      `iam.googleapis.com/roles.list`.
    deniedPrincipals: Immutable. The identities that are prevented from using
      one or more permissions on Google Cloud resources. This field can
      contain the following values: * `principal://goog/subject/{email_id}`: A
      specific Google Account. Includes Gmail, Cloud Identity, and Google
      Workspace user accounts. For example,
      `principal://goog/subject/<EMAIL>`. * `principal://iam.googlea
      pis.com/projects/-/serviceAccounts/{service_account_id}`: A Google Cloud
      service account. For example,
      `principal://iam.googleapis.com/projects/-/serviceAccounts/my-service-
      <EMAIL>`. *
      `principalSet://goog/group/{group_id}`: A Google group. For example,
      `principalSet://goog/group/<EMAIL>`. *
      `principalSet://goog/public:all`: A special identifier that represents
      any principal that is on the internet, even if they do not have a Google
      Account or are not logged in. *
      `principalSet://goog/cloudIdentityCustomerId/{customer_id}`: All of the
      principals associated with the specified Google Workspace or Cloud
      Identity customer ID. For example,
      `principalSet://goog/cloudIdentityCustomerId/C01Abc35`. * `principal://i
      am.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{sub
      ject_attribute_value}`: A single identity in a workforce identity pool.
      * `principalSet://iam.googleapis.com/locations/global/workforcePools/{po
      ol_id}/group/{group_id}`: All workforce identities in a group. * `princi
      palSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/at
      tribute.{attribute_name}/{attribute_value}`: All workforce identities
      with a specific attribute value. * `principalSet://iam.googleapis.com/lo
      cations/global/workforcePools/{pool_id}/*`: All identities in a
      workforce identity pool. * `principal://iam.googleapis.com/projects/{pro
      ject_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{s
      ubject_attribute_value}`: A single identity in a workload identity pool.
      * `principalSet://iam.googleapis.com/projects/{project_number}/locations
      /global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload
      identity pool group. * `principalSet://iam.googleapis.com/projects/{proj
      ect_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{
      attribute_name}/{attribute_value}`: All identities in a workload
      identity pool with a certain attribute. * `principalSet://iam.googleapis
      .com/projects/{project_number}/locations/global/workloadIdentityPools/{p
      ool_id}/*`: All identities in a workload identity pool. * `principalSet:
      //cloudresourcemanager.googleapis.com/[projects|folders|organizations]/{
      project_number|folder_number|org_number}/type/ServiceAccount`: All
      service accounts grouped under a resource (project, folder, or
      organization). * `principalSet://cloudresourcemanager.googleapis.com/[pr
      ojects|folders|organizations]/{project_number|folder_number|org_number}/
      type/ServiceAgent`: All service agents grouped under a resource
      (project, folder, or organization). *
      `deleted:principal://goog/subject/{email_id}?uid={uid}`: A specific
      Google Account that was deleted recently. For example,
      `deleted:principal://goog/subject/<EMAIL>?uid=**********`. If
      the Google Account is recovered, this identifier reverts to the standard
      identifier for a Google Account. *
      `deleted:principalSet://goog/group/{group_id}?uid={uid}`: A Google group
      that was deleted recently. For example,
      `deleted:principalSet://goog/group/<EMAIL>?uid=**********`.
      If the Google group is restored, this identifier reverts to the standard
      identifier for a Google group. * `deleted:principal://iam.googleapis.com
      /projects/-/serviceAccounts/{service_account_id}?uid={uid}`: A Google
      Cloud service account that was deleted recently. For example,
      `deleted:principal://iam.googleapis.com/projects/-/serviceAccounts/my-
      <EMAIL>?uid=**********`. If the service
      account is undeleted, this identifier reverts to the standard identifier
      for a service account. * `deleted:principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`:
      Deleted single identity in a workforce identity pool. For example, `dele
      ted:principal://iam.googleapis.com/locations/global/workforcePools/my-
      pool-id/subject/my-subject-attribute-value`.
    exceptionPermissions: Immutable. Specifies the permissions that this rule
      excludes from the set of denied permissions given by
      `denied_permissions`. If a permission appears in `denied_permissions`
      _and_ in `exception_permissions` then it will _not_ be denied. The
      excluded permissions can be specified using the same syntax as
      `denied_permissions`.
    exceptionPrincipals: Immutable. The identities that are excluded from the
      deny rule, even if they are listed in the `denied_principals`. For
      example, you could add a Google group to the `denied_principals`, then
      exclude specific users who belong to that group. This field can contain
      the same values as the `denied_principals` field, excluding
      `principalSet://goog/public:all`, which represents all users on the
      internet.
    exemptedCredentialLevels: Immutable. A list of credential levels that are
      excluded from this rule. If a request contains _any_ of the
      exempted_credential_levels, it will _not_ be denied.
  """

  denialCondition = _messages.MessageField('GoogleTypeExpr', 1)
  deniedPermissions = _messages.StringField(2, repeated=True)
  deniedPrincipals = _messages.StringField(3, repeated=True)
  exceptionPermissions = _messages.StringField(4, repeated=True)
  exceptionPrincipals = _messages.StringField(5, repeated=True)
  exemptedCredentialLevels = _messages.StringField(6, repeated=True)


class GoogleCloudPolicysimulatorV1alphaExplainedAccess(_messages.Message):
  r"""Details about how a set of policies, listed in ExplainedPolicy, resulted
  in a certain AccessState when replaying an access tuple.

  Enums:
    AccessStateValueValuesEnum: Whether the principal in the access tuple has
      permission to access the resource in the access tuple under the given
      policies.

  Fields:
    accessState: Whether the principal in the access tuple has permission to
      access the resource in the access tuple under the given policies.
    errors: If the AccessState is `UNKNOWN`, this field contains a list of
      errors explaining why the result is `UNKNOWN`. If the `AccessState` is
      `GRANTED` or `NOT_GRANTED`, this field is omitted.
    policies: If the AccessState is `UNKNOWN`, this field contains the
      policies that led to that result. If the `AccessState` is `GRANTED` or
      `NOT_GRANTED`, this field is omitted.
  """

  class AccessStateValueValuesEnum(_messages.Enum):
    r"""Whether the principal in the access tuple has permission to access the
    resource in the access tuple under the given policies.

    Values:
      ACCESS_STATE_UNSPECIFIED: Default value. This value is unused.
      GRANTED: The principal has the permission.
      NOT_GRANTED: The principal does not have the permission.
      UNKNOWN_CONDITIONAL: The principal has the permission only if a
        condition expression evaluates to `true`.
      UNKNOWN_INFO_DENIED: The user who created the Replay does not have
        access to all of the policies that Policy Simulator needs to evaluate.
    """
    ACCESS_STATE_UNSPECIFIED = 0
    GRANTED = 1
    NOT_GRANTED = 2
    UNKNOWN_CONDITIONAL = 3
    UNKNOWN_INFO_DENIED = 4

  accessState = _messages.EnumField('AccessStateValueValuesEnum', 1)
  errors = _messages.MessageField('GoogleRpcStatus', 2, repeated=True)
  policies = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaExplainedPolicy', 3, repeated=True)


class GoogleCloudPolicysimulatorV1alphaExplainedPolicy(_messages.Message):
  r"""Details about how a specific IAM Policy contributed to the access check.

  Enums:
    AccessValueValuesEnum: Indicates whether _this policy_ provides the
      specified permission to the specified principal for the specified
      resource. This field does _not_ indicate whether the principal actually
      has the permission for the resource. There might be another policy that
      overrides this policy. To determine whether the principal actually has
      the permission, use the `access` field in the
      TroubleshootIamPolicyResponse.
    RelevanceValueValuesEnum: The relevance of this policy to the overall
      determination in the TroubleshootIamPolicyResponse. If the user who
      created the Replay does not have access to the policy, this field is
      omitted.

  Fields:
    access: Indicates whether _this policy_ provides the specified permission
      to the specified principal for the specified resource. This field does
      _not_ indicate whether the principal actually has the permission for the
      resource. There might be another policy that overrides this policy. To
      determine whether the principal actually has the permission, use the
      `access` field in the TroubleshootIamPolicyResponse.
    bindingExplanations: Details about how each binding in the policy affects
      the principal's ability, or inability, to use the permission for the
      resource. If the user who created the Replay does not have access to the
      policy, this field is omitted.
    fullResourceName: The full resource name that identifies the resource. For
      example, `//compute.googleapis.com/projects/my-project/zones/us-
      central1-a/instances/my-instance`. If the user who created the Replay
      does not have access to the policy, this field is omitted. For examples
      of full resource names for Google Cloud services, see
      https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
    policy: The IAM policy attached to the resource. If the user who created
      the Replay does not have access to the policy, this field is empty.
    relevance: The relevance of this policy to the overall determination in
      the TroubleshootIamPolicyResponse. If the user who created the Replay
      does not have access to the policy, this field is omitted.
  """

  class AccessValueValuesEnum(_messages.Enum):
    r"""Indicates whether _this policy_ provides the specified permission to
    the specified principal for the specified resource. This field does _not_
    indicate whether the principal actually has the permission for the
    resource. There might be another policy that overrides this policy. To
    determine whether the principal actually has the permission, use the
    `access` field in the TroubleshootIamPolicyResponse.

    Values:
      ACCESS_STATE_UNSPECIFIED: Default value. This value is unused.
      GRANTED: The principal has the permission.
      NOT_GRANTED: The principal does not have the permission.
      UNKNOWN_CONDITIONAL: The principal has the permission only if a
        condition expression evaluates to `true`.
      UNKNOWN_INFO_DENIED: The user who created the Replay does not have
        access to all of the policies that Policy Simulator needs to evaluate.
    """
    ACCESS_STATE_UNSPECIFIED = 0
    GRANTED = 1
    NOT_GRANTED = 2
    UNKNOWN_CONDITIONAL = 3
    UNKNOWN_INFO_DENIED = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this policy to the overall determination in the
    TroubleshootIamPolicyResponse. If the user who created the Replay does not
    have access to the policy, this field is omitted.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Default value. This value is unused.
      NORMAL: The data point has a limited effect on the result. Changing the
        data point is unlikely to affect the overall determination.
      HIGH: The data point has a strong effect on the result. Changing the
        data point is likely to affect the overall determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    NORMAL = 1
    HIGH = 2

  access = _messages.EnumField('AccessValueValuesEnum', 1)
  bindingExplanations = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaBindingExplanation', 2, repeated=True)
  fullResourceName = _messages.StringField(3)
  policy = _messages.MessageField('GoogleIamV1Policy', 4)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 5)


class GoogleCloudPolicysimulatorV1alphaGenerateOrgPolicyViolationsPreviewOperationMetadata(_messages.Message):
  r"""GenerateOrgPolicyViolationsPreviewOperationMetadata is metadata about an
  OrgPolicyViolationsPreview generations operation.

  Enums:
    StateValueValuesEnum: The current state of the operation.

  Fields:
    requestTime: Time when the request was received.
    resourcesFound: Total number of resources that need scanning. Should equal
      resource_scanned + resources_pending
    resourcesPending: Number of resources still to scan.
    resourcesScanned: Number of resources already scanned.
    startTime: Time when the request started processing, i.e. when the state
      was set to RUNNING.
    state: The current state of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the operation.

    Values:
      PREVIEW_STATE_UNSPECIFIED: The state is unspecified.
      PREVIEW_PENDING: The OrgPolicyViolationsPreview has not been created
        yet.
      PREVIEW_RUNNING: The OrgPolicyViolationsPreview is currently being
        created.
      PREVIEW_SUCCEEDED: The OrgPolicyViolationsPreview creation finished
        successfully.
      PREVIEW_FAILED: The OrgPolicyViolationsPreview creation failed with an
        error.
    """
    PREVIEW_STATE_UNSPECIFIED = 0
    PREVIEW_PENDING = 1
    PREVIEW_RUNNING = 2
    PREVIEW_SUCCEEDED = 3
    PREVIEW_FAILED = 4

  requestTime = _messages.StringField(1)
  resourcesFound = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  resourcesPending = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  resourcesScanned = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  startTime = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class GoogleCloudPolicysimulatorV1alphaListAccessPolicySimulationResultsResponse(_messages.Message):
  r"""Response message for ListAccessPolicySimulationResults.

  Fields:
    accessPolicySimulationResults: The results of the simulation.
    nextPageToken: Optional. A token that you can use to retrieve the next
      page of AccessPolicySimulationResult objects. If this field is omitted,
      there are no subsequent pages.
  """

  accessPolicySimulationResults = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaAccessPolicySimulationResult', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudPolicysimulatorV1alphaListAccessPolicySimulationsResponse(_messages.Message):
  r"""Response message for ListAccessPolicySimulationResults.

  Fields:
    accessPolicySimulations: The list of simulation.
    nextPageToken: Optional. A token that you can use to retrieve the next
      page of AccessPolicySimulation objects. If this field is omitted, there
      are no subsequent pages.
  """

  accessPolicySimulations = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaAccessPolicySimulation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudPolicysimulatorV1alphaListOrgPolicyViolationsPreviewsResponse(_messages.Message):
  r"""ListOrgPolicyViolationsPreviewsResponse is the response message for
  OrgPolicyViolationsPreviewService.ListOrgPolicyViolationsPreviews.

  Fields:
    nextPageToken: A token that you can use to retrieve the next page of
      results. If this field is omitted, there are no subsequent pages.
    orgPolicyViolationsPreviews: The list of OrgPolicyViolationsPreview
  """

  nextPageToken = _messages.StringField(1)
  orgPolicyViolationsPreviews = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview', 2, repeated=True)


class GoogleCloudPolicysimulatorV1alphaListOrgPolicyViolationsResponse(_messages.Message):
  r"""ListOrgPolicyViolationsResponse is the response message for
  OrgPolicyViolationsPreviewService.ListOrgPolicyViolations

  Fields:
    nextPageToken: A token that you can use to retrieve the next page of
      results. If this field is omitted, there are no subsequent pages.
    orgPolicyViolations: The list of OrgPolicyViolations
  """

  nextPageToken = _messages.StringField(1)
  orgPolicyViolations = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaOrgPolicyViolation', 2, repeated=True)


class GoogleCloudPolicysimulatorV1alphaListPabSimulationResultsResponse(_messages.Message):
  r"""Response message for ListPolicySimulationResults.

  Fields:
    nextPageToken: Optional. A token that you can use to retrieve the next
      page of PolicySimulationResult objects. If this field is omitted, there
      are no subsequent pages.
    pabSimulationResults: The results of the simulation.
  """

  nextPageToken = _messages.StringField(1)
  pabSimulationResults = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaPabSimulationResult', 2, repeated=True)


class GoogleCloudPolicysimulatorV1alphaListReplayResultsResponse(_messages.Message):
  r"""Response message for Simulator.ListReplayResults.

  Fields:
    nextPageToken: A token that you can use to retrieve the next page of
      ReplayResult objects. If this field is omitted, there are no subsequent
      pages.
    replayResults: The results of running a Replay.
  """

  nextPageToken = _messages.StringField(1)
  replayResults = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaReplayResult', 2, repeated=True)


class GoogleCloudPolicysimulatorV1alphaListReplaysResponse(_messages.Message):
  r"""Response message for Simulator.ListReplays.

  Fields:
    nextPageToken: A token that you can use to retrieve the next page of
      results. If this field is omitted, there are no subsequent pages.
    replays: The list of Replay objects.
  """

  nextPageToken = _messages.StringField(1)
  replays = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaReplay', 2, repeated=True)


class GoogleCloudPolicysimulatorV1alphaMutation(_messages.Message):
  r"""Mutation describing a single policy change.

  Enums:
    ActionValueValuesEnum: Required. Immutable. The type of the mutation.

  Fields:
    action: Required. Immutable. The type of the mutation.
    denyPolicyOverlay: The overlay containing the new content of the deny
      policy.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The type of the mutation.

    Values:
      ACTION_UNSPECIFIED: Default value. This value is unused.
      CREATE: Create a new policy.
      UPDATE: Update an existing policy.
      DELETE: Delete an existing policy.
    """
    ACTION_UNSPECIFIED = 0
    CREATE = 1
    UPDATE = 2
    DELETE = 3

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  denyPolicyOverlay = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaDenyPolicyOverlay', 2)


class GoogleCloudPolicysimulatorV1alphaObservationPeriod(_messages.Message):
  r"""Represents data observation period.

  Fields:
    endTime: The end of the observation period.
    startTime: The start of the observation period.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class GoogleCloudPolicysimulatorV1alphaOrgPolicyOverlay(_messages.Message):
  r"""The proposed changes to OrgPolicy.

  Fields:
    customConstraints: Optional. The OrgPolicy CustomConstraint changes to
      preview violations for. Any existing CustomConstraints with the same
      name will be overridden in the simulation. That is, violations will be
      determined as if all custom constraints in the overlay were
      instantiated. Only a single custom_constraint is supported in the
      overlay at a time. For evaluating multiple constraints, multiple
      `GenerateOrgPolicyViolationsPreview` requests are made, where each
      request evaluates a single constraint.
    policies: Optional. The OrgPolicy changes to preview violations for. Any
      existing OrgPolicies with the same name will be overridden in the
      simulation. That is, violations will be determined as if all policies in
      the overlay were created or updated.
  """

  customConstraints = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaOrgPolicyOverlayCustomConstraintOverlay', 1, repeated=True)
  policies = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaOrgPolicyOverlayPolicyOverlay', 2, repeated=True)


class GoogleCloudPolicysimulatorV1alphaOrgPolicyOverlayCustomConstraintOverlay(_messages.Message):
  r"""A change to an OrgPolicy custom constraint.

  Fields:
    customConstraint: Optional. The new or updated custom constraint.
    customConstraintParent: Optional. Resource the constraint is attached to.
      Example: "organization/987654"
  """

  customConstraint = _messages.MessageField('GoogleCloudOrgpolicyV2CustomConstraint', 1)
  customConstraintParent = _messages.StringField(2)


class GoogleCloudPolicysimulatorV1alphaOrgPolicyOverlayPolicyOverlay(_messages.Message):
  r"""A change to an OrgPolicy.

  Fields:
    policy: Optional. The new or updated OrgPolicy.
    policyParent: Optional. The parent of the policy we are attaching to.
      Example: "projects/123456"
  """

  policy = _messages.MessageField('GoogleCloudOrgpolicyV2Policy', 1)
  policyParent = _messages.StringField(2)


class GoogleCloudPolicysimulatorV1alphaOrgPolicyViolation(_messages.Message):
  r"""OrgPolicyViolation is a resource representing a single resource
  violating a single OrgPolicy constraint.

  Fields:
    customConstraint: The custom constraint being violated.
    error: Any error encountered during the evaluation.
    name: The name of the `OrgPolicyViolation`. Example: organizations/my-
      example-org/locations/global/orgPolicyViolationsPreviews/506a5f7f/orgPol
      icyViolations/38ce`
    resource: The resource violating the constraint.
  """

  customConstraint = _messages.MessageField('GoogleCloudOrgpolicyV2CustomConstraint', 1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  name = _messages.StringField(3)
  resource = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaResourceContext', 4)


class GoogleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview(_messages.Message):
  r"""OrgPolicyViolationsPreview is a resource providing a preview of the
  violations that will exist if an OrgPolicy change is made. The list of
  violations are modeled as child resources and retrieved via a
  ListOrgPolicyViolations API call. There are potentially more
  OrgPolicyViolations than could fit in an embedded field. Thus, the use of a
  child resource instead of a field.

  Enums:
    StateValueValuesEnum: Output only. The state of the
      `OrgPolicyViolationsPreview`.

  Fields:
    createTime: Output only. Time when this `OrgPolicyViolationsPreview` was
      created.
    customConstraints: Output only. The names of the constraints against which
      all `OrgPolicyViolations` were evaluated. If `OrgPolicyOverlay` only
      contains `PolicyOverlay` then it contains the name of the configured
      custom constraint, applicable to the specified policies. Otherwise it
      contains the name of the constraint specified in
      `CustomConstraintOverlay`. Format: `organizations/{organization_id}/cust
      omConstraints/{custom_constraint_id}` Example:
      `organizations/123/customConstraints/custom.createOnlyE2TypeVms`
    name: Output only. The resource name of the `OrgPolicyViolationsPreview`.
      It has the following format: `organizations/{organization}/locations/{lo
      cation}/orgPolicyViolationsPreviews/{orgPolicyViolationsPreview}`
      Example: `organizations/my-example-
      org/locations/global/orgPolicyViolationsPreviews/506a5f7f`
    overlay: Required. The proposed changes we are previewing violations for.
    resourceCounts: Output only. A summary of the state of all resources
      scanned for compliance with the changed OrgPolicy.
    state: Output only. The state of the `OrgPolicyViolationsPreview`.
    violationsCount: Output only. The number of OrgPolicyViolations in this
      `OrgPolicyViolationsPreview`. This count may differ from
      `resource_summary.noncompliant_count` because each OrgPolicyViolation is
      specific to a resource **and** constraint. If there are multiple
      constraints being evaluated (i.e. multiple policies in the overlay), a
      single resource may violate multiple constraints.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the `OrgPolicyViolationsPreview`.

    Values:
      PREVIEW_STATE_UNSPECIFIED: The state is unspecified.
      PREVIEW_PENDING: The OrgPolicyViolationsPreview has not been created
        yet.
      PREVIEW_RUNNING: The OrgPolicyViolationsPreview is currently being
        created.
      PREVIEW_SUCCEEDED: The OrgPolicyViolationsPreview creation finished
        successfully.
      PREVIEW_FAILED: The OrgPolicyViolationsPreview creation failed with an
        error.
    """
    PREVIEW_STATE_UNSPECIFIED = 0
    PREVIEW_PENDING = 1
    PREVIEW_RUNNING = 2
    PREVIEW_SUCCEEDED = 3
    PREVIEW_FAILED = 4

  createTime = _messages.StringField(1)
  customConstraints = _messages.StringField(2, repeated=True)
  name = _messages.StringField(3)
  overlay = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaOrgPolicyOverlay', 4)
  resourceCounts = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreviewResourceCounts', 5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  violationsCount = _messages.IntegerField(7, variant=_messages.Variant.INT32)


class GoogleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreviewResourceCounts(_messages.Message):
  r"""A summary of the state of all resources scanned for compliance with the
  changed OrgPolicy.

  Fields:
    compliant: Output only. Number of scanned resources with zero violations.
    errors: Output only. Number of resources that returned an error when
      scanned.
    noncompliant: Output only. Number of scanned resources with at least one
      violation.
    scanned: Output only. Number of resources checked for compliance. Must
      equal: unenforced + noncompliant + compliant + error
    unenforced: Output only. Number of resources where the constraint was not
      enforced, i.e. the Policy set `enforced: false` for that resource.
  """

  compliant = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  errors = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  noncompliant = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  scanned = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  unenforced = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class GoogleCloudPolicysimulatorV1alphaPabOverlay(_messages.Message):
  r"""Overlay for PAB Simulator.

  Enums:
    ActionValueValuesEnum: Required. Immutable. The action to take.

  Fields:
    action: Required. Immutable. The action to take.
    pabPolicyBindingOverlay: The PAB binding overlay to apply.
    pabPolicyOverlay: The PAB policy overlay to apply.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The action to take.

    Values:
      OVERLAY_ACTION_UNSPECIFIED: Default value. This value is unused.
      CREATE: Create the policy binding.
      UPDATE: Update the PAB policy or binding.
      DELETE: Only the policy name matters. Everything else in the policy
        ignored.
    """
    OVERLAY_ACTION_UNSPECIFIED = 0
    CREATE = 1
    UPDATE = 2
    DELETE = 3

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  pabPolicyBindingOverlay = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaPabPolicyBindingOverlay', 2)
  pabPolicyOverlay = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaPabPolicyOverlay', 3)


class GoogleCloudPolicysimulatorV1alphaPabPolicyBindingOverlay(_messages.Message):
  r"""PolicyOverlay for PAB PolicyBinding.

  Fields:
    parent: Optional. Parent of the policy binding. Required for CREATE
      action, not supported for other actions.
    policyBinding: Required. The PAB policy binding
    policyBindingId: Optional. The ID to use for the policy binding, which
      will become the final component of the policy binding's resource name.
      Required for CREATE action, not supported for other actions. This value
      must start with a lowercase letter followed by up to 62 lowercase
      letters, numbers, hyphens, or dots. Pattern, /a-z{2,62}/.
    updateMask: Optional. An empty update mask will imply that only the
      present fields are being updated. Set to `*` to completely overwrite.
      Only supported for UPDATE action.
  """

  parent = _messages.StringField(1)
  policyBinding = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaPolicyBinding', 2)
  policyBindingId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class GoogleCloudPolicysimulatorV1alphaPabPolicyOverlay(_messages.Message):
  r"""PolicyOverlay for PAB Policy.

  Fields:
    force: Optional. If true, the policy will be deleted even if the policy is
      bound to targets. Only supported for DELETE action.
    parent: Optional. Parent of the Principal Access Boundary policy. Required
      for CREATE action, not supported for other actions.
    policyId: Optional. The ID to use for the principal access boundary
      policy, which will become the final component of the principal access
      boundary policy's resource name. Required for CREATE action, not
      supported for other actions. This value must start with a lowercase
      letter followed by up to 62 lowercase letters, numbers, hyphens, or
      dots. Pattern, /a-z{2,62}/.
    principalAccessBoundaryPolicy: Required. The PAB policy.
    updateMask: Optional. An empty update mask will imply that only the
      present fields are being updated. Set to `*` to completely overwrite.
      Only supported for UPDATE action.
  """

  force = _messages.BooleanField(1)
  parent = _messages.StringField(2)
  policyId = _messages.StringField(3)
  principalAccessBoundaryPolicy = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaPrincipalAccessBoundaryPolicy', 4)
  updateMask = _messages.StringField(5)


class GoogleCloudPolicysimulatorV1alphaPabSimulation(_messages.Message):
  r"""PolicySimulation resource.

  Enums:
    StateValueValuesEnum: Output only. The state of the simulation. Output
      only.

  Fields:
    endTime: Output only. The end time of the simulation. Output only.
    name: Identifier. The resource name of the simulation. Output only. {organ
      izations}/{organization_id}/locations/{location}/pabSimulations/{pab_sim
      ulation_id}
    pabOverlay: Required. Immutable. The overlay to apply to the simulation.
    startTime: Output only. The start time of the simulation. Output only.
    state: Output only. The state of the simulation. Output only.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the simulation. Output only.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      PENDING: The `PABSimulation` has not started yet.
      RUNNING: The `PABSimulation` is currently running.
      SUCCEEDED: The `PABSimulation` has successfully completed.
      FAILED: The `PABSimulation` has finished with an error.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4

  endTime = _messages.StringField(1)
  name = _messages.StringField(2)
  pabOverlay = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaPabOverlay', 3, repeated=True)
  startTime = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class GoogleCloudPolicysimulatorV1alphaPabSimulationResult(_messages.Message):
  r"""PolicySimulation result for a access tuple.

  Enums:
    AccessDiffValueValuesEnum: Access difference for the tuple.

  Fields:
    accessDiff: Access difference for the tuple.
    accessTuple: The access tuple.
    daysAccessed: The number of distinct days this access was observed during
      the look back period. Default look back period is 90 days.
    lastSeenDate: The time when the access tuple was last accessed (date only,
      no time).
    name: Identifier. The resource name of the `PABSimulationResult`, in the
      following format: `{organizations}/{organization-
      id}/locations/global/pabSimulations/{pab-simulation-id}/results/{result-
      id}`, where `{organization-id}` is the ID of the organization that owns
      the PolicySimulation. Example: `organizations/123456/locations/global/pa
      bSimulations/506a5f7f-38ce-4d7d-8e03-479ce1833c36/results/1234`
    parent: Output only. The PABSimulation that the access diff was included
      in.
  """

  class AccessDiffValueValuesEnum(_messages.Enum):
    r"""Access difference for the tuple.

    Values:
      ACCESS_DIFF_TYPE_UNSPECIFIED: Default value. This value is unused.
      ACCESS_LOST: The principal will lose access to the resource.
      ACCESS_GAINED: The principal will gain access to the resource.
    """
    ACCESS_DIFF_TYPE_UNSPECIFIED = 0
    ACCESS_LOST = 1
    ACCESS_GAINED = 2

  accessDiff = _messages.EnumField('AccessDiffValueValuesEnum', 1)
  accessTuple = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaPabSimulationResultAccessTuple', 2)
  daysAccessed = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  lastSeenDate = _messages.MessageField('GoogleTypeDate', 4)
  name = _messages.StringField(5)
  parent = _messages.StringField(6)


class GoogleCloudPolicysimulatorV1alphaPabSimulationResultAccessTuple(_messages.Message):
  r"""The access tuple of this simulation result.

  Fields:
    permission: The permission used in the last access.
    principal: The principal that will lose or gain access.
    resource: The resource the principal will lose or gain access to.
  """

  permission = _messages.StringField(1)
  principal = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaPabSimulationResultAccessTuplePrincipal', 2)
  resource = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaPabSimulationResultAccessTupleResource', 3)


class GoogleCloudPolicysimulatorV1alphaPabSimulationResultAccessTuplePrincipal(_messages.Message):
  r"""The principal of the access tuple.

  Fields:
    subject: The subject of the principal. For 1st party users, this is the
      email address. For 3rd party users, this is the 3P subject.
    type: The type of the principal. Supported principal types are Workspace,
      Workforce Pool, Workload Pool and Service Account. Allowed string must
      be one of: * iam.googleapis.com/WorkspaceIdentity *
      iam.googleapis.com/WorkforcePoolIdentity *
      iam.googleapis.com/WorkloadPoolIdentity *
      iam.googleapis.com/ServiceAccount
  """

  subject = _messages.StringField(1)
  type = _messages.StringField(2)


class GoogleCloudPolicysimulatorV1alphaPabSimulationResultAccessTupleResource(_messages.Message):
  r"""The resource the principal will lose or gain access to.

  Fields:
    name: The resource name in the short format (e.g. `projects/123`)
    service: The service name of the resource. (e.g.
      `cloudresourcemanager.googleapis.com`)
    type: The resource type.
  """

  name = _messages.StringField(1)
  service = _messages.StringField(2)
  type = _messages.StringField(3)


class GoogleCloudPolicysimulatorV1alphaPolicyBinding(_messages.Message):
  r"""IAM policy binding resource.

  Enums:
    PolicyKindValueValuesEnum: Immutable. The kind of the policy to attach in
      this binding. This field must be one of the following: - Left empty
      (will be automatically set to the policy kind) - The input policy kind

  Messages:
    AnnotationsValue: Optional. User-defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations

  Fields:
    annotations: Optional. User-defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations
    condition: Optional. The condition to apply to the policy binding. When
      set, the `expression` field in the `Expr` must include from 1 to 10
      subexpressions, joined by the "||"(Logical OR), "&&"(Logical AND) or
      "!"(Logical NOT) operators and cannot contain more than 250 characters.
      The condition is currently only supported when bound to policies of kind
      principal access boundary. When the bound policy is a principal access
      boundary policy, the only supported attributes in any subexpression are
      `principal.type` and `principal.subject`. An example expression is:
      "principal.type == 'iam.googleapis.com/ServiceAccount'" or
      "principal.subject == '<EMAIL>'". Allowed operations for
      `principal.subject`: - `principal.subject == ` - `principal.subject != `
      - `principal.subject in []` - `principal.subject.startsWith()` -
      `principal.subject.endsWith()` Allowed operations for `principal.type`:
      - `principal.type == ` - `principal.type != ` - `principal.type in []`
      Supported principal types are Workspace, Workforce Pool, Workload Pool
      and Service Account. Allowed string must be one of: -
      iam.googleapis.com/WorkspaceIdentity -
      iam.googleapis.com/WorkforcePoolIdentity -
      iam.googleapis.com/WorkloadPoolIdentity -
      iam.googleapis.com/ServiceAccount
    createTime: Output only. The time when the policy binding was created.
    displayName: Optional. The description of the policy binding. Must be less
      than or equal to 63 characters.
    etag: Optional. The etag for the policy binding. If this is provided on
      update, it must match the server's etag.
    name: Identifier. The name of the policy binding, in the format
      `{binding_parent/locations/{location}/policyBindings/{policy_binding_id}
      `. The binding parent is the closest Resource Manager resource (project,
      folder, or organization) to the binding target. Format: * `projects/{pro
      ject_id}/locations/{location}/policyBindings/{policy_binding_id}` * `pro
      jects/{project_number}/locations/{location}/policyBindings/{policy_bindi
      ng_id}` * `folders/{folder_id}/locations/{location}/policyBindings/{poli
      cy_binding_id}` * `organizations/{organization_id}/locations/{location}/
      policyBindings/{policy_binding_id}`
    policy: Required. Immutable. The resource name of the policy to be bound.
      The binding parent and policy must belong to the same organization.
    policyKind: Immutable. The kind of the policy to attach in this binding.
      This field must be one of the following: - Left empty (will be
      automatically set to the policy kind) - The input policy kind
    policyUid: Output only. The globally unique ID of the policy to be bound.
    target: Required. Immutable. Target is the full resource name of the
      resource to which the policy will be bound. Immutable once set.
    uid: Output only. The globally unique ID of the policy binding. Assigned
      when the policy binding is created.
    updateTime: Output only. The time when the policy binding was most
      recently updated.
  """

  class PolicyKindValueValuesEnum(_messages.Enum):
    r"""Immutable. The kind of the policy to attach in this binding. This
    field must be one of the following: - Left empty (will be automatically
    set to the policy kind) - The input policy kind

    Values:
      POLICY_KIND_UNSPECIFIED: Unspecified policy kind; Not a valid state
      PRINCIPAL_ACCESS_BOUNDARY: Principal access boundary policy kind
      ACCESS: Access policy kind. Keep behind visibility label until Access
        Policy launch.
    """
    POLICY_KIND_UNSPECIFIED = 0
    PRINCIPAL_ACCESS_BOUNDARY = 1
    ACCESS = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User-defined annotations. See
    https://google.aip.dev/148#annotations for more details such as format and
    size limitations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  condition = _messages.MessageField('GoogleTypeExpr', 2)
  createTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  policy = _messages.StringField(7)
  policyKind = _messages.EnumField('PolicyKindValueValuesEnum', 8)
  policyUid = _messages.StringField(9)
  target = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaPolicyBindingTarget', 10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class GoogleCloudPolicysimulatorV1alphaPolicyBindingTarget(_messages.Message):
  r"""Target is the full resource name of the resource to which the policy
  will be bound. Immutable once set.

  Fields:
    principalSet: Immutable. Full Resource Name used for principal access
      boundary policy bindings. The principal set must be directly parented by
      the policy binding's parent or same as the parent if the target is a
      project/folder/organization. Examples: * For binding's parented by an
      organization: * Organization:
      `//cloudresourcemanager.googleapis.com/organizations/ORGANIZATION_ID` *
      Workforce Identity:
      `//iam.googleapis.com/locations/global/workforcePools/WORKFORCE_POOL_ID`
      * Workspace Identity:
      `//iam.googleapis.com/locations/global/workspace/WORKSPACE_ID` * For
      binding's parented by a folder: * Folder:
      `//cloudresourcemanager.googleapis.com/folders/FOLDER_ID` * For
      binding's parented by a project: * Project: *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_NUMBER` *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_ID` * Workload
      Identity Pool: `//iam.googleapis.com/projects/PROJECT_NUMBER/locations/L
      OCATION/workloadIdentityPools/WORKLOAD_POOL_ID`
    resource: Immutable. Full Resource Name used for access policy bindings
      Examples: * Organization:
      `//cloudresourcemanager.googleapis.com/organizations/ORGANIZATION_ID` *
      Folder: `//cloudresourcemanager.googleapis.com/folders/FOLDER_ID` *
      Project: *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_NUMBER` *
      `//cloudresourcemanager.googleapis.com/projects/PROJECT_ID`
  """

  principalSet = _messages.StringField(1)
  resource = _messages.StringField(2)


class GoogleCloudPolicysimulatorV1alphaPrincipalAccessBoundaryPolicy(_messages.Message):
  r"""An IAM principal access boundary policy resource.

  Messages:
    AnnotationsValue: Optional. User defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations

  Fields:
    annotations: Optional. User defined annotations. See
      https://google.aip.dev/148#annotations for more details such as format
      and size limitations
    createTime: Output only. The time when the principal access boundary
      policy was created.
    details: Optional. The details for the principal access boundary policy.
    displayName: Optional. The description of the principal access boundary
      policy. Must be less than or equal to 63 characters.
    etag: Optional. The etag for the principal access boundary. If this is
      provided on update, it must match the server's etag.
    name: Identifier. The resource name of the principal access boundary
      policy. The following format is supported: `organizations/{organization_
      id}/locations/{location}/principalAccessBoundaryPolicies/{policy_id}`
    uid: Output only. The globally unique ID of the principal access boundary
      policy.
    updateTime: Output only. The time when the principal access boundary
      policy was most recently updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User defined annotations. See
    https://google.aip.dev/148#annotations for more details such as format and
    size limitations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  details = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaPrincipalAccessBoundaryPolicyDetails', 3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  uid = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class GoogleCloudPolicysimulatorV1alphaPrincipalAccessBoundaryPolicyDetails(_messages.Message):
  r"""Principal access boundary policy details

  Fields:
    enforcementVersion: Optional. The version number (for example, `1` or
      `latest`) that indicates which permissions are able to be blocked by the
      policy. If empty, the PAB policy version will be set to the most recent
      version number at the time of the policy's creation.
    rules: Required. A list of principal access boundary policy rules. The
      number of rules in a policy is limited to 500.
  """

  enforcementVersion = _messages.StringField(1)
  rules = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaPrincipalAccessBoundaryPolicyRule', 2, repeated=True)


class GoogleCloudPolicysimulatorV1alphaPrincipalAccessBoundaryPolicyRule(_messages.Message):
  r"""Principal access boundary policy rule that defines the resource
  boundary.

  Enums:
    EffectValueValuesEnum: Required. The access relationship of principals to
      the resources in this rule.

  Fields:
    description: Optional. The description of the principal access boundary
      policy rule. Must be less than or equal to 256 characters.
    effect: Required. The access relationship of principals to the resources
      in this rule.
    resources: Required. A list of Resource Manager resources. If a resource
      is listed in the rule, then the rule applies for that resource and its
      descendants. The number of resources in a policy is limited to 500
      across all rules in the policy. The following resource types are
      supported: * Organizations, such as
      `//cloudresourcemanager.googleapis.com/organizations/123`. * Folders,
      such as `//cloudresourcemanager.googleapis.com/folders/123`. * Projects,
      such as `//cloudresourcemanager.googleapis.com/projects/123` or
      `//cloudresourcemanager.googleapis.com/projects/my-project-id`.
  """

  class EffectValueValuesEnum(_messages.Enum):
    r"""Required. The access relationship of principals to the resources in
    this rule.

    Values:
      EFFECT_UNSPECIFIED: Effect unspecified.
      ALLOW: Allows access to the resources in this rule.
    """
    EFFECT_UNSPECIFIED = 0
    ALLOW = 1

  description = _messages.StringField(1)
  effect = _messages.EnumField('EffectValueValuesEnum', 2)
  resources = _messages.StringField(3, repeated=True)


class GoogleCloudPolicysimulatorV1alphaReplay(_messages.Message):
  r"""A resource describing a `Replay`, or simulation.

  Enums:
    StateValueValuesEnum: Output only. The current state of the `Replay`.

  Fields:
    config: Required. The configuration used for the `Replay`.
    name: Output only. The resource name of the `Replay`, which has the
      following format: `{projects|folders|organizations}/{resource-
      id}/locations/global/replays/{replay-id}`, where `{resource-id}` is the
      ID of the project, folder, or organization that owns the Replay.
      Example: `projects/my-example-
      project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
    resultsSummary: Output only. Summary statistics about the replayed log
      entries.
    state: Output only. The current state of the `Replay`.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the `Replay`.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      PENDING: The `Replay` has not started yet.
      RUNNING: The `Replay` is currently running.
      SUCCEEDED: The `Replay` has successfully completed.
      FAILED: The `Replay` has finished with an error.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4

  config = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaReplayConfig', 1)
  name = _messages.StringField(2)
  resultsSummary = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaReplayResultsSummary', 3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class GoogleCloudPolicysimulatorV1alphaReplayConfig(_messages.Message):
  r"""The configuration used for a Replay.

  Enums:
    LogSourceValueValuesEnum: The logs to use as input for the Replay.

  Messages:
    PolicyOverlayValue: A mapping of the resources that you want to simulate
      policies for and the policies that you want to simulate. Keys are the
      full resource names for the resources. For example,
      `//cloudresourcemanager.googleapis.com/projects/my-project`. For
      examples of full resource names for Google Cloud services, see
      https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
      Values are Policy objects representing the policies that you want to
      simulate. Replays automatically take into account any IAM policies
      inherited through the resource hierarchy, and any policies set on
      descendant resources. You do not need to include these policies in the
      policy overlay.

  Fields:
    logSource: The logs to use as input for the Replay.
    policyOverlay: A mapping of the resources that you want to simulate
      policies for and the policies that you want to simulate. Keys are the
      full resource names for the resources. For example,
      `//cloudresourcemanager.googleapis.com/projects/my-project`. For
      examples of full resource names for Google Cloud services, see
      https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
      Values are Policy objects representing the policies that you want to
      simulate. Replays automatically take into account any IAM policies
      inherited through the resource hierarchy, and any policies set on
      descendant resources. You do not need to include these policies in the
      policy overlay.
  """

  class LogSourceValueValuesEnum(_messages.Enum):
    r"""The logs to use as input for the Replay.

    Values:
      LOG_SOURCE_UNSPECIFIED: An unspecified log source. If the log source is
        unspecified, the Replay defaults to using `RECENT_ACCESSES`.
      RECENT_ACCESSES: All access logs from the last 90 days. These logs may
        not include logs from the most recent 7 days.
    """
    LOG_SOURCE_UNSPECIFIED = 0
    RECENT_ACCESSES = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PolicyOverlayValue(_messages.Message):
    r"""A mapping of the resources that you want to simulate policies for and
    the policies that you want to simulate. Keys are the full resource names
    for the resources. For example,
    `//cloudresourcemanager.googleapis.com/projects/my-project`. For examples
    of full resource names for Google Cloud services, see
    https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
    Values are Policy objects representing the policies that you want to
    simulate. Replays automatically take into account any IAM policies
    inherited through the resource hierarchy, and any policies set on
    descendant resources. You do not need to include these policies in the
    policy overlay.

    Messages:
      AdditionalProperty: An additional property for a PolicyOverlayValue
        object.

    Fields:
      additionalProperties: Additional properties of type PolicyOverlayValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PolicyOverlayValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleIamV1Policy attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleIamV1Policy', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  logSource = _messages.EnumField('LogSourceValueValuesEnum', 1)
  policyOverlay = _messages.MessageField('PolicyOverlayValue', 2)


class GoogleCloudPolicysimulatorV1alphaReplayDiff(_messages.Message):
  r"""The difference between the results of evaluating an access tuple under
  the current (baseline) policies and under the proposed (simulated) policies.
  This difference explains how a principal's access could change if the
  proposed policies were applied.

  Fields:
    accessDiff: A summary and comparison of the principal's access under the
      current (baseline) policies and the proposed (simulated) policies for a
      single access tuple. The evaluation of the principal's access is
      reported in the AccessState field.
  """

  accessDiff = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaAccessStateDiff', 1)


class GoogleCloudPolicysimulatorV1alphaReplayResult(_messages.Message):
  r"""The result of replaying a single access tuple against a simulated state.

  Fields:
    accessTuple: The access tuple that was replayed. This field includes
      information about the principal, resource, and permission that were
      involved in the access attempt.
    diff: The difference between the principal's access under the current
      (baseline) policies and the principal's access under the proposed
      (simulated) policies. This field is only included for access tuples that
      were successfully replayed and had different results under the current
      policies and the proposed policies.
    error: The error that caused the access tuple replay to fail. This field
      is only included for access tuples that were not replayed successfully.
    lastSeenDate: The latest date this access tuple was seen in the logs.
    name: The resource name of the `ReplayResult`, in the following format:
      `{projects|folders|organizations}/{resource-
      id}/locations/global/replays/{replay-id}/results/{replay-result-id}`,
      where `{resource-id}` is the ID of the project, folder, or organization
      that owns the Replay. Example: `projects/my-example-project/locations/gl
      obal/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36/results/1234`
    parent: The Replay that the access tuple was included in.
  """

  accessTuple = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaAccessTuple', 1)
  diff = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaReplayDiff', 2)
  error = _messages.MessageField('GoogleRpcStatus', 3)
  lastSeenDate = _messages.MessageField('GoogleTypeDate', 4)
  name = _messages.StringField(5)
  parent = _messages.StringField(6)


class GoogleCloudPolicysimulatorV1alphaReplayResultsSummary(_messages.Message):
  r"""Summary statistics about the replayed log entries.

  Fields:
    differenceCount: The number of replayed log entries with a difference
      between baseline and simulated policies.
    errorCount: The number of log entries that could not be replayed.
    logCount: The total number of log entries replayed.
    newestDate: The date of the newest log entry replayed.
    oldestDate: The date of the oldest log entry replayed.
    unchangedCount: The number of replayed log entries with no difference
      between baseline and simulated policies.
  """

  differenceCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  errorCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  logCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  newestDate = _messages.MessageField('GoogleTypeDate', 4)
  oldestDate = _messages.MessageField('GoogleTypeDate', 5)
  unchangedCount = _messages.IntegerField(6, variant=_messages.Variant.INT32)


class GoogleCloudPolicysimulatorV1alphaResourceContext(_messages.Message):
  r"""ResourceContext provides the context we know about a resource. It is
  similar in concept to google.cloud.asset.v1.Resource, but focuses on the
  information specifically used by Simulator.

  Fields:
    ancestors: The ancestry path of the resource in Google Cloud [resource
      hierarchy](https://cloud.google.com/resource-manager/docs/cloud-
      platform-resource-hierarchy), represented as a list of relative resource
      names. An ancestry path starts with the closest ancestor in the
      hierarchy and ends at root. If the resource is a project, folder, or
      organization, the ancestry path starts from the resource itself.
      Example: `["projects/123456789", "folders/5432", "organizations/1234"]`
    assetType: The asset type of the resource as defined by CAIS. Example:
      `compute.googleapis.com/Firewall` See [Supported asset
      types](https://cloud.google.com/asset-inventory/docs/supported-asset-
      types) for more information.
    resource: The full name of the resource. Example: `//compute.googleapis.co
      m/projects/my_project_123/zones/zone1/instances/instance1` See [Resource
      names](https://cloud.google.com/apis/design/resource_names#full_resource
      _name) for more information.
  """

  ancestors = _messages.StringField(1, repeated=True)
  assetType = _messages.StringField(2)
  resource = _messages.StringField(3)


class GoogleCloudPolicysimulatorV1betaCreateOrgPolicyViolationsPreviewOperationMetadata(_messages.Message):
  r"""CreateOrgPolicyViolationsPreviewOperationMetadata is metadata about an
  OrgPolicyViolationsPreview generations operation.

  Enums:
    StateValueValuesEnum: Output only. The current state of the operation.

  Fields:
    requestTime: Time when the request was received.
    resourcesFound: Total number of resources that need scanning. Should equal
      resource_scanned + resources_pending
    resourcesPending: Number of resources still to scan.
    resourcesScanned: Number of resources already scanned.
    startTime: Time when the request started processing, i.e., when the state
      was set to RUNNING.
    state: Output only. The current state of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the operation.

    Values:
      PREVIEW_STATE_UNSPECIFIED: The state is unspecified.
      PREVIEW_PENDING: The OrgPolicyViolationsPreview has not been created
        yet.
      PREVIEW_RUNNING: The OrgPolicyViolationsPreview is currently being
        created.
      PREVIEW_SUCCEEDED: The OrgPolicyViolationsPreview creation finished
        successfully.
      PREVIEW_FAILED: The OrgPolicyViolationsPreview creation failed with an
        error.
    """
    PREVIEW_STATE_UNSPECIFIED = 0
    PREVIEW_PENDING = 1
    PREVIEW_RUNNING = 2
    PREVIEW_SUCCEEDED = 3
    PREVIEW_FAILED = 4

  requestTime = _messages.StringField(1)
  resourcesFound = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  resourcesPending = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  resourcesScanned = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  startTime = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class GoogleCloudPolicysimulatorV1betaGenerateOrgPolicyViolationsPreviewOperationMetadata(_messages.Message):
  r"""GenerateOrgPolicyViolationsPreviewOperationMetadata is metadata about an
  OrgPolicyViolationsPreview generations operation.

  Enums:
    StateValueValuesEnum: The current state of the operation.

  Fields:
    requestTime: Time when the request was received.
    resourcesFound: Total number of resources that need scanning. Should equal
      resource_scanned + resources_pending
    resourcesPending: Number of resources still to scan.
    resourcesScanned: Number of resources already scanned.
    startTime: Time when the request started processing, i.e. when the state
      was set to RUNNING.
    state: The current state of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the operation.

    Values:
      PREVIEW_STATE_UNSPECIFIED: The state is unspecified.
      PREVIEW_PENDING: The OrgPolicyViolationsPreview has not been created
        yet.
      PREVIEW_RUNNING: The OrgPolicyViolationsPreview is currently being
        created.
      PREVIEW_SUCCEEDED: The OrgPolicyViolationsPreview creation finished
        successfully.
      PREVIEW_FAILED: The OrgPolicyViolationsPreview creation failed with an
        error.
    """
    PREVIEW_STATE_UNSPECIFIED = 0
    PREVIEW_PENDING = 1
    PREVIEW_RUNNING = 2
    PREVIEW_SUCCEEDED = 3
    PREVIEW_FAILED = 4

  requestTime = _messages.StringField(1)
  resourcesFound = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  resourcesPending = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  resourcesScanned = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  startTime = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class GoogleCloudPolicysimulatorV1betaOrgPolicyOverlay(_messages.Message):
  r"""The proposed changes to OrgPolicy.

  Fields:
    customConstraints: Optional. The OrgPolicy CustomConstraint changes to
      preview violations for. Any existing CustomConstraints with the same
      name will be overridden in the simulation. That is, violations will be
      determined as if all custom constraints in the overlay were
      instantiated. Only a single custom_constraint is supported in the
      overlay at a time. For evaluating multiple constraints, multiple
      `GenerateOrgPolicyViolationsPreview` requests are made, where each
      request evaluates a single constraint.
    policies: Optional. The OrgPolicy changes to preview violations for. Any
      existing OrgPolicies with the same name will be overridden in the
      simulation. That is, violations will be determined as if all policies in
      the overlay were created or updated.
  """

  customConstraints = _messages.MessageField('GoogleCloudPolicysimulatorV1betaOrgPolicyOverlayCustomConstraintOverlay', 1, repeated=True)
  policies = _messages.MessageField('GoogleCloudPolicysimulatorV1betaOrgPolicyOverlayPolicyOverlay', 2, repeated=True)


class GoogleCloudPolicysimulatorV1betaOrgPolicyOverlayCustomConstraintOverlay(_messages.Message):
  r"""A change to an OrgPolicy custom constraint.

  Fields:
    customConstraint: Optional. The new or updated custom constraint.
    customConstraintParent: Optional. Resource the constraint is attached to.
      Example: "organization/987654"
  """

  customConstraint = _messages.MessageField('GoogleCloudOrgpolicyV2CustomConstraint', 1)
  customConstraintParent = _messages.StringField(2)


class GoogleCloudPolicysimulatorV1betaOrgPolicyOverlayPolicyOverlay(_messages.Message):
  r"""A change to an OrgPolicy.

  Fields:
    policy: Optional. The new or updated OrgPolicy.
    policyParent: Optional. The parent of the policy we are attaching to.
      Example: "projects/123456"
  """

  policy = _messages.MessageField('GoogleCloudOrgpolicyV2Policy', 1)
  policyParent = _messages.StringField(2)


class GoogleCloudPolicysimulatorV1betaOrgPolicyViolationsPreview(_messages.Message):
  r"""OrgPolicyViolationsPreview is a resource providing a preview of the
  violations that will exist if an OrgPolicy change is made. The list of
  violations are modeled as child resources and retrieved via a
  ListOrgPolicyViolations API call. There are potentially more
  OrgPolicyViolations than could fit in an embedded field. Thus, the use of a
  child resource instead of a field.

  Enums:
    StateValueValuesEnum: Output only. The state of the
      `OrgPolicyViolationsPreview`.

  Fields:
    createTime: Output only. Time when this `OrgPolicyViolationsPreview` was
      created.
    customConstraints: Output only. The names of the constraints against which
      all `OrgPolicyViolations` were evaluated. If `OrgPolicyOverlay` only
      contains `PolicyOverlay` then it contains the name of the configured
      custom constraint, applicable to the specified policies. Otherwise it
      contains the name of the constraint specified in
      `CustomConstraintOverlay`. Format: `organizations/{organization_id}/cust
      omConstraints/{custom_constraint_id}` Example:
      `organizations/123/customConstraints/custom.createOnlyE2TypeVms`
    name: Output only. The resource name of the `OrgPolicyViolationsPreview`.
      It has the following format: `organizations/{organization}/locations/{lo
      cation}/orgPolicyViolationsPreviews/{orgPolicyViolationsPreview}`
      Example: `organizations/my-example-
      org/locations/global/orgPolicyViolationsPreviews/506a5f7f`
    overlay: Required. The proposed changes we are previewing violations for.
    resourceCounts: Output only. A summary of the state of all resources
      scanned for compliance with the changed OrgPolicy.
    state: Output only. The state of the `OrgPolicyViolationsPreview`.
    violationsCount: Output only. The number of OrgPolicyViolations in this
      `OrgPolicyViolationsPreview`. This count may differ from
      `resource_summary.noncompliant_count` because each OrgPolicyViolation is
      specific to a resource **and** constraint. If there are multiple
      constraints being evaluated (i.e. multiple policies in the overlay), a
      single resource may violate multiple constraints.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the `OrgPolicyViolationsPreview`.

    Values:
      PREVIEW_STATE_UNSPECIFIED: The state is unspecified.
      PREVIEW_PENDING: The OrgPolicyViolationsPreview has not been created
        yet.
      PREVIEW_RUNNING: The OrgPolicyViolationsPreview is currently being
        created.
      PREVIEW_SUCCEEDED: The OrgPolicyViolationsPreview creation finished
        successfully.
      PREVIEW_FAILED: The OrgPolicyViolationsPreview creation failed with an
        error.
    """
    PREVIEW_STATE_UNSPECIFIED = 0
    PREVIEW_PENDING = 1
    PREVIEW_RUNNING = 2
    PREVIEW_SUCCEEDED = 3
    PREVIEW_FAILED = 4

  createTime = _messages.StringField(1)
  customConstraints = _messages.StringField(2, repeated=True)
  name = _messages.StringField(3)
  overlay = _messages.MessageField('GoogleCloudPolicysimulatorV1betaOrgPolicyOverlay', 4)
  resourceCounts = _messages.MessageField('GoogleCloudPolicysimulatorV1betaOrgPolicyViolationsPreviewResourceCounts', 5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  violationsCount = _messages.IntegerField(7, variant=_messages.Variant.INT32)


class GoogleCloudPolicysimulatorV1betaOrgPolicyViolationsPreviewResourceCounts(_messages.Message):
  r"""A summary of the state of all resources scanned for compliance with the
  changed OrgPolicy.

  Fields:
    compliant: Output only. Number of scanned resources with zero violations.
    errors: Output only. Number of resources that returned an error when
      scanned.
    noncompliant: Output only. Number of scanned resources with at least one
      violation.
    scanned: Output only. Number of resources checked for compliance. Must
      equal: unenforced + noncompliant + compliant + error
    unenforced: Output only. Number of resources where the constraint was not
      enforced, i.e. the Policy set `enforced: false` for that resource.
  """

  compliant = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  errors = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  noncompliant = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  scanned = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  unenforced = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class GoogleIamV1AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('GoogleIamV1AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class GoogleIamV1AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    ignoreChildExemptions: A boolean attribute.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  ignoreChildExemptions = _messages.BooleanField(2)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 3)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    bindingId: A string attribute.
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=********************1`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=********************1`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=********************1`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  bindingId = _messages.StringField(1)
  condition = _messages.MessageField('GoogleTypeExpr', 2)
  members = _messages.StringField(3, repeated=True)
  role = _messages.StringField(4)


class GoogleIamV1Condition(_messages.Message):
  r"""A condition to be met.

  Enums:
    IamValueValuesEnum: Trusted attributes supplied by the IAM system.
    OpValueValuesEnum: An operator to apply the subject with.
    SysValueValuesEnum: Trusted attributes supplied by any service that owns
      resources and uses the IAM system for access control.

  Fields:
    iam: Trusted attributes supplied by the IAM system.
    op: An operator to apply the subject with.
    svc: Trusted attributes discharged by the service.
    sys: Trusted attributes supplied by any service that owns resources and
      uses the IAM system for access control.
    values: The objects of the condition.
  """

  class IamValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by the IAM system.

    Values:
      NO_ATTR: Default non-attribute.
      AUTHORITY: Either principal or (if present) authority selector.
      ATTRIBUTION: The principal (even if an authority selector is present),
        which must only be used for attribution, not authorization.
      SECURITY_REALM: Any of the security realms in the IAMContext
        (go/security-realms). When used with IN, the condition indicates "any
        of the request's realms match one of the given values; with NOT_IN,
        "none of the realms match any of the given values". Note that a value
        can be: - 'self:campus' (i.e., clients that are in the same campus) -
        'self:metro' (i.e., clients that are in the same metro) - 'self:cloud-
        region' (i.e., allow connections from clients that are in the same
        cloud region) - 'self:prod-region' (i.e., allow connections from
        clients that are in the same prod region) - 'guardians' (i.e., allow
        connections from its guardian realms. See go/security-realms-
        glossary#guardian for more information.) - 'cryto_core_guardians'
        (i.e., allow connections from its crypto core guardian realms. See
        go/security-realms-glossary#guardian for more information.) Crypto
        Core coverage is a super-set of Default coverage, containing
        information about coverage between higher tier data centers (e.g.,
        YAWNs). Most services should use Default coverage and only use Crypto
        Core coverage if the service is involved in greenfield turnup of new
        higher tier data centers (e.g., credential infrastructure, machine/job
        management systems, etc.). - 'self' [DEPRECATED] (i.e., allow
        connections from clients that are in the same security realm, which is
        currently but not guaranteed to be campus-sized) - a realm (e.g.,
        'campus-abc') - a realm group (e.g., 'realms-for-borg-cell-xx', see:
        go/realm-groups) A match is determined by a realm group membership
        check performed by a RealmAclRep object (go/realm-acl-howto). It is
        not permitted to grant access based on the *absence* of a realm, so
        realm conditions can only be used in a "positive" context (e.g.,
        ALLOW/IN or DENY/NOT_IN).
      APPROVER: An approver (distinct from the requester) that has authorized
        this request. When used with IN, the condition indicates that one of
        the approvers associated with the request matches the specified
        principal, or is a member of the specified group. Approvers can only
        grant additional access, and are thus only used in a strictly positive
        context (e.g. ALLOW/IN or DENY/NOT_IN).
      JUSTIFICATION_TYPE: What types of justifications have been supplied with
        this request. String values should match enum names from
        security.credentials.JustificationType, e.g. "MANUAL_STRING". It is
        not permitted to grant access based on the *absence* of a
        justification, so justification conditions can only be used in a
        "positive" context (e.g., ALLOW/IN or DENY/NOT_IN). Multiple
        justifications, e.g., a Buganizer ID and a manually-entered reason,
        are normal and supported.
      CREDENTIALS_TYPE: What type of credentials have been supplied with this
        request. String values should match enum names from
        security_loas_l2.CredentialsType - currently, only
        CREDS_TYPE_EMERGENCY is supported. It is not permitted to grant access
        based on the *absence* of a credentials type, so the conditions can
        only be used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
      CREDS_ASSERTION: Properties of the credentials supplied with this
        request. See http://go/rpcsp-credential-assertions?polyglot=rpcsp-v1-0
        The conditions can only be used in a "positive" context (e.g.,
        ALLOW/IN or DENY/NOT_IN).
    """
    NO_ATTR = 0
    AUTHORITY = 1
    ATTRIBUTION = 2
    SECURITY_REALM = 3
    APPROVER = 4
    JUSTIFICATION_TYPE = 5
    CREDENTIALS_TYPE = 6
    CREDS_ASSERTION = 7

  class OpValueValuesEnum(_messages.Enum):
    r"""An operator to apply the subject with.

    Values:
      NO_OP: Default no-op.
      EQUALS: DEPRECATED. Use IN instead.
      NOT_EQUALS: DEPRECATED. Use NOT_IN instead.
      IN: The condition is true if the subject (or any element of it if it is
        a set) matches any of the supplied values.
      NOT_IN: The condition is true if the subject (or every element of it if
        it is a set) matches none of the supplied values.
      DISCHARGED: Subject is discharged
    """
    NO_OP = 0
    EQUALS = 1
    NOT_EQUALS = 2
    IN = 3
    NOT_IN = 4
    DISCHARGED = 5

  class SysValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by any service that owns resources and
    uses the IAM system for access control.

    Values:
      NO_ATTR: Default non-attribute type
      REGION: Region of the resource
      SERVICE: Service name
      NAME: Resource name
      IP: IP address of the caller
    """
    NO_ATTR = 0
    REGION = 1
    SERVICE = 2
    NAME = 3
    IP = 4

  iam = _messages.EnumField('IamValueValuesEnum', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  svc = _messages.StringField(3)
  sys = _messages.EnumField('SysValueValuesEnum', 4)
  values = _messages.StringField(5, repeated=True)


class GoogleIamV1LogConfig(_messages.Message):
  r"""Specifies what kind of log the caller must write

  Fields:
    cloudAudit: Cloud audit options.
    counter: Counter options.
    dataAccess: Data access options.
  """

  cloudAudit = _messages.MessageField('GoogleIamV1LogConfigCloudAuditOptions', 1)
  counter = _messages.MessageField('GoogleIamV1LogConfigCounterOptions', 2)
  dataAccess = _messages.MessageField('GoogleIamV1LogConfigDataAccessOptions', 3)


class GoogleIamV1LogConfigCloudAuditOptions(_messages.Message):
  r"""Write a Cloud Audit log

  Enums:
    LogNameValueValuesEnum: The log_name to populate in the Cloud Audit
      Record.
    PermissionTypeValueValuesEnum: The type associated with the permission.

  Fields:
    authorizationLoggingOptions: Information used by the Cloud Audit Logging
      pipeline. Will be deprecated once the migration to PermissionType is
      complete (b/201806118).
    logName: The log_name to populate in the Cloud Audit Record.
    permissionType: The type associated with the permission.
  """

  class LogNameValueValuesEnum(_messages.Enum):
    r"""The log_name to populate in the Cloud Audit Record.

    Values:
      UNSPECIFIED_LOG_NAME: Default. Should not be used.
      ADMIN_ACTIVITY: Corresponds to "cloudaudit.googleapis.com/activity"
      DATA_ACCESS: Corresponds to "cloudaudit.googleapis.com/data_access"
    """
    UNSPECIFIED_LOG_NAME = 0
    ADMIN_ACTIVITY = 1
    DATA_ACCESS = 2

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type associated with the permission.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: Permissions that gate reading resource configuration or
        metadata.
      ADMIN_WRITE: Permissions that gate modification of resource
        configuration or metadata.
      DATA_READ: Permissions that gate reading user-provided data.
      DATA_WRITE: Permissions that gate writing user-provided data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  authorizationLoggingOptions = _messages.MessageField('GoogleCloudAuditAuthorizationLoggingOptions', 1)
  logName = _messages.EnumField('LogNameValueValuesEnum', 2)
  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 3)


class GoogleIamV1LogConfigCounterOptions(_messages.Message):
  r"""Increment a streamz counter with the specified metric and field names.
  Metric names should start with a '/', generally be lowercase-only, and end
  in "_count". Field names should not contain an initial slash. The actual
  exported metric names will have "/iam/policy" prepended. Field names
  correspond to IAM request parameters and field values are their respective
  values. Supported field names: - "authority", which is "[token]" if
  IAMContext.token is present, otherwise the value of
  IAMContext.authority_selector if present, and otherwise a representation of
  IAMContext.principal; or - "iam_principal", a representation of
  IAMContext.principal even if a token or authority selector is present; or -
  "" (empty string), resulting in a counter with no fields. Examples: counter
  { metric: "/debug_access_count" field: "iam_principal" } ==> increment
  counter /iam/policy/debug_access_count {iam_principal=[value of
  IAMContext.principal]}

  Fields:
    customFields: Custom fields.
    field: The field value to attribute.
    metric: The metric to update.
  """

  customFields = _messages.MessageField('GoogleIamV1LogConfigCounterOptionsCustomField', 1, repeated=True)
  field = _messages.StringField(2)
  metric = _messages.StringField(3)


class GoogleIamV1LogConfigCounterOptionsCustomField(_messages.Message):
  r"""Custom fields. These can be used to create a counter with arbitrary
  field/value pairs. See: go/rpcsp-custom-fields.

  Fields:
    name: Name is the field name.
    value: Value is the field value. It is important that in contrast to the
      CounterOptions.field, the value here is a constant that is not derived
      from the IAMContext.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleIamV1LogConfigDataAccessOptions(_messages.Message):
  r"""Write a Data Access (Gin) log

  Enums:
    LogModeValueValuesEnum:

  Fields:
    isDirectAuth: Indicates that access was granted by a regular grant policy
    logMode: A LogModeValueValuesEnum attribute.
  """

  class LogModeValueValuesEnum(_messages.Enum):
    r"""LogModeValueValuesEnum enum type.

    Values:
      LOG_MODE_UNSPECIFIED: Client is not required to write a partial Gin log
        immediately after the authorization check. If client chooses to write
        one and it fails, client may either fail open (allow the operation to
        continue) or fail closed (handle as a DENY outcome).
      LOG_FAIL_CLOSED: The application's operation in the context of which
        this authorization check is being made may only be performed if it is
        successfully logged to Gin. For instance, the authorization library
        may satisfy this obligation by emitting a partial log entry at
        authorization check time and only returning ALLOW to the application
        if it succeeds. If a matching Rule has this directive, but the client
        has not indicated that it will honor such requirements, then the IAM
        check will result in authorization failure by setting
        CheckPolicyResponse.success=false.
    """
    LOG_MODE_UNSPECIFIED = 0
    LOG_FAIL_CLOSED = 1

  isDirectAuth = _messages.BooleanField(1)
  logMode = _messages.EnumField('LogModeValueValuesEnum', 2)


class GoogleIamV1Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    rules: If more than one rule is specified, the rules are applied in the
      following manner: - All matching LOG rules are always applied. - If any
      DENY/DENY_WITH_LOG rule matches, permission is denied. Logging will be
      applied if one or more matching rule requires logging. - Otherwise, if
      any ALLOW/ALLOW_WITH_LOG rule matches, permission is granted. Logging
      will be applied if one or more matching rule requires logging. -
      Otherwise, if no rule applies, permission is denied.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('GoogleIamV1AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  rules = _messages.MessageField('GoogleIamV1Rule', 4, repeated=True)
  version = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class GoogleIamV1Rule(_messages.Message):
  r"""A rule to be applied in a Policy.

  Enums:
    ActionValueValuesEnum: Required

  Fields:
    action: Required
    conditions: Additional restrictions that must be met. All conditions must
      pass for the rule to match.
    description: Human-readable description of the rule.
    in_: If one or more 'in' clauses are specified, the rule matches if the
      PRINCIPAL/AUTHORITY_SELECTOR is in at least one of these entries.
    logConfig: The config returned to callers of CheckPolicy for any entries
      that match the LOG action.
    notIn: If one or more 'not_in' clauses are specified, the rule matches if
      the PRINCIPAL/AUTHORITY_SELECTOR is in none of the entries. The format
      for in and not_in entries can be found at in the Local IAM documentation
      (see go/local-iam#features).
    permissions: A permission is a string of form `..` (e.g.,
      'storage.buckets.list'). A value of '*' matches all permissions, and a
      verb part of '*' (e.g., 'storage.buckets.*') matches all verbs.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required

    Values:
      NO_ACTION: Default no action.
      ALLOW: Matching 'Entries' grant access.
      ALLOW_WITH_LOG: Matching 'Entries' grant access and the caller promises
        to log the request per the returned log_configs.
      DENY: Matching 'Entries' deny access.
      DENY_WITH_LOG: Matching 'Entries' deny access and the caller promises to
        log the request per the returned log_configs.
      LOG: Matching 'Entries' tell IAM.Check callers to generate logs.
    """
    NO_ACTION = 0
    ALLOW = 1
    ALLOW_WITH_LOG = 2
    DENY = 3
    DENY_WITH_LOG = 4
    LOG = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  conditions = _messages.MessageField('GoogleIamV1Condition', 2, repeated=True)
  description = _messages.StringField(3)
  in_ = _messages.StringField(4, repeated=True)
  logConfig = _messages.MessageField('GoogleIamV1LogConfig', 5, repeated=True)
  notIn = _messages.StringField(6, repeated=True)
  permissions = _messages.StringField(7, repeated=True)


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeDate(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleTypeExpr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class PolicysimulatorFoldersLocationsAccessPolicySimulationsCreateRequest(_messages.Message):
  r"""A PolicysimulatorFoldersLocationsAccessPolicySimulationsCreateRequest
  object.

  Fields:
    accessPolicySimulationId: Optional. An optional user-specified ID for the
      AccessPolicySimulation. If not provided, a random ID will be generated.
      If provided, it must be unique within the parent resource and should
      limit to only letters, numbers, hyphens, and underscores, and should
      start with a letter. (regex : [a-zA-Z]+[a-zA-Z0-9-_]*])
    googleCloudPolicysimulatorV1alphaAccessPolicySimulation: A
      GoogleCloudPolicysimulatorV1alphaAccessPolicySimulation resource to be
      passed as the request body.
    parent: Required. The resource name of the simulation parent.
      `{projects|folders|organizations}/{resource-id}/locations/{location}`
  """

  accessPolicySimulationId = _messages.StringField(1)
  googleCloudPolicysimulatorV1alphaAccessPolicySimulation = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaAccessPolicySimulation', 2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorFoldersLocationsAccessPolicySimulationsGetRequest(_messages.Message):
  r"""A PolicysimulatorFoldersLocationsAccessPolicySimulationsGetRequest
  object.

  Fields:
    name: Required. The resource name of the simulation resource.
      `{projects|folders|organizations}/{resource-
      id}/locations/{location}/accessPolicySimulations/{simulation_id}`
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorFoldersLocationsAccessPolicySimulationsListRequest(_messages.Message):
  r"""A PolicysimulatorFoldersLocationsAccessPolicySimulationsListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of AccessPolicySimulation objects
      to return. Defaults The maximum value is 100 which is also the default.
    pageToken: Optional. A page token, received from a previous
      ListAccessPolicySimulations call. Provide this token to retrieve the
      next page of results. When paginating, all other parameters (except
      page_size) provided to ListAccessPolicySimulations must match the call
      that provided the page token.
    parent: Required. The parent node of the simulation resources.
      `{projects|folders|organizations}/{resource-id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorFoldersLocationsAccessPolicySimulationsResultsGetRequest(_messages.Message):
  r"""A
  PolicysimulatorFoldersLocationsAccessPolicySimulationsResultsGetRequest
  object.

  Fields:
    name: Required. The resource name of the simulation result resource.
      `{projects|folders|organizations}/{resource-id}/locations/{location}/acc
      essPolicySimulations/{simulation_id}/results/{result_id}`
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorFoldersLocationsAccessPolicySimulationsResultsListRequest(_messages.Message):
  r"""A
  PolicysimulatorFoldersLocationsAccessPolicySimulationsResultsListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of AccessPolicySimulationResult
      objects to return. Defaults to 1000. The maximum value is 1000; values
      above 1000 are rounded down to 1000.
    pageToken: Optional. A page token, received from a previous
      ListAccessPolicySimulationResults call. Provide this token to retrieve
      the next page of results. When paginating, all other parameters provided
      to ListAccessPolicySimulationResults must match the call that provided
      the page token.
    parent: Required. The simulation for which to return the results.
      `{projects|folders|organizations}/{resource-
      id}/locations/{location}/accessPolicySimulations/{simulation_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorFoldersLocationsOrgPolicyViolationsPreviewsOperationsGetRequest(_messages.Message):
  r"""A PolicysimulatorFoldersLocationsOrgPolicyViolationsPreviewsOperationsGe
  tRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorFoldersLocationsReplaysCreateRequest(_messages.Message):
  r"""A PolicysimulatorFoldersLocationsReplaysCreateRequest object.

  Fields:
    googleCloudPolicysimulatorV1alphaReplay: A
      GoogleCloudPolicysimulatorV1alphaReplay resource to be passed as the
      request body.
    parent: Required. The parent resource where this Replay will be created.
      This resource must be a project, folder, or organization with a
      location. Example: `projects/my-example-project/locations/global`
  """

  googleCloudPolicysimulatorV1alphaReplay = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaReplay', 1)
  parent = _messages.StringField(2, required=True)


class PolicysimulatorFoldersLocationsReplaysGetRequest(_messages.Message):
  r"""A PolicysimulatorFoldersLocationsReplaysGetRequest object.

  Fields:
    name: Required. The name of the Replay to retrieve, in the following
      format: `{projects|folders|organizations}/{resource-
      id}/locations/global/replays/{replay-id}`, where `{resource-id}` is the
      ID of the project, folder, or organization that owns the `Replay`.
      Example: `projects/my-example-
      project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorFoldersLocationsReplaysListRequest(_messages.Message):
  r"""A PolicysimulatorFoldersLocationsReplaysListRequest object.

  Fields:
    pageSize: The maximum number of Replay objects to return. Defaults to 50.
      The maximum value is 1000; values above 1000 are rounded down to 1000.
    pageToken: A page token, received from a previous Simulator.ListReplays
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to Simulator.ListReplays must match the call
      that provided the page token.
    parent: Required. The parent resource, in the following format:
      `{projects|folders|organizations}/{resource-id}/locations/global`, where
      `{resource-id}` is the ID of the project, folder, or organization that
      owns the Replay. Example: `projects/my-example-project/locations/global`
      Only `Replay` objects that are direct children of the provided parent
      are listed. In other words, `Replay` objects that are children of a
      project will not be included when the parent is a folder of that
      project.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorFoldersLocationsReplaysOperationsGetRequest(_messages.Message):
  r"""A PolicysimulatorFoldersLocationsReplaysOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorFoldersLocationsReplaysOperationsListRequest(_messages.Message):
  r"""A PolicysimulatorFoldersLocationsReplaysOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class PolicysimulatorFoldersLocationsReplaysResultsListRequest(_messages.Message):
  r"""A PolicysimulatorFoldersLocationsReplaysResultsListRequest object.

  Fields:
    pageSize: The maximum number of ReplayResult objects to return. Defaults
      to 5000. The maximum value is 5000; values above 5000 are rounded down
      to 5000.
    pageToken: A page token, received from a previous
      Simulator.ListReplayResults call. Provide this token to retrieve the
      next page of results. When paginating, all other parameters provided to
      [Simulator.ListReplayResults[] must match the call that provided the
      page token.
    parent: Required. The Replay whose results are listed, in the following
      format: `{projects|folders|organizations}/{resource-
      id}/locations/global/replays/{replay-id}` Example: `projects/my-
      project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorOperationsGetRequest(_messages.Message):
  r"""A PolicysimulatorOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorOperationsListRequest(_messages.Message):
  r"""A PolicysimulatorOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class PolicysimulatorOrganizationsLocationsAccessPolicySimulationsCreateRequest(_messages.Message):
  r"""A
  PolicysimulatorOrganizationsLocationsAccessPolicySimulationsCreateRequest
  object.

  Fields:
    accessPolicySimulationId: Optional. An optional user-specified ID for the
      AccessPolicySimulation. If not provided, a random ID will be generated.
      If provided, it must be unique within the parent resource and should
      limit to only letters, numbers, hyphens, and underscores, and should
      start with a letter. (regex : [a-zA-Z]+[a-zA-Z0-9-_]*])
    googleCloudPolicysimulatorV1alphaAccessPolicySimulation: A
      GoogleCloudPolicysimulatorV1alphaAccessPolicySimulation resource to be
      passed as the request body.
    parent: Required. The resource name of the simulation parent.
      `{projects|folders|organizations}/{resource-id}/locations/{location}`
  """

  accessPolicySimulationId = _messages.StringField(1)
  googleCloudPolicysimulatorV1alphaAccessPolicySimulation = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaAccessPolicySimulation', 2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorOrganizationsLocationsAccessPolicySimulationsGetRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsAccessPolicySimulationsGetRequest
  object.

  Fields:
    name: Required. The resource name of the simulation resource.
      `{projects|folders|organizations}/{resource-
      id}/locations/{location}/accessPolicySimulations/{simulation_id}`
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorOrganizationsLocationsAccessPolicySimulationsListRequest(_messages.Message):
  r"""A
  PolicysimulatorOrganizationsLocationsAccessPolicySimulationsListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of AccessPolicySimulation objects
      to return. Defaults The maximum value is 100 which is also the default.
    pageToken: Optional. A page token, received from a previous
      ListAccessPolicySimulations call. Provide this token to retrieve the
      next page of results. When paginating, all other parameters (except
      page_size) provided to ListAccessPolicySimulations must match the call
      that provided the page token.
    parent: Required. The parent node of the simulation resources.
      `{projects|folders|organizations}/{resource-id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorOrganizationsLocationsAccessPolicySimulationsResultsGetRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsAccessPolicySimulationsResultsGet
  Request object.

  Fields:
    name: Required. The resource name of the simulation result resource.
      `{projects|folders|organizations}/{resource-id}/locations/{location}/acc
      essPolicySimulations/{simulation_id}/results/{result_id}`
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorOrganizationsLocationsAccessPolicySimulationsResultsListRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsAccessPolicySimulationsResultsLis
  tRequest object.

  Fields:
    pageSize: Optional. The maximum number of AccessPolicySimulationResult
      objects to return. Defaults to 1000. The maximum value is 1000; values
      above 1000 are rounded down to 1000.
    pageToken: Optional. A page token, received from a previous
      ListAccessPolicySimulationResults call. Provide this token to retrieve
      the next page of results. When paginating, all other parameters provided
      to ListAccessPolicySimulationResults must match the call that provided
      the page token.
    parent: Required. The simulation for which to return the results.
      `{projects|folders|organizations}/{resource-
      id}/locations/{location}/accessPolicySimulations/{simulation_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsCreateRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsCreate
  Request object.

  Fields:
    googleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview: A
      GoogleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview resource to
      be passed as the request body.
    orgPolicyViolationsPreviewId: Optional. An optional user-specified ID for
      the OrgPolicyViolationsPreview. If not provided, a random ID will be
      generated.
    parent: Required. The organization under which this
      OrgPolicyViolationsPreview will be created. Example: `organizations/my-
      example-org/locations/global`
  """

  googleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview', 1)
  orgPolicyViolationsPreviewId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsGenerateRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsGenera
  teRequest object.

  Fields:
    googleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview: A
      GoogleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview resource to
      be passed as the request body.
    parent: Required. The organization under which this
      OrgPolicyViolationsPreview will be created. Example: `organizations/my-
      example-org/locations/global`
  """

  googleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview', 1)
  parent = _messages.StringField(2, required=True)


class PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsGetRequest(_messages.Message):
  r"""A
  PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsGetRequest
  object.

  Fields:
    name: Required. The name of the OrgPolicyViolationsPreview to get.
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsListRequest(_messages.Message):
  r"""A
  PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of items to return. The service may
      return fewer than this value. If unspecified, at most 5 items will be
      returned. The maximum value is 10; values above 10 will be coerced to
      10.
    pageToken: Optional. A page token, received from a previous call. Provide
      this to retrieve the subsequent page. When paginating, all other
      parameters must match the call that provided the page token.
    parent: Required. The parent the violations are scoped to. Format:
      `organizations/{organization}/locations/{location}` Example:
      `organizations/my-example-org/locations/global`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsOperationsGetRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsOperat
  ionsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsOrgPolicyViolationsGetRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsOrgPol
  icyViolationsGetRequest object.

  Fields:
    name: Required. The name of the OrgPolicyViolation to get.
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsOrgPolicyViolationsListRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsOrgPol
  icyViolationsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of items to return. The service may
      return fewer than this value. If unspecified, at most 50 items will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. A page token, received from a previous call. Provide
      this to retrieve the subsequent page. When paginating, all other
      parameters must match the call that provided the page token.
    parent: Required. The OrgPolicyViolationsPreview to get
      OrgPolicyViolations from. Format: organizations/{organization}/locations
      /{location}/orgPolicyViolationsPreviews/{orgPolicyViolationsPreview}
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorOrganizationsLocationsPabSimulationsCreateRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsPabSimulationsCreateRequest
  object.

  Fields:
    googleCloudPolicysimulatorV1alphaPabSimulation: A
      GoogleCloudPolicysimulatorV1alphaPabSimulation resource to be passed as
      the request body.
    pabSimulationId: Optional. An optional user-specified ID for the
      PabSimulation. If not provided, a random ID will be generated.
    parent: Required. The resource name of the simulation parent. Required.
      `organizations/{organization_id}/locations/{location}`
  """

  googleCloudPolicysimulatorV1alphaPabSimulation = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaPabSimulation', 1)
  pabSimulationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorOrganizationsLocationsPabSimulationsResultsListRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsPabSimulationsResultsListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of PolicySimulationResult objects
      to return. Defaults to 1000. The maximum value is 1000; values above
      1000 are rounded down to 1000.
    pageToken: Optional. A page token, received from a previous
      ListPolicySimulationResults call. Provide this token to retrieve the
      next page of results. When paginating, all other parameters provided to
      ListPolicySimulationResults must match the call that provided the page
      token.
    parent: Required. The simulation for which to return the results.
      Required. `organizations/{organization_id}/locations/{location}/pabSimul
      ations/{pab_simulation_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorOrganizationsLocationsReplaysCreateRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsReplaysCreateRequest object.

  Fields:
    googleCloudPolicysimulatorV1alphaReplay: A
      GoogleCloudPolicysimulatorV1alphaReplay resource to be passed as the
      request body.
    parent: Required. The parent resource where this Replay will be created.
      This resource must be a project, folder, or organization with a
      location. Example: `projects/my-example-project/locations/global`
  """

  googleCloudPolicysimulatorV1alphaReplay = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaReplay', 1)
  parent = _messages.StringField(2, required=True)


class PolicysimulatorOrganizationsLocationsReplaysGetRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsReplaysGetRequest object.

  Fields:
    name: Required. The name of the Replay to retrieve, in the following
      format: `{projects|folders|organizations}/{resource-
      id}/locations/global/replays/{replay-id}`, where `{resource-id}` is the
      ID of the project, folder, or organization that owns the `Replay`.
      Example: `projects/my-example-
      project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorOrganizationsLocationsReplaysListRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsReplaysListRequest object.

  Fields:
    pageSize: The maximum number of Replay objects to return. Defaults to 50.
      The maximum value is 1000; values above 1000 are rounded down to 1000.
    pageToken: A page token, received from a previous Simulator.ListReplays
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to Simulator.ListReplays must match the call
      that provided the page token.
    parent: Required. The parent resource, in the following format:
      `{projects|folders|organizations}/{resource-id}/locations/global`, where
      `{resource-id}` is the ID of the project, folder, or organization that
      owns the Replay. Example: `projects/my-example-project/locations/global`
      Only `Replay` objects that are direct children of the provided parent
      are listed. In other words, `Replay` objects that are children of a
      project will not be included when the parent is a folder of that
      project.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorOrganizationsLocationsReplaysOperationsGetRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsReplaysOperationsGetRequest
  object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorOrganizationsLocationsReplaysOperationsListRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsReplaysOperationsListRequest
  object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class PolicysimulatorOrganizationsLocationsReplaysResultsListRequest(_messages.Message):
  r"""A PolicysimulatorOrganizationsLocationsReplaysResultsListRequest object.

  Fields:
    pageSize: The maximum number of ReplayResult objects to return. Defaults
      to 5000. The maximum value is 5000; values above 5000 are rounded down
      to 5000.
    pageToken: A page token, received from a previous
      Simulator.ListReplayResults call. Provide this token to retrieve the
      next page of results. When paginating, all other parameters provided to
      [Simulator.ListReplayResults[] must match the call that provided the
      page token.
    parent: Required. The Replay whose results are listed, in the following
      format: `{projects|folders|organizations}/{resource-
      id}/locations/global/replays/{replay-id}` Example: `projects/my-
      project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorProjectsLocationsAccessPolicySimulationsCreateRequest(_messages.Message):
  r"""A PolicysimulatorProjectsLocationsAccessPolicySimulationsCreateRequest
  object.

  Fields:
    accessPolicySimulationId: Optional. An optional user-specified ID for the
      AccessPolicySimulation. If not provided, a random ID will be generated.
      If provided, it must be unique within the parent resource and should
      limit to only letters, numbers, hyphens, and underscores, and should
      start with a letter. (regex : [a-zA-Z]+[a-zA-Z0-9-_]*])
    googleCloudPolicysimulatorV1alphaAccessPolicySimulation: A
      GoogleCloudPolicysimulatorV1alphaAccessPolicySimulation resource to be
      passed as the request body.
    parent: Required. The resource name of the simulation parent.
      `{projects|folders|organizations}/{resource-id}/locations/{location}`
  """

  accessPolicySimulationId = _messages.StringField(1)
  googleCloudPolicysimulatorV1alphaAccessPolicySimulation = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaAccessPolicySimulation', 2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorProjectsLocationsAccessPolicySimulationsGetRequest(_messages.Message):
  r"""A PolicysimulatorProjectsLocationsAccessPolicySimulationsGetRequest
  object.

  Fields:
    name: Required. The resource name of the simulation resource.
      `{projects|folders|organizations}/{resource-
      id}/locations/{location}/accessPolicySimulations/{simulation_id}`
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorProjectsLocationsAccessPolicySimulationsListRequest(_messages.Message):
  r"""A PolicysimulatorProjectsLocationsAccessPolicySimulationsListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of AccessPolicySimulation objects
      to return. Defaults The maximum value is 100 which is also the default.
    pageToken: Optional. A page token, received from a previous
      ListAccessPolicySimulations call. Provide this token to retrieve the
      next page of results. When paginating, all other parameters (except
      page_size) provided to ListAccessPolicySimulations must match the call
      that provided the page token.
    parent: Required. The parent node of the simulation resources.
      `{projects|folders|organizations}/{resource-id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorProjectsLocationsAccessPolicySimulationsResultsGetRequest(_messages.Message):
  r"""A
  PolicysimulatorProjectsLocationsAccessPolicySimulationsResultsGetRequest
  object.

  Fields:
    name: Required. The resource name of the simulation result resource.
      `{projects|folders|organizations}/{resource-id}/locations/{location}/acc
      essPolicySimulations/{simulation_id}/results/{result_id}`
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorProjectsLocationsAccessPolicySimulationsResultsListRequest(_messages.Message):
  r"""A
  PolicysimulatorProjectsLocationsAccessPolicySimulationsResultsListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of AccessPolicySimulationResult
      objects to return. Defaults to 1000. The maximum value is 1000; values
      above 1000 are rounded down to 1000.
    pageToken: Optional. A page token, received from a previous
      ListAccessPolicySimulationResults call. Provide this token to retrieve
      the next page of results. When paginating, all other parameters provided
      to ListAccessPolicySimulationResults must match the call that provided
      the page token.
    parent: Required. The simulation for which to return the results.
      `{projects|folders|organizations}/{resource-
      id}/locations/{location}/accessPolicySimulations/{simulation_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorProjectsLocationsOrgPolicyViolationsPreviewsOperationsGetRequest(_messages.Message):
  r"""A PolicysimulatorProjectsLocationsOrgPolicyViolationsPreviewsOperationsG
  etRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorProjectsLocationsReplaysCreateRequest(_messages.Message):
  r"""A PolicysimulatorProjectsLocationsReplaysCreateRequest object.

  Fields:
    googleCloudPolicysimulatorV1alphaReplay: A
      GoogleCloudPolicysimulatorV1alphaReplay resource to be passed as the
      request body.
    parent: Required. The parent resource where this Replay will be created.
      This resource must be a project, folder, or organization with a
      location. Example: `projects/my-example-project/locations/global`
  """

  googleCloudPolicysimulatorV1alphaReplay = _messages.MessageField('GoogleCloudPolicysimulatorV1alphaReplay', 1)
  parent = _messages.StringField(2, required=True)


class PolicysimulatorProjectsLocationsReplaysGetRequest(_messages.Message):
  r"""A PolicysimulatorProjectsLocationsReplaysGetRequest object.

  Fields:
    name: Required. The name of the Replay to retrieve, in the following
      format: `{projects|folders|organizations}/{resource-
      id}/locations/global/replays/{replay-id}`, where `{resource-id}` is the
      ID of the project, folder, or organization that owns the `Replay`.
      Example: `projects/my-example-
      project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorProjectsLocationsReplaysListRequest(_messages.Message):
  r"""A PolicysimulatorProjectsLocationsReplaysListRequest object.

  Fields:
    pageSize: The maximum number of Replay objects to return. Defaults to 50.
      The maximum value is 1000; values above 1000 are rounded down to 1000.
    pageToken: A page token, received from a previous Simulator.ListReplays
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to Simulator.ListReplays must match the call
      that provided the page token.
    parent: Required. The parent resource, in the following format:
      `{projects|folders|organizations}/{resource-id}/locations/global`, where
      `{resource-id}` is the ID of the project, folder, or organization that
      owns the Replay. Example: `projects/my-example-project/locations/global`
      Only `Replay` objects that are direct children of the provided parent
      are listed. In other words, `Replay` objects that are children of a
      project will not be included when the parent is a folder of that
      project.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PolicysimulatorProjectsLocationsReplaysOperationsGetRequest(_messages.Message):
  r"""A PolicysimulatorProjectsLocationsReplaysOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class PolicysimulatorProjectsLocationsReplaysOperationsListRequest(_messages.Message):
  r"""A PolicysimulatorProjectsLocationsReplaysOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class PolicysimulatorProjectsLocationsReplaysResultsListRequest(_messages.Message):
  r"""A PolicysimulatorProjectsLocationsReplaysResultsListRequest object.

  Fields:
    pageSize: The maximum number of ReplayResult objects to return. Defaults
      to 5000. The maximum value is 5000; values above 5000 are rounded down
      to 5000.
    pageToken: A page token, received from a previous
      Simulator.ListReplayResults call. Provide this token to retrieve the
      next page of results. When paginating, all other parameters provided to
      [Simulator.ListReplayResults[] must match the call that provided the
      page token.
    parent: Required. The Replay whose results are listed, in the following
      format: `{projects|folders|organizations}/{resource-
      id}/locations/global/replays/{replay-id}` Example: `projects/my-
      project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    GoogleIamV1Rule, 'in_', 'in')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
