"""Generated client library for policysimulator version v1alpha."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.policysimulator.v1alpha import policysimulator_v1alpha_messages as messages


class PolicysimulatorV1alpha(base_api.BaseApiClient):
  """Generated client library for service policysimulator version v1alpha."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://policysimulator.googleapis.com/'
  MTLS_BASE_URL = 'https://policysimulator.mtls.googleapis.com/'

  _PACKAGE = 'policysimulator'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'PolicysimulatorV1alpha'
  _URL_VERSION = 'v1alpha'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new policysimulator handle."""
    url = url or self.BASE_URL
    super(PolicysimulatorV1alpha, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.folders_locations_accessPolicySimulations_results = self.FoldersLocationsAccessPolicySimulationsResultsService(self)
    self.folders_locations_accessPolicySimulations = self.FoldersLocationsAccessPolicySimulationsService(self)
    self.folders_locations_orgPolicyViolationsPreviews_operations = self.FoldersLocationsOrgPolicyViolationsPreviewsOperationsService(self)
    self.folders_locations_orgPolicyViolationsPreviews = self.FoldersLocationsOrgPolicyViolationsPreviewsService(self)
    self.folders_locations_replays_operations = self.FoldersLocationsReplaysOperationsService(self)
    self.folders_locations_replays_results = self.FoldersLocationsReplaysResultsService(self)
    self.folders_locations_replays = self.FoldersLocationsReplaysService(self)
    self.folders_locations = self.FoldersLocationsService(self)
    self.folders = self.FoldersService(self)
    self.operations = self.OperationsService(self)
    self.organizations_locations_accessPolicySimulations_results = self.OrganizationsLocationsAccessPolicySimulationsResultsService(self)
    self.organizations_locations_accessPolicySimulations = self.OrganizationsLocationsAccessPolicySimulationsService(self)
    self.organizations_locations_orgPolicyViolationsPreviews_operations = self.OrganizationsLocationsOrgPolicyViolationsPreviewsOperationsService(self)
    self.organizations_locations_orgPolicyViolationsPreviews_orgPolicyViolations = self.OrganizationsLocationsOrgPolicyViolationsPreviewsOrgPolicyViolationsService(self)
    self.organizations_locations_orgPolicyViolationsPreviews = self.OrganizationsLocationsOrgPolicyViolationsPreviewsService(self)
    self.organizations_locations_pabSimulations_results = self.OrganizationsLocationsPabSimulationsResultsService(self)
    self.organizations_locations_pabSimulations = self.OrganizationsLocationsPabSimulationsService(self)
    self.organizations_locations_replays_operations = self.OrganizationsLocationsReplaysOperationsService(self)
    self.organizations_locations_replays_results = self.OrganizationsLocationsReplaysResultsService(self)
    self.organizations_locations_replays = self.OrganizationsLocationsReplaysService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_locations_accessPolicySimulations_results = self.ProjectsLocationsAccessPolicySimulationsResultsService(self)
    self.projects_locations_accessPolicySimulations = self.ProjectsLocationsAccessPolicySimulationsService(self)
    self.projects_locations_orgPolicyViolationsPreviews_operations = self.ProjectsLocationsOrgPolicyViolationsPreviewsOperationsService(self)
    self.projects_locations_orgPolicyViolationsPreviews = self.ProjectsLocationsOrgPolicyViolationsPreviewsService(self)
    self.projects_locations_replays_operations = self.ProjectsLocationsReplaysOperationsService(self)
    self.projects_locations_replays_results = self.ProjectsLocationsReplaysResultsService(self)
    self.projects_locations_replays = self.ProjectsLocationsReplaysService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class FoldersLocationsAccessPolicySimulationsResultsService(base_api.BaseApiService):
    """Service class for the folders_locations_accessPolicySimulations_results resource."""

    _NAME = 'folders_locations_accessPolicySimulations_results'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.FoldersLocationsAccessPolicySimulationsResultsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a policy simulation result.

      Args:
        request: (PolicysimulatorFoldersLocationsAccessPolicySimulationsResultsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaAccessPolicySimulationResult) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/accessPolicySimulations/{accessPolicySimulationsId}/results/{resultsId}',
        http_method='GET',
        method_id='policysimulator.folders.locations.accessPolicySimulations.results.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorFoldersLocationsAccessPolicySimulationsResultsGetRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaAccessPolicySimulationResult',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the results for a policy simulation.

      Args:
        request: (PolicysimulatorFoldersLocationsAccessPolicySimulationsResultsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListAccessPolicySimulationResultsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/accessPolicySimulations/{accessPolicySimulationsId}/results',
        http_method='GET',
        method_id='policysimulator.folders.locations.accessPolicySimulations.results.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/results',
        request_field='',
        request_type_name='PolicysimulatorFoldersLocationsAccessPolicySimulationsResultsListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListAccessPolicySimulationResultsResponse',
        supports_download=False,
    )

  class FoldersLocationsAccessPolicySimulationsService(base_api.BaseApiService):
    """Service class for the folders_locations_accessPolicySimulations resource."""

    _NAME = 'folders_locations_accessPolicySimulations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.FoldersLocationsAccessPolicySimulationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates and starts a policy simulation.

      Args:
        request: (PolicysimulatorFoldersLocationsAccessPolicySimulationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/accessPolicySimulations',
        http_method='POST',
        method_id='policysimulator.folders.locations.accessPolicySimulations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['accessPolicySimulationId'],
        relative_path='v1alpha/{+parent}/accessPolicySimulations',
        request_field='googleCloudPolicysimulatorV1alphaAccessPolicySimulation',
        request_type_name='PolicysimulatorFoldersLocationsAccessPolicySimulationsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a policy simulation.

      Args:
        request: (PolicysimulatorFoldersLocationsAccessPolicySimulationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaAccessPolicySimulation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/accessPolicySimulations/{accessPolicySimulationsId}',
        http_method='GET',
        method_id='policysimulator.folders.locations.accessPolicySimulations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorFoldersLocationsAccessPolicySimulationsGetRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaAccessPolicySimulation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the simulations for a given parent resource.

      Args:
        request: (PolicysimulatorFoldersLocationsAccessPolicySimulationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListAccessPolicySimulationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/accessPolicySimulations',
        http_method='GET',
        method_id='policysimulator.folders.locations.accessPolicySimulations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/accessPolicySimulations',
        request_field='',
        request_type_name='PolicysimulatorFoldersLocationsAccessPolicySimulationsListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListAccessPolicySimulationsResponse',
        supports_download=False,
    )

  class FoldersLocationsOrgPolicyViolationsPreviewsOperationsService(base_api.BaseApiService):
    """Service class for the folders_locations_orgPolicyViolationsPreviews_operations resource."""

    _NAME = 'folders_locations_orgPolicyViolationsPreviews_operations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.FoldersLocationsOrgPolicyViolationsPreviewsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (PolicysimulatorFoldersLocationsOrgPolicyViolationsPreviewsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/orgPolicyViolationsPreviews/{orgPolicyViolationsPreviewsId}/operations/{operationsId}',
        http_method='GET',
        method_id='policysimulator.folders.locations.orgPolicyViolationsPreviews.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorFoldersLocationsOrgPolicyViolationsPreviewsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class FoldersLocationsOrgPolicyViolationsPreviewsService(base_api.BaseApiService):
    """Service class for the folders_locations_orgPolicyViolationsPreviews resource."""

    _NAME = 'folders_locations_orgPolicyViolationsPreviews'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.FoldersLocationsOrgPolicyViolationsPreviewsService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersLocationsReplaysOperationsService(base_api.BaseApiService):
    """Service class for the folders_locations_replays_operations resource."""

    _NAME = 'folders_locations_replays_operations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.FoldersLocationsReplaysOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (PolicysimulatorFoldersLocationsReplaysOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/replays/{replaysId}/operations/{operationsId}',
        http_method='GET',
        method_id='policysimulator.folders.locations.replays.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorFoldersLocationsReplaysOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (PolicysimulatorFoldersLocationsReplaysOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/replays/{replaysId}/operations',
        http_method='GET',
        method_id='policysimulator.folders.locations.replays.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorFoldersLocationsReplaysOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class FoldersLocationsReplaysResultsService(base_api.BaseApiService):
    """Service class for the folders_locations_replays_results resource."""

    _NAME = 'folders_locations_replays_results'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.FoldersLocationsReplaysResultsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the results of running a Replay.

      Args:
        request: (PolicysimulatorFoldersLocationsReplaysResultsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListReplayResultsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/replays/{replaysId}/results',
        http_method='GET',
        method_id='policysimulator.folders.locations.replays.results.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/results',
        request_field='',
        request_type_name='PolicysimulatorFoldersLocationsReplaysResultsListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListReplayResultsResponse',
        supports_download=False,
    )

  class FoldersLocationsReplaysService(base_api.BaseApiService):
    """Service class for the folders_locations_replays resource."""

    _NAME = 'folders_locations_replays'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.FoldersLocationsReplaysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates and starts a Replay using the given ReplayConfig.

      Args:
        request: (PolicysimulatorFoldersLocationsReplaysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/replays',
        http_method='POST',
        method_id='policysimulator.folders.locations.replays.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/replays',
        request_field='googleCloudPolicysimulatorV1alphaReplay',
        request_type_name='PolicysimulatorFoldersLocationsReplaysCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified Replay. Each `Replay` is available for at least 7 days.

      Args:
        request: (PolicysimulatorFoldersLocationsReplaysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaReplay) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/replays/{replaysId}',
        http_method='GET',
        method_id='policysimulator.folders.locations.replays.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorFoldersLocationsReplaysGetRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaReplay',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists each Replay in a project, folder, or organization. Each `Replay` is available for at least 7 days.

      Args:
        request: (PolicysimulatorFoldersLocationsReplaysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListReplaysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/replays',
        http_method='GET',
        method_id='policysimulator.folders.locations.replays.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/replays',
        request_field='',
        request_type_name='PolicysimulatorFoldersLocationsReplaysListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListReplaysResponse',
        supports_download=False,
    )

  class FoldersLocationsService(base_api.BaseApiService):
    """Service class for the folders_locations resource."""

    _NAME = 'folders_locations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.FoldersLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

  class OperationsService(base_api.BaseApiService):
    """Service class for the operations resource."""

    _NAME = 'operations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.OperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (PolicysimulatorOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/operations/{operationsId}',
        http_method='GET',
        method_id='policysimulator.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (PolicysimulatorOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/operations',
        http_method='GET',
        method_id='policysimulator.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsAccessPolicySimulationsResultsService(base_api.BaseApiService):
    """Service class for the organizations_locations_accessPolicySimulations_results resource."""

    _NAME = 'organizations_locations_accessPolicySimulations_results'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.OrganizationsLocationsAccessPolicySimulationsResultsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a policy simulation result.

      Args:
        request: (PolicysimulatorOrganizationsLocationsAccessPolicySimulationsResultsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaAccessPolicySimulationResult) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/accessPolicySimulations/{accessPolicySimulationsId}/results/{resultsId}',
        http_method='GET',
        method_id='policysimulator.organizations.locations.accessPolicySimulations.results.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsAccessPolicySimulationsResultsGetRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaAccessPolicySimulationResult',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the results for a policy simulation.

      Args:
        request: (PolicysimulatorOrganizationsLocationsAccessPolicySimulationsResultsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListAccessPolicySimulationResultsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/accessPolicySimulations/{accessPolicySimulationsId}/results',
        http_method='GET',
        method_id='policysimulator.organizations.locations.accessPolicySimulations.results.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/results',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsAccessPolicySimulationsResultsListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListAccessPolicySimulationResultsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsAccessPolicySimulationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_accessPolicySimulations resource."""

    _NAME = 'organizations_locations_accessPolicySimulations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.OrganizationsLocationsAccessPolicySimulationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates and starts a policy simulation.

      Args:
        request: (PolicysimulatorOrganizationsLocationsAccessPolicySimulationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/accessPolicySimulations',
        http_method='POST',
        method_id='policysimulator.organizations.locations.accessPolicySimulations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['accessPolicySimulationId'],
        relative_path='v1alpha/{+parent}/accessPolicySimulations',
        request_field='googleCloudPolicysimulatorV1alphaAccessPolicySimulation',
        request_type_name='PolicysimulatorOrganizationsLocationsAccessPolicySimulationsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a policy simulation.

      Args:
        request: (PolicysimulatorOrganizationsLocationsAccessPolicySimulationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaAccessPolicySimulation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/accessPolicySimulations/{accessPolicySimulationsId}',
        http_method='GET',
        method_id='policysimulator.organizations.locations.accessPolicySimulations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsAccessPolicySimulationsGetRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaAccessPolicySimulation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the simulations for a given parent resource.

      Args:
        request: (PolicysimulatorOrganizationsLocationsAccessPolicySimulationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListAccessPolicySimulationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/accessPolicySimulations',
        http_method='GET',
        method_id='policysimulator.organizations.locations.accessPolicySimulations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/accessPolicySimulations',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsAccessPolicySimulationsListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListAccessPolicySimulationsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsOrgPolicyViolationsPreviewsOperationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_orgPolicyViolationsPreviews_operations resource."""

    _NAME = 'organizations_locations_orgPolicyViolationsPreviews_operations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.OrganizationsLocationsOrgPolicyViolationsPreviewsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/orgPolicyViolationsPreviews/{orgPolicyViolationsPreviewsId}/operations/{operationsId}',
        http_method='GET',
        method_id='policysimulator.organizations.locations.orgPolicyViolationsPreviews.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class OrganizationsLocationsOrgPolicyViolationsPreviewsOrgPolicyViolationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_orgPolicyViolationsPreviews_orgPolicyViolations resource."""

    _NAME = 'organizations_locations_orgPolicyViolationsPreviews_orgPolicyViolations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.OrganizationsLocationsOrgPolicyViolationsPreviewsOrgPolicyViolationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""GetOrgPolicyViolation gets the specified OrgPolicyViolation that is present in an OrgPolicyViolationsPreview. This method is currently unimplemented.

      Args:
        request: (PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsOrgPolicyViolationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaOrgPolicyViolation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/orgPolicyViolationsPreviews/{orgPolicyViolationsPreviewsId}/orgPolicyViolations/{orgPolicyViolationsId}',
        http_method='GET',
        method_id='policysimulator.organizations.locations.orgPolicyViolationsPreviews.orgPolicyViolations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsOrgPolicyViolationsGetRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaOrgPolicyViolation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""ListOrgPolicyViolations lists the OrgPolicyViolations that are present in an OrgPolicyViolationsPreview.

      Args:
        request: (PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsOrgPolicyViolationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListOrgPolicyViolationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/orgPolicyViolationsPreviews/{orgPolicyViolationsPreviewsId}/orgPolicyViolations',
        http_method='GET',
        method_id='policysimulator.organizations.locations.orgPolicyViolationsPreviews.orgPolicyViolations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/orgPolicyViolations',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsOrgPolicyViolationsListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListOrgPolicyViolationsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsOrgPolicyViolationsPreviewsService(base_api.BaseApiService):
    """Service class for the organizations_locations_orgPolicyViolationsPreviews resource."""

    _NAME = 'organizations_locations_orgPolicyViolationsPreviews'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.OrganizationsLocationsOrgPolicyViolationsPreviewsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""CreateOrgPolicyViolationsPreview creates an OrgPolicyViolationsPreview for the proposed changes in the provided OrgPolicyViolationsPreview.OrgPolicyOverlay. The changes to OrgPolicy are specified by this `OrgPolicyOverlay`. The resources to scan are inferred from these specified changes.

      Args:
        request: (PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/orgPolicyViolationsPreviews',
        http_method='POST',
        method_id='policysimulator.organizations.locations.orgPolicyViolationsPreviews.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['orgPolicyViolationsPreviewId'],
        relative_path='v1alpha/{+parent}/orgPolicyViolationsPreviews',
        request_field='googleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview',
        request_type_name='PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Generate(self, request, global_params=None):
      r"""GenerateOrgPolicyViolationsPreview generates an OrgPolicyViolationsPreview for the proposed changes in the provided OrgPolicyViolationsPreview.OrgPolicyOverlay. The changes to OrgPolicy are specified by this `OrgPolicyOverlay`. The resources to scan are inferred from these specified changes.

      Args:
        request: (PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsGenerateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Generate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Generate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/orgPolicyViolationsPreviews:generate',
        http_method='POST',
        method_id='policysimulator.organizations.locations.orgPolicyViolationsPreviews.generate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/orgPolicyViolationsPreviews:generate',
        request_field='googleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview',
        request_type_name='PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsGenerateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""GetOrgPolicyViolationsPreview gets the specified OrgPolicyViolationsPreview. Each OrgPolicyViolationsPreview is available for at least 7 days.

      Args:
        request: (PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/orgPolicyViolationsPreviews/{orgPolicyViolationsPreviewsId}',
        http_method='GET',
        method_id='policysimulator.organizations.locations.orgPolicyViolationsPreviews.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsGetRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""ListOrgPolicyViolationsPreviews lists each OrgPolicyViolationsPreview in an organization. Each OrgPolicyViolationsPreview is available for at least 7 days.

      Args:
        request: (PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListOrgPolicyViolationsPreviewsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/orgPolicyViolationsPreviews',
        http_method='GET',
        method_id='policysimulator.organizations.locations.orgPolicyViolationsPreviews.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/orgPolicyViolationsPreviews',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsOrgPolicyViolationsPreviewsListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListOrgPolicyViolationsPreviewsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsPabSimulationsResultsService(base_api.BaseApiService):
    """Service class for the organizations_locations_pabSimulations_results resource."""

    _NAME = 'organizations_locations_pabSimulations_results'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.OrganizationsLocationsPabSimulationsResultsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the results for a policy simulation.

      Args:
        request: (PolicysimulatorOrganizationsLocationsPabSimulationsResultsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListPabSimulationResultsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/pabSimulations/{pabSimulationsId}/results',
        http_method='GET',
        method_id='policysimulator.organizations.locations.pabSimulations.results.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/results',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsPabSimulationsResultsListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListPabSimulationResultsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsPabSimulationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_pabSimulations resource."""

    _NAME = 'organizations_locations_pabSimulations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.OrganizationsLocationsPabSimulationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates and starts a policy simulation.

      Args:
        request: (PolicysimulatorOrganizationsLocationsPabSimulationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/pabSimulations',
        http_method='POST',
        method_id='policysimulator.organizations.locations.pabSimulations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pabSimulationId'],
        relative_path='v1alpha/{+parent}/pabSimulations',
        request_field='googleCloudPolicysimulatorV1alphaPabSimulation',
        request_type_name='PolicysimulatorOrganizationsLocationsPabSimulationsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class OrganizationsLocationsReplaysOperationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_replays_operations resource."""

    _NAME = 'organizations_locations_replays_operations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.OrganizationsLocationsReplaysOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (PolicysimulatorOrganizationsLocationsReplaysOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/replays/{replaysId}/operations/{operationsId}',
        http_method='GET',
        method_id='policysimulator.organizations.locations.replays.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsReplaysOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (PolicysimulatorOrganizationsLocationsReplaysOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/replays/{replaysId}/operations',
        http_method='GET',
        method_id='policysimulator.organizations.locations.replays.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsReplaysOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsReplaysResultsService(base_api.BaseApiService):
    """Service class for the organizations_locations_replays_results resource."""

    _NAME = 'organizations_locations_replays_results'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.OrganizationsLocationsReplaysResultsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the results of running a Replay.

      Args:
        request: (PolicysimulatorOrganizationsLocationsReplaysResultsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListReplayResultsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/replays/{replaysId}/results',
        http_method='GET',
        method_id='policysimulator.organizations.locations.replays.results.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/results',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsReplaysResultsListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListReplayResultsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsReplaysService(base_api.BaseApiService):
    """Service class for the organizations_locations_replays resource."""

    _NAME = 'organizations_locations_replays'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.OrganizationsLocationsReplaysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates and starts a Replay using the given ReplayConfig.

      Args:
        request: (PolicysimulatorOrganizationsLocationsReplaysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/replays',
        http_method='POST',
        method_id='policysimulator.organizations.locations.replays.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/replays',
        request_field='googleCloudPolicysimulatorV1alphaReplay',
        request_type_name='PolicysimulatorOrganizationsLocationsReplaysCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified Replay. Each `Replay` is available for at least 7 days.

      Args:
        request: (PolicysimulatorOrganizationsLocationsReplaysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaReplay) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/replays/{replaysId}',
        http_method='GET',
        method_id='policysimulator.organizations.locations.replays.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsReplaysGetRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaReplay',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists each Replay in a project, folder, or organization. Each `Replay` is available for at least 7 days.

      Args:
        request: (PolicysimulatorOrganizationsLocationsReplaysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListReplaysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/replays',
        http_method='GET',
        method_id='policysimulator.organizations.locations.replays.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/replays',
        request_field='',
        request_type_name='PolicysimulatorOrganizationsLocationsReplaysListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListReplaysResponse',
        supports_download=False,
    )

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsAccessPolicySimulationsResultsService(base_api.BaseApiService):
    """Service class for the projects_locations_accessPolicySimulations_results resource."""

    _NAME = 'projects_locations_accessPolicySimulations_results'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.ProjectsLocationsAccessPolicySimulationsResultsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a policy simulation result.

      Args:
        request: (PolicysimulatorProjectsLocationsAccessPolicySimulationsResultsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaAccessPolicySimulationResult) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/accessPolicySimulations/{accessPolicySimulationsId}/results/{resultsId}',
        http_method='GET',
        method_id='policysimulator.projects.locations.accessPolicySimulations.results.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorProjectsLocationsAccessPolicySimulationsResultsGetRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaAccessPolicySimulationResult',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the results for a policy simulation.

      Args:
        request: (PolicysimulatorProjectsLocationsAccessPolicySimulationsResultsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListAccessPolicySimulationResultsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/accessPolicySimulations/{accessPolicySimulationsId}/results',
        http_method='GET',
        method_id='policysimulator.projects.locations.accessPolicySimulations.results.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/results',
        request_field='',
        request_type_name='PolicysimulatorProjectsLocationsAccessPolicySimulationsResultsListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListAccessPolicySimulationResultsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAccessPolicySimulationsService(base_api.BaseApiService):
    """Service class for the projects_locations_accessPolicySimulations resource."""

    _NAME = 'projects_locations_accessPolicySimulations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.ProjectsLocationsAccessPolicySimulationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates and starts a policy simulation.

      Args:
        request: (PolicysimulatorProjectsLocationsAccessPolicySimulationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/accessPolicySimulations',
        http_method='POST',
        method_id='policysimulator.projects.locations.accessPolicySimulations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['accessPolicySimulationId'],
        relative_path='v1alpha/{+parent}/accessPolicySimulations',
        request_field='googleCloudPolicysimulatorV1alphaAccessPolicySimulation',
        request_type_name='PolicysimulatorProjectsLocationsAccessPolicySimulationsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a policy simulation.

      Args:
        request: (PolicysimulatorProjectsLocationsAccessPolicySimulationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaAccessPolicySimulation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/accessPolicySimulations/{accessPolicySimulationsId}',
        http_method='GET',
        method_id='policysimulator.projects.locations.accessPolicySimulations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorProjectsLocationsAccessPolicySimulationsGetRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaAccessPolicySimulation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the simulations for a given parent resource.

      Args:
        request: (PolicysimulatorProjectsLocationsAccessPolicySimulationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListAccessPolicySimulationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/accessPolicySimulations',
        http_method='GET',
        method_id='policysimulator.projects.locations.accessPolicySimulations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/accessPolicySimulations',
        request_field='',
        request_type_name='PolicysimulatorProjectsLocationsAccessPolicySimulationsListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListAccessPolicySimulationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOrgPolicyViolationsPreviewsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_orgPolicyViolationsPreviews_operations resource."""

    _NAME = 'projects_locations_orgPolicyViolationsPreviews_operations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.ProjectsLocationsOrgPolicyViolationsPreviewsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (PolicysimulatorProjectsLocationsOrgPolicyViolationsPreviewsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/orgPolicyViolationsPreviews/{orgPolicyViolationsPreviewsId}/operations/{operationsId}',
        http_method='GET',
        method_id='policysimulator.projects.locations.orgPolicyViolationsPreviews.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorProjectsLocationsOrgPolicyViolationsPreviewsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsOrgPolicyViolationsPreviewsService(base_api.BaseApiService):
    """Service class for the projects_locations_orgPolicyViolationsPreviews resource."""

    _NAME = 'projects_locations_orgPolicyViolationsPreviews'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.ProjectsLocationsOrgPolicyViolationsPreviewsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsReplaysOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_replays_operations resource."""

    _NAME = 'projects_locations_replays_operations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.ProjectsLocationsReplaysOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (PolicysimulatorProjectsLocationsReplaysOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/replays/{replaysId}/operations/{operationsId}',
        http_method='GET',
        method_id='policysimulator.projects.locations.replays.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorProjectsLocationsReplaysOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (PolicysimulatorProjectsLocationsReplaysOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/replays/{replaysId}/operations',
        http_method='GET',
        method_id='policysimulator.projects.locations.replays.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorProjectsLocationsReplaysOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsReplaysResultsService(base_api.BaseApiService):
    """Service class for the projects_locations_replays_results resource."""

    _NAME = 'projects_locations_replays_results'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.ProjectsLocationsReplaysResultsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the results of running a Replay.

      Args:
        request: (PolicysimulatorProjectsLocationsReplaysResultsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListReplayResultsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/replays/{replaysId}/results',
        http_method='GET',
        method_id='policysimulator.projects.locations.replays.results.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/results',
        request_field='',
        request_type_name='PolicysimulatorProjectsLocationsReplaysResultsListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListReplayResultsResponse',
        supports_download=False,
    )

  class ProjectsLocationsReplaysService(base_api.BaseApiService):
    """Service class for the projects_locations_replays resource."""

    _NAME = 'projects_locations_replays'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.ProjectsLocationsReplaysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates and starts a Replay using the given ReplayConfig.

      Args:
        request: (PolicysimulatorProjectsLocationsReplaysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/replays',
        http_method='POST',
        method_id='policysimulator.projects.locations.replays.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/replays',
        request_field='googleCloudPolicysimulatorV1alphaReplay',
        request_type_name='PolicysimulatorProjectsLocationsReplaysCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified Replay. Each `Replay` is available for at least 7 days.

      Args:
        request: (PolicysimulatorProjectsLocationsReplaysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaReplay) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/replays/{replaysId}',
        http_method='GET',
        method_id='policysimulator.projects.locations.replays.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PolicysimulatorProjectsLocationsReplaysGetRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaReplay',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists each Replay in a project, folder, or organization. Each `Replay` is available for at least 7 days.

      Args:
        request: (PolicysimulatorProjectsLocationsReplaysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudPolicysimulatorV1alphaListReplaysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/replays',
        http_method='GET',
        method_id='policysimulator.projects.locations.replays.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/replays',
        request_field='',
        request_type_name='PolicysimulatorProjectsLocationsReplaysListRequest',
        response_type_name='GoogleCloudPolicysimulatorV1alphaListReplaysResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(PolicysimulatorV1alpha.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
