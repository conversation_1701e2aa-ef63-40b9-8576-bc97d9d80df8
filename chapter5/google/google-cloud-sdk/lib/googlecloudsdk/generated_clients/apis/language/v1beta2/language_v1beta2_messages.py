"""Generated message classes for language version v1beta2.

Provides natural language understanding technologies to developers. Examples
include sentiment analysis, entity recognition, entity sentiment analysis, and
text annotations.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'language'


class AnalyzeEntitiesRequest(_messages.Message):
  r"""The entity analysis request message.

  Enums:
    EncodingTypeValueValuesEnum: The encoding type used by the API to
      calculate offsets.

  Fields:
    document: Input document.
    encodingType: The encoding type used by the API to calculate offsets.
  """

  class EncodingTypeValueValuesEnum(_messages.Enum):
    r"""The encoding type used by the API to calculate offsets.

    Values:
      NONE: If `EncodingType` is not specified, encoding-dependent information
        (such as `begin_offset`) will be set at `-1`.
      UTF8: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-8 encoding of the input. C++ and Go are
        examples of languages that use this encoding natively.
      UTF16: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-16 encoding of the input. Java and
        Javascript are examples of languages that use this encoding natively.
      UTF32: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-32 encoding of the input. Python is an
        example of a language that uses this encoding natively.
    """
    NONE = 0
    UTF8 = 1
    UTF16 = 2
    UTF32 = 3

  document = _messages.MessageField('Document', 1)
  encodingType = _messages.EnumField('EncodingTypeValueValuesEnum', 2)


class AnalyzeEntitiesResponse(_messages.Message):
  r"""The entity analysis response message.

  Fields:
    entities: The recognized entities in the input document.
    language: The language of the text, which will be the same as the language
      specified in the request or, if not specified, the automatically-
      detected language. See Document.language field for more details.
  """

  entities = _messages.MessageField('Entity', 1, repeated=True)
  language = _messages.StringField(2)


class AnalyzeEntitySentimentRequest(_messages.Message):
  r"""The entity-level sentiment analysis request message.

  Enums:
    EncodingTypeValueValuesEnum: The encoding type used by the API to
      calculate offsets.

  Fields:
    document: Input document.
    encodingType: The encoding type used by the API to calculate offsets.
  """

  class EncodingTypeValueValuesEnum(_messages.Enum):
    r"""The encoding type used by the API to calculate offsets.

    Values:
      NONE: If `EncodingType` is not specified, encoding-dependent information
        (such as `begin_offset`) will be set at `-1`.
      UTF8: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-8 encoding of the input. C++ and Go are
        examples of languages that use this encoding natively.
      UTF16: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-16 encoding of the input. Java and
        Javascript are examples of languages that use this encoding natively.
      UTF32: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-32 encoding of the input. Python is an
        example of a language that uses this encoding natively.
    """
    NONE = 0
    UTF8 = 1
    UTF16 = 2
    UTF32 = 3

  document = _messages.MessageField('Document', 1)
  encodingType = _messages.EnumField('EncodingTypeValueValuesEnum', 2)


class AnalyzeEntitySentimentResponse(_messages.Message):
  r"""The entity-level sentiment analysis response message.

  Fields:
    entities: The recognized entities in the input document with associated
      sentiments.
    language: The language of the text, which will be the same as the language
      specified in the request or, if not specified, the automatically-
      detected language. See Document.language field for more details.
  """

  entities = _messages.MessageField('Entity', 1, repeated=True)
  language = _messages.StringField(2)


class AnalyzeSentimentRequest(_messages.Message):
  r"""The sentiment analysis request message.

  Enums:
    EncodingTypeValueValuesEnum: The encoding type used by the API to
      calculate sentence offsets for the sentence sentiment.

  Fields:
    document: Input document.
    encodingType: The encoding type used by the API to calculate sentence
      offsets for the sentence sentiment.
  """

  class EncodingTypeValueValuesEnum(_messages.Enum):
    r"""The encoding type used by the API to calculate sentence offsets for
    the sentence sentiment.

    Values:
      NONE: If `EncodingType` is not specified, encoding-dependent information
        (such as `begin_offset`) will be set at `-1`.
      UTF8: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-8 encoding of the input. C++ and Go are
        examples of languages that use this encoding natively.
      UTF16: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-16 encoding of the input. Java and
        Javascript are examples of languages that use this encoding natively.
      UTF32: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-32 encoding of the input. Python is an
        example of a language that uses this encoding natively.
    """
    NONE = 0
    UTF8 = 1
    UTF16 = 2
    UTF32 = 3

  document = _messages.MessageField('Document', 1)
  encodingType = _messages.EnumField('EncodingTypeValueValuesEnum', 2)


class AnalyzeSentimentResponse(_messages.Message):
  r"""The sentiment analysis response message.

  Fields:
    documentSentiment: The overall sentiment of the input document.
    language: The language of the text, which will be the same as the language
      specified in the request or, if not specified, the automatically-
      detected language. See Document.language field for more details.
    sentences: The sentiment for all the sentences in the document.
  """

  documentSentiment = _messages.MessageField('Sentiment', 1)
  language = _messages.StringField(2)
  sentences = _messages.MessageField('Sentence', 3, repeated=True)


class AnalyzeSyntaxRequest(_messages.Message):
  r"""The syntax analysis request message.

  Enums:
    EncodingTypeValueValuesEnum: The encoding type used by the API to
      calculate offsets.

  Fields:
    document: Input document.
    encodingType: The encoding type used by the API to calculate offsets.
  """

  class EncodingTypeValueValuesEnum(_messages.Enum):
    r"""The encoding type used by the API to calculate offsets.

    Values:
      NONE: If `EncodingType` is not specified, encoding-dependent information
        (such as `begin_offset`) will be set at `-1`.
      UTF8: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-8 encoding of the input. C++ and Go are
        examples of languages that use this encoding natively.
      UTF16: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-16 encoding of the input. Java and
        Javascript are examples of languages that use this encoding natively.
      UTF32: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-32 encoding of the input. Python is an
        example of a language that uses this encoding natively.
    """
    NONE = 0
    UTF8 = 1
    UTF16 = 2
    UTF32 = 3

  document = _messages.MessageField('Document', 1)
  encodingType = _messages.EnumField('EncodingTypeValueValuesEnum', 2)


class AnalyzeSyntaxResponse(_messages.Message):
  r"""The syntax analysis response message.

  Fields:
    language: The language of the text, which will be the same as the language
      specified in the request or, if not specified, the automatically-
      detected language. See Document.language field for more details.
    sentences: Sentences in the input document.
    tokens: Tokens, along with their syntactic information, in the input
      document.
  """

  language = _messages.StringField(1)
  sentences = _messages.MessageField('Sentence', 2, repeated=True)
  tokens = _messages.MessageField('Token', 3, repeated=True)


class AnnotateTextRequest(_messages.Message):
  r"""The request message for the text annotation API, which can perform
  multiple analysis types (sentiment, entities, and syntax) in one call.

  Enums:
    EncodingTypeValueValuesEnum: The encoding type used by the API to
      calculate offsets.

  Fields:
    document: Input document.
    encodingType: The encoding type used by the API to calculate offsets.
    features: The enabled features.
  """

  class EncodingTypeValueValuesEnum(_messages.Enum):
    r"""The encoding type used by the API to calculate offsets.

    Values:
      NONE: If `EncodingType` is not specified, encoding-dependent information
        (such as `begin_offset`) will be set at `-1`.
      UTF8: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-8 encoding of the input. C++ and Go are
        examples of languages that use this encoding natively.
      UTF16: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-16 encoding of the input. Java and
        Javascript are examples of languages that use this encoding natively.
      UTF32: Encoding-dependent information (such as `begin_offset`) is
        calculated based on the UTF-32 encoding of the input. Python is an
        example of a language that uses this encoding natively.
    """
    NONE = 0
    UTF8 = 1
    UTF16 = 2
    UTF32 = 3

  document = _messages.MessageField('Document', 1)
  encodingType = _messages.EnumField('EncodingTypeValueValuesEnum', 2)
  features = _messages.MessageField('Features', 3)


class AnnotateTextResponse(_messages.Message):
  r"""The text annotations response message.

  Fields:
    categories: Categories identified in the input document.
    documentSentiment: The overall sentiment for the document. Populated if
      the user enables
      AnnotateTextRequest.Features.extract_document_sentiment.
    entities: Entities, along with their semantic information, in the input
      document. Populated if the user enables
      AnnotateTextRequest.Features.extract_entities.
    language: The language of the text, which will be the same as the language
      specified in the request or, if not specified, the automatically-
      detected language. See Document.language field for more details.
    sentences: Sentences in the input document. Populated if the user enables
      AnnotateTextRequest.Features.extract_syntax.
    tokens: Tokens, along with their syntactic information, in the input
      document. Populated if the user enables
      AnnotateTextRequest.Features.extract_syntax.
  """

  categories = _messages.MessageField('ClassificationCategory', 1, repeated=True)
  documentSentiment = _messages.MessageField('Sentiment', 2)
  entities = _messages.MessageField('Entity', 3, repeated=True)
  language = _messages.StringField(4)
  sentences = _messages.MessageField('Sentence', 5, repeated=True)
  tokens = _messages.MessageField('Token', 6, repeated=True)


class ClassificationCategory(_messages.Message):
  r"""Represents a category returned from the text classifier.

  Fields:
    confidence: The classifier's confidence of the category. Number represents
      how certain the classifier is that this category represents the given
      text.
    name: The name of the category representing the document.
  """

  confidence = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  name = _messages.StringField(2)


class ClassifyTextRequest(_messages.Message):
  r"""The document classification request message.

  Fields:
    document: Input document.
  """

  document = _messages.MessageField('Document', 1)


class ClassifyTextResponse(_messages.Message):
  r"""The document classification response message.

  Fields:
    categories: Categories representing the input document.
  """

  categories = _messages.MessageField('ClassificationCategory', 1, repeated=True)


class DependencyEdge(_messages.Message):
  r"""Represents dependency parse tree information for a token.

  Enums:
    LabelValueValuesEnum: The parse label for the token.

  Fields:
    headTokenIndex: Represents the head of this token in the dependency tree.
      This is the index of the token which has an arc going to this token. The
      index is the position of the token in the array of tokens returned by
      the API method. If this token is a root token, then the
      `head_token_index` is its own index.
    label: The parse label for the token.
  """

  class LabelValueValuesEnum(_messages.Enum):
    r"""The parse label for the token.

    Values:
      UNKNOWN: Unknown
      ABBREV: Abbreviation modifier
      ACOMP: Adjectival complement
      ADVCL: Adverbial clause modifier
      ADVMOD: Adverbial modifier
      AMOD: Adjectival modifier of an NP
      APPOS: Appositional modifier of an NP
      ATTR: Attribute dependent of a copular verb
      AUX: Auxiliary (non-main) verb
      AUXPASS: Passive auxiliary
      CC: Coordinating conjunction
      CCOMP: Clausal complement of a verb or adjective
      CONJ: Conjunct
      CSUBJ: Clausal subject
      CSUBJPASS: Clausal passive subject
      DEP: Dependency (unable to determine)
      DET: Determiner
      DISCOURSE: Discourse
      DOBJ: Direct object
      EXPL: Expletive
      GOESWITH: Goes with (part of a word in a text not well edited)
      IOBJ: Indirect object
      MARK: Marker (word introducing a subordinate clause)
      MWE: Multi-word expression
      MWV: Multi-word verbal expression
      NEG: Negation modifier
      NN: Noun compound modifier
      NPADVMOD: Noun phrase used as an adverbial modifier
      NSUBJ: Nominal subject
      NSUBJPASS: Passive nominal subject
      NUM: Numeric modifier of a noun
      NUMBER: Element of compound number
      P: Punctuation mark
      PARATAXIS: Parataxis relation
      PARTMOD: Participial modifier
      PCOMP: The complement of a preposition is a clause
      POBJ: Object of a preposition
      POSS: Possession modifier
      POSTNEG: Postverbal negative particle
      PRECOMP: Predicate complement
      PRECONJ: Preconjunt
      PREDET: Predeterminer
      PREF: Prefix
      PREP: Prepositional modifier
      PRONL: The relationship between a verb and verbal morpheme
      PRT: Particle
      PS: Associative or possessive marker
      QUANTMOD: Quantifier phrase modifier
      RCMOD: Relative clause modifier
      RCMODREL: Complementizer in relative clause
      RDROP: Ellipsis without a preceding predicate
      REF: Referent
      REMNANT: Remnant
      REPARANDUM: Reparandum
      ROOT: Root
      SNUM: Suffix specifying a unit of number
      SUFF: Suffix
      TMOD: Temporal modifier
      TOPIC: Topic marker
      VMOD: Clause headed by an infinite form of the verb that modifies a noun
      VOCATIVE: Vocative
      XCOMP: Open clausal complement
      SUFFIX: Name suffix
      TITLE: Name title
      ADVPHMOD: Adverbial phrase modifier
      AUXCAUS: Causative auxiliary
      AUXVV: Helper auxiliary
      DTMOD: Rentaishi (Prenominal modifier)
      FOREIGN: Foreign words
      KW: Keyword
      LIST: List for chains of comparable items
      NOMC: Nominalized clause
      NOMCSUBJ: Nominalized clausal subject
      NOMCSUBJPASS: Nominalized clausal passive
      NUMC: Compound of numeric modifier
      COP: Copula
      DISLOCATED: Dislocated relation (for fronted/topicalized elements)
    """
    UNKNOWN = 0
    ABBREV = 1
    ACOMP = 2
    ADVCL = 3
    ADVMOD = 4
    AMOD = 5
    APPOS = 6
    ATTR = 7
    AUX = 8
    AUXPASS = 9
    CC = 10
    CCOMP = 11
    CONJ = 12
    CSUBJ = 13
    CSUBJPASS = 14
    DEP = 15
    DET = 16
    DISCOURSE = 17
    DOBJ = 18
    EXPL = 19
    GOESWITH = 20
    IOBJ = 21
    MARK = 22
    MWE = 23
    MWV = 24
    NEG = 25
    NN = 26
    NPADVMOD = 27
    NSUBJ = 28
    NSUBJPASS = 29
    NUM = 30
    NUMBER = 31
    P = 32
    PARATAXIS = 33
    PARTMOD = 34
    PCOMP = 35
    POBJ = 36
    POSS = 37
    POSTNEG = 38
    PRECOMP = 39
    PRECONJ = 40
    PREDET = 41
    PREF = 42
    PREP = 43
    PRONL = 44
    PRT = 45
    PS = 46
    QUANTMOD = 47
    RCMOD = 48
    RCMODREL = 49
    RDROP = 50
    REF = 51
    REMNANT = 52
    REPARANDUM = 53
    ROOT = 54
    SNUM = 55
    SUFF = 56
    TMOD = 57
    TOPIC = 58
    VMOD = 59
    VOCATIVE = 60
    XCOMP = 61
    SUFFIX = 62
    TITLE = 63
    ADVPHMOD = 64
    AUXCAUS = 65
    AUXVV = 66
    DTMOD = 67
    FOREIGN = 68
    KW = 69
    LIST = 70
    NOMC = 71
    NOMCSUBJ = 72
    NOMCSUBJPASS = 73
    NUMC = 74
    COP = 75
    DISLOCATED = 76

  headTokenIndex = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  label = _messages.EnumField('LabelValueValuesEnum', 2)


class Document(_messages.Message):
  r"""################################################################ #
  Represents the input to API methods.

  Enums:
    TypeValueValuesEnum: Required. If the type is not set or is
      `TYPE_UNSPECIFIED`, returns an `INVALID_ARGUMENT` error.

  Fields:
    content: The content of the input in string format.
    gcsContentUri: The Google Cloud Storage URI where the file content is
      located. This URI must be of the form: gs://bucket_name/object_name. For
      more details, see https://cloud.google.com/storage/docs/reference-uris.
      NOTE: Cloud Storage object versioning is not supported.
    language: The language of the document (if not specified, the language is
      automatically detected). Both ISO and BCP-47 language codes are
      accepted.<br> [Language Support](/natural-language/docs/languages) lists
      currently supported languages for each API method. If the language
      (either specified by the caller or automatically detected) is not
      supported by the called API method, an `INVALID_ARGUMENT` error is
      returned.
    type: Required. If the type is not set or is `TYPE_UNSPECIFIED`, returns
      an `INVALID_ARGUMENT` error.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. If the type is not set or is `TYPE_UNSPECIFIED`, returns an
    `INVALID_ARGUMENT` error.

    Values:
      TYPE_UNSPECIFIED: The content type is not specified.
      PLAIN_TEXT: Plain text
      HTML: HTML
    """
    TYPE_UNSPECIFIED = 0
    PLAIN_TEXT = 1
    HTML = 2

  content = _messages.StringField(1)
  gcsContentUri = _messages.StringField(2)
  language = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class Entity(_messages.Message):
  r"""Represents a phrase in the text that is a known entity, such as a
  person, an organization, or location. The API associates information, such
  as salience and mentions, with entities.

  Enums:
    TypeValueValuesEnum: The entity type.

  Messages:
    MetadataValue: Metadata associated with the entity.  Currently, Wikipedia
      URLs and Knowledge Graph MIDs are provided, if available. The associated
      keys are "wikipedia_url" and "mid", respectively.

  Fields:
    mentions: The mentions of this entity in the input document. The API
      currently supports proper noun mentions.
    metadata: Metadata associated with the entity.  Currently, Wikipedia URLs
      and Knowledge Graph MIDs are provided, if available. The associated keys
      are "wikipedia_url" and "mid", respectively.
    name: The representative name for the entity.
    salience: The salience score associated with the entity in the [0, 1.0]
      range.  The salience score for an entity provides information about the
      importance or centrality of that entity to the entire document text.
      Scores closer to 0 are less salient, while scores closer to 1.0 are
      highly salient.
    sentiment: For calls to AnalyzeEntitySentiment or if
      AnnotateTextRequest.Features.extract_entity_sentiment is set to true,
      this field will contain the aggregate sentiment expressed for this
      entity in the provided document.
    type: The entity type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The entity type.

    Values:
      UNKNOWN: Unknown
      PERSON: Person
      LOCATION: Location
      ORGANIZATION: Organization
      EVENT: Event
      WORK_OF_ART: Work of art
      CONSUMER_GOOD: Consumer goods
      OTHER: Other types
    """
    UNKNOWN = 0
    PERSON = 1
    LOCATION = 2
    ORGANIZATION = 3
    EVENT = 4
    WORK_OF_ART = 5
    CONSUMER_GOOD = 6
    OTHER = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Metadata associated with the entity.  Currently, Wikipedia URLs and
    Knowledge Graph MIDs are provided, if available. The associated keys are
    "wikipedia_url" and "mid", respectively.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  mentions = _messages.MessageField('EntityMention', 1, repeated=True)
  metadata = _messages.MessageField('MetadataValue', 2)
  name = _messages.StringField(3)
  salience = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  sentiment = _messages.MessageField('Sentiment', 5)
  type = _messages.EnumField('TypeValueValuesEnum', 6)


class EntityMention(_messages.Message):
  r"""Represents a mention for an entity in the text. Currently, proper noun
  mentions are supported.

  Enums:
    TypeValueValuesEnum: The type of the entity mention.

  Fields:
    sentiment: For calls to AnalyzeEntitySentiment or if
      AnnotateTextRequest.Features.extract_entity_sentiment is set to true,
      this field will contain the sentiment expressed for this mention of the
      entity in the provided document.
    text: The mention text.
    type: The type of the entity mention.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the entity mention.

    Values:
      TYPE_UNKNOWN: Unknown
      PROPER: Proper name
      COMMON: Common noun (or noun compound)
    """
    TYPE_UNKNOWN = 0
    PROPER = 1
    COMMON = 2

  sentiment = _messages.MessageField('Sentiment', 1)
  text = _messages.MessageField('TextSpan', 2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class Features(_messages.Message):
  r"""All available features for sentiment, syntax, and semantic analysis.
  Setting each one to true will enable that specific analysis for the input.

  Fields:
    classifyText: Classify the full document into categories.
    extractDocumentSentiment: Extract document-level sentiment.
    extractEntities: Extract entities.
    extractEntitySentiment: Extract entities and their associated sentiment.
    extractSyntax: Extract syntax information.
  """

  classifyText = _messages.BooleanField(1)
  extractDocumentSentiment = _messages.BooleanField(2)
  extractEntities = _messages.BooleanField(3)
  extractEntitySentiment = _messages.BooleanField(4)
  extractSyntax = _messages.BooleanField(5)


class PartOfSpeech(_messages.Message):
  r"""Represents part of speech information for a token.

  Enums:
    AspectValueValuesEnum: The grammatical aspect.
    CaseValueValuesEnum: The grammatical case.
    FormValueValuesEnum: The grammatical form.
    GenderValueValuesEnum: The grammatical gender.
    MoodValueValuesEnum: The grammatical mood.
    NumberValueValuesEnum: The grammatical number.
    PersonValueValuesEnum: The grammatical person.
    ProperValueValuesEnum: The grammatical properness.
    ReciprocityValueValuesEnum: The grammatical reciprocity.
    TagValueValuesEnum: The part of speech tag.
    TenseValueValuesEnum: The grammatical tense.
    VoiceValueValuesEnum: The grammatical voice.

  Fields:
    aspect: The grammatical aspect.
    case: The grammatical case.
    form: The grammatical form.
    gender: The grammatical gender.
    mood: The grammatical mood.
    number: The grammatical number.
    person: The grammatical person.
    proper: The grammatical properness.
    reciprocity: The grammatical reciprocity.
    tag: The part of speech tag.
    tense: The grammatical tense.
    voice: The grammatical voice.
  """

  class AspectValueValuesEnum(_messages.Enum):
    r"""The grammatical aspect.

    Values:
      ASPECT_UNKNOWN: Aspect is not applicable in the analyzed language or is
        not predicted.
      PERFECTIVE: Perfective
      IMPERFECTIVE: Imperfective
      PROGRESSIVE: Progressive
    """
    ASPECT_UNKNOWN = 0
    PERFECTIVE = 1
    IMPERFECTIVE = 2
    PROGRESSIVE = 3

  class CaseValueValuesEnum(_messages.Enum):
    r"""The grammatical case.

    Values:
      CASE_UNKNOWN: Case is not applicable in the analyzed language or is not
        predicted.
      ACCUSATIVE: Accusative
      ADVERBIAL: Adverbial
      COMPLEMENTIVE: Complementive
      DATIVE: Dative
      GENITIVE: Genitive
      INSTRUMENTAL: Instrumental
      LOCATIVE: Locative
      NOMINATIVE: Nominative
      OBLIQUE: Oblique
      PARTITIVE: Partitive
      PREPOSITIONAL: Prepositional
      REFLEXIVE_CASE: Reflexive
      RELATIVE_CASE: Relative
      VOCATIVE: Vocative
    """
    CASE_UNKNOWN = 0
    ACCUSATIVE = 1
    ADVERBIAL = 2
    COMPLEMENTIVE = 3
    DATIVE = 4
    GENITIVE = 5
    INSTRUMENTAL = 6
    LOCATIVE = 7
    NOMINATIVE = 8
    OBLIQUE = 9
    PARTITIVE = 10
    PREPOSITIONAL = 11
    REFLEXIVE_CASE = 12
    RELATIVE_CASE = 13
    VOCATIVE = 14

  class FormValueValuesEnum(_messages.Enum):
    r"""The grammatical form.

    Values:
      FORM_UNKNOWN: Form is not applicable in the analyzed language or is not
        predicted.
      ADNOMIAL: Adnomial
      AUXILIARY: Auxiliary
      COMPLEMENTIZER: Complementizer
      FINAL_ENDING: Final ending
      GERUND: Gerund
      REALIS: Realis
      IRREALIS: Irrealis
      SHORT: Short form
      LONG: Long form
      ORDER: Order form
      SPECIFIC: Specific form
    """
    FORM_UNKNOWN = 0
    ADNOMIAL = 1
    AUXILIARY = 2
    COMPLEMENTIZER = 3
    FINAL_ENDING = 4
    GERUND = 5
    REALIS = 6
    IRREALIS = 7
    SHORT = 8
    LONG = 9
    ORDER = 10
    SPECIFIC = 11

  class GenderValueValuesEnum(_messages.Enum):
    r"""The grammatical gender.

    Values:
      GENDER_UNKNOWN: Gender is not applicable in the analyzed language or is
        not predicted.
      FEMININE: Feminine
      MASCULINE: Masculine
      NEUTER: Neuter
    """
    GENDER_UNKNOWN = 0
    FEMININE = 1
    MASCULINE = 2
    NEUTER = 3

  class MoodValueValuesEnum(_messages.Enum):
    r"""The grammatical mood.

    Values:
      MOOD_UNKNOWN: Mood is not applicable in the analyzed language or is not
        predicted.
      CONDITIONAL_MOOD: Conditional
      IMPERATIVE: Imperative
      INDICATIVE: Indicative
      INTERROGATIVE: Interrogative
      JUSSIVE: Jussive
      SUBJUNCTIVE: Subjunctive
    """
    MOOD_UNKNOWN = 0
    CONDITIONAL_MOOD = 1
    IMPERATIVE = 2
    INDICATIVE = 3
    INTERROGATIVE = 4
    JUSSIVE = 5
    SUBJUNCTIVE = 6

  class NumberValueValuesEnum(_messages.Enum):
    r"""The grammatical number.

    Values:
      NUMBER_UNKNOWN: Number is not applicable in the analyzed language or is
        not predicted.
      SINGULAR: Singular
      PLURAL: Plural
      DUAL: Dual
    """
    NUMBER_UNKNOWN = 0
    SINGULAR = 1
    PLURAL = 2
    DUAL = 3

  class PersonValueValuesEnum(_messages.Enum):
    r"""The grammatical person.

    Values:
      PERSON_UNKNOWN: Person is not applicable in the analyzed language or is
        not predicted.
      FIRST: First
      SECOND: Second
      THIRD: Third
      REFLEXIVE_PERSON: Reflexive
    """
    PERSON_UNKNOWN = 0
    FIRST = 1
    SECOND = 2
    THIRD = 3
    REFLEXIVE_PERSON = 4

  class ProperValueValuesEnum(_messages.Enum):
    r"""The grammatical properness.

    Values:
      PROPER_UNKNOWN: Proper is not applicable in the analyzed language or is
        not predicted.
      PROPER: Proper
      NOT_PROPER: Not proper
    """
    PROPER_UNKNOWN = 0
    PROPER = 1
    NOT_PROPER = 2

  class ReciprocityValueValuesEnum(_messages.Enum):
    r"""The grammatical reciprocity.

    Values:
      RECIPROCITY_UNKNOWN: Reciprocity is not applicable in the analyzed
        language or is not predicted.
      RECIPROCAL: Reciprocal
      NON_RECIPROCAL: Non-reciprocal
    """
    RECIPROCITY_UNKNOWN = 0
    RECIPROCAL = 1
    NON_RECIPROCAL = 2

  class TagValueValuesEnum(_messages.Enum):
    r"""The part of speech tag.

    Values:
      UNKNOWN: Unknown
      ADJ: Adjective
      ADP: Adposition (preposition and postposition)
      ADV: Adverb
      CONJ: Conjunction
      DET: Determiner
      NOUN: Noun (common and proper)
      NUM: Cardinal number
      PRON: Pronoun
      PRT: Particle or other function word
      PUNCT: Punctuation
      VERB: Verb (all tenses and modes)
      X: Other: foreign words, typos, abbreviations
      AFFIX: Affix
    """
    UNKNOWN = 0
    ADJ = 1
    ADP = 2
    ADV = 3
    CONJ = 4
    DET = 5
    NOUN = 6
    NUM = 7
    PRON = 8
    PRT = 9
    PUNCT = 10
    VERB = 11
    X = 12
    AFFIX = 13

  class TenseValueValuesEnum(_messages.Enum):
    r"""The grammatical tense.

    Values:
      TENSE_UNKNOWN: Tense is not applicable in the analyzed language or is
        not predicted.
      CONDITIONAL_TENSE: Conditional
      FUTURE: Future
      PAST: Past
      PRESENT: Present
      IMPERFECT: Imperfect
      PLUPERFECT: Pluperfect
    """
    TENSE_UNKNOWN = 0
    CONDITIONAL_TENSE = 1
    FUTURE = 2
    PAST = 3
    PRESENT = 4
    IMPERFECT = 5
    PLUPERFECT = 6

  class VoiceValueValuesEnum(_messages.Enum):
    r"""The grammatical voice.

    Values:
      VOICE_UNKNOWN: Voice is not applicable in the analyzed language or is
        not predicted.
      ACTIVE: Active
      CAUSATIVE: Causative
      PASSIVE: Passive
    """
    VOICE_UNKNOWN = 0
    ACTIVE = 1
    CAUSATIVE = 2
    PASSIVE = 3

  aspect = _messages.EnumField('AspectValueValuesEnum', 1)
  case = _messages.EnumField('CaseValueValuesEnum', 2)
  form = _messages.EnumField('FormValueValuesEnum', 3)
  gender = _messages.EnumField('GenderValueValuesEnum', 4)
  mood = _messages.EnumField('MoodValueValuesEnum', 5)
  number = _messages.EnumField('NumberValueValuesEnum', 6)
  person = _messages.EnumField('PersonValueValuesEnum', 7)
  proper = _messages.EnumField('ProperValueValuesEnum', 8)
  reciprocity = _messages.EnumField('ReciprocityValueValuesEnum', 9)
  tag = _messages.EnumField('TagValueValuesEnum', 10)
  tense = _messages.EnumField('TenseValueValuesEnum', 11)
  voice = _messages.EnumField('VoiceValueValuesEnum', 12)


class Sentence(_messages.Message):
  r"""Represents a sentence in the input document.

  Fields:
    sentiment: For calls to AnalyzeSentiment or if
      AnnotateTextRequest.Features.extract_document_sentiment is set to true,
      this field will contain the sentiment for the sentence.
    text: The sentence text.
  """

  sentiment = _messages.MessageField('Sentiment', 1)
  text = _messages.MessageField('TextSpan', 2)


class Sentiment(_messages.Message):
  r"""Represents the feeling associated with the entire text or entities in
  the text.

  Fields:
    magnitude: A non-negative number in the [0, +inf) range, which represents
      the absolute magnitude of sentiment regardless of score (positive or
      negative).
    score: Sentiment score between -1.0 (negative sentiment) and 1.0 (positive
      sentiment).
  """

  magnitude = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  score = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    bearer_token: OAuth bearer token.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    pp: Pretty-print response.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  bearer_token = _messages.StringField(4)
  callback = _messages.StringField(5)
  fields = _messages.StringField(6)
  key = _messages.StringField(7)
  oauth_token = _messages.StringField(8)
  pp = _messages.BooleanField(9, default=True)
  prettyPrint = _messages.BooleanField(10, default=True)
  quotaUser = _messages.StringField(11)
  trace = _messages.StringField(12)
  uploadType = _messages.StringField(13)
  upload_protocol = _messages.StringField(14)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). The error model is designed to be:
  - Simple to use and understand for most users - Flexible enough to meet
  unexpected needs  # Overview  The `Status` message contains three pieces of
  data: error code, error message, and error details. The error code should be
  an enum value of google.rpc.Code, but it may accept additional error codes
  if needed.  The error message should be a developer-facing English message
  that helps developers *understand* and *resolve* the error. If a localized
  user-facing error message is needed, put the localized message in the error
  details or localize it in the client. The optional error details may contain
  arbitrary information about the error. There is a predefined set of error
  detail types in the package `google.rpc` that can be used for common error
  conditions.  # Language mapping  The `Status` message is the logical
  representation of the error model, but it is not necessarily the actual wire
  format. When the `Status` message is exposed in different client libraries
  and different wire protocols, it can be mapped differently. For example, it
  will likely be mapped to some exceptions in Java, but more likely mapped to
  some error codes in C.  # Other uses  The error model and the `Status`
  message can be used in a variety of environments, either with or without
  APIs, to provide a consistent developer experience across different
  environments.  Example uses of this error model include:  - Partial errors.
  If a service needs to return partial errors to the client,     it may embed
  the `Status` in the normal response to indicate the partial     errors.  -
  Workflow errors. A typical workflow has multiple steps. Each step may
  have a `Status` message for error reporting.  - Batch operations. If a
  client uses batch request and batch response, the     `Status` message
  should be used directly inside batch response, one for     each error sub-
  response.  - Asynchronous operations. If an API call embeds asynchronous
  operation     results in its response, the status of those operations should
  be     represented directly using the `Status` message.  - Logging. If some
  API errors are stored in logs, the message `Status` could     be used
  directly after any stripping needed for security/privacy reasons.

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details.  There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TextSpan(_messages.Message):
  r"""Represents an output piece of text.

  Fields:
    beginOffset: The API calculates the beginning offset of the content in the
      original document according to the EncodingType specified in the API
      request.
    content: The content of the output text.
  """

  beginOffset = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  content = _messages.StringField(2)


class Token(_messages.Message):
  r"""Represents the smallest syntactic building block of the text.

  Fields:
    dependencyEdge: Dependency tree parse for this token.
    lemma: [Lemma](https://en.wikipedia.org/wiki/Lemma_%28morphology%29) of
      the token.
    partOfSpeech: Parts of speech tag for this token.
    text: The token text.
  """

  dependencyEdge = _messages.MessageField('DependencyEdge', 1)
  lemma = _messages.StringField(2)
  partOfSpeech = _messages.MessageField('PartOfSpeech', 3)
  text = _messages.MessageField('TextSpan', 4)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
