"""Generated message classes for secretmanager version v1beta2.

Stores sensitive data such as API keys, passwords, and certificates. Provides
convenience while improving security.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'secretmanager'


class AccessSecretVersionResponse(_messages.Message):
  r"""Response message for SecretManagerService.AccessSecretVersion.

  Fields:
    name: The resource name of the SecretVersion in the format
      `projects/*/secrets/*/versions/*` or
      `projects/*/locations/*/secrets/*/versions/*`.
    payload: Secret payload
  """

  name = _messages.StringField(1)
  payload = _messages.MessageField('SecretPayload', 2)


class AddSecretVersionRequest(_messages.Message):
  r"""Request message for SecretManagerService.AddSecretVersion.

  Fields:
    payload: Required. The secret payload of the SecretVersion.
  """

  payload = _messages.MessageField('SecretPayload', 1)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Automatic(_messages.Message):
  r"""A replication policy that replicates the Secret payload without any
  restrictions.

  Fields:
    customerManagedEncryption: Optional. The customer-managed encryption
      configuration of the Secret. If no configuration is provided, Google-
      managed default encryption is used. Updates to the Secret encryption
      configuration only apply to SecretVersions added afterwards. They do not
      apply retroactively to existing SecretVersions.
  """

  customerManagedEncryption = _messages.MessageField('CustomerManagedEncryption', 1)


class AutomaticStatus(_messages.Message):
  r"""The replication status of a SecretVersion using automatic replication.
  Only populated if the parent Secret has an automatic replication policy.

  Fields:
    customerManagedEncryption: Output only. The customer-managed encryption
      status of the SecretVersion. Only populated if customer-managed
      encryption is used.
  """

  customerManagedEncryption = _messages.MessageField('CustomerManagedEncryptionStatus', 1)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CustomerManagedEncryption(_messages.Message):
  r"""Configuration for encrypting secret payloads using customer-managed
  encryption keys (CMEK).

  Fields:
    kmsKeyName: Required. The resource name of the Cloud KMS CryptoKey used to
      encrypt secret payloads. For secrets using the UserManaged replication
      policy type, Cloud KMS CryptoKeys must reside in the same location as
      the replica location. For secrets using the Automatic replication policy
      type, Cloud KMS CryptoKeys must reside in `global`. The expected format
      is `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
  """

  kmsKeyName = _messages.StringField(1)


class CustomerManagedEncryptionStatus(_messages.Message):
  r"""Describes the status of customer-managed encryption.

  Fields:
    kmsKeyVersionName: Required. The resource name of the Cloud KMS
      CryptoKeyVersion used to encrypt the secret payload, in the following
      format: `projects/*/locations/*/keyRings/*/cryptoKeys/*/versions/*`.
  """

  kmsKeyVersionName = _messages.StringField(1)


class DestroySecretVersionRequest(_messages.Message):
  r"""Request message for SecretManagerService.DestroySecretVersion.

  Fields:
    etag: Optional. Etag of the SecretVersion. The request succeeds if it
      matches the etag of the currently stored secret version object. If the
      etag is omitted, the request succeeds.
  """

  etag = _messages.StringField(1)


class DisableSecretVersionRequest(_messages.Message):
  r"""Request message for SecretManagerService.DisableSecretVersion.

  Fields:
    etag: Optional. Etag of the SecretVersion. The request succeeds if it
      matches the etag of the currently stored secret version object. If the
      etag is omitted, the request succeeds.
  """

  etag = _messages.StringField(1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EnableSecretVersionRequest(_messages.Message):
  r"""Request message for SecretManagerService.EnableSecretVersion.

  Fields:
    etag: Optional. Etag of the SecretVersion. The request succeeds if it
      matches the etag of the currently stored secret version object. If the
      etag is omitted, the request succeeds.
  """

  etag = _messages.StringField(1)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListSecretVersionsResponse(_messages.Message):
  r"""Response message for SecretManagerService.ListSecretVersions.

  Fields:
    nextPageToken: A token to retrieve the next page of results. Pass this
      value in ListSecretVersionsRequest.page_token to retrieve the next page.
    totalSize: The total number of SecretVersions but 0 when the
      ListSecretsRequest.filter field is set.
    versions: The list of SecretVersions sorted in reverse by create_time
      (newest first).
  """

  nextPageToken = _messages.StringField(1)
  totalSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  versions = _messages.MessageField('SecretVersion', 3, repeated=True)


class ListSecretsResponse(_messages.Message):
  r"""Response message for SecretManagerService.ListSecrets.

  Fields:
    nextPageToken: A token to retrieve the next page of results. Pass this
      value in ListSecretsRequest.page_token to retrieve the next page.
    secrets: The list of Secrets sorted in reverse by create_time (newest
      first).
    totalSize: The total number of Secrets but 0 when the
      ListSecretsRequest.filter field is set.
  """

  nextPageToken = _messages.StringField(1)
  secrets = _messages.MessageField('Secret', 2, repeated=True)
  totalSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Replica(_messages.Message):
  r"""Represents a Replica for this Secret.

  Fields:
    customerManagedEncryption: Optional. The customer-managed encryption
      configuration of the User-Managed Replica. If no configuration is
      provided, Google-managed default encryption is used. Updates to the
      Secret encryption configuration only apply to SecretVersions added
      afterwards. They do not apply retroactively to existing SecretVersions.
    location: The canonical IDs of the location to replicate data. For
      example: `"us-east1"`.
  """

  customerManagedEncryption = _messages.MessageField('CustomerManagedEncryption', 1)
  location = _messages.StringField(2)


class ReplicaStatus(_messages.Message):
  r"""Describes the status of a user-managed replica for the SecretVersion.

  Fields:
    customerManagedEncryption: Output only. The customer-managed encryption
      status of the SecretVersion. Only populated if customer-managed
      encryption is used.
    location: Output only. The canonical ID of the replica location. For
      example: `"us-east1"`.
  """

  customerManagedEncryption = _messages.MessageField('CustomerManagedEncryptionStatus', 1)
  location = _messages.StringField(2)


class Replication(_messages.Message):
  r"""A policy that defines the replication and encryption configuration of
  data.

  Fields:
    automatic: The Secret will automatically be replicated without any
      restrictions.
    userManaged: The Secret will only be replicated into the locations
      specified.
  """

  automatic = _messages.MessageField('Automatic', 1)
  userManaged = _messages.MessageField('UserManaged', 2)


class ReplicationStatus(_messages.Message):
  r"""The replication status of a SecretVersion.

  Fields:
    automatic: Describes the replication status of a SecretVersion with
      automatic replication. Only populated if the parent Secret has an
      automatic replication policy.
    userManaged: Describes the replication status of a SecretVersion with
      user-managed replication. Only populated if the parent Secret has a
      user-managed replication policy.
  """

  automatic = _messages.MessageField('AutomaticStatus', 1)
  userManaged = _messages.MessageField('UserManagedStatus', 2)


class Rotation(_messages.Message):
  r"""The rotation time and period for a Secret. At next_rotation_time, Secret
  Manager will send a Pub/Sub notification to the topics configured on the
  Secret. Secret.topics must be set to configure rotation.

  Fields:
    nextRotationTime: Optional. Timestamp in UTC at which the Secret is
      scheduled to rotate. Cannot be set to less than 300s (5 min) in the
      future and at most 3153600000s (100 years). next_rotation_time MUST be
      set if rotation_period is set.
    rotationPeriod: Input only. The Duration between rotation notifications.
      Must be in seconds and at least 3600s (1h) and at most 3153600000s (100
      years). If rotation_period is set, next_rotation_time must be set.
      next_rotation_time will be advanced by this period when the service
      automatically sends rotation notifications.
  """

  nextRotationTime = _messages.StringField(1)
  rotationPeriod = _messages.StringField(2)


class Secret(_messages.Message):
  r"""A Secret is a logical secret whose value and versions can be accessed. A
  Secret is made up of zero or more SecretVersions that represent the secret
  data.

  Messages:
    AnnotationsValue: Optional. Custom metadata about the secret. Annotations
      are distinct from various forms of labels. Annotations exist to allow
      client tools to store their own state information without requiring a
      database. Annotation keys must be between 1 and 63 characters long, have
      a UTF-8 encoding of maximum 128 bytes, begin and end with an
      alphanumeric character ([a-z0-9A-Z]), and may have dashes (-),
      underscores (_), dots (.), and alphanumerics in between these symbols.
      The total size of annotation keys and values must be less than 16KiB.
    LabelsValue: The labels assigned to this Secret. Label keys must be
      between 1 and 63 characters long, have a UTF-8 encoding of maximum 128
      bytes, and must conform to the following PCRE regular expression:
      `\p{Ll}\p{Lo}{0,62}` Label values must be between 0 and 63 characters
      long, have a UTF-8 encoding of maximum 128 bytes, and must conform to
      the following PCRE regular expression: `[\p{Ll}\p{Lo}\p{N}_-]{0,63}` No
      more than 64 labels can be assigned to a given resource.
    TagsValue: Optional. Input only. Immutable. Mapping of Tag keys/values
      directly bound to this resource. For example: "123/environment":
      "production", "123/costCenter": "marketing" Tags are used to organize
      and group resources. Tags can be used to control policy evaluation for
      the resource.
    VersionAliasesValue: Optional. Mapping from version alias to version name.
      A version alias is a string with a maximum length of 63 characters and
      can contain uppercase and lowercase letters, numerals, and the hyphen
      (`-`) and underscore ('_') characters. An alias string must start with a
      letter and cannot be the string 'latest' or 'NEW'. No more than 50
      aliases can be assigned to a given secret. Version-Alias pairs will be
      viewable via GetSecret and modifiable via UpdateSecret. Access by alias
      is only supported for GetSecretVersion and AccessSecretVersion.

  Fields:
    annotations: Optional. Custom metadata about the secret. Annotations are
      distinct from various forms of labels. Annotations exist to allow client
      tools to store their own state information without requiring a database.
      Annotation keys must be between 1 and 63 characters long, have a UTF-8
      encoding of maximum 128 bytes, begin and end with an alphanumeric
      character ([a-z0-9A-Z]), and may have dashes (-), underscores (_), dots
      (.), and alphanumerics in between these symbols. The total size of
      annotation keys and values must be less than 16KiB.
    createTime: Output only. The time at which the Secret was created.
    customerManagedEncryption: Optional. The customer-managed encryption
      configuration of the Regionalised Secrets. If no configuration is
      provided, Google-managed default encryption is used. Updates to the
      Secret encryption configuration only apply to SecretVersions added
      afterwards. They do not apply retroactively to existing SecretVersions.
    etag: Optional. Etag of the currently stored Secret.
    expireTime: Optional. Timestamp in UTC when the Secret is scheduled to
      expire. This is always provided on output, regardless of what was sent
      on input.
    labels: The labels assigned to this Secret. Label keys must be between 1
      and 63 characters long, have a UTF-8 encoding of maximum 128 bytes, and
      must conform to the following PCRE regular expression:
      `\p{Ll}\p{Lo}{0,62}` Label values must be between 0 and 63 characters
      long, have a UTF-8 encoding of maximum 128 bytes, and must conform to
      the following PCRE regular expression: `[\p{Ll}\p{Lo}\p{N}_-]{0,63}` No
      more than 64 labels can be assigned to a given resource.
    name: Output only. The resource name of the Secret in the format
      `projects/*/secrets/*`.
    replication: Optional. Immutable. The replication policy of the secret
      data attached to the Secret. The replication policy cannot be changed
      after the Secret has been created.
    rotation: Optional. Rotation policy attached to the Secret. May be
      excluded if there is no rotation policy.
    tags: Optional. Input only. Immutable. Mapping of Tag keys/values directly
      bound to this resource. For example: "123/environment": "production",
      "123/costCenter": "marketing" Tags are used to organize and group
      resources. Tags can be used to control policy evaluation for the
      resource.
    topics: Optional. A list of up to 10 Pub/Sub topics to which messages are
      published when control plane operations are called on the secret or its
      versions.
    ttl: Input only. The TTL for the Secret.
    versionAliases: Optional. Mapping from version alias to version name. A
      version alias is a string with a maximum length of 63 characters and can
      contain uppercase and lowercase letters, numerals, and the hyphen (`-`)
      and underscore ('_') characters. An alias string must start with a
      letter and cannot be the string 'latest' or 'NEW'. No more than 50
      aliases can be assigned to a given secret. Version-Alias pairs will be
      viewable via GetSecret and modifiable via UpdateSecret. Access by alias
      is only supported for GetSecretVersion and AccessSecretVersion.
    versionDestroyTtl: Optional. Secret Version TTL after destruction request
      This is a part of the Delayed secret version destroy feature. For secret
      with TTL>0, version destruction doesn't happen immediately on calling
      destroy instead the version goes to a disabled state and destruction
      happens after the TTL expires.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Custom metadata about the secret. Annotations are distinct
    from various forms of labels. Annotations exist to allow client tools to
    store their own state information without requiring a database. Annotation
    keys must be between 1 and 63 characters long, have a UTF-8 encoding of
    maximum 128 bytes, begin and end with an alphanumeric character
    ([a-z0-9A-Z]), and may have dashes (-), underscores (_), dots (.), and
    alphanumerics in between these symbols. The total size of annotation keys
    and values must be less than 16KiB.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels assigned to this Secret. Label keys must be between 1 and
    63 characters long, have a UTF-8 encoding of maximum 128 bytes, and must
    conform to the following PCRE regular expression: `\p{Ll}\p{Lo}{0,62}`
    Label values must be between 0 and 63 characters long, have a UTF-8
    encoding of maximum 128 bytes, and must conform to the following PCRE
    regular expression: `[\p{Ll}\p{Lo}\p{N}_-]{0,63}` No more than 64 labels
    can be assigned to a given resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Input only. Immutable. Mapping of Tag keys/values directly
    bound to this resource. For example: "123/environment": "production",
    "123/costCenter": "marketing" Tags are used to organize and group
    resources. Tags can be used to control policy evaluation for the resource.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class VersionAliasesValue(_messages.Message):
    r"""Optional. Mapping from version alias to version name. A version alias
    is a string with a maximum length of 63 characters and can contain
    uppercase and lowercase letters, numerals, and the hyphen (`-`) and
    underscore ('_') characters. An alias string must start with a letter and
    cannot be the string 'latest' or 'NEW'. No more than 50 aliases can be
    assigned to a given secret. Version-Alias pairs will be viewable via
    GetSecret and modifiable via UpdateSecret. Access by alias is only
    supported for GetSecretVersion and AccessSecretVersion.

    Messages:
      AdditionalProperty: An additional property for a VersionAliasesValue
        object.

    Fields:
      additionalProperties: Additional properties of type VersionAliasesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a VersionAliasesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  customerManagedEncryption = _messages.MessageField('CustomerManagedEncryption', 3)
  etag = _messages.StringField(4)
  expireTime = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  replication = _messages.MessageField('Replication', 8)
  rotation = _messages.MessageField('Rotation', 9)
  tags = _messages.MessageField('TagsValue', 10)
  topics = _messages.MessageField('Topic', 11, repeated=True)
  ttl = _messages.StringField(12)
  versionAliases = _messages.MessageField('VersionAliasesValue', 13)
  versionDestroyTtl = _messages.StringField(14)


class SecretPayload(_messages.Message):
  r"""A secret payload resource in the Secret Manager API. This contains the
  sensitive secret payload that is associated with a SecretVersion.

  Fields:
    data: The secret data. Must be no larger than 64KiB.
    dataCrc32c: Optional. If specified, SecretManagerService will verify the
      integrity of the received data on SecretManagerService.AddSecretVersion
      calls using the crc32c checksum and store it to include in future
      SecretManagerService.AccessSecretVersion responses. If a checksum is not
      provided in the SecretManagerService.AddSecretVersion request, the
      SecretManagerService will generate and store one for you. The CRC32C
      value is encoded as a Int64 for compatibility, and can be safely
      downconverted to uint32 in languages that support this type.
      https://cloud.google.com/apis/design/design_patterns#integer_types
  """

  data = _messages.BytesField(1)
  dataCrc32c = _messages.IntegerField(2)


class SecretVersion(_messages.Message):
  r"""A secret version resource in the Secret Manager API.

  Enums:
    StateValueValuesEnum: Output only. The current state of the SecretVersion.

  Fields:
    clientSpecifiedPayloadChecksum: Output only. True if payload checksum
      specified in SecretPayload object has been received by
      SecretManagerService on SecretManagerService.AddSecretVersion.
    createTime: Output only. The time at which the SecretVersion was created.
    customerManagedEncryption: Output only. The customer-managed encryption
      status of the SecretVersion. Only populated if customer-managed
      encryption is used and Secret is a Regionalised Secret.
    destroyTime: Output only. The time this SecretVersion was destroyed. Only
      present if state is DESTROYED.
    etag: Output only. Etag of the currently stored SecretVersion.
    name: Output only. The resource name of the SecretVersion in the format
      `projects/*/secrets/*/versions/*`. SecretVersion IDs in a Secret start
      at 1 and are incremented for each subsequent version of the secret.
    replicationStatus: The replication status of the SecretVersion.
    scheduledDestroyTime: Optional. Output only. Scheduled destroy time for
      secret version. This is a part of the Delayed secret version destroy
      feature. For a Secret with a valid version destroy TTL, when a secert
      version is destroyed, version is moved to disabled state and it is
      scheduled for destruction Version is destroyed only after the
      scheduled_destroy_time.
    state: Output only. The current state of the SecretVersion.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the SecretVersion.

    Values:
      STATE_UNSPECIFIED: Not specified. This value is unused and invalid.
      ENABLED: The SecretVersion may be accessed.
      DISABLED: The SecretVersion may not be accessed, but the secret data is
        still available and can be placed back into the ENABLED state.
      DESTROYED: The SecretVersion is destroyed and the secret data is no
        longer stored. A version may not leave this state once entered.
    """
    STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2
    DESTROYED = 3

  clientSpecifiedPayloadChecksum = _messages.BooleanField(1)
  createTime = _messages.StringField(2)
  customerManagedEncryption = _messages.MessageField('CustomerManagedEncryptionStatus', 3)
  destroyTime = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  replicationStatus = _messages.MessageField('ReplicationStatus', 7)
  scheduledDestroyTime = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)


class SecretmanagerProjectsLocationsGetRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class SecretmanagerProjectsLocationsListRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class SecretmanagerProjectsLocationsSecretsAddVersionRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsAddVersionRequest object.

  Fields:
    addSecretVersionRequest: A AddSecretVersionRequest resource to be passed
      as the request body.
    parent: Required. The resource name of the Secret to associate with the
      SecretVersion in the format `projects/*/secrets/*` or
      `projects/*/locations/*/secrets/*`.
  """

  addSecretVersionRequest = _messages.MessageField('AddSecretVersionRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecretmanagerProjectsLocationsSecretsCreateRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsCreateRequest object.

  Fields:
    parent: Required. The resource name of the project to associate with the
      Secret, in the format `projects/*` or `projects/*/locations/*`.
    secret: A Secret resource to be passed as the request body.
    secretId: Required. This must be unique within the project. A secret ID is
      a string with a maximum length of 255 characters and can contain
      uppercase and lowercase letters, numerals, and the hyphen (`-`) and
      underscore (`_`) characters.
  """

  parent = _messages.StringField(1, required=True)
  secret = _messages.MessageField('Secret', 2)
  secretId = _messages.StringField(3)


class SecretmanagerProjectsLocationsSecretsDeleteRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsDeleteRequest object.

  Fields:
    etag: Optional. Etag of the Secret. The request succeeds if it matches the
      etag of the currently stored secret object. If the etag is omitted, the
      request succeeds.
    name: Required. The resource name of the Secret to delete in the format
      `projects/*/secrets/*`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class SecretmanagerProjectsLocationsSecretsGetIamPolicyRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class SecretmanagerProjectsLocationsSecretsGetRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsGetRequest object.

  Fields:
    name: Required. The resource name of the Secret, in the format
      `projects/*/secrets/*` or `projects/*/locations/*/secrets/*`.
  """

  name = _messages.StringField(1, required=True)


class SecretmanagerProjectsLocationsSecretsListRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsListRequest object.

  Fields:
    filter: Optional. Filter string, adhering to the rules in [List-operation
      filtering](https://cloud.google.com/secret-manager/docs/filtering). List
      only secrets matching the filter. If filter is empty, all secrets are
      listed.
    pageSize: Optional. The maximum number of results to be returned in a
      single page. If set to 0, the server decides the number of results to
      return. If the number is greater than 25000, it is capped at 25000.
    pageToken: Optional. Pagination token, returned earlier via
      ListSecretsResponse.next_page_token.
    parent: Required. The resource name of the project associated with the
      Secrets, in the format `projects/*` or `projects/*/locations/*`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SecretmanagerProjectsLocationsSecretsPatchRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsPatchRequest object.

  Fields:
    name: Output only. The resource name of the Secret in the format
      `projects/*/secrets/*`.
    secret: A Secret resource to be passed as the request body.
    updateMask: Required. Specifies the fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  secret = _messages.MessageField('Secret', 2)
  updateMask = _messages.StringField(3)


class SecretmanagerProjectsLocationsSecretsSetIamPolicyRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class SecretmanagerProjectsLocationsSecretsTestIamPermissionsRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class SecretmanagerProjectsLocationsSecretsVersionsAccessRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsVersionsAccessRequest object.

  Fields:
    name: Required. The resource name of the SecretVersion in the format
      `projects/*/secrets/*/versions/*` or
      `projects/*/locations/*/secrets/*/versions/*`.
      `projects/*/secrets/*/versions/latest` or
      `projects/*/locations/*/secrets/*/versions/latest` is an alias to the
      most recently created SecretVersion.
  """

  name = _messages.StringField(1, required=True)


class SecretmanagerProjectsLocationsSecretsVersionsDestroyRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsVersionsDestroyRequest object.

  Fields:
    destroySecretVersionRequest: A DestroySecretVersionRequest resource to be
      passed as the request body.
    name: Required. The resource name of the SecretVersion to destroy in the
      format `projects/*/secrets/*/versions/*` or
      `projects/*/locations/*/secrets/*/versions/*`.
  """

  destroySecretVersionRequest = _messages.MessageField('DestroySecretVersionRequest', 1)
  name = _messages.StringField(2, required=True)


class SecretmanagerProjectsLocationsSecretsVersionsDisableRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsVersionsDisableRequest object.

  Fields:
    disableSecretVersionRequest: A DisableSecretVersionRequest resource to be
      passed as the request body.
    name: Required. The resource name of the SecretVersion to disable in the
      format `projects/*/secrets/*/versions/*` or
      `projects/*/locations/*/secrets/*/versions/*`.
  """

  disableSecretVersionRequest = _messages.MessageField('DisableSecretVersionRequest', 1)
  name = _messages.StringField(2, required=True)


class SecretmanagerProjectsLocationsSecretsVersionsEnableRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsVersionsEnableRequest object.

  Fields:
    enableSecretVersionRequest: A EnableSecretVersionRequest resource to be
      passed as the request body.
    name: Required. The resource name of the SecretVersion to enable in the
      format `projects/*/secrets/*/versions/*` or
      `projects/*/locations/*/secrets/*/versions/*`.
  """

  enableSecretVersionRequest = _messages.MessageField('EnableSecretVersionRequest', 1)
  name = _messages.StringField(2, required=True)


class SecretmanagerProjectsLocationsSecretsVersionsGetRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsVersionsGetRequest object.

  Fields:
    name: Required. The resource name of the SecretVersion in the format
      `projects/*/secrets/*/versions/*` or
      `projects/*/locations/*/secrets/*/versions/*`.
      `projects/*/secrets/*/versions/latest` or
      `projects/*/locations/*/secrets/*/versions/latest` is an alias to the
      most recently created SecretVersion.
  """

  name = _messages.StringField(1, required=True)


class SecretmanagerProjectsLocationsSecretsVersionsListRequest(_messages.Message):
  r"""A SecretmanagerProjectsLocationsSecretsVersionsListRequest object.

  Fields:
    filter: Optional. Filter string, adhering to the rules in [List-operation
      filtering](https://cloud.google.com/secret-manager/docs/filtering). List
      only secret versions matching the filter. If filter is empty, all secret
      versions are listed.
    pageSize: Optional. The maximum number of results to be returned in a
      single page. If set to 0, the server decides the number of results to
      return. If the number is greater than 25000, it is capped at 25000.
    pageToken: Optional. Pagination token, returned earlier via
      ListSecretVersionsResponse.next_page_token][].
    parent: Required. The resource name of the Secret associated with the
      SecretVersions to list, in the format `projects/*/secrets/*` or
      `projects/*/locations/*/secrets/*`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SecretmanagerProjectsSecretsAddVersionRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsAddVersionRequest object.

  Fields:
    addSecretVersionRequest: A AddSecretVersionRequest resource to be passed
      as the request body.
    parent: Required. The resource name of the Secret to associate with the
      SecretVersion in the format `projects/*/secrets/*` or
      `projects/*/locations/*/secrets/*`.
  """

  addSecretVersionRequest = _messages.MessageField('AddSecretVersionRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecretmanagerProjectsSecretsCreateRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsCreateRequest object.

  Fields:
    parent: Required. The resource name of the project to associate with the
      Secret, in the format `projects/*` or `projects/*/locations/*`.
    secret: A Secret resource to be passed as the request body.
    secretId: Required. This must be unique within the project. A secret ID is
      a string with a maximum length of 255 characters and can contain
      uppercase and lowercase letters, numerals, and the hyphen (`-`) and
      underscore (`_`) characters.
  """

  parent = _messages.StringField(1, required=True)
  secret = _messages.MessageField('Secret', 2)
  secretId = _messages.StringField(3)


class SecretmanagerProjectsSecretsDeleteRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsDeleteRequest object.

  Fields:
    etag: Optional. Etag of the Secret. The request succeeds if it matches the
      etag of the currently stored secret object. If the etag is omitted, the
      request succeeds.
    name: Required. The resource name of the Secret to delete in the format
      `projects/*/secrets/*`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class SecretmanagerProjectsSecretsGetIamPolicyRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class SecretmanagerProjectsSecretsGetRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsGetRequest object.

  Fields:
    name: Required. The resource name of the Secret, in the format
      `projects/*/secrets/*` or `projects/*/locations/*/secrets/*`.
  """

  name = _messages.StringField(1, required=True)


class SecretmanagerProjectsSecretsListRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsListRequest object.

  Fields:
    filter: Optional. Filter string, adhering to the rules in [List-operation
      filtering](https://cloud.google.com/secret-manager/docs/filtering). List
      only secrets matching the filter. If filter is empty, all secrets are
      listed.
    pageSize: Optional. The maximum number of results to be returned in a
      single page. If set to 0, the server decides the number of results to
      return. If the number is greater than 25000, it is capped at 25000.
    pageToken: Optional. Pagination token, returned earlier via
      ListSecretsResponse.next_page_token.
    parent: Required. The resource name of the project associated with the
      Secrets, in the format `projects/*` or `projects/*/locations/*`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SecretmanagerProjectsSecretsPatchRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsPatchRequest object.

  Fields:
    name: Output only. The resource name of the Secret in the format
      `projects/*/secrets/*`.
    secret: A Secret resource to be passed as the request body.
    updateMask: Required. Specifies the fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  secret = _messages.MessageField('Secret', 2)
  updateMask = _messages.StringField(3)


class SecretmanagerProjectsSecretsSetIamPolicyRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class SecretmanagerProjectsSecretsTestIamPermissionsRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class SecretmanagerProjectsSecretsVersionsAccessRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsVersionsAccessRequest object.

  Fields:
    name: Required. The resource name of the SecretVersion in the format
      `projects/*/secrets/*/versions/*` or
      `projects/*/locations/*/secrets/*/versions/*`.
      `projects/*/secrets/*/versions/latest` or
      `projects/*/locations/*/secrets/*/versions/latest` is an alias to the
      most recently created SecretVersion.
  """

  name = _messages.StringField(1, required=True)


class SecretmanagerProjectsSecretsVersionsDestroyRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsVersionsDestroyRequest object.

  Fields:
    destroySecretVersionRequest: A DestroySecretVersionRequest resource to be
      passed as the request body.
    name: Required. The resource name of the SecretVersion to destroy in the
      format `projects/*/secrets/*/versions/*` or
      `projects/*/locations/*/secrets/*/versions/*`.
  """

  destroySecretVersionRequest = _messages.MessageField('DestroySecretVersionRequest', 1)
  name = _messages.StringField(2, required=True)


class SecretmanagerProjectsSecretsVersionsDisableRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsVersionsDisableRequest object.

  Fields:
    disableSecretVersionRequest: A DisableSecretVersionRequest resource to be
      passed as the request body.
    name: Required. The resource name of the SecretVersion to disable in the
      format `projects/*/secrets/*/versions/*` or
      `projects/*/locations/*/secrets/*/versions/*`.
  """

  disableSecretVersionRequest = _messages.MessageField('DisableSecretVersionRequest', 1)
  name = _messages.StringField(2, required=True)


class SecretmanagerProjectsSecretsVersionsEnableRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsVersionsEnableRequest object.

  Fields:
    enableSecretVersionRequest: A EnableSecretVersionRequest resource to be
      passed as the request body.
    name: Required. The resource name of the SecretVersion to enable in the
      format `projects/*/secrets/*/versions/*` or
      `projects/*/locations/*/secrets/*/versions/*`.
  """

  enableSecretVersionRequest = _messages.MessageField('EnableSecretVersionRequest', 1)
  name = _messages.StringField(2, required=True)


class SecretmanagerProjectsSecretsVersionsGetRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsVersionsGetRequest object.

  Fields:
    name: Required. The resource name of the SecretVersion in the format
      `projects/*/secrets/*/versions/*` or
      `projects/*/locations/*/secrets/*/versions/*`.
      `projects/*/secrets/*/versions/latest` or
      `projects/*/locations/*/secrets/*/versions/latest` is an alias to the
      most recently created SecretVersion.
  """

  name = _messages.StringField(1, required=True)


class SecretmanagerProjectsSecretsVersionsListRequest(_messages.Message):
  r"""A SecretmanagerProjectsSecretsVersionsListRequest object.

  Fields:
    filter: Optional. Filter string, adhering to the rules in [List-operation
      filtering](https://cloud.google.com/secret-manager/docs/filtering). List
      only secret versions matching the filter. If filter is empty, all secret
      versions are listed.
    pageSize: Optional. The maximum number of results to be returned in a
      single page. If set to 0, the server decides the number of results to
      return. If the number is greater than 25000, it is capped at 25000.
    pageToken: Optional. Pagination token, returned earlier via
      ListSecretVersionsResponse.next_page_token][].
    parent: Required. The resource name of the Secret associated with the
      SecretVersions to list, in the format `projects/*/secrets/*` or
      `projects/*/locations/*/secrets/*`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class Topic(_messages.Message):
  r"""A Pub/Sub topic which Secret Manager will publish to when control plane
  events occur on this secret.

  Fields:
    name: Required. The resource name of the Pub/Sub topic that will be
      published to, in the following format: `projects/*/topics/*`. For
      publication to succeed, the Secret Manager service agent must have the
      `pubsub.topic.publish` permission on the topic. The Pub/Sub Publisher
      role (`roles/pubsub.publisher`) includes this permission.
  """

  name = _messages.StringField(1)


class UserManaged(_messages.Message):
  r"""A replication policy that replicates the Secret payload into the
  locations specified in Replication.UserManaged.replicas

  Fields:
    replicas: Required. The list of Replicas for this Secret. Cannot be empty.
  """

  replicas = _messages.MessageField('Replica', 1, repeated=True)


class UserManagedStatus(_messages.Message):
  r"""The replication status of a SecretVersion using user-managed
  replication. Only populated if the parent Secret has a user-managed
  replication policy.

  Fields:
    replicas: Output only. The list of replica statuses for the SecretVersion.
  """

  replicas = _messages.MessageField('ReplicaStatus', 1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    SecretmanagerProjectsLocationsSecretsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    SecretmanagerProjectsSecretsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
