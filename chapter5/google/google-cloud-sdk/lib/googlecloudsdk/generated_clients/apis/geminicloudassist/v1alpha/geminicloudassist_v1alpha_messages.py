"""Generated message classes for geminicloudassist version v1alpha.

The AI-powered assistant for Google Cloud.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'geminicloudassist'


class AbsentObservation(_messages.Message):
  r"""An identifier of an observation that is needed. Typically a parameter
  but extensible to other cases.

  Fields:
    generalMissingObservation: Optional. A missing observation that the user
      could supply that is not a runbook parameter.
    param: Optional. A runbook parameter.
    pendingObservation: Optional. An observation that is not yet created that
      an observer should create by running. This might prompt the system to
      execute a runbook.
  """

  generalMissingObservation = _messages.MessageField('GeneralAbsentObservation', 1)
  param = _messages.StringField(2)
  pendingObservation = _messages.StringField(3)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ClarificationNeeded(_messages.Message):
  r"""A clarification needed by the system.

  Fields:
    generalMissingObservation: Optional. A missing observation that is not a
      runbook parameter.
    parentObserverNames: Optional. The display names of the observers that
      asked for this clarification. The UI will group by these names.
    runbookParameter: Optional. The result of a clarification is an
      observation. A runbook parameter that the user needs to provide.
  """

  generalMissingObservation = _messages.MessageField('GeneralAbsentObservation', 1)
  parentObserverNames = _messages.StringField(2, repeated=True)
  runbookParameter = _messages.MessageField('RunbookParameter', 3)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GeminicloudassistProjectsLocationsGetRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class GeminicloudassistProjectsLocationsInvestigationsCreateRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsInvestigationsCreateRequest object.

  Fields:
    investigation: A Investigation resource to be passed as the request body.
    investigationId: Optional. ID to use for the investigation, which will
      become the final component of the investigation's resource name. This
      value should be 4-63 characters, and valid characters are /a-z-/. If not
      provided, the server will generate a unique ID for the investigation.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  investigation = _messages.MessageField('Investigation', 1)
  investigationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class GeminicloudassistProjectsLocationsInvestigationsDeleteRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsInvestigationsDeleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class GeminicloudassistProjectsLocationsInvestigationsGetIamPolicyRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsInvestigationsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GeminicloudassistProjectsLocationsInvestigationsGetRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsInvestigationsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class GeminicloudassistProjectsLocationsInvestigationsListRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsInvestigationsListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListInvestigationsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class GeminicloudassistProjectsLocationsInvestigationsPatchRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsInvestigationsPatchRequest object.

  Fields:
    investigation: A Investigation resource to be passed as the request body.
    name: Identifier. name of resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Investigation resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields present in the request will be
      overwritten.
  """

  investigation = _messages.MessageField('Investigation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class GeminicloudassistProjectsLocationsInvestigationsRevisionsCreateRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsInvestigationsRevisionsCreateRequest
  object.

  Fields:
    investigationRevision: A InvestigationRevision resource to be passed as
      the request body.
    investigationRevisionId: Optional. ID to use for the revision, which will
      become the final component of the revision's resource name. If not
      provided, the server will generate a unique ID for the revision.
    parent: Required. Parent investigation
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  investigationRevision = _messages.MessageField('InvestigationRevision', 1)
  investigationRevisionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class GeminicloudassistProjectsLocationsInvestigationsRevisionsDeleteRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsInvestigationsRevisionsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class GeminicloudassistProjectsLocationsInvestigationsRevisionsGetRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsInvestigationsRevisionsGetRequest
  object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class GeminicloudassistProjectsLocationsInvestigationsRevisionsListRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsInvestigationsRevisionsListRequest
  object.

  Fields:
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListInvestigationRevisionsRequest
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GeminicloudassistProjectsLocationsInvestigationsRevisionsPatchRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsInvestigationsRevisionsPatchRequest
  object.

  Fields:
    investigationRevision: A InvestigationRevision resource to be passed as
      the request body.
    name: Identifier. name of resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Investigation resource by the update. The fields
      specified in the update_mask are relative to the revision's snapshot,
      not the full request. A field will be overwritten if it is in the mask.
      If the user does not provide a mask then all fields present in the
      request will be overwritten.
  """

  investigationRevision = _messages.MessageField('InvestigationRevision', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class GeminicloudassistProjectsLocationsInvestigationsRevisionsRunRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsInvestigationsRevisionsRunRequest
  object.

  Fields:
    name: Required. Run the investigation revision. The revision to run,
      format: projects/{project}/locations/global/investigations/{investigatio
      n}/revisions/{revision}
    runInvestigationRevisionRequest: A RunInvestigationRevisionRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  runInvestigationRevisionRequest = _messages.MessageField('RunInvestigationRevisionRequest', 2)


class GeminicloudassistProjectsLocationsInvestigationsSetIamPolicyRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsInvestigationsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GeminicloudassistProjectsLocationsInvestigationsTestIamPermissionsRequest(_messages.Message):
  r"""A
  GeminicloudassistProjectsLocationsInvestigationsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GeminicloudassistProjectsLocationsListRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class GeminicloudassistProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class GeminicloudassistProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class GeminicloudassistProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GeminicloudassistProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A GeminicloudassistProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GeneralAbsentObservation(_messages.Message):
  r"""An absent observation that is not a runbook parameter.

  Fields:
    id: Optional. The ID of the missing observation.
    title: Optional. The title to display in the UI
    validationRegex: Optional. The regex that the answer must match. Must
      conform to JavaScript's regular expression string pattern syntax. See
      https://developer.mozilla.org/en-
      US/docs/Web/JavaScript/Reference/Global_Objects/RegExp/RegExp#syntax
  """

  id = _messages.StringField(1)
  title = _messages.StringField(2)
  validationRegex = _messages.StringField(3)


class Interval(_messages.Message):
  r"""Represents a time interval, encoded as a Timestamp start (inclusive) and
  a Timestamp end (exclusive). The start must be less than or equal to the
  end. When the start equals the end, the interval is empty (matches no time).
  When both start and end are unspecified, the interval matches any time.

  Fields:
    endTime: Optional. Exclusive end of the interval. If specified, a
      Timestamp matching this interval will have to be before the end.
    startTime: Optional. Inclusive start of the interval. If specified, a
      Timestamp matching this interval will have to be the same or after the
      start.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class Investigation(_messages.Message):
  r"""Message describing Investigation object Next Id: 24

  Enums:
    ExecutionStateValueValuesEnum: Output only. [Output only] The execution
      state of this investigation.

  Messages:
    ClarificationsNeededValue: Optional. Questions that the system needs to
      ask the user. The results will be passed back by the UI as new
      Observations. The ID for those observations will be the key of the entry
      in the clarifications_needed map.
    LabelsValue: Optional. Labels as key value pairs
    ObservationsValue: Optional. A map from observation ID to the observation.
      This is a map so that we can cleanly overwrite old observations with the
      version from the latest revision. See Observation for guidance on
      choosing IDs.
    ObserverStatusesValue: Optional. Plural version of above. Code will
      transition to this over time.

  Fields:
    annotations: Optional. Annotations on the investigation. Unlike labels,
      these may carry semantic meaning in running the investigation, and will
      not be read by other systems such as billing.
    clarificationsNeeded: Optional. Questions that the system needs to ask the
      user. The results will be passed back by the UI as new Observations. The
      ID for those observations will be the key of the entry in the
      clarifications_needed map.
    createTime: Output only. [Output only] Create time stamp
    dataVersion: Optional. The data model version of this Investigation.
      Should be either 1 or 2. Treat 0 as 1. If 1, use V1 Investigation data
      model. If 2, use the Investigation Observation data model.
    error: Output only. [Output only] If the investigation execution state is
      FAILED, this field will contain the error message.
    executionState: Output only. [Output only] The execution state of this
      investigation.
    labels: Optional. Labels as key value pairs
    name: Identifier. name of resource
    observations: Optional. A map from observation ID to the observation. This
      is a map so that we can cleanly overwrite old observations with the
      version from the latest revision. See Observation for guidance on
      choosing IDs.
    observerStatuses: Optional. Plural version of above. Code will transition
      to this over time.
    operation: Output only. The Run operation most recently performed on the
      investigation.
    revision: Output only. [Output only] Current revision of the investigation
    revisionIndex: Output only. [Output only] Index of the current revision of
      the investigation. 1-based.
    revisionPredecessor: Optional. The name of the revision that was this
      revision's predecessor. The UI, for example, will set this to the
      existing revision when when a new revision is created due to an edit.
    title: Required. Human-readable display title for the investigation.
    updateTime: Output only. [Output only] Update time stamp
  """

  class ExecutionStateValueValuesEnum(_messages.Enum):
    r"""Output only. [Output only] The execution state of this investigation.

    Values:
      INVESTIGATION_EXECUTION_STATE_UNSPECIFIED: Default value. This value is
        unused.
      INVESTIGATION_EXECUTION_STATE_RUNNING: The investigation is being
        executed.
      INVESTIGATION_EXECUTION_STATE_MODIFIED: The investigation has not yet
        been executed since the symptom was last updated.
      INVESTIGATION_EXECUTION_STATE_FAILED: The investigation execution has
        completed, but the execution has failed.
      INVESTIGATION_EXECUTION_STATE_COMPLETED: All execution tasks have
        completed and the investigation is at rest.
    """
    INVESTIGATION_EXECUTION_STATE_UNSPECIFIED = 0
    INVESTIGATION_EXECUTION_STATE_RUNNING = 1
    INVESTIGATION_EXECUTION_STATE_MODIFIED = 2
    INVESTIGATION_EXECUTION_STATE_FAILED = 3
    INVESTIGATION_EXECUTION_STATE_COMPLETED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ClarificationsNeededValue(_messages.Message):
    r"""Optional. Questions that the system needs to ask the user. The results
    will be passed back by the UI as new Observations. The ID for those
    observations will be the key of the entry in the clarifications_needed
    map.

    Messages:
      AdditionalProperty: An additional property for a
        ClarificationsNeededValue object.

    Fields:
      additionalProperties: Additional properties of type
        ClarificationsNeededValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ClarificationsNeededValue object.

      Fields:
        key: Name of the additional property.
        value: A ClarificationNeeded attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ClarificationNeeded', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ObservationsValue(_messages.Message):
    r"""Optional. A map from observation ID to the observation. This is a map
    so that we can cleanly overwrite old observations with the version from
    the latest revision. See Observation for guidance on choosing IDs.

    Messages:
      AdditionalProperty: An additional property for a ObservationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ObservationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ObservationsValue object.

      Fields:
        key: Name of the additional property.
        value: A Observation attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Observation', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ObserverStatusesValue(_messages.Message):
    r"""Optional. Plural version of above. Code will transition to this over
    time.

    Messages:
      AdditionalProperty: An additional property for a ObserverStatusesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ObserverStatusesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ObserverStatusesValue object.

      Fields:
        key: Name of the additional property.
        value: A ObserverStatus attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ObserverStatus', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('InvestigationAnnotations', 1)
  clarificationsNeeded = _messages.MessageField('ClarificationsNeededValue', 2)
  createTime = _messages.StringField(3)
  dataVersion = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  error = _messages.MessageField('Status', 5)
  executionState = _messages.EnumField('ExecutionStateValueValuesEnum', 6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  observations = _messages.MessageField('ObservationsValue', 9)
  observerStatuses = _messages.MessageField('ObserverStatusesValue', 10)
  operation = _messages.StringField(11)
  revision = _messages.StringField(12)
  revisionIndex = _messages.IntegerField(13, variant=_messages.Variant.INT32)
  revisionPredecessor = _messages.StringField(14)
  title = _messages.StringField(15)
  updateTime = _messages.StringField(16)


class InvestigationAnnotations(_messages.Message):
  r"""Additional user-defined annotations on an Investigation. There are some
  pre-defined ones, and a map for new applications to add their own.

  Messages:
    ComponentVersionsValue: Output only. Map of component key to version.
      Filled in by the run process. The key is unique to a "component",
      broadly defined. A component might be the TAF framework, Titan, a GCA
      tool, etc. The version is a string that is unique to a particular
      release of the component, e.g., a build label.
    ExtrasMapValue: Optional. Additional annotations required by applications.
      These will not be redacted and should NOT contain any CCC/PII.
    FeatureFlagsValue: Output only. Map of feature flag names to their
      (string-serialized) values. Filled in by, and at the very start of, the
      run process. Treat as immutable for the subsequent life of an
      investigation run.

  Fields:
    componentVersions: Output only. Map of component key to version. Filled in
      by the run process. The key is unique to a "component", broadly defined.
      A component might be the TAF framework, Titan, a GCA tool, etc. The
      version is a string that is unique to a particular release of the
      component, e.g., a build label.
    extrasMap: Optional. Additional annotations required by applications.
      These will not be redacted and should NOT contain any CCC/PII.
    featureFlags: Output only. Map of feature flag names to their (string-
      serialized) values. Filled in by, and at the very start of, the run
      process. Treat as immutable for the subsequent life of an investigation
      run.
    followUp: Output only. Follow-up is required to continue the
      investigation. Generally set to true by the troubleshooter and false
      when the questions have been answered.
    newlyCreated: Optional. This investigation is been newly created and
      hasn't been saved by the user yet. Set to true when an Investigation is
      created by an application (like Chat) and false when the user requests
      action via the UI.
    pagePath: Optional. Page path field set by the UI.
    revisionLastRunInterval: Output only. Start/end time when the revision was
      last run.
    supportCase: Optional. The support case ID associated with the
      investigation.
    uiHidden: Optional. Whether the UI should hide this investigation from its
      list.
    uiReadOnly: Optional. Whether the UI should disable editing of this
      investigation.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ComponentVersionsValue(_messages.Message):
    r"""Output only. Map of component key to version. Filled in by the run
    process. The key is unique to a "component", broadly defined. A component
    might be the TAF framework, Titan, a GCA tool, etc. The version is a
    string that is unique to a particular release of the component, e.g., a
    build label.

    Messages:
      AdditionalProperty: An additional property for a ComponentVersionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ComponentVersionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ComponentVersionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtrasMapValue(_messages.Message):
    r"""Optional. Additional annotations required by applications. These will
    not be redacted and should NOT contain any CCC/PII.

    Messages:
      AdditionalProperty: An additional property for a ExtrasMapValue object.

    Fields:
      additionalProperties: Additional properties of type ExtrasMapValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtrasMapValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FeatureFlagsValue(_messages.Message):
    r"""Output only. Map of feature flag names to their (string-serialized)
    values. Filled in by, and at the very start of, the run process. Treat as
    immutable for the subsequent life of an investigation run.

    Messages:
      AdditionalProperty: An additional property for a FeatureFlagsValue
        object.

    Fields:
      additionalProperties: Additional properties of type FeatureFlagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FeatureFlagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  componentVersions = _messages.MessageField('ComponentVersionsValue', 1)
  extrasMap = _messages.MessageField('ExtrasMapValue', 2)
  featureFlags = _messages.MessageField('FeatureFlagsValue', 3)
  followUp = _messages.BooleanField(4)
  newlyCreated = _messages.BooleanField(5)
  pagePath = _messages.StringField(6)
  revisionLastRunInterval = _messages.MessageField('Interval', 7)
  supportCase = _messages.StringField(8)
  uiHidden = _messages.BooleanField(9)
  uiReadOnly = _messages.BooleanField(10)


class InvestigationRevision(_messages.Message):
  r"""Message describing a revision of an Investigation

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    createTime: Output only. [Output only] Create time stamp
    index: Output only. Revision index number, in order of creation.
    labels: Optional. Labels as key value pairs
    name: Identifier. name of resource
    snapshot: Optional. [Output only] Snapshot of the investigation contents
      at this revision
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  index = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  snapshot = _messages.MessageField('Investigation', 5)


class InvestigationRunParameters(_messages.Message):
  r"""Represents user parameters for running an investigation.

  Messages:
    AccessTokensValue: Optional. If populated, map of project to access token
      for TSE-triggered investigations.

  Fields:
    accessTokens: Optional. If populated, map of project to access token for
      TSE-triggered investigations.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AccessTokensValue(_messages.Message):
    r"""Optional. If populated, map of project to access token for TSE-
    triggered investigations.

    Messages:
      AdditionalProperty: An additional property for a AccessTokensValue
        object.

    Fields:
      additionalProperties: Additional properties of type AccessTokensValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AccessTokensValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accessTokens = _messages.MessageField('AccessTokensValue', 1)


class ListInvestigationRevisionsResponse(_messages.Message):
  r"""Message for response to listing revisions of a given Investigation

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    revisions: The list of Investigation revisions
    unreachable: Unordered list. Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  revisions = _messages.MessageField('InvestigationRevision', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListInvestigationsResponse(_messages.Message):
  r"""Message for response to listing Investigations

  Fields:
    investigations: The list of Investigation
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Unordered list. Locations that could not be reached.
  """

  investigations = _messages.MessageField('Investigation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Observation(_messages.Message):
  r"""An observation is the basic unit of interchange between user and system,
  or between different components of the system. It is the element that has a
  relevance. They should therefore be relatively small; if you expect users to
  react to "part" of an observation, it should be broken up into smaller
  observations. A particular runbook run, a particular user parameter input, a
  particular interesting log entry might all be separate observations. This
  means there might be dozens or hundreds in an investigation. Next Id: 26

  Enums:
    ObservationCompletionStateValueValuesEnum: Optional. An
      ObservationCompletionState represents whether the emitted observation is
      fully formed and should be shown the to the user. This is intended to
      allow hiding observations that are in an intermediate state.
    ObservationTypeValueValuesEnum: Required. The type of the observation
      (e.g. log, metric, etc.)
    ObserverTypeValueValuesEnum: Required. The origin of the data, e.g. user,
      system code, LLM etc.
    RelevanceOverrideValueValuesEnum: Optional. The user's relevance
      judgement.

  Messages:
    DataValue: Optional. A structured representation of the observation, as
      chosen by the observer. Optional. If present, an observer SHOULD also
      supply a text description of the observation to facilitate processing by
      an LLM and rendering in the UI.
    DataUrlsValue: Optional. A map from human-readable names to URLs for
      supportive evidence. The map key will be rendered as URL anchor text.
      Fill this in whenever an observation depends on a thing outside the
      system. For example, logging/metrics/etc query that can regenerate the
      observation.
    KnowledgeUrlsValue: Optional. A map from human-readable names to URLs for
      documentation.

  Fields:
    baseObservations: Optional. The ids of other observations that this
      observation is based on. For example, a conclusion observation will
      record the observations that were used to generate it. An extracted
      param will record what it was extracted from. The graph of premises and
      conclusions will be acyclic within a revision.
    data: Optional. A structured representation of the observation, as chosen
      by the observer. Optional. If present, an observer SHOULD also supply a
      text description of the observation to facilitate processing by an LLM
      and rendering in the UI.
    dataUrls: Optional. A map from human-readable names to URLs for supportive
      evidence. The map key will be rendered as URL anchor text. Fill this in
      whenever an observation depends on a thing outside the system. For
      example, logging/metrics/etc query that can regenerate the observation.
    id: Required. Uniquely identifies this observation. Should depend on the
      'core content' of the observation, but not e.g. on the relevance. Should
      not depend on anything that can vary unpredictably from revision to
      revision to run. This is also the map key in the parent Investigation.
      They should be hierarchical with '.' as the separator starting with the
      name of the observer. So for instance, diagnostics.runbook.ABC, or
      signals.logs, or user.input.2 It should be usable as a URL component.
      (Case-insensitive [a-z0-9-._]+) These will not be rendered for users,
      but will be visible in the data model. They will be used by Google
      engineers to localize bugs so should be semi-readable.
    knowledgeUrls: Optional. A map from human-readable names to URLs for
      documentation.
    observationCompletionState: Optional. An ObservationCompletionState
      represents whether the emitted observation is fully formed and should be
      shown the to the user. This is intended to allow hiding observations
      that are in an intermediate state.
    observationType: Required. The type of the observation (e.g. log, metric,
      etc.)
    observedNormalOperation: Optional. Whether this observation gives us
      information about an issue / root cause (false) or indicates normal
      operation (true). This is conceptually different from the relevance and
      used differently. An irrelevant observation should be hidden from the
      LLM and also the user. A relevant observation of a problem should be
      shown as an observation and should motivate a hypothesis. A relevant
      finding of normalcy may / may not be shown in the UI, but should be used
      by the LLM to filter out hypotheses that are refuted by the finding.
    observerErrors: Output only. An error within the Investigation system that
      blocked an observer from making a particular observation. The error
      string here will be shown to users. Repeated because an observer might
      lack multiple permissions. Deprecated: Use
      ObserverStatus.observer_errors instead.
    observerType: Required. The origin of the data, e.g. user, system code,
      LLM etc.
    recommendation: Optional. Natural language [markdown] text which describes
      a recommended action to remediate / fix the root cause. This is free
      form and not machine- processed at this time. A recommendation can be: -
      High-level remediation descriptions - Specific and tactical remediation
      steps with executable commands - Specific and tactical troubleshooting
      steps for where to investigate next with executable commands
    relevanceOverride: Optional. The user's relevance judgement.
    relevantResources: Optional. The Google Cloud resources relevant to the
      observation. These should be fully qualified resource URIs, e.g.,
      "//compute.googleapis.com/projects/my-project/zones/us-
      central1-a/instances/my-instance"
    systemRelevanceScore: Optional. How relevant this observation is to the
      investigation, as inferred by the system. Optional. Should be in the
      range [-1, 1]. For OBSERVATION_TYPE_HYPOTHESIS, represents confidence in
      the explanation. Only root-cause hypotheses are ranked against each
      other. For other ObservationTypes, this represents a relevance score,
      and they are ranked against each other. A value of 0 is neutral.
    text: Optional. Natural-language [markdown] text associated with the
      observation. This is the core content, not a metadata description.
    timeIntervals: Optional. When this observation occurred. Observations
      should have at least one time range so that the observations can be
      shown on a timeline and so we can find related events. For a repeated
      but not continuous event, it is appropriate to have more than one range.
      The UI may combine these.
    timeRanges: Optional. When this observation occurred. Observations should
      have at least one time range so that the observations can be shown on a
      timeline and so we can find related events. For a repeated but not
      continuous event, it is appropriate to have more than one range. The UI
      may combine these. DEPRECATED: Use time_intervals instead.
    title: Optional. The label shown in the UI. This need not be unique within
      an investigation. However, it should be specific and less than 80
      characters so that the user can easily scan across many observations.
      "Nettools pod configured with ALL capabilities dropped" is much better
      than "Interesting pod configuration".
  """

  class ObservationCompletionStateValueValuesEnum(_messages.Enum):
    r"""Optional. An ObservationCompletionState represents whether the emitted
    observation is fully formed and should be shown the to the user. This is
    intended to allow hiding observations that are in an intermediate state.

    Values:
      OBSERVATION_COMPLETION_STATE_UNSPECIFIED: Do not use.
      OBSERVATION_COMPLETION_STATE_COMPLETE: This observation is fully formed
        and should be shown the to the user.
      OBSERVATION_COMPLETION_STATE_INCOMPLETE: This observation is missing
        some information, or needs further processing by a different observer.
        This type of Observation should not be persisted into future
        investigation revisions.
    """
    OBSERVATION_COMPLETION_STATE_UNSPECIFIED = 0
    OBSERVATION_COMPLETION_STATE_COMPLETE = 1
    OBSERVATION_COMPLETION_STATE_INCOMPLETE = 2

  class ObservationTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of the observation (e.g. log, metric, etc.)

    Values:
      OBSERVATION_TYPE_UNSPECIFIED: Do not use. Specify the type of the
        observation. Add a new enum if you need it.
      OBSERVATION_TYPE_CLOUD_LOG: The text of this observation is a log entry.
      OBSERVATION_TYPE_CLOUD_METRIC: The content of this observation is a
        metric or group of metrics.
      OBSERVATION_TYPE_CAIS_CONFIG: A config
      OBSERVATION_TYPE_CAIS_CONFIG_DIFF: A change of config
      OBSERVATION_TYPE_CLOUD_ALERT: An alert
      OBSERVATION_TYPE_CICD_EVENT: An event from a continuous integration
        system, e.g. prober failure.
      OBSERVATION_TYPE_TEXT_DESCRIPTION: Free text input, such as the initial
        user input. Can be markdown.
      OBSERVATION_TYPE_HYPOTHESIS: This is for [tentative] conclusions drawn
        by the system. These can become inputs to a subsequent revision. In
        this case the recommendations will be used, but not the remediation.
      OBSERVATION_TYPE_STRUCTURED_INPUT: Structured input, e.g. runbook
        parameters put into a form
      OBSERVATION_TYPE_COMPOSITE: Used for an observation that includes more
        than one kind of evidence, such as a runbook output.
      OBSERVATION_TYPE_OTHER: Runbook output can be 'other' if none of the
        other types apply.
      OBSERVATION_TYPE_RELATED_RESOURCES: Resources we think are related to
        the investigation.
      OBSERVATION_TYPE_LOG_THEME: A theme discovered in the logs.
      OBSERVATION_TYPE_CONFIG_ANALYSIS: Signals output that includes a config
        analyzed by LLM.
      OBSERVATION_TYPE_OUTAGE: Signals output that includes an outage from
        PSH.
      OBSERVATION_TYPE_KNOWLEDGE: Text that provides knowledge about a
        particular user's problem. For example, error catalog
        instructions/external links, RAG etc.
    """
    OBSERVATION_TYPE_UNSPECIFIED = 0
    OBSERVATION_TYPE_CLOUD_LOG = 1
    OBSERVATION_TYPE_CLOUD_METRIC = 2
    OBSERVATION_TYPE_CAIS_CONFIG = 3
    OBSERVATION_TYPE_CAIS_CONFIG_DIFF = 4
    OBSERVATION_TYPE_CLOUD_ALERT = 5
    OBSERVATION_TYPE_CICD_EVENT = 6
    OBSERVATION_TYPE_TEXT_DESCRIPTION = 7
    OBSERVATION_TYPE_HYPOTHESIS = 8
    OBSERVATION_TYPE_STRUCTURED_INPUT = 9
    OBSERVATION_TYPE_COMPOSITE = 10
    OBSERVATION_TYPE_OTHER = 11
    OBSERVATION_TYPE_RELATED_RESOURCES = 12
    OBSERVATION_TYPE_LOG_THEME = 13
    OBSERVATION_TYPE_CONFIG_ANALYSIS = 14
    OBSERVATION_TYPE_OUTAGE = 15
    OBSERVATION_TYPE_KNOWLEDGE = 16

  class ObserverTypeValueValuesEnum(_messages.Enum):
    r"""Required. The origin of the data, e.g. user, system code, LLM etc.

    Values:
      OBSERVER_TYPE_UNSPECIFIED: Do not use. Specify where the observation
        came from. Add a new enum if you need it.
      OBSERVER_TYPE_DIAGNOSTICS: We separate these for internal attribution
        reasons. Diagnostics have an explicit notion of root causes, e.g. via
        runbooks.
      OBSERVER_TYPE_SIGNALS: Signals is for processing that doesn't have
        explicit root causes.
      OBSERVER_TYPE_DETERMINISTIC_CODE: This is for code that depends only on
        premises. In particular, error catalog lookups.
      OBSERVER_TYPE_AI: This is for AI inferences made along the way that
        depend only on observations listed as premises.
      OBSERVER_TYPE_USER: User-input observation, including answers to
        clarifications.
      OBSERVER_TYPE_ALERT: An observation from an external-to-GCA alert.
    """
    OBSERVER_TYPE_UNSPECIFIED = 0
    OBSERVER_TYPE_DIAGNOSTICS = 1
    OBSERVER_TYPE_SIGNALS = 2
    OBSERVER_TYPE_DETERMINISTIC_CODE = 3
    OBSERVER_TYPE_AI = 4
    OBSERVER_TYPE_USER = 5
    OBSERVER_TYPE_ALERT = 6

  class RelevanceOverrideValueValuesEnum(_messages.Enum):
    r"""Optional. The user's relevance judgement.

    Values:
      USER_RELEVANCE_UNSPECIFIED: The user has not marked this observation as
        relevant or irrelevant.
      USER_RELEVANCE_PROMOTED: The user marked this observation as relevant.
      USER_RELEVANCE_REJECTED: The user marked this observation as irrelevant.
    """
    USER_RELEVANCE_UNSPECIFIED = 0
    USER_RELEVANCE_PROMOTED = 1
    USER_RELEVANCE_REJECTED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DataUrlsValue(_messages.Message):
    r"""Optional. A map from human-readable names to URLs for supportive
    evidence. The map key will be rendered as URL anchor text. Fill this in
    whenever an observation depends on a thing outside the system. For
    example, logging/metrics/etc query that can regenerate the observation.

    Messages:
      AdditionalProperty: An additional property for a DataUrlsValue object.

    Fields:
      additionalProperties: Additional properties of type DataUrlsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DataUrlsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DataValue(_messages.Message):
    r"""Optional. A structured representation of the observation, as chosen by
    the observer. Optional. If present, an observer SHOULD also supply a text
    description of the observation to facilitate processing by an LLM and
    rendering in the UI.

    Messages:
      AdditionalProperty: An additional property for a DataValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class KnowledgeUrlsValue(_messages.Message):
    r"""Optional. A map from human-readable names to URLs for documentation.

    Messages:
      AdditionalProperty: An additional property for a KnowledgeUrlsValue
        object.

    Fields:
      additionalProperties: Additional properties of type KnowledgeUrlsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a KnowledgeUrlsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  baseObservations = _messages.StringField(1, repeated=True)
  data = _messages.MessageField('DataValue', 2)
  dataUrls = _messages.MessageField('DataUrlsValue', 3)
  id = _messages.StringField(4)
  knowledgeUrls = _messages.MessageField('KnowledgeUrlsValue', 5)
  observationCompletionState = _messages.EnumField('ObservationCompletionStateValueValuesEnum', 6)
  observationType = _messages.EnumField('ObservationTypeValueValuesEnum', 7)
  observedNormalOperation = _messages.BooleanField(8)
  observerErrors = _messages.MessageField('Status', 9, repeated=True)
  observerType = _messages.EnumField('ObserverTypeValueValuesEnum', 10)
  recommendation = _messages.StringField(11)
  relevanceOverride = _messages.EnumField('RelevanceOverrideValueValuesEnum', 12)
  relevantResources = _messages.StringField(13, repeated=True)
  systemRelevanceScore = _messages.FloatField(14, variant=_messages.Variant.FLOAT)
  text = _messages.StringField(15)
  timeIntervals = _messages.MessageField('Interval', 16, repeated=True)
  timeRanges = _messages.MessageField('TimeRange', 17, repeated=True)
  title = _messages.StringField(18)


class ObserverStatus(_messages.Message):
  r"""An ObserverStatus represents the status of an observer at a particular
  point during execution of an investigation. NOTE: By default, nothing in
  this message is redacted. Components should NOT put PII / CCC here except
  where redacted. Next Id: 13

  Enums:
    ObserverExecutionStateValueValuesEnum: Optional. The current execution
      state of the observer.

  Fields:
    absentObservations: Optional. The IDs of any predicate observations that
      would be needed to run this observer, but are missing. Runbook
      parameters are the motivating example. An observer must not emit an ID
      corresponding to an existing observation.
    observer: Required. The ID of the observer that this status is for.
      Observer IDs should be human-readable and hierarchical, e.g.
      "signals.logs.firewall_rules" or "diagnostics.error_catalog".
    observerDisplayName: Required. The name to show the user when describing
      this observer. Note that the UI might replace this with an
      internationalized counterpart, so it should not be generated
      dynamically. Required so that the user can see which observer (e.g.
      runbook) the system is talking about.
    observerErrors: Optional. An error within the Investigation system that
      blocked an observer from making a particular observation. The error
      string here will be shown to users. Repeated because an observer might
      lack multiple permissions.
    observerExecutionState: Optional. The current execution state of the
      observer.
    startTime: Optional. The time when the observer started. Optional because
      the observer is responsible for setting it. When the observer is
      finished, the difference between this and update_time is the observer
      run time.
    updateComment: Optional. A status update from the observer. May be logged
      for debugging purposes. These may be shown to users. A good update would
      be "parameters matched, queued for execution" or "checked log file 2/5".
    updateTime: Optional. The time when the status was updated. Optional
      because the observer is responsible for setting it. Becomes end_time
      when the observer is complete.
  """

  class ObserverExecutionStateValueValuesEnum(_messages.Enum):
    r"""Optional. The current execution state of the observer.

    Values:
      OBSERVER_EXECUTION_UNSPECIFIED: The observer state is unspecified.
      OBSERVER_EXECUTION_NOT_STARTED: The investigation has not yet started.
      OBSERVER_EXECUTION_RUNNING: The investigation is running and this
        observer is runnable or running.
      OBSERVER_EXECUTION_COMPLETE: The observer has finished without an
        internal error.
      OBSERVER_EXECUTION_FAILED: The observer tried to run but failed due to
        an error. This is specific to a component and may be rendered in the
        UI if the observation is shown, as very low priority.
      OBSERVER_EXECUTION_BLOCKED: The observer is blocked pending an input.
      OBSERVER_EXECUTION_INVESTIGATION_BLOCKED: The observer reports an error
        that blocks or severely impacts the investigation, for example CAIS or
        logging disabled. Should be rendered in the UI prominently.
      OBSERVER_EXECUTION_INVESTIGATION_DEGRADED: The observer reports an error
        that degrades the investigation, may require user to escalate or re-
        run the investigation after mitigating the cause.
    """
    OBSERVER_EXECUTION_UNSPECIFIED = 0
    OBSERVER_EXECUTION_NOT_STARTED = 1
    OBSERVER_EXECUTION_RUNNING = 2
    OBSERVER_EXECUTION_COMPLETE = 3
    OBSERVER_EXECUTION_FAILED = 4
    OBSERVER_EXECUTION_BLOCKED = 5
    OBSERVER_EXECUTION_INVESTIGATION_BLOCKED = 6
    OBSERVER_EXECUTION_INVESTIGATION_DEGRADED = 7

  absentObservations = _messages.MessageField('AbsentObservation', 1, repeated=True)
  observer = _messages.StringField(2)
  observerDisplayName = _messages.StringField(3)
  observerErrors = _messages.MessageField('Status', 4, repeated=True)
  observerExecutionState = _messages.EnumField('ObserverExecutionStateValueValuesEnum', 5)
  startTime = _messages.StringField(6)
  updateComment = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class RunInvestigationRevisionRequest(_messages.Message):
  r"""Request for running an investigation at a particular revision.

  Fields:
    runParameters: Optional. Parameters to pass through to Titan.
    updateMask: Optional. The fields of the revision to update. Note that only
      user-writeable fields can be updated.
    updatedRevision: Optional. Revision to update before running.
  """

  runParameters = _messages.MessageField('InvestigationRunParameters', 1)
  updateMask = _messages.StringField(2)
  updatedRevision = _messages.MessageField('InvestigationRevision', 3)


class RunbookParameter(_messages.Message):
  r"""Parameter metadata for runbooks updated by the Diagnostic task.

  Fields:
    associatedAssetTypes: Optional. If present, a list of resource types that
      this parameter might be. For example, "compute.googleapis.com/Instance".
    description: Optional. The description of the parameter.
    displayName: Optional. The name of the parameter to be displayed to the
      user.
    exampleValues: Optional. Examples of the parameter value.
    id: Optional. The name of the parameter.
    value: Optional. The value of the parameter if available.
  """

  associatedAssetTypes = _messages.StringField(1, repeated=True)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  exampleValues = _messages.StringField(4, repeated=True)
  id = _messages.StringField(5)
  value = _messages.StringField(6)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TimeRange(_messages.Message):
  r"""Represents a time range at which an observation applies DEPRECATED: Use
  google.type.Interval instead.

  Fields:
    endTime: If end is unspecified, that implies that it continues to the
      present. Use end==start for a single moment.
    startTime: When this observation began applying. Use 'now' if the observer
      is just looking at the current state of Google Cloud.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    GeminicloudassistProjectsLocationsInvestigationsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
