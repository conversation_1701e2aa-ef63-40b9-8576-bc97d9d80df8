"""Generated client library for storage version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.storage.v1 import storage_v1_messages as messages


class StorageV1(base_api.BaseApiClient):
  """Generated client library for service storage version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://storage.googleapis.com/storage/v1/'
  MTLS_BASE_URL = 'https://storage.mtls.googleapis.com/storage/v1/'

  _PACKAGE = 'storage'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/cloud-platform.read-only', 'https://www.googleapis.com/auth/devstorage.full_control', 'https://www.googleapis.com/auth/devstorage.read_only', 'https://www.googleapis.com/auth/devstorage.read_write']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'StorageV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new storage handle."""
    url = url or self.BASE_URL
    super(StorageV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.anywhereCaches = self.AnywhereCachesService(self)
    self.bucketAccessControls = self.BucketAccessControlsService(self)
    self.buckets = self.BucketsService(self)
    self.channels = self.ChannelsService(self)
    self.defaultObjectAccessControls = self.DefaultObjectAccessControlsService(self)
    self.folders = self.FoldersService(self)
    self.managedFolders = self.ManagedFoldersService(self)
    self.notifications = self.NotificationsService(self)
    self.objectAccessControls = self.ObjectAccessControlsService(self)
    self.objects = self.ObjectsService(self)
    self.operations = self.OperationsService(self)
    self.projects_hmacKeys = self.ProjectsHmacKeysService(self)
    self.projects_serviceAccount = self.ProjectsServiceAccountService(self)
    self.projects = self.ProjectsService(self)

  class AnywhereCachesService(base_api.BaseApiService):
    """Service class for the anywhereCaches resource."""

    _NAME = 'anywhereCaches'

    def __init__(self, client):
      super(StorageV1.AnywhereCachesService, self).__init__(client)
      self._upload_configs = {
          }

    def Disable(self, request, global_params=None):
      r"""Disables an Anywhere Cache instance.

      Args:
        request: (StorageAnywhereCachesDisableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AnywhereCache) The response message.
      """
      config = self.GetMethodConfig('Disable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Disable.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.anywhereCaches.disable',
        ordered_params=['bucket', 'anywhereCacheId'],
        path_params=['anywhereCacheId', 'bucket'],
        query_params=[],
        relative_path='b/{bucket}/anywhereCaches/{anywhereCacheId}/disable',
        request_field='',
        request_type_name='StorageAnywhereCachesDisableRequest',
        response_type_name='AnywhereCache',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the metadata of an Anywhere Cache instance.

      Args:
        request: (StorageAnywhereCachesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AnywhereCache) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.anywhereCaches.get',
        ordered_params=['bucket', 'anywhereCacheId'],
        path_params=['anywhereCacheId', 'bucket'],
        query_params=[],
        relative_path='b/{bucket}/anywhereCaches/{anywhereCacheId}',
        request_field='',
        request_type_name='StorageAnywhereCachesGetRequest',
        response_type_name='AnywhereCache',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates an Anywhere Cache instance.

      Args:
        request: (AnywhereCache) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.anywhereCaches.insert',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=[],
        relative_path='b/{bucket}/anywhereCaches',
        request_field='<request>',
        request_type_name='AnywhereCache',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of Anywhere Cache instances of the bucket matching the criteria.

      Args:
        request: (StorageAnywhereCachesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AnywhereCaches) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.anywhereCaches.list',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['pageSize', 'pageToken'],
        relative_path='b/{bucket}/anywhereCaches',
        request_field='',
        request_type_name='StorageAnywhereCachesListRequest',
        response_type_name='AnywhereCaches',
        supports_download=False,
    )

    def Pause(self, request, global_params=None):
      r"""Pauses an Anywhere Cache instance.

      Args:
        request: (StorageAnywhereCachesPauseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AnywhereCache) The response message.
      """
      config = self.GetMethodConfig('Pause')
      return self._RunMethod(
          config, request, global_params=global_params)

    Pause.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.anywhereCaches.pause',
        ordered_params=['bucket', 'anywhereCacheId'],
        path_params=['anywhereCacheId', 'bucket'],
        query_params=[],
        relative_path='b/{bucket}/anywhereCaches/{anywhereCacheId}/pause',
        request_field='',
        request_type_name='StorageAnywhereCachesPauseRequest',
        response_type_name='AnywhereCache',
        supports_download=False,
    )

    def Resume(self, request, global_params=None):
      r"""Resumes a paused or disabled Anywhere Cache instance.

      Args:
        request: (StorageAnywhereCachesResumeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AnywhereCache) The response message.
      """
      config = self.GetMethodConfig('Resume')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resume.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.anywhereCaches.resume',
        ordered_params=['bucket', 'anywhereCacheId'],
        path_params=['anywhereCacheId', 'bucket'],
        query_params=[],
        relative_path='b/{bucket}/anywhereCaches/{anywhereCacheId}/resume',
        request_field='',
        request_type_name='StorageAnywhereCachesResumeRequest',
        response_type_name='AnywhereCache',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates the config(ttl and admissionPolicy) of an Anywhere Cache instance.

      Args:
        request: (AnywhereCache) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='storage.anywhereCaches.update',
        ordered_params=['bucket', 'anywhereCacheId'],
        path_params=['anywhereCacheId', 'bucket'],
        query_params=[],
        relative_path='b/{bucket}/anywhereCaches/{anywhereCacheId}',
        request_field='<request>',
        request_type_name='AnywhereCache',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class BucketAccessControlsService(base_api.BaseApiService):
    """Service class for the bucketAccessControls resource."""

    _NAME = 'bucketAccessControls'

    def __init__(self, client):
      super(StorageV1.BucketAccessControlsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Permanently deletes the ACL entry for the specified entity on the specified bucket.

      Args:
        request: (StorageBucketAccessControlsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (StorageBucketAccessControlsDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='storage.bucketAccessControls.delete',
        ordered_params=['bucket', 'entity'],
        path_params=['bucket', 'entity'],
        query_params=['userProject'],
        relative_path='b/{bucket}/acl/{entity}',
        request_field='',
        request_type_name='StorageBucketAccessControlsDeleteRequest',
        response_type_name='StorageBucketAccessControlsDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the ACL entry for the specified entity on the specified bucket.

      Args:
        request: (StorageBucketAccessControlsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BucketAccessControl) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.bucketAccessControls.get',
        ordered_params=['bucket', 'entity'],
        path_params=['bucket', 'entity'],
        query_params=['userProject'],
        relative_path='b/{bucket}/acl/{entity}',
        request_field='',
        request_type_name='StorageBucketAccessControlsGetRequest',
        response_type_name='BucketAccessControl',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates a new ACL entry on the specified bucket.

      Args:
        request: (StorageBucketAccessControlsInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BucketAccessControl) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.bucketAccessControls.insert',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['userProject'],
        relative_path='b/{bucket}/acl',
        request_field='bucketAccessControl',
        request_type_name='StorageBucketAccessControlsInsertRequest',
        response_type_name='BucketAccessControl',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieves ACL entries on the specified bucket.

      Args:
        request: (StorageBucketAccessControlsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BucketAccessControls) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.bucketAccessControls.list',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['userProject'],
        relative_path='b/{bucket}/acl',
        request_field='',
        request_type_name='StorageBucketAccessControlsListRequest',
        response_type_name='BucketAccessControls',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Patches an ACL entry on the specified bucket.

      Args:
        request: (StorageBucketAccessControlsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BucketAccessControl) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='storage.bucketAccessControls.patch',
        ordered_params=['bucket', 'entity'],
        path_params=['bucket', 'entity'],
        query_params=['userProject'],
        relative_path='b/{bucket}/acl/{entity}',
        request_field='bucketAccessControl',
        request_type_name='StorageBucketAccessControlsPatchRequest',
        response_type_name='BucketAccessControl',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an ACL entry on the specified bucket.

      Args:
        request: (StorageBucketAccessControlsUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BucketAccessControl) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='storage.bucketAccessControls.update',
        ordered_params=['bucket', 'entity'],
        path_params=['bucket', 'entity'],
        query_params=['userProject'],
        relative_path='b/{bucket}/acl/{entity}',
        request_field='bucketAccessControl',
        request_type_name='StorageBucketAccessControlsUpdateRequest',
        response_type_name='BucketAccessControl',
        supports_download=False,
    )

  class BucketsService(base_api.BaseApiService):
    """Service class for the buckets resource."""

    _NAME = 'buckets'

    def __init__(self, client):
      super(StorageV1.BucketsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes an empty bucket. Deletions are permanent unless soft delete is enabled on the bucket.

      Args:
        request: (StorageBucketsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (StorageBucketsDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='storage.buckets.delete',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['ifMetagenerationMatch', 'ifMetagenerationNotMatch', 'userProject'],
        relative_path='b/{bucket}',
        request_field='',
        request_type_name='StorageBucketsDeleteRequest',
        response_type_name='StorageBucketsDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns metadata for the specified bucket.

      Args:
        request: (StorageBucketsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Bucket) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.buckets.get',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['generation', 'ifMetagenerationMatch', 'ifMetagenerationNotMatch', 'projection', 'softDeleted', 'userProject'],
        relative_path='b/{bucket}',
        request_field='',
        request_type_name='StorageBucketsGetRequest',
        response_type_name='Bucket',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Returns an IAM policy for the specified bucket.

      Args:
        request: (StorageBucketsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.buckets.getIamPolicy',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['optionsRequestedPolicyVersion', 'userProject'],
        relative_path='b/{bucket}/iam',
        request_field='',
        request_type_name='StorageBucketsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def GetStorageLayout(self, request, global_params=None):
      r"""Returns the storage layout configuration for the specified bucket. Note that this operation requires storage.objects.list permission.

      Args:
        request: (StorageBucketsGetStorageLayoutRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BucketStorageLayout) The response message.
      """
      config = self.GetMethodConfig('GetStorageLayout')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetStorageLayout.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.buckets.getStorageLayout',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['prefix'],
        relative_path='b/{bucket}/storageLayout',
        request_field='',
        request_type_name='StorageBucketsGetStorageLayoutRequest',
        response_type_name='BucketStorageLayout',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates a new bucket.

      Args:
        request: (StorageBucketsInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Bucket) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.buckets.insert',
        ordered_params=['project'],
        path_params=[],
        query_params=['enableObjectRetention', 'predefinedAcl', 'predefinedDefaultObjectAcl', 'project', 'projection', 'userProject'],
        relative_path='b',
        request_field='bucket',
        request_type_name='StorageBucketsInsertRequest',
        response_type_name='Bucket',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieves a list of buckets for a given project.

      Args:
        request: (StorageBucketsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Buckets) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.buckets.list',
        ordered_params=['project'],
        path_params=[],
        query_params=['maxResults', 'pageToken', 'prefix', 'project', 'projection', 'softDeleted', 'userProject'],
        relative_path='b',
        request_field='',
        request_type_name='StorageBucketsListRequest',
        response_type_name='Buckets',
        supports_download=False,
    )

    def LockRetentionPolicy(self, request, global_params=None):
      r"""Locks retention policy on a bucket.

      Args:
        request: (StorageBucketsLockRetentionPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Bucket) The response message.
      """
      config = self.GetMethodConfig('LockRetentionPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    LockRetentionPolicy.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.buckets.lockRetentionPolicy',
        ordered_params=['bucket', 'ifMetagenerationMatch'],
        path_params=['bucket'],
        query_params=['ifMetagenerationMatch', 'userProject'],
        relative_path='b/{bucket}/lockRetentionPolicy',
        request_field='',
        request_type_name='StorageBucketsLockRetentionPolicyRequest',
        response_type_name='Bucket',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Patches a bucket. Changes to the bucket will be readable immediately after writing, but configuration changes may take time to propagate.

      Args:
        request: (StorageBucketsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Bucket) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='storage.buckets.patch',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['ifMetagenerationMatch', 'ifMetagenerationNotMatch', 'predefinedAcl', 'predefinedDefaultObjectAcl', 'projection', 'userProject'],
        relative_path='b/{bucket}',
        request_field='bucketResource',
        request_type_name='StorageBucketsPatchRequest',
        response_type_name='Bucket',
        supports_download=False,
    )

    def Relocate(self, request, global_params=None):
      r"""Initiates a long-running Relocate Bucket operation on the specified bucket.

      Args:
        request: (StorageBucketsRelocateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Relocate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Relocate.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.buckets.relocate',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=[],
        relative_path='b/{bucket}/relocate',
        request_field='relocateBucketRequest',
        request_type_name='StorageBucketsRelocateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Restore(self, request, global_params=None):
      r"""Restores a soft-deleted bucket.

      Args:
        request: (StorageBucketsRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Bucket) The response message.
      """
      config = self.GetMethodConfig('Restore')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restore.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.buckets.restore',
        ordered_params=['bucket', 'generation'],
        path_params=['bucket'],
        query_params=['generation', 'projection', 'userProject'],
        relative_path='b/{bucket}/restore',
        request_field='',
        request_type_name='StorageBucketsRestoreRequest',
        response_type_name='Bucket',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Updates an IAM policy for the specified bucket.

      Args:
        request: (StorageBucketsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='storage.buckets.setIamPolicy',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['userProject'],
        relative_path='b/{bucket}/iam',
        request_field='policy',
        request_type_name='StorageBucketsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Tests a set of permissions on the given bucket to see which, if any, are held by the caller.

      Args:
        request: (StorageBucketsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.buckets.testIamPermissions',
        ordered_params=['bucket', 'permissions'],
        path_params=['bucket'],
        query_params=['permissions', 'userProject'],
        relative_path='b/{bucket}/iam/testPermissions',
        request_field='',
        request_type_name='StorageBucketsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates a bucket. Changes to the bucket will be readable immediately after writing, but configuration changes may take time to propagate.

      Args:
        request: (StorageBucketsUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Bucket) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='storage.buckets.update',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['ifMetagenerationMatch', 'ifMetagenerationNotMatch', 'predefinedAcl', 'predefinedDefaultObjectAcl', 'projection', 'userProject'],
        relative_path='b/{bucket}',
        request_field='bucketResource',
        request_type_name='StorageBucketsUpdateRequest',
        response_type_name='Bucket',
        supports_download=False,
    )

  class ChannelsService(base_api.BaseApiService):
    """Service class for the channels resource."""

    _NAME = 'channels'

    def __init__(self, client):
      super(StorageV1.ChannelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Stop(self, request, global_params=None):
      r"""Stop watching resources through this channel.

      Args:
        request: (Channel) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (StorageChannelsStopResponse) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.channels.stop',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='channels/stop',
        request_field='<request>',
        request_type_name='Channel',
        response_type_name='StorageChannelsStopResponse',
        supports_download=False,
    )

  class DefaultObjectAccessControlsService(base_api.BaseApiService):
    """Service class for the defaultObjectAccessControls resource."""

    _NAME = 'defaultObjectAccessControls'

    def __init__(self, client):
      super(StorageV1.DefaultObjectAccessControlsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Permanently deletes the default object ACL entry for the specified entity on the specified bucket.

      Args:
        request: (StorageDefaultObjectAccessControlsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (StorageDefaultObjectAccessControlsDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='storage.defaultObjectAccessControls.delete',
        ordered_params=['bucket', 'entity'],
        path_params=['bucket', 'entity'],
        query_params=['userProject'],
        relative_path='b/{bucket}/defaultObjectAcl/{entity}',
        request_field='',
        request_type_name='StorageDefaultObjectAccessControlsDeleteRequest',
        response_type_name='StorageDefaultObjectAccessControlsDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the default object ACL entry for the specified entity on the specified bucket.

      Args:
        request: (StorageDefaultObjectAccessControlsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ObjectAccessControl) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.defaultObjectAccessControls.get',
        ordered_params=['bucket', 'entity'],
        path_params=['bucket', 'entity'],
        query_params=['userProject'],
        relative_path='b/{bucket}/defaultObjectAcl/{entity}',
        request_field='',
        request_type_name='StorageDefaultObjectAccessControlsGetRequest',
        response_type_name='ObjectAccessControl',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates a new default object ACL entry on the specified bucket.

      Args:
        request: (StorageDefaultObjectAccessControlsInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ObjectAccessControl) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.defaultObjectAccessControls.insert',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['userProject'],
        relative_path='b/{bucket}/defaultObjectAcl',
        request_field='objectAccessControl',
        request_type_name='StorageDefaultObjectAccessControlsInsertRequest',
        response_type_name='ObjectAccessControl',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieves default object ACL entries on the specified bucket.

      Args:
        request: (StorageDefaultObjectAccessControlsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ObjectAccessControls) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.defaultObjectAccessControls.list',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['ifMetagenerationMatch', 'ifMetagenerationNotMatch', 'userProject'],
        relative_path='b/{bucket}/defaultObjectAcl',
        request_field='',
        request_type_name='StorageDefaultObjectAccessControlsListRequest',
        response_type_name='ObjectAccessControls',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Patches a default object ACL entry on the specified bucket.

      Args:
        request: (StorageDefaultObjectAccessControlsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ObjectAccessControl) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='storage.defaultObjectAccessControls.patch',
        ordered_params=['bucket', 'entity'],
        path_params=['bucket', 'entity'],
        query_params=['userProject'],
        relative_path='b/{bucket}/defaultObjectAcl/{entity}',
        request_field='objectAccessControl',
        request_type_name='StorageDefaultObjectAccessControlsPatchRequest',
        response_type_name='ObjectAccessControl',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates a default object ACL entry on the specified bucket.

      Args:
        request: (StorageDefaultObjectAccessControlsUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ObjectAccessControl) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='storage.defaultObjectAccessControls.update',
        ordered_params=['bucket', 'entity'],
        path_params=['bucket', 'entity'],
        query_params=['userProject'],
        relative_path='b/{bucket}/defaultObjectAcl/{entity}',
        request_field='objectAccessControl',
        request_type_name='StorageDefaultObjectAccessControlsUpdateRequest',
        response_type_name='ObjectAccessControl',
        supports_download=False,
    )

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(StorageV1.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Permanently deletes a folder. Only applicable to buckets with hierarchical namespace enabled.

      Args:
        request: (StorageFoldersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (StorageFoldersDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='storage.folders.delete',
        ordered_params=['bucket', 'folder'],
        path_params=['bucket', 'folder'],
        query_params=['ifMetagenerationMatch', 'ifMetagenerationNotMatch'],
        relative_path='b/{bucket}/folders/{folder}',
        request_field='',
        request_type_name='StorageFoldersDeleteRequest',
        response_type_name='StorageFoldersDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns metadata for the specified folder. Only applicable to buckets with hierarchical namespace enabled.

      Args:
        request: (StorageFoldersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Folder) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.folders.get',
        ordered_params=['bucket', 'folder'],
        path_params=['bucket', 'folder'],
        query_params=['ifMetagenerationMatch', 'ifMetagenerationNotMatch'],
        relative_path='b/{bucket}/folders/{folder}',
        request_field='',
        request_type_name='StorageFoldersGetRequest',
        response_type_name='Folder',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates a new folder. Only applicable to buckets with hierarchical namespace enabled.

      Args:
        request: (StorageFoldersInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Folder) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.folders.insert',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['recursive'],
        relative_path='b/{bucket}/folders',
        request_field='folder',
        request_type_name='StorageFoldersInsertRequest',
        response_type_name='Folder',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieves a list of folders matching the criteria. Only applicable to buckets with hierarchical namespace enabled.

      Args:
        request: (StorageFoldersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Folders) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.folders.list',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['delimiter', 'endOffset', 'pageSize', 'pageToken', 'prefix', 'startOffset'],
        relative_path='b/{bucket}/folders',
        request_field='',
        request_type_name='StorageFoldersListRequest',
        response_type_name='Folders',
        supports_download=False,
    )

    def Rename(self, request, global_params=None):
      r"""Renames a source folder to a destination folder. Only applicable to buckets with hierarchical namespace enabled.

      Args:
        request: (StorageFoldersRenameRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Rename')
      return self._RunMethod(
          config, request, global_params=global_params)

    Rename.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.folders.rename',
        ordered_params=['bucket', 'sourceFolder', 'destinationFolder'],
        path_params=['bucket', 'destinationFolder', 'sourceFolder'],
        query_params=['ifSourceMetagenerationMatch', 'ifSourceMetagenerationNotMatch'],
        relative_path='b/{bucket}/folders/{sourceFolder}/renameTo/folders/{destinationFolder}',
        request_field='',
        request_type_name='StorageFoldersRenameRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ManagedFoldersService(base_api.BaseApiService):
    """Service class for the managedFolders resource."""

    _NAME = 'managedFolders'

    def __init__(self, client):
      super(StorageV1.ManagedFoldersService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Permanently deletes a managed folder.

      Args:
        request: (StorageManagedFoldersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (StorageManagedFoldersDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='storage.managedFolders.delete',
        ordered_params=['bucket', 'managedFolder'],
        path_params=['bucket', 'managedFolder'],
        query_params=['allowNonEmpty', 'ifMetagenerationMatch', 'ifMetagenerationNotMatch'],
        relative_path='b/{bucket}/managedFolders/{managedFolder}',
        request_field='',
        request_type_name='StorageManagedFoldersDeleteRequest',
        response_type_name='StorageManagedFoldersDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns metadata of the specified managed folder.

      Args:
        request: (StorageManagedFoldersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ManagedFolder) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.managedFolders.get',
        ordered_params=['bucket', 'managedFolder'],
        path_params=['bucket', 'managedFolder'],
        query_params=['ifMetagenerationMatch', 'ifMetagenerationNotMatch'],
        relative_path='b/{bucket}/managedFolders/{managedFolder}',
        request_field='',
        request_type_name='StorageManagedFoldersGetRequest',
        response_type_name='ManagedFolder',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Returns an IAM policy for the specified managed folder.

      Args:
        request: (StorageManagedFoldersGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.managedFolders.getIamPolicy',
        ordered_params=['bucket', 'managedFolder'],
        path_params=['bucket', 'managedFolder'],
        query_params=['optionsRequestedPolicyVersion', 'userProject'],
        relative_path='b/{bucket}/managedFolders/{managedFolder}/iam',
        request_field='',
        request_type_name='StorageManagedFoldersGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates a new managed folder.

      Args:
        request: (ManagedFolder) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ManagedFolder) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.managedFolders.insert',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=[],
        relative_path='b/{bucket}/managedFolders',
        request_field='<request>',
        request_type_name='ManagedFolder',
        response_type_name='ManagedFolder',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists managed folders in the given bucket.

      Args:
        request: (StorageManagedFoldersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ManagedFolders) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.managedFolders.list',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['pageSize', 'pageToken', 'prefix'],
        relative_path='b/{bucket}/managedFolders',
        request_field='',
        request_type_name='StorageManagedFoldersListRequest',
        response_type_name='ManagedFolders',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Updates an IAM policy for the specified managed folder.

      Args:
        request: (StorageManagedFoldersSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='storage.managedFolders.setIamPolicy',
        ordered_params=['bucket', 'managedFolder'],
        path_params=['bucket', 'managedFolder'],
        query_params=['userProject'],
        relative_path='b/{bucket}/managedFolders/{managedFolder}/iam',
        request_field='policy',
        request_type_name='StorageManagedFoldersSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Tests a set of permissions on the given managed folder to see which, if any, are held by the caller.

      Args:
        request: (StorageManagedFoldersTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.managedFolders.testIamPermissions',
        ordered_params=['bucket', 'managedFolder', 'permissions'],
        path_params=['bucket', 'managedFolder'],
        query_params=['permissions', 'userProject'],
        relative_path='b/{bucket}/managedFolders/{managedFolder}/iam/testPermissions',
        request_field='',
        request_type_name='StorageManagedFoldersTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class NotificationsService(base_api.BaseApiService):
    """Service class for the notifications resource."""

    _NAME = 'notifications'

    def __init__(self, client):
      super(StorageV1.NotificationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Permanently deletes a notification subscription.

      Args:
        request: (StorageNotificationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (StorageNotificationsDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='storage.notifications.delete',
        ordered_params=['bucket', 'notification'],
        path_params=['bucket', 'notification'],
        query_params=['userProject'],
        relative_path='b/{bucket}/notificationConfigs/{notification}',
        request_field='',
        request_type_name='StorageNotificationsDeleteRequest',
        response_type_name='StorageNotificationsDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""View a notification configuration.

      Args:
        request: (StorageNotificationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Notification) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.notifications.get',
        ordered_params=['bucket', 'notification'],
        path_params=['bucket', 'notification'],
        query_params=['userProject'],
        relative_path='b/{bucket}/notificationConfigs/{notification}',
        request_field='',
        request_type_name='StorageNotificationsGetRequest',
        response_type_name='Notification',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates a notification subscription for a given bucket.

      Args:
        request: (StorageNotificationsInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Notification) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.notifications.insert',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['userProject'],
        relative_path='b/{bucket}/notificationConfigs',
        request_field='notification',
        request_type_name='StorageNotificationsInsertRequest',
        response_type_name='Notification',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieves a list of notification subscriptions for a given bucket.

      Args:
        request: (StorageNotificationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Notifications) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.notifications.list',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['userProject'],
        relative_path='b/{bucket}/notificationConfigs',
        request_field='',
        request_type_name='StorageNotificationsListRequest',
        response_type_name='Notifications',
        supports_download=False,
    )

  class ObjectAccessControlsService(base_api.BaseApiService):
    """Service class for the objectAccessControls resource."""

    _NAME = 'objectAccessControls'

    def __init__(self, client):
      super(StorageV1.ObjectAccessControlsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Permanently deletes the ACL entry for the specified entity on the specified object.

      Args:
        request: (StorageObjectAccessControlsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (StorageObjectAccessControlsDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='storage.objectAccessControls.delete',
        ordered_params=['bucket', 'object', 'entity'],
        path_params=['bucket', 'entity', 'object'],
        query_params=['generation', 'userProject'],
        relative_path='b/{bucket}/o/{object}/acl/{entity}',
        request_field='',
        request_type_name='StorageObjectAccessControlsDeleteRequest',
        response_type_name='StorageObjectAccessControlsDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the ACL entry for the specified entity on the specified object.

      Args:
        request: (StorageObjectAccessControlsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ObjectAccessControl) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.objectAccessControls.get',
        ordered_params=['bucket', 'object', 'entity'],
        path_params=['bucket', 'entity', 'object'],
        query_params=['generation', 'userProject'],
        relative_path='b/{bucket}/o/{object}/acl/{entity}',
        request_field='',
        request_type_name='StorageObjectAccessControlsGetRequest',
        response_type_name='ObjectAccessControl',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates a new ACL entry on the specified object.

      Args:
        request: (StorageObjectAccessControlsInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ObjectAccessControl) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.objectAccessControls.insert',
        ordered_params=['bucket', 'object'],
        path_params=['bucket', 'object'],
        query_params=['generation', 'userProject'],
        relative_path='b/{bucket}/o/{object}/acl',
        request_field='objectAccessControl',
        request_type_name='StorageObjectAccessControlsInsertRequest',
        response_type_name='ObjectAccessControl',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieves ACL entries on the specified object.

      Args:
        request: (StorageObjectAccessControlsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ObjectAccessControls) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.objectAccessControls.list',
        ordered_params=['bucket', 'object'],
        path_params=['bucket', 'object'],
        query_params=['generation', 'userProject'],
        relative_path='b/{bucket}/o/{object}/acl',
        request_field='',
        request_type_name='StorageObjectAccessControlsListRequest',
        response_type_name='ObjectAccessControls',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Patches an ACL entry on the specified object.

      Args:
        request: (StorageObjectAccessControlsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ObjectAccessControl) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='storage.objectAccessControls.patch',
        ordered_params=['bucket', 'object', 'entity'],
        path_params=['bucket', 'entity', 'object'],
        query_params=['generation', 'userProject'],
        relative_path='b/{bucket}/o/{object}/acl/{entity}',
        request_field='objectAccessControl',
        request_type_name='StorageObjectAccessControlsPatchRequest',
        response_type_name='ObjectAccessControl',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an ACL entry on the specified object.

      Args:
        request: (StorageObjectAccessControlsUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ObjectAccessControl) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='storage.objectAccessControls.update',
        ordered_params=['bucket', 'object', 'entity'],
        path_params=['bucket', 'entity', 'object'],
        query_params=['generation', 'userProject'],
        relative_path='b/{bucket}/o/{object}/acl/{entity}',
        request_field='objectAccessControl',
        request_type_name='StorageObjectAccessControlsUpdateRequest',
        response_type_name='ObjectAccessControl',
        supports_download=False,
    )

  class ObjectsService(base_api.BaseApiService):
    """Service class for the objects resource."""

    _NAME = 'objects'

    def __init__(self, client):
      super(StorageV1.ObjectsService, self).__init__(client)
      self._upload_configs = {
          'Insert': base_api.ApiUploadInfo(
              accept=['*/*'],
              max_size=None,
              resumable_multipart=True,
              resumable_path='/resumable/upload/storage/v1/b/{bucket}/o',
              simple_multipart=True,
              simple_path='/upload/storage/v1/b/{bucket}/o',
          ),
          }

    def BulkRestore(self, request, global_params=None):
      r"""Initiates a long-running bulk restore operation on the specified bucket.

      Args:
        request: (StorageObjectsBulkRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BulkRestore')
      return self._RunMethod(
          config, request, global_params=global_params)

    BulkRestore.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.objects.bulkRestore',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=[],
        relative_path='b/{bucket}/o/bulkRestore',
        request_field='bulkRestoreObjectsRequest',
        request_type_name='StorageObjectsBulkRestoreRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Compose(self, request, global_params=None):
      r"""Concatenates a list of existing objects into a new object in the same bucket.

      Args:
        request: (StorageObjectsComposeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Object) The response message.
      """
      config = self.GetMethodConfig('Compose')
      return self._RunMethod(
          config, request, global_params=global_params)

    Compose.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.objects.compose',
        ordered_params=['destinationBucket', 'destinationObject'],
        path_params=['destinationBucket', 'destinationObject'],
        query_params=['destinationPredefinedAcl', 'ifGenerationMatch', 'ifMetagenerationMatch', 'kmsKeyName', 'userProject'],
        relative_path='b/{destinationBucket}/o/{destinationObject}/compose',
        request_field='composeRequest',
        request_type_name='StorageObjectsComposeRequest',
        response_type_name='Object',
        supports_download=False,
    )

    def Copy(self, request, global_params=None):
      r"""Copies a source object to a destination object. Optionally overrides metadata.

      Args:
        request: (StorageObjectsCopyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Object) The response message.
      """
      config = self.GetMethodConfig('Copy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Copy.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.objects.copy',
        ordered_params=['sourceBucket', 'sourceObject', 'destinationBucket', 'destinationObject'],
        path_params=['destinationBucket', 'destinationObject', 'sourceBucket', 'sourceObject'],
        query_params=['destinationKmsKeyName', 'destinationPredefinedAcl', 'ifGenerationMatch', 'ifGenerationNotMatch', 'ifMetagenerationMatch', 'ifMetagenerationNotMatch', 'ifSourceGenerationMatch', 'ifSourceGenerationNotMatch', 'ifSourceMetagenerationMatch', 'ifSourceMetagenerationNotMatch', 'projection', 'sourceGeneration', 'userProject'],
        relative_path='b/{sourceBucket}/o/{sourceObject}/copyTo/b/{destinationBucket}/o/{destinationObject}',
        request_field='object',
        request_type_name='StorageObjectsCopyRequest',
        response_type_name='Object',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an object and its metadata. Deletions are permanent if versioning is not enabled for the bucket, or if the generation parameter is used.

      Args:
        request: (StorageObjectsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (StorageObjectsDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='storage.objects.delete',
        ordered_params=['bucket', 'object'],
        path_params=['bucket', 'object'],
        query_params=['generation', 'ifGenerationMatch', 'ifGenerationNotMatch', 'ifMetagenerationMatch', 'ifMetagenerationNotMatch', 'userProject'],
        relative_path='b/{bucket}/o/{object}',
        request_field='',
        request_type_name='StorageObjectsDeleteRequest',
        response_type_name='StorageObjectsDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None, download=None):
      r"""Retrieves an object or its metadata.

      Args:
        request: (StorageObjectsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
        download: (Download, default: None) If present, download
            data from the request via this stream.
      Returns:
        (Object) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params,
          download=download)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.objects.get',
        ordered_params=['bucket', 'object'],
        path_params=['bucket', 'object'],
        query_params=['generation', 'ifGenerationMatch', 'ifGenerationNotMatch', 'ifMetagenerationMatch', 'ifMetagenerationNotMatch', 'projection', 'restoreToken', 'softDeleted', 'userProject'],
        relative_path='b/{bucket}/o/{object}',
        request_field='',
        request_type_name='StorageObjectsGetRequest',
        response_type_name='Object',
        supports_download=True,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Returns an IAM policy for the specified object.

      Args:
        request: (StorageObjectsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.objects.getIamPolicy',
        ordered_params=['bucket', 'object'],
        path_params=['bucket', 'object'],
        query_params=['generation', 'userProject'],
        relative_path='b/{bucket}/o/{object}/iam',
        request_field='',
        request_type_name='StorageObjectsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Insert(self, request, global_params=None, upload=None):
      r"""Stores a new object and metadata.

      Args:
        request: (StorageObjectsInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
        upload: (Upload, default: None) If present, upload
            this stream with the request.
      Returns:
        (Object) The response message.
      """
      config = self.GetMethodConfig('Insert')
      upload_config = self.GetUploadConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params,
          upload=upload, upload_config=upload_config)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.objects.insert',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['contentEncoding', 'ifGenerationMatch', 'ifGenerationNotMatch', 'ifMetagenerationMatch', 'ifMetagenerationNotMatch', 'kmsKeyName', 'name', 'predefinedAcl', 'projection', 'userProject'],
        relative_path='b/{bucket}/o',
        request_field='object',
        request_type_name='StorageObjectsInsertRequest',
        response_type_name='Object',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieves a list of objects matching the criteria.

      Args:
        request: (StorageObjectsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Objects) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.objects.list',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['delimiter', 'endOffset', 'filter', 'includeFoldersAsPrefixes', 'includeTrailingDelimiter', 'matchGlob', 'maxResults', 'pageToken', 'prefix', 'projection', 'softDeleted', 'startOffset', 'userProject', 'versions'],
        relative_path='b/{bucket}/o',
        request_field='',
        request_type_name='StorageObjectsListRequest',
        response_type_name='Objects',
        supports_download=False,
    )

    def Move(self, request, global_params=None):
      r"""Moves the source object to the destination object in the same bucket.

      Args:
        request: (StorageObjectsMoveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Object) The response message.
      """
      config = self.GetMethodConfig('Move')
      return self._RunMethod(
          config, request, global_params=global_params)

    Move.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.objects.move',
        ordered_params=['bucket', 'sourceObject', 'destinationObject'],
        path_params=['bucket', 'destinationObject', 'sourceObject'],
        query_params=['ifGenerationMatch', 'ifGenerationNotMatch', 'ifMetagenerationMatch', 'ifMetagenerationNotMatch', 'ifSourceGenerationMatch', 'ifSourceGenerationNotMatch', 'ifSourceMetagenerationMatch', 'ifSourceMetagenerationNotMatch', 'projection', 'userProject'],
        relative_path='b/{bucket}/o/{sourceObject}/moveTo/o/{destinationObject}',
        request_field='',
        request_type_name='StorageObjectsMoveRequest',
        response_type_name='Object',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Patches an object's metadata.

      Args:
        request: (StorageObjectsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Object) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='storage.objects.patch',
        ordered_params=['bucket', 'object'],
        path_params=['bucket', 'object'],
        query_params=['generation', 'ifGenerationMatch', 'ifGenerationNotMatch', 'ifMetagenerationMatch', 'ifMetagenerationNotMatch', 'overrideUnlockedRetention', 'predefinedAcl', 'projection', 'userProject'],
        relative_path='b/{bucket}/o/{object}',
        request_field='objectResource',
        request_type_name='StorageObjectsPatchRequest',
        response_type_name='Object',
        supports_download=False,
    )

    def Restore(self, request, global_params=None):
      r"""Restores a soft-deleted object.

      Args:
        request: (StorageObjectsRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Object) The response message.
      """
      config = self.GetMethodConfig('Restore')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restore.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.objects.restore',
        ordered_params=['bucket', 'object', 'generation'],
        path_params=['bucket', 'object'],
        query_params=['copySourceAcl', 'generation', 'ifGenerationMatch', 'ifGenerationNotMatch', 'ifMetagenerationMatch', 'ifMetagenerationNotMatch', 'projection', 'restoreToken', 'userProject'],
        relative_path='b/{bucket}/o/{object}/restore',
        request_field='',
        request_type_name='StorageObjectsRestoreRequest',
        response_type_name='Object',
        supports_download=False,
    )

    def Rewrite(self, request, global_params=None):
      r"""Rewrites a source object to a destination object. Optionally overrides metadata.

      Args:
        request: (StorageObjectsRewriteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RewriteResponse) The response message.
      """
      config = self.GetMethodConfig('Rewrite')
      return self._RunMethod(
          config, request, global_params=global_params)

    Rewrite.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.objects.rewrite',
        ordered_params=['sourceBucket', 'sourceObject', 'destinationBucket', 'destinationObject'],
        path_params=['destinationBucket', 'destinationObject', 'sourceBucket', 'sourceObject'],
        query_params=['destinationKmsKeyName', 'destinationPredefinedAcl', 'ifGenerationMatch', 'ifGenerationNotMatch', 'ifMetagenerationMatch', 'ifMetagenerationNotMatch', 'ifSourceGenerationMatch', 'ifSourceGenerationNotMatch', 'ifSourceMetagenerationMatch', 'ifSourceMetagenerationNotMatch', 'maxBytesRewrittenPerCall', 'projection', 'rewriteToken', 'sourceGeneration', 'userProject'],
        relative_path='b/{sourceBucket}/o/{sourceObject}/rewriteTo/b/{destinationBucket}/o/{destinationObject}',
        request_field='object',
        request_type_name='StorageObjectsRewriteRequest',
        response_type_name='RewriteResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Updates an IAM policy for the specified object.

      Args:
        request: (StorageObjectsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='storage.objects.setIamPolicy',
        ordered_params=['bucket', 'object'],
        path_params=['bucket', 'object'],
        query_params=['generation', 'userProject'],
        relative_path='b/{bucket}/o/{object}/iam',
        request_field='policy',
        request_type_name='StorageObjectsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Tests a set of permissions on the given object to see which, if any, are held by the caller.

      Args:
        request: (StorageObjectsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.objects.testIamPermissions',
        ordered_params=['bucket', 'object', 'permissions'],
        path_params=['bucket', 'object'],
        query_params=['generation', 'permissions', 'userProject'],
        relative_path='b/{bucket}/o/{object}/iam/testPermissions',
        request_field='',
        request_type_name='StorageObjectsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an object's metadata.

      Args:
        request: (StorageObjectsUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Object) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='storage.objects.update',
        ordered_params=['bucket', 'object'],
        path_params=['bucket', 'object'],
        query_params=['generation', 'ifGenerationMatch', 'ifGenerationNotMatch', 'ifMetagenerationMatch', 'ifMetagenerationNotMatch', 'overrideUnlockedRetention', 'predefinedAcl', 'projection', 'userProject'],
        relative_path='b/{bucket}/o/{object}',
        request_field='objectResource',
        request_type_name='StorageObjectsUpdateRequest',
        response_type_name='Object',
        supports_download=False,
    )

    def WatchAll(self, request, global_params=None):
      r"""Watch for changes on all objects in a bucket.

      Args:
        request: (StorageObjectsWatchAllRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Channel) The response message.
      """
      config = self.GetMethodConfig('WatchAll')
      return self._RunMethod(
          config, request, global_params=global_params)

    WatchAll.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.objects.watchAll',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['delimiter', 'endOffset', 'includeTrailingDelimiter', 'maxResults', 'pageToken', 'prefix', 'projection', 'startOffset', 'userProject', 'versions'],
        relative_path='b/{bucket}/o/watch',
        request_field='channel',
        request_type_name='StorageObjectsWatchAllRequest',
        response_type_name='Channel',
        supports_download=False,
    )

  class OperationsService(base_api.BaseApiService):
    """Service class for the operations resource."""

    _NAME = 'operations'

    def __init__(self, client):
      super(StorageV1.OperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def AdvanceRelocateBucket(self, request, global_params=None):
      r"""Starts asynchronous advancement of the relocate bucket operation in the case of required write downtime, to allow it to lock the bucket at the source location, and proceed with the bucket location swap. The server makes a best effort to advance the relocate bucket operation, but success is not guaranteed.

      Args:
        request: (StorageBucketsOperationsAdvanceRelocateBucketRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (StorageBucketsOperationsAdvanceRelocateBucketResponse) The response message.
      """
      config = self.GetMethodConfig('AdvanceRelocateBucket')
      return self._RunMethod(
          config, request, global_params=global_params)

    AdvanceRelocateBucket.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.buckets.operations.advanceRelocateBucket',
        ordered_params=['bucket', 'operationId'],
        path_params=['bucket', 'operationId'],
        query_params=[],
        relative_path='b/{bucket}/operations/{operationId}/advanceRelocateBucket',
        request_field='advanceRelocateBucketOperationRequest',
        request_type_name='StorageBucketsOperationsAdvanceRelocateBucketRequest',
        response_type_name='StorageBucketsOperationsAdvanceRelocateBucketResponse',
        supports_download=False,
    )

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed.

      Args:
        request: (StorageBucketsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (StorageBucketsOperationsCancelResponse) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.buckets.operations.cancel',
        ordered_params=['bucket', 'operationId'],
        path_params=['bucket', 'operationId'],
        query_params=[],
        relative_path='b/{bucket}/operations/{operationId}/cancel',
        request_field='',
        request_type_name='StorageBucketsOperationsCancelRequest',
        response_type_name='StorageBucketsOperationsCancelResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation.

      Args:
        request: (StorageBucketsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.buckets.operations.get',
        ordered_params=['bucket', 'operationId'],
        path_params=['bucket', 'operationId'],
        query_params=[],
        relative_path='b/{bucket}/operations/{operationId}',
        request_field='',
        request_type_name='StorageBucketsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request.

      Args:
        request: (StorageBucketsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.buckets.operations.list',
        ordered_params=['bucket'],
        path_params=['bucket'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='b/{bucket}/operations',
        request_field='',
        request_type_name='StorageBucketsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class ProjectsHmacKeysService(base_api.BaseApiService):
    """Service class for the projects_hmacKeys resource."""

    _NAME = 'projects_hmacKeys'

    def __init__(self, client):
      super(StorageV1.ProjectsHmacKeysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new HMAC key for the specified service account.

      Args:
        request: (StorageProjectsHmacKeysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HmacKey) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='storage.projects.hmacKeys.create',
        ordered_params=['projectId', 'serviceAccountEmail'],
        path_params=['projectId'],
        query_params=['serviceAccountEmail', 'userProject'],
        relative_path='projects/{projectId}/hmacKeys',
        request_field='',
        request_type_name='StorageProjectsHmacKeysCreateRequest',
        response_type_name='HmacKey',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an HMAC key.

      Args:
        request: (StorageProjectsHmacKeysDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (StorageProjectsHmacKeysDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='storage.projects.hmacKeys.delete',
        ordered_params=['projectId', 'accessId'],
        path_params=['accessId', 'projectId'],
        query_params=['userProject'],
        relative_path='projects/{projectId}/hmacKeys/{accessId}',
        request_field='',
        request_type_name='StorageProjectsHmacKeysDeleteRequest',
        response_type_name='StorageProjectsHmacKeysDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves an HMAC key's metadata.

      Args:
        request: (StorageProjectsHmacKeysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HmacKeyMetadata) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.projects.hmacKeys.get',
        ordered_params=['projectId', 'accessId'],
        path_params=['accessId', 'projectId'],
        query_params=['userProject'],
        relative_path='projects/{projectId}/hmacKeys/{accessId}',
        request_field='',
        request_type_name='StorageProjectsHmacKeysGetRequest',
        response_type_name='HmacKeyMetadata',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieves a list of HMAC keys matching the criteria.

      Args:
        request: (StorageProjectsHmacKeysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HmacKeysMetadata) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.projects.hmacKeys.list',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['maxResults', 'pageToken', 'serviceAccountEmail', 'showDeletedKeys', 'userProject'],
        relative_path='projects/{projectId}/hmacKeys',
        request_field='',
        request_type_name='StorageProjectsHmacKeysListRequest',
        response_type_name='HmacKeysMetadata',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates the state of an HMAC key. See the [HMAC Key resource descriptor](https://cloud.google.com/storage/docs/json_api/v1/projects/hmacKeys/update#request-body) for valid states.

      Args:
        request: (StorageProjectsHmacKeysUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HmacKeyMetadata) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='storage.projects.hmacKeys.update',
        ordered_params=['projectId', 'accessId'],
        path_params=['accessId', 'projectId'],
        query_params=['userProject'],
        relative_path='projects/{projectId}/hmacKeys/{accessId}',
        request_field='hmacKeyMetadata',
        request_type_name='StorageProjectsHmacKeysUpdateRequest',
        response_type_name='HmacKeyMetadata',
        supports_download=False,
    )

  class ProjectsServiceAccountService(base_api.BaseApiService):
    """Service class for the projects_serviceAccount resource."""

    _NAME = 'projects_serviceAccount'

    def __init__(self, client):
      super(StorageV1.ProjectsServiceAccountService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get the email address of this project's Google Cloud Storage service account.

      Args:
        request: (StorageProjectsServiceAccountGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceAccount) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='storage.projects.serviceAccount.get',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['userProject'],
        relative_path='projects/{projectId}/serviceAccount',
        request_field='',
        request_type_name='StorageProjectsServiceAccountGetRequest',
        response_type_name='ServiceAccount',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(StorageV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
