"""Generated message classes for storage version v1.

Stores and retrieves potentially large, immutable data objects.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import message_types as _message_types
from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'storage'


class AdvanceRelocateBucketOperationRequest(_messages.Message):
  r"""An AdvanceRelocateBucketOperation request.

  Fields:
    expireTime: Specifies the time when the relocation will revert to the sync
      stage if the relocation hasn't succeeded.
    ttl: Specifies the duration after which the relocation will revert to the
      sync stage if the relocation hasn't succeeded. Optional, if not
      supplied, a default value of 12h will be used.
  """

  expireTime = _message_types.DateTimeField(1)
  ttl = _messages.StringField(2)


class AnywhereCache(_messages.Message):
  r"""An Anywhere Cache instance.

  Fields:
    admissionPolicy: The cache-level entry admission policy.
    anywhereCacheId: The ID of the Anywhere cache instance.
    bucket: The name of the bucket containing this cache instance.
    createTime: The creation time of the cache instance in RFC 3339 format.
    id: The ID of the resource, including the project number, bucket name and
      anywhere cache ID.
    kind: The kind of item this is. For Anywhere Cache, this is always
      storage#anywhereCache.
    pendingUpdate: True if the cache instance has an active Update long-
      running operation.
    selfLink: The link to this cache instance.
    state: The current state of the cache instance.
    ttl: The TTL of all cache entries in whole seconds. e.g., "7200s".
    updateTime: The modification time of the cache instance metadata in RFC
      3339 format.
    zone: The zone in which the cache instance is running. For example, us-
      central1-a.
  """

  admissionPolicy = _messages.StringField(1)
  anywhereCacheId = _messages.StringField(2)
  bucket = _messages.StringField(3)
  createTime = _message_types.DateTimeField(4)
  id = _messages.StringField(5)
  kind = _messages.StringField(6, default='storage#anywhereCache')
  pendingUpdate = _messages.BooleanField(7)
  selfLink = _messages.StringField(8)
  state = _messages.StringField(9)
  ttl = _messages.StringField(10)
  updateTime = _message_types.DateTimeField(11)
  zone = _messages.StringField(12)


class AnywhereCaches(_messages.Message):
  r"""A list of Anywhere Caches.

  Fields:
    items: The list of items.
    kind: The kind of item this is. For lists of Anywhere Caches, this is
      always storage#anywhereCaches.
    nextPageToken: The continuation token, used to page through large result
      sets. Provide this value in a subsequent request to return the next page
      of results.
  """

  items = _messages.MessageField('AnywhereCache', 1, repeated=True)
  kind = _messages.StringField(2, default='storage#anywhereCaches')
  nextPageToken = _messages.StringField(3)


class Bucket(_messages.Message):
  r"""A bucket.

  Messages:
    AutoclassValue: The bucket's Autoclass configuration.
    BillingValue: The bucket's billing configuration.
    CorsValueListEntry: A CorsValueListEntry object.
    CustomPlacementConfigValue: The bucket's custom placement configuration
      for Custom Dual Regions.
    EncryptionValue: Encryption configuration for a bucket.
    HierarchicalNamespaceValue: The bucket's hierarchical namespace
      configuration.
    IamConfigurationValue: The bucket's IAM configuration.
    IpFilterValue: The bucket's IP filter configuration. Specifies the network
      sources that are allowed to access the operations on the bucket, as well
      as its underlying objects. Only enforced when the mode is set to
      'Enabled'.
    LabelsValue: User-provided labels, in key/value pairs.
    LifecycleValue: The bucket's lifecycle configuration. See [Lifecycle
      Management](https://cloud.google.com/storage/docs/lifecycle) for more
      information.
    LoggingValue: The bucket's logging configuration, which defines the
      destination bucket and optional name prefix for the current bucket's
      logs.
    ObjectRetentionValue: The bucket's object retention config.
    OwnerValue: The owner of the bucket. This is always the project team's
      owner group.
    RetentionPolicyValue: The bucket's retention policy. The retention policy
      enforces a minimum retention time for all objects contained in the
      bucket, based on their creation time. Any attempt to overwrite or delete
      objects younger than the retention period will result in a
      PERMISSION_DENIED error. An unlocked retention policy can be modified or
      removed from the bucket via a storage.buckets.update operation. A locked
      retention policy cannot be removed or shortened in duration for the
      lifetime of the bucket. Attempting to remove or decrease period of a
      locked retention policy will result in a PERMISSION_DENIED error.
    SoftDeletePolicyValue: The bucket's soft delete policy, which defines the
      period of time that soft-deleted objects will be retained, and cannot be
      permanently deleted.
    VersioningValue: The bucket's versioning configuration.
    WebsiteValue: The bucket's website configuration, controlling how the
      service behaves when accessing bucket contents as a web site. See the
      [Static Website Examples](https://cloud.google.com/storage/docs/static-
      website) for more information.

  Fields:
    acl: Access controls on the bucket.
    autoclass: The bucket's Autoclass configuration.
    billing: The bucket's billing configuration.
    cors: The bucket's Cross-Origin Resource Sharing (CORS) configuration.
    customPlacementConfig: The bucket's custom placement configuration for
      Custom Dual Regions.
    defaultEventBasedHold: The default value for event-based hold on newly
      created objects in this bucket. Event-based hold is a way to retain
      objects indefinitely until an event occurs, signified by the hold's
      release. After being released, such objects will be subject to bucket-
      level retention (if any). One sample use case of this flag is for banks
      to hold loan documents for at least 3 years after loan is paid in full.
      Here, bucket-level retention is 3 years and the event is loan being paid
      in full. In this example, these objects will be held intact for any
      number of years until the event has occurred (event-based hold on the
      object is released) and then 3 more years after that. That means
      retention duration of the objects begins from the moment event-based
      hold transitioned from true to false. Objects under event-based hold
      cannot be deleted, overwritten or archived until the hold is removed.
    defaultObjectAcl: Default access controls to apply to new objects when no
      ACL is provided.
    encryption: Encryption configuration for a bucket.
    etag: HTTP 1.1 Entity tag for the bucket.
    generation: The generation of this bucket.
    hardDeleteTime: The hard delete time of the bucket in RFC 3339 format.
    hierarchicalNamespace: The bucket's hierarchical namespace configuration.
    iamConfiguration: The bucket's IAM configuration.
    id: The ID of the bucket. For buckets, the id and name properties are the
      same.
    ipFilter: The bucket's IP filter configuration. Specifies the network
      sources that are allowed to access the operations on the bucket, as well
      as its underlying objects. Only enforced when the mode is set to
      'Enabled'.
    kind: The kind of item this is. For buckets, this is always
      storage#bucket.
    labels: User-provided labels, in key/value pairs.
    lifecycle: The bucket's lifecycle configuration. See [Lifecycle
      Management](https://cloud.google.com/storage/docs/lifecycle) for more
      information.
    location: The location of the bucket. Object data for objects in the
      bucket resides in physical storage within this region. Defaults to US.
      See the [Developer's
      Guide](https://cloud.google.com/storage/docs/locations) for the
      authoritative list.
    locationType: The type of the bucket location.
    logging: The bucket's logging configuration, which defines the destination
      bucket and optional name prefix for the current bucket's logs.
    metageneration: The metadata generation of this bucket.
    name: The name of the bucket.
    objectRetention: The bucket's object retention config.
    owner: The owner of the bucket. This is always the project team's owner
      group.
    projectNumber: The project number of the project the bucket belongs to.
    retentionPolicy: The bucket's retention policy. The retention policy
      enforces a minimum retention time for all objects contained in the
      bucket, based on their creation time. Any attempt to overwrite or delete
      objects younger than the retention period will result in a
      PERMISSION_DENIED error. An unlocked retention policy can be modified or
      removed from the bucket via a storage.buckets.update operation. A locked
      retention policy cannot be removed or shortened in duration for the
      lifetime of the bucket. Attempting to remove or decrease period of a
      locked retention policy will result in a PERMISSION_DENIED error.
    rpo: The Recovery Point Objective (RPO) of this bucket. Set to ASYNC_TURBO
      to turn on Turbo Replication on a bucket.
    satisfiesPZI: Reserved for future use.
    satisfiesPZS: Reserved for future use.
    selfLink: The URI of this bucket.
    softDeletePolicy: The bucket's soft delete policy, which defines the
      period of time that soft-deleted objects will be retained, and cannot be
      permanently deleted.
    softDeleteTime: The soft delete time of the bucket in RFC 3339 format.
    storageClass: The bucket's default storage class, used whenever no
      storageClass is specified for a newly-created object. This defines how
      objects in the bucket are stored and determines the SLA and the cost of
      storage. Values include MULTI_REGIONAL, REGIONAL, STANDARD, NEARLINE,
      COLDLINE, ARCHIVE, and DURABLE_REDUCED_AVAILABILITY. If this value is
      not specified when the bucket is created, it will default to STANDARD.
      For more information, see [Storage
      Classes](https://cloud.google.com/storage/docs/storage-classes).
    timeCreated: The creation time of the bucket in RFC 3339 format.
    updated: The modification time of the bucket in RFC 3339 format.
    versioning: The bucket's versioning configuration.
    website: The bucket's website configuration, controlling how the service
      behaves when accessing bucket contents as a web site. See the [Static
      Website Examples](https://cloud.google.com/storage/docs/static-website)
      for more information.
  """

  class AutoclassValue(_messages.Message):
    r"""The bucket's Autoclass configuration.

    Fields:
      enabled: Whether or not Autoclass is enabled on this bucket
      terminalStorageClass: The storage class that objects in the bucket
        eventually transition to if they are not read for a certain length of
        time. Valid values are NEARLINE and ARCHIVE.
      terminalStorageClassUpdateTime: A date and time in RFC 3339 format
        representing the time of the most recent update to
        "terminalStorageClass".
      toggleTime: A date and time in RFC 3339 format representing the instant
        at which "enabled" was last toggled.
    """

    enabled = _messages.BooleanField(1)
    terminalStorageClass = _messages.StringField(2)
    terminalStorageClassUpdateTime = _message_types.DateTimeField(3)
    toggleTime = _message_types.DateTimeField(4)

  class BillingValue(_messages.Message):
    r"""The bucket's billing configuration.

    Fields:
      requesterPays: When set to true, Requester Pays is enabled for this
        bucket.
    """

    requesterPays = _messages.BooleanField(1)

  class CorsValueListEntry(_messages.Message):
    r"""A CorsValueListEntry object.

    Fields:
      maxAgeSeconds: The value, in seconds, to return in the  Access-Control-
        Max-Age header used in preflight responses.
      method: The list of HTTP methods on which to include CORS response
        headers, (GET, OPTIONS, POST, etc) Note: "*" is permitted in the list
        of methods, and means "any method".
      origin: The list of Origins eligible to receive CORS response headers.
        Note: "*" is permitted in the list of origins, and means "any Origin".
      responseHeader: The list of HTTP headers other than the simple response
        headers to give permission for the user-agent to share across domains.
    """

    maxAgeSeconds = _messages.IntegerField(1, variant=_messages.Variant.INT32)
    method = _messages.StringField(2, repeated=True)
    origin = _messages.StringField(3, repeated=True)
    responseHeader = _messages.StringField(4, repeated=True)

  class CustomPlacementConfigValue(_messages.Message):
    r"""The bucket's custom placement configuration for Custom Dual Regions.

    Fields:
      dataLocations: The list of regional locations in which data is placed.
    """

    dataLocations = _messages.StringField(1, repeated=True)

  class EncryptionValue(_messages.Message):
    r"""Encryption configuration for a bucket.

    Messages:
      CustomerManagedEncryptionEnforcementConfigValue: If set, the new objects
        created in this bucket must comply with this enforcement config.
        Changing this has no effect on existing objects; it applies to new
        objects only. If omitted, the new objects are allowed to be encrypted
        with Customer Managed Encryption type by default.
      CustomerSuppliedEncryptionEnforcementConfigValue: If set, the new
        objects created in this bucket must comply with this enforcement
        config. Changing this has no effect on existing objects; it applies to
        new objects only. If omitted, the new objects are allowed to be
        encrypted with Customer Supplied Encryption type by default.
      GoogleManagedEncryptionEnforcementConfigValue: If set, the new objects
        created in this bucket must comply with this enforcement config.
        Changing this has no effect on existing objects; it applies to new
        objects only. If omitted, the new objects are allowed to be encrypted
        with Google Managed Encryption type by default.

    Fields:
      customerManagedEncryptionEnforcementConfig: If set, the new objects
        created in this bucket must comply with this enforcement config.
        Changing this has no effect on existing objects; it applies to new
        objects only. If omitted, the new objects are allowed to be encrypted
        with Customer Managed Encryption type by default.
      customerSuppliedEncryptionEnforcementConfig: If set, the new objects
        created in this bucket must comply with this enforcement config.
        Changing this has no effect on existing objects; it applies to new
        objects only. If omitted, the new objects are allowed to be encrypted
        with Customer Supplied Encryption type by default.
      defaultKmsKeyName: A Cloud KMS key that will be used to encrypt objects
        inserted into this bucket, if no encryption method is specified.
      googleManagedEncryptionEnforcementConfig: If set, the new objects
        created in this bucket must comply with this enforcement config.
        Changing this has no effect on existing objects; it applies to new
        objects only. If omitted, the new objects are allowed to be encrypted
        with Google Managed Encryption type by default.
    """

    class CustomerManagedEncryptionEnforcementConfigValue(_messages.Message):
      r"""If set, the new objects created in this bucket must comply with this
      enforcement config. Changing this has no effect on existing objects; it
      applies to new objects only. If omitted, the new objects are allowed to
      be encrypted with Customer Managed Encryption type by default.

      Enums:
        RestrictionModeValueValuesEnum: Restriction mode for Customer-Managed
          Encryption Keys. Defaults to NotRestricted.

      Fields:
        effectiveTime: Server-determined value that indicates the time from
          which configuration was enforced and effective. This value is in RFC
          3339 format.
        restrictionMode: Restriction mode for Customer-Managed Encryption
          Keys. Defaults to NotRestricted.
      """

      class RestrictionModeValueValuesEnum(_messages.Enum):
        r"""Restriction mode for Customer-Managed Encryption Keys. Defaults to
        NotRestricted.

        Values:
          NotRestricted: Creation of new objects with Customer-Managed
            Encryption is not restricted.
          FullyRestricted: Creation of new objects with Customer-Managed
            Encryption is fully restricted.
        """
        NotRestricted = 0
        FullyRestricted = 1

      effectiveTime = _message_types.DateTimeField(1)
      restrictionMode = _messages.EnumField('RestrictionModeValueValuesEnum', 2)

    class CustomerSuppliedEncryptionEnforcementConfigValue(_messages.Message):
      r"""If set, the new objects created in this bucket must comply with this
      enforcement config. Changing this has no effect on existing objects; it
      applies to new objects only. If omitted, the new objects are allowed to
      be encrypted with Customer Supplied Encryption type by default.

      Enums:
        RestrictionModeValueValuesEnum: Restriction mode for Customer-Supplied
          Encryption Keys. Defaults to NotRestricted.

      Fields:
        effectiveTime: Server-determined value that indicates the time from
          which configuration was enforced and effective. This value is in RFC
          3339 format.
        restrictionMode: Restriction mode for Customer-Supplied Encryption
          Keys. Defaults to NotRestricted.
      """

      class RestrictionModeValueValuesEnum(_messages.Enum):
        r"""Restriction mode for Customer-Supplied Encryption Keys. Defaults
        to NotRestricted.

        Values:
          NotRestricted: Creation of new objects with Customer-Supplied
            Encryption is not restricted.
          FullyRestricted: Creation of new objects with Customer-Supplied
            Encryption is fully restricted.
        """
        NotRestricted = 0
        FullyRestricted = 1

      effectiveTime = _message_types.DateTimeField(1)
      restrictionMode = _messages.EnumField('RestrictionModeValueValuesEnum', 2)

    class GoogleManagedEncryptionEnforcementConfigValue(_messages.Message):
      r"""If set, the new objects created in this bucket must comply with this
      enforcement config. Changing this has no effect on existing objects; it
      applies to new objects only. If omitted, the new objects are allowed to
      be encrypted with Google Managed Encryption type by default.

      Enums:
        RestrictionModeValueValuesEnum: Restriction mode for Google-Managed
          Encryption Keys. Defaults to NotRestricted.

      Fields:
        effectiveTime: Server-determined value that indicates the time from
          which configuration was enforced and effective. This value is in RFC
          3339 format.
        restrictionMode: Restriction mode for Google-Managed Encryption Keys.
          Defaults to NotRestricted.
      """

      class RestrictionModeValueValuesEnum(_messages.Enum):
        r"""Restriction mode for Google-Managed Encryption Keys. Defaults to
        NotRestricted.

        Values:
          NotRestricted: Creation of new objects with Google Managed
            Encryption is not restricted.
          FullyRestricted: Creation of new objects with Google Managed
            Encryption is fully restricted.
        """
        NotRestricted = 0
        FullyRestricted = 1

      effectiveTime = _message_types.DateTimeField(1)
      restrictionMode = _messages.EnumField('RestrictionModeValueValuesEnum', 2)

    customerManagedEncryptionEnforcementConfig = _messages.MessageField('CustomerManagedEncryptionEnforcementConfigValue', 1)
    customerSuppliedEncryptionEnforcementConfig = _messages.MessageField('CustomerSuppliedEncryptionEnforcementConfigValue', 2)
    defaultKmsKeyName = _messages.StringField(3)
    googleManagedEncryptionEnforcementConfig = _messages.MessageField('GoogleManagedEncryptionEnforcementConfigValue', 4)

  class HierarchicalNamespaceValue(_messages.Message):
    r"""The bucket's hierarchical namespace configuration.

    Fields:
      enabled: When set to true, hierarchical namespace is enabled for this
        bucket.
    """

    enabled = _messages.BooleanField(1)

  class IamConfigurationValue(_messages.Message):
    r"""The bucket's IAM configuration.

    Messages:
      BucketPolicyOnlyValue: The bucket's uniform bucket-level access
        configuration. The feature was formerly known as Bucket Policy Only.
        For backward compatibility, this field will be populated with
        identical information as the uniformBucketLevelAccess field. We
        recommend using the uniformBucketLevelAccess field to enable and
        disable the feature.
      UniformBucketLevelAccessValue: The bucket's uniform bucket-level access
        configuration.

    Fields:
      bucketPolicyOnly: The bucket's uniform bucket-level access
        configuration. The feature was formerly known as Bucket Policy Only.
        For backward compatibility, this field will be populated with
        identical information as the uniformBucketLevelAccess field. We
        recommend using the uniformBucketLevelAccess field to enable and
        disable the feature.
      publicAccessPrevention: The bucket's Public Access Prevention
        configuration. Currently, 'inherited' and 'enforced' are supported.
      uniformBucketLevelAccess: The bucket's uniform bucket-level access
        configuration.
    """

    class BucketPolicyOnlyValue(_messages.Message):
      r"""The bucket's uniform bucket-level access configuration. The feature
      was formerly known as Bucket Policy Only. For backward compatibility,
      this field will be populated with identical information as the
      uniformBucketLevelAccess field. We recommend using the
      uniformBucketLevelAccess field to enable and disable the feature.

      Fields:
        enabled: If set, access is controlled only by bucket-level or above
          IAM policies.
        lockedTime: The deadline for changing
          iamConfiguration.bucketPolicyOnly.enabled from true to false in RFC
          3339 format. iamConfiguration.bucketPolicyOnly.enabled may be
          changed from true to false until the locked time, after which the
          field is immutable.
      """

      enabled = _messages.BooleanField(1)
      lockedTime = _message_types.DateTimeField(2)

    class UniformBucketLevelAccessValue(_messages.Message):
      r"""The bucket's uniform bucket-level access configuration.

      Fields:
        enabled: If set, access is controlled only by bucket-level or above
          IAM policies.
        lockedTime: The deadline for changing
          iamConfiguration.uniformBucketLevelAccess.enabled from true to false
          in RFC 3339  format.
          iamConfiguration.uniformBucketLevelAccess.enabled may be changed
          from true to false until the locked time, after which the field is
          immutable.
      """

      enabled = _messages.BooleanField(1)
      lockedTime = _message_types.DateTimeField(2)

    bucketPolicyOnly = _messages.MessageField('BucketPolicyOnlyValue', 1)
    publicAccessPrevention = _messages.StringField(2)
    uniformBucketLevelAccess = _messages.MessageField('UniformBucketLevelAccessValue', 3)

  class IpFilterValue(_messages.Message):
    r"""The bucket's IP filter configuration. Specifies the network sources
    that are allowed to access the operations on the bucket, as well as its
    underlying objects. Only enforced when the mode is set to 'Enabled'.

    Messages:
      PublicNetworkSourceValue: The public network source of the bucket's IP
        filter.
      VpcNetworkSourcesValueListEntry: A VpcNetworkSourcesValueListEntry
        object.

    Fields:
      allowAllServiceAgentAccess: Whether to allow all service agents to
        access the bucket regardless of the IP filter configuration.
      allowCrossOrgVpcs: Whether to allow cross-org VPCs in the bucket's IP
        filter configuration.
      mode: The mode of the IP filter. Valid values are 'Enabled' and
        'Disabled'.
      publicNetworkSource: The public network source of the bucket's IP
        filter.
      vpcNetworkSources: The list of [VPC
        network](https://cloud.google.com/vpc/docs/vpc) sources of the
        bucket's IP filter.
    """

    class PublicNetworkSourceValue(_messages.Message):
      r"""The public network source of the bucket's IP filter.

      Fields:
        allowedIpCidrRanges: The list of public IPv4, IPv6 cidr ranges that
          are allowed to access the bucket.
      """

      allowedIpCidrRanges = _messages.StringField(1, repeated=True)

    class VpcNetworkSourcesValueListEntry(_messages.Message):
      r"""A VpcNetworkSourcesValueListEntry object.

      Fields:
        allowedIpCidrRanges: The list of IPv4, IPv6 cidr ranges subnetworks
          that are allowed to access the bucket.
        network: Name of the network. Format:
          projects/{PROJECT_ID}/global/networks/{NETWORK_NAME}
      """

      allowedIpCidrRanges = _messages.StringField(1, repeated=True)
      network = _messages.StringField(2)

    allowAllServiceAgentAccess = _messages.BooleanField(1)
    allowCrossOrgVpcs = _messages.BooleanField(2)
    mode = _messages.StringField(3)
    publicNetworkSource = _messages.MessageField('PublicNetworkSourceValue', 4)
    vpcNetworkSources = _messages.MessageField('VpcNetworkSourcesValueListEntry', 5, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-provided labels, in key/value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: An individual label entry.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  class LifecycleValue(_messages.Message):
    r"""The bucket's lifecycle configuration. See [Lifecycle
    Management](https://cloud.google.com/storage/docs/lifecycle) for more
    information.

    Messages:
      RuleValueListEntry: A RuleValueListEntry object.

    Fields:
      rule: A lifecycle management rule, which is made of an action to take
        and the condition(s) under which the action will be taken.
    """

    class RuleValueListEntry(_messages.Message):
      r"""A RuleValueListEntry object.

      Messages:
        ActionValue: The action to take.
        ConditionValue: The condition(s) under which the action will be taken.

      Fields:
        action: The action to take.
        condition: The condition(s) under which the action will be taken.
      """

      class ActionValue(_messages.Message):
        r"""The action to take.

        Fields:
          storageClass: Target storage class. Required iff the type of the
            action is SetStorageClass.
          type: Type of the action. Currently, only Delete, SetStorageClass,
            and AbortIncompleteMultipartUpload are supported.
        """

        storageClass = _messages.StringField(1)
        type = _messages.StringField(2)

      class ConditionValue(_messages.Message):
        r"""The condition(s) under which the action will be taken.

        Fields:
          age: Age of an object (in days). This condition is satisfied when an
            object reaches the specified age.
          createdBefore: A date in RFC 3339 format with only the date part
            (for instance, "2013-01-15"). This condition is satisfied when an
            object is created before midnight of the specified date in UTC.
          customTimeBefore: A date in RFC 3339 format with only the date part
            (for instance, "2013-01-15"). This condition is satisfied when the
            custom time on an object is before this date in UTC.
          daysSinceCustomTime: Number of days elapsed since the user-specified
            timestamp set on an object. The condition is satisfied if the days
            elapsed is at least this number. If no custom timestamp is
            specified on an object, the condition does not apply.
          daysSinceNoncurrentTime: Number of days elapsed since the noncurrent
            timestamp of an object. The condition is satisfied if the days
            elapsed is at least this number. This condition is relevant only
            for versioned objects. The value of the field must be a
            nonnegative integer. If it's zero, the object version will become
            eligible for Lifecycle action as soon as it becomes noncurrent.
          isLive: Relevant only for versioned objects. If the value is true,
            this condition matches live objects; if the value is false, it
            matches archived objects.
          matchesPattern: A regular expression that satisfies the RE2 syntax.
            This condition is satisfied when the name of the object matches
            the RE2 pattern. Note: This feature is currently in the "Early
            Access" launch stage and is only available to a whitelisted set of
            users; that means that this feature may be changed in backward-
            incompatible ways and that it is not guaranteed to be released.
          matchesPrefix: List of object name prefixes. This condition will be
            satisfied when at least one of the prefixes exactly matches the
            beginning of the object name.
          matchesStorageClass: Objects having any of the storage classes
            specified by this condition will be matched. Values include
            MULTI_REGIONAL, REGIONAL, NEARLINE, COLDLINE, ARCHIVE, STANDARD,
            and DURABLE_REDUCED_AVAILABILITY.
          matchesSuffix: List of object name suffixes. This condition will be
            satisfied when at least one of the suffixes exactly matches the
            end of the object name.
          noncurrentTimeBefore: A date in RFC 3339 format with only the date
            part (for instance, "2013-01-15"). This condition is satisfied
            when the noncurrent time on an object is before this date in UTC.
            This condition is relevant only for versioned objects.
          numNewerVersions: Relevant only for versioned objects. If the value
            is N, this condition is satisfied when there are at least N
            versions (including the live version) newer than this version of
            the object.
        """

        age = _messages.IntegerField(1, variant=_messages.Variant.INT32)
        createdBefore = extra_types.DateField(2)
        customTimeBefore = extra_types.DateField(3)
        daysSinceCustomTime = _messages.IntegerField(4, variant=_messages.Variant.INT32)
        daysSinceNoncurrentTime = _messages.IntegerField(5, variant=_messages.Variant.INT32)
        isLive = _messages.BooleanField(6)
        matchesPattern = _messages.StringField(7)
        matchesPrefix = _messages.StringField(8, repeated=True)
        matchesStorageClass = _messages.StringField(9, repeated=True)
        matchesSuffix = _messages.StringField(10, repeated=True)
        noncurrentTimeBefore = extra_types.DateField(11)
        numNewerVersions = _messages.IntegerField(12, variant=_messages.Variant.INT32)

      action = _messages.MessageField('ActionValue', 1)
      condition = _messages.MessageField('ConditionValue', 2)

    rule = _messages.MessageField('RuleValueListEntry', 1, repeated=True)

  class LoggingValue(_messages.Message):
    r"""The bucket's logging configuration, which defines the destination
    bucket and optional name prefix for the current bucket's logs.

    Fields:
      logBucket: The destination bucket where the current bucket's logs should
        be placed.
      logObjectPrefix: A prefix for log object names.
    """

    logBucket = _messages.StringField(1)
    logObjectPrefix = _messages.StringField(2)

  class ObjectRetentionValue(_messages.Message):
    r"""The bucket's object retention config.

    Fields:
      mode: The bucket's object retention mode. Can be Enabled.
    """

    mode = _messages.StringField(1)

  class OwnerValue(_messages.Message):
    r"""The owner of the bucket. This is always the project team's owner
    group.

    Fields:
      entity: The entity, in the form project-owner-projectId.
      entityId: The ID for the entity.
    """

    entity = _messages.StringField(1)
    entityId = _messages.StringField(2)

  class RetentionPolicyValue(_messages.Message):
    r"""The bucket's retention policy. The retention policy enforces a minimum
    retention time for all objects contained in the bucket, based on their
    creation time. Any attempt to overwrite or delete objects younger than the
    retention period will result in a PERMISSION_DENIED error. An unlocked
    retention policy can be modified or removed from the bucket via a
    storage.buckets.update operation. A locked retention policy cannot be
    removed or shortened in duration for the lifetime of the bucket.
    Attempting to remove or decrease period of a locked retention policy will
    result in a PERMISSION_DENIED error.

    Fields:
      effectiveTime: Server-determined value that indicates the time from
        which policy was enforced and effective. This value is in RFC 3339
        format.
      isLocked: Once locked, an object retention policy cannot be modified.
      retentionPeriod: The duration in seconds that objects need to be
        retained. Retention duration must be greater than zero and less than
        100 years. Note that enforcement of retention periods less than a day
        is not guaranteed. Such periods should only be used for testing
        purposes.
    """

    effectiveTime = _message_types.DateTimeField(1)
    isLocked = _messages.BooleanField(2)
    retentionPeriod = _messages.IntegerField(3)

  class SoftDeletePolicyValue(_messages.Message):
    r"""The bucket's soft delete policy, which defines the period of time that
    soft-deleted objects will be retained, and cannot be permanently deleted.

    Fields:
      effectiveTime: Server-determined value that indicates the time from
        which the policy, or one with a greater retention, was effective. This
        value is in RFC 3339 format.
      retentionDurationSeconds: The duration in seconds that soft-deleted
        objects in the bucket will be retained and cannot be permanently
        deleted.
    """

    effectiveTime = _message_types.DateTimeField(1)
    retentionDurationSeconds = _messages.IntegerField(2)

  class VersioningValue(_messages.Message):
    r"""The bucket's versioning configuration.

    Fields:
      enabled: While set to true, versioning is fully enabled for this bucket.
    """

    enabled = _messages.BooleanField(1)

  class WebsiteValue(_messages.Message):
    r"""The bucket's website configuration, controlling how the service
    behaves when accessing bucket contents as a web site. See the [Static
    Website Examples](https://cloud.google.com/storage/docs/static-website)
    for more information.

    Fields:
      mainPageSuffix: If the requested object path is missing, the service
        will ensure the path has a trailing '/', append this suffix, and
        attempt to retrieve the resulting object. This allows the creation of
        index.html objects to represent directory pages.
      notFoundPage: If the requested object path is missing, and any
        mainPageSuffix object is missing, if applicable, the service will
        return the named object from this bucket as the content for a 404 Not
        Found result.
    """

    mainPageSuffix = _messages.StringField(1)
    notFoundPage = _messages.StringField(2)

  acl = _messages.MessageField('BucketAccessControl', 1, repeated=True)
  autoclass = _messages.MessageField('AutoclassValue', 2)
  billing = _messages.MessageField('BillingValue', 3)
  cors = _messages.MessageField('CorsValueListEntry', 4, repeated=True)
  customPlacementConfig = _messages.MessageField('CustomPlacementConfigValue', 5)
  defaultEventBasedHold = _messages.BooleanField(6)
  defaultObjectAcl = _messages.MessageField('ObjectAccessControl', 7, repeated=True)
  encryption = _messages.MessageField('EncryptionValue', 8)
  etag = _messages.StringField(9)
  generation = _messages.IntegerField(10)
  hardDeleteTime = _message_types.DateTimeField(11)
  hierarchicalNamespace = _messages.MessageField('HierarchicalNamespaceValue', 12)
  iamConfiguration = _messages.MessageField('IamConfigurationValue', 13)
  id = _messages.StringField(14)
  ipFilter = _messages.MessageField('IpFilterValue', 15)
  kind = _messages.StringField(16, default='storage#bucket')
  labels = _messages.MessageField('LabelsValue', 17)
  lifecycle = _messages.MessageField('LifecycleValue', 18)
  location = _messages.StringField(19)
  locationType = _messages.StringField(20)
  logging = _messages.MessageField('LoggingValue', 21)
  metageneration = _messages.IntegerField(22)
  name = _messages.StringField(23)
  objectRetention = _messages.MessageField('ObjectRetentionValue', 24)
  owner = _messages.MessageField('OwnerValue', 25)
  projectNumber = _messages.IntegerField(26, variant=_messages.Variant.UINT64)
  retentionPolicy = _messages.MessageField('RetentionPolicyValue', 27)
  rpo = _messages.StringField(28)
  satisfiesPZI = _messages.BooleanField(29)
  satisfiesPZS = _messages.BooleanField(30)
  selfLink = _messages.StringField(31)
  softDeletePolicy = _messages.MessageField('SoftDeletePolicyValue', 32)
  softDeleteTime = _message_types.DateTimeField(33)
  storageClass = _messages.StringField(34)
  timeCreated = _message_types.DateTimeField(35)
  updated = _message_types.DateTimeField(36)
  versioning = _messages.MessageField('VersioningValue', 37)
  website = _messages.MessageField('WebsiteValue', 38)


class BucketAccessControl(_messages.Message):
  r"""An access-control entry.

  Messages:
    ProjectTeamValue: The project team associated with the entity, if any.

  Fields:
    bucket: The name of the bucket.
    domain: The domain associated with the entity, if any.
    email: The email address associated with the entity, if any.
    entity: The entity holding the permission, in one of the following forms:
      - user-userId  - user-email  - group-groupId  - group-email  - domain-
      domain  - project-team-projectId  - allUsers  - allAuthenticatedUsers
      Examples:  - <NAME_EMAIL> <NAME_EMAIL>.  -
      <NAME_EMAIL> would be group-
      <EMAIL>.  - To refer to all members of the Google Apps
      for Business domain example.com, the entity would be domain-example.com.
    entityId: The ID for the entity, if any.
    etag: HTTP 1.1 Entity tag for the access-control entry.
    id: The ID of the access-control entry.
    kind: The kind of item this is. For bucket access control entries, this is
      always storage#bucketAccessControl.
    projectTeam: The project team associated with the entity, if any.
    role: The access permission for the entity.
    selfLink: The link to this access-control entry.
  """

  class ProjectTeamValue(_messages.Message):
    r"""The project team associated with the entity, if any.

    Fields:
      projectNumber: The project number.
      team: The team.
    """

    projectNumber = _messages.StringField(1)
    team = _messages.StringField(2)

  bucket = _messages.StringField(1)
  domain = _messages.StringField(2)
  email = _messages.StringField(3)
  entity = _messages.StringField(4)
  entityId = _messages.StringField(5)
  etag = _messages.StringField(6)
  id = _messages.StringField(7)
  kind = _messages.StringField(8, default='storage#bucketAccessControl')
  projectTeam = _messages.MessageField('ProjectTeamValue', 9)
  role = _messages.StringField(10)
  selfLink = _messages.StringField(11)


class BucketAccessControls(_messages.Message):
  r"""An access-control list.

  Fields:
    items: The list of items.
    kind: The kind of item this is. For lists of bucket access control
      entries, this is always storage#bucketAccessControls.
  """

  items = _messages.MessageField('BucketAccessControl', 1, repeated=True)
  kind = _messages.StringField(2, default='storage#bucketAccessControls')


class BucketStorageLayout(_messages.Message):
  r"""The storage layout configuration of a bucket.

  Messages:
    CustomPlacementConfigValue: The bucket's custom placement configuration
      for Custom Dual Regions.
    HierarchicalNamespaceValue: The bucket's hierarchical namespace
      configuration.

  Fields:
    bucket: The name of the bucket.
    customPlacementConfig: The bucket's custom placement configuration for
      Custom Dual Regions.
    hierarchicalNamespace: The bucket's hierarchical namespace configuration.
    kind: The kind of item this is. For storage layout, this is always
      storage#storageLayout.
    location: The location of the bucket.
    locationType: The type of the bucket location.
  """

  class CustomPlacementConfigValue(_messages.Message):
    r"""The bucket's custom placement configuration for Custom Dual Regions.

    Fields:
      dataLocations: The list of regional locations in which data is placed.
    """

    dataLocations = _messages.StringField(1, repeated=True)

  class HierarchicalNamespaceValue(_messages.Message):
    r"""The bucket's hierarchical namespace configuration.

    Fields:
      enabled: When set to true, hierarchical namespace is enabled for this
        bucket.
    """

    enabled = _messages.BooleanField(1)

  bucket = _messages.StringField(1)
  customPlacementConfig = _messages.MessageField('CustomPlacementConfigValue', 2)
  hierarchicalNamespace = _messages.MessageField('HierarchicalNamespaceValue', 3)
  kind = _messages.StringField(4, default='storage#storageLayout')
  location = _messages.StringField(5)
  locationType = _messages.StringField(6)


class Buckets(_messages.Message):
  r"""A list of buckets.

  Fields:
    items: The list of items.
    kind: The kind of item this is. For lists of buckets, this is always
      storage#buckets.
    nextPageToken: The continuation token, used to page through large result
      sets. Provide this value in a subsequent request to return the next page
      of results.
  """

  items = _messages.MessageField('Bucket', 1, repeated=True)
  kind = _messages.StringField(2, default='storage#buckets')
  nextPageToken = _messages.StringField(3)


class BulkRestoreObjectsRequest(_messages.Message):
  r"""A bulk restore objects request.

  Fields:
    allowOverwrite: If false (default), the restore will not overwrite live
      objects with the same name at the destination. This means some deleted
      objects may be skipped. If true, live objects will be overwritten
      resulting in a noncurrent object (if versioning is enabled). If
      versioning is not enabled, overwriting the object will result in a soft-
      deleted object. In either case, if a noncurrent object already exists
      with the same name, a live version can be written without issue.
    copySourceAcl: If true, copies the source object's ACL; otherwise, uses
      the bucket's default object ACL. The default is false.
    matchGlobs: Restores only the objects matching any of the specified
      glob(s). If this parameter is not specified, all objects will be
      restored within the specified time range.
    softDeletedAfterTime: Restores only the objects that were soft-deleted
      after this time.
    softDeletedBeforeTime: Restores only the objects that were soft-deleted
      before this time.
  """

  allowOverwrite = _messages.BooleanField(1)
  copySourceAcl = _messages.BooleanField(2)
  matchGlobs = _messages.StringField(3, repeated=True)
  softDeletedAfterTime = _message_types.DateTimeField(4)
  softDeletedBeforeTime = _message_types.DateTimeField(5)


class Channel(_messages.Message):
  r"""An notification channel used to watch for resource changes.

  Messages:
    ParamsValue: Additional parameters controlling delivery channel behavior.
      Optional.

  Fields:
    address: The address where notifications are delivered for this channel.
    expiration: Date and time of notification channel expiration, expressed as
      a Unix timestamp, in milliseconds. Optional.
    id: A UUID or similar unique string that identifies this channel.
    kind: Identifies this as a notification channel used to watch for changes
      to a resource, which is "api#channel".
    params: Additional parameters controlling delivery channel behavior.
      Optional.
    payload: A Boolean value to indicate whether payload is wanted. Optional.
    resourceId: An opaque ID that identifies the resource being watched on
      this channel. Stable across different API versions.
    resourceUri: A version-specific identifier for the watched resource.
    token: An arbitrary string delivered to the target address with each
      notification delivered over this channel. Optional.
    type: The type of delivery mechanism used for this channel.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParamsValue(_messages.Message):
    r"""Additional parameters controlling delivery channel behavior. Optional.

    Messages:
      AdditionalProperty: An additional property for a ParamsValue object.

    Fields:
      additionalProperties: Declares a new parameter by name.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParamsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  address = _messages.StringField(1)
  expiration = _messages.IntegerField(2)
  id = _messages.StringField(3)
  kind = _messages.StringField(4, default='api#channel')
  params = _messages.MessageField('ParamsValue', 5)
  payload = _messages.BooleanField(6)
  resourceId = _messages.StringField(7)
  resourceUri = _messages.StringField(8)
  token = _messages.StringField(9)
  type = _messages.StringField(10)


class ComposeRequest(_messages.Message):
  r"""A Compose request.

  Messages:
    SourceObjectsValueListEntry: A SourceObjectsValueListEntry object.

  Fields:
    destination: Properties of the resulting object.
    kind: The kind of item this is.
    sourceObjects: The list of source objects that will be concatenated into a
      single object.
  """

  class SourceObjectsValueListEntry(_messages.Message):
    r"""A SourceObjectsValueListEntry object.

    Messages:
      ObjectPreconditionsValue: Conditions that must be met for this operation
        to execute.

    Fields:
      generation: The generation of this object to use as the source.
      name: The source object's name. All source objects must reside in the
        same bucket.
      objectPreconditions: Conditions that must be met for this operation to
        execute.
    """

    class ObjectPreconditionsValue(_messages.Message):
      r"""Conditions that must be met for this operation to execute.

      Fields:
        ifGenerationMatch: Only perform the composition if the generation of
          the source object that would be used matches this value. If this
          value and a generation are both specified, they must be the same
          value or the call will fail.
      """

      ifGenerationMatch = _messages.IntegerField(1)

    generation = _messages.IntegerField(1)
    name = _messages.StringField(2)
    objectPreconditions = _messages.MessageField('ObjectPreconditionsValue', 3)

  destination = _messages.MessageField('Object', 1)
  kind = _messages.StringField(2, default='storage#composeRequest')
  sourceObjects = _messages.MessageField('SourceObjectsValueListEntry', 3, repeated=True)


class Expr(_messages.Message):
  r"""Represents an expression text. Example: title: "User account presence"
  description: "Determines whether the request has a user account" expression:
  "size(request.user) > 0"

  Fields:
    description: An optional description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax. The application context of the containing message
      determines which well-known feature set of CEL is supported.
    location: An optional string indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: An optional title for the expression, i.e. a short string
      describing its purpose. This can be used e.g. in UIs which allow to
      enter the expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Folder(_messages.Message):
  r"""A folder. Only available in buckets with hierarchical namespace enabled.

  Messages:
    PendingRenameInfoValue: Only present if the folder is part of an ongoing
      rename folder operation. Contains information which can be used to query
      the operation status.

  Fields:
    bucket: The name of the bucket containing this folder.
    createTime: The creation time of the folder in RFC 3339 format.
    id: The ID of the folder, including the bucket name, folder name.
    kind: The kind of item this is. For folders, this is always
      storage#folder.
    metageneration: The version of the metadata for this folder. Used for
      preconditions and for detecting changes in metadata.
    name: The name of the folder. Required if not specified by URL parameter.
    pendingRenameInfo: Only present if the folder is part of an ongoing rename
      folder operation. Contains information which can be used to query the
      operation status.
    selfLink: The link to this folder.
    updateTime: The modification time of the folder metadata in RFC 3339
      format.
  """

  class PendingRenameInfoValue(_messages.Message):
    r"""Only present if the folder is part of an ongoing rename folder
    operation. Contains information which can be used to query the operation
    status.

    Fields:
      operationId: The ID of the rename folder operation.
    """

    operationId = _messages.StringField(1)

  bucket = _messages.StringField(1)
  createTime = _message_types.DateTimeField(2)
  id = _messages.StringField(3)
  kind = _messages.StringField(4, default='storage#folder')
  metageneration = _messages.IntegerField(5)
  name = _messages.StringField(6)
  pendingRenameInfo = _messages.MessageField('PendingRenameInfoValue', 7)
  selfLink = _messages.StringField(8)
  updateTime = _message_types.DateTimeField(9)


class Folders(_messages.Message):
  r"""A list of folders.

  Fields:
    items: The list of items.
    kind: The kind of item this is. For lists of folders, this is always
      storage#folders.
    nextPageToken: The continuation token, used to page through large result
      sets. Provide this value in a subsequent request to return the next page
      of results.
  """

  items = _messages.MessageField('Folder', 1, repeated=True)
  kind = _messages.StringField(2, default='storage#folders')
  nextPageToken = _messages.StringField(3)


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for storage.buckets.operations.list.

  Fields:
    kind: The kind of item this is. For lists of operations, this is always
      storage#operations.
    nextPageToken: The continuation token, used to page through large result
      sets. Provide this value in a subsequent request to return the next page
      of results.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  kind = _messages.StringField(1, default='storage#operations')
  nextPageToken = _messages.StringField(2)
  operations = _messages.MessageField('GoogleLongrunningOperation', 3, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal response of the operation in case of success. If
      the original method returns no data on success, such as "Delete", the
      response is google.protobuf.Empty. If the original method is standard
      Get/Create/Update, the response should be the resource. For other
      methods, the response should have the type "XxxResponse", where "Xxx" is
      the original method name. For example, if the original method name is
      "TakeSnapshot()", the inferred response type is "TakeSnapshotResponse".

  Fields:
    done: If the value is "false", it means the operation is still in
      progress. If "true", the operation is completed, and either "error" or
      "response" is available.
    error: The error result of the operation in case of failure or
      cancellation.
    kind: The kind of item this is. For operations, this is always
      storage#operation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the "name" should be a resource name ending with
      "operations/{operationId}".
    response: The normal response of the operation in case of success. If the
      original method returns no data on success, such as "Delete", the
      response is google.protobuf.Empty. If the original method is standard
      Get/Create/Update, the response should be the resource. For other
      methods, the response should have the type "XxxResponse", where "Xxx" is
      the original method name. For example, if the original method name is
      "TakeSnapshot()", the inferred response type is "TakeSnapshotResponse".
    selfLink: The link to this long running operation.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal response of the operation in case of success. If the
    original method returns no data on success, such as "Delete", the response
    is google.protobuf.Empty. If the original method is standard
    Get/Create/Update, the response should be the resource. For other methods,
    the response should have the type "XxxResponse", where "Xxx" is the
    original method name. For example, if the original method name is
    "TakeSnapshot()", the inferred response type is "TakeSnapshotResponse".

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  kind = _messages.StringField(3, default='storage#operation')
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)
  response = _messages.MessageField('ResponseValue', 6)
  selfLink = _messages.StringField(7)


class GoogleRpcStatus(_messages.Message):
  r"""The "Status" type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each "Status" message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class HmacKey(_messages.Message):
  r"""JSON template to produce a JSON-style HMAC Key resource for Create
  responses.

  Fields:
    kind: The kind of item this is. For HMAC keys, this is always
      storage#hmacKey.
    metadata: Key metadata.
    secret: HMAC secret key material.
  """

  kind = _messages.StringField(1, default='storage#hmacKey')
  metadata = _messages.MessageField('HmacKeyMetadata', 2)
  secret = _messages.StringField(3)


class HmacKeyMetadata(_messages.Message):
  r"""JSON template to produce a JSON-style HMAC Key metadata resource.

  Fields:
    accessId: The ID of the HMAC Key.
    etag: HTTP 1.1 Entity tag for the HMAC key.
    id: The ID of the HMAC key, including the Project ID and the Access ID.
    kind: The kind of item this is. For HMAC Key metadata, this is always
      storage#hmacKeyMetadata.
    projectId: Project ID owning the service account to which the key
      authenticates.
    selfLink: The link to this resource.
    serviceAccountEmail: The email address of the key's associated service
      account.
    state: The state of the key. Can be one of ACTIVE, INACTIVE, or DELETED.
    timeCreated: The creation time of the HMAC key in RFC 3339 format.
    updated: The last modification time of the HMAC key metadata in RFC 3339
      format.
  """

  accessId = _messages.StringField(1)
  etag = _messages.StringField(2)
  id = _messages.StringField(3)
  kind = _messages.StringField(4, default='storage#hmacKeyMetadata')
  projectId = _messages.StringField(5)
  selfLink = _messages.StringField(6)
  serviceAccountEmail = _messages.StringField(7)
  state = _messages.StringField(8)
  timeCreated = _message_types.DateTimeField(9)
  updated = _message_types.DateTimeField(10)


class HmacKeysMetadata(_messages.Message):
  r"""A list of hmacKeys.

  Fields:
    items: The list of items.
    kind: The kind of item this is. For lists of hmacKeys, this is always
      storage#hmacKeysMetadata.
    nextPageToken: The continuation token, used to page through large result
      sets. Provide this value in a subsequent request to return the next page
      of results.
  """

  items = _messages.MessageField('HmacKeyMetadata', 1, repeated=True)
  kind = _messages.StringField(2, default='storage#hmacKeysMetadata')
  nextPageToken = _messages.StringField(3)


class ManagedFolder(_messages.Message):
  r"""A managed folder.

  Fields:
    bucket: The name of the bucket containing this managed folder.
    createTime: The creation time of the managed folder in RFC 3339 format.
    id: The ID of the managed folder, including the bucket name and managed
      folder name.
    kind: The kind of item this is. For managed folders, this is always
      storage#managedFolder.
    metageneration: The version of the metadata for this managed folder. Used
      for preconditions and for detecting changes in metadata.
    name: The name of the managed folder. Required if not specified by URL
      parameter.
    selfLink: The link to this managed folder.
    updateTime: The last update time of the managed folder metadata in RFC
      3339 format.
  """

  bucket = _messages.StringField(1)
  createTime = _message_types.DateTimeField(2)
  id = _messages.StringField(3)
  kind = _messages.StringField(4, default='storage#managedFolder')
  metageneration = _messages.IntegerField(5)
  name = _messages.StringField(6)
  selfLink = _messages.StringField(7)
  updateTime = _message_types.DateTimeField(8)


class ManagedFolders(_messages.Message):
  r"""A list of managed folders.

  Fields:
    items: The list of items.
    kind: The kind of item this is. For lists of managed folders, this is
      always storage#managedFolders.
    nextPageToken: The continuation token, used to page through large result
      sets. Provide this value in a subsequent request to return the next page
      of results.
  """

  items = _messages.MessageField('ManagedFolder', 1, repeated=True)
  kind = _messages.StringField(2, default='storage#managedFolders')
  nextPageToken = _messages.StringField(3)


class Notification(_messages.Message):
  r"""A subscription to receive Google PubSub notifications.

  Messages:
    CustomAttributesValue: An optional list of additional attributes to attach
      to each Cloud PubSub message published for this notification
      subscription.

  Fields:
    custom_attributes: An optional list of additional attributes to attach to
      each Cloud PubSub message published for this notification subscription.
    etag: HTTP 1.1 Entity tag for this subscription notification.
    event_types: If present, only send notifications about listed event types.
      If empty, sent notifications for all event types.
    id: The ID of the notification.
    kind: The kind of item this is. For notifications, this is always
      storage#notification.
    object_name_prefix: If present, only apply this notification configuration
      to object names that begin with this prefix.
    payload_format: The desired content of the Payload.
    selfLink: The canonical URL of this notification.
    topic: The Cloud PubSub topic to which this subscription publishes.
      Formatted as: '//pubsub.googleapis.com/projects/{project-
      identifier}/topics/{my-topic}'
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomAttributesValue(_messages.Message):
    r"""An optional list of additional attributes to attach to each Cloud
    PubSub message published for this notification subscription.

    Messages:
      AdditionalProperty: An additional property for a CustomAttributesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        CustomAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  custom_attributes = _messages.MessageField('CustomAttributesValue', 1)
  etag = _messages.StringField(2)
  event_types = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)
  kind = _messages.StringField(5, default='storage#notification')
  object_name_prefix = _messages.StringField(6)
  payload_format = _messages.StringField(7, default='JSON_API_V1')
  selfLink = _messages.StringField(8)
  topic = _messages.StringField(9)


class Notifications(_messages.Message):
  r"""A list of notification subscriptions.

  Fields:
    items: The list of items.
    kind: The kind of item this is. For lists of notifications, this is always
      storage#notifications.
  """

  items = _messages.MessageField('Notification', 1, repeated=True)
  kind = _messages.StringField(2, default='storage#notifications')


class Object(_messages.Message):
  r"""An object.

  Messages:
    ContextsValue: User-defined or system-defined object contexts. Each object
      context is a key-payload pair, where the key provides the identification
      and the payload holds the associated value and additional metadata.
    CustomerEncryptionValue: Metadata of customer-supplied encryption key, if
      the object is encrypted by such a key.
    MetadataValue: User-provided metadata, in key/value pairs.
    OwnerValue: The owner of the object. This will always be the uploader of
      the object.
    RetentionValue: A collection of object level retention parameters.

  Fields:
    acl: Access controls on the object.
    bucket: The name of the bucket containing this object.
    cacheControl: Cache-Control directive for the object data. If omitted, and
      the object is accessible to all anonymous users, the default will be
      public, max-age=3600.
    componentCount: Number of underlying components that make up this object.
      Components are accumulated by compose operations.
    contentDisposition: Content-Disposition of the object data.
    contentEncoding: Content-Encoding of the object data.
    contentLanguage: Content-Language of the object data.
    contentType: Content-Type of the object data. If an object is stored
      without a Content-Type, it is served as application/octet-stream.
    contexts: User-defined or system-defined object contexts. Each object
      context is a key-payload pair, where the key provides the identification
      and the payload holds the associated value and additional metadata.
    crc32c: CRC32c checksum, as described in RFC 4960, Appendix B; encoded
      using base64 in big-endian byte order. For more information about using
      the CRC32c checksum, see [Data Validation and Change
      Detection](https://cloud.google.com/storage/docs/data-validation).
    customTime: A timestamp in RFC 3339 format specified by the user for an
      object.
    customerEncryption: Metadata of customer-supplied encryption key, if the
      object is encrypted by such a key.
    etag: HTTP 1.1 Entity tag for the object.
    eventBasedHold: Whether an object is under event-based hold. Event-based
      hold is a way to retain objects until an event occurs, which is
      signified by the hold's release (i.e. this value is set to false). After
      being released (set to false), such objects will be subject to bucket-
      level retention (if any). One sample use case of this flag is for banks
      to hold loan documents for at least 3 years after loan is paid in full.
      Here, bucket-level retention is 3 years and the event is the loan being
      paid in full. In this example, these objects will be held intact for any
      number of years until the event has occurred (event-based hold on the
      object is released) and then 3 more years after that. That means
      retention duration of the objects begins from the moment event-based
      hold transitioned from true to false.
    generation: The content generation of this object. Used for object
      versioning.
    hardDeleteTime: This is the time (in the future) when the soft-deleted
      object will no longer be restorable. It is equal to the soft delete time
      plus the current soft delete retention duration of the bucket.
    id: The ID of the object, including the bucket name, object name, and
      generation number.
    kind: The kind of item this is. For objects, this is always
      storage#object.
    kmsKeyName: Not currently supported. Specifying the parameter causes the
      request to fail with status code 400 - Bad Request.
    md5Hash: MD5 hash of the data; encoded using base64. For more information
      about using the MD5 hash, see [Data Validation and Change
      Detection](https://cloud.google.com/storage/docs/data-validation).
    mediaLink: Media download link.
    metadata: User-provided metadata, in key/value pairs.
    metageneration: The version of the metadata for this object at this
      generation. Used for preconditions and for detecting changes in
      metadata. A metageneration number is only meaningful in the context of a
      particular generation of a particular object.
    name: The name of the object. Required if not specified by URL parameter.
    owner: The owner of the object. This will always be the uploader of the
      object.
    restoreToken: Restore token used to differentiate deleted objects with the
      same name and generation. This field is only returned for deleted
      objects in hierarchical namespace buckets.
    retention: A collection of object level retention parameters.
    retentionExpirationTime: A server-determined value that specifies the
      earliest time that the object's retention period expires. This value is
      in RFC 3339 format. Note 1: This field is not provided for objects with
      an active event-based hold, since retention expiration is unknown until
      the hold is removed. Note 2: This value can be provided even when
      temporary hold is set (so that the user can reason about policy without
      having to first unset the temporary hold).
    selfLink: The link to this object.
    size: Content-Length of the data in bytes.
    softDeleteTime: The time at which the object became soft-deleted in RFC
      3339 format.
    storageClass: Storage class of the object.
    temporaryHold: Whether an object is under temporary hold. While this flag
      is set to true, the object is protected against deletion and overwrites.
      A common use case of this flag is regulatory investigations where
      objects need to be retained while the investigation is ongoing. Note
      that unlike event-based hold, temporary hold does not impact retention
      expiration time of an object.
    timeCreated: The creation time of the object in RFC 3339 format.
    timeDeleted: The time at which the object became noncurrent in RFC 3339
      format. Will be returned if and only if this version of the object has
      been deleted.
    timeFinalized: The time when the object was finalized.
    timeStorageClassUpdated: The time at which the object's storage class was
      last changed. When the object is initially created, it will be set to
      timeCreated.
    updated: The modification time of the object metadata in RFC 3339 format.
      Set initially to object creation time and then updated whenever any
      metadata of the object changes. This includes changes made by a
      requester, such as modifying custom metadata, as well as changes made by
      Cloud Storage on behalf of a requester, such as changing the storage
      class based on an Object Lifecycle Configuration.
  """

  class ContextsValue(_messages.Message):
    r"""User-defined or system-defined object contexts. Each object context is
    a key-payload pair, where the key provides the identification and the
    payload holds the associated value and additional metadata.

    Messages:
      CustomValue: User-defined object contexts.

    Fields:
      custom: User-defined object contexts.
    """

    @encoding.MapUnrecognizedFields('additionalProperties')
    class CustomValue(_messages.Message):
      r"""User-defined object contexts.

      Messages:
        AdditionalProperty: An additional property for a CustomValue object.

      Fields:
        additionalProperties: A single user-defined object context.
      """

      class AdditionalProperty(_messages.Message):
        r"""An additional property for a CustomValue object.

        Fields:
          key: Name of the additional property.
          value: A ObjectCustomContextPayload attribute.
        """

        key = _messages.StringField(1)
        value = _messages.MessageField('ObjectCustomContextPayload', 2)

      additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

    custom = _messages.MessageField('CustomValue', 1)

  class CustomerEncryptionValue(_messages.Message):
    r"""Metadata of customer-supplied encryption key, if the object is
    encrypted by such a key.

    Fields:
      encryptionAlgorithm: The encryption algorithm.
      keySha256: SHA256 hash value of the encryption key.
    """

    encryptionAlgorithm = _messages.StringField(1)
    keySha256 = _messages.StringField(2)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""User-provided metadata, in key/value pairs.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: An individual metadata entry.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  class OwnerValue(_messages.Message):
    r"""The owner of the object. This will always be the uploader of the
    object.

    Fields:
      entity: The entity, in the form user-userId.
      entityId: The ID for the entity.
    """

    entity = _messages.StringField(1)
    entityId = _messages.StringField(2)

  class RetentionValue(_messages.Message):
    r"""A collection of object level retention parameters.

    Fields:
      mode: The bucket's object retention mode, can only be Unlocked or
        Locked.
      retainUntilTime: A time in RFC 3339 format until which object retention
        protects this object.
    """

    mode = _messages.StringField(1)
    retainUntilTime = _message_types.DateTimeField(2)

  acl = _messages.MessageField('ObjectAccessControl', 1, repeated=True)
  bucket = _messages.StringField(2)
  cacheControl = _messages.StringField(3)
  componentCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  contentDisposition = _messages.StringField(5)
  contentEncoding = _messages.StringField(6)
  contentLanguage = _messages.StringField(7)
  contentType = _messages.StringField(8)
  contexts = _messages.MessageField('ContextsValue', 9)
  crc32c = _messages.StringField(10)
  customTime = _message_types.DateTimeField(11)
  customerEncryption = _messages.MessageField('CustomerEncryptionValue', 12)
  etag = _messages.StringField(13)
  eventBasedHold = _messages.BooleanField(14)
  generation = _messages.IntegerField(15)
  hardDeleteTime = _message_types.DateTimeField(16)
  id = _messages.StringField(17)
  kind = _messages.StringField(18, default='storage#object')
  kmsKeyName = _messages.StringField(19)
  md5Hash = _messages.StringField(20)
  mediaLink = _messages.StringField(21)
  metadata = _messages.MessageField('MetadataValue', 22)
  metageneration = _messages.IntegerField(23)
  name = _messages.StringField(24)
  owner = _messages.MessageField('OwnerValue', 25)
  restoreToken = _messages.StringField(26)
  retention = _messages.MessageField('RetentionValue', 27)
  retentionExpirationTime = _message_types.DateTimeField(28)
  selfLink = _messages.StringField(29)
  size = _messages.IntegerField(30, variant=_messages.Variant.UINT64)
  softDeleteTime = _message_types.DateTimeField(31)
  storageClass = _messages.StringField(32)
  temporaryHold = _messages.BooleanField(33)
  timeCreated = _message_types.DateTimeField(34)
  timeDeleted = _message_types.DateTimeField(35)
  timeFinalized = _message_types.DateTimeField(36)
  timeStorageClassUpdated = _message_types.DateTimeField(37)
  updated = _message_types.DateTimeField(38)


class ObjectAccessControl(_messages.Message):
  r"""An access-control entry.

  Messages:
    ProjectTeamValue: The project team associated with the entity, if any.

  Fields:
    bucket: The name of the bucket.
    domain: The domain associated with the entity, if any.
    email: The email address associated with the entity, if any.
    entity: The entity holding the permission, in one of the following forms:
      - user-userId  - user-email  - group-groupId  - group-email  - domain-
      domain  - project-team-projectId  - allUsers  - allAuthenticatedUsers
      Examples:  - <NAME_EMAIL> <NAME_EMAIL>.  -
      <NAME_EMAIL> would be group-
      <EMAIL>.  - To refer to all members of the Google Apps
      for Business domain example.com, the entity would be domain-example.com.
    entityId: The ID for the entity, if any.
    etag: HTTP 1.1 Entity tag for the access-control entry.
    generation: The content generation of the object, if applied to an object.
    id: The ID of the access-control entry.
    kind: The kind of item this is. For object access control entries, this is
      always storage#objectAccessControl.
    object: The name of the object, if applied to an object.
    projectTeam: The project team associated with the entity, if any.
    role: The access permission for the entity.
    selfLink: The link to this access-control entry.
  """

  class ProjectTeamValue(_messages.Message):
    r"""The project team associated with the entity, if any.

    Fields:
      projectNumber: The project number.
      team: The team.
    """

    projectNumber = _messages.StringField(1)
    team = _messages.StringField(2)

  bucket = _messages.StringField(1)
  domain = _messages.StringField(2)
  email = _messages.StringField(3)
  entity = _messages.StringField(4)
  entityId = _messages.StringField(5)
  etag = _messages.StringField(6)
  generation = _messages.IntegerField(7)
  id = _messages.StringField(8)
  kind = _messages.StringField(9, default='storage#objectAccessControl')
  object = _messages.StringField(10)
  projectTeam = _messages.MessageField('ProjectTeamValue', 11)
  role = _messages.StringField(12)
  selfLink = _messages.StringField(13)


class ObjectAccessControls(_messages.Message):
  r"""An access-control list.

  Fields:
    items: The list of items.
    kind: The kind of item this is. For lists of object access control
      entries, this is always storage#objectAccessControls.
  """

  items = _messages.MessageField('ObjectAccessControl', 1, repeated=True)
  kind = _messages.StringField(2, default='storage#objectAccessControls')


class ObjectCustomContextPayload(_messages.Message):
  r"""The payload of a single user-defined object context.

  Fields:
    createTime: The time at which the object context was created in RFC 3339
      format.
    updateTime: The time at which the object context was last updated in RFC
      3339 format.
    value: The value of the object context.
  """

  createTime = _message_types.DateTimeField(1)
  updateTime = _message_types.DateTimeField(2)
  value = _messages.StringField(3)


class Objects(_messages.Message):
  r"""A list of objects.

  Fields:
    items: The list of items.
    kind: The kind of item this is. For lists of objects, this is always
      storage#objects.
    nextPageToken: The continuation token, used to page through large result
      sets. Provide this value in a subsequent request to return the next page
      of results.
    prefixes: The list of prefixes of objects matching-but-not-listed up to
      and including the requested delimiter.
  """

  items = _messages.MessageField('Object', 1, repeated=True)
  kind = _messages.StringField(2, default='storage#objects')
  nextPageToken = _messages.StringField(3)
  prefixes = _messages.StringField(4, repeated=True)


class Policy(_messages.Message):
  r"""A bucket/object/managedFolder IAM policy.

  Messages:
    BindingsValueListEntry: A BindingsValueListEntry object.

  Fields:
    bindings: An association between a role, which comes with a set of
      permissions, and members who may assume that role.
    etag: HTTP 1.1  Entity tag for the policy.
    kind: The kind of item this is. For policies, this is always
      storage#policy. This field is ignored on input.
    resourceId: The ID of the resource to which this policy belongs. Will be
      of the form projects/_/buckets/bucket for buckets,
      projects/_/buckets/bucket/objects/object for objects, and
      projects/_/buckets/bucket/managedFolders/managedFolder. A specific
      generation may be specified by appending #generationNumber to the end of
      the object name, e.g. projects/_/buckets/my-bucket/objects/data.txt#17.
      The current generation can be denoted with #0. This field is ignored on
      input.
    version: The IAM policy format version.
  """

  class BindingsValueListEntry(_messages.Message):
    r"""A BindingsValueListEntry object.

    Fields:
      condition: The condition that is associated with this binding. NOTE: an
        unsatisfied condition will not allow user access via current binding.
        Different bindings, including their conditions, are examined
        independently.
      members: A collection of identifiers for members who may assume the
        provided role. Recognized identifiers are as follows:   - allUsers - A
        special identifier that represents anyone on the internet; with or
        without a Google account.   - allAuthenticatedUsers - A special
        identifier that represents anyone who is authenticated with a Google
        account or a service account.   - user:emailid - An email address that
        represents a specific account. For example, user:<EMAIL> or
        user:<EMAIL>.   - serviceAccount:emailid - An email address
        that represents a service account. For example,  serviceAccount:my-
        <EMAIL> .   - group:emailid - An email
        address that represents a Google group. For example,
        group:<EMAIL>.   - domain:domain - A Google Apps domain
        name that represents all the users of that domain. For example,
        domain:google.com or domain:example.com.   - projectOwner:projectid -
        Owners of the given project. For example, projectOwner:my-example-
        project   - projectEditor:projectid - Editors of the given project.
        For example, projectEditor:my-example-project   -
        projectViewer:projectid - Viewers of the given project. For example,
        projectViewer:my-example-project
      role: The role to which members belong. Two types of roles are
        supported: new IAM roles, which grant permissions that do not map
        directly to those provided by ACLs, and legacy IAM roles, which do map
        directly to ACL permissions. All roles are of the format
        roles/storage.specificRole. The new IAM roles are:   -
        roles/storage.admin - Full control of Google Cloud Storage resources.
        - roles/storage.objectViewer - Read-Only access to Google Cloud
        Storage objects.   - roles/storage.objectCreator - Access to create
        objects in Google Cloud Storage.   - roles/storage.objectAdmin - Full
        control of Google Cloud Storage objects.   The legacy IAM roles are:
        - roles/storage.legacyObjectReader - Read-only access to objects
        without listing. Equivalent to an ACL entry on an object with the
        READER role.   - roles/storage.legacyObjectOwner - Read/write access
        to existing objects without listing. Equivalent to an ACL entry on an
        object with the OWNER role.   - roles/storage.legacyBucketReader -
        Read access to buckets with object listing. Equivalent to an ACL entry
        on a bucket with the READER role.   - roles/storage.legacyBucketWriter
        - Read access to buckets with object listing/creation/deletion.
        Equivalent to an ACL entry on a bucket with the WRITER role.   -
        roles/storage.legacyBucketOwner - Read and write access to existing
        buckets with object listing/creation/deletion. Equivalent to an ACL
        entry on a bucket with the OWNER role.
    """

    condition = _messages.MessageField('Expr', 1)
    members = _messages.StringField(2, repeated=True)
    role = _messages.StringField(3)

  bindings = _messages.MessageField('BindingsValueListEntry', 1, repeated=True)
  etag = _messages.BytesField(2)
  kind = _messages.StringField(3, default='storage#policy')
  resourceId = _messages.StringField(4)
  version = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class RelocateBucketRequest(_messages.Message):
  r"""A Relocate Bucket request.

  Messages:
    DestinationCustomPlacementConfigValue: The bucket's new custom placement
      configuration if relocating to a Custom Dual Region.

  Fields:
    destinationCustomPlacementConfig: The bucket's new custom placement
      configuration if relocating to a Custom Dual Region.
    destinationLocation: The new location the bucket will be relocated to.
    validateOnly: If true, validate the operation, but do not actually
      relocate the bucket.
  """

  class DestinationCustomPlacementConfigValue(_messages.Message):
    r"""The bucket's new custom placement configuration if relocating to a
    Custom Dual Region.

    Fields:
      dataLocations: The list of regional locations in which data is placed.
    """

    dataLocations = _messages.StringField(1, repeated=True)

  destinationCustomPlacementConfig = _messages.MessageField('DestinationCustomPlacementConfigValue', 1)
  destinationLocation = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class RewriteResponse(_messages.Message):
  r"""A rewrite response.

  Fields:
    done: true if the copy is finished; otherwise, false if the copy is in
      progress. This property is always present in the response.
    kind: The kind of item this is.
    objectSize: The total size of the object being copied in bytes. This
      property is always present in the response.
    resource: A resource containing the metadata for the copied-to object.
      This property is present in the response only when copying completes.
    rewriteToken: A token to use in subsequent requests to continue copying
      data. This token is present in the response only when there is more data
      to copy.
    totalBytesRewritten: The total bytes written so far, which can be used to
      provide a waiting user with a progress indicator. This property is
      always present in the response.
  """

  done = _messages.BooleanField(1)
  kind = _messages.StringField(2, default='storage#rewriteResponse')
  objectSize = _messages.IntegerField(3)
  resource = _messages.MessageField('Object', 4)
  rewriteToken = _messages.StringField(5)
  totalBytesRewritten = _messages.IntegerField(6)


class ServiceAccount(_messages.Message):
  r"""A subscription to receive Google PubSub notifications.

  Fields:
    email_address: The ID of the notification.
    kind: The kind of item this is. For notifications, this is always
      storage#notification.
  """

  email_address = _messages.StringField(1)
  kind = _messages.StringField(2, default='storage#serviceAccount')


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    AltValueValuesEnum: Data format for the response.

  Fields:
    alt: Data format for the response.
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: An opaque string that represents a user for quota purposes.
      Must not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Upload protocol for media (e.g. "media", "multipart",
      "resumable").
    userIp: Deprecated. Please use quotaUser instead.
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for the response.

    Values:
      json: Responses with Content-Type of application/json
      media: Responses containing object data
    """
    json = 0
    media = 1

  alt = _messages.EnumField('AltValueValuesEnum', 1, default='json')
  fields = _messages.StringField(2)
  key = _messages.StringField(3)
  oauth_token = _messages.StringField(4)
  prettyPrint = _messages.BooleanField(5, default=True)
  quotaUser = _messages.StringField(6)
  trace = _messages.StringField(7)
  uploadType = _messages.StringField(8)
  userIp = _messages.StringField(9)


class StorageAnywhereCachesDisableRequest(_messages.Message):
  r"""A StorageAnywhereCachesDisableRequest object.

  Fields:
    anywhereCacheId: The ID of requested Anywhere Cache instance.
    bucket: Name of the parent bucket.
  """

  anywhereCacheId = _messages.StringField(1, required=True)
  bucket = _messages.StringField(2, required=True)


class StorageAnywhereCachesGetRequest(_messages.Message):
  r"""A StorageAnywhereCachesGetRequest object.

  Fields:
    anywhereCacheId: The ID of requested Anywhere Cache instance.
    bucket: Name of the parent bucket.
  """

  anywhereCacheId = _messages.StringField(1, required=True)
  bucket = _messages.StringField(2, required=True)


class StorageAnywhereCachesListRequest(_messages.Message):
  r"""A StorageAnywhereCachesListRequest object.

  Fields:
    bucket: Name of the parent bucket.
    pageSize: Maximum number of items to return in a single page of responses.
      Maximum 1000.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.
  """

  bucket = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class StorageAnywhereCachesPauseRequest(_messages.Message):
  r"""A StorageAnywhereCachesPauseRequest object.

  Fields:
    anywhereCacheId: The ID of requested Anywhere Cache instance.
    bucket: Name of the parent bucket.
  """

  anywhereCacheId = _messages.StringField(1, required=True)
  bucket = _messages.StringField(2, required=True)


class StorageAnywhereCachesResumeRequest(_messages.Message):
  r"""A StorageAnywhereCachesResumeRequest object.

  Fields:
    anywhereCacheId: The ID of requested Anywhere Cache instance.
    bucket: Name of the parent bucket.
  """

  anywhereCacheId = _messages.StringField(1, required=True)
  bucket = _messages.StringField(2, required=True)


class StorageBucketAccessControlsDeleteRequest(_messages.Message):
  r"""A StorageBucketAccessControlsDeleteRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageBucketAccessControlsDeleteResponse(_messages.Message):
  r"""An empty StorageBucketAccessControlsDelete response."""


class StorageBucketAccessControlsGetRequest(_messages.Message):
  r"""A StorageBucketAccessControlsGetRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageBucketAccessControlsInsertRequest(_messages.Message):
  r"""A StorageBucketAccessControlsInsertRequest object.

  Fields:
    bucket: Name of a bucket.
    bucketAccessControl: A BucketAccessControl resource to be passed as the
      request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  bucketAccessControl = _messages.MessageField('BucketAccessControl', 2)
  userProject = _messages.StringField(3)


class StorageBucketAccessControlsListRequest(_messages.Message):
  r"""A StorageBucketAccessControlsListRequest object.

  Fields:
    bucket: Name of a bucket.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  userProject = _messages.StringField(2)


class StorageBucketAccessControlsPatchRequest(_messages.Message):
  r"""A StorageBucketAccessControlsPatchRequest object.

  Fields:
    bucket: Name of a bucket.
    bucketAccessControl: A BucketAccessControl resource to be passed as the
      request body.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  bucketAccessControl = _messages.MessageField('BucketAccessControl', 2)
  entity = _messages.StringField(3, required=True)
  userProject = _messages.StringField(4)


class StorageBucketAccessControlsUpdateRequest(_messages.Message):
  r"""A StorageBucketAccessControlsUpdateRequest object.

  Fields:
    bucket: Name of a bucket.
    bucketAccessControl: A BucketAccessControl resource to be passed as the
      request body.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  bucketAccessControl = _messages.MessageField('BucketAccessControl', 2)
  entity = _messages.StringField(3, required=True)
  userProject = _messages.StringField(4)


class StorageBucketsDeleteRequest(_messages.Message):
  r"""A StorageBucketsDeleteRequest object.

  Fields:
    bucket: Name of a bucket.
    ifMetagenerationMatch: If set, only deletes the bucket if its
      metageneration matches this value.
    ifMetagenerationNotMatch: If set, only deletes the bucket if its
      metageneration does not match this value.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  ifMetagenerationMatch = _messages.IntegerField(2)
  ifMetagenerationNotMatch = _messages.IntegerField(3)
  userProject = _messages.StringField(4)


class StorageBucketsDeleteResponse(_messages.Message):
  r"""An empty StorageBucketsDelete response."""


class StorageBucketsGetIamPolicyRequest(_messages.Message):
  r"""A StorageBucketsGetIamPolicyRequest object.

  Fields:
    bucket: Name of a bucket.
    optionsRequestedPolicyVersion: The IAM policy format version to be
      returned. If the optionsRequestedPolicyVersion is for an older version
      that doesn't support part of the requested IAM policy, the request
      fails.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  optionsRequestedPolicyVersion = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  userProject = _messages.StringField(3)


class StorageBucketsGetRequest(_messages.Message):
  r"""A StorageBucketsGetRequest object.

  Enums:
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl.

  Fields:
    bucket: Name of a bucket.
    generation: If present, specifies the generation of the bucket. This is
      required if softDeleted is true.
    ifMetagenerationMatch: Makes the return of the bucket metadata conditional
      on whether the bucket's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the return of the bucket metadata
      conditional on whether the bucket's current metageneration does not
      match the given value.
    projection: Set of properties to return. Defaults to noAcl.
    softDeleted: If true, return the soft-deleted version of this bucket. The
      default is false. For more information, see [Soft
      Delete](https://cloud.google.com/storage/docs/soft-delete).
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl.

    Values:
      full: Include all properties.
      noAcl: Omit owner, acl and defaultObjectAcl properties.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  ifMetagenerationMatch = _messages.IntegerField(3)
  ifMetagenerationNotMatch = _messages.IntegerField(4)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 5)
  softDeleted = _messages.BooleanField(6)
  userProject = _messages.StringField(7)


class StorageBucketsGetStorageLayoutRequest(_messages.Message):
  r"""A StorageBucketsGetStorageLayoutRequest object.

  Fields:
    bucket: Name of a bucket.
    prefix: An optional prefix used for permission check. It is useful when
      the caller only has storage.objects.list permission under a specific
      prefix.
  """

  bucket = _messages.StringField(1, required=True)
  prefix = _messages.StringField(2)


class StorageBucketsInsertRequest(_messages.Message):
  r"""A StorageBucketsInsertRequest object.

  Enums:
    PredefinedAclValueValuesEnum: Apply a predefined set of access controls to
      this bucket.
    PredefinedDefaultObjectAclValueValuesEnum: Apply a predefined set of
      default object access controls to this bucket.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl,
      unless the bucket resource specifies acl or defaultObjectAcl properties,
      when it defaults to full.

  Fields:
    bucket: A Bucket resource to be passed as the request body.
    enableObjectRetention: When set to true, object retention is enabled for
      this bucket.
    predefinedAcl: Apply a predefined set of access controls to this bucket.
    predefinedDefaultObjectAcl: Apply a predefined set of default object
      access controls to this bucket.
    project: A valid API project identifier.
    projection: Set of properties to return. Defaults to noAcl, unless the
      bucket resource specifies acl or defaultObjectAcl properties, when it
      defaults to full.
    userProject: The project to be billed for this request.
  """

  class PredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to this bucket.

    Values:
      authenticatedRead: Project team owners get OWNER access, and
        allAuthenticatedUsers get READER access.
      private: Project team owners get OWNER access.
      projectPrivate: Project team members get access according to their
        roles.
      publicRead: Project team owners get OWNER access, and allUsers get
        READER access.
      publicReadWrite: Project team owners get OWNER access, and allUsers get
        WRITER access.
    """
    authenticatedRead = 0
    private = 1
    projectPrivate = 2
    publicRead = 3
    publicReadWrite = 4

  class PredefinedDefaultObjectAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of default object access controls to this
    bucket.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl, unless the bucket
    resource specifies acl or defaultObjectAcl properties, when it defaults to
    full.

    Values:
      full: Include all properties.
      noAcl: Omit owner, acl and defaultObjectAcl properties.
    """
    full = 0
    noAcl = 1

  bucket = _messages.MessageField('Bucket', 1)
  enableObjectRetention = _messages.BooleanField(2, default=False)
  predefinedAcl = _messages.EnumField('PredefinedAclValueValuesEnum', 3)
  predefinedDefaultObjectAcl = _messages.EnumField('PredefinedDefaultObjectAclValueValuesEnum', 4)
  project = _messages.StringField(5, required=True)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 6)
  userProject = _messages.StringField(7)


class StorageBucketsListRequest(_messages.Message):
  r"""A StorageBucketsListRequest object.

  Enums:
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl.

  Fields:
    maxResults: Maximum number of buckets to return in a single response. The
      service will use this parameter or 1,000 items, whichever is smaller.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.
    prefix: Filter results to buckets whose names begin with this prefix.
    project: A valid API project identifier.
    projection: Set of properties to return. Defaults to noAcl.
    softDeleted: If true, only soft-deleted bucket versions will be returned.
      The default is false. For more information, see [Soft
      Delete](https://cloud.google.com/storage/docs/soft-delete).
    userProject: The project to be billed for this request.
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl.

    Values:
      full: Include all properties.
      noAcl: Omit owner, acl and defaultObjectAcl properties.
    """
    full = 0
    noAcl = 1

  maxResults = _messages.IntegerField(1, variant=_messages.Variant.UINT32, default=1000)
  pageToken = _messages.StringField(2)
  prefix = _messages.StringField(3)
  project = _messages.StringField(4, required=True)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 5)
  softDeleted = _messages.BooleanField(6)
  userProject = _messages.StringField(7)


class StorageBucketsLockRetentionPolicyRequest(_messages.Message):
  r"""A StorageBucketsLockRetentionPolicyRequest object.

  Fields:
    bucket: Name of a bucket.
    ifMetagenerationMatch: Makes the operation conditional on whether bucket's
      current metageneration matches the given value.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  ifMetagenerationMatch = _messages.IntegerField(2, required=True)
  userProject = _messages.StringField(3)


class StorageBucketsOperationsAdvanceRelocateBucketRequest(_messages.Message):
  r"""A StorageBucketsOperationsAdvanceRelocateBucketRequest object.

  Fields:
    advanceRelocateBucketOperationRequest: A
      AdvanceRelocateBucketOperationRequest resource to be passed as the
      request body.
    bucket: Name of the bucket to advance the relocate for.
    operationId: ID of the operation resource.
  """

  advanceRelocateBucketOperationRequest = _messages.MessageField('AdvanceRelocateBucketOperationRequest', 1)
  bucket = _messages.StringField(2, required=True)
  operationId = _messages.StringField(3, required=True)


class StorageBucketsOperationsAdvanceRelocateBucketResponse(_messages.Message):
  r"""An empty StorageBucketsOperationsAdvanceRelocateBucket response."""


class StorageBucketsOperationsCancelRequest(_messages.Message):
  r"""A StorageBucketsOperationsCancelRequest object.

  Fields:
    bucket: The parent bucket of the operation resource.
    operationId: The ID of the operation resource.
  """

  bucket = _messages.StringField(1, required=True)
  operationId = _messages.StringField(2, required=True)


class StorageBucketsOperationsCancelResponse(_messages.Message):
  r"""An empty StorageBucketsOperationsCancel response."""


class StorageBucketsOperationsGetRequest(_messages.Message):
  r"""A StorageBucketsOperationsGetRequest object.

  Fields:
    bucket: The parent bucket of the operation resource.
    operationId: The ID of the operation resource.
  """

  bucket = _messages.StringField(1, required=True)
  operationId = _messages.StringField(2, required=True)


class StorageBucketsOperationsListRequest(_messages.Message):
  r"""A StorageBucketsOperationsListRequest object.

  Fields:
    bucket: Name of the bucket in which to look for operations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language is documented in more detail in
      [AIP-160](https://google.aip.dev/160).
    pageSize: Maximum number of items to return in a single page of responses.
      Fewer total results may be returned than requested. The service uses
      this parameter or 100 items, whichever is smaller.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.
  """

  bucket = _messages.StringField(1, required=True)
  filter = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class StorageBucketsPatchRequest(_messages.Message):
  r"""A StorageBucketsPatchRequest object.

  Enums:
    PredefinedAclValueValuesEnum: Apply a predefined set of access controls to
      this bucket.
    PredefinedDefaultObjectAclValueValuesEnum: Apply a predefined set of
      default object access controls to this bucket.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to full.

  Fields:
    bucket: Name of a bucket.
    bucketResource: A Bucket resource to be passed as the request body.
    ifMetagenerationMatch: Makes the return of the bucket metadata conditional
      on whether the bucket's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the return of the bucket metadata
      conditional on whether the bucket's current metageneration does not
      match the given value.
    predefinedAcl: Apply a predefined set of access controls to this bucket.
    predefinedDefaultObjectAcl: Apply a predefined set of default object
      access controls to this bucket.
    projection: Set of properties to return. Defaults to full.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class PredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to this bucket.

    Values:
      authenticatedRead: Project team owners get OWNER access, and
        allAuthenticatedUsers get READER access.
      private: Project team owners get OWNER access.
      projectPrivate: Project team members get access according to their
        roles.
      publicRead: Project team owners get OWNER access, and allUsers get
        READER access.
      publicReadWrite: Project team owners get OWNER access, and allUsers get
        WRITER access.
    """
    authenticatedRead = 0
    private = 1
    projectPrivate = 2
    publicRead = 3
    publicReadWrite = 4

  class PredefinedDefaultObjectAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of default object access controls to this
    bucket.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit owner, acl and defaultObjectAcl properties.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  bucketResource = _messages.MessageField('Bucket', 2)
  ifMetagenerationMatch = _messages.IntegerField(3)
  ifMetagenerationNotMatch = _messages.IntegerField(4)
  predefinedAcl = _messages.EnumField('PredefinedAclValueValuesEnum', 5)
  predefinedDefaultObjectAcl = _messages.EnumField('PredefinedDefaultObjectAclValueValuesEnum', 6)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 7)
  userProject = _messages.StringField(8)


class StorageBucketsRelocateRequest(_messages.Message):
  r"""A StorageBucketsRelocateRequest object.

  Fields:
    bucket: Name of the bucket to be moved.
    relocateBucketRequest: A RelocateBucketRequest resource to be passed as
      the request body.
  """

  bucket = _messages.StringField(1, required=True)
  relocateBucketRequest = _messages.MessageField('RelocateBucketRequest', 2)


class StorageBucketsRestoreRequest(_messages.Message):
  r"""A StorageBucketsRestoreRequest object.

  Enums:
    ProjectionValueValuesEnum: Set of properties to return. Defaults to full.

  Fields:
    bucket: Name of a bucket.
    generation: Generation of a bucket.
    projection: Set of properties to return. Defaults to full.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit owner, acl and defaultObjectAcl properties.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2, required=True)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 3)
  userProject = _messages.StringField(4)


class StorageBucketsSetIamPolicyRequest(_messages.Message):
  r"""A StorageBucketsSetIamPolicyRequest object.

  Fields:
    bucket: Name of a bucket.
    policy: A Policy resource to be passed as the request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  policy = _messages.MessageField('Policy', 2)
  userProject = _messages.StringField(3)


class StorageBucketsTestIamPermissionsRequest(_messages.Message):
  r"""A StorageBucketsTestIamPermissionsRequest object.

  Fields:
    bucket: Name of a bucket.
    permissions: Permissions to test.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  permissions = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageBucketsUpdateRequest(_messages.Message):
  r"""A StorageBucketsUpdateRequest object.

  Enums:
    PredefinedAclValueValuesEnum: Apply a predefined set of access controls to
      this bucket.
    PredefinedDefaultObjectAclValueValuesEnum: Apply a predefined set of
      default object access controls to this bucket.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to full.

  Fields:
    bucket: Name of a bucket.
    bucketResource: A Bucket resource to be passed as the request body.
    ifMetagenerationMatch: Makes the return of the bucket metadata conditional
      on whether the bucket's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the return of the bucket metadata
      conditional on whether the bucket's current metageneration does not
      match the given value.
    predefinedAcl: Apply a predefined set of access controls to this bucket.
    predefinedDefaultObjectAcl: Apply a predefined set of default object
      access controls to this bucket.
    projection: Set of properties to return. Defaults to full.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class PredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to this bucket.

    Values:
      authenticatedRead: Project team owners get OWNER access, and
        allAuthenticatedUsers get READER access.
      private: Project team owners get OWNER access.
      projectPrivate: Project team members get access according to their
        roles.
      publicRead: Project team owners get OWNER access, and allUsers get
        READER access.
      publicReadWrite: Project team owners get OWNER access, and allUsers get
        WRITER access.
    """
    authenticatedRead = 0
    private = 1
    projectPrivate = 2
    publicRead = 3
    publicReadWrite = 4

  class PredefinedDefaultObjectAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of default object access controls to this
    bucket.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit owner, acl and defaultObjectAcl properties.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  bucketResource = _messages.MessageField('Bucket', 2)
  ifMetagenerationMatch = _messages.IntegerField(3)
  ifMetagenerationNotMatch = _messages.IntegerField(4)
  predefinedAcl = _messages.EnumField('PredefinedAclValueValuesEnum', 5)
  predefinedDefaultObjectAcl = _messages.EnumField('PredefinedDefaultObjectAclValueValuesEnum', 6)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 7)
  userProject = _messages.StringField(8)


class StorageChannelsStopResponse(_messages.Message):
  r"""An empty StorageChannelsStop response."""


class StorageDefaultObjectAccessControlsDeleteRequest(_messages.Message):
  r"""A StorageDefaultObjectAccessControlsDeleteRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageDefaultObjectAccessControlsDeleteResponse(_messages.Message):
  r"""An empty StorageDefaultObjectAccessControlsDelete response."""


class StorageDefaultObjectAccessControlsGetRequest(_messages.Message):
  r"""A StorageDefaultObjectAccessControlsGetRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageDefaultObjectAccessControlsInsertRequest(_messages.Message):
  r"""A StorageDefaultObjectAccessControlsInsertRequest object.

  Fields:
    bucket: Name of a bucket.
    objectAccessControl: A ObjectAccessControl resource to be passed as the
      request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  objectAccessControl = _messages.MessageField('ObjectAccessControl', 2)
  userProject = _messages.StringField(3)


class StorageDefaultObjectAccessControlsListRequest(_messages.Message):
  r"""A StorageDefaultObjectAccessControlsListRequest object.

  Fields:
    bucket: Name of a bucket.
    ifMetagenerationMatch: If present, only return default ACL listing if the
      bucket's current metageneration matches this value.
    ifMetagenerationNotMatch: If present, only return default ACL listing if
      the bucket's current metageneration does not match the given value.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  ifMetagenerationMatch = _messages.IntegerField(2)
  ifMetagenerationNotMatch = _messages.IntegerField(3)
  userProject = _messages.StringField(4)


class StorageDefaultObjectAccessControlsPatchRequest(_messages.Message):
  r"""A StorageDefaultObjectAccessControlsPatchRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    objectAccessControl: A ObjectAccessControl resource to be passed as the
      request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  objectAccessControl = _messages.MessageField('ObjectAccessControl', 3)
  userProject = _messages.StringField(4)


class StorageDefaultObjectAccessControlsUpdateRequest(_messages.Message):
  r"""A StorageDefaultObjectAccessControlsUpdateRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    objectAccessControl: A ObjectAccessControl resource to be passed as the
      request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  objectAccessControl = _messages.MessageField('ObjectAccessControl', 3)
  userProject = _messages.StringField(4)


class StorageFoldersDeleteRequest(_messages.Message):
  r"""A StorageFoldersDeleteRequest object.

  Fields:
    bucket: Name of the bucket in which the folder resides.
    folder: Name of a folder.
    ifMetagenerationMatch: If set, only deletes the folder if its
      metageneration matches this value.
    ifMetagenerationNotMatch: If set, only deletes the folder if its
      metageneration does not match this value.
  """

  bucket = _messages.StringField(1, required=True)
  folder = _messages.StringField(2, required=True)
  ifMetagenerationMatch = _messages.IntegerField(3)
  ifMetagenerationNotMatch = _messages.IntegerField(4)


class StorageFoldersDeleteResponse(_messages.Message):
  r"""An empty StorageFoldersDelete response."""


class StorageFoldersGetRequest(_messages.Message):
  r"""A StorageFoldersGetRequest object.

  Fields:
    bucket: Name of the bucket in which the folder resides.
    folder: Name of a folder.
    ifMetagenerationMatch: Makes the return of the folder metadata conditional
      on whether the folder's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the return of the folder metadata
      conditional on whether the folder's current metageneration does not
      match the given value.
  """

  bucket = _messages.StringField(1, required=True)
  folder = _messages.StringField(2, required=True)
  ifMetagenerationMatch = _messages.IntegerField(3)
  ifMetagenerationNotMatch = _messages.IntegerField(4)


class StorageFoldersInsertRequest(_messages.Message):
  r"""A StorageFoldersInsertRequest object.

  Fields:
    bucket: Name of the bucket in which the folder resides.
    folder: A Folder resource to be passed as the request body.
    recursive: If true, any parent folder which doesn't exist will be created
      automatically.
  """

  bucket = _messages.StringField(1, required=True)
  folder = _messages.MessageField('Folder', 2)
  recursive = _messages.BooleanField(3)


class StorageFoldersListRequest(_messages.Message):
  r"""A StorageFoldersListRequest object.

  Fields:
    bucket: Name of the bucket in which to look for folders.
    delimiter: Returns results in a directory-like mode. The only supported
      value is '/'. If set, items will only contain folders that either
      exactly match the prefix, or are one level below the prefix.
    endOffset: Filter results to folders whose names are lexicographically
      before endOffset. If startOffset is also set, the folders listed will
      have names between startOffset (inclusive) and endOffset (exclusive).
    pageSize: Maximum number of items to return in a single page of responses.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.
    prefix: Filter results to folders whose paths begin with this prefix. If
      set, the value must either be an empty string or end with a '/'.
    startOffset: Filter results to folders whose names are lexicographically
      equal to or after startOffset. If endOffset is also set, the folders
      listed will have names between startOffset (inclusive) and endOffset
      (exclusive).
  """

  bucket = _messages.StringField(1, required=True)
  delimiter = _messages.StringField(2)
  endOffset = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  prefix = _messages.StringField(6)
  startOffset = _messages.StringField(7)


class StorageFoldersRenameRequest(_messages.Message):
  r"""A StorageFoldersRenameRequest object.

  Fields:
    bucket: Name of the bucket in which the folders are in.
    destinationFolder: Name of the destination folder.
    ifSourceMetagenerationMatch: Makes the operation conditional on whether
      the source object's current metageneration matches the given value.
    ifSourceMetagenerationNotMatch: Makes the operation conditional on whether
      the source object's current metageneration does not match the given
      value.
    sourceFolder: Name of the source folder.
  """

  bucket = _messages.StringField(1, required=True)
  destinationFolder = _messages.StringField(2, required=True)
  ifSourceMetagenerationMatch = _messages.IntegerField(3)
  ifSourceMetagenerationNotMatch = _messages.IntegerField(4)
  sourceFolder = _messages.StringField(5, required=True)


class StorageManagedFoldersDeleteRequest(_messages.Message):
  r"""A StorageManagedFoldersDeleteRequest object.

  Fields:
    allowNonEmpty: Allows the deletion of a managed folder even if it is not
      empty. A managed folder is empty if there are no objects or managed
      folders that it applies to. Callers must have
      storage.managedFolders.setIamPolicy permission.
    bucket: Name of the bucket containing the managed folder.
    ifMetagenerationMatch: If set, only deletes the managed folder if its
      metageneration matches this value.
    ifMetagenerationNotMatch: If set, only deletes the managed folder if its
      metageneration does not match this value.
    managedFolder: The managed folder name/path.
  """

  allowNonEmpty = _messages.BooleanField(1)
  bucket = _messages.StringField(2, required=True)
  ifMetagenerationMatch = _messages.IntegerField(3)
  ifMetagenerationNotMatch = _messages.IntegerField(4)
  managedFolder = _messages.StringField(5, required=True)


class StorageManagedFoldersDeleteResponse(_messages.Message):
  r"""An empty StorageManagedFoldersDelete response."""


class StorageManagedFoldersGetIamPolicyRequest(_messages.Message):
  r"""A StorageManagedFoldersGetIamPolicyRequest object.

  Fields:
    bucket: Name of the bucket containing the managed folder.
    managedFolder: The managed folder name/path.
    optionsRequestedPolicyVersion: The IAM policy format version to be
      returned. If the optionsRequestedPolicyVersion is for an older version
      that doesn't support part of the requested IAM policy, the request
      fails.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  managedFolder = _messages.StringField(2, required=True)
  optionsRequestedPolicyVersion = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  userProject = _messages.StringField(4)


class StorageManagedFoldersGetRequest(_messages.Message):
  r"""A StorageManagedFoldersGetRequest object.

  Fields:
    bucket: Name of the bucket containing the managed folder.
    ifMetagenerationMatch: Makes the return of the managed folder metadata
      conditional on whether the managed folder's current metageneration
      matches the given value.
    ifMetagenerationNotMatch: Makes the return of the managed folder metadata
      conditional on whether the managed folder's current metageneration does
      not match the given value.
    managedFolder: The managed folder name/path.
  """

  bucket = _messages.StringField(1, required=True)
  ifMetagenerationMatch = _messages.IntegerField(2)
  ifMetagenerationNotMatch = _messages.IntegerField(3)
  managedFolder = _messages.StringField(4, required=True)


class StorageManagedFoldersListRequest(_messages.Message):
  r"""A StorageManagedFoldersListRequest object.

  Fields:
    bucket: Name of the bucket containing the managed folder.
    pageSize: Maximum number of items to return in a single page of responses.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.
    prefix: The managed folder name/path prefix to filter the output list of
      results.
  """

  bucket = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  prefix = _messages.StringField(4)


class StorageManagedFoldersSetIamPolicyRequest(_messages.Message):
  r"""A StorageManagedFoldersSetIamPolicyRequest object.

  Fields:
    bucket: Name of the bucket containing the managed folder.
    managedFolder: The managed folder name/path.
    policy: A Policy resource to be passed as the request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  managedFolder = _messages.StringField(2, required=True)
  policy = _messages.MessageField('Policy', 3)
  userProject = _messages.StringField(4)


class StorageManagedFoldersTestIamPermissionsRequest(_messages.Message):
  r"""A StorageManagedFoldersTestIamPermissionsRequest object.

  Fields:
    bucket: Name of the bucket containing the managed folder.
    managedFolder: The managed folder name/path.
    permissions: Permissions to test.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  managedFolder = _messages.StringField(2, required=True)
  permissions = _messages.StringField(3, required=True)
  userProject = _messages.StringField(4)


class StorageNotificationsDeleteRequest(_messages.Message):
  r"""A StorageNotificationsDeleteRequest object.

  Fields:
    bucket: The parent bucket of the notification.
    notification: ID of the notification to delete.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  notification = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageNotificationsDeleteResponse(_messages.Message):
  r"""An empty StorageNotificationsDelete response."""


class StorageNotificationsGetRequest(_messages.Message):
  r"""A StorageNotificationsGetRequest object.

  Fields:
    bucket: The parent bucket of the notification.
    notification: Notification ID
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  notification = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageNotificationsInsertRequest(_messages.Message):
  r"""A StorageNotificationsInsertRequest object.

  Fields:
    bucket: The parent bucket of the notification.
    notification: A Notification resource to be passed as the request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  notification = _messages.MessageField('Notification', 2)
  userProject = _messages.StringField(3)


class StorageNotificationsListRequest(_messages.Message):
  r"""A StorageNotificationsListRequest object.

  Fields:
    bucket: Name of a Google Cloud Storage bucket.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  userProject = _messages.StringField(2)


class StorageObjectAccessControlsDeleteRequest(_messages.Message):
  r"""A StorageObjectAccessControlsDeleteRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  generation = _messages.IntegerField(3)
  object = _messages.StringField(4, required=True)
  userProject = _messages.StringField(5)


class StorageObjectAccessControlsDeleteResponse(_messages.Message):
  r"""An empty StorageObjectAccessControlsDelete response."""


class StorageObjectAccessControlsGetRequest(_messages.Message):
  r"""A StorageObjectAccessControlsGetRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  generation = _messages.IntegerField(3)
  object = _messages.StringField(4, required=True)
  userProject = _messages.StringField(5)


class StorageObjectAccessControlsInsertRequest(_messages.Message):
  r"""A StorageObjectAccessControlsInsertRequest object.

  Fields:
    bucket: Name of a bucket.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    objectAccessControl: A ObjectAccessControl resource to be passed as the
      request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3, required=True)
  objectAccessControl = _messages.MessageField('ObjectAccessControl', 4)
  userProject = _messages.StringField(5)


class StorageObjectAccessControlsListRequest(_messages.Message):
  r"""A StorageObjectAccessControlsListRequest object.

  Fields:
    bucket: Name of a bucket.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3, required=True)
  userProject = _messages.StringField(4)


class StorageObjectAccessControlsPatchRequest(_messages.Message):
  r"""A StorageObjectAccessControlsPatchRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    objectAccessControl: A ObjectAccessControl resource to be passed as the
      request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  generation = _messages.IntegerField(3)
  object = _messages.StringField(4, required=True)
  objectAccessControl = _messages.MessageField('ObjectAccessControl', 5)
  userProject = _messages.StringField(6)


class StorageObjectAccessControlsUpdateRequest(_messages.Message):
  r"""A StorageObjectAccessControlsUpdateRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    objectAccessControl: A ObjectAccessControl resource to be passed as the
      request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  generation = _messages.IntegerField(3)
  object = _messages.StringField(4, required=True)
  objectAccessControl = _messages.MessageField('ObjectAccessControl', 5)
  userProject = _messages.StringField(6)


class StorageObjectsBulkRestoreRequest(_messages.Message):
  r"""A StorageObjectsBulkRestoreRequest object.

  Fields:
    bucket: Name of the bucket in which the object resides.
    bulkRestoreObjectsRequest: A BulkRestoreObjectsRequest resource to be
      passed as the request body.
  """

  bucket = _messages.StringField(1, required=True)
  bulkRestoreObjectsRequest = _messages.MessageField('BulkRestoreObjectsRequest', 2)


class StorageObjectsComposeRequest(_messages.Message):
  r"""A StorageObjectsComposeRequest object.

  Enums:
    DestinationPredefinedAclValueValuesEnum: Apply a predefined set of access
      controls to the destination object.

  Fields:
    composeRequest: A ComposeRequest resource to be passed as the request
      body.
    destinationBucket: Name of the bucket containing the source objects. The
      destination object is stored in this bucket.
    destinationObject: Name of the new object. For information about how to
      URL encode object names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    destinationPredefinedAcl: Apply a predefined set of access controls to the
      destination object.
    ifGenerationMatch: Makes the operation conditional on whether the object's
      current generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      object's current metageneration matches the given value.
    kmsKeyName: Resource name of the Cloud KMS key, of the form projects/my-
      project/locations/global/keyRings/my-kr/cryptoKeys/my-key, that will be
      used to encrypt the object. Overrides the object metadata's kms_key_name
      value, if any.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class DestinationPredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to the destination object.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  composeRequest = _messages.MessageField('ComposeRequest', 1)
  destinationBucket = _messages.StringField(2, required=True)
  destinationObject = _messages.StringField(3, required=True)
  destinationPredefinedAcl = _messages.EnumField('DestinationPredefinedAclValueValuesEnum', 4)
  ifGenerationMatch = _messages.IntegerField(5)
  ifMetagenerationMatch = _messages.IntegerField(6)
  kmsKeyName = _messages.StringField(7)
  userProject = _messages.StringField(8)


class StorageObjectsCopyRequest(_messages.Message):
  r"""A StorageObjectsCopyRequest object.

  Enums:
    DestinationPredefinedAclValueValuesEnum: Apply a predefined set of access
      controls to the destination object.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl,
      unless the object resource specifies the acl property, when it defaults
      to full.

  Fields:
    destinationBucket: Name of the bucket in which to store the new object.
      Overrides the provided object metadata's bucket value, if any.For
      information about how to URL encode object names to be path safe, see
      [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    destinationKmsKeyName: Resource name of the Cloud KMS key, of the form
      projects/my-project/locations/global/keyRings/my-kr/cryptoKeys/my-key,
      that will be used to encrypt the object. Overrides the object metadata's
      kms_key_name value, if any.
    destinationObject: Name of the new object. Required when the object
      metadata is not otherwise provided. Overrides the object metadata's name
      value, if any.
    destinationPredefinedAcl: Apply a predefined set of access controls to the
      destination object.
    ifGenerationMatch: Makes the operation conditional on whether the
      destination object's current generation matches the given value. Setting
      to 0 makes the operation succeed only if there are no live versions of
      the object.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      destination object's current generation does not match the given value.
      If no live object exists, the precondition fails. Setting to 0 makes the
      operation succeed only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      destination object's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      destination object's current metageneration does not match the given
      value.
    ifSourceGenerationMatch: Makes the operation conditional on whether the
      source object's current generation matches the given value.
    ifSourceGenerationNotMatch: Makes the operation conditional on whether the
      source object's current generation does not match the given value.
    ifSourceMetagenerationMatch: Makes the operation conditional on whether
      the source object's current metageneration matches the given value.
    ifSourceMetagenerationNotMatch: Makes the operation conditional on whether
      the source object's current metageneration does not match the given
      value.
    object: A Object resource to be passed as the request body.
    projection: Set of properties to return. Defaults to noAcl, unless the
      object resource specifies the acl property, when it defaults to full.
    sourceBucket: Name of the bucket in which to find the source object.
    sourceGeneration: If present, selects a specific revision of the source
      object (as opposed to the latest version, the default).
    sourceObject: Name of the source object. For information about how to URL
      encode object names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class DestinationPredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to the destination object.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl, unless the object
    resource specifies the acl property, when it defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  destinationBucket = _messages.StringField(1, required=True)
  destinationKmsKeyName = _messages.StringField(2)
  destinationObject = _messages.StringField(3, required=True)
  destinationPredefinedAcl = _messages.EnumField('DestinationPredefinedAclValueValuesEnum', 4)
  ifGenerationMatch = _messages.IntegerField(5)
  ifGenerationNotMatch = _messages.IntegerField(6)
  ifMetagenerationMatch = _messages.IntegerField(7)
  ifMetagenerationNotMatch = _messages.IntegerField(8)
  ifSourceGenerationMatch = _messages.IntegerField(9)
  ifSourceGenerationNotMatch = _messages.IntegerField(10)
  ifSourceMetagenerationMatch = _messages.IntegerField(11)
  ifSourceMetagenerationNotMatch = _messages.IntegerField(12)
  object = _messages.MessageField('Object', 13)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 14)
  sourceBucket = _messages.StringField(15, required=True)
  sourceGeneration = _messages.IntegerField(16)
  sourceObject = _messages.StringField(17, required=True)
  userProject = _messages.StringField(18)


class StorageObjectsDeleteRequest(_messages.Message):
  r"""A StorageObjectsDeleteRequest object.

  Fields:
    bucket: Name of the bucket in which the object resides.
    generation: If present, permanently deletes a specific revision of this
      object (as opposed to the latest version, the default).
    ifGenerationMatch: Makes the operation conditional on whether the object's
      current generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      object's current generation does not match the given value. If no live
      object exists, the precondition fails. Setting to 0 makes the operation
      succeed only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      object's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      object's current metageneration does not match the given value.
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  ifGenerationMatch = _messages.IntegerField(3)
  ifGenerationNotMatch = _messages.IntegerField(4)
  ifMetagenerationMatch = _messages.IntegerField(5)
  ifMetagenerationNotMatch = _messages.IntegerField(6)
  object = _messages.StringField(7, required=True)
  userProject = _messages.StringField(8)


class StorageObjectsDeleteResponse(_messages.Message):
  r"""An empty StorageObjectsDelete response."""


class StorageObjectsGetIamPolicyRequest(_messages.Message):
  r"""A StorageObjectsGetIamPolicyRequest object.

  Fields:
    bucket: Name of the bucket in which the object resides.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3, required=True)
  userProject = _messages.StringField(4)


class StorageObjectsGetRequest(_messages.Message):
  r"""A StorageObjectsGetRequest object.

  Enums:
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl.

  Fields:
    bucket: Name of the bucket in which the object resides.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    ifGenerationMatch: Makes the operation conditional on whether the object's
      current generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      object's current generation does not match the given value. If no live
      object exists, the precondition fails. Setting to 0 makes the operation
      succeed only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      object's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      object's current metageneration does not match the given value.
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    projection: Set of properties to return. Defaults to noAcl.
    restoreToken: Restore token used to differentiate soft-deleted objects
      with the same name and generation. Only applicable for hierarchical
      namespace buckets and if softDeleted is set to true. This parameter is
      optional, and is only required in the rare case when there are multiple
      soft-deleted objects with the same name and generation.
    softDeleted: If true, only soft-deleted object versions will be listed.
      The default is false. For more information, see [Soft
      Delete](https://cloud.google.com/storage/docs/soft-delete).
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  ifGenerationMatch = _messages.IntegerField(3)
  ifGenerationNotMatch = _messages.IntegerField(4)
  ifMetagenerationMatch = _messages.IntegerField(5)
  ifMetagenerationNotMatch = _messages.IntegerField(6)
  object = _messages.StringField(7, required=True)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 8)
  restoreToken = _messages.StringField(9)
  softDeleted = _messages.BooleanField(10)
  userProject = _messages.StringField(11)


class StorageObjectsInsertRequest(_messages.Message):
  r"""A StorageObjectsInsertRequest object.

  Enums:
    PredefinedAclValueValuesEnum: Apply a predefined set of access controls to
      this object.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl,
      unless the object resource specifies the acl property, when it defaults
      to full.

  Fields:
    bucket: Name of the bucket in which to store the new object. Overrides the
      provided object metadata's bucket value, if any.
    contentEncoding: If set, sets the contentEncoding property of the final
      object to this value. Setting this parameter is equivalent to setting
      the contentEncoding metadata property. This can be useful when uploading
      an object with uploadType=media to indicate the encoding of the content
      being uploaded.
    ifGenerationMatch: Makes the operation conditional on whether the object's
      current generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      object's current generation does not match the given value. If no live
      object exists, the precondition fails. Setting to 0 makes the operation
      succeed only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      object's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      object's current metageneration does not match the given value.
    kmsKeyName: Resource name of the Cloud KMS key, of the form projects/my-
      project/locations/global/keyRings/my-kr/cryptoKeys/my-key, that will be
      used to encrypt the object. Overrides the object metadata's kms_key_name
      value, if any.
    name: Name of the object. Required when the object metadata is not
      otherwise provided. Overrides the object metadata's name value, if any.
      For information about how to URL encode object names to be path safe,
      see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    object: A Object resource to be passed as the request body.
    predefinedAcl: Apply a predefined set of access controls to this object.
    projection: Set of properties to return. Defaults to noAcl, unless the
      object resource specifies the acl property, when it defaults to full.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class PredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to this object.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl, unless the object
    resource specifies the acl property, when it defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  contentEncoding = _messages.StringField(2)
  ifGenerationMatch = _messages.IntegerField(3)
  ifGenerationNotMatch = _messages.IntegerField(4)
  ifMetagenerationMatch = _messages.IntegerField(5)
  ifMetagenerationNotMatch = _messages.IntegerField(6)
  kmsKeyName = _messages.StringField(7)
  name = _messages.StringField(8)
  object = _messages.MessageField('Object', 9)
  predefinedAcl = _messages.EnumField('PredefinedAclValueValuesEnum', 10)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 11)
  userProject = _messages.StringField(12)


class StorageObjectsListRequest(_messages.Message):
  r"""A StorageObjectsListRequest object.

  Enums:
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl.

  Fields:
    bucket: Name of the bucket in which to look for objects.
    delimiter: Returns results in a directory-like mode. items will contain
      only objects whose names, aside from the prefix, do not contain
      delimiter. Objects whose names, aside from the prefix, contain delimiter
      will have their name, truncated after the delimiter, returned in
      prefixes. Duplicate prefixes are omitted.
    endOffset: Filter results to objects whose names are lexicographically
      before endOffset. If startOffset is also set, the objects listed will
      have names between startOffset (inclusive) and endOffset (exclusive).
    filter: Filter the returned objects. Currently only supported for the
      contexts field. If delimiter is set, the returned prefixes are exempt
      from this filter.
    includeFoldersAsPrefixes: Only applicable if delimiter is set to '/'. If
      true, will also include folders and managed folders (besides objects) in
      the returned prefixes.
    includeTrailingDelimiter: If true, objects that end in exactly one
      instance of delimiter will have their metadata included in items in
      addition to prefixes.
    matchGlob: Filter results to objects and prefixes that match this glob
      pattern.
    maxResults: Maximum number of items plus prefixes to return in a single
      page of responses. As duplicate prefixes are omitted, fewer total
      results may be returned than requested. The service will use this
      parameter or 1,000 items, whichever is smaller.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.
    prefix: Filter results to objects whose names begin with this prefix.
    projection: Set of properties to return. Defaults to noAcl.
    softDeleted: If true, only soft-deleted object versions will be listed.
      The default is false. For more information, see [Soft
      Delete](https://cloud.google.com/storage/docs/soft-delete).
    startOffset: Filter results to objects whose names are lexicographically
      equal to or after startOffset. If endOffset is also set, the objects
      listed will have names between startOffset (inclusive) and endOffset
      (exclusive).
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
    versions: If true, lists all versions of an object as distinct results.
      The default is false. For more information, see [Object
      Versioning](https://cloud.google.com/storage/docs/object-versioning).
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  delimiter = _messages.StringField(2)
  endOffset = _messages.StringField(3)
  filter = _messages.StringField(4)
  includeFoldersAsPrefixes = _messages.BooleanField(5)
  includeTrailingDelimiter = _messages.BooleanField(6)
  matchGlob = _messages.StringField(7)
  maxResults = _messages.IntegerField(8, variant=_messages.Variant.UINT32, default=1000)
  pageToken = _messages.StringField(9)
  prefix = _messages.StringField(10)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 11)
  softDeleted = _messages.BooleanField(12)
  startOffset = _messages.StringField(13)
  userProject = _messages.StringField(14)
  versions = _messages.BooleanField(15)


class StorageObjectsMoveRequest(_messages.Message):
  r"""A StorageObjectsMoveRequest object.

  Enums:
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl.

  Fields:
    bucket: Name of the bucket in which the object resides.
    destinationObject: Name of the destination object. For information about
      how to URL encode object names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    ifGenerationMatch: Makes the operation conditional on whether the
      destination object's current generation matches the given value. Setting
      to 0 makes the operation succeed only if there are no live versions of
      the object. `ifGenerationMatch` and `ifGenerationNotMatch` conditions
      are mutually exclusive: it's an error for both of them to be set in the
      request.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      destination object's current generation does not match the given value.
      If no live object exists, the precondition fails. Setting to 0 makes the
      operation succeed only if there is a live version of the
      object.`ifGenerationMatch` and `ifGenerationNotMatch` conditions are
      mutually exclusive: it's an error for both of them to be set in the
      request.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      destination object's current metageneration matches the given value.
      `ifMetagenerationMatch` and `ifMetagenerationNotMatch` conditions are
      mutually exclusive: it's an error for both of them to be set in the
      request.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      destination object's current metageneration does not match the given
      value. `ifMetagenerationMatch` and `ifMetagenerationNotMatch` conditions
      are mutually exclusive: it's an error for both of them to be set in the
      request.
    ifSourceGenerationMatch: Makes the operation conditional on whether the
      source object's current generation matches the given value.
      `ifSourceGenerationMatch` and `ifSourceGenerationNotMatch` conditions
      are mutually exclusive: it's an error for both of them to be set in the
      request.
    ifSourceGenerationNotMatch: Makes the operation conditional on whether the
      source object's current generation does not match the given value.
      `ifSourceGenerationMatch` and `ifSourceGenerationNotMatch` conditions
      are mutually exclusive: it's an error for both of them to be set in the
      request.
    ifSourceMetagenerationMatch: Makes the operation conditional on whether
      the source object's current metageneration matches the given value.
      `ifSourceMetagenerationMatch` and `ifSourceMetagenerationNotMatch`
      conditions are mutually exclusive: it's an error for both of them to be
      set in the request.
    ifSourceMetagenerationNotMatch: Makes the operation conditional on whether
      the source object's current metageneration does not match the given
      value. `ifSourceMetagenerationMatch` and
      `ifSourceMetagenerationNotMatch` conditions are mutually exclusive: it's
      an error for both of them to be set in the request.
    projection: Set of properties to return. Defaults to noAcl.
    sourceObject: Name of the source object. For information about how to URL
      encode object names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  destinationObject = _messages.StringField(2, required=True)
  ifGenerationMatch = _messages.IntegerField(3)
  ifGenerationNotMatch = _messages.IntegerField(4)
  ifMetagenerationMatch = _messages.IntegerField(5)
  ifMetagenerationNotMatch = _messages.IntegerField(6)
  ifSourceGenerationMatch = _messages.IntegerField(7)
  ifSourceGenerationNotMatch = _messages.IntegerField(8)
  ifSourceMetagenerationMatch = _messages.IntegerField(9)
  ifSourceMetagenerationNotMatch = _messages.IntegerField(10)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 11)
  sourceObject = _messages.StringField(12, required=True)
  userProject = _messages.StringField(13)


class StorageObjectsPatchRequest(_messages.Message):
  r"""A StorageObjectsPatchRequest object.

  Enums:
    PredefinedAclValueValuesEnum: Apply a predefined set of access controls to
      this object.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to full.

  Fields:
    bucket: Name of the bucket in which the object resides.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    ifGenerationMatch: Makes the operation conditional on whether the object's
      current generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      object's current generation does not match the given value. If no live
      object exists, the precondition fails. Setting to 0 makes the operation
      succeed only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      object's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      object's current metageneration does not match the given value.
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    objectResource: A Object resource to be passed as the request body.
    overrideUnlockedRetention: Must be true to remove the retention
      configuration, reduce its unlocked retention period, or change its mode
      from unlocked to locked.
    predefinedAcl: Apply a predefined set of access controls to this object.
    projection: Set of properties to return. Defaults to full.
    userProject: The project to be billed for this request, for Requester Pays
      buckets.
  """

  class PredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to this object.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  ifGenerationMatch = _messages.IntegerField(3)
  ifGenerationNotMatch = _messages.IntegerField(4)
  ifMetagenerationMatch = _messages.IntegerField(5)
  ifMetagenerationNotMatch = _messages.IntegerField(6)
  object = _messages.StringField(7, required=True)
  objectResource = _messages.MessageField('Object', 8)
  overrideUnlockedRetention = _messages.BooleanField(9)
  predefinedAcl = _messages.EnumField('PredefinedAclValueValuesEnum', 10)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 11)
  userProject = _messages.StringField(12)


class StorageObjectsRestoreRequest(_messages.Message):
  r"""A StorageObjectsRestoreRequest object.

  Enums:
    ProjectionValueValuesEnum: Set of properties to return. Defaults to full.

  Fields:
    bucket: Name of the bucket in which the object resides.
    copySourceAcl: If true, copies the source object's ACL; otherwise, uses
      the bucket's default object ACL. The default is false.
    generation: Selects a specific revision of this object.
    ifGenerationMatch: Makes the operation conditional on whether the object's
      one live generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifGenerationNotMatch: Makes the operation conditional on whether none of
      the object's live generations match the given value. If no live object
      exists, the precondition fails. Setting to 0 makes the operation succeed
      only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      object's one live metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether none
      of the object's live metagenerations match the given value.
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    projection: Set of properties to return. Defaults to full.
    restoreToken: Restore token used to differentiate sof-deleted objects with
      the same name and generation. Only applicable for hierarchical namespace
      buckets. This parameter is optional, and is only required in the rare
      case when there are multiple soft-deleted objects with the same name and
      generation.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  copySourceAcl = _messages.BooleanField(2)
  generation = _messages.IntegerField(3, required=True)
  ifGenerationMatch = _messages.IntegerField(4)
  ifGenerationNotMatch = _messages.IntegerField(5)
  ifMetagenerationMatch = _messages.IntegerField(6)
  ifMetagenerationNotMatch = _messages.IntegerField(7)
  object = _messages.StringField(8, required=True)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 9)
  restoreToken = _messages.StringField(10)
  userProject = _messages.StringField(11)


class StorageObjectsRewriteRequest(_messages.Message):
  r"""A StorageObjectsRewriteRequest object.

  Enums:
    DestinationPredefinedAclValueValuesEnum: Apply a predefined set of access
      controls to the destination object.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl,
      unless the object resource specifies the acl property, when it defaults
      to full.

  Fields:
    destinationBucket: Name of the bucket in which to store the new object.
      Overrides the provided object metadata's bucket value, if any.
    destinationKmsKeyName: Resource name of the Cloud KMS key, of the form
      projects/my-project/locations/global/keyRings/my-kr/cryptoKeys/my-key,
      that will be used to encrypt the object. Overrides the object metadata's
      kms_key_name value, if any.
    destinationObject: Name of the new object. Required when the object
      metadata is not otherwise provided. Overrides the object metadata's name
      value, if any. For information about how to URL encode object names to
      be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    destinationPredefinedAcl: Apply a predefined set of access controls to the
      destination object.
    ifGenerationMatch: Makes the operation conditional on whether the object's
      current generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      object's current generation does not match the given value. If no live
      object exists, the precondition fails. Setting to 0 makes the operation
      succeed only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      destination object's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      destination object's current metageneration does not match the given
      value.
    ifSourceGenerationMatch: Makes the operation conditional on whether the
      source object's current generation matches the given value.
    ifSourceGenerationNotMatch: Makes the operation conditional on whether the
      source object's current generation does not match the given value.
    ifSourceMetagenerationMatch: Makes the operation conditional on whether
      the source object's current metageneration matches the given value.
    ifSourceMetagenerationNotMatch: Makes the operation conditional on whether
      the source object's current metageneration does not match the given
      value.
    maxBytesRewrittenPerCall: The maximum number of bytes that will be
      rewritten per rewrite request. Most callers shouldn't need to specify
      this parameter - it is primarily in place to support testing. If
      specified the value must be an integral multiple of 1 MiB (1048576).
      Also, this only applies to requests where the source and destination
      span locations and/or storage classes. Finally, this value must not
      change across rewrite calls else you'll get an error that the
      rewriteToken is invalid.
    object: A Object resource to be passed as the request body.
    projection: Set of properties to return. Defaults to noAcl, unless the
      object resource specifies the acl property, when it defaults to full.
    rewriteToken: Include this field (from the previous rewrite response) on
      each rewrite request after the first one, until the rewrite response
      'done' flag is true. Calls that provide a rewriteToken can omit all
      other request fields, but if included those fields must match the values
      provided in the first rewrite request.
    sourceBucket: Name of the bucket in which to find the source object.
    sourceGeneration: If present, selects a specific revision of the source
      object (as opposed to the latest version, the default).
    sourceObject: Name of the source object. For information about how to URL
      encode object names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class DestinationPredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to the destination object.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl, unless the object
    resource specifies the acl property, when it defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  destinationBucket = _messages.StringField(1, required=True)
  destinationKmsKeyName = _messages.StringField(2)
  destinationObject = _messages.StringField(3, required=True)
  destinationPredefinedAcl = _messages.EnumField('DestinationPredefinedAclValueValuesEnum', 4)
  ifGenerationMatch = _messages.IntegerField(5)
  ifGenerationNotMatch = _messages.IntegerField(6)
  ifMetagenerationMatch = _messages.IntegerField(7)
  ifMetagenerationNotMatch = _messages.IntegerField(8)
  ifSourceGenerationMatch = _messages.IntegerField(9)
  ifSourceGenerationNotMatch = _messages.IntegerField(10)
  ifSourceMetagenerationMatch = _messages.IntegerField(11)
  ifSourceMetagenerationNotMatch = _messages.IntegerField(12)
  maxBytesRewrittenPerCall = _messages.IntegerField(13)
  object = _messages.MessageField('Object', 14)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 15)
  rewriteToken = _messages.StringField(16)
  sourceBucket = _messages.StringField(17, required=True)
  sourceGeneration = _messages.IntegerField(18)
  sourceObject = _messages.StringField(19, required=True)
  userProject = _messages.StringField(20)


class StorageObjectsSetIamPolicyRequest(_messages.Message):
  r"""A StorageObjectsSetIamPolicyRequest object.

  Fields:
    bucket: Name of the bucket in which the object resides.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    policy: A Policy resource to be passed as the request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3, required=True)
  policy = _messages.MessageField('Policy', 4)
  userProject = _messages.StringField(5)


class StorageObjectsTestIamPermissionsRequest(_messages.Message):
  r"""A StorageObjectsTestIamPermissionsRequest object.

  Fields:
    bucket: Name of the bucket in which the object resides.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    permissions: Permissions to test.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3, required=True)
  permissions = _messages.StringField(4, required=True)
  userProject = _messages.StringField(5)


class StorageObjectsUpdateRequest(_messages.Message):
  r"""A StorageObjectsUpdateRequest object.

  Enums:
    PredefinedAclValueValuesEnum: Apply a predefined set of access controls to
      this object.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to full.

  Fields:
    bucket: Name of the bucket in which the object resides.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    ifGenerationMatch: Makes the operation conditional on whether the object's
      current generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      object's current generation does not match the given value. If no live
      object exists, the precondition fails. Setting to 0 makes the operation
      succeed only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      object's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      object's current metageneration does not match the given value.
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see [Encoding URI Path
      Parts](https://cloud.google.com/storage/docs/request-
      endpoints#encoding).
    objectResource: A Object resource to be passed as the request body.
    overrideUnlockedRetention: Must be true to remove the retention
      configuration, reduce its unlocked retention period, or change its mode
      from unlocked to locked.
    predefinedAcl: Apply a predefined set of access controls to this object.
    projection: Set of properties to return. Defaults to full.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class PredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to this object.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  ifGenerationMatch = _messages.IntegerField(3)
  ifGenerationNotMatch = _messages.IntegerField(4)
  ifMetagenerationMatch = _messages.IntegerField(5)
  ifMetagenerationNotMatch = _messages.IntegerField(6)
  object = _messages.StringField(7, required=True)
  objectResource = _messages.MessageField('Object', 8)
  overrideUnlockedRetention = _messages.BooleanField(9)
  predefinedAcl = _messages.EnumField('PredefinedAclValueValuesEnum', 10)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 11)
  userProject = _messages.StringField(12)


class StorageObjectsWatchAllRequest(_messages.Message):
  r"""A StorageObjectsWatchAllRequest object.

  Enums:
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl.

  Fields:
    bucket: Name of the bucket in which to look for objects.
    channel: A Channel resource to be passed as the request body.
    delimiter: Returns results in a directory-like mode. items will contain
      only objects whose names, aside from the prefix, do not contain
      delimiter. Objects whose names, aside from the prefix, contain delimiter
      will have their name, truncated after the delimiter, returned in
      prefixes. Duplicate prefixes are omitted.
    endOffset: Filter results to objects whose names are lexicographically
      before endOffset. If startOffset is also set, the objects listed will
      have names between startOffset (inclusive) and endOffset (exclusive).
    includeTrailingDelimiter: If true, objects that end in exactly one
      instance of delimiter will have their metadata included in items in
      addition to prefixes.
    maxResults: Maximum number of items plus prefixes to return in a single
      page of responses. As duplicate prefixes are omitted, fewer total
      results may be returned than requested. The service will use this
      parameter or 1,000 items, whichever is smaller.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.
    prefix: Filter results to objects whose names begin with this prefix.
    projection: Set of properties to return. Defaults to noAcl.
    startOffset: Filter results to objects whose names are lexicographically
      equal to or after startOffset. If endOffset is also set, the objects
      listed will have names between startOffset (inclusive) and endOffset
      (exclusive).
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
    versions: If true, lists all versions of an object as distinct results.
      The default is false. For more information, see [Object
      Versioning](https://cloud.google.com/storage/docs/object-versioning).
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  channel = _messages.MessageField('Channel', 2)
  delimiter = _messages.StringField(3)
  endOffset = _messages.StringField(4)
  includeTrailingDelimiter = _messages.BooleanField(5)
  maxResults = _messages.IntegerField(6, variant=_messages.Variant.UINT32, default=1000)
  pageToken = _messages.StringField(7)
  prefix = _messages.StringField(8)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 9)
  startOffset = _messages.StringField(10)
  userProject = _messages.StringField(11)
  versions = _messages.BooleanField(12)


class StorageProjectsHmacKeysCreateRequest(_messages.Message):
  r"""A StorageProjectsHmacKeysCreateRequest object.

  Fields:
    projectId: Project ID owning the service account.
    serviceAccountEmail: Email address of the service account.
    userProject: The project to be billed for this request.
  """

  projectId = _messages.StringField(1, required=True)
  serviceAccountEmail = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageProjectsHmacKeysDeleteRequest(_messages.Message):
  r"""A StorageProjectsHmacKeysDeleteRequest object.

  Fields:
    accessId: Name of the HMAC key to be deleted.
    projectId: Project ID owning the requested key
    userProject: The project to be billed for this request.
  """

  accessId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageProjectsHmacKeysDeleteResponse(_messages.Message):
  r"""An empty StorageProjectsHmacKeysDelete response."""


class StorageProjectsHmacKeysGetRequest(_messages.Message):
  r"""A StorageProjectsHmacKeysGetRequest object.

  Fields:
    accessId: Name of the HMAC key.
    projectId: Project ID owning the service account of the requested key.
    userProject: The project to be billed for this request.
  """

  accessId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageProjectsHmacKeysListRequest(_messages.Message):
  r"""A StorageProjectsHmacKeysListRequest object.

  Fields:
    maxResults: Maximum number of items to return in a single page of
      responses. The service uses this parameter or 250 items, whichever is
      smaller. The max number of items per page will also be limited by the
      number of distinct service accounts in the response. If the number of
      service accounts in a single response is too high, the page will
      truncated and a next page token will be returned.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.
    projectId: Name of the project in which to look for HMAC keys.
    serviceAccountEmail: If present, only keys for the given service account
      are returned.
    showDeletedKeys: Whether or not to show keys in the DELETED state.
    userProject: The project to be billed for this request.
  """

  maxResults = _messages.IntegerField(1, variant=_messages.Variant.UINT32, default=250)
  pageToken = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)
  serviceAccountEmail = _messages.StringField(4)
  showDeletedKeys = _messages.BooleanField(5)
  userProject = _messages.StringField(6)


class StorageProjectsHmacKeysUpdateRequest(_messages.Message):
  r"""A StorageProjectsHmacKeysUpdateRequest object.

  Fields:
    accessId: Name of the HMAC key being updated.
    hmacKeyMetadata: A HmacKeyMetadata resource to be passed as the request
      body.
    projectId: Project ID owning the service account of the updated key.
    userProject: The project to be billed for this request.
  """

  accessId = _messages.StringField(1, required=True)
  hmacKeyMetadata = _messages.MessageField('HmacKeyMetadata', 2)
  projectId = _messages.StringField(3, required=True)
  userProject = _messages.StringField(4)


class StorageProjectsServiceAccountGetRequest(_messages.Message):
  r"""A StorageProjectsServiceAccountGetRequest object.

  Fields:
    projectId: Project ID
    userProject: The project to be billed for this request.
  """

  projectId = _messages.StringField(1, required=True)
  userProject = _messages.StringField(2)


class TestIamPermissionsResponse(_messages.Message):
  r"""A storage.(buckets|objects|managedFolders).testIamPermissions response.

  Fields:
    kind: The kind of item this is.
    permissions: The permissions held by the caller. Permissions are always of
      the format storage.resource.capability, where resource is one of
      buckets, objects, or managedFolders. The supported permissions are as
      follows:   - storage.buckets.delete - Delete bucket.   -
      storage.buckets.get - Read bucket metadata.   -
      storage.buckets.getIamPolicy - Read bucket IAM policy.   -
      storage.buckets.create - Create bucket.   - storage.buckets.list - List
      buckets.   - storage.buckets.setIamPolicy - Update bucket IAM policy.
      - storage.buckets.update - Update bucket metadata.   -
      storage.objects.delete - Delete object.   - storage.objects.get - Read
      object data and metadata.   - storage.objects.getIamPolicy - Read object
      IAM policy.   - storage.objects.create - Create object.   -
      storage.objects.list - List objects.   - storage.objects.setIamPolicy -
      Update object IAM policy.   - storage.objects.update - Update object
      metadata.  - storage.managedFolders.delete - Delete managed folder.   -
      storage.managedFolders.get - Read managed folder metadata.   -
      storage.managedFolders.getIamPolicy - Read managed folder IAM policy.
      - storage.managedFolders.create - Create managed folder.   -
      storage.managedFolders.list - List managed folders.   -
      storage.managedFolders.setIamPolicy - Update managed folder IAM policy.
  """

  kind = _messages.StringField(1, default='storage#testIamPermissionsResponse')
  permissions = _messages.StringField(2, repeated=True)


