{"cells": [{"cell_type": "markdown", "id": "FjxjdkFqRERE", "metadata": {"id": "FjxjdkFqRERE"}, "source": ["# Tool example"]}, {"cell_type": "code", "execution_count": null, "id": "sNjI6aGKBzAJ0aIcO0YDYpWs", "metadata": {"id": "sNjI6aGKBzAJ0aIcO0YDYpWs", "tags": []}, "outputs": [], "source": ["from langchain_google_vertexai import ChatVertexAI\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "llm = ChatVertexAI(model=\"gemini-2.0-flash-001\")"]}, {"cell_type": "markdown", "id": "4b70bbb5", "metadata": {}, "source": ["Let's take a look the example of enhancing an LLM with tools. We will mock tools responses for now, and demonstrate how tool calling works in general. Let's instruct LLM either to use a tool or to reply to the user:"]}, {"cell_type": "code", "execution_count": null, "id": "5b279629", "metadata": {}, "outputs": [], "source": ["question = \"how old is the US president?\"\n", "\n", "raw_prompt_template = (\n", "  \"You have access to search engine that provides you an \"\n", "  \"information about fresh events and news given the query. \"\n", "  \"Given the question, decide whether you need an additional \"\n", "  \"information from the search engine (reply with 'SEARCH: \"\n", "   \"<generated query>' or you know enough to answer the user \"\n", "   \"then reply with 'RESPONSE <final response>').\\n\"\n", "   \"Do not make any assumptions on recent events or things that can change.\"\n", "   \"Now, act to answer a user question:\\n{QUESTION}\"\n", ")\n", "prompt_template = PromptTemplate.from_template(raw_prompt_template)"]}, {"cell_type": "markdown", "id": "a6e2fd6a", "metadata": {}, "source": ["Now let's call an LLM with a simple question that can be answered based on its internal knowledge:"]}, {"cell_type": "code", "execution_count": null, "id": "sLLhGygGY7nP", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 1, "status": "ok", "timestamp": 1738937769106, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "sLLhGygGY7nP", "outputId": "3c5e1b98-21d0-475c-dcfa-1f9874805498"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RESPONSE: Berlin\n", "\n"]}], "source": ["question1 = \"What is the capital of Germany?\"\n", "result = (prompt_template | llm).invoke(question1)\n", "print(result.content)"]}, {"cell_type": "markdown", "id": "a7ec1f60", "metadata": {}, "source": ["If we change the question, we can see how an LLM has decided to use a search tool and generated a search query to be used with it: "]}, {"cell_type": "code", "execution_count": null, "id": "5Mo24RCeYeXl", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 367, "status": "ok", "timestamp": 1738937768279, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "5Mo24RCeYeXl", "outputId": "277ffe77-54a4-4d38-a78b-279429cc626e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SEARCH: current age of the US president\n", "\n"]}], "source": ["response = (prompt_template | llm).invoke(question)\n", "print(response.content)"]}, {"cell_type": "markdown", "id": "21a62c22", "metadata": {}, "source": ["We can make the next step, and give a history of previous intercations (including a response from the tool). Please, note that we haven't implemented any tools yet, we'll do it in the next sections:"]}, {"cell_type": "code", "execution_count": null, "id": "qEn3zIWsZcN5", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 1, "status": "ok", "timestamp": 1738937769857, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "qEn3zIWsZcN5", "outputId": "a61ba6e3-a039-4175-8ed1-306df25bccc1"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RESPONSE: The current US president, <PERSON>, is 78 years old.\n", "\n"]}], "source": ["query = \"age of current US president\"\n", "search_result = (\n", "    \"<PERSON> › Age 78 years June 14, 1946\\n\"\n", "    \"<PERSON> 45th and 47th U.S. President <PERSON> is an American \"\n", "    \"politician, media personality, and businessman who has served as the 47th \"\n", "    \"president of the United States since January 20, 2025. A member of the \"\n", "    \"Republican Party, he previously served as the 45th president from 2017 to 2021. Wikipedia\"\n", ")\n", "\n", "raw_prompt_template = (\n", "  \"You have access to search engine that provides you an \"\n", "  \"information about fresh events and news given the query. \"\n", "  \"Given the question, decide whether you need an additional \"\n", "  \"information from the search engine (reply with 'SEARCH: \"\n", "   \"<generated query>' or you know enough to answer the user \"\n", "   \"then reply with 'RESPONSE <final response>').\\n\"\n", "   \"Today is {date}.\"\n", "   \"Now, act to answer a user question and \"\n", "   \"take into account your previous actions:\\n\"\n", "   \"HUMAN: {question}\\n\"\n", "   \"AI: SEARCH: {query}\\n\"\n", "   \"RESPONSE FROM SEARCH: {search_result}\\n\"\n", ")\n", "prompt_template = PromptTemplate.from_template(raw_prompt_template)\n", "\n", "result = (prompt_template | llm).invoke({\"question\": question, \"query\": query, \"search_result\": search_result, \"date\": \"Feb 2025\"})\n", "print(result.content)"]}, {"cell_type": "markdown", "id": "e7fca685", "metadata": {}, "source": ["We can see that an LLM can decide to call a tool as the second step if the previous interaction hasn't provided a clear answer:"]}, {"cell_type": "code", "execution_count": null, "id": "dpyIZ_WNgWrU", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 360, "status": "ok", "timestamp": *************, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "dpyIZ_WNgWrU", "outputId": "6f657f05-f5f6-4b1f-bedd-38be14c13e04"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SEARCH: <PERSON> age\n", "\n"]}], "source": ["query = \"current US president\"\n", "search_result = (\n", "    \"<PERSON> 45th and 47th U.S.\"\n", ")\n", "\n", "raw_prompt_template = (\n", "  \"You have access to search engine that provides you an \"\n", "  \"information about fresh events and news given the query. \"\n", "  \"Given the question, decide whether you need an additional \"\n", "  \"information from the search engine (reply with 'SEARCH: \"\n", "   \"<generated query>' or you know enough to answer the user \"\n", "   \"then reply with 'RESPONSE <final response>').\\n\"\n", "   \"Today is {date}.\"\n", "   \"Now, act to answer a user question and \"\n", "   \"take into account your previous actions:\\n\"\n", "   \"HUMAN: {question}\\n\"\n", "   \"AI: SEARCH: {query}\\n\"\n", "   \"RESPONSE FROM SEARCH: {search_result}\\n\"\n", ")\n", "prompt_template = PromptTemplate.from_template(raw_prompt_template)\n", "\n", "result = (prompt_template | llm).invoke({\"question\": question, \"query\": query, \"search_result\": search_result, \"date\": \"Feb 2025\"})\n", "print(result.content)"]}], "metadata": {"colab": {"name": "Chapter2.5", "provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}