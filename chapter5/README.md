# Chapter 5 - Building Intelligent Agents

Please make sure you set up your environment with pip, conda, poetry, or docker! You can set up the keys for the different providers in a `config.py` as recommended in the book. Please check the [setup instructions](../SETUP.md) for dependencies and API keys before you start.

| Section	| File | Colab	 | Kaggle	|
|-----------|--------|--------|-----------|
| Tool calling basics | [notebook](tools_with_llm_example.ipynb)  | [![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/tools_with_llm_example.ipynb) | [![Open in Kaggle](https://kaggle.com/static/images/open-in-kaggle.svg)](https://www.kaggle.com/code/new) |
| Tools in LangChain | [notebook](tools_langchain.ipynb)  | [![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/tools_langchain.ipynb) | [![Open in Kaggle](https://kaggle.com/static/images/open-in-kaggle.svg)](https://www.kaggle.com/code/new) |
| Custom tools | [notebook](custom_tools.ipynb)     | [![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/custom_tools.ipynb) | [![Open in Kaggle](https://kaggle.com/static/images/open-in-kaggle.svg)](https://www.kaggle.com/code/new) |
| Built-in tools | [notebook](built-in_tools.ipynb)     | [![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/built-in_tools.ipynb) | [![Open in Kaggle](https://kaggle.com/static/images/open-in-kaggle.svg)](https://www.kaggle.com/code/new) |
| ReACT | [notebook](react_example.ipynb)     | [![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/react_example.ipynb) | [![Open in Kaggle](https://kaggle.com/static/images/open-in-kaggle.svg)](https://www.kaggle.com/code/new) |
| Advanced tool calling capabilities | [notebook](tool_node.ipynb)     | [![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/tool_node.ipynb) | [![Open in Kaggle](https://kaggle.com/static/images/open-in-kaggle.svg)](https://www.kaggle.com/code/new) |
| Structured outputs and controlled generation  | [notebook](structured_output.ipynb)     | [![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/structured_output.ipynb) | [![Open in Kaggle](https://kaggle.com/static/images/open-in-kaggle.svg)](https://www.kaggle.com/code/new) |
| Plan-and-solve agent  | [notebook](plan_and_solve.ipynb)     | [![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/plan_and_solve.ipynb) | [![Open in Kaggle](https://kaggle.com/static/images/open-in-kaggle.svg)](https://www.kaggle.com/code/new) |

#### 📋 **Kaggle Import Instructions:**
To import any notebook to Kaggle:
1. Click the Kaggle badge above
2. Go to **File** → **Import Notebook** → **GitHub**
3. Paste the corresponding GitHub URL:
   - **Tool Calling Basics**: `https://github.com/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/tools_with_llm_example.ipynb`
   - **Tools in LangChain**: `https://github.com/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/tools_langchain.ipynb`
   - **Custom Tools**: `https://github.com/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/custom_tools.ipynb`
   - **Built-in Tools**: `https://github.com/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/built-in_tools.ipynb`
   - **ReACT**: `https://github.com/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/react_example.ipynb`
   - **Advanced Tool Calling**: `https://github.com/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/tool_node.ipynb`
   - **Structured Outputs**: `https://github.com/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/structured_output.ipynb`
   - **Plan-and-Solve Agent**: `https://github.com/benman1/generative_ai_with_langchain/blob/second_edition/chapter5/plan_and_solve.ipynb`
  
